{"ast": null, "code": "import { OpacityUpdater } from \"./OpacityUpdater.js\";\nexport async function loadOpacityUpdater(engine, refresh = true) {\n  await engine.addParticleUpdater(\"opacity\", container => {\n    return Promise.resolve(new OpacityUpdater(container));\n  }, refresh);\n}", "map": {"version": 3, "names": ["OpacityUpdater", "loadOpacityUpdater", "engine", "refresh", "addParticleUpdater", "container", "Promise", "resolve"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/updater-opacity/browser/index.js"], "sourcesContent": ["import { OpacityUpdater } from \"./OpacityUpdater.js\";\nexport async function loadOpacityUpdater(engine, refresh = true) {\n    await engine.addParticleUpdater(\"opacity\", container => {\n        return Promise.resolve(new OpacityUpdater(container));\n    }, refresh);\n}\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,qBAAqB;AACpD,OAAO,eAAeC,kBAAkBA,CAACC,MAAM,EAAEC,OAAO,GAAG,IAAI,EAAE;EAC7D,MAAMD,MAAM,CAACE,kBAAkB,CAAC,SAAS,EAAEC,SAAS,IAAI;IACpD,OAAOC,OAAO,CAACC,OAAO,CAAC,IAAIP,cAAc,CAACK,SAAS,CAAC,CAAC;EACzD,CAAC,EAAEF,OAAO,CAAC;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
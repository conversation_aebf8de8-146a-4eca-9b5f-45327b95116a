{"ast": null, "code": "export class CircleShape {\n  randomPosition(position, size, fill) {\n    const generateTheta = (x, y) => {\n      const u = Math.random() / 4.0;\n      const theta = Math.atan(y / x * Math.tan(2 * Math.PI * u));\n      const v = Math.random();\n\n      if (v < 0.25) {\n        return theta;\n      } else if (v < 0.5) {\n        return Math.PI - theta;\n      } else if (v < 0.75) {\n        return Math.PI + theta;\n      } else {\n        return -theta;\n      }\n    };\n\n    const radius = (x, y, theta) => x * y / Math.sqrt((y * Math.cos(theta)) ** 2 + (x * Math.sin(theta)) ** 2);\n\n    const [a, b] = [size.width / 2, size.height / 2];\n    const randomTheta = generateTheta(a, b),\n          maxRadius = radius(a, b, randomTheta),\n          randomRadius = fill ? maxRadius * Math.sqrt(Math.random()) : maxRadius;\n    return {\n      x: position.x + randomRadius * Math.cos(randomTheta),\n      y: position.y + randomRadius * Math.sin(randomTheta)\n    };\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Plugins/Emitters/Shapes/Circle/CircleShape.js"], "names": ["CircleShape", "randomPosition", "position", "size", "fill", "generateTheta", "x", "y", "u", "Math", "random", "theta", "atan", "tan", "PI", "v", "radius", "sqrt", "cos", "sin", "a", "b", "width", "height", "randomTheta", "maxRadius", "randomRadius"], "mappings": "AAAA,OAAO,MAAMA,WAAN,CAAkB;AACrBC,EAAAA,cAAc,CAACC,QAAD,EAAWC,IAAX,EAAiBC,IAAjB,EAAuB;AACjC,UAAMC,aAAa,GAAG,CAACC,CAAD,EAAIC,CAAJ,KAAU;AAC5B,YAAMC,CAAC,GAAGC,IAAI,CAACC,MAAL,KAAgB,GAA1B;AACA,YAAMC,KAAK,GAAGF,IAAI,CAACG,IAAL,CAAWL,CAAC,GAAGD,CAAL,GAAUG,IAAI,CAACI,GAAL,CAAS,IAAIJ,IAAI,CAACK,EAAT,GAAcN,CAAvB,CAApB,CAAd;AACA,YAAMO,CAAC,GAAGN,IAAI,CAACC,MAAL,EAAV;;AACA,UAAIK,CAAC,GAAG,IAAR,EAAc;AACV,eAAOJ,KAAP;AACH,OAFD,MAGK,IAAII,CAAC,GAAG,GAAR,EAAa;AACd,eAAON,IAAI,CAACK,EAAL,GAAUH,KAAjB;AACH,OAFI,MAGA,IAAII,CAAC,GAAG,IAAR,EAAc;AACf,eAAON,IAAI,CAACK,EAAL,GAAUH,KAAjB;AACH,OAFI,MAGA;AACD,eAAO,CAACA,KAAR;AACH;AACJ,KAhBD;;AAiBA,UAAMK,MAAM,GAAG,CAACV,CAAD,EAAIC,CAAJ,EAAOI,KAAP,KAAkBL,CAAC,GAAGC,CAAL,GAAUE,IAAI,CAACQ,IAAL,CAAU,CAACV,CAAC,GAAGE,IAAI,CAACS,GAAL,CAASP,KAAT,CAAL,KAAyB,CAAzB,GAA6B,CAACL,CAAC,GAAGG,IAAI,CAACU,GAAL,CAASR,KAAT,CAAL,KAAyB,CAAhE,CAA1C;;AACA,UAAM,CAACS,CAAD,EAAIC,CAAJ,IAAS,CAAClB,IAAI,CAACmB,KAAL,GAAa,CAAd,EAAiBnB,IAAI,CAACoB,MAAL,GAAc,CAA/B,CAAf;AACA,UAAMC,WAAW,GAAGnB,aAAa,CAACe,CAAD,EAAIC,CAAJ,CAAjC;AAAA,UAAyCI,SAAS,GAAGT,MAAM,CAACI,CAAD,EAAIC,CAAJ,EAAOG,WAAP,CAA3D;AAAA,UAAgFE,YAAY,GAAGtB,IAAI,GAAGqB,SAAS,GAAGhB,IAAI,CAACQ,IAAL,CAAUR,IAAI,CAACC,MAAL,EAAV,CAAf,GAA0Ce,SAA7I;AACA,WAAO;AACHnB,MAAAA,CAAC,EAAEJ,QAAQ,CAACI,CAAT,GAAaoB,YAAY,GAAGjB,IAAI,CAACS,GAAL,CAASM,WAAT,CAD5B;AAEHjB,MAAAA,CAAC,EAAEL,QAAQ,CAACK,CAAT,GAAamB,YAAY,GAAGjB,IAAI,CAACU,GAAL,CAASK,WAAT;AAF5B,KAAP;AAIH;;AA1BoB", "sourcesContent": ["export class CircleShape {\n    randomPosition(position, size, fill) {\n        const generateTheta = (x, y) => {\n            const u = Math.random() / 4.0;\n            const theta = Math.atan((y / x) * Math.tan(2 * Math.PI * u));\n            const v = Math.random();\n            if (v < 0.25) {\n                return theta;\n            }\n            else if (v < 0.5) {\n                return Math.PI - theta;\n            }\n            else if (v < 0.75) {\n                return Math.PI + theta;\n            }\n            else {\n                return -theta;\n            }\n        };\n        const radius = (x, y, theta) => (x * y) / Math.sqrt((y * Math.cos(theta)) ** 2 + (x * Math.sin(theta)) ** 2);\n        const [a, b] = [size.width / 2, size.height / 2];\n        const randomTheta = generateTheta(a, b), maxRadius = radius(a, b, randomTheta), randomRadius = fill ? maxRadius * Math.sqrt(Math.random()) : maxRadius;\n        return {\n            x: position.x + randomRadius * Math.cos(randomTheta),\n            y: position.y + randomRadius * Math.sin(randomTheta),\n        };\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
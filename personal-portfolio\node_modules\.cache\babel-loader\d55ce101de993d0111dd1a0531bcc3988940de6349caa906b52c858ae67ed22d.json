{"ast": null, "code": "import { Circle, DivType, Rectangle, Vector, calculateBounds, circleBounce, circleBounceDataFromParticle, divModeExecute, rectBounce } from \"@tsparticles/engine\";\nconst squareExp = 2,\n  half = 0.5,\n  halfPI = Math.PI * half,\n  double = 2,\n  toleranceFactor = 10,\n  minRadius = 0;\nfunction processBounce(container, position, radius, area, enabledCb) {\n  const query = container.particles.quadTree.query(area, enabledCb);\n  for (const particle of query) {\n    if (area instanceof Circle) {\n      circleBounce(circleBounceDataFromParticle(particle), {\n        position,\n        radius,\n        mass: radius ** squareExp * halfPI,\n        velocity: Vector.origin,\n        factor: Vector.origin\n      });\n    } else if (area instanceof Rectangle) {\n      rectBounce(particle, calculateBounds(position, radius));\n    }\n  }\n}\nfunction singleSelectorBounce(container, selector, div, bounceCb) {\n  const query = document.querySelectorAll(selector);\n  if (!query.length) {\n    return;\n  }\n  query.forEach(item => {\n    const elem = item,\n      pxRatio = container.retina.pixelRatio,\n      pos = {\n        x: (elem.offsetLeft + elem.offsetWidth * half) * pxRatio,\n        y: (elem.offsetTop + elem.offsetHeight * half) * pxRatio\n      },\n      radius = elem.offsetWidth * half * pxRatio,\n      tolerance = toleranceFactor * pxRatio,\n      area = div.type === DivType.circle ? new Circle(pos.x, pos.y, radius + tolerance) : new Rectangle(elem.offsetLeft * pxRatio - tolerance, elem.offsetTop * pxRatio - tolerance, elem.offsetWidth * pxRatio + tolerance * double, elem.offsetHeight * pxRatio + tolerance * double);\n    bounceCb(pos, radius, area);\n  });\n}\nexport function divBounce(container, divs, bounceMode, enabledCb) {\n  divModeExecute(bounceMode, divs, (selector, div) => singleSelectorBounce(container, selector, div, (pos, radius, area) => processBounce(container, pos, radius, area, enabledCb)));\n}\nexport function mouseBounce(container, enabledCb) {\n  const pxRatio = container.retina.pixelRatio,\n    tolerance = toleranceFactor * pxRatio,\n    mousePos = container.interactivity.mouse.position,\n    radius = container.retina.bounceModeDistance;\n  if (!radius || radius < minRadius || !mousePos) {\n    return;\n  }\n  processBounce(container, mousePos, radius, new Circle(mousePos.x, mousePos.y, radius + tolerance), enabledCb);\n}", "map": {"version": 3, "names": ["Circle", "DivType", "Rectangle", "Vector", "calculateBounds", "circleBounce", "circleBounceDataFromParticle", "divModeExecute", "rectBounce", "squareExp", "half", "halfPI", "Math", "PI", "double", "toleranceFactor", "minRadius", "processBounce", "container", "position", "radius", "area", "enabledCb", "query", "particles", "quadTree", "particle", "mass", "velocity", "origin", "factor", "singleSelectorBounce", "selector", "div", "bounceCb", "document", "querySelectorAll", "length", "for<PERSON>ach", "item", "elem", "pxRatio", "retina", "pixelRatio", "pos", "x", "offsetLeft", "offsetWidth", "y", "offsetTop", "offsetHeight", "tolerance", "type", "circle", "divBounce", "divs", "bounceMode", "mouseBounce", "mousePos", "interactivity", "mouse", "bounceModeDistance"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/interaction-external-bounce/browser/Utils.js"], "sourcesContent": ["import { Circle, DivType, Rectangle, Vector, calculateBounds, circleBounce, circleBounceDataFromParticle, divModeExecute, rectBounce, } from \"@tsparticles/engine\";\nconst squareExp = 2, half = 0.5, halfPI = Math.PI * half, double = 2, toleranceFactor = 10, minRadius = 0;\nfunction processBounce(container, position, radius, area, enabledCb) {\n    const query = container.particles.quadTree.query(area, enabledCb);\n    for (const particle of query) {\n        if (area instanceof Circle) {\n            circleBounce(circleBounceDataFromParticle(particle), {\n                position,\n                radius,\n                mass: radius ** squareExp * halfPI,\n                velocity: Vector.origin,\n                factor: Vector.origin,\n            });\n        }\n        else if (area instanceof Rectangle) {\n            rectBounce(particle, calculateBounds(position, radius));\n        }\n    }\n}\nfunction singleSelectorBounce(container, selector, div, bounceCb) {\n    const query = document.querySelectorAll(selector);\n    if (!query.length) {\n        return;\n    }\n    query.forEach(item => {\n        const elem = item, pxRatio = container.retina.pixelRatio, pos = {\n            x: (elem.offsetLeft + elem.offsetWidth * half) * pxRatio,\n            y: (elem.offsetTop + elem.offsetHeight * half) * pxRatio,\n        }, radius = elem.offsetWidth * half * pxRatio, tolerance = toleranceFactor * pxRatio, area = div.type === DivType.circle\n            ? new Circle(pos.x, pos.y, radius + tolerance)\n            : new Rectangle(elem.offsetLeft * pxRatio - tolerance, elem.offsetTop * pxRatio - tolerance, elem.offsetWidth * pxRatio + tolerance * double, elem.offsetHeight * pxRatio + tolerance * double);\n        bounceCb(pos, radius, area);\n    });\n}\nexport function divBounce(container, divs, bounceMode, enabledCb) {\n    divModeExecute(bounceMode, divs, (selector, div) => singleSelectorBounce(container, selector, div, (pos, radius, area) => processBounce(container, pos, radius, area, enabledCb)));\n}\nexport function mouseBounce(container, enabledCb) {\n    const pxRatio = container.retina.pixelRatio, tolerance = toleranceFactor * pxRatio, mousePos = container.interactivity.mouse.position, radius = container.retina.bounceModeDistance;\n    if (!radius || radius < minRadius || !mousePos) {\n        return;\n    }\n    processBounce(container, mousePos, radius, new Circle(mousePos.x, mousePos.y, radius + tolerance), enabledCb);\n}\n"], "mappings": "AAAA,SAASA,MAAM,EAAEC,OAAO,EAAEC,SAAS,EAAEC,MAAM,EAAEC,eAAe,EAAEC,YAAY,EAAEC,4BAA4B,EAAEC,cAAc,EAAEC,UAAU,QAAS,qBAAqB;AAClK,MAAMC,SAAS,GAAG,CAAC;EAAEC,IAAI,GAAG,GAAG;EAAEC,MAAM,GAAGC,IAAI,CAACC,EAAE,GAAGH,IAAI;EAAEI,MAAM,GAAG,CAAC;EAAEC,eAAe,GAAG,EAAE;EAAEC,SAAS,GAAG,CAAC;AACzG,SAASC,aAAaA,CAACC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,IAAI,EAAEC,SAAS,EAAE;EACjE,MAAMC,KAAK,GAAGL,SAAS,CAACM,SAAS,CAACC,QAAQ,CAACF,KAAK,CAACF,IAAI,EAAEC,SAAS,CAAC;EACjE,KAAK,MAAMI,QAAQ,IAAIH,KAAK,EAAE;IAC1B,IAAIF,IAAI,YAAYrB,MAAM,EAAE;MACxBK,YAAY,CAACC,4BAA4B,CAACoB,QAAQ,CAAC,EAAE;QACjDP,QAAQ;QACRC,MAAM;QACNO,IAAI,EAAEP,MAAM,IAAIX,SAAS,GAAGE,MAAM;QAClCiB,QAAQ,EAAEzB,MAAM,CAAC0B,MAAM;QACvBC,MAAM,EAAE3B,MAAM,CAAC0B;MACnB,CAAC,CAAC;IACN,CAAC,MACI,IAAIR,IAAI,YAAYnB,SAAS,EAAE;MAChCM,UAAU,CAACkB,QAAQ,EAAEtB,eAAe,CAACe,QAAQ,EAAEC,MAAM,CAAC,CAAC;IAC3D;EACJ;AACJ;AACA,SAASW,oBAAoBA,CAACb,SAAS,EAAEc,QAAQ,EAAEC,GAAG,EAAEC,QAAQ,EAAE;EAC9D,MAAMX,KAAK,GAAGY,QAAQ,CAACC,gBAAgB,CAACJ,QAAQ,CAAC;EACjD,IAAI,CAACT,KAAK,CAACc,MAAM,EAAE;IACf;EACJ;EACAd,KAAK,CAACe,OAAO,CAACC,IAAI,IAAI;IAClB,MAAMC,IAAI,GAAGD,IAAI;MAAEE,OAAO,GAAGvB,SAAS,CAACwB,MAAM,CAACC,UAAU;MAAEC,GAAG,GAAG;QAC5DC,CAAC,EAAE,CAACL,IAAI,CAACM,UAAU,GAAGN,IAAI,CAACO,WAAW,GAAGrC,IAAI,IAAI+B,OAAO;QACxDO,CAAC,EAAE,CAACR,IAAI,CAACS,SAAS,GAAGT,IAAI,CAACU,YAAY,GAAGxC,IAAI,IAAI+B;MACrD,CAAC;MAAErB,MAAM,GAAGoB,IAAI,CAACO,WAAW,GAAGrC,IAAI,GAAG+B,OAAO;MAAEU,SAAS,GAAGpC,eAAe,GAAG0B,OAAO;MAAEpB,IAAI,GAAGY,GAAG,CAACmB,IAAI,KAAKnD,OAAO,CAACoD,MAAM,GAClH,IAAIrD,MAAM,CAAC4C,GAAG,CAACC,CAAC,EAAED,GAAG,CAACI,CAAC,EAAE5B,MAAM,GAAG+B,SAAS,CAAC,GAC5C,IAAIjD,SAAS,CAACsC,IAAI,CAACM,UAAU,GAAGL,OAAO,GAAGU,SAAS,EAAEX,IAAI,CAACS,SAAS,GAAGR,OAAO,GAAGU,SAAS,EAAEX,IAAI,CAACO,WAAW,GAAGN,OAAO,GAAGU,SAAS,GAAGrC,MAAM,EAAE0B,IAAI,CAACU,YAAY,GAAGT,OAAO,GAAGU,SAAS,GAAGrC,MAAM,CAAC;IACnMoB,QAAQ,CAACU,GAAG,EAAExB,MAAM,EAAEC,IAAI,CAAC;EAC/B,CAAC,CAAC;AACN;AACA,OAAO,SAASiC,SAASA,CAACpC,SAAS,EAAEqC,IAAI,EAAEC,UAAU,EAAElC,SAAS,EAAE;EAC9Df,cAAc,CAACiD,UAAU,EAAED,IAAI,EAAE,CAACvB,QAAQ,EAAEC,GAAG,KAAKF,oBAAoB,CAACb,SAAS,EAAEc,QAAQ,EAAEC,GAAG,EAAE,CAACW,GAAG,EAAExB,MAAM,EAAEC,IAAI,KAAKJ,aAAa,CAACC,SAAS,EAAE0B,GAAG,EAAExB,MAAM,EAAEC,IAAI,EAAEC,SAAS,CAAC,CAAC,CAAC;AACtL;AACA,OAAO,SAASmC,WAAWA,CAACvC,SAAS,EAAEI,SAAS,EAAE;EAC9C,MAAMmB,OAAO,GAAGvB,SAAS,CAACwB,MAAM,CAACC,UAAU;IAAEQ,SAAS,GAAGpC,eAAe,GAAG0B,OAAO;IAAEiB,QAAQ,GAAGxC,SAAS,CAACyC,aAAa,CAACC,KAAK,CAACzC,QAAQ;IAAEC,MAAM,GAAGF,SAAS,CAACwB,MAAM,CAACmB,kBAAkB;EACnL,IAAI,CAACzC,MAAM,IAAIA,MAAM,GAAGJ,SAAS,IAAI,CAAC0C,QAAQ,EAAE;IAC5C;EACJ;EACAzC,aAAa,CAACC,SAAS,EAAEwC,QAAQ,EAAEtC,MAAM,EAAE,IAAIpB,MAAM,CAAC0D,QAAQ,CAACb,CAAC,EAAEa,QAAQ,CAACV,CAAC,EAAE5B,MAAM,GAAG+B,SAAS,CAAC,EAAE7B,SAAS,CAAC;AACjH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
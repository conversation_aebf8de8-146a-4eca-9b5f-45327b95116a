{"ast": null, "code": "import { AngleUpdater } from \"./AngleUpdater\";\nexport async function loadAngleUpdater(engine) {\n  await engine.addParticleUpdater(\"angle\", container => new AngleUpdater(container));\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Updaters/Angle/index.js"], "names": ["AngleUpdater", "loadAngleUpdater", "engine", "addParticleUpdater", "container"], "mappings": "AAAA,SAASA,YAAT,QAA6B,gBAA7B;AACA,OAAO,eAAeC,gBAAf,CAAgCC,MAAhC,EAAwC;AAC3C,QAAMA,MAAM,CAACC,kBAAP,CAA0B,OAA1B,EAAoCC,SAAD,IAAe,IAAIJ,YAAJ,CAAiBI,SAAjB,CAAlD,CAAN;AACH", "sourcesContent": ["import { AngleUpdater } from \"./AngleUpdater\";\nexport async function loadAngleUpdater(engine) {\n    await engine.addParticleUpdater(\"angle\", (container) => new AngleUpdater(container));\n}\n"]}, "metadata": {}, "sourceType": "module"}
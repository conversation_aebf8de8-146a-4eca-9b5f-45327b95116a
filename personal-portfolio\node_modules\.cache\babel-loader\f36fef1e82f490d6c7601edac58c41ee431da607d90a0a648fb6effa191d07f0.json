{"ast": null, "code": "export var AnimationMode;\n(function (AnimationMode) {\n  AnimationMode[\"auto\"] = \"auto\";\n  AnimationMode[\"increase\"] = \"increase\";\n  AnimationMode[\"decrease\"] = \"decrease\";\n  AnimationMode[\"random\"] = \"random\";\n})(AnimationMode || (AnimationMode = {}));", "map": {"version": 3, "names": ["AnimationMode"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/engine/browser/Enums/Modes/AnimationMode.js"], "sourcesContent": ["export var AnimationMode;\n(function (AnimationMode) {\n    AnimationMode[\"auto\"] = \"auto\";\n    AnimationMode[\"increase\"] = \"increase\";\n    AnimationMode[\"decrease\"] = \"decrease\";\n    AnimationMode[\"random\"] = \"random\";\n})(AnimationMode || (AnimationMode = {}));\n"], "mappings": "AAAA,OAAO,IAAIA,aAAa;AACxB,CAAC,UAAUA,aAAa,EAAE;EACtBA,aAAa,CAAC,MAAM,CAAC,GAAG,MAAM;EAC9BA,aAAa,CAAC,UAAU,CAAC,GAAG,UAAU;EACtCA,aAAa,CAAC,UAAU,CAAC,GAAG,UAAU;EACtCA,aAAa,CAAC,QAAQ,CAAC,GAAG,QAAQ;AACtC,CAAC,EAAEA,aAAa,KAAKA,aAAa,GAAG,CAAC,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
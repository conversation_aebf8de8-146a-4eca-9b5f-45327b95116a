{"ast": null, "code": "import { Linker } from \"./Linker.js\";\nexport async function loadLinksInteraction(engine, refresh = true) {\n  await engine.addInteractor(\"particlesLinks\", async container => {\n    return Promise.resolve(new Linker(container));\n  }, refresh);\n}", "map": {"version": 3, "names": ["<PERSON><PERSON>", "loadLinksInteraction", "engine", "refresh", "addInteractor", "container", "Promise", "resolve"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/interaction-particles-links/browser/interaction.js"], "sourcesContent": ["import { Linker } from \"./Linker.js\";\nexport async function loadLinksInteraction(engine, refresh = true) {\n    await engine.addInteractor(\"particlesLinks\", async (container) => {\n        return Promise.resolve(new Linker(container));\n    }, refresh);\n}\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,aAAa;AACpC,OAAO,eAAeC,oBAAoBA,CAACC,MAAM,EAAEC,OAAO,GAAG,IAAI,EAAE;EAC/D,MAAMD,MAAM,CAACE,aAAa,CAAC,gBAAgB,EAAE,MAAOC,SAAS,IAAK;IAC9D,OAAOC,OAAO,CAACC,OAAO,CAAC,IAAIP,MAAM,CAACK,SAAS,CAAC,CAAC;EACjD,CAAC,EAAEF,OAAO,CAAC;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { OptionsColor } from \"../OptionsColor\";\nexport class BackgroundMaskCover {\n  constructor() {\n    this.color = new OptionsColor();\n    this.opacity = 1;\n  }\n\n  load(data) {\n    if (data === undefined) {\n      return;\n    }\n\n    if (data.color !== undefined) {\n      this.color = OptionsColor.create(this.color, data.color);\n    }\n\n    if (data.opacity !== undefined) {\n      this.opacity = data.opacity;\n    }\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Options/Classes/BackgroundMask/BackgroundMaskCover.js"], "names": ["OptionsColor", "BackgroundMaskCover", "constructor", "color", "opacity", "load", "data", "undefined", "create"], "mappings": "AAAA,SAASA,YAAT,QAA6B,iBAA7B;AACA,OAAO,MAAMC,mBAAN,CAA0B;AAC7BC,EAAAA,WAAW,GAAG;AACV,SAAKC,KAAL,GAAa,IAAIH,YAAJ,EAAb;AACA,SAAKI,OAAL,GAAe,CAAf;AACH;;AACDC,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,QAAIA,IAAI,KAAKC,SAAb,EAAwB;AACpB;AACH;;AACD,QAAID,IAAI,CAACH,KAAL,KAAeI,SAAnB,EAA8B;AAC1B,WAAKJ,KAAL,GAAaH,YAAY,CAACQ,MAAb,CAAoB,KAAKL,KAAzB,EAAgCG,IAAI,CAACH,KAArC,CAAb;AACH;;AACD,QAAIG,IAAI,CAACF,OAAL,KAAiBG,SAArB,EAAgC;AAC5B,WAAKH,OAAL,GAAeE,IAAI,CAACF,OAApB;AACH;AACJ;;AAf4B", "sourcesContent": ["import { OptionsColor } from \"../OptionsColor\";\nexport class BackgroundMaskCover {\n    constructor() {\n        this.color = new OptionsColor();\n        this.opacity = 1;\n    }\n    load(data) {\n        if (data === undefined) {\n            return;\n        }\n        if (data.color !== undefined) {\n            this.color = OptionsColor.create(this.color, data.color);\n        }\n        if (data.opacity !== undefined) {\n            this.opacity = data.opacity;\n        }\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
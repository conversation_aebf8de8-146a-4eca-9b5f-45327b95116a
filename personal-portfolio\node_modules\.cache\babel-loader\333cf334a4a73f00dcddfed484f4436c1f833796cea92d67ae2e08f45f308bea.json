{"ast": null, "code": "import { executeOnSingleOrMultiple, safeMatchMedia } from \"../../Utils/Utils.js\";\nimport { millisecondsToSeconds, mouseDownEvent, mouseLeaveEvent, mouseMoveEvent, mouseOutEvent, mouseUpEvent, resizeEvent, touchCancelEvent, touchEndEvent, touchMoveEvent, touchStartEvent, visibilityChangeEvent } from \"./Constants.js\";\nimport { InteractivityDetect } from \"../../Enums/InteractivityDetect.js\";\nimport { isBoolean } from \"../../Utils/TypeUtils.js\";\nconst double = 2;\nfunction manageListener(element, event, handler, add, options) {\n  if (add) {\n    let addOptions = {\n      passive: true\n    };\n    if (isBoolean(options)) {\n      addOptions.capture = options;\n    } else if (options !== undefined) {\n      addOptions = options;\n    }\n    element.addEventListener(event, handler, addOptions);\n  } else {\n    const removeOptions = options;\n    element.removeEventListener(event, handler, removeOptions);\n  }\n}\nexport class EventListeners {\n  constructor(container) {\n    this.container = container;\n    this._doMouseTouchClick = e => {\n      const container = this.container,\n        options = container.actualOptions;\n      if (this._canPush) {\n        const mouseInteractivity = container.interactivity.mouse,\n          mousePos = mouseInteractivity.position;\n        if (!mousePos) {\n          return;\n        }\n        mouseInteractivity.clickPosition = {\n          ...mousePos\n        };\n        mouseInteractivity.clickTime = new Date().getTime();\n        const onClick = options.interactivity.events.onClick;\n        executeOnSingleOrMultiple(onClick.mode, mode => this.container.handleClickMode(mode));\n      }\n      if (e.type === \"touchend\") {\n        const touchDelay = 500;\n        setTimeout(() => this._mouseTouchFinish(), touchDelay);\n      }\n    };\n    this._handleThemeChange = e => {\n      const mediaEvent = e,\n        container = this.container,\n        options = container.options,\n        defaultThemes = options.defaultThemes,\n        themeName = mediaEvent.matches ? defaultThemes.dark : defaultThemes.light,\n        theme = options.themes.find(theme => theme.name === themeName);\n      if (theme?.default.auto) {\n        void container.loadTheme(themeName);\n      }\n    };\n    this._handleVisibilityChange = () => {\n      const container = this.container,\n        options = container.actualOptions;\n      this._mouseTouchFinish();\n      if (!options.pauseOnBlur) {\n        return;\n      }\n      if (document?.hidden) {\n        container.pageHidden = true;\n        container.pause();\n      } else {\n        container.pageHidden = false;\n        if (container.animationStatus) {\n          void container.play(true);\n        } else {\n          void container.draw(true);\n        }\n      }\n    };\n    this._handleWindowResize = () => {\n      if (this._resizeTimeout) {\n        clearTimeout(this._resizeTimeout);\n        delete this._resizeTimeout;\n      }\n      const handleResize = async () => {\n        const canvas = this.container.canvas;\n        await canvas?.windowResize();\n      };\n      this._resizeTimeout = setTimeout(() => void handleResize(), this.container.actualOptions.interactivity.events.resize.delay * millisecondsToSeconds);\n    };\n    this._manageInteractivityListeners = (mouseLeaveTmpEvent, add) => {\n      const handlers = this._handlers,\n        container = this.container,\n        options = container.actualOptions;\n      const interactivityEl = container.interactivity.element;\n      if (!interactivityEl) {\n        return;\n      }\n      const html = interactivityEl,\n        canvasEl = container.canvas.element;\n      if (canvasEl) {\n        canvasEl.style.pointerEvents = html === canvasEl ? \"initial\" : \"none\";\n      }\n      if (!(options.interactivity.events.onHover.enable || options.interactivity.events.onClick.enable)) {\n        return;\n      }\n      manageListener(interactivityEl, mouseMoveEvent, handlers.mouseMove, add);\n      manageListener(interactivityEl, touchStartEvent, handlers.touchStart, add);\n      manageListener(interactivityEl, touchMoveEvent, handlers.touchMove, add);\n      if (!options.interactivity.events.onClick.enable) {\n        manageListener(interactivityEl, touchEndEvent, handlers.touchEnd, add);\n      } else {\n        manageListener(interactivityEl, touchEndEvent, handlers.touchEndClick, add);\n        manageListener(interactivityEl, mouseUpEvent, handlers.mouseUp, add);\n        manageListener(interactivityEl, mouseDownEvent, handlers.mouseDown, add);\n      }\n      manageListener(interactivityEl, mouseLeaveTmpEvent, handlers.mouseLeave, add);\n      manageListener(interactivityEl, touchCancelEvent, handlers.touchCancel, add);\n    };\n    this._manageListeners = add => {\n      const handlers = this._handlers,\n        container = this.container,\n        options = container.actualOptions,\n        detectType = options.interactivity.detectsOn,\n        canvasEl = container.canvas.element;\n      let mouseLeaveTmpEvent = mouseLeaveEvent;\n      if (detectType === InteractivityDetect.window) {\n        container.interactivity.element = window;\n        mouseLeaveTmpEvent = mouseOutEvent;\n      } else if (detectType === InteractivityDetect.parent && canvasEl) {\n        container.interactivity.element = canvasEl.parentElement ?? canvasEl.parentNode;\n      } else {\n        container.interactivity.element = canvasEl;\n      }\n      this._manageMediaMatch(add);\n      this._manageResize(add);\n      this._manageInteractivityListeners(mouseLeaveTmpEvent, add);\n      if (document) {\n        manageListener(document, visibilityChangeEvent, handlers.visibilityChange, add, false);\n      }\n    };\n    this._manageMediaMatch = add => {\n      const handlers = this._handlers,\n        mediaMatch = safeMatchMedia(\"(prefers-color-scheme: dark)\");\n      if (!mediaMatch) {\n        return;\n      }\n      if (mediaMatch.addEventListener !== undefined) {\n        manageListener(mediaMatch, \"change\", handlers.themeChange, add);\n        return;\n      }\n      if (mediaMatch.addListener === undefined) {\n        return;\n      }\n      if (add) {\n        mediaMatch.addListener(handlers.oldThemeChange);\n      } else {\n        mediaMatch.removeListener(handlers.oldThemeChange);\n      }\n    };\n    this._manageResize = add => {\n      const handlers = this._handlers,\n        container = this.container,\n        options = container.actualOptions;\n      if (!options.interactivity.events.resize) {\n        return;\n      }\n      if (typeof ResizeObserver === \"undefined\") {\n        manageListener(window, resizeEvent, handlers.resize, add);\n        return;\n      }\n      const canvasEl = container.canvas.element;\n      if (this._resizeObserver && !add) {\n        if (canvasEl) {\n          this._resizeObserver.unobserve(canvasEl);\n        }\n        this._resizeObserver.disconnect();\n        delete this._resizeObserver;\n      } else if (!this._resizeObserver && add && canvasEl) {\n        this._resizeObserver = new ResizeObserver(entries => {\n          const entry = entries.find(e => e.target === canvasEl);\n          if (!entry) {\n            return;\n          }\n          this._handleWindowResize();\n        });\n        this._resizeObserver.observe(canvasEl);\n      }\n    };\n    this._mouseDown = () => {\n      const {\n        interactivity\n      } = this.container;\n      if (!interactivity) {\n        return;\n      }\n      const {\n        mouse\n      } = interactivity;\n      mouse.clicking = true;\n      mouse.downPosition = mouse.position;\n    };\n    this._mouseTouchClick = e => {\n      const container = this.container,\n        options = container.actualOptions,\n        {\n          mouse\n        } = container.interactivity;\n      mouse.inside = true;\n      let handled = false;\n      const mousePosition = mouse.position;\n      if (!mousePosition || !options.interactivity.events.onClick.enable) {\n        return;\n      }\n      for (const [, plugin] of container.plugins) {\n        if (!plugin.clickPositionValid) {\n          continue;\n        }\n        handled = plugin.clickPositionValid(mousePosition);\n        if (handled) {\n          break;\n        }\n      }\n      if (!handled) {\n        this._doMouseTouchClick(e);\n      }\n      mouse.clicking = false;\n    };\n    this._mouseTouchFinish = () => {\n      const interactivity = this.container.interactivity;\n      if (!interactivity) {\n        return;\n      }\n      const mouse = interactivity.mouse;\n      delete mouse.position;\n      delete mouse.clickPosition;\n      delete mouse.downPosition;\n      interactivity.status = mouseLeaveEvent;\n      mouse.inside = false;\n      mouse.clicking = false;\n    };\n    this._mouseTouchMove = e => {\n      const container = this.container,\n        options = container.actualOptions,\n        interactivity = container.interactivity,\n        canvasEl = container.canvas.element;\n      if (!interactivity?.element) {\n        return;\n      }\n      interactivity.mouse.inside = true;\n      let pos;\n      if (e.type.startsWith(\"pointer\")) {\n        this._canPush = true;\n        const mouseEvent = e;\n        if (interactivity.element === window) {\n          if (canvasEl) {\n            const clientRect = canvasEl.getBoundingClientRect();\n            pos = {\n              x: mouseEvent.clientX - clientRect.left,\n              y: mouseEvent.clientY - clientRect.top\n            };\n          }\n        } else if (options.interactivity.detectsOn === InteractivityDetect.parent) {\n          const source = mouseEvent.target,\n            target = mouseEvent.currentTarget;\n          if (source && target && canvasEl) {\n            const sourceRect = source.getBoundingClientRect(),\n              targetRect = target.getBoundingClientRect(),\n              canvasRect = canvasEl.getBoundingClientRect();\n            pos = {\n              x: mouseEvent.offsetX + double * sourceRect.left - (targetRect.left + canvasRect.left),\n              y: mouseEvent.offsetY + double * sourceRect.top - (targetRect.top + canvasRect.top)\n            };\n          } else {\n            pos = {\n              x: mouseEvent.offsetX ?? mouseEvent.clientX,\n              y: mouseEvent.offsetY ?? mouseEvent.clientY\n            };\n          }\n        } else if (mouseEvent.target === canvasEl) {\n          pos = {\n            x: mouseEvent.offsetX ?? mouseEvent.clientX,\n            y: mouseEvent.offsetY ?? mouseEvent.clientY\n          };\n        }\n      } else {\n        this._canPush = e.type !== \"touchmove\";\n        if (canvasEl) {\n          const touchEvent = e,\n            lengthOffset = 1,\n            lastTouch = touchEvent.touches[touchEvent.touches.length - lengthOffset],\n            canvasRect = canvasEl.getBoundingClientRect(),\n            defaultCoordinate = 0;\n          pos = {\n            x: lastTouch.clientX - (canvasRect.left ?? defaultCoordinate),\n            y: lastTouch.clientY - (canvasRect.top ?? defaultCoordinate)\n          };\n        }\n      }\n      const pxRatio = container.retina.pixelRatio;\n      if (pos) {\n        pos.x *= pxRatio;\n        pos.y *= pxRatio;\n      }\n      interactivity.mouse.position = pos;\n      interactivity.status = mouseMoveEvent;\n    };\n    this._touchEnd = e => {\n      const evt = e,\n        touches = Array.from(evt.changedTouches);\n      for (const touch of touches) {\n        this._touches.delete(touch.identifier);\n      }\n      this._mouseTouchFinish();\n    };\n    this._touchEndClick = e => {\n      const evt = e,\n        touches = Array.from(evt.changedTouches);\n      for (const touch of touches) {\n        this._touches.delete(touch.identifier);\n      }\n      this._mouseTouchClick(e);\n    };\n    this._touchStart = e => {\n      const evt = e,\n        touches = Array.from(evt.changedTouches);\n      for (const touch of touches) {\n        this._touches.set(touch.identifier, performance.now());\n      }\n      this._mouseTouchMove(e);\n    };\n    this._canPush = true;\n    this._touches = new Map();\n    this._handlers = {\n      mouseDown: () => this._mouseDown(),\n      mouseLeave: () => this._mouseTouchFinish(),\n      mouseMove: e => this._mouseTouchMove(e),\n      mouseUp: e => this._mouseTouchClick(e),\n      touchStart: e => this._touchStart(e),\n      touchMove: e => this._mouseTouchMove(e),\n      touchEnd: e => this._touchEnd(e),\n      touchCancel: e => this._touchEnd(e),\n      touchEndClick: e => this._touchEndClick(e),\n      visibilityChange: () => this._handleVisibilityChange(),\n      themeChange: e => this._handleThemeChange(e),\n      oldThemeChange: e => this._handleThemeChange(e),\n      resize: () => {\n        this._handleWindowResize();\n      }\n    };\n  }\n  addListeners() {\n    this._manageListeners(true);\n  }\n  removeListeners() {\n    this._manageListeners(false);\n  }\n}", "map": {"version": 3, "names": ["executeOnSingleOrMultiple", "safeMatchMedia", "millisecondsToSeconds", "mouseDownEvent", "mouseLeaveEvent", "mouseMoveEvent", "mouseOutEvent", "mouseUpEvent", "resizeEvent", "touchCancelEvent", "touchEndEvent", "touchMoveEvent", "touchStartEvent", "visibilityChangeEvent", "InteractivityDetect", "isBoolean", "double", "manageListener", "element", "event", "handler", "add", "options", "addOptions", "passive", "capture", "undefined", "addEventListener", "removeOptions", "removeEventListener", "EventListeners", "constructor", "container", "_doMouseTouchClick", "e", "actualOptions", "_canPush", "mouseInteractivity", "interactivity", "mouse", "mousePos", "position", "clickPosition", "clickTime", "Date", "getTime", "onClick", "events", "mode", "handleClickMode", "type", "touchDelay", "setTimeout", "_mouseTouchFinish", "_handleThemeChange", "mediaEvent", "defaultThemes", "themeName", "matches", "dark", "light", "theme", "themes", "find", "name", "default", "auto", "loadTheme", "_handleVisibilityChange", "pauseOnBlur", "document", "hidden", "pageHidden", "pause", "animationStatus", "play", "draw", "_handleWindowResize", "_resizeTimeout", "clearTimeout", "handleResize", "canvas", "windowResize", "resize", "delay", "_manageInteractivityListeners", "mouseLeaveTmpEvent", "handlers", "_handlers", "interactivityEl", "html", "canvasEl", "style", "pointerEvents", "onHover", "enable", "mouseMove", "touchStart", "touchMove", "touchEnd", "touchEndClick", "mouseUp", "mouseDown", "mouseLeave", "touchCancel", "_manageListeners", "detectType", "detectsOn", "window", "parent", "parentElement", "parentNode", "_manageMediaMatch", "_manageResize", "visibilityChange", "mediaMatch", "themeChange", "addListener", "oldThemeChange", "removeListener", "ResizeObserver", "_resizeObserver", "unobserve", "disconnect", "entries", "entry", "target", "observe", "_mouseDown", "clicking", "downPosition", "_mouseTouchClick", "inside", "handled", "mousePosition", "plugin", "plugins", "clickPositionValid", "status", "_mouseTouchMove", "pos", "startsWith", "mouseEvent", "clientRect", "getBoundingClientRect", "x", "clientX", "left", "y", "clientY", "top", "source", "currentTarget", "sourceRect", "targetRect", "canvasRect", "offsetX", "offsetY", "touchEvent", "lengthOffset", "lastTouch", "touches", "length", "defaultCoordinate", "pxRatio", "retina", "pixelRatio", "_touchEnd", "evt", "Array", "from", "changedTouches", "touch", "_touches", "delete", "identifier", "_touchEndClick", "_touchStart", "set", "performance", "now", "Map", "addListeners", "removeListeners"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/engine/browser/Core/Utils/EventListeners.js"], "sourcesContent": ["import { executeOnSingleOrMultiple, safeMatchMedia } from \"../../Utils/Utils.js\";\nimport { millisecondsToSeconds, mouseDownEvent, mouseLeaveEvent, mouseMoveEvent, mouseOutEvent, mouseUpEvent, resizeEvent, touchCancelEvent, touchEndEvent, touchMoveEvent, touchStartEvent, visibilityChangeEvent, } from \"./Constants.js\";\nimport { InteractivityDetect } from \"../../Enums/InteractivityDetect.js\";\nimport { isBoolean } from \"../../Utils/TypeUtils.js\";\nconst double = 2;\nfunction manageListener(element, event, handler, add, options) {\n    if (add) {\n        let addOptions = { passive: true };\n        if (isBoolean(options)) {\n            addOptions.capture = options;\n        }\n        else if (options !== undefined) {\n            addOptions = options;\n        }\n        element.addEventListener(event, handler, addOptions);\n    }\n    else {\n        const removeOptions = options;\n        element.removeEventListener(event, handler, removeOptions);\n    }\n}\nexport class EventListeners {\n    constructor(container) {\n        this.container = container;\n        this._doMouseTouchClick = e => {\n            const container = this.container, options = container.actualOptions;\n            if (this._canPush) {\n                const mouseInteractivity = container.interactivity.mouse, mousePos = mouseInteractivity.position;\n                if (!mousePos) {\n                    return;\n                }\n                mouseInteractivity.clickPosition = { ...mousePos };\n                mouseInteractivity.clickTime = new Date().getTime();\n                const onClick = options.interactivity.events.onClick;\n                executeOnSingleOrMultiple(onClick.mode, mode => this.container.handleClickMode(mode));\n            }\n            if (e.type === \"touchend\") {\n                const touchDelay = 500;\n                setTimeout(() => this._mouseTouchFinish(), touchDelay);\n            }\n        };\n        this._handleThemeChange = (e) => {\n            const mediaEvent = e, container = this.container, options = container.options, defaultThemes = options.defaultThemes, themeName = mediaEvent.matches ? defaultThemes.dark : defaultThemes.light, theme = options.themes.find(theme => theme.name === themeName);\n            if (theme?.default.auto) {\n                void container.loadTheme(themeName);\n            }\n        };\n        this._handleVisibilityChange = () => {\n            const container = this.container, options = container.actualOptions;\n            this._mouseTouchFinish();\n            if (!options.pauseOnBlur) {\n                return;\n            }\n            if (document?.hidden) {\n                container.pageHidden = true;\n                container.pause();\n            }\n            else {\n                container.pageHidden = false;\n                if (container.animationStatus) {\n                    void container.play(true);\n                }\n                else {\n                    void container.draw(true);\n                }\n            }\n        };\n        this._handleWindowResize = () => {\n            if (this._resizeTimeout) {\n                clearTimeout(this._resizeTimeout);\n                delete this._resizeTimeout;\n            }\n            const handleResize = async () => {\n                const canvas = this.container.canvas;\n                await canvas?.windowResize();\n            };\n            this._resizeTimeout = setTimeout(() => void handleResize(), this.container.actualOptions.interactivity.events.resize.delay * millisecondsToSeconds);\n        };\n        this._manageInteractivityListeners = (mouseLeaveTmpEvent, add) => {\n            const handlers = this._handlers, container = this.container, options = container.actualOptions;\n            const interactivityEl = container.interactivity.element;\n            if (!interactivityEl) {\n                return;\n            }\n            const html = interactivityEl, canvasEl = container.canvas.element;\n            if (canvasEl) {\n                canvasEl.style.pointerEvents = html === canvasEl ? \"initial\" : \"none\";\n            }\n            if (!(options.interactivity.events.onHover.enable || options.interactivity.events.onClick.enable)) {\n                return;\n            }\n            manageListener(interactivityEl, mouseMoveEvent, handlers.mouseMove, add);\n            manageListener(interactivityEl, touchStartEvent, handlers.touchStart, add);\n            manageListener(interactivityEl, touchMoveEvent, handlers.touchMove, add);\n            if (!options.interactivity.events.onClick.enable) {\n                manageListener(interactivityEl, touchEndEvent, handlers.touchEnd, add);\n            }\n            else {\n                manageListener(interactivityEl, touchEndEvent, handlers.touchEndClick, add);\n                manageListener(interactivityEl, mouseUpEvent, handlers.mouseUp, add);\n                manageListener(interactivityEl, mouseDownEvent, handlers.mouseDown, add);\n            }\n            manageListener(interactivityEl, mouseLeaveTmpEvent, handlers.mouseLeave, add);\n            manageListener(interactivityEl, touchCancelEvent, handlers.touchCancel, add);\n        };\n        this._manageListeners = add => {\n            const handlers = this._handlers, container = this.container, options = container.actualOptions, detectType = options.interactivity.detectsOn, canvasEl = container.canvas.element;\n            let mouseLeaveTmpEvent = mouseLeaveEvent;\n            if (detectType === InteractivityDetect.window) {\n                container.interactivity.element = window;\n                mouseLeaveTmpEvent = mouseOutEvent;\n            }\n            else if (detectType === InteractivityDetect.parent && canvasEl) {\n                container.interactivity.element = canvasEl.parentElement ?? canvasEl.parentNode;\n            }\n            else {\n                container.interactivity.element = canvasEl;\n            }\n            this._manageMediaMatch(add);\n            this._manageResize(add);\n            this._manageInteractivityListeners(mouseLeaveTmpEvent, add);\n            if (document) {\n                manageListener(document, visibilityChangeEvent, handlers.visibilityChange, add, false);\n            }\n        };\n        this._manageMediaMatch = add => {\n            const handlers = this._handlers, mediaMatch = safeMatchMedia(\"(prefers-color-scheme: dark)\");\n            if (!mediaMatch) {\n                return;\n            }\n            if (mediaMatch.addEventListener !== undefined) {\n                manageListener(mediaMatch, \"change\", handlers.themeChange, add);\n                return;\n            }\n            if (mediaMatch.addListener === undefined) {\n                return;\n            }\n            if (add) {\n                mediaMatch.addListener(handlers.oldThemeChange);\n            }\n            else {\n                mediaMatch.removeListener(handlers.oldThemeChange);\n            }\n        };\n        this._manageResize = add => {\n            const handlers = this._handlers, container = this.container, options = container.actualOptions;\n            if (!options.interactivity.events.resize) {\n                return;\n            }\n            if (typeof ResizeObserver === \"undefined\") {\n                manageListener(window, resizeEvent, handlers.resize, add);\n                return;\n            }\n            const canvasEl = container.canvas.element;\n            if (this._resizeObserver && !add) {\n                if (canvasEl) {\n                    this._resizeObserver.unobserve(canvasEl);\n                }\n                this._resizeObserver.disconnect();\n                delete this._resizeObserver;\n            }\n            else if (!this._resizeObserver && add && canvasEl) {\n                this._resizeObserver = new ResizeObserver((entries) => {\n                    const entry = entries.find(e => e.target === canvasEl);\n                    if (!entry) {\n                        return;\n                    }\n                    this._handleWindowResize();\n                });\n                this._resizeObserver.observe(canvasEl);\n            }\n        };\n        this._mouseDown = () => {\n            const { interactivity } = this.container;\n            if (!interactivity) {\n                return;\n            }\n            const { mouse } = interactivity;\n            mouse.clicking = true;\n            mouse.downPosition = mouse.position;\n        };\n        this._mouseTouchClick = e => {\n            const container = this.container, options = container.actualOptions, { mouse } = container.interactivity;\n            mouse.inside = true;\n            let handled = false;\n            const mousePosition = mouse.position;\n            if (!mousePosition || !options.interactivity.events.onClick.enable) {\n                return;\n            }\n            for (const [, plugin] of container.plugins) {\n                if (!plugin.clickPositionValid) {\n                    continue;\n                }\n                handled = plugin.clickPositionValid(mousePosition);\n                if (handled) {\n                    break;\n                }\n            }\n            if (!handled) {\n                this._doMouseTouchClick(e);\n            }\n            mouse.clicking = false;\n        };\n        this._mouseTouchFinish = () => {\n            const interactivity = this.container.interactivity;\n            if (!interactivity) {\n                return;\n            }\n            const mouse = interactivity.mouse;\n            delete mouse.position;\n            delete mouse.clickPosition;\n            delete mouse.downPosition;\n            interactivity.status = mouseLeaveEvent;\n            mouse.inside = false;\n            mouse.clicking = false;\n        };\n        this._mouseTouchMove = e => {\n            const container = this.container, options = container.actualOptions, interactivity = container.interactivity, canvasEl = container.canvas.element;\n            if (!interactivity?.element) {\n                return;\n            }\n            interactivity.mouse.inside = true;\n            let pos;\n            if (e.type.startsWith(\"pointer\")) {\n                this._canPush = true;\n                const mouseEvent = e;\n                if (interactivity.element === window) {\n                    if (canvasEl) {\n                        const clientRect = canvasEl.getBoundingClientRect();\n                        pos = {\n                            x: mouseEvent.clientX - clientRect.left,\n                            y: mouseEvent.clientY - clientRect.top,\n                        };\n                    }\n                }\n                else if (options.interactivity.detectsOn === InteractivityDetect.parent) {\n                    const source = mouseEvent.target, target = mouseEvent.currentTarget;\n                    if (source && target && canvasEl) {\n                        const sourceRect = source.getBoundingClientRect(), targetRect = target.getBoundingClientRect(), canvasRect = canvasEl.getBoundingClientRect();\n                        pos = {\n                            x: mouseEvent.offsetX + double * sourceRect.left - (targetRect.left + canvasRect.left),\n                            y: mouseEvent.offsetY + double * sourceRect.top - (targetRect.top + canvasRect.top),\n                        };\n                    }\n                    else {\n                        pos = {\n                            x: mouseEvent.offsetX ?? mouseEvent.clientX,\n                            y: mouseEvent.offsetY ?? mouseEvent.clientY,\n                        };\n                    }\n                }\n                else if (mouseEvent.target === canvasEl) {\n                    pos = {\n                        x: mouseEvent.offsetX ?? mouseEvent.clientX,\n                        y: mouseEvent.offsetY ?? mouseEvent.clientY,\n                    };\n                }\n            }\n            else {\n                this._canPush = e.type !== \"touchmove\";\n                if (canvasEl) {\n                    const touchEvent = e, lengthOffset = 1, lastTouch = touchEvent.touches[touchEvent.touches.length - lengthOffset], canvasRect = canvasEl.getBoundingClientRect(), defaultCoordinate = 0;\n                    pos = {\n                        x: lastTouch.clientX - (canvasRect.left ?? defaultCoordinate),\n                        y: lastTouch.clientY - (canvasRect.top ?? defaultCoordinate),\n                    };\n                }\n            }\n            const pxRatio = container.retina.pixelRatio;\n            if (pos) {\n                pos.x *= pxRatio;\n                pos.y *= pxRatio;\n            }\n            interactivity.mouse.position = pos;\n            interactivity.status = mouseMoveEvent;\n        };\n        this._touchEnd = e => {\n            const evt = e, touches = Array.from(evt.changedTouches);\n            for (const touch of touches) {\n                this._touches.delete(touch.identifier);\n            }\n            this._mouseTouchFinish();\n        };\n        this._touchEndClick = e => {\n            const evt = e, touches = Array.from(evt.changedTouches);\n            for (const touch of touches) {\n                this._touches.delete(touch.identifier);\n            }\n            this._mouseTouchClick(e);\n        };\n        this._touchStart = e => {\n            const evt = e, touches = Array.from(evt.changedTouches);\n            for (const touch of touches) {\n                this._touches.set(touch.identifier, performance.now());\n            }\n            this._mouseTouchMove(e);\n        };\n        this._canPush = true;\n        this._touches = new Map();\n        this._handlers = {\n            mouseDown: () => this._mouseDown(),\n            mouseLeave: () => this._mouseTouchFinish(),\n            mouseMove: (e) => this._mouseTouchMove(e),\n            mouseUp: (e) => this._mouseTouchClick(e),\n            touchStart: (e) => this._touchStart(e),\n            touchMove: (e) => this._mouseTouchMove(e),\n            touchEnd: (e) => this._touchEnd(e),\n            touchCancel: (e) => this._touchEnd(e),\n            touchEndClick: (e) => this._touchEndClick(e),\n            visibilityChange: () => this._handleVisibilityChange(),\n            themeChange: (e) => this._handleThemeChange(e),\n            oldThemeChange: (e) => this._handleThemeChange(e),\n            resize: () => {\n                this._handleWindowResize();\n            },\n        };\n    }\n    addListeners() {\n        this._manageListeners(true);\n    }\n    removeListeners() {\n        this._manageListeners(false);\n    }\n}\n"], "mappings": "AAAA,SAASA,yBAAyB,EAAEC,cAAc,QAAQ,sBAAsB;AAChF,SAASC,qBAAqB,EAAEC,cAAc,EAAEC,eAAe,EAAEC,cAAc,EAAEC,aAAa,EAAEC,YAAY,EAAEC,WAAW,EAAEC,gBAAgB,EAAEC,aAAa,EAAEC,cAAc,EAAEC,eAAe,EAAEC,qBAAqB,QAAS,gBAAgB;AAC3O,SAASC,mBAAmB,QAAQ,oCAAoC;AACxE,SAASC,SAAS,QAAQ,0BAA0B;AACpD,MAAMC,MAAM,GAAG,CAAC;AAChB,SAASC,cAAcA,CAACC,OAAO,EAAEC,KAAK,EAAEC,OAAO,EAAEC,GAAG,EAAEC,OAAO,EAAE;EAC3D,IAAID,GAAG,EAAE;IACL,IAAIE,UAAU,GAAG;MAAEC,OAAO,EAAE;IAAK,CAAC;IAClC,IAAIT,SAAS,CAACO,OAAO,CAAC,EAAE;MACpBC,UAAU,CAACE,OAAO,GAAGH,OAAO;IAChC,CAAC,MACI,IAAIA,OAAO,KAAKI,SAAS,EAAE;MAC5BH,UAAU,GAAGD,OAAO;IACxB;IACAJ,OAAO,CAACS,gBAAgB,CAACR,KAAK,EAAEC,OAAO,EAAEG,UAAU,CAAC;EACxD,CAAC,MACI;IACD,MAAMK,aAAa,GAAGN,OAAO;IAC7BJ,OAAO,CAACW,mBAAmB,CAACV,KAAK,EAAEC,OAAO,EAAEQ,aAAa,CAAC;EAC9D;AACJ;AACA,OAAO,MAAME,cAAc,CAAC;EACxBC,WAAWA,CAACC,SAAS,EAAE;IACnB,IAAI,CAACA,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,kBAAkB,GAAGC,CAAC,IAAI;MAC3B,MAAMF,SAAS,GAAG,IAAI,CAACA,SAAS;QAAEV,OAAO,GAAGU,SAAS,CAACG,aAAa;MACnE,IAAI,IAAI,CAACC,QAAQ,EAAE;QACf,MAAMC,kBAAkB,GAAGL,SAAS,CAACM,aAAa,CAACC,KAAK;UAAEC,QAAQ,GAAGH,kBAAkB,CAACI,QAAQ;QAChG,IAAI,CAACD,QAAQ,EAAE;UACX;QACJ;QACAH,kBAAkB,CAACK,aAAa,GAAG;UAAE,GAAGF;QAAS,CAAC;QAClDH,kBAAkB,CAACM,SAAS,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;QACnD,MAAMC,OAAO,GAAGxB,OAAO,CAACgB,aAAa,CAACS,MAAM,CAACD,OAAO;QACpD9C,yBAAyB,CAAC8C,OAAO,CAACE,IAAI,EAAEA,IAAI,IAAI,IAAI,CAAChB,SAAS,CAACiB,eAAe,CAACD,IAAI,CAAC,CAAC;MACzF;MACA,IAAId,CAAC,CAACgB,IAAI,KAAK,UAAU,EAAE;QACvB,MAAMC,UAAU,GAAG,GAAG;QACtBC,UAAU,CAAC,MAAM,IAAI,CAACC,iBAAiB,CAAC,CAAC,EAAEF,UAAU,CAAC;MAC1D;IACJ,CAAC;IACD,IAAI,CAACG,kBAAkB,GAAIpB,CAAC,IAAK;MAC7B,MAAMqB,UAAU,GAAGrB,CAAC;QAAEF,SAAS,GAAG,IAAI,CAACA,SAAS;QAAEV,OAAO,GAAGU,SAAS,CAACV,OAAO;QAAEkC,aAAa,GAAGlC,OAAO,CAACkC,aAAa;QAAEC,SAAS,GAAGF,UAAU,CAACG,OAAO,GAAGF,aAAa,CAACG,IAAI,GAAGH,aAAa,CAACI,KAAK;QAAEC,KAAK,GAAGvC,OAAO,CAACwC,MAAM,CAACC,IAAI,CAACF,KAAK,IAAIA,KAAK,CAACG,IAAI,KAAKP,SAAS,CAAC;MAC/P,IAAII,KAAK,EAAEI,OAAO,CAACC,IAAI,EAAE;QACrB,KAAKlC,SAAS,CAACmC,SAAS,CAACV,SAAS,CAAC;MACvC;IACJ,CAAC;IACD,IAAI,CAACW,uBAAuB,GAAG,MAAM;MACjC,MAAMpC,SAAS,GAAG,IAAI,CAACA,SAAS;QAAEV,OAAO,GAAGU,SAAS,CAACG,aAAa;MACnE,IAAI,CAACkB,iBAAiB,CAAC,CAAC;MACxB,IAAI,CAAC/B,OAAO,CAAC+C,WAAW,EAAE;QACtB;MACJ;MACA,IAAIC,QAAQ,EAAEC,MAAM,EAAE;QAClBvC,SAAS,CAACwC,UAAU,GAAG,IAAI;QAC3BxC,SAAS,CAACyC,KAAK,CAAC,CAAC;MACrB,CAAC,MACI;QACDzC,SAAS,CAACwC,UAAU,GAAG,KAAK;QAC5B,IAAIxC,SAAS,CAAC0C,eAAe,EAAE;UAC3B,KAAK1C,SAAS,CAAC2C,IAAI,CAAC,IAAI,CAAC;QAC7B,CAAC,MACI;UACD,KAAK3C,SAAS,CAAC4C,IAAI,CAAC,IAAI,CAAC;QAC7B;MACJ;IACJ,CAAC;IACD,IAAI,CAACC,mBAAmB,GAAG,MAAM;MAC7B,IAAI,IAAI,CAACC,cAAc,EAAE;QACrBC,YAAY,CAAC,IAAI,CAACD,cAAc,CAAC;QACjC,OAAO,IAAI,CAACA,cAAc;MAC9B;MACA,MAAME,YAAY,GAAG,MAAAA,CAAA,KAAY;QAC7B,MAAMC,MAAM,GAAG,IAAI,CAACjD,SAAS,CAACiD,MAAM;QACpC,MAAMA,MAAM,EAAEC,YAAY,CAAC,CAAC;MAChC,CAAC;MACD,IAAI,CAACJ,cAAc,GAAG1B,UAAU,CAAC,MAAM,KAAK4B,YAAY,CAAC,CAAC,EAAE,IAAI,CAAChD,SAAS,CAACG,aAAa,CAACG,aAAa,CAACS,MAAM,CAACoC,MAAM,CAACC,KAAK,GAAGlF,qBAAqB,CAAC;IACvJ,CAAC;IACD,IAAI,CAACmF,6BAA6B,GAAG,CAACC,kBAAkB,EAAEjE,GAAG,KAAK;MAC9D,MAAMkE,QAAQ,GAAG,IAAI,CAACC,SAAS;QAAExD,SAAS,GAAG,IAAI,CAACA,SAAS;QAAEV,OAAO,GAAGU,SAAS,CAACG,aAAa;MAC9F,MAAMsD,eAAe,GAAGzD,SAAS,CAACM,aAAa,CAACpB,OAAO;MACvD,IAAI,CAACuE,eAAe,EAAE;QAClB;MACJ;MACA,MAAMC,IAAI,GAAGD,eAAe;QAAEE,QAAQ,GAAG3D,SAAS,CAACiD,MAAM,CAAC/D,OAAO;MACjE,IAAIyE,QAAQ,EAAE;QACVA,QAAQ,CAACC,KAAK,CAACC,aAAa,GAAGH,IAAI,KAAKC,QAAQ,GAAG,SAAS,GAAG,MAAM;MACzE;MACA,IAAI,EAAErE,OAAO,CAACgB,aAAa,CAACS,MAAM,CAAC+C,OAAO,CAACC,MAAM,IAAIzE,OAAO,CAACgB,aAAa,CAACS,MAAM,CAACD,OAAO,CAACiD,MAAM,CAAC,EAAE;QAC/F;MACJ;MACA9E,cAAc,CAACwE,eAAe,EAAEpF,cAAc,EAAEkF,QAAQ,CAACS,SAAS,EAAE3E,GAAG,CAAC;MACxEJ,cAAc,CAACwE,eAAe,EAAE7E,eAAe,EAAE2E,QAAQ,CAACU,UAAU,EAAE5E,GAAG,CAAC;MAC1EJ,cAAc,CAACwE,eAAe,EAAE9E,cAAc,EAAE4E,QAAQ,CAACW,SAAS,EAAE7E,GAAG,CAAC;MACxE,IAAI,CAACC,OAAO,CAACgB,aAAa,CAACS,MAAM,CAACD,OAAO,CAACiD,MAAM,EAAE;QAC9C9E,cAAc,CAACwE,eAAe,EAAE/E,aAAa,EAAE6E,QAAQ,CAACY,QAAQ,EAAE9E,GAAG,CAAC;MAC1E,CAAC,MACI;QACDJ,cAAc,CAACwE,eAAe,EAAE/E,aAAa,EAAE6E,QAAQ,CAACa,aAAa,EAAE/E,GAAG,CAAC;QAC3EJ,cAAc,CAACwE,eAAe,EAAElF,YAAY,EAAEgF,QAAQ,CAACc,OAAO,EAAEhF,GAAG,CAAC;QACpEJ,cAAc,CAACwE,eAAe,EAAEtF,cAAc,EAAEoF,QAAQ,CAACe,SAAS,EAAEjF,GAAG,CAAC;MAC5E;MACAJ,cAAc,CAACwE,eAAe,EAAEH,kBAAkB,EAAEC,QAAQ,CAACgB,UAAU,EAAElF,GAAG,CAAC;MAC7EJ,cAAc,CAACwE,eAAe,EAAEhF,gBAAgB,EAAE8E,QAAQ,CAACiB,WAAW,EAAEnF,GAAG,CAAC;IAChF,CAAC;IACD,IAAI,CAACoF,gBAAgB,GAAGpF,GAAG,IAAI;MAC3B,MAAMkE,QAAQ,GAAG,IAAI,CAACC,SAAS;QAAExD,SAAS,GAAG,IAAI,CAACA,SAAS;QAAEV,OAAO,GAAGU,SAAS,CAACG,aAAa;QAAEuE,UAAU,GAAGpF,OAAO,CAACgB,aAAa,CAACqE,SAAS;QAAEhB,QAAQ,GAAG3D,SAAS,CAACiD,MAAM,CAAC/D,OAAO;MACjL,IAAIoE,kBAAkB,GAAGlF,eAAe;MACxC,IAAIsG,UAAU,KAAK5F,mBAAmB,CAAC8F,MAAM,EAAE;QAC3C5E,SAAS,CAACM,aAAa,CAACpB,OAAO,GAAG0F,MAAM;QACxCtB,kBAAkB,GAAGhF,aAAa;MACtC,CAAC,MACI,IAAIoG,UAAU,KAAK5F,mBAAmB,CAAC+F,MAAM,IAAIlB,QAAQ,EAAE;QAC5D3D,SAAS,CAACM,aAAa,CAACpB,OAAO,GAAGyE,QAAQ,CAACmB,aAAa,IAAInB,QAAQ,CAACoB,UAAU;MACnF,CAAC,MACI;QACD/E,SAAS,CAACM,aAAa,CAACpB,OAAO,GAAGyE,QAAQ;MAC9C;MACA,IAAI,CAACqB,iBAAiB,CAAC3F,GAAG,CAAC;MAC3B,IAAI,CAAC4F,aAAa,CAAC5F,GAAG,CAAC;MACvB,IAAI,CAACgE,6BAA6B,CAACC,kBAAkB,EAAEjE,GAAG,CAAC;MAC3D,IAAIiD,QAAQ,EAAE;QACVrD,cAAc,CAACqD,QAAQ,EAAEzD,qBAAqB,EAAE0E,QAAQ,CAAC2B,gBAAgB,EAAE7F,GAAG,EAAE,KAAK,CAAC;MAC1F;IACJ,CAAC;IACD,IAAI,CAAC2F,iBAAiB,GAAG3F,GAAG,IAAI;MAC5B,MAAMkE,QAAQ,GAAG,IAAI,CAACC,SAAS;QAAE2B,UAAU,GAAGlH,cAAc,CAAC,8BAA8B,CAAC;MAC5F,IAAI,CAACkH,UAAU,EAAE;QACb;MACJ;MACA,IAAIA,UAAU,CAACxF,gBAAgB,KAAKD,SAAS,EAAE;QAC3CT,cAAc,CAACkG,UAAU,EAAE,QAAQ,EAAE5B,QAAQ,CAAC6B,WAAW,EAAE/F,GAAG,CAAC;QAC/D;MACJ;MACA,IAAI8F,UAAU,CAACE,WAAW,KAAK3F,SAAS,EAAE;QACtC;MACJ;MACA,IAAIL,GAAG,EAAE;QACL8F,UAAU,CAACE,WAAW,CAAC9B,QAAQ,CAAC+B,cAAc,CAAC;MACnD,CAAC,MACI;QACDH,UAAU,CAACI,cAAc,CAAChC,QAAQ,CAAC+B,cAAc,CAAC;MACtD;IACJ,CAAC;IACD,IAAI,CAACL,aAAa,GAAG5F,GAAG,IAAI;MACxB,MAAMkE,QAAQ,GAAG,IAAI,CAACC,SAAS;QAAExD,SAAS,GAAG,IAAI,CAACA,SAAS;QAAEV,OAAO,GAAGU,SAAS,CAACG,aAAa;MAC9F,IAAI,CAACb,OAAO,CAACgB,aAAa,CAACS,MAAM,CAACoC,MAAM,EAAE;QACtC;MACJ;MACA,IAAI,OAAOqC,cAAc,KAAK,WAAW,EAAE;QACvCvG,cAAc,CAAC2F,MAAM,EAAEpG,WAAW,EAAE+E,QAAQ,CAACJ,MAAM,EAAE9D,GAAG,CAAC;QACzD;MACJ;MACA,MAAMsE,QAAQ,GAAG3D,SAAS,CAACiD,MAAM,CAAC/D,OAAO;MACzC,IAAI,IAAI,CAACuG,eAAe,IAAI,CAACpG,GAAG,EAAE;QAC9B,IAAIsE,QAAQ,EAAE;UACV,IAAI,CAAC8B,eAAe,CAACC,SAAS,CAAC/B,QAAQ,CAAC;QAC5C;QACA,IAAI,CAAC8B,eAAe,CAACE,UAAU,CAAC,CAAC;QACjC,OAAO,IAAI,CAACF,eAAe;MAC/B,CAAC,MACI,IAAI,CAAC,IAAI,CAACA,eAAe,IAAIpG,GAAG,IAAIsE,QAAQ,EAAE;QAC/C,IAAI,CAAC8B,eAAe,GAAG,IAAID,cAAc,CAAEI,OAAO,IAAK;UACnD,MAAMC,KAAK,GAAGD,OAAO,CAAC7D,IAAI,CAAC7B,CAAC,IAAIA,CAAC,CAAC4F,MAAM,KAAKnC,QAAQ,CAAC;UACtD,IAAI,CAACkC,KAAK,EAAE;YACR;UACJ;UACA,IAAI,CAAChD,mBAAmB,CAAC,CAAC;QAC9B,CAAC,CAAC;QACF,IAAI,CAAC4C,eAAe,CAACM,OAAO,CAACpC,QAAQ,CAAC;MAC1C;IACJ,CAAC;IACD,IAAI,CAACqC,UAAU,GAAG,MAAM;MACpB,MAAM;QAAE1F;MAAc,CAAC,GAAG,IAAI,CAACN,SAAS;MACxC,IAAI,CAACM,aAAa,EAAE;QAChB;MACJ;MACA,MAAM;QAAEC;MAAM,CAAC,GAAGD,aAAa;MAC/BC,KAAK,CAAC0F,QAAQ,GAAG,IAAI;MACrB1F,KAAK,CAAC2F,YAAY,GAAG3F,KAAK,CAACE,QAAQ;IACvC,CAAC;IACD,IAAI,CAAC0F,gBAAgB,GAAGjG,CAAC,IAAI;MACzB,MAAMF,SAAS,GAAG,IAAI,CAACA,SAAS;QAAEV,OAAO,GAAGU,SAAS,CAACG,aAAa;QAAE;UAAEI;QAAM,CAAC,GAAGP,SAAS,CAACM,aAAa;MACxGC,KAAK,CAAC6F,MAAM,GAAG,IAAI;MACnB,IAAIC,OAAO,GAAG,KAAK;MACnB,MAAMC,aAAa,GAAG/F,KAAK,CAACE,QAAQ;MACpC,IAAI,CAAC6F,aAAa,IAAI,CAAChH,OAAO,CAACgB,aAAa,CAACS,MAAM,CAACD,OAAO,CAACiD,MAAM,EAAE;QAChE;MACJ;MACA,KAAK,MAAM,GAAGwC,MAAM,CAAC,IAAIvG,SAAS,CAACwG,OAAO,EAAE;QACxC,IAAI,CAACD,MAAM,CAACE,kBAAkB,EAAE;UAC5B;QACJ;QACAJ,OAAO,GAAGE,MAAM,CAACE,kBAAkB,CAACH,aAAa,CAAC;QAClD,IAAID,OAAO,EAAE;UACT;QACJ;MACJ;MACA,IAAI,CAACA,OAAO,EAAE;QACV,IAAI,CAACpG,kBAAkB,CAACC,CAAC,CAAC;MAC9B;MACAK,KAAK,CAAC0F,QAAQ,GAAG,KAAK;IAC1B,CAAC;IACD,IAAI,CAAC5E,iBAAiB,GAAG,MAAM;MAC3B,MAAMf,aAAa,GAAG,IAAI,CAACN,SAAS,CAACM,aAAa;MAClD,IAAI,CAACA,aAAa,EAAE;QAChB;MACJ;MACA,MAAMC,KAAK,GAAGD,aAAa,CAACC,KAAK;MACjC,OAAOA,KAAK,CAACE,QAAQ;MACrB,OAAOF,KAAK,CAACG,aAAa;MAC1B,OAAOH,KAAK,CAAC2F,YAAY;MACzB5F,aAAa,CAACoG,MAAM,GAAGtI,eAAe;MACtCmC,KAAK,CAAC6F,MAAM,GAAG,KAAK;MACpB7F,KAAK,CAAC0F,QAAQ,GAAG,KAAK;IAC1B,CAAC;IACD,IAAI,CAACU,eAAe,GAAGzG,CAAC,IAAI;MACxB,MAAMF,SAAS,GAAG,IAAI,CAACA,SAAS;QAAEV,OAAO,GAAGU,SAAS,CAACG,aAAa;QAAEG,aAAa,GAAGN,SAAS,CAACM,aAAa;QAAEqD,QAAQ,GAAG3D,SAAS,CAACiD,MAAM,CAAC/D,OAAO;MACjJ,IAAI,CAACoB,aAAa,EAAEpB,OAAO,EAAE;QACzB;MACJ;MACAoB,aAAa,CAACC,KAAK,CAAC6F,MAAM,GAAG,IAAI;MACjC,IAAIQ,GAAG;MACP,IAAI1G,CAAC,CAACgB,IAAI,CAAC2F,UAAU,CAAC,SAAS,CAAC,EAAE;QAC9B,IAAI,CAACzG,QAAQ,GAAG,IAAI;QACpB,MAAM0G,UAAU,GAAG5G,CAAC;QACpB,IAAII,aAAa,CAACpB,OAAO,KAAK0F,MAAM,EAAE;UAClC,IAAIjB,QAAQ,EAAE;YACV,MAAMoD,UAAU,GAAGpD,QAAQ,CAACqD,qBAAqB,CAAC,CAAC;YACnDJ,GAAG,GAAG;cACFK,CAAC,EAAEH,UAAU,CAACI,OAAO,GAAGH,UAAU,CAACI,IAAI;cACvCC,CAAC,EAAEN,UAAU,CAACO,OAAO,GAAGN,UAAU,CAACO;YACvC,CAAC;UACL;QACJ,CAAC,MACI,IAAIhI,OAAO,CAACgB,aAAa,CAACqE,SAAS,KAAK7F,mBAAmB,CAAC+F,MAAM,EAAE;UACrE,MAAM0C,MAAM,GAAGT,UAAU,CAAChB,MAAM;YAAEA,MAAM,GAAGgB,UAAU,CAACU,aAAa;UACnE,IAAID,MAAM,IAAIzB,MAAM,IAAInC,QAAQ,EAAE;YAC9B,MAAM8D,UAAU,GAAGF,MAAM,CAACP,qBAAqB,CAAC,CAAC;cAAEU,UAAU,GAAG5B,MAAM,CAACkB,qBAAqB,CAAC,CAAC;cAAEW,UAAU,GAAGhE,QAAQ,CAACqD,qBAAqB,CAAC,CAAC;YAC7IJ,GAAG,GAAG;cACFK,CAAC,EAAEH,UAAU,CAACc,OAAO,GAAG5I,MAAM,GAAGyI,UAAU,CAACN,IAAI,IAAIO,UAAU,CAACP,IAAI,GAAGQ,UAAU,CAACR,IAAI,CAAC;cACtFC,CAAC,EAAEN,UAAU,CAACe,OAAO,GAAG7I,MAAM,GAAGyI,UAAU,CAACH,GAAG,IAAII,UAAU,CAACJ,GAAG,GAAGK,UAAU,CAACL,GAAG;YACtF,CAAC;UACL,CAAC,MACI;YACDV,GAAG,GAAG;cACFK,CAAC,EAAEH,UAAU,CAACc,OAAO,IAAId,UAAU,CAACI,OAAO;cAC3CE,CAAC,EAAEN,UAAU,CAACe,OAAO,IAAIf,UAAU,CAACO;YACxC,CAAC;UACL;QACJ,CAAC,MACI,IAAIP,UAAU,CAAChB,MAAM,KAAKnC,QAAQ,EAAE;UACrCiD,GAAG,GAAG;YACFK,CAAC,EAAEH,UAAU,CAACc,OAAO,IAAId,UAAU,CAACI,OAAO;YAC3CE,CAAC,EAAEN,UAAU,CAACe,OAAO,IAAIf,UAAU,CAACO;UACxC,CAAC;QACL;MACJ,CAAC,MACI;QACD,IAAI,CAACjH,QAAQ,GAAGF,CAAC,CAACgB,IAAI,KAAK,WAAW;QACtC,IAAIyC,QAAQ,EAAE;UACV,MAAMmE,UAAU,GAAG5H,CAAC;YAAE6H,YAAY,GAAG,CAAC;YAAEC,SAAS,GAAGF,UAAU,CAACG,OAAO,CAACH,UAAU,CAACG,OAAO,CAACC,MAAM,GAAGH,YAAY,CAAC;YAAEJ,UAAU,GAAGhE,QAAQ,CAACqD,qBAAqB,CAAC,CAAC;YAAEmB,iBAAiB,GAAG,CAAC;UACtLvB,GAAG,GAAG;YACFK,CAAC,EAAEe,SAAS,CAACd,OAAO,IAAIS,UAAU,CAACR,IAAI,IAAIgB,iBAAiB,CAAC;YAC7Df,CAAC,EAAEY,SAAS,CAACX,OAAO,IAAIM,UAAU,CAACL,GAAG,IAAIa,iBAAiB;UAC/D,CAAC;QACL;MACJ;MACA,MAAMC,OAAO,GAAGpI,SAAS,CAACqI,MAAM,CAACC,UAAU;MAC3C,IAAI1B,GAAG,EAAE;QACLA,GAAG,CAACK,CAAC,IAAImB,OAAO;QAChBxB,GAAG,CAACQ,CAAC,IAAIgB,OAAO;MACpB;MACA9H,aAAa,CAACC,KAAK,CAACE,QAAQ,GAAGmG,GAAG;MAClCtG,aAAa,CAACoG,MAAM,GAAGrI,cAAc;IACzC,CAAC;IACD,IAAI,CAACkK,SAAS,GAAGrI,CAAC,IAAI;MAClB,MAAMsI,GAAG,GAAGtI,CAAC;QAAE+H,OAAO,GAAGQ,KAAK,CAACC,IAAI,CAACF,GAAG,CAACG,cAAc,CAAC;MACvD,KAAK,MAAMC,KAAK,IAAIX,OAAO,EAAE;QACzB,IAAI,CAACY,QAAQ,CAACC,MAAM,CAACF,KAAK,CAACG,UAAU,CAAC;MAC1C;MACA,IAAI,CAAC1H,iBAAiB,CAAC,CAAC;IAC5B,CAAC;IACD,IAAI,CAAC2H,cAAc,GAAG9I,CAAC,IAAI;MACvB,MAAMsI,GAAG,GAAGtI,CAAC;QAAE+H,OAAO,GAAGQ,KAAK,CAACC,IAAI,CAACF,GAAG,CAACG,cAAc,CAAC;MACvD,KAAK,MAAMC,KAAK,IAAIX,OAAO,EAAE;QACzB,IAAI,CAACY,QAAQ,CAACC,MAAM,CAACF,KAAK,CAACG,UAAU,CAAC;MAC1C;MACA,IAAI,CAAC5C,gBAAgB,CAACjG,CAAC,CAAC;IAC5B,CAAC;IACD,IAAI,CAAC+I,WAAW,GAAG/I,CAAC,IAAI;MACpB,MAAMsI,GAAG,GAAGtI,CAAC;QAAE+H,OAAO,GAAGQ,KAAK,CAACC,IAAI,CAACF,GAAG,CAACG,cAAc,CAAC;MACvD,KAAK,MAAMC,KAAK,IAAIX,OAAO,EAAE;QACzB,IAAI,CAACY,QAAQ,CAACK,GAAG,CAACN,KAAK,CAACG,UAAU,EAAEI,WAAW,CAACC,GAAG,CAAC,CAAC,CAAC;MAC1D;MACA,IAAI,CAACzC,eAAe,CAACzG,CAAC,CAAC;IAC3B,CAAC;IACD,IAAI,CAACE,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACyI,QAAQ,GAAG,IAAIQ,GAAG,CAAC,CAAC;IACzB,IAAI,CAAC7F,SAAS,GAAG;MACbc,SAAS,EAAEA,CAAA,KAAM,IAAI,CAAC0B,UAAU,CAAC,CAAC;MAClCzB,UAAU,EAAEA,CAAA,KAAM,IAAI,CAAClD,iBAAiB,CAAC,CAAC;MAC1C2C,SAAS,EAAG9D,CAAC,IAAK,IAAI,CAACyG,eAAe,CAACzG,CAAC,CAAC;MACzCmE,OAAO,EAAGnE,CAAC,IAAK,IAAI,CAACiG,gBAAgB,CAACjG,CAAC,CAAC;MACxC+D,UAAU,EAAG/D,CAAC,IAAK,IAAI,CAAC+I,WAAW,CAAC/I,CAAC,CAAC;MACtCgE,SAAS,EAAGhE,CAAC,IAAK,IAAI,CAACyG,eAAe,CAACzG,CAAC,CAAC;MACzCiE,QAAQ,EAAGjE,CAAC,IAAK,IAAI,CAACqI,SAAS,CAACrI,CAAC,CAAC;MAClCsE,WAAW,EAAGtE,CAAC,IAAK,IAAI,CAACqI,SAAS,CAACrI,CAAC,CAAC;MACrCkE,aAAa,EAAGlE,CAAC,IAAK,IAAI,CAAC8I,cAAc,CAAC9I,CAAC,CAAC;MAC5CgF,gBAAgB,EAAEA,CAAA,KAAM,IAAI,CAAC9C,uBAAuB,CAAC,CAAC;MACtDgD,WAAW,EAAGlF,CAAC,IAAK,IAAI,CAACoB,kBAAkB,CAACpB,CAAC,CAAC;MAC9CoF,cAAc,EAAGpF,CAAC,IAAK,IAAI,CAACoB,kBAAkB,CAACpB,CAAC,CAAC;MACjDiD,MAAM,EAAEA,CAAA,KAAM;QACV,IAAI,CAACN,mBAAmB,CAAC,CAAC;MAC9B;IACJ,CAAC;EACL;EACAyG,YAAYA,CAAA,EAAG;IACX,IAAI,CAAC7E,gBAAgB,CAAC,IAAI,CAAC;EAC/B;EACA8E,eAAeA,CAAA,EAAG;IACd,IAAI,CAAC9E,gBAAgB,CAAC,KAAK,CAAC;EAChC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
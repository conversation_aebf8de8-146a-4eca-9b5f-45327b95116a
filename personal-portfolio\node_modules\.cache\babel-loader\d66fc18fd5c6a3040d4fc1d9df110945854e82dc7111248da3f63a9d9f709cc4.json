{"ast": null, "code": "import { Remover } from \"./Remover.js\";\nexport async function loadExternalRemoveInteraction(engine, refresh = true) {\n  await engine.addInteractor(\"externalRemove\", container => {\n    return Promise.resolve(new Remover(container));\n  }, refresh);\n}\nexport * from \"./Options/Classes/Remove.js\";\nexport * from \"./Options/Interfaces/IRemove.js\";", "map": {"version": 3, "names": ["Remover", "loadExternalRemoveInteraction", "engine", "refresh", "addInteractor", "container", "Promise", "resolve"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/interaction-external-remove/browser/index.js"], "sourcesContent": ["import { Remover } from \"./Remover.js\";\nexport async function loadExternalRemoveInteraction(engine, refresh = true) {\n    await engine.addInteractor(\"externalRemove\", container => {\n        return Promise.resolve(new Remover(container));\n    }, refresh);\n}\nexport * from \"./Options/Classes/Remove.js\";\nexport * from \"./Options/Interfaces/IRemove.js\";\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc;AACtC,OAAO,eAAeC,6BAA6BA,CAACC,MAAM,EAAEC,OAAO,GAAG,IAAI,EAAE;EACxE,MAAMD,MAAM,CAACE,aAAa,CAAC,gBAAgB,EAAEC,SAAS,IAAI;IACtD,OAAOC,OAAO,CAACC,OAAO,CAAC,IAAIP,OAAO,CAACK,SAAS,CAAC,CAAC;EAClD,CAAC,EAAEF,OAAO,CAAC;AACf;AACA,cAAc,6BAA6B;AAC3C,cAAc,iCAAiC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
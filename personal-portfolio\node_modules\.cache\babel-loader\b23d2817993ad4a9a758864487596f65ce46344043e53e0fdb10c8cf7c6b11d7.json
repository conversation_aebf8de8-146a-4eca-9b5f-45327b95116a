{"ast": null, "code": "import { executeOnSingleOrMultiple, isArray, isInArray } from \"@tsparticles/engine\";\nimport { Absorber } from \"./Options/Classes/Absorber.js\";\nimport { AbsorberClickMode } from \"./Enums/AbsorberClickMode.js\";\nimport { Absorbers } from \"./Absorbers.js\";\nexport class AbsorbersPlugin {\n  constructor() {\n    this.id = \"absorbers\";\n  }\n  async getPlugin(container) {\n    return Promise.resolve(new Absorbers(container));\n  }\n  loadOptions(options, source) {\n    if (!this.needsPlugin(options) && !this.needsPlugin(source)) {\n      return;\n    }\n    if (source?.absorbers) {\n      options.absorbers = executeOnSingleOrMultiple(source.absorbers, absorber => {\n        const tmp = new Absorber();\n        tmp.load(absorber);\n        return tmp;\n      });\n    }\n    options.interactivity.modes.absorbers = executeOnSingleOrMultiple(source?.interactivity?.modes?.absorbers, absorber => {\n      const tmp = new Absorber();\n      tmp.load(absorber);\n      return tmp;\n    });\n  }\n  needsPlugin(options) {\n    if (!options) {\n      return false;\n    }\n    const absorbers = options.absorbers;\n    if (isArray(absorbers)) {\n      return !!absorbers.length;\n    } else if (absorbers) {\n      return true;\n    } else if (options.interactivity?.events?.onClick?.mode && isInArray(AbsorberClickMode.absorber, options.interactivity.events.onClick.mode)) {\n      return true;\n    }\n    return false;\n  }\n}", "map": {"version": 3, "names": ["executeOnSingleOrMultiple", "isArray", "isInArray", "Absorber", "AbsorberClickMode", "Absorbers", "AbsorbersPlugin", "constructor", "id", "getPlugin", "container", "Promise", "resolve", "loadOptions", "options", "source", "needsPlugin", "absorbers", "absorber", "tmp", "load", "interactivity", "modes", "length", "events", "onClick", "mode"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/plugin-absorbers/browser/AbsorbersPlugin.js"], "sourcesContent": ["import { executeOnSingleOrMultiple, isArray, isInArray, } from \"@tsparticles/engine\";\nimport { Absorber } from \"./Options/Classes/Absorber.js\";\nimport { AbsorberClickMode } from \"./Enums/AbsorberClickMode.js\";\nimport { Absorbers } from \"./Absorbers.js\";\nexport class AbsorbersPlugin {\n    constructor() {\n        this.id = \"absorbers\";\n    }\n    async getPlugin(container) {\n        return Promise.resolve(new Absorbers(container));\n    }\n    loadOptions(options, source) {\n        if (!this.needsPlugin(options) && !this.needsPlugin(source)) {\n            return;\n        }\n        if (source?.absorbers) {\n            options.absorbers = executeOnSingleOrMultiple(source.absorbers, absorber => {\n                const tmp = new Absorber();\n                tmp.load(absorber);\n                return tmp;\n            });\n        }\n        options.interactivity.modes.absorbers = executeOnSingleOrMultiple(source?.interactivity?.modes?.absorbers, absorber => {\n            const tmp = new Absorber();\n            tmp.load(absorber);\n            return tmp;\n        });\n    }\n    needsPlugin(options) {\n        if (!options) {\n            return false;\n        }\n        const absorbers = options.absorbers;\n        if (isArray(absorbers)) {\n            return !!absorbers.length;\n        }\n        else if (absorbers) {\n            return true;\n        }\n        else if (options.interactivity?.events?.onClick?.mode &&\n            isInArray(AbsorberClickMode.absorber, options.interactivity.events.onClick.mode)) {\n            return true;\n        }\n        return false;\n    }\n}\n"], "mappings": "AAAA,SAASA,yBAAyB,EAAEC,OAAO,EAAEC,SAAS,QAAS,qBAAqB;AACpF,SAASC,QAAQ,QAAQ,+BAA+B;AACxD,SAASC,iBAAiB,QAAQ,8BAA8B;AAChE,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,OAAO,MAAMC,eAAe,CAAC;EACzBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,EAAE,GAAG,WAAW;EACzB;EACA,MAAMC,SAASA,CAACC,SAAS,EAAE;IACvB,OAAOC,OAAO,CAACC,OAAO,CAAC,IAAIP,SAAS,CAACK,SAAS,CAAC,CAAC;EACpD;EACAG,WAAWA,CAACC,OAAO,EAAEC,MAAM,EAAE;IACzB,IAAI,CAAC,IAAI,CAACC,WAAW,CAACF,OAAO,CAAC,IAAI,CAAC,IAAI,CAACE,WAAW,CAACD,MAAM,CAAC,EAAE;MACzD;IACJ;IACA,IAAIA,MAAM,EAAEE,SAAS,EAAE;MACnBH,OAAO,CAACG,SAAS,GAAGjB,yBAAyB,CAACe,MAAM,CAACE,SAAS,EAAEC,QAAQ,IAAI;QACxE,MAAMC,GAAG,GAAG,IAAIhB,QAAQ,CAAC,CAAC;QAC1BgB,GAAG,CAACC,IAAI,CAACF,QAAQ,CAAC;QAClB,OAAOC,GAAG;MACd,CAAC,CAAC;IACN;IACAL,OAAO,CAACO,aAAa,CAACC,KAAK,CAACL,SAAS,GAAGjB,yBAAyB,CAACe,MAAM,EAAEM,aAAa,EAAEC,KAAK,EAAEL,SAAS,EAAEC,QAAQ,IAAI;MACnH,MAAMC,GAAG,GAAG,IAAIhB,QAAQ,CAAC,CAAC;MAC1BgB,GAAG,CAACC,IAAI,CAACF,QAAQ,CAAC;MAClB,OAAOC,GAAG;IACd,CAAC,CAAC;EACN;EACAH,WAAWA,CAACF,OAAO,EAAE;IACjB,IAAI,CAACA,OAAO,EAAE;MACV,OAAO,KAAK;IAChB;IACA,MAAMG,SAAS,GAAGH,OAAO,CAACG,SAAS;IACnC,IAAIhB,OAAO,CAACgB,SAAS,CAAC,EAAE;MACpB,OAAO,CAAC,CAACA,SAAS,CAACM,MAAM;IAC7B,CAAC,MACI,IAAIN,SAAS,EAAE;MAChB,OAAO,IAAI;IACf,CAAC,MACI,IAAIH,OAAO,CAACO,aAAa,EAAEG,MAAM,EAAEC,OAAO,EAAEC,IAAI,IACjDxB,SAAS,CAACE,iBAAiB,CAACc,QAAQ,EAAEJ,OAAO,CAACO,aAAa,CAACG,MAAM,CAACC,OAAO,CAACC,IAAI,CAAC,EAAE;MAClF,OAAO,IAAI;IACf;IACA,OAAO,KAAK;EAChB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
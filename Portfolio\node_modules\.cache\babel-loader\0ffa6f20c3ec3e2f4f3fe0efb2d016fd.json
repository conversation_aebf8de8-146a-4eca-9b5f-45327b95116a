{"ast": null, "code": "import { deepExtend, setRangeValue } from \"../../../../Utils\";\nimport { Attract } from \"./Attract\";\nimport { MoveAngle } from \"./MoveAngle\";\nimport { MoveGravity } from \"./MoveGravity\";\nimport { OutModes } from \"./OutModes\";\nimport { Path } from \"./Path/Path\";\nimport { Spin } from \"./Spin\";\nimport { Trail } from \"./Trail\";\nexport class Move {\n  constructor() {\n    this.angle = new MoveAngle();\n    this.attract = new Attract();\n    this.decay = 0;\n    this.distance = {};\n    this.direction = \"none\";\n    this.drift = 0;\n    this.enable = false;\n    this.gravity = new MoveGravity();\n    this.path = new Path();\n    this.outModes = new OutModes();\n    this.random = false;\n    this.size = false;\n    this.speed = 2;\n    this.spin = new Spin();\n    this.straight = false;\n    this.trail = new Trail();\n    this.vibrate = false;\n    this.warp = false;\n  }\n\n  get collisions() {\n    return false;\n  }\n\n  set collisions(value) {}\n\n  get bounce() {\n    return this.collisions;\n  }\n\n  set bounce(value) {\n    this.collisions = value;\n  }\n\n  get out_mode() {\n    return this.outMode;\n  }\n\n  set out_mode(value) {\n    this.outMode = value;\n  }\n\n  get outMode() {\n    return this.outModes.default;\n  }\n\n  set outMode(value) {\n    this.outModes.default = value;\n  }\n\n  get noise() {\n    return this.path;\n  }\n\n  set noise(value) {\n    this.path = value;\n  }\n\n  load(data) {\n    var _a, _b, _c;\n\n    if (data === undefined) {\n      return;\n    }\n\n    if (data.angle !== undefined) {\n      if (typeof data.angle === \"number\") {\n        this.angle.value = data.angle;\n      } else {\n        this.angle.load(data.angle);\n      }\n    }\n\n    this.attract.load(data.attract);\n\n    if (data.decay !== undefined) {\n      this.decay = data.decay;\n    }\n\n    if (data.direction !== undefined) {\n      this.direction = data.direction;\n    }\n\n    if (data.distance !== undefined) {\n      this.distance = typeof data.distance === \"number\" ? {\n        horizontal: data.distance,\n        vertical: data.distance\n      } : deepExtend({}, data.distance);\n    }\n\n    if (data.drift !== undefined) {\n      this.drift = setRangeValue(data.drift);\n    }\n\n    if (data.enable !== undefined) {\n      this.enable = data.enable;\n    }\n\n    this.gravity.load(data.gravity);\n    const outMode = (_a = data.outMode) !== null && _a !== void 0 ? _a : data.out_mode;\n\n    if (data.outModes !== undefined || outMode !== undefined) {\n      if (typeof data.outModes === \"string\" || data.outModes === undefined && outMode !== undefined) {\n        this.outModes.load({\n          default: (_b = data.outModes) !== null && _b !== void 0 ? _b : outMode\n        });\n      } else {\n        this.outModes.load(data.outModes);\n      }\n    }\n\n    this.path.load((_c = data.path) !== null && _c !== void 0 ? _c : data.noise);\n\n    if (data.random !== undefined) {\n      this.random = data.random;\n    }\n\n    if (data.size !== undefined) {\n      this.size = data.size;\n    }\n\n    if (data.speed !== undefined) {\n      this.speed = setRangeValue(data.speed);\n    }\n\n    this.spin.load(data.spin);\n\n    if (data.straight !== undefined) {\n      this.straight = data.straight;\n    }\n\n    this.trail.load(data.trail);\n\n    if (data.vibrate !== undefined) {\n      this.vibrate = data.vibrate;\n    }\n\n    if (data.warp !== undefined) {\n      this.warp = data.warp;\n    }\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Options/Classes/Particles/Move/Move.js"], "names": ["deepExtend", "setRangeValue", "Attract", "MoveAngle", "MoveGravity", "OutModes", "Path", "Spin", "Trail", "Move", "constructor", "angle", "attract", "decay", "distance", "direction", "drift", "enable", "gravity", "path", "outModes", "random", "size", "speed", "spin", "straight", "trail", "vibrate", "warp", "collisions", "value", "bounce", "out_mode", "outMode", "default", "noise", "load", "data", "_a", "_b", "_c", "undefined", "horizontal", "vertical"], "mappings": "AAAA,SAASA,UAAT,EAAqBC,aAArB,QAA0C,mBAA1C;AACA,SAASC,OAAT,QAAwB,WAAxB;AACA,SAASC,SAAT,QAA0B,aAA1B;AACA,SAASC,WAAT,QAA4B,eAA5B;AACA,SAASC,QAAT,QAAyB,YAAzB;AACA,SAASC,IAAT,QAAqB,aAArB;AACA,SAASC,IAAT,QAAqB,QAArB;AACA,SAASC,KAAT,QAAsB,SAAtB;AACA,OAAO,MAAMC,IAAN,CAAW;AACdC,EAAAA,WAAW,GAAG;AACV,SAAKC,KAAL,GAAa,IAAIR,SAAJ,EAAb;AACA,SAAKS,OAAL,GAAe,IAAIV,OAAJ,EAAf;AACA,SAAKW,KAAL,GAAa,CAAb;AACA,SAAKC,QAAL,GAAgB,EAAhB;AACA,SAAKC,SAAL,GAAiB,MAAjB;AACA,SAAKC,KAAL,GAAa,CAAb;AACA,SAAKC,MAAL,GAAc,KAAd;AACA,SAAKC,OAAL,GAAe,IAAId,WAAJ,EAAf;AACA,SAAKe,IAAL,GAAY,IAAIb,IAAJ,EAAZ;AACA,SAAKc,QAAL,GAAgB,IAAIf,QAAJ,EAAhB;AACA,SAAKgB,MAAL,GAAc,KAAd;AACA,SAAKC,IAAL,GAAY,KAAZ;AACA,SAAKC,KAAL,GAAa,CAAb;AACA,SAAKC,IAAL,GAAY,IAAIjB,IAAJ,EAAZ;AACA,SAAKkB,QAAL,GAAgB,KAAhB;AACA,SAAKC,KAAL,GAAa,IAAIlB,KAAJ,EAAb;AACA,SAAKmB,OAAL,GAAe,KAAf;AACA,SAAKC,IAAL,GAAY,KAAZ;AACH;;AACa,MAAVC,UAAU,GAAG;AACb,WAAO,KAAP;AACH;;AACa,MAAVA,UAAU,CAACC,KAAD,EAAQ,CACrB;;AACS,MAANC,MAAM,GAAG;AACT,WAAO,KAAKF,UAAZ;AACH;;AACS,MAANE,MAAM,CAACD,KAAD,EAAQ;AACd,SAAKD,UAAL,GAAkBC,KAAlB;AACH;;AACW,MAARE,QAAQ,GAAG;AACX,WAAO,KAAKC,OAAZ;AACH;;AACW,MAARD,QAAQ,CAACF,KAAD,EAAQ;AAChB,SAAKG,OAAL,GAAeH,KAAf;AACH;;AACU,MAAPG,OAAO,GAAG;AACV,WAAO,KAAKb,QAAL,CAAcc,OAArB;AACH;;AACU,MAAPD,OAAO,CAACH,KAAD,EAAQ;AACf,SAAKV,QAAL,CAAcc,OAAd,GAAwBJ,KAAxB;AACH;;AACQ,MAALK,KAAK,GAAG;AACR,WAAO,KAAKhB,IAAZ;AACH;;AACQ,MAALgB,KAAK,CAACL,KAAD,EAAQ;AACb,SAAKX,IAAL,GAAYW,KAAZ;AACH;;AACDM,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,QAAIC,EAAJ,EAAQC,EAAR,EAAYC,EAAZ;;AACA,QAAIH,IAAI,KAAKI,SAAb,EAAwB;AACpB;AACH;;AACD,QAAIJ,IAAI,CAAC1B,KAAL,KAAe8B,SAAnB,EAA8B;AAC1B,UAAI,OAAOJ,IAAI,CAAC1B,KAAZ,KAAsB,QAA1B,EAAoC;AAChC,aAAKA,KAAL,CAAWmB,KAAX,GAAmBO,IAAI,CAAC1B,KAAxB;AACH,OAFD,MAGK;AACD,aAAKA,KAAL,CAAWyB,IAAX,CAAgBC,IAAI,CAAC1B,KAArB;AACH;AACJ;;AACD,SAAKC,OAAL,CAAawB,IAAb,CAAkBC,IAAI,CAACzB,OAAvB;;AACA,QAAIyB,IAAI,CAACxB,KAAL,KAAe4B,SAAnB,EAA8B;AAC1B,WAAK5B,KAAL,GAAawB,IAAI,CAACxB,KAAlB;AACH;;AACD,QAAIwB,IAAI,CAACtB,SAAL,KAAmB0B,SAAvB,EAAkC;AAC9B,WAAK1B,SAAL,GAAiBsB,IAAI,CAACtB,SAAtB;AACH;;AACD,QAAIsB,IAAI,CAACvB,QAAL,KAAkB2B,SAAtB,EAAiC;AAC7B,WAAK3B,QAAL,GACI,OAAOuB,IAAI,CAACvB,QAAZ,KAAyB,QAAzB,GACM;AACE4B,QAAAA,UAAU,EAAEL,IAAI,CAACvB,QADnB;AAEE6B,QAAAA,QAAQ,EAAEN,IAAI,CAACvB;AAFjB,OADN,GAKMd,UAAU,CAAC,EAAD,EAAKqC,IAAI,CAACvB,QAAV,CANpB;AAOH;;AACD,QAAIuB,IAAI,CAACrB,KAAL,KAAeyB,SAAnB,EAA8B;AAC1B,WAAKzB,KAAL,GAAaf,aAAa,CAACoC,IAAI,CAACrB,KAAN,CAA1B;AACH;;AACD,QAAIqB,IAAI,CAACpB,MAAL,KAAgBwB,SAApB,EAA+B;AAC3B,WAAKxB,MAAL,GAAcoB,IAAI,CAACpB,MAAnB;AACH;;AACD,SAAKC,OAAL,CAAakB,IAAb,CAAkBC,IAAI,CAACnB,OAAvB;AACA,UAAMe,OAAO,GAAG,CAACK,EAAE,GAAGD,IAAI,CAACJ,OAAX,MAAwB,IAAxB,IAAgCK,EAAE,KAAK,KAAK,CAA5C,GAAgDA,EAAhD,GAAqDD,IAAI,CAACL,QAA1E;;AACA,QAAIK,IAAI,CAACjB,QAAL,KAAkBqB,SAAlB,IAA+BR,OAAO,KAAKQ,SAA/C,EAA0D;AACtD,UAAI,OAAOJ,IAAI,CAACjB,QAAZ,KAAyB,QAAzB,IAAsCiB,IAAI,CAACjB,QAAL,KAAkBqB,SAAlB,IAA+BR,OAAO,KAAKQ,SAArF,EAAiG;AAC7F,aAAKrB,QAAL,CAAcgB,IAAd,CAAmB;AACfF,UAAAA,OAAO,EAAE,CAACK,EAAE,GAAGF,IAAI,CAACjB,QAAX,MAAyB,IAAzB,IAAiCmB,EAAE,KAAK,KAAK,CAA7C,GAAiDA,EAAjD,GAAsDN;AADhD,SAAnB;AAGH,OAJD,MAKK;AACD,aAAKb,QAAL,CAAcgB,IAAd,CAAmBC,IAAI,CAACjB,QAAxB;AACH;AACJ;;AACD,SAAKD,IAAL,CAAUiB,IAAV,CAAe,CAACI,EAAE,GAAGH,IAAI,CAAClB,IAAX,MAAqB,IAArB,IAA6BqB,EAAE,KAAK,KAAK,CAAzC,GAA6CA,EAA7C,GAAkDH,IAAI,CAACF,KAAtE;;AACA,QAAIE,IAAI,CAAChB,MAAL,KAAgBoB,SAApB,EAA+B;AAC3B,WAAKpB,MAAL,GAAcgB,IAAI,CAAChB,MAAnB;AACH;;AACD,QAAIgB,IAAI,CAACf,IAAL,KAAcmB,SAAlB,EAA6B;AACzB,WAAKnB,IAAL,GAAYe,IAAI,CAACf,IAAjB;AACH;;AACD,QAAIe,IAAI,CAACd,KAAL,KAAekB,SAAnB,EAA8B;AAC1B,WAAKlB,KAAL,GAAatB,aAAa,CAACoC,IAAI,CAACd,KAAN,CAA1B;AACH;;AACD,SAAKC,IAAL,CAAUY,IAAV,CAAeC,IAAI,CAACb,IAApB;;AACA,QAAIa,IAAI,CAACZ,QAAL,KAAkBgB,SAAtB,EAAiC;AAC7B,WAAKhB,QAAL,GAAgBY,IAAI,CAACZ,QAArB;AACH;;AACD,SAAKC,KAAL,CAAWU,IAAX,CAAgBC,IAAI,CAACX,KAArB;;AACA,QAAIW,IAAI,CAACV,OAAL,KAAiBc,SAArB,EAAgC;AAC5B,WAAKd,OAAL,GAAeU,IAAI,CAACV,OAApB;AACH;;AACD,QAAIU,IAAI,CAACT,IAAL,KAAca,SAAlB,EAA6B;AACzB,WAAKb,IAAL,GAAYS,IAAI,CAACT,IAAjB;AACH;AACJ;;AAtHa", "sourcesContent": ["import { deepExtend, setRangeValue } from \"../../../../Utils\";\nimport { Attract } from \"./Attract\";\nimport { MoveAngle } from \"./MoveAngle\";\nimport { MoveGravity } from \"./MoveGravity\";\nimport { OutModes } from \"./OutModes\";\nimport { Path } from \"./Path/Path\";\nimport { Spin } from \"./Spin\";\nimport { Trail } from \"./Trail\";\nexport class Move {\n    constructor() {\n        this.angle = new MoveAngle();\n        this.attract = new Attract();\n        this.decay = 0;\n        this.distance = {};\n        this.direction = \"none\";\n        this.drift = 0;\n        this.enable = false;\n        this.gravity = new MoveGravity();\n        this.path = new Path();\n        this.outModes = new OutModes();\n        this.random = false;\n        this.size = false;\n        this.speed = 2;\n        this.spin = new Spin();\n        this.straight = false;\n        this.trail = new Trail();\n        this.vibrate = false;\n        this.warp = false;\n    }\n    get collisions() {\n        return false;\n    }\n    set collisions(value) {\n    }\n    get bounce() {\n        return this.collisions;\n    }\n    set bounce(value) {\n        this.collisions = value;\n    }\n    get out_mode() {\n        return this.outMode;\n    }\n    set out_mode(value) {\n        this.outMode = value;\n    }\n    get outMode() {\n        return this.outModes.default;\n    }\n    set outMode(value) {\n        this.outModes.default = value;\n    }\n    get noise() {\n        return this.path;\n    }\n    set noise(value) {\n        this.path = value;\n    }\n    load(data) {\n        var _a, _b, _c;\n        if (data === undefined) {\n            return;\n        }\n        if (data.angle !== undefined) {\n            if (typeof data.angle === \"number\") {\n                this.angle.value = data.angle;\n            }\n            else {\n                this.angle.load(data.angle);\n            }\n        }\n        this.attract.load(data.attract);\n        if (data.decay !== undefined) {\n            this.decay = data.decay;\n        }\n        if (data.direction !== undefined) {\n            this.direction = data.direction;\n        }\n        if (data.distance !== undefined) {\n            this.distance =\n                typeof data.distance === \"number\"\n                    ? {\n                        horizontal: data.distance,\n                        vertical: data.distance,\n                    }\n                    : deepExtend({}, data.distance);\n        }\n        if (data.drift !== undefined) {\n            this.drift = setRangeValue(data.drift);\n        }\n        if (data.enable !== undefined) {\n            this.enable = data.enable;\n        }\n        this.gravity.load(data.gravity);\n        const outMode = (_a = data.outMode) !== null && _a !== void 0 ? _a : data.out_mode;\n        if (data.outModes !== undefined || outMode !== undefined) {\n            if (typeof data.outModes === \"string\" || (data.outModes === undefined && outMode !== undefined)) {\n                this.outModes.load({\n                    default: (_b = data.outModes) !== null && _b !== void 0 ? _b : outMode,\n                });\n            }\n            else {\n                this.outModes.load(data.outModes);\n            }\n        }\n        this.path.load((_c = data.path) !== null && _c !== void 0 ? _c : data.noise);\n        if (data.random !== undefined) {\n            this.random = data.random;\n        }\n        if (data.size !== undefined) {\n            this.size = data.size;\n        }\n        if (data.speed !== undefined) {\n            this.speed = setRangeValue(data.speed);\n        }\n        this.spin.load(data.spin);\n        if (data.straight !== undefined) {\n            this.straight = data.straight;\n        }\n        this.trail.load(data.trail);\n        if (data.vibrate !== undefined) {\n            this.vibrate = data.vibrate;\n        }\n        if (data.warp !== undefined) {\n            this.warp = data.warp;\n        }\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
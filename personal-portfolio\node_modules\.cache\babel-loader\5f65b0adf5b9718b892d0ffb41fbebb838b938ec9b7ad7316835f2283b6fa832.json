{"ast": null, "code": "import { PolygonDrawer } from \"./PolygonDrawer\";\nimport { TriangleDrawer } from \"./TriangleDrawer\";\nexport async function loadGenericPolygonShape(engine, refresh = true) {\n  await engine.addShape(\"polygon\", new PolygonDrawer(), refresh);\n}\nexport async function loadTriangleShape(engine, refresh = true) {\n  await engine.addShape(\"triangle\", new TriangleDrawer(), refresh);\n}\nexport async function loadPolygonShape(engine, refresh = true) {\n  await loadGenericPolygonShape(engine, refresh);\n  await loadTriangleShape(engine, refresh);\n}", "map": {"version": 3, "names": ["PolygonDrawer", "TriangleDrawer", "loadGenericPolygonShape", "engine", "refresh", "addShape", "loadTriangleShape", "loadPolygonShape"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/tsparticles-shape-polygon/esm/index.js"], "sourcesContent": ["import { PolygonDrawer } from \"./PolygonDrawer\";\nimport { TriangleDrawer } from \"./TriangleDrawer\";\nexport async function loadGenericPolygonShape(engine, refresh = true) {\n    await engine.addShape(\"polygon\", new PolygonDrawer(), refresh);\n}\nexport async function loadTriangleShape(engine, refresh = true) {\n    await engine.addShape(\"triangle\", new TriangleDrawer(), refresh);\n}\nexport async function loadPolygonShape(engine, refresh = true) {\n    await loadGenericPolygonShape(engine, refresh);\n    await loadTriangleShape(engine, refresh);\n}\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,cAAc,QAAQ,kBAAkB;AACjD,OAAO,eAAeC,uBAAuBA,CAACC,MAAM,EAAEC,OAAO,GAAG,IAAI,EAAE;EAClE,MAAMD,MAAM,CAACE,QAAQ,CAAC,SAAS,EAAE,IAAIL,aAAa,CAAC,CAAC,EAAEI,OAAO,CAAC;AAClE;AACA,OAAO,eAAeE,iBAAiBA,CAACH,MAAM,EAAEC,OAAO,GAAG,IAAI,EAAE;EAC5D,MAAMD,MAAM,CAACE,QAAQ,CAAC,UAAU,EAAE,IAAIJ,cAAc,CAAC,CAAC,EAAEG,OAAO,CAAC;AACpE;AACA,OAAO,eAAeG,gBAAgBA,CAACJ,MAAM,EAAEC,OAAO,GAAG,IAAI,EAAE;EAC3D,MAAMF,uBAAuB,CAACC,MAAM,EAAEC,OAAO,CAAC;EAC9C,MAAME,iBAAiB,CAACH,MAAM,EAAEC,OAAO,CAAC;AAC5C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
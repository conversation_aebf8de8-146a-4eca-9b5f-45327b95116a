{"ast": null, "code": "export class Bounce {\n  constructor() {\n    this.distance = 200;\n  }\n  load(data) {\n    if (!data) {\n      return;\n    }\n    if (data.distance !== undefined) {\n      this.distance = data.distance;\n    }\n  }\n}", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "constructor", "distance", "load", "data", "undefined"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/interaction-external-bounce/browser/Options/Classes/Bounce.js"], "sourcesContent": ["export class Bounce {\n    constructor() {\n        this.distance = 200;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.distance !== undefined) {\n            this.distance = data.distance;\n        }\n    }\n}\n"], "mappings": "AAAA,OAAO,MAAMA,MAAM,CAAC;EAChBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,QAAQ,GAAG,GAAG;EACvB;EACAC,IAAIA,CAACC,IAAI,EAAE;IACP,IAAI,CAACA,IAAI,EAAE;MACP;IACJ;IACA,IAAIA,IAAI,CAACF,QAAQ,KAAKG,SAAS,EAAE;MAC7B,IAAI,CAACH,QAAQ,GAAGE,IAAI,CAACF,QAAQ;IACjC;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
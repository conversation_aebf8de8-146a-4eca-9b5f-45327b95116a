{"ast": null, "code": "import { ExternalInteractorBase, isInArray, millisecondsToSeconds } from \"@tsparticles/engine\";\nimport { Trail } from \"./Options/Classes/Trail.js\";\nconst trailMode = \"trail\";\nexport class TrailMaker extends ExternalInteractorBase {\n  constructor(container) {\n    super(container);\n    this._delay = 0;\n  }\n  clear() {}\n  init() {}\n  interact(delta) {\n    const container = this.container,\n      {\n        interactivity\n      } = container;\n    if (!container.retina.reduceFactor) {\n      return;\n    }\n    const options = container.actualOptions,\n      trailOptions = options.interactivity.modes.trail;\n    if (!trailOptions) {\n      return;\n    }\n    const optDelay = trailOptions.delay * millisecondsToSeconds / this.container.retina.reduceFactor;\n    if (this._delay < optDelay) {\n      this._delay += delta.value;\n    }\n    if (this._delay < optDelay) {\n      return;\n    }\n    const canEmit = !(trailOptions.pauseOnStop && (interactivity.mouse.position === this._lastPosition || interactivity.mouse.position?.x === this._lastPosition?.x && interactivity.mouse.position?.y === this._lastPosition?.y));\n    const mousePos = container.interactivity.mouse.position;\n    if (mousePos) {\n      this._lastPosition = {\n        ...mousePos\n      };\n    } else {\n      delete this._lastPosition;\n    }\n    if (canEmit) {\n      container.particles.push(trailOptions.quantity, container.interactivity.mouse, trailOptions.particles);\n    }\n    this._delay -= optDelay;\n  }\n  isEnabled(particle) {\n    const container = this.container,\n      options = container.actualOptions,\n      mouse = container.interactivity.mouse,\n      events = (particle?.interactivity ?? options.interactivity).events;\n    return mouse.clicking && mouse.inside && !!mouse.position && isInArray(trailMode, events.onClick.mode) || mouse.inside && !!mouse.position && isInArray(trailMode, events.onHover.mode);\n  }\n  loadModeOptions(options, ...sources) {\n    if (!options.trail) {\n      options.trail = new Trail();\n    }\n    for (const source of sources) {\n      options.trail.load(source?.trail);\n    }\n  }\n  reset() {}\n}", "map": {"version": 3, "names": ["ExternalInteractorBase", "isInArray", "millisecondsToSeconds", "Trail", "trailMode", "TrailMaker", "constructor", "container", "_delay", "clear", "init", "interact", "delta", "interactivity", "retina", "reduceFactor", "options", "actualOptions", "trailOptions", "modes", "trail", "optDelay", "delay", "value", "canEmit", "pauseOnStop", "mouse", "position", "_lastPosition", "x", "y", "mousePos", "particles", "push", "quantity", "isEnabled", "particle", "events", "clicking", "inside", "onClick", "mode", "onHover", "loadModeOptions", "sources", "source", "load", "reset"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/interaction-external-trail/browser/TrailMaker.js"], "sourcesContent": ["import { ExternalInteractorBase, isInArray, millisecondsToSeconds, } from \"@tsparticles/engine\";\nimport { Trail } from \"./Options/Classes/Trail.js\";\nconst trailMode = \"trail\";\nexport class TrailMaker extends ExternalInteractorBase {\n    constructor(container) {\n        super(container);\n        this._delay = 0;\n    }\n    clear() {\n    }\n    init() {\n    }\n    interact(delta) {\n        const container = this.container, { interactivity } = container;\n        if (!container.retina.reduceFactor) {\n            return;\n        }\n        const options = container.actualOptions, trailOptions = options.interactivity.modes.trail;\n        if (!trailOptions) {\n            return;\n        }\n        const optDelay = (trailOptions.delay * millisecondsToSeconds) / this.container.retina.reduceFactor;\n        if (this._delay < optDelay) {\n            this._delay += delta.value;\n        }\n        if (this._delay < optDelay) {\n            return;\n        }\n        const canEmit = !(trailOptions.pauseOnStop &&\n            (interactivity.mouse.position === this._lastPosition ||\n                (interactivity.mouse.position?.x === this._lastPosition?.x &&\n                    interactivity.mouse.position?.y === this._lastPosition?.y)));\n        const mousePos = container.interactivity.mouse.position;\n        if (mousePos) {\n            this._lastPosition = { ...mousePos };\n        }\n        else {\n            delete this._lastPosition;\n        }\n        if (canEmit) {\n            container.particles.push(trailOptions.quantity, container.interactivity.mouse, trailOptions.particles);\n        }\n        this._delay -= optDelay;\n    }\n    isEnabled(particle) {\n        const container = this.container, options = container.actualOptions, mouse = container.interactivity.mouse, events = (particle?.interactivity ?? options.interactivity).events;\n        return ((mouse.clicking && mouse.inside && !!mouse.position && isInArray(trailMode, events.onClick.mode)) ||\n            (mouse.inside && !!mouse.position && isInArray(trailMode, events.onHover.mode)));\n    }\n    loadModeOptions(options, ...sources) {\n        if (!options.trail) {\n            options.trail = new Trail();\n        }\n        for (const source of sources) {\n            options.trail.load(source?.trail);\n        }\n    }\n    reset() {\n    }\n}\n"], "mappings": "AAAA,SAASA,sBAAsB,EAAEC,SAAS,EAAEC,qBAAqB,QAAS,qBAAqB;AAC/F,SAASC,KAAK,QAAQ,4BAA4B;AAClD,MAAMC,SAAS,GAAG,OAAO;AACzB,OAAO,MAAMC,UAAU,SAASL,sBAAsB,CAAC;EACnDM,WAAWA,CAACC,SAAS,EAAE;IACnB,KAAK,CAACA,SAAS,CAAC;IAChB,IAAI,CAACC,MAAM,GAAG,CAAC;EACnB;EACAC,KAAKA,CAAA,EAAG,CACR;EACAC,IAAIA,CAAA,EAAG,CACP;EACAC,QAAQA,CAACC,KAAK,EAAE;IACZ,MAAML,SAAS,GAAG,IAAI,CAACA,SAAS;MAAE;QAAEM;MAAc,CAAC,GAAGN,SAAS;IAC/D,IAAI,CAACA,SAAS,CAACO,MAAM,CAACC,YAAY,EAAE;MAChC;IACJ;IACA,MAAMC,OAAO,GAAGT,SAAS,CAACU,aAAa;MAAEC,YAAY,GAAGF,OAAO,CAACH,aAAa,CAACM,KAAK,CAACC,KAAK;IACzF,IAAI,CAACF,YAAY,EAAE;MACf;IACJ;IACA,MAAMG,QAAQ,GAAIH,YAAY,CAACI,KAAK,GAAGpB,qBAAqB,GAAI,IAAI,CAACK,SAAS,CAACO,MAAM,CAACC,YAAY;IAClG,IAAI,IAAI,CAACP,MAAM,GAAGa,QAAQ,EAAE;MACxB,IAAI,CAACb,MAAM,IAAII,KAAK,CAACW,KAAK;IAC9B;IACA,IAAI,IAAI,CAACf,MAAM,GAAGa,QAAQ,EAAE;MACxB;IACJ;IACA,MAAMG,OAAO,GAAG,EAAEN,YAAY,CAACO,WAAW,KACrCZ,aAAa,CAACa,KAAK,CAACC,QAAQ,KAAK,IAAI,CAACC,aAAa,IAC/Cf,aAAa,CAACa,KAAK,CAACC,QAAQ,EAAEE,CAAC,KAAK,IAAI,CAACD,aAAa,EAAEC,CAAC,IACtDhB,aAAa,CAACa,KAAK,CAACC,QAAQ,EAAEG,CAAC,KAAK,IAAI,CAACF,aAAa,EAAEE,CAAE,CAAC,CAAC;IACxE,MAAMC,QAAQ,GAAGxB,SAAS,CAACM,aAAa,CAACa,KAAK,CAACC,QAAQ;IACvD,IAAII,QAAQ,EAAE;MACV,IAAI,CAACH,aAAa,GAAG;QAAE,GAAGG;MAAS,CAAC;IACxC,CAAC,MACI;MACD,OAAO,IAAI,CAACH,aAAa;IAC7B;IACA,IAAIJ,OAAO,EAAE;MACTjB,SAAS,CAACyB,SAAS,CAACC,IAAI,CAACf,YAAY,CAACgB,QAAQ,EAAE3B,SAAS,CAACM,aAAa,CAACa,KAAK,EAAER,YAAY,CAACc,SAAS,CAAC;IAC1G;IACA,IAAI,CAACxB,MAAM,IAAIa,QAAQ;EAC3B;EACAc,SAASA,CAACC,QAAQ,EAAE;IAChB,MAAM7B,SAAS,GAAG,IAAI,CAACA,SAAS;MAAES,OAAO,GAAGT,SAAS,CAACU,aAAa;MAAES,KAAK,GAAGnB,SAAS,CAACM,aAAa,CAACa,KAAK;MAAEW,MAAM,GAAG,CAACD,QAAQ,EAAEvB,aAAa,IAAIG,OAAO,CAACH,aAAa,EAAEwB,MAAM;IAC9K,OAASX,KAAK,CAACY,QAAQ,IAAIZ,KAAK,CAACa,MAAM,IAAI,CAAC,CAACb,KAAK,CAACC,QAAQ,IAAI1B,SAAS,CAACG,SAAS,EAAEiC,MAAM,CAACG,OAAO,CAACC,IAAI,CAAC,IACnGf,KAAK,CAACa,MAAM,IAAI,CAAC,CAACb,KAAK,CAACC,QAAQ,IAAI1B,SAAS,CAACG,SAAS,EAAEiC,MAAM,CAACK,OAAO,CAACD,IAAI,CAAE;EACvF;EACAE,eAAeA,CAAC3B,OAAO,EAAE,GAAG4B,OAAO,EAAE;IACjC,IAAI,CAAC5B,OAAO,CAACI,KAAK,EAAE;MAChBJ,OAAO,CAACI,KAAK,GAAG,IAAIjB,KAAK,CAAC,CAAC;IAC/B;IACA,KAAK,MAAM0C,MAAM,IAAID,OAAO,EAAE;MAC1B5B,OAAO,CAACI,KAAK,CAAC0B,IAAI,CAACD,MAAM,EAAEzB,KAAK,CAAC;IACrC;EACJ;EACA2B,KAAKA,CAAA,EAAG,CACR;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { OpacityUpdater } from \"./OpacityUpdater\";\nexport async function loadOpacityUpdater(engine) {\n  await engine.addParticleUpdater(\"opacity\", container => new OpacityUpdater(container));\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Updaters/Opacity/index.js"], "names": ["OpacityUpdater", "loadOpacityUpdater", "engine", "addParticleUpdater", "container"], "mappings": "AAAA,SAASA,cAAT,QAA+B,kBAA/B;AACA,OAAO,eAAeC,kBAAf,CAAkCC,MAAlC,EAA0C;AAC7C,QAAMA,MAAM,CAACC,kBAAP,CAA0B,SAA1B,EAAsCC,SAAD,IAAe,IAAIJ,cAAJ,CAAmBI,SAAnB,CAApD,CAAN;AACH", "sourcesContent": ["import { OpacityUpdater } from \"./OpacityUpdater\";\nexport async function loadOpacityUpdater(engine) {\n    await engine.addParticleUpdater(\"opacity\", (container) => new OpacityUpdater(container));\n}\n"]}, "metadata": {}, "sourceType": "module"}
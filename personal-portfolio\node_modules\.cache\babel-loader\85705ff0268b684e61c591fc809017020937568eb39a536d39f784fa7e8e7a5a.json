{"ast": null, "code": "import { Repulser } from \"./Repulser.js\";\nexport async function loadExternalRepulseInteraction(engine, refresh = true) {\n  await engine.addInteractor(\"externalRepulse\", container => {\n    return Promise.resolve(new Repulser(engine, container));\n  }, refresh);\n}\nexport * from \"./Options/Classes/RepulseBase.js\";\nexport * from \"./Options/Classes/RepulseDiv.js\";\nexport * from \"./Options/Classes/Repulse.js\";\nexport * from \"./Options/Interfaces/IRepulseBase.js\";\nexport * from \"./Options/Interfaces/IRepulseDiv.js\";\nexport * from \"./Options/Interfaces/IRepulse.js\";", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "loadExternalRepulseInteraction", "engine", "refresh", "addInteractor", "container", "Promise", "resolve"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/interaction-external-repulse/browser/index.js"], "sourcesContent": ["import { Repulser } from \"./Repulser.js\";\nexport async function loadExternalRepulseInteraction(engine, refresh = true) {\n    await engine.addInteractor(\"externalRepulse\", container => {\n        return Promise.resolve(new Repulser(engine, container));\n    }, refresh);\n}\nexport * from \"./Options/Classes/RepulseBase.js\";\nexport * from \"./Options/Classes/RepulseDiv.js\";\nexport * from \"./Options/Classes/Repulse.js\";\nexport * from \"./Options/Interfaces/IRepulseBase.js\";\nexport * from \"./Options/Interfaces/IRepulseDiv.js\";\nexport * from \"./Options/Interfaces/IRepulse.js\";\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,eAAe;AACxC,OAAO,eAAeC,8BAA8BA,CAACC,MAAM,EAAEC,OAAO,GAAG,IAAI,EAAE;EACzE,MAAMD,MAAM,CAACE,aAAa,CAAC,iBAAiB,EAAEC,SAAS,IAAI;IACvD,OAAOC,OAAO,CAACC,OAAO,CAAC,IAAIP,QAAQ,CAACE,MAAM,EAAEG,SAAS,CAAC,CAAC;EAC3D,CAAC,EAAEF,OAAO,CAAC;AACf;AACA,cAAc,kCAAkC;AAChD,cAAc,iCAAiC;AAC/C,cAAc,8BAA8B;AAC5C,cAAc,sCAAsC;AACpD,cAAc,qCAAqC;AACnD,cAAc,kCAAkC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
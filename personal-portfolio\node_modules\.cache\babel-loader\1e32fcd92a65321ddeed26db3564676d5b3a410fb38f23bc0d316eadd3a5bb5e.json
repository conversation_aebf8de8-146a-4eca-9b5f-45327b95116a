{"ast": null, "code": "import { ValueWithRandom } from \"@tsparticles/engine\";\nexport class LifeDelay extends ValueWithRandom {\n  constructor() {\n    super();\n    this.sync = false;\n  }\n  load(data) {\n    if (!data) {\n      return;\n    }\n    super.load(data);\n    if (data.sync !== undefined) {\n      this.sync = data.sync;\n    }\n  }\n}", "map": {"version": 3, "names": ["ValueWithRandom", "LifeDelay", "constructor", "sync", "load", "data", "undefined"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/updater-life/browser/Options/Classes/LifeDelay.js"], "sourcesContent": ["import { ValueWithRandom } from \"@tsparticles/engine\";\nexport class LifeDelay extends ValueWithRandom {\n    constructor() {\n        super();\n        this.sync = false;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        super.load(data);\n        if (data.sync !== undefined) {\n            this.sync = data.sync;\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,qBAAqB;AACrD,OAAO,MAAMC,SAAS,SAASD,eAAe,CAAC;EAC3CE,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,IAAI,GAAG,KAAK;EACrB;EACAC,IAAIA,CAACC,IAAI,EAAE;IACP,IAAI,CAACA,IAAI,EAAE;MACP;IACJ;IACA,KAAK,CAACD,IAAI,CAACC,IAAI,CAAC;IAChB,IAAIA,IAAI,CAACF,IAAI,KAAKG,SAAS,EAAE;MACzB,IAAI,CAACH,IAAI,GAAGE,IAAI,CAACF,IAAI;IACzB;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
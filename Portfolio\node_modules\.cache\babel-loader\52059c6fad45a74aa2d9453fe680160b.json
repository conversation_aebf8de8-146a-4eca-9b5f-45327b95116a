{"ast": null, "code": "import { OptionsColor } from \"../OptionsColor\";\nexport class Background {\n  constructor() {\n    this.color = new OptionsColor();\n    this.color.value = \"\";\n    this.image = \"\";\n    this.position = \"\";\n    this.repeat = \"\";\n    this.size = \"\";\n    this.opacity = 1;\n  }\n\n  load(data) {\n    if (data === undefined) {\n      return;\n    }\n\n    if (data.color !== undefined) {\n      this.color = OptionsColor.create(this.color, data.color);\n    }\n\n    if (data.image !== undefined) {\n      this.image = data.image;\n    }\n\n    if (data.position !== undefined) {\n      this.position = data.position;\n    }\n\n    if (data.repeat !== undefined) {\n      this.repeat = data.repeat;\n    }\n\n    if (data.size !== undefined) {\n      this.size = data.size;\n    }\n\n    if (data.opacity !== undefined) {\n      this.opacity = data.opacity;\n    }\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Options/Classes/Background/Background.js"], "names": ["OptionsColor", "Background", "constructor", "color", "value", "image", "position", "repeat", "size", "opacity", "load", "data", "undefined", "create"], "mappings": "AAAA,SAASA,YAAT,QAA6B,iBAA7B;AACA,OAAO,MAAMC,UAAN,CAAiB;AACpBC,EAAAA,WAAW,GAAG;AACV,SAAKC,KAAL,GAAa,IAAIH,YAAJ,EAAb;AACA,SAAKG,KAAL,CAAWC,KAAX,GAAmB,EAAnB;AACA,SAAKC,KAAL,GAAa,EAAb;AACA,SAAKC,QAAL,GAAgB,EAAhB;AACA,SAAKC,MAAL,GAAc,EAAd;AACA,SAAKC,IAAL,GAAY,EAAZ;AACA,SAAKC,OAAL,GAAe,CAAf;AACH;;AACDC,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,QAAIA,IAAI,KAAKC,SAAb,EAAwB;AACpB;AACH;;AACD,QAAID,IAAI,CAACR,KAAL,KAAeS,SAAnB,EAA8B;AAC1B,WAAKT,KAAL,GAAaH,YAAY,CAACa,MAAb,CAAoB,KAAKV,KAAzB,EAAgCQ,IAAI,CAACR,KAArC,CAAb;AACH;;AACD,QAAIQ,IAAI,CAACN,KAAL,KAAeO,SAAnB,EAA8B;AAC1B,WAAKP,KAAL,GAAaM,IAAI,CAACN,KAAlB;AACH;;AACD,QAAIM,IAAI,CAACL,QAAL,KAAkBM,SAAtB,EAAiC;AAC7B,WAAKN,QAAL,GAAgBK,IAAI,CAACL,QAArB;AACH;;AACD,QAAIK,IAAI,CAACJ,MAAL,KAAgBK,SAApB,EAA+B;AAC3B,WAAKL,MAAL,GAAcI,IAAI,CAACJ,MAAnB;AACH;;AACD,QAAII,IAAI,CAACH,IAAL,KAAcI,SAAlB,EAA6B;AACzB,WAAKJ,IAAL,GAAYG,IAAI,CAACH,IAAjB;AACH;;AACD,QAAIG,IAAI,CAACF,OAAL,KAAiBG,SAArB,EAAgC;AAC5B,WAAKH,OAAL,GAAeE,IAAI,CAACF,OAApB;AACH;AACJ;;AAhCmB", "sourcesContent": ["import { OptionsColor } from \"../OptionsColor\";\nexport class Background {\n    constructor() {\n        this.color = new OptionsColor();\n        this.color.value = \"\";\n        this.image = \"\";\n        this.position = \"\";\n        this.repeat = \"\";\n        this.size = \"\";\n        this.opacity = 1;\n    }\n    load(data) {\n        if (data === undefined) {\n            return;\n        }\n        if (data.color !== undefined) {\n            this.color = OptionsColor.create(this.color, data.color);\n        }\n        if (data.image !== undefined) {\n            this.image = data.image;\n        }\n        if (data.position !== undefined) {\n            this.position = data.position;\n        }\n        if (data.repeat !== undefined) {\n            this.repeat = data.repeat;\n        }\n        if (data.size !== undefined) {\n            this.size = data.size;\n        }\n        if (data.opacity !== undefined) {\n            this.opacity = data.opacity;\n        }\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
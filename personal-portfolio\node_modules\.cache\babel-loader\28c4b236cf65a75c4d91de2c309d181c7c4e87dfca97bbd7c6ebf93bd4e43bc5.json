{"ast": null, "code": "import { executeOnSingleOrMultiple } from \"tsparticles-engine\";\nimport { BubbleBase } from \"./BubbleBase\";\nimport { BubbleDiv } from \"./BubbleDiv\";\nexport class Bubble extends BubbleBase {\n  load(data) {\n    super.load(data);\n    if (!data) {\n      return;\n    }\n    this.divs = executeOnSingleOrMultiple(data.divs, div => {\n      const tmp = new BubbleDiv();\n      tmp.load(div);\n      return tmp;\n    });\n  }\n}", "map": {"version": 3, "names": ["executeOnSingleOrMultiple", "BubbleBase", "BubbleDiv", "Bubble", "load", "data", "divs", "div", "tmp"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/tsparticles-interaction-external-bubble/esm/Options/Classes/Bubble.js"], "sourcesContent": ["import { executeOnSingleOrMultiple, } from \"tsparticles-engine\";\nimport { BubbleBase } from \"./BubbleBase\";\nimport { BubbleDiv } from \"./BubbleDiv\";\nexport class Bubble extends BubbleBase {\n    load(data) {\n        super.load(data);\n        if (!data) {\n            return;\n        }\n        this.divs = executeOnSingleOrMultiple(data.divs, (div) => {\n            const tmp = new BubbleDiv();\n            tmp.load(div);\n            return tmp;\n        });\n    }\n}\n"], "mappings": "AAAA,SAASA,yBAAyB,QAAS,oBAAoB;AAC/D,SAASC,UAAU,QAAQ,cAAc;AACzC,SAASC,SAAS,QAAQ,aAAa;AACvC,OAAO,MAAMC,MAAM,SAASF,UAAU,CAAC;EACnCG,IAAIA,CAACC,IAAI,EAAE;IACP,KAAK,CAACD,IAAI,CAACC,IAAI,CAAC;IAChB,IAAI,CAACA,IAAI,EAAE;MACP;IACJ;IACA,IAAI,CAACC,IAAI,GAAGN,yBAAyB,CAACK,IAAI,CAACC,IAAI,EAAGC,GAAG,IAAK;MACtD,MAAMC,GAAG,GAAG,IAAIN,SAAS,CAAC,CAAC;MAC3BM,GAAG,CAACJ,IAAI,CAACG,GAAG,CAAC;MACb,OAAOC,GAAG;IACd,CAAC,CAAC;EACN;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
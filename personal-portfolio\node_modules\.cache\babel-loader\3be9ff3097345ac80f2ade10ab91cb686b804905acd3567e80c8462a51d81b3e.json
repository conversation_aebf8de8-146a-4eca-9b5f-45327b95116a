{"ast": null, "code": "import { errorPrefix, getLogger, getStyleFromHsl } from \"tsparticles-engine\";\nimport { decodeGIF, getGIFLoopAmount } from \"./GifUtils/Utils\";\nconst currentColorRegex = /(#(?:[0-9a-f]{2}){2,4}|(#[0-9a-f]{3})|(rgb|hsl)a?\\((-?\\d+%?[,\\s]+){2,3}\\s*[\\d.]+%?\\))|currentcolor/gi;\nfunction replaceColorSvg(imageShape, color, opacity) {\n  const {\n    svgData\n  } = imageShape;\n  if (!svgData) {\n    return \"\";\n  }\n  const colorStyle = getStyleFromHsl(color, opacity);\n  if (svgData.includes(\"fill\")) {\n    return svgData.replace(currentColorRegex, () => colorStyle);\n  }\n  const preFillIndex = svgData.indexOf(\">\");\n  return `${svgData.substring(0, preFillIndex)} fill=\"${colorStyle}\"${svgData.substring(preFillIndex)}`;\n}\nexport async function loadImage(image) {\n  return new Promise(resolve => {\n    image.loading = true;\n    const img = new Image();\n    image.element = img;\n    img.addEventListener(\"load\", () => {\n      image.loading = false;\n      resolve();\n    });\n    img.addEventListener(\"error\", () => {\n      image.element = undefined;\n      image.error = true;\n      image.loading = false;\n      getLogger().error(`${errorPrefix} loading image: ${image.source}`);\n      resolve();\n    });\n    img.src = image.source;\n  });\n}\nexport async function loadGifImage(image) {\n  if (image.type !== \"gif\") {\n    await loadImage(image);\n    return;\n  }\n  image.loading = true;\n  try {\n    image.gifData = await decodeGIF(image.source);\n    image.gifLoopCount = getGIFLoopAmount(image.gifData) ?? 0;\n    if (image.gifLoopCount === 0) {\n      image.gifLoopCount = Infinity;\n    }\n  } catch {\n    image.error = true;\n  }\n  image.loading = false;\n}\nexport async function downloadSvgImage(image) {\n  if (image.type !== \"svg\") {\n    await loadImage(image);\n    return;\n  }\n  image.loading = true;\n  const response = await fetch(image.source);\n  if (!response.ok) {\n    getLogger().error(`${errorPrefix} Image not found`);\n    image.error = true;\n  } else {\n    image.svgData = await response.text();\n  }\n  image.loading = false;\n}\nexport function replaceImageColor(image, imageData, color, particle) {\n  const svgColoredData = replaceColorSvg(image, color, particle.opacity?.value ?? 1),\n    imageRes = {\n      color,\n      gif: imageData.gif,\n      data: {\n        ...image,\n        svgData: svgColoredData\n      },\n      loaded: false,\n      ratio: imageData.width / imageData.height,\n      replaceColor: imageData.replaceColor ?? imageData.replace_color,\n      source: imageData.src\n    };\n  return new Promise(resolve => {\n    const svg = new Blob([svgColoredData], {\n        type: \"image/svg+xml\"\n      }),\n      domUrl = URL || window.URL || window.webkitURL || window,\n      url = domUrl.createObjectURL(svg),\n      img = new Image();\n    img.addEventListener(\"load\", () => {\n      imageRes.loaded = true;\n      imageRes.element = img;\n      resolve(imageRes);\n      domUrl.revokeObjectURL(url);\n    });\n    img.addEventListener(\"error\", async () => {\n      domUrl.revokeObjectURL(url);\n      const img2 = {\n        ...image,\n        error: false,\n        loading: true\n      };\n      await loadImage(img2);\n      imageRes.loaded = true;\n      imageRes.element = img2.element;\n      resolve(imageRes);\n    });\n    img.src = url;\n  });\n}", "map": {"version": 3, "names": ["errorPrefix", "<PERSON><PERSON><PERSON><PERSON>", "getStyleFromHsl", "decodeGIF", "getGIFLoopAmount", "currentColorRegex", "replaceColorSvg", "imageShape", "color", "opacity", "svgData", "colorStyle", "includes", "replace", "preFillIndex", "indexOf", "substring", "loadImage", "image", "Promise", "resolve", "loading", "img", "Image", "element", "addEventListener", "undefined", "error", "source", "src", "loadGifImage", "type", "gifData", "gifLoopCount", "Infinity", "downloadSvgImage", "response", "fetch", "ok", "text", "replaceImageColor", "imageData", "particle", "svgColoredData", "value", "imageRes", "gif", "data", "loaded", "ratio", "width", "height", "replaceColor", "replace_color", "svg", "Blob", "domUrl", "URL", "window", "webkitURL", "url", "createObjectURL", "revokeObjectURL", "img2"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/tsparticles-shape-image/esm/Utils.js"], "sourcesContent": ["import { errorPrefix, getLogger, getStyleFromHsl } from \"tsparticles-engine\";\nimport { decodeGIF, getGIFLoopAmount } from \"./GifUtils/Utils\";\nconst currentColorRegex = /(#(?:[0-9a-f]{2}){2,4}|(#[0-9a-f]{3})|(rgb|hsl)a?\\((-?\\d+%?[,\\s]+){2,3}\\s*[\\d.]+%?\\))|currentcolor/gi;\nfunction replaceColorSvg(imageShape, color, opacity) {\n    const { svgData } = imageShape;\n    if (!svgData) {\n        return \"\";\n    }\n    const colorStyle = getStyleFromHsl(color, opacity);\n    if (svgData.includes(\"fill\")) {\n        return svgData.replace(currentColorRegex, () => colorStyle);\n    }\n    const preFillIndex = svgData.indexOf(\">\");\n    return `${svgData.substring(0, preFillIndex)} fill=\"${colorStyle}\"${svgData.substring(preFillIndex)}`;\n}\nexport async function loadImage(image) {\n    return new Promise((resolve) => {\n        image.loading = true;\n        const img = new Image();\n        image.element = img;\n        img.addEventListener(\"load\", () => {\n            image.loading = false;\n            resolve();\n        });\n        img.addEventListener(\"error\", () => {\n            image.element = undefined;\n            image.error = true;\n            image.loading = false;\n            getLogger().error(`${errorPrefix} loading image: ${image.source}`);\n            resolve();\n        });\n        img.src = image.source;\n    });\n}\nexport async function loadGifImage(image) {\n    if (image.type !== \"gif\") {\n        await loadImage(image);\n        return;\n    }\n    image.loading = true;\n    try {\n        image.gifData = await decodeGIF(image.source);\n        image.gifLoopCount = getGIFLoopAmount(image.gifData) ?? 0;\n        if (image.gifLoopCount === 0) {\n            image.gifLoopCount = Infinity;\n        }\n    }\n    catch {\n        image.error = true;\n    }\n    image.loading = false;\n}\nexport async function downloadSvgImage(image) {\n    if (image.type !== \"svg\") {\n        await loadImage(image);\n        return;\n    }\n    image.loading = true;\n    const response = await fetch(image.source);\n    if (!response.ok) {\n        getLogger().error(`${errorPrefix} Image not found`);\n        image.error = true;\n    }\n    else {\n        image.svgData = await response.text();\n    }\n    image.loading = false;\n}\nexport function replaceImageColor(image, imageData, color, particle) {\n    const svgColoredData = replaceColorSvg(image, color, particle.opacity?.value ?? 1), imageRes = {\n        color,\n        gif: imageData.gif,\n        data: {\n            ...image,\n            svgData: svgColoredData,\n        },\n        loaded: false,\n        ratio: imageData.width / imageData.height,\n        replaceColor: imageData.replaceColor ?? imageData.replace_color,\n        source: imageData.src,\n    };\n    return new Promise((resolve) => {\n        const svg = new Blob([svgColoredData], { type: \"image/svg+xml\" }), domUrl = URL || window.URL || window.webkitURL || window, url = domUrl.createObjectURL(svg), img = new Image();\n        img.addEventListener(\"load\", () => {\n            imageRes.loaded = true;\n            imageRes.element = img;\n            resolve(imageRes);\n            domUrl.revokeObjectURL(url);\n        });\n        img.addEventListener(\"error\", async () => {\n            domUrl.revokeObjectURL(url);\n            const img2 = {\n                ...image,\n                error: false,\n                loading: true,\n            };\n            await loadImage(img2);\n            imageRes.loaded = true;\n            imageRes.element = img2.element;\n            resolve(imageRes);\n        });\n        img.src = url;\n    });\n}\n"], "mappings": "AAAA,SAASA,WAAW,EAAEC,SAAS,EAAEC,eAAe,QAAQ,oBAAoB;AAC5E,SAASC,SAAS,EAAEC,gBAAgB,QAAQ,kBAAkB;AAC9D,MAAMC,iBAAiB,GAAG,sGAAsG;AAChI,SAASC,eAAeA,CAACC,UAAU,EAAEC,KAAK,EAAEC,OAAO,EAAE;EACjD,MAAM;IAAEC;EAAQ,CAAC,GAAGH,UAAU;EAC9B,IAAI,CAACG,OAAO,EAAE;IACV,OAAO,EAAE;EACb;EACA,MAAMC,UAAU,GAAGT,eAAe,CAACM,KAAK,EAAEC,OAAO,CAAC;EAClD,IAAIC,OAAO,CAACE,QAAQ,CAAC,MAAM,CAAC,EAAE;IAC1B,OAAOF,OAAO,CAACG,OAAO,CAACR,iBAAiB,EAAE,MAAMM,UAAU,CAAC;EAC/D;EACA,MAAMG,YAAY,GAAGJ,OAAO,CAACK,OAAO,CAAC,GAAG,CAAC;EACzC,OAAO,GAAGL,OAAO,CAACM,SAAS,CAAC,CAAC,EAAEF,YAAY,CAAC,UAAUH,UAAU,IAAID,OAAO,CAACM,SAAS,CAACF,YAAY,CAAC,EAAE;AACzG;AACA,OAAO,eAAeG,SAASA,CAACC,KAAK,EAAE;EACnC,OAAO,IAAIC,OAAO,CAAEC,OAAO,IAAK;IAC5BF,KAAK,CAACG,OAAO,GAAG,IAAI;IACpB,MAAMC,GAAG,GAAG,IAAIC,KAAK,CAAC,CAAC;IACvBL,KAAK,CAACM,OAAO,GAAGF,GAAG;IACnBA,GAAG,CAACG,gBAAgB,CAAC,MAAM,EAAE,MAAM;MAC/BP,KAAK,CAACG,OAAO,GAAG,KAAK;MACrBD,OAAO,CAAC,CAAC;IACb,CAAC,CAAC;IACFE,GAAG,CAACG,gBAAgB,CAAC,OAAO,EAAE,MAAM;MAChCP,KAAK,CAACM,OAAO,GAAGE,SAAS;MACzBR,KAAK,CAACS,KAAK,GAAG,IAAI;MAClBT,KAAK,CAACG,OAAO,GAAG,KAAK;MACrBpB,SAAS,CAAC,CAAC,CAAC0B,KAAK,CAAC,GAAG3B,WAAW,mBAAmBkB,KAAK,CAACU,MAAM,EAAE,CAAC;MAClER,OAAO,CAAC,CAAC;IACb,CAAC,CAAC;IACFE,GAAG,CAACO,GAAG,GAAGX,KAAK,CAACU,MAAM;EAC1B,CAAC,CAAC;AACN;AACA,OAAO,eAAeE,YAAYA,CAACZ,KAAK,EAAE;EACtC,IAAIA,KAAK,CAACa,IAAI,KAAK,KAAK,EAAE;IACtB,MAAMd,SAAS,CAACC,KAAK,CAAC;IACtB;EACJ;EACAA,KAAK,CAACG,OAAO,GAAG,IAAI;EACpB,IAAI;IACAH,KAAK,CAACc,OAAO,GAAG,MAAM7B,SAAS,CAACe,KAAK,CAACU,MAAM,CAAC;IAC7CV,KAAK,CAACe,YAAY,GAAG7B,gBAAgB,CAACc,KAAK,CAACc,OAAO,CAAC,IAAI,CAAC;IACzD,IAAId,KAAK,CAACe,YAAY,KAAK,CAAC,EAAE;MAC1Bf,KAAK,CAACe,YAAY,GAAGC,QAAQ;IACjC;EACJ,CAAC,CACD,MAAM;IACFhB,KAAK,CAACS,KAAK,GAAG,IAAI;EACtB;EACAT,KAAK,CAACG,OAAO,GAAG,KAAK;AACzB;AACA,OAAO,eAAec,gBAAgBA,CAACjB,KAAK,EAAE;EAC1C,IAAIA,KAAK,CAACa,IAAI,KAAK,KAAK,EAAE;IACtB,MAAMd,SAAS,CAACC,KAAK,CAAC;IACtB;EACJ;EACAA,KAAK,CAACG,OAAO,GAAG,IAAI;EACpB,MAAMe,QAAQ,GAAG,MAAMC,KAAK,CAACnB,KAAK,CAACU,MAAM,CAAC;EAC1C,IAAI,CAACQ,QAAQ,CAACE,EAAE,EAAE;IACdrC,SAAS,CAAC,CAAC,CAAC0B,KAAK,CAAC,GAAG3B,WAAW,kBAAkB,CAAC;IACnDkB,KAAK,CAACS,KAAK,GAAG,IAAI;EACtB,CAAC,MACI;IACDT,KAAK,CAACR,OAAO,GAAG,MAAM0B,QAAQ,CAACG,IAAI,CAAC,CAAC;EACzC;EACArB,KAAK,CAACG,OAAO,GAAG,KAAK;AACzB;AACA,OAAO,SAASmB,iBAAiBA,CAACtB,KAAK,EAAEuB,SAAS,EAAEjC,KAAK,EAAEkC,QAAQ,EAAE;EACjE,MAAMC,cAAc,GAAGrC,eAAe,CAACY,KAAK,EAAEV,KAAK,EAAEkC,QAAQ,CAACjC,OAAO,EAAEmC,KAAK,IAAI,CAAC,CAAC;IAAEC,QAAQ,GAAG;MAC3FrC,KAAK;MACLsC,GAAG,EAAEL,SAAS,CAACK,GAAG;MAClBC,IAAI,EAAE;QACF,GAAG7B,KAAK;QACRR,OAAO,EAAEiC;MACb,CAAC;MACDK,MAAM,EAAE,KAAK;MACbC,KAAK,EAAER,SAAS,CAACS,KAAK,GAAGT,SAAS,CAACU,MAAM;MACzCC,YAAY,EAAEX,SAAS,CAACW,YAAY,IAAIX,SAAS,CAACY,aAAa;MAC/DzB,MAAM,EAAEa,SAAS,CAACZ;IACtB,CAAC;EACD,OAAO,IAAIV,OAAO,CAAEC,OAAO,IAAK;IAC5B,MAAMkC,GAAG,GAAG,IAAIC,IAAI,CAAC,CAACZ,cAAc,CAAC,EAAE;QAAEZ,IAAI,EAAE;MAAgB,CAAC,CAAC;MAAEyB,MAAM,GAAGC,GAAG,IAAIC,MAAM,CAACD,GAAG,IAAIC,MAAM,CAACC,SAAS,IAAID,MAAM;MAAEE,GAAG,GAAGJ,MAAM,CAACK,eAAe,CAACP,GAAG,CAAC;MAAEhC,GAAG,GAAG,IAAIC,KAAK,CAAC,CAAC;IACjLD,GAAG,CAACG,gBAAgB,CAAC,MAAM,EAAE,MAAM;MAC/BoB,QAAQ,CAACG,MAAM,GAAG,IAAI;MACtBH,QAAQ,CAACrB,OAAO,GAAGF,GAAG;MACtBF,OAAO,CAACyB,QAAQ,CAAC;MACjBW,MAAM,CAACM,eAAe,CAACF,GAAG,CAAC;IAC/B,CAAC,CAAC;IACFtC,GAAG,CAACG,gBAAgB,CAAC,OAAO,EAAE,YAAY;MACtC+B,MAAM,CAACM,eAAe,CAACF,GAAG,CAAC;MAC3B,MAAMG,IAAI,GAAG;QACT,GAAG7C,KAAK;QACRS,KAAK,EAAE,KAAK;QACZN,OAAO,EAAE;MACb,CAAC;MACD,MAAMJ,SAAS,CAAC8C,IAAI,CAAC;MACrBlB,QAAQ,CAACG,MAAM,GAAG,IAAI;MACtBH,QAAQ,CAACrB,OAAO,GAAGuC,IAAI,CAACvC,OAAO;MAC/BJ,OAAO,CAACyB,QAAQ,CAAC;IACrB,CAAC,CAAC;IACFvB,GAAG,CAACO,GAAG,GAAG+B,GAAG;EACjB,CAAC,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { RepulseBase } from \"./RepulseBase.js\";\nexport class RepulseDiv extends RepulseBase {\n  constructor() {\n    super();\n    this.selectors = [];\n  }\n  load(data) {\n    super.load(data);\n    if (!data) {\n      return;\n    }\n    if (data.selectors !== undefined) {\n      this.selectors = data.selectors;\n    }\n  }\n}", "map": {"version": 3, "names": ["RepulseBase", "RepulseDiv", "constructor", "selectors", "load", "data", "undefined"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/interaction-external-repulse/browser/Options/Classes/RepulseDiv.js"], "sourcesContent": ["import { RepulseBase } from \"./RepulseBase.js\";\nexport class RepulseDiv extends RepulseBase {\n    constructor() {\n        super();\n        this.selectors = [];\n    }\n    load(data) {\n        super.load(data);\n        if (!data) {\n            return;\n        }\n        if (data.selectors !== undefined) {\n            this.selectors = data.selectors;\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,kBAAkB;AAC9C,OAAO,MAAMC,UAAU,SAASD,WAAW,CAAC;EACxCE,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,SAAS,GAAG,EAAE;EACvB;EACAC,IAAIA,CAACC,IAAI,EAAE;IACP,KAAK,CAACD,IAAI,CAACC,IAAI,CAAC;IAChB,IAAI,CAACA,IAAI,EAAE;MACP;IACJ;IACA,IAAIA,IAAI,CAACF,SAAS,KAAKG,SAAS,EAAE;MAC9B,IAAI,CAACH,SAAS,GAAGE,IAAI,CAACF,SAAS;IACnC;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { Grabber } from \"./Grabber\";\nexport async function loadExternalGrabInteraction(engine) {\n  await engine.addInteractor(\"externalGrab\", container => new Grabber(container));\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Interactions/External/Grab/index.js"], "names": ["<PERSON><PERSON><PERSON>", "loadExternalGrabInteraction", "engine", "addInteractor", "container"], "mappings": "AAAA,SAASA,OAAT,QAAwB,WAAxB;AACA,OAAO,eAAeC,2BAAf,CAA2CC,MAA3C,EAAmD;AACtD,QAAMA,MAAM,CAACC,aAAP,CAAqB,cAArB,EAAsCC,SAAD,IAAe,IAAIJ,OAAJ,CAAYI,SAAZ,CAApD,CAAN;AACH", "sourcesContent": ["import { Grabber } from \"./Grabber\";\nexport async function loadExternalGrabInteraction(engine) {\n    await engine.addInteractor(\"externalGrab\", (container) => new Grabber(container));\n}\n"]}, "metadata": {}, "sourceType": "module"}
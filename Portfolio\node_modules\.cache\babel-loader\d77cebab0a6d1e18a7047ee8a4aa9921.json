{"ast": null, "code": "import { TextDrawer, validTypes } from \"./TextDrawer\";\nexport async function loadTextShape(engine) {\n  const drawer = new TextDrawer();\n\n  for (const type of validTypes) {\n    await engine.addShape(type, drawer);\n  }\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Shapes/Text/index.js"], "names": ["TextDrawer", "validTypes", "loadTextShape", "engine", "drawer", "type", "addShape"], "mappings": "AAAA,SAASA,UAAT,EAAqBC,UAArB,QAAuC,cAAvC;AACA,OAAO,eAAeC,aAAf,CAA6BC,MAA7B,EAAqC;AACxC,QAAMC,MAAM,GAAG,IAAIJ,UAAJ,EAAf;;AACA,OAAK,MAAMK,IAAX,IAAmBJ,UAAnB,EAA+B;AAC3B,UAAME,MAAM,CAACG,QAAP,CAAgBD,IAAhB,EAAsBD,MAAtB,CAAN;AACH;AACJ", "sourcesContent": ["import { TextDrawer, validTypes } from \"./TextDrawer\";\nexport async function loadTextShape(engine) {\n    const drawer = new TextDrawer();\n    for (const type of validTypes) {\n        await engine.addShape(type, drawer);\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
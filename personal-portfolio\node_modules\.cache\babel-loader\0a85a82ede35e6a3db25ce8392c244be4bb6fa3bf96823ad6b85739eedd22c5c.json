{"ast": null, "code": "import { executeOnSingleOrMultiple } from \"tsparticles-engine\";\nimport { BubbleBase } from \"./BubbleBase\";\nexport class BubbleDiv extends BubbleBase {\n  constructor() {\n    super();\n    this.selectors = [];\n  }\n  get ids() {\n    return executeOnSingleOrMultiple(this.selectors, t => t.replace(\"#\", \"\"));\n  }\n  set ids(value) {\n    this.selectors = executeOnSingleOrMultiple(value, t => `#${t}`);\n  }\n  load(data) {\n    super.load(data);\n    if (!data) {\n      return;\n    }\n    if (data.ids !== undefined) {\n      this.ids = data.ids;\n    }\n    if (data.selectors !== undefined) {\n      this.selectors = data.selectors;\n    }\n  }\n}", "map": {"version": 3, "names": ["executeOnSingleOrMultiple", "BubbleBase", "BubbleDiv", "constructor", "selectors", "ids", "t", "replace", "value", "load", "data", "undefined"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/tsparticles-interaction-external-bubble/esm/Options/Classes/BubbleDiv.js"], "sourcesContent": ["import { executeOnSingleOrMultiple, } from \"tsparticles-engine\";\nimport { BubbleBase } from \"./BubbleBase\";\nexport class BubbleDiv extends BubbleBase {\n    constructor() {\n        super();\n        this.selectors = [];\n    }\n    get ids() {\n        return executeOnSingleOrMultiple(this.selectors, (t) => t.replace(\"#\", \"\"));\n    }\n    set ids(value) {\n        this.selectors = executeOnSingleOrMultiple(value, (t) => `#${t}`);\n    }\n    load(data) {\n        super.load(data);\n        if (!data) {\n            return;\n        }\n        if (data.ids !== undefined) {\n            this.ids = data.ids;\n        }\n        if (data.selectors !== undefined) {\n            this.selectors = data.selectors;\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,yBAAyB,QAAS,oBAAoB;AAC/D,SAASC,UAAU,QAAQ,cAAc;AACzC,OAAO,MAAMC,SAAS,SAASD,UAAU,CAAC;EACtCE,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,SAAS,GAAG,EAAE;EACvB;EACA,IAAIC,GAAGA,CAAA,EAAG;IACN,OAAOL,yBAAyB,CAAC,IAAI,CAACI,SAAS,EAAGE,CAAC,IAAKA,CAAC,CAACC,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;EAC/E;EACA,IAAIF,GAAGA,CAACG,KAAK,EAAE;IACX,IAAI,CAACJ,SAAS,GAAGJ,yBAAyB,CAACQ,KAAK,EAAGF,CAAC,IAAK,IAAIA,CAAC,EAAE,CAAC;EACrE;EACAG,IAAIA,CAACC,IAAI,EAAE;IACP,KAAK,CAACD,IAAI,CAACC,IAAI,CAAC;IAChB,IAAI,CAACA,IAAI,EAAE;MACP;IACJ;IACA,IAAIA,IAAI,CAACL,GAAG,KAAKM,SAAS,EAAE;MACxB,IAAI,CAACN,GAAG,GAAGK,IAAI,CAACL,GAAG;IACvB;IACA,IAAIK,IAAI,CAACN,SAAS,KAAKO,SAAS,EAAE;MAC9B,IAAI,CAACP,SAAS,GAAGM,IAAI,CAACN,SAAS;IACnC;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
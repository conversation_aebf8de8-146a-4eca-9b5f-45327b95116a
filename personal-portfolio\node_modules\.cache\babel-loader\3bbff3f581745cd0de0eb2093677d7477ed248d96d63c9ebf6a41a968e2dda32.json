{"ast": null, "code": "import { loadBaseMover } from \"@tsparticles/move-base\";\nimport { loadCircleShape } from \"@tsparticles/shape-circle\";\nimport { loadColorUpdater } from \"@tsparticles/updater-color\";\nimport { loadOpacityUpdater } from \"@tsparticles/updater-opacity\";\nimport { loadOutModesUpdater } from \"@tsparticles/updater-out-modes\";\nimport { loadSizeUpdater } from \"@tsparticles/updater-size\";\nexport async function loadBasic(engine, refresh = true) {\n  await loadBaseMover(engine, false);\n  await loadCircleShape(engine, false);\n  await loadColorUpdater(engine, false);\n  await loadOpacityUpdater(engine, false);\n  await loadOutModesUpdater(engine, false);\n  await loadSizeUpdater(engine, false);\n  await engine.refresh(refresh);\n}", "map": {"version": 3, "names": ["loadBaseMover", "loadCircleShape", "loadColorUpdater", "loadOpacityUpdater", "loadOutModesUpdater", "loadSizeUpdater", "loadBasic", "engine", "refresh"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/basic/browser/index.js"], "sourcesContent": ["import { loadBaseMover } from \"@tsparticles/move-base\";\nimport { loadCircleShape } from \"@tsparticles/shape-circle\";\nimport { loadColorUpdater } from \"@tsparticles/updater-color\";\nimport { loadOpacityUpdater } from \"@tsparticles/updater-opacity\";\nimport { loadOutModesUpdater } from \"@tsparticles/updater-out-modes\";\nimport { loadSizeUpdater } from \"@tsparticles/updater-size\";\nexport async function loadBasic(engine, refresh = true) {\n    await loadBaseMover(engine, false);\n    await loadCircleShape(engine, false);\n    await loadColorUpdater(engine, false);\n    await loadOpacityUpdater(engine, false);\n    await loadOutModesUpdater(engine, false);\n    await loadSizeUpdater(engine, false);\n    await engine.refresh(refresh);\n}\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,2BAA2B;AAC3D,SAASC,gBAAgB,QAAQ,4BAA4B;AAC7D,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,mBAAmB,QAAQ,gCAAgC;AACpE,SAASC,eAAe,QAAQ,2BAA2B;AAC3D,OAAO,eAAeC,SAASA,CAACC,MAAM,EAAEC,OAAO,GAAG,IAAI,EAAE;EACpD,MAAMR,aAAa,CAACO,MAAM,EAAE,KAAK,CAAC;EAClC,MAAMN,eAAe,CAACM,MAAM,EAAE,KAAK,CAAC;EACpC,MAAML,gBAAgB,CAACK,MAAM,EAAE,KAAK,CAAC;EACrC,MAAMJ,kBAAkB,CAACI,MAAM,EAAE,KAAK,CAAC;EACvC,MAAMH,mBAAmB,CAACG,MAAM,EAAE,KAAK,CAAC;EACxC,MAAMF,eAAe,CAACE,MAAM,EAAE,KAAK,CAAC;EACpC,MAAMA,MAAM,CAACC,OAAO,CAACA,OAAO,CAAC;AACjC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { deepExtend } from \"../../../../Utils/Utils.js\";\nexport class Shape {\n  constructor() {\n    this.close = true;\n    this.fill = true;\n    this.options = {};\n    this.type = \"circle\";\n  }\n  load(data) {\n    if (!data) {\n      return;\n    }\n    const options = data.options;\n    if (options !== undefined) {\n      for (const shape in options) {\n        const item = options[shape];\n        if (item) {\n          this.options[shape] = deepExtend(this.options[shape] ?? {}, item);\n        }\n      }\n    }\n    if (data.close !== undefined) {\n      this.close = data.close;\n    }\n    if (data.fill !== undefined) {\n      this.fill = data.fill;\n    }\n    if (data.type !== undefined) {\n      this.type = data.type;\n    }\n  }\n}", "map": {"version": 3, "names": ["deepExtend", "<PERSON><PERSON><PERSON>", "constructor", "close", "fill", "options", "type", "load", "data", "undefined", "shape", "item"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/engine/browser/Options/Classes/Particles/Shape/Shape.js"], "sourcesContent": ["import { deepExtend } from \"../../../../Utils/Utils.js\";\nexport class Shape {\n    constructor() {\n        this.close = true;\n        this.fill = true;\n        this.options = {};\n        this.type = \"circle\";\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        const options = data.options;\n        if (options !== undefined) {\n            for (const shape in options) {\n                const item = options[shape];\n                if (item) {\n                    this.options[shape] = deepExtend(this.options[shape] ?? {}, item);\n                }\n            }\n        }\n        if (data.close !== undefined) {\n            this.close = data.close;\n        }\n        if (data.fill !== undefined) {\n            this.fill = data.fill;\n        }\n        if (data.type !== undefined) {\n            this.type = data.type;\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,4BAA4B;AACvD,OAAO,MAAMC,KAAK,CAAC;EACfC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,KAAK,GAAG,IAAI;IACjB,IAAI,CAACC,IAAI,GAAG,IAAI;IAChB,IAAI,CAACC,OAAO,GAAG,CAAC,CAAC;IACjB,IAAI,CAACC,IAAI,GAAG,QAAQ;EACxB;EACAC,IAAIA,CAACC,IAAI,EAAE;IACP,IAAI,CAACA,IAAI,EAAE;MACP;IACJ;IACA,MAAMH,OAAO,GAAGG,IAAI,CAACH,OAAO;IAC5B,IAAIA,OAAO,KAAKI,SAAS,EAAE;MACvB,KAAK,MAAMC,KAAK,IAAIL,OAAO,EAAE;QACzB,MAAMM,IAAI,GAAGN,OAAO,CAACK,KAAK,CAAC;QAC3B,IAAIC,IAAI,EAAE;UACN,IAAI,CAACN,OAAO,CAACK,KAAK,CAAC,GAAGV,UAAU,CAAC,IAAI,CAACK,OAAO,CAACK,KAAK,CAAC,IAAI,CAAC,CAAC,EAAEC,IAAI,CAAC;QACrE;MACJ;IACJ;IACA,IAAIH,IAAI,CAACL,KAAK,KAAKM,SAAS,EAAE;MAC1B,IAAI,CAACN,KAAK,GAAGK,IAAI,CAACL,KAAK;IAC3B;IACA,IAAIK,IAAI,CAACJ,IAAI,KAAKK,SAAS,EAAE;MACzB,IAAI,CAACL,IAAI,GAAGI,IAAI,CAACJ,IAAI;IACzB;IACA,IAAII,IAAI,CAACF,IAAI,KAAKG,SAAS,EAAE;MACzB,IAAI,CAACH,IAAI,GAAGE,IAAI,CAACF,IAAI;IACzB;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
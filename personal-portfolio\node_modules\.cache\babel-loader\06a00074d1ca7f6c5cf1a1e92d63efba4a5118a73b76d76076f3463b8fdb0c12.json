{"ast": null, "code": "import { LifeDelay } from \"./LifeDelay\";\nimport { LifeDuration } from \"./LifeDuration\";\nexport class Life {\n  constructor() {\n    this.count = 0;\n    this.delay = new LifeDelay();\n    this.duration = new LifeDuration();\n  }\n  load(data) {\n    if (!data) {\n      return;\n    }\n    if (data.count !== undefined) {\n      this.count = data.count;\n    }\n    this.delay.load(data.delay);\n    this.duration.load(data.duration);\n  }\n}", "map": {"version": 3, "names": ["LifeDelay", "LifeDuration", "Life", "constructor", "count", "delay", "duration", "load", "data", "undefined"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/tsparticles-updater-life/esm/Options/Classes/Life.js"], "sourcesContent": ["import { LifeDelay } from \"./LifeDelay\";\nimport { LifeDuration } from \"./LifeDuration\";\nexport class Life {\n    constructor() {\n        this.count = 0;\n        this.delay = new LifeDelay();\n        this.duration = new LifeDuration();\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.count !== undefined) {\n            this.count = data.count;\n        }\n        this.delay.load(data.delay);\n        this.duration.load(data.duration);\n    }\n}\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,aAAa;AACvC,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,OAAO,MAAMC,IAAI,CAAC;EACdC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,KAAK,GAAG,CAAC;IACd,IAAI,CAACC,KAAK,GAAG,IAAIL,SAAS,CAAC,CAAC;IAC5B,IAAI,CAACM,QAAQ,GAAG,IAAIL,YAAY,CAAC,CAAC;EACtC;EACAM,IAAIA,CAACC,IAAI,EAAE;IACP,IAAI,CAACA,IAAI,EAAE;MACP;IACJ;IACA,IAAIA,IAAI,CAACJ,KAAK,KAAKK,SAAS,EAAE;MAC1B,IAAI,CAACL,KAAK,GAAGI,IAAI,CAACJ,KAAK;IAC3B;IACA,IAAI,CAACC,KAAK,CAACE,IAAI,CAACC,IAAI,CAACH,KAAK,CAAC;IAC3B,IAAI,CAACC,QAAQ,CAACC,IAAI,CAACC,IAAI,CAACF,QAAQ,CAAC;EACrC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "var formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: 'less than a second',\n    other: 'less than {{count}} seconds'\n  },\n  xSeconds: {\n    one: '1 second',\n    other: '{{count}} seconds'\n  },\n  halfAMinute: 'half a minute',\n  lessThanXMinutes: {\n    one: 'less than a minute',\n    other: 'less than {{count}} minutes'\n  },\n  xMinutes: {\n    one: '1 minute',\n    other: '{{count}} minutes'\n  },\n  aboutXHours: {\n    one: 'about 1 hour',\n    other: 'about {{count}} hours'\n  },\n  xHours: {\n    one: '1 hour',\n    other: '{{count}} hours'\n  },\n  xDays: {\n    one: '1 day',\n    other: '{{count}} days'\n  },\n  aboutXWeeks: {\n    one: 'about 1 week',\n    other: 'about {{count}} weeks'\n  },\n  xWeeks: {\n    one: '1 week',\n    other: '{{count}} weeks'\n  },\n  aboutXMonths: {\n    one: 'about 1 month',\n    other: 'about {{count}} months'\n  },\n  xMonths: {\n    one: '1 month',\n    other: '{{count}} months'\n  },\n  aboutXYears: {\n    one: 'about 1 year',\n    other: 'about {{count}} years'\n  },\n  xYears: {\n    one: '1 year',\n    other: '{{count}} years'\n  },\n  overXYears: {\n    one: 'over 1 year',\n    other: 'over {{count}} years'\n  },\n  almostXYears: {\n    one: 'almost 1 year',\n    other: 'almost {{count}} years'\n  }\n};\n\nvar formatDistance = function (token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace('{{count}}', count.toString());\n  }\n\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return 'in ' + result;\n    } else {\n      return result + ' ago';\n    }\n  }\n\n  return result;\n};\n\nexport default formatDistance;", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/date-fns/esm/locale/en-US/_lib/formatDistance/index.js"], "names": ["formatDistanceLocale", "lessThanXSeconds", "one", "other", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "formatDistance", "token", "count", "options", "result", "tokenValue", "replace", "toString", "addSuffix", "comparison"], "mappings": "AAAA,IAAIA,oBAAoB,GAAG;AACzBC,EAAAA,gBAAgB,EAAE;AAChBC,IAAAA,GAAG,EAAE,oBADW;AAEhBC,IAAAA,KAAK,EAAE;AAFS,GADO;AAKzBC,EAAAA,QAAQ,EAAE;AACRF,IAAAA,GAAG,EAAE,UADG;AAERC,IAAAA,KAAK,EAAE;AAFC,GALe;AASzBE,EAAAA,WAAW,EAAE,eATY;AAUzBC,EAAAA,gBAAgB,EAAE;AAChBJ,IAAAA,GAAG,EAAE,oBADW;AAEhBC,IAAAA,KAAK,EAAE;AAFS,GAVO;AAczBI,EAAAA,QAAQ,EAAE;AACRL,IAAAA,GAAG,EAAE,UADG;AAERC,IAAAA,KAAK,EAAE;AAFC,GAde;AAkBzBK,EAAAA,WAAW,EAAE;AACXN,IAAAA,GAAG,EAAE,cADM;AAEXC,IAAAA,KAAK,EAAE;AAFI,GAlBY;AAsBzBM,EAAAA,MAAM,EAAE;AACNP,IAAAA,GAAG,EAAE,QADC;AAENC,IAAAA,KAAK,EAAE;AAFD,GAtBiB;AA0BzBO,EAAAA,KAAK,EAAE;AACLR,IAAAA,GAAG,EAAE,OADA;AAELC,IAAAA,KAAK,EAAE;AAFF,GA1BkB;AA8BzBQ,EAAAA,WAAW,EAAE;AACXT,IAAAA,GAAG,EAAE,cADM;AAEXC,IAAAA,KAAK,EAAE;AAFI,GA9BY;AAkCzBS,EAAAA,MAAM,EAAE;AACNV,IAAAA,GAAG,EAAE,QADC;AAENC,IAAAA,KAAK,EAAE;AAFD,GAlCiB;AAsCzBU,EAAAA,YAAY,EAAE;AACZX,IAAAA,GAAG,EAAE,eADO;AAEZC,IAAAA,KAAK,EAAE;AAFK,GAtCW;AA0CzBW,EAAAA,OAAO,EAAE;AACPZ,IAAAA,GAAG,EAAE,SADE;AAEPC,IAAAA,KAAK,EAAE;AAFA,GA1CgB;AA8CzBY,EAAAA,WAAW,EAAE;AACXb,IAAAA,GAAG,EAAE,cADM;AAEXC,IAAAA,KAAK,EAAE;AAFI,GA9CY;AAkDzBa,EAAAA,MAAM,EAAE;AACNd,IAAAA,GAAG,EAAE,QADC;AAENC,IAAAA,KAAK,EAAE;AAFD,GAlDiB;AAsDzBc,EAAAA,UAAU,EAAE;AACVf,IAAAA,GAAG,EAAE,aADK;AAEVC,IAAAA,KAAK,EAAE;AAFG,GAtDa;AA0DzBe,EAAAA,YAAY,EAAE;AACZhB,IAAAA,GAAG,EAAE,eADO;AAEZC,IAAAA,KAAK,EAAE;AAFK;AA1DW,CAA3B;;AAgEA,IAAIgB,cAAc,GAAG,UAAUC,KAAV,EAAiBC,KAAjB,EAAwBC,OAAxB,EAAiC;AACpD,MAAIC,MAAJ;AACA,MAAIC,UAAU,GAAGxB,oBAAoB,CAACoB,KAAD,CAArC;;AAEA,MAAI,OAAOI,UAAP,KAAsB,QAA1B,EAAoC;AAClCD,IAAAA,MAAM,GAAGC,UAAT;AACD,GAFD,MAEO,IAAIH,KAAK,KAAK,CAAd,EAAiB;AACtBE,IAAAA,MAAM,GAAGC,UAAU,CAACtB,GAApB;AACD,GAFM,MAEA;AACLqB,IAAAA,MAAM,GAAGC,UAAU,CAACrB,KAAX,CAAiBsB,OAAjB,CAAyB,WAAzB,EAAsCJ,KAAK,CAACK,QAAN,EAAtC,CAAT;AACD;;AAED,MAAIJ,OAAO,KAAK,IAAZ,IAAoBA,OAAO,KAAK,KAAK,CAArC,IAA0CA,OAAO,CAACK,SAAtD,EAAiE;AAC/D,QAAIL,OAAO,CAACM,UAAR,IAAsBN,OAAO,CAACM,UAAR,GAAqB,CAA/C,EAAkD;AAChD,aAAO,QAAQL,MAAf;AACD,KAFD,MAEO;AACL,aAAOA,MAAM,GAAG,MAAhB;AACD;AACF;;AAED,SAAOA,MAAP;AACD,CArBD;;AAuBA,eAAeJ,cAAf", "sourcesContent": ["var formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: 'less than a second',\n    other: 'less than {{count}} seconds'\n  },\n  xSeconds: {\n    one: '1 second',\n    other: '{{count}} seconds'\n  },\n  halfAMinute: 'half a minute',\n  lessThanXMinutes: {\n    one: 'less than a minute',\n    other: 'less than {{count}} minutes'\n  },\n  xMinutes: {\n    one: '1 minute',\n    other: '{{count}} minutes'\n  },\n  aboutXHours: {\n    one: 'about 1 hour',\n    other: 'about {{count}} hours'\n  },\n  xHours: {\n    one: '1 hour',\n    other: '{{count}} hours'\n  },\n  xDays: {\n    one: '1 day',\n    other: '{{count}} days'\n  },\n  aboutXWeeks: {\n    one: 'about 1 week',\n    other: 'about {{count}} weeks'\n  },\n  xWeeks: {\n    one: '1 week',\n    other: '{{count}} weeks'\n  },\n  aboutXMonths: {\n    one: 'about 1 month',\n    other: 'about {{count}} months'\n  },\n  xMonths: {\n    one: '1 month',\n    other: '{{count}} months'\n  },\n  aboutXYears: {\n    one: 'about 1 year',\n    other: 'about {{count}} years'\n  },\n  xYears: {\n    one: '1 year',\n    other: '{{count}} years'\n  },\n  overXYears: {\n    one: 'over 1 year',\n    other: 'over {{count}} years'\n  },\n  almostXYears: {\n    one: 'almost 1 year',\n    other: 'almost {{count}} years'\n  }\n};\n\nvar formatDistance = function (token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace('{{count}}', count.toString());\n  }\n\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return 'in ' + result;\n    } else {\n      return result + ' ago';\n    }\n  }\n\n  return result;\n};\n\nexport default formatDistance;"]}, "metadata": {}, "sourceType": "module"}
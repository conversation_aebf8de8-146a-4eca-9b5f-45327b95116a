{"ast": null, "code": "export var LimitMode;\n(function (LimitMode) {\n  LimitMode[\"delete\"] = \"delete\";\n  LimitMode[\"wait\"] = \"wait\";\n})(LimitMode || (LimitMode = {}));", "map": {"version": 3, "names": ["LimitMode"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/engine/browser/Enums/Modes/LimitMode.js"], "sourcesContent": ["export var LimitMode;\n(function (LimitMode) {\n    LimitMode[\"delete\"] = \"delete\";\n    LimitMode[\"wait\"] = \"wait\";\n})(LimitMode || (LimitMode = {}));\n"], "mappings": "AAAA,OAAO,IAAIA,SAAS;AACpB,CAAC,UAAUA,SAAS,EAAE;EAClBA,SAAS,CAAC,QAAQ,CAAC,GAAG,QAAQ;EAC9BA,SAAS,CAAC,MAAM,CAAC,GAAG,MAAM;AAC9B,CAAC,EAAEA,SAAS,KAAKA,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { setRangeValue } from \"../../../../Utils\";\nexport class Wobble {\n  constructor() {\n    this.distance = 5;\n    this.enable = false;\n    this.speed = 50;\n  }\n\n  load(data) {\n    if (!data) {\n      return;\n    }\n\n    if (data.distance !== undefined) {\n      this.distance = setRangeValue(data.distance);\n    }\n\n    if (data.enable !== undefined) {\n      this.enable = data.enable;\n    }\n\n    if (data.speed !== undefined) {\n      this.speed = setRangeValue(data.speed);\n    }\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Options/Classes/Particles/Wobble/Wobble.js"], "names": ["setRangeValue", "Wobble", "constructor", "distance", "enable", "speed", "load", "data", "undefined"], "mappings": "AAAA,SAASA,aAAT,QAA8B,mBAA9B;AACA,OAAO,MAAMC,MAAN,CAAa;AAChBC,EAAAA,WAAW,GAAG;AACV,SAAKC,QAAL,GAAgB,CAAhB;AACA,SAAKC,MAAL,GAAc,KAAd;AACA,SAAKC,KAAL,GAAa,EAAb;AACH;;AACDC,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,QAAI,CAACA,IAAL,EAAW;AACP;AACH;;AACD,QAAIA,IAAI,CAACJ,QAAL,KAAkBK,SAAtB,EAAiC;AAC7B,WAAKL,QAAL,GAAgBH,aAAa,CAACO,IAAI,CAACJ,QAAN,CAA7B;AACH;;AACD,QAAII,IAAI,CAACH,MAAL,KAAgBI,SAApB,EAA+B;AAC3B,WAAKJ,MAAL,GAAcG,IAAI,CAACH,MAAnB;AACH;;AACD,QAAIG,IAAI,CAACF,KAAL,KAAeG,SAAnB,EAA8B;AAC1B,WAAKH,KAAL,GAAaL,aAAa,CAACO,IAAI,CAACF,KAAN,CAA1B;AACH;AACJ;;AAnBe", "sourcesContent": ["import { setRangeValue } from \"../../../../Utils\";\nexport class Wobble {\n    constructor() {\n        this.distance = 5;\n        this.enable = false;\n        this.speed = 50;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.distance !== undefined) {\n            this.distance = setRangeValue(data.distance);\n        }\n        if (data.enable !== undefined) {\n            this.enable = data.enable;\n        }\n        if (data.speed !== undefined) {\n            this.speed = setRangeValue(data.speed);\n        }\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
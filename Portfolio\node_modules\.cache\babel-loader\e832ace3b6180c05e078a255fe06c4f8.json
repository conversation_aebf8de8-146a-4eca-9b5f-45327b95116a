{"ast": null, "code": "export default function debounce(fn) {\n  var pending;\n  return function () {\n    if (!pending) {\n      pending = new Promise(function (resolve) {\n        Promise.resolve().then(function () {\n          pending = undefined;\n          resolve(fn());\n        });\n      });\n    }\n\n    return pending;\n  };\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/@popperjs/core/lib/utils/debounce.js"], "names": ["debounce", "fn", "pending", "Promise", "resolve", "then", "undefined"], "mappings": "AAAA,eAAe,SAASA,QAAT,CAAkBC,EAAlB,EAAsB;AACnC,MAAIC,OAAJ;AACA,SAAO,YAAY;AACjB,QAAI,CAACA,OAAL,EAAc;AACZA,MAAAA,OAAO,GAAG,IAAIC,OAAJ,CAAY,UAAUC,OAAV,EAAmB;AACvCD,QAAAA,OAAO,CAACC,OAAR,GAAkBC,IAAlB,CAAuB,YAAY;AACjCH,UAAAA,OAAO,GAAGI,SAAV;AACAF,UAAAA,OAAO,CAACH,EAAE,EAAH,CAAP;AACD,SAHD;AAID,OALS,CAAV;AAMD;;AAED,WAAOC,OAAP;AACD,GAXD;AAYD", "sourcesContent": ["export default function debounce(fn) {\n  var pending;\n  return function () {\n    if (!pending) {\n      pending = new Promise(function (resolve) {\n        Promise.resolve().then(function () {\n          pending = undefined;\n          resolve(fn());\n        });\n      });\n    }\n\n    return pending;\n  };\n}"]}, "metadata": {}, "sourceType": "module"}
{"ast": null, "code": "import { getDistance, getLinkColor, getRandom, getRangeValue, rangeColorToRgb } from \"@tsparticles/engine\";\nimport { drawLinkLine, drawLinkTriangle, setLinkFrequency } from \"./Utils.js\";\nconst minOpacity = 0,\n  minWidth = 0,\n  minDistance = 0,\n  half = 0.5,\n  maxFrequency = 1;\nexport class LinkInstance {\n  constructor(container) {\n    this.container = container;\n    this._drawLinkLine = (p1, link) => {\n      const p1LinksOptions = p1.options.links;\n      if (!p1LinksOptions?.enable) {\n        return;\n      }\n      const container = this.container,\n        options = container.actualOptions,\n        p2 = link.destination,\n        pos1 = p1.getPosition(),\n        pos2 = p2.getPosition();\n      let opacity = link.opacity;\n      container.canvas.draw(ctx => {\n        let colorLine;\n        const twinkle = p1.options.twinkle?.lines;\n        if (twinkle?.enable) {\n          const twinkleFreq = twinkle.frequency,\n            twinkleRgb = rangeColorToRgb(twinkle.color),\n            twinkling = getRandom() < twinkleFreq;\n          if (twinkling && twinkleRgb) {\n            colorLine = twinkleRgb;\n            opacity = getRangeValue(twinkle.opacity);\n          }\n        }\n        if (!colorLine) {\n          const linkColor = p1LinksOptions.id !== undefined ? container.particles.linksColors.get(p1LinksOptions.id) : container.particles.linksColor;\n          colorLine = getLinkColor(p1, p2, linkColor);\n        }\n        if (!colorLine) {\n          return;\n        }\n        const width = p1.retina.linksWidth ?? minWidth,\n          maxDistance = p1.retina.linksDistance ?? minDistance,\n          {\n            backgroundMask\n          } = options;\n        drawLinkLine({\n          context: ctx,\n          width,\n          begin: pos1,\n          end: pos2,\n          maxDistance,\n          canvasSize: container.canvas.size,\n          links: p1LinksOptions,\n          backgroundMask: backgroundMask,\n          colorLine,\n          opacity\n        });\n      });\n    };\n    this._drawLinkTriangle = (p1, link1, link2) => {\n      const linksOptions = p1.options.links;\n      if (!linksOptions?.enable) {\n        return;\n      }\n      const triangleOptions = linksOptions.triangles;\n      if (!triangleOptions.enable) {\n        return;\n      }\n      const container = this.container,\n        options = container.actualOptions,\n        p2 = link1.destination,\n        p3 = link2.destination,\n        opacityTriangle = triangleOptions.opacity ?? (link1.opacity + link2.opacity) * half;\n      if (opacityTriangle <= minOpacity) {\n        return;\n      }\n      container.canvas.draw(ctx => {\n        const pos1 = p1.getPosition(),\n          pos2 = p2.getPosition(),\n          pos3 = p3.getPosition(),\n          linksDistance = p1.retina.linksDistance ?? minDistance;\n        if (getDistance(pos1, pos2) > linksDistance || getDistance(pos3, pos2) > linksDistance || getDistance(pos3, pos1) > linksDistance) {\n          return;\n        }\n        let colorTriangle = rangeColorToRgb(triangleOptions.color);\n        if (!colorTriangle) {\n          const linkColor = linksOptions.id !== undefined ? container.particles.linksColors.get(linksOptions.id) : container.particles.linksColor;\n          colorTriangle = getLinkColor(p1, p2, linkColor);\n        }\n        if (!colorTriangle) {\n          return;\n        }\n        drawLinkTriangle({\n          context: ctx,\n          pos1,\n          pos2,\n          pos3,\n          backgroundMask: options.backgroundMask,\n          colorTriangle,\n          opacityTriangle\n        });\n      });\n    };\n    this._drawTriangles = (options, p1, link, p1Links) => {\n      const p2 = link.destination;\n      if (!(options.links?.triangles.enable && p2.options.links?.triangles.enable)) {\n        return;\n      }\n      const vertices = p2.links?.filter(t => {\n        const linkFreq = this._getLinkFrequency(p2, t.destination),\n          minCount = 0;\n        return p2.options.links && linkFreq <= p2.options.links.frequency && p1Links.findIndex(l => l.destination === t.destination) >= minCount;\n      });\n      if (!vertices?.length) {\n        return;\n      }\n      for (const vertex of vertices) {\n        const p3 = vertex.destination,\n          triangleFreq = this._getTriangleFrequency(p1, p2, p3);\n        if (triangleFreq > options.links.triangles.frequency) {\n          continue;\n        }\n        this._drawLinkTriangle(p1, link, vertex);\n      }\n    };\n    this._getLinkFrequency = (p1, p2) => {\n      return setLinkFrequency([p1, p2], this._freqs.links);\n    };\n    this._getTriangleFrequency = (p1, p2, p3) => {\n      return setLinkFrequency([p1, p2, p3], this._freqs.triangles);\n    };\n    this._freqs = {\n      links: new Map(),\n      triangles: new Map()\n    };\n  }\n  drawParticle(context, particle) {\n    const {\n      links,\n      options\n    } = particle;\n    if (!links?.length) {\n      return;\n    }\n    const p1Links = links.filter(l => options.links && (options.links.frequency >= maxFrequency || this._getLinkFrequency(particle, l.destination) <= options.links.frequency));\n    for (const link of p1Links) {\n      this._drawTriangles(options, particle, link, p1Links);\n      if (link.opacity > minOpacity && (particle.retina.linksWidth ?? minWidth) > minWidth) {\n        this._drawLinkLine(particle, link);\n      }\n    }\n  }\n  async init() {\n    this._freqs.links = new Map();\n    this._freqs.triangles = new Map();\n    await Promise.resolve();\n  }\n  particleCreated(particle) {\n    particle.links = [];\n    if (!particle.options.links) {\n      return;\n    }\n    const ratio = this.container.retina.pixelRatio,\n      {\n        retina\n      } = particle,\n      {\n        distance,\n        width\n      } = particle.options.links;\n    retina.linksDistance = distance * ratio;\n    retina.linksWidth = width * ratio;\n  }\n  particleDestroyed(particle) {\n    particle.links = [];\n  }\n}", "map": {"version": 3, "names": ["getDistance", "getLinkColor", "getRandom", "getRangeValue", "rangeColorToRgb", "drawLinkLine", "drawLinkTriangle", "setLinkFrequency", "minOpacity", "min<PERSON><PERSON><PERSON>", "minDistance", "half", "maxFrequency", "LinkInstance", "constructor", "container", "_drawLinkLine", "p1", "link", "p1LinksOptions", "options", "links", "enable", "actualOptions", "p2", "destination", "pos1", "getPosition", "pos2", "opacity", "canvas", "draw", "ctx", "colorLine", "twinkle", "lines", "twinkleFreq", "frequency", "twinkleRgb", "color", "twinkling", "linkColor", "id", "undefined", "particles", "linksColors", "get", "linksColor", "width", "retina", "linksWidth", "maxDistance", "linksDistance", "backgroundMask", "context", "begin", "end", "canvasSize", "size", "_drawLinkTriangle", "link1", "link2", "linksOptions", "triangleOptions", "triangles", "p3", "opacityTriangle", "pos3", "colorTriangle", "_drawTriangles", "p1Links", "vertices", "filter", "t", "linkFreq", "_getLinkFrequency", "minCount", "findIndex", "l", "length", "vertex", "triangleFreq", "_getTriangleFrequency", "_freqs", "Map", "drawParticle", "particle", "init", "Promise", "resolve", "particleCreated", "ratio", "pixelRatio", "distance", "particleDestroyed"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/interaction-particles-links/browser/LinkInstance.js"], "sourcesContent": ["import { getDistance, getLinkColor, getRandom, getRangeValue, rangeColorToRgb, } from \"@tsparticles/engine\";\nimport { drawLinkLine, drawLinkTriangle, setLinkFrequency } from \"./Utils.js\";\nconst minOpacity = 0, minWidth = 0, minDistance = 0, half = 0.5, maxFrequency = 1;\nexport class LinkInstance {\n    constructor(container) {\n        this.container = container;\n        this._drawLinkLine = (p1, link) => {\n            const p1LinksOptions = p1.options.links;\n            if (!p1LinksOptions?.enable) {\n                return;\n            }\n            const container = this.container, options = container.actualOptions, p2 = link.destination, pos1 = p1.getPosition(), pos2 = p2.getPosition();\n            let opacity = link.opacity;\n            container.canvas.draw(ctx => {\n                let colorLine;\n                const twinkle = p1.options.twinkle?.lines;\n                if (twinkle?.enable) {\n                    const twinkleFreq = twinkle.frequency, twinkleRgb = rangeColorToRgb(twinkle.color), twinkling = getRandom() < twinkleFreq;\n                    if (twinkling && twinkleRgb) {\n                        colorLine = twinkleRgb;\n                        opacity = getRangeValue(twinkle.opacity);\n                    }\n                }\n                if (!colorLine) {\n                    const linkColor = p1LinksOptions.id !== undefined\n                        ? container.particles.linksColors.get(p1LinksOptions.id)\n                        : container.particles.linksColor;\n                    colorLine = getLinkColor(p1, p2, linkColor);\n                }\n                if (!colorLine) {\n                    return;\n                }\n                const width = p1.retina.linksWidth ?? minWidth, maxDistance = p1.retina.linksDistance ?? minDistance, { backgroundMask } = options;\n                drawLinkLine({\n                    context: ctx,\n                    width,\n                    begin: pos1,\n                    end: pos2,\n                    maxDistance,\n                    canvasSize: container.canvas.size,\n                    links: p1LinksOptions,\n                    backgroundMask: backgroundMask,\n                    colorLine,\n                    opacity,\n                });\n            });\n        };\n        this._drawLinkTriangle = (p1, link1, link2) => {\n            const linksOptions = p1.options.links;\n            if (!linksOptions?.enable) {\n                return;\n            }\n            const triangleOptions = linksOptions.triangles;\n            if (!triangleOptions.enable) {\n                return;\n            }\n            const container = this.container, options = container.actualOptions, p2 = link1.destination, p3 = link2.destination, opacityTriangle = triangleOptions.opacity ?? (link1.opacity + link2.opacity) * half;\n            if (opacityTriangle <= minOpacity) {\n                return;\n            }\n            container.canvas.draw(ctx => {\n                const pos1 = p1.getPosition(), pos2 = p2.getPosition(), pos3 = p3.getPosition(), linksDistance = p1.retina.linksDistance ?? minDistance;\n                if (getDistance(pos1, pos2) > linksDistance ||\n                    getDistance(pos3, pos2) > linksDistance ||\n                    getDistance(pos3, pos1) > linksDistance) {\n                    return;\n                }\n                let colorTriangle = rangeColorToRgb(triangleOptions.color);\n                if (!colorTriangle) {\n                    const linkColor = linksOptions.id !== undefined\n                        ? container.particles.linksColors.get(linksOptions.id)\n                        : container.particles.linksColor;\n                    colorTriangle = getLinkColor(p1, p2, linkColor);\n                }\n                if (!colorTriangle) {\n                    return;\n                }\n                drawLinkTriangle({\n                    context: ctx,\n                    pos1,\n                    pos2,\n                    pos3,\n                    backgroundMask: options.backgroundMask,\n                    colorTriangle,\n                    opacityTriangle,\n                });\n            });\n        };\n        this._drawTriangles = (options, p1, link, p1Links) => {\n            const p2 = link.destination;\n            if (!(options.links?.triangles.enable && p2.options.links?.triangles.enable)) {\n                return;\n            }\n            const vertices = p2.links?.filter(t => {\n                const linkFreq = this._getLinkFrequency(p2, t.destination), minCount = 0;\n                return (p2.options.links &&\n                    linkFreq <= p2.options.links.frequency &&\n                    p1Links.findIndex(l => l.destination === t.destination) >= minCount);\n            });\n            if (!vertices?.length) {\n                return;\n            }\n            for (const vertex of vertices) {\n                const p3 = vertex.destination, triangleFreq = this._getTriangleFrequency(p1, p2, p3);\n                if (triangleFreq > options.links.triangles.frequency) {\n                    continue;\n                }\n                this._drawLinkTriangle(p1, link, vertex);\n            }\n        };\n        this._getLinkFrequency = (p1, p2) => {\n            return setLinkFrequency([p1, p2], this._freqs.links);\n        };\n        this._getTriangleFrequency = (p1, p2, p3) => {\n            return setLinkFrequency([p1, p2, p3], this._freqs.triangles);\n        };\n        this._freqs = {\n            links: new Map(),\n            triangles: new Map(),\n        };\n    }\n    drawParticle(context, particle) {\n        const { links, options } = particle;\n        if (!links?.length) {\n            return;\n        }\n        const p1Links = links.filter(l => options.links &&\n            (options.links.frequency >= maxFrequency ||\n                this._getLinkFrequency(particle, l.destination) <= options.links.frequency));\n        for (const link of p1Links) {\n            this._drawTriangles(options, particle, link, p1Links);\n            if (link.opacity > minOpacity && (particle.retina.linksWidth ?? minWidth) > minWidth) {\n                this._drawLinkLine(particle, link);\n            }\n        }\n    }\n    async init() {\n        this._freqs.links = new Map();\n        this._freqs.triangles = new Map();\n        await Promise.resolve();\n    }\n    particleCreated(particle) {\n        particle.links = [];\n        if (!particle.options.links) {\n            return;\n        }\n        const ratio = this.container.retina.pixelRatio, { retina } = particle, { distance, width } = particle.options.links;\n        retina.linksDistance = distance * ratio;\n        retina.linksWidth = width * ratio;\n    }\n    particleDestroyed(particle) {\n        particle.links = [];\n    }\n}\n"], "mappings": "AAAA,SAASA,WAAW,EAAEC,YAAY,EAAEC,SAAS,EAAEC,aAAa,EAAEC,eAAe,QAAS,qBAAqB;AAC3G,SAASC,YAAY,EAAEC,gBAAgB,EAAEC,gBAAgB,QAAQ,YAAY;AAC7E,MAAMC,UAAU,GAAG,CAAC;EAAEC,QAAQ,GAAG,CAAC;EAAEC,WAAW,GAAG,CAAC;EAAEC,IAAI,GAAG,GAAG;EAAEC,YAAY,GAAG,CAAC;AACjF,OAAO,MAAMC,YAAY,CAAC;EACtBC,WAAWA,CAACC,SAAS,EAAE;IACnB,IAAI,CAACA,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,aAAa,GAAG,CAACC,EAAE,EAAEC,IAAI,KAAK;MAC/B,MAAMC,cAAc,GAAGF,EAAE,CAACG,OAAO,CAACC,KAAK;MACvC,IAAI,CAACF,cAAc,EAAEG,MAAM,EAAE;QACzB;MACJ;MACA,MAAMP,SAAS,GAAG,IAAI,CAACA,SAAS;QAAEK,OAAO,GAAGL,SAAS,CAACQ,aAAa;QAAEC,EAAE,GAAGN,IAAI,CAACO,WAAW;QAAEC,IAAI,GAAGT,EAAE,CAACU,WAAW,CAAC,CAAC;QAAEC,IAAI,GAAGJ,EAAE,CAACG,WAAW,CAAC,CAAC;MAC5I,IAAIE,OAAO,GAAGX,IAAI,CAACW,OAAO;MAC1Bd,SAAS,CAACe,MAAM,CAACC,IAAI,CAACC,GAAG,IAAI;QACzB,IAAIC,SAAS;QACb,MAAMC,OAAO,GAAGjB,EAAE,CAACG,OAAO,CAACc,OAAO,EAAEC,KAAK;QACzC,IAAID,OAAO,EAAEZ,MAAM,EAAE;UACjB,MAAMc,WAAW,GAAGF,OAAO,CAACG,SAAS;YAAEC,UAAU,GAAGlC,eAAe,CAAC8B,OAAO,CAACK,KAAK,CAAC;YAAEC,SAAS,GAAGtC,SAAS,CAAC,CAAC,GAAGkC,WAAW;UACzH,IAAII,SAAS,IAAIF,UAAU,EAAE;YACzBL,SAAS,GAAGK,UAAU;YACtBT,OAAO,GAAG1B,aAAa,CAAC+B,OAAO,CAACL,OAAO,CAAC;UAC5C;QACJ;QACA,IAAI,CAACI,SAAS,EAAE;UACZ,MAAMQ,SAAS,GAAGtB,cAAc,CAACuB,EAAE,KAAKC,SAAS,GAC3C5B,SAAS,CAAC6B,SAAS,CAACC,WAAW,CAACC,GAAG,CAAC3B,cAAc,CAACuB,EAAE,CAAC,GACtD3B,SAAS,CAAC6B,SAAS,CAACG,UAAU;UACpCd,SAAS,GAAGhC,YAAY,CAACgB,EAAE,EAAEO,EAAE,EAAEiB,SAAS,CAAC;QAC/C;QACA,IAAI,CAACR,SAAS,EAAE;UACZ;QACJ;QACA,MAAMe,KAAK,GAAG/B,EAAE,CAACgC,MAAM,CAACC,UAAU,IAAIzC,QAAQ;UAAE0C,WAAW,GAAGlC,EAAE,CAACgC,MAAM,CAACG,aAAa,IAAI1C,WAAW;UAAE;YAAE2C;UAAe,CAAC,GAAGjC,OAAO;QAClIf,YAAY,CAAC;UACTiD,OAAO,EAAEtB,GAAG;UACZgB,KAAK;UACLO,KAAK,EAAE7B,IAAI;UACX8B,GAAG,EAAE5B,IAAI;UACTuB,WAAW;UACXM,UAAU,EAAE1C,SAAS,CAACe,MAAM,CAAC4B,IAAI;UACjCrC,KAAK,EAAEF,cAAc;UACrBkC,cAAc,EAAEA,cAAc;UAC9BpB,SAAS;UACTJ;QACJ,CAAC,CAAC;MACN,CAAC,CAAC;IACN,CAAC;IACD,IAAI,CAAC8B,iBAAiB,GAAG,CAAC1C,EAAE,EAAE2C,KAAK,EAAEC,KAAK,KAAK;MAC3C,MAAMC,YAAY,GAAG7C,EAAE,CAACG,OAAO,CAACC,KAAK;MACrC,IAAI,CAACyC,YAAY,EAAExC,MAAM,EAAE;QACvB;MACJ;MACA,MAAMyC,eAAe,GAAGD,YAAY,CAACE,SAAS;MAC9C,IAAI,CAACD,eAAe,CAACzC,MAAM,EAAE;QACzB;MACJ;MACA,MAAMP,SAAS,GAAG,IAAI,CAACA,SAAS;QAAEK,OAAO,GAAGL,SAAS,CAACQ,aAAa;QAAEC,EAAE,GAAGoC,KAAK,CAACnC,WAAW;QAAEwC,EAAE,GAAGJ,KAAK,CAACpC,WAAW;QAAEyC,eAAe,GAAGH,eAAe,CAAClC,OAAO,IAAI,CAAC+B,KAAK,CAAC/B,OAAO,GAAGgC,KAAK,CAAChC,OAAO,IAAIlB,IAAI;MACxM,IAAIuD,eAAe,IAAI1D,UAAU,EAAE;QAC/B;MACJ;MACAO,SAAS,CAACe,MAAM,CAACC,IAAI,CAACC,GAAG,IAAI;QACzB,MAAMN,IAAI,GAAGT,EAAE,CAACU,WAAW,CAAC,CAAC;UAAEC,IAAI,GAAGJ,EAAE,CAACG,WAAW,CAAC,CAAC;UAAEwC,IAAI,GAAGF,EAAE,CAACtC,WAAW,CAAC,CAAC;UAAEyB,aAAa,GAAGnC,EAAE,CAACgC,MAAM,CAACG,aAAa,IAAI1C,WAAW;QACvI,IAAIV,WAAW,CAAC0B,IAAI,EAAEE,IAAI,CAAC,GAAGwB,aAAa,IACvCpD,WAAW,CAACmE,IAAI,EAAEvC,IAAI,CAAC,GAAGwB,aAAa,IACvCpD,WAAW,CAACmE,IAAI,EAAEzC,IAAI,CAAC,GAAG0B,aAAa,EAAE;UACzC;QACJ;QACA,IAAIgB,aAAa,GAAGhE,eAAe,CAAC2D,eAAe,CAACxB,KAAK,CAAC;QAC1D,IAAI,CAAC6B,aAAa,EAAE;UAChB,MAAM3B,SAAS,GAAGqB,YAAY,CAACpB,EAAE,KAAKC,SAAS,GACzC5B,SAAS,CAAC6B,SAAS,CAACC,WAAW,CAACC,GAAG,CAACgB,YAAY,CAACpB,EAAE,CAAC,GACpD3B,SAAS,CAAC6B,SAAS,CAACG,UAAU;UACpCqB,aAAa,GAAGnE,YAAY,CAACgB,EAAE,EAAEO,EAAE,EAAEiB,SAAS,CAAC;QACnD;QACA,IAAI,CAAC2B,aAAa,EAAE;UAChB;QACJ;QACA9D,gBAAgB,CAAC;UACbgD,OAAO,EAAEtB,GAAG;UACZN,IAAI;UACJE,IAAI;UACJuC,IAAI;UACJd,cAAc,EAAEjC,OAAO,CAACiC,cAAc;UACtCe,aAAa;UACbF;QACJ,CAAC,CAAC;MACN,CAAC,CAAC;IACN,CAAC;IACD,IAAI,CAACG,cAAc,GAAG,CAACjD,OAAO,EAAEH,EAAE,EAAEC,IAAI,EAAEoD,OAAO,KAAK;MAClD,MAAM9C,EAAE,GAAGN,IAAI,CAACO,WAAW;MAC3B,IAAI,EAAEL,OAAO,CAACC,KAAK,EAAE2C,SAAS,CAAC1C,MAAM,IAAIE,EAAE,CAACJ,OAAO,CAACC,KAAK,EAAE2C,SAAS,CAAC1C,MAAM,CAAC,EAAE;QAC1E;MACJ;MACA,MAAMiD,QAAQ,GAAG/C,EAAE,CAACH,KAAK,EAAEmD,MAAM,CAACC,CAAC,IAAI;QACnC,MAAMC,QAAQ,GAAG,IAAI,CAACC,iBAAiB,CAACnD,EAAE,EAAEiD,CAAC,CAAChD,WAAW,CAAC;UAAEmD,QAAQ,GAAG,CAAC;QACxE,OAAQpD,EAAE,CAACJ,OAAO,CAACC,KAAK,IACpBqD,QAAQ,IAAIlD,EAAE,CAACJ,OAAO,CAACC,KAAK,CAACgB,SAAS,IACtCiC,OAAO,CAACO,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACrD,WAAW,KAAKgD,CAAC,CAAChD,WAAW,CAAC,IAAImD,QAAQ;MAC3E,CAAC,CAAC;MACF,IAAI,CAACL,QAAQ,EAAEQ,MAAM,EAAE;QACnB;MACJ;MACA,KAAK,MAAMC,MAAM,IAAIT,QAAQ,EAAE;QAC3B,MAAMN,EAAE,GAAGe,MAAM,CAACvD,WAAW;UAAEwD,YAAY,GAAG,IAAI,CAACC,qBAAqB,CAACjE,EAAE,EAAEO,EAAE,EAAEyC,EAAE,CAAC;QACpF,IAAIgB,YAAY,GAAG7D,OAAO,CAACC,KAAK,CAAC2C,SAAS,CAAC3B,SAAS,EAAE;UAClD;QACJ;QACA,IAAI,CAACsB,iBAAiB,CAAC1C,EAAE,EAAEC,IAAI,EAAE8D,MAAM,CAAC;MAC5C;IACJ,CAAC;IACD,IAAI,CAACL,iBAAiB,GAAG,CAAC1D,EAAE,EAAEO,EAAE,KAAK;MACjC,OAAOjB,gBAAgB,CAAC,CAACU,EAAE,EAAEO,EAAE,CAAC,EAAE,IAAI,CAAC2D,MAAM,CAAC9D,KAAK,CAAC;IACxD,CAAC;IACD,IAAI,CAAC6D,qBAAqB,GAAG,CAACjE,EAAE,EAAEO,EAAE,EAAEyC,EAAE,KAAK;MACzC,OAAO1D,gBAAgB,CAAC,CAACU,EAAE,EAAEO,EAAE,EAAEyC,EAAE,CAAC,EAAE,IAAI,CAACkB,MAAM,CAACnB,SAAS,CAAC;IAChE,CAAC;IACD,IAAI,CAACmB,MAAM,GAAG;MACV9D,KAAK,EAAE,IAAI+D,GAAG,CAAC,CAAC;MAChBpB,SAAS,EAAE,IAAIoB,GAAG,CAAC;IACvB,CAAC;EACL;EACAC,YAAYA,CAAC/B,OAAO,EAAEgC,QAAQ,EAAE;IAC5B,MAAM;MAAEjE,KAAK;MAAED;IAAQ,CAAC,GAAGkE,QAAQ;IACnC,IAAI,CAACjE,KAAK,EAAE0D,MAAM,EAAE;MAChB;IACJ;IACA,MAAMT,OAAO,GAAGjD,KAAK,CAACmD,MAAM,CAACM,CAAC,IAAI1D,OAAO,CAACC,KAAK,KAC1CD,OAAO,CAACC,KAAK,CAACgB,SAAS,IAAIzB,YAAY,IACpC,IAAI,CAAC+D,iBAAiB,CAACW,QAAQ,EAAER,CAAC,CAACrD,WAAW,CAAC,IAAIL,OAAO,CAACC,KAAK,CAACgB,SAAS,CAAC,CAAC;IACpF,KAAK,MAAMnB,IAAI,IAAIoD,OAAO,EAAE;MACxB,IAAI,CAACD,cAAc,CAACjD,OAAO,EAAEkE,QAAQ,EAAEpE,IAAI,EAAEoD,OAAO,CAAC;MACrD,IAAIpD,IAAI,CAACW,OAAO,GAAGrB,UAAU,IAAI,CAAC8E,QAAQ,CAACrC,MAAM,CAACC,UAAU,IAAIzC,QAAQ,IAAIA,QAAQ,EAAE;QAClF,IAAI,CAACO,aAAa,CAACsE,QAAQ,EAAEpE,IAAI,CAAC;MACtC;IACJ;EACJ;EACA,MAAMqE,IAAIA,CAAA,EAAG;IACT,IAAI,CAACJ,MAAM,CAAC9D,KAAK,GAAG,IAAI+D,GAAG,CAAC,CAAC;IAC7B,IAAI,CAACD,MAAM,CAACnB,SAAS,GAAG,IAAIoB,GAAG,CAAC,CAAC;IACjC,MAAMI,OAAO,CAACC,OAAO,CAAC,CAAC;EAC3B;EACAC,eAAeA,CAACJ,QAAQ,EAAE;IACtBA,QAAQ,CAACjE,KAAK,GAAG,EAAE;IACnB,IAAI,CAACiE,QAAQ,CAAClE,OAAO,CAACC,KAAK,EAAE;MACzB;IACJ;IACA,MAAMsE,KAAK,GAAG,IAAI,CAAC5E,SAAS,CAACkC,MAAM,CAAC2C,UAAU;MAAE;QAAE3C;MAAO,CAAC,GAAGqC,QAAQ;MAAE;QAAEO,QAAQ;QAAE7C;MAAM,CAAC,GAAGsC,QAAQ,CAAClE,OAAO,CAACC,KAAK;IACnH4B,MAAM,CAACG,aAAa,GAAGyC,QAAQ,GAAGF,KAAK;IACvC1C,MAAM,CAACC,UAAU,GAAGF,KAAK,GAAG2C,KAAK;EACrC;EACAG,iBAAiBA,CAACR,QAAQ,EAAE;IACxBA,QAAQ,CAACjE,KAAK,GAAG,EAAE;EACvB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
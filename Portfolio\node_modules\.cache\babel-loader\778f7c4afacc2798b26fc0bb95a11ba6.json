{"ast": null, "code": "export class Density {\n  constructor() {\n    this.enable = false;\n    this.area = 800;\n    this.factor = 1000;\n  }\n\n  get value_area() {\n    return this.area;\n  }\n\n  set value_area(value) {\n    this.area = value;\n  }\n\n  load(data) {\n    var _a;\n\n    if (data === undefined) {\n      return;\n    }\n\n    if (data.enable !== undefined) {\n      this.enable = data.enable;\n    }\n\n    const area = (_a = data.area) !== null && _a !== void 0 ? _a : data.value_area;\n\n    if (area !== undefined) {\n      this.area = area;\n    }\n\n    if (data.factor !== undefined) {\n      this.factor = data.factor;\n    }\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Options/Classes/Particles/Number/Density.js"], "names": ["Density", "constructor", "enable", "area", "factor", "value_area", "value", "load", "data", "_a", "undefined"], "mappings": "AAAA,OAAO,MAAMA,OAAN,CAAc;AACjBC,EAAAA,WAAW,GAAG;AACV,SAAKC,MAAL,GAAc,KAAd;AACA,SAAKC,IAAL,GAAY,GAAZ;AACA,SAAKC,MAAL,GAAc,IAAd;AACH;;AACa,MAAVC,UAAU,GAAG;AACb,WAAO,KAAKF,IAAZ;AACH;;AACa,MAAVE,UAAU,CAACC,KAAD,EAAQ;AAClB,SAAKH,IAAL,GAAYG,KAAZ;AACH;;AACDC,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,QAAIC,EAAJ;;AACA,QAAID,IAAI,KAAKE,SAAb,EAAwB;AACpB;AACH;;AACD,QAAIF,IAAI,CAACN,MAAL,KAAgBQ,SAApB,EAA+B;AAC3B,WAAKR,MAAL,GAAcM,IAAI,CAACN,MAAnB;AACH;;AACD,UAAMC,IAAI,GAAG,CAACM,EAAE,GAAGD,IAAI,CAACL,IAAX,MAAqB,IAArB,IAA6BM,EAAE,KAAK,KAAK,CAAzC,GAA6CA,EAA7C,GAAkDD,IAAI,CAACH,UAApE;;AACA,QAAIF,IAAI,KAAKO,SAAb,EAAwB;AACpB,WAAKP,IAAL,GAAYA,IAAZ;AACH;;AACD,QAAIK,IAAI,CAACJ,MAAL,KAAgBM,SAApB,EAA+B;AAC3B,WAAKN,MAAL,GAAcI,IAAI,CAACJ,MAAnB;AACH;AACJ;;AA3BgB", "sourcesContent": ["export class Density {\n    constructor() {\n        this.enable = false;\n        this.area = 800;\n        this.factor = 1000;\n    }\n    get value_area() {\n        return this.area;\n    }\n    set value_area(value) {\n        this.area = value;\n    }\n    load(data) {\n        var _a;\n        if (data === undefined) {\n            return;\n        }\n        if (data.enable !== undefined) {\n            this.enable = data.enable;\n        }\n        const area = (_a = data.area) !== null && _a !== void 0 ? _a : data.value_area;\n        if (area !== undefined) {\n            this.area = area;\n        }\n        if (data.factor !== undefined) {\n            this.factor = data.factor;\n        }\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
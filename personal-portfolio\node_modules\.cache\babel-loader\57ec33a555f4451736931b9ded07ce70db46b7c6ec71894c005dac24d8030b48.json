{"ast": null, "code": "import { Circle, Vector, clamp, getDistances, getEasing } from \"@tsparticles/engine\";\nconst minFactor = 1,\n  identity = 1,\n  minRadius = 0;\nfunction processAttract(container, position, attractRadius, area, queryCb) {\n  const attractOptions = container.actualOptions.interactivity.modes.attract;\n  if (!attractOptions) {\n    return;\n  }\n  const query = container.particles.quadTree.query(area, queryCb);\n  for (const particle of query) {\n    const {\n        dx,\n        dy,\n        distance\n      } = getDistances(particle.position, position),\n      velocity = attractOptions.speed * attractOptions.factor,\n      attractFactor = clamp(getEasing(attractOptions.easing)(identity - distance / attractRadius) * velocity, minFactor, attractOptions.maxSpeed),\n      normVec = Vector.create(!distance ? velocity : dx / distance * attractFactor, !distance ? velocity : dy / distance * attractFactor);\n    particle.position.subFrom(normVec);\n  }\n}\nexport function clickAttract(container, enabledCb) {\n  if (!container.attract) {\n    container.attract = {\n      particles: []\n    };\n  }\n  const {\n    attract\n  } = container;\n  if (!attract.finish) {\n    if (!attract.count) {\n      attract.count = 0;\n    }\n    attract.count++;\n    if (attract.count === container.particles.count) {\n      attract.finish = true;\n    }\n  }\n  if (attract.clicking) {\n    const mousePos = container.interactivity.mouse.clickPosition,\n      attractRadius = container.retina.attractModeDistance;\n    if (!attractRadius || attractRadius < minRadius || !mousePos) {\n      return;\n    }\n    processAttract(container, mousePos, attractRadius, new Circle(mousePos.x, mousePos.y, attractRadius), p => enabledCb(p));\n  } else if (attract.clicking === false) {\n    attract.particles = [];\n  }\n}\nexport function hoverAttract(container, enabledCb) {\n  const mousePos = container.interactivity.mouse.position,\n    attractRadius = container.retina.attractModeDistance;\n  if (!attractRadius || attractRadius < minRadius || !mousePos) {\n    return;\n  }\n  processAttract(container, mousePos, attractRadius, new Circle(mousePos.x, mousePos.y, attractRadius), p => enabledCb(p));\n}", "map": {"version": 3, "names": ["Circle", "Vector", "clamp", "getDistances", "getEasing", "minFactor", "identity", "minRadius", "processAttract", "container", "position", "attractRadius", "area", "queryCb", "attractOptions", "actualOptions", "interactivity", "modes", "attract", "query", "particles", "quadTree", "particle", "dx", "dy", "distance", "velocity", "speed", "factor", "attractFactor", "easing", "maxSpeed", "normVec", "create", "subFrom", "clickAttract", "enabledCb", "finish", "count", "clicking", "mousePos", "mouse", "clickPosition", "retina", "attractModeDistance", "x", "y", "p", "hoverAttract"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/interaction-external-attract/browser/Utils.js"], "sourcesContent": ["import { Circle, Vector, clamp, getDistances, getEasing, } from \"@tsparticles/engine\";\nconst minFactor = 1, identity = 1, minRadius = 0;\nfunction processAttract(container, position, attractRadius, area, queryCb) {\n    const attractOptions = container.actualOptions.interactivity.modes.attract;\n    if (!attractOptions) {\n        return;\n    }\n    const query = container.particles.quadTree.query(area, queryCb);\n    for (const particle of query) {\n        const { dx, dy, distance } = getDistances(particle.position, position), velocity = attractOptions.speed * attractOptions.factor, attractFactor = clamp(getEasing(attractOptions.easing)(identity - distance / attractRadius) * velocity, minFactor, attractOptions.maxSpeed), normVec = Vector.create(!distance ? velocity : (dx / distance) * attractFactor, !distance ? velocity : (dy / distance) * attractFactor);\n        particle.position.subFrom(normVec);\n    }\n}\nexport function clickAttract(container, enabledCb) {\n    if (!container.attract) {\n        container.attract = { particles: [] };\n    }\n    const { attract } = container;\n    if (!attract.finish) {\n        if (!attract.count) {\n            attract.count = 0;\n        }\n        attract.count++;\n        if (attract.count === container.particles.count) {\n            attract.finish = true;\n        }\n    }\n    if (attract.clicking) {\n        const mousePos = container.interactivity.mouse.clickPosition, attractRadius = container.retina.attractModeDistance;\n        if (!attractRadius || attractRadius < minRadius || !mousePos) {\n            return;\n        }\n        processAttract(container, mousePos, attractRadius, new Circle(mousePos.x, mousePos.y, attractRadius), (p) => enabledCb(p));\n    }\n    else if (attract.clicking === false) {\n        attract.particles = [];\n    }\n}\nexport function hoverAttract(container, enabledCb) {\n    const mousePos = container.interactivity.mouse.position, attractRadius = container.retina.attractModeDistance;\n    if (!attractRadius || attractRadius < minRadius || !mousePos) {\n        return;\n    }\n    processAttract(container, mousePos, attractRadius, new Circle(mousePos.x, mousePos.y, attractRadius), (p) => enabledCb(p));\n}\n"], "mappings": "AAAA,SAASA,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,YAAY,EAAEC,SAAS,QAAS,qBAAqB;AACrF,MAAMC,SAAS,GAAG,CAAC;EAAEC,QAAQ,GAAG,CAAC;EAAEC,SAAS,GAAG,CAAC;AAChD,SAASC,cAAcA,CAACC,SAAS,EAAEC,QAAQ,EAAEC,aAAa,EAAEC,IAAI,EAAEC,OAAO,EAAE;EACvE,MAAMC,cAAc,GAAGL,SAAS,CAACM,aAAa,CAACC,aAAa,CAACC,KAAK,CAACC,OAAO;EAC1E,IAAI,CAACJ,cAAc,EAAE;IACjB;EACJ;EACA,MAAMK,KAAK,GAAGV,SAAS,CAACW,SAAS,CAACC,QAAQ,CAACF,KAAK,CAACP,IAAI,EAAEC,OAAO,CAAC;EAC/D,KAAK,MAAMS,QAAQ,IAAIH,KAAK,EAAE;IAC1B,MAAM;QAAEI,EAAE;QAAEC,EAAE;QAAEC;MAAS,CAAC,GAAGtB,YAAY,CAACmB,QAAQ,CAACZ,QAAQ,EAAEA,QAAQ,CAAC;MAAEgB,QAAQ,GAAGZ,cAAc,CAACa,KAAK,GAAGb,cAAc,CAACc,MAAM;MAAEC,aAAa,GAAG3B,KAAK,CAACE,SAAS,CAACU,cAAc,CAACgB,MAAM,CAAC,CAACxB,QAAQ,GAAGmB,QAAQ,GAAGd,aAAa,CAAC,GAAGe,QAAQ,EAAErB,SAAS,EAAES,cAAc,CAACiB,QAAQ,CAAC;MAAEC,OAAO,GAAG/B,MAAM,CAACgC,MAAM,CAAC,CAACR,QAAQ,GAAGC,QAAQ,GAAIH,EAAE,GAAGE,QAAQ,GAAII,aAAa,EAAE,CAACJ,QAAQ,GAAGC,QAAQ,GAAIF,EAAE,GAAGC,QAAQ,GAAII,aAAa,CAAC;IACrZP,QAAQ,CAACZ,QAAQ,CAACwB,OAAO,CAACF,OAAO,CAAC;EACtC;AACJ;AACA,OAAO,SAASG,YAAYA,CAAC1B,SAAS,EAAE2B,SAAS,EAAE;EAC/C,IAAI,CAAC3B,SAAS,CAACS,OAAO,EAAE;IACpBT,SAAS,CAACS,OAAO,GAAG;MAAEE,SAAS,EAAE;IAAG,CAAC;EACzC;EACA,MAAM;IAAEF;EAAQ,CAAC,GAAGT,SAAS;EAC7B,IAAI,CAACS,OAAO,CAACmB,MAAM,EAAE;IACjB,IAAI,CAACnB,OAAO,CAACoB,KAAK,EAAE;MAChBpB,OAAO,CAACoB,KAAK,GAAG,CAAC;IACrB;IACApB,OAAO,CAACoB,KAAK,EAAE;IACf,IAAIpB,OAAO,CAACoB,KAAK,KAAK7B,SAAS,CAACW,SAAS,CAACkB,KAAK,EAAE;MAC7CpB,OAAO,CAACmB,MAAM,GAAG,IAAI;IACzB;EACJ;EACA,IAAInB,OAAO,CAACqB,QAAQ,EAAE;IAClB,MAAMC,QAAQ,GAAG/B,SAAS,CAACO,aAAa,CAACyB,KAAK,CAACC,aAAa;MAAE/B,aAAa,GAAGF,SAAS,CAACkC,MAAM,CAACC,mBAAmB;IAClH,IAAI,CAACjC,aAAa,IAAIA,aAAa,GAAGJ,SAAS,IAAI,CAACiC,QAAQ,EAAE;MAC1D;IACJ;IACAhC,cAAc,CAACC,SAAS,EAAE+B,QAAQ,EAAE7B,aAAa,EAAE,IAAIX,MAAM,CAACwC,QAAQ,CAACK,CAAC,EAAEL,QAAQ,CAACM,CAAC,EAAEnC,aAAa,CAAC,EAAGoC,CAAC,IAAKX,SAAS,CAACW,CAAC,CAAC,CAAC;EAC9H,CAAC,MACI,IAAI7B,OAAO,CAACqB,QAAQ,KAAK,KAAK,EAAE;IACjCrB,OAAO,CAACE,SAAS,GAAG,EAAE;EAC1B;AACJ;AACA,OAAO,SAAS4B,YAAYA,CAACvC,SAAS,EAAE2B,SAAS,EAAE;EAC/C,MAAMI,QAAQ,GAAG/B,SAAS,CAACO,aAAa,CAACyB,KAAK,CAAC/B,QAAQ;IAAEC,aAAa,GAAGF,SAAS,CAACkC,MAAM,CAACC,mBAAmB;EAC7G,IAAI,CAACjC,aAAa,IAAIA,aAAa,GAAGJ,SAAS,IAAI,CAACiC,QAAQ,EAAE;IAC1D;EACJ;EACAhC,cAAc,CAACC,SAAS,EAAE+B,QAAQ,EAAE7B,aAAa,EAAE,IAAIX,MAAM,CAACwC,QAAQ,CAACK,CAAC,EAAEL,QAAQ,CAACM,CAAC,EAAEnC,aAAa,CAAC,EAAGoC,CAAC,IAAKX,SAAS,CAACW,CAAC,CAAC,CAAC;AAC9H", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
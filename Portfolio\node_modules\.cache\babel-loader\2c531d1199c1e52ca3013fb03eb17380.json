{"ast": null, "code": "import toDate from \"../../toDate/index.js\";\nimport requiredArgs from \"../requiredArgs/index.js\";\nvar MILLISECONDS_IN_DAY = 86400000; // This function will be a part of public API when UTC function will be implemented.\n// See issue: https://github.com/date-fns/date-fns/issues/376\n\nexport default function getUTCDayOfYear(dirtyDate) {\n  requiredArgs(1, arguments);\n  var date = toDate(dirtyDate);\n  var timestamp = date.getTime();\n  date.setUTCMonth(0, 1);\n  date.setUTCHours(0, 0, 0, 0);\n  var startOfYearTimestamp = date.getTime();\n  var difference = timestamp - startOfYearTimestamp;\n  return Math.floor(difference / MILLISECONDS_IN_DAY) + 1;\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/date-fns/esm/_lib/getUTCDayOfYear/index.js"], "names": ["toDate", "requiredArgs", "MILLISECONDS_IN_DAY", "getUTCDayOfYear", "dirtyDate", "arguments", "date", "timestamp", "getTime", "setUTCMonth", "setUTCHours", "startOfYearTimestamp", "difference", "Math", "floor"], "mappings": "AAAA,OAAOA,MAAP,MAAmB,uBAAnB;AACA,OAAOC,YAAP,MAAyB,0BAAzB;AACA,IAAIC,mBAAmB,GAAG,QAA1B,C,CAAoC;AACpC;;AAEA,eAAe,SAASC,eAAT,CAAyBC,SAAzB,EAAoC;AACjDH,EAAAA,YAAY,CAAC,CAAD,EAAII,SAAJ,CAAZ;AACA,MAAIC,IAAI,GAAGN,MAAM,CAACI,SAAD,CAAjB;AACA,MAAIG,SAAS,GAAGD,IAAI,CAACE,OAAL,EAAhB;AACAF,EAAAA,IAAI,CAACG,WAAL,CAAiB,CAAjB,EAAoB,CAApB;AACAH,EAAAA,IAAI,CAACI,WAAL,CAAiB,CAAjB,EAAoB,CAApB,EAAuB,CAAvB,EAA0B,CAA1B;AACA,MAAIC,oBAAoB,GAAGL,IAAI,CAACE,OAAL,EAA3B;AACA,MAAII,UAAU,GAAGL,SAAS,GAAGI,oBAA7B;AACA,SAAOE,IAAI,CAACC,KAAL,CAAWF,UAAU,GAAGV,mBAAxB,IAA+C,CAAtD;AACD", "sourcesContent": ["import toDate from \"../../toDate/index.js\";\nimport requiredArgs from \"../requiredArgs/index.js\";\nvar MILLISECONDS_IN_DAY = 86400000; // This function will be a part of public API when UTC function will be implemented.\n// See issue: https://github.com/date-fns/date-fns/issues/376\n\nexport default function getUTCDayOfYear(dirtyDate) {\n  requiredArgs(1, arguments);\n  var date = toDate(dirtyDate);\n  var timestamp = date.getTime();\n  date.setUTCMonth(0, 1);\n  date.setUTCHours(0, 0, 0, 0);\n  var startOfYearTimestamp = date.getTime();\n  var difference = timestamp - startOfYearTimestamp;\n  return Math.floor(difference / MILLISECONDS_IN_DAY) + 1;\n}"]}, "metadata": {}, "sourceType": "module"}
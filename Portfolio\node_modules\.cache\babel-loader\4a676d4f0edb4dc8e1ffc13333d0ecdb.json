{"ast": null, "code": "import { LineDrawer } from \"./LineDrawer\";\nexport async function loadLineShape(engine) {\n  await engine.addShape(\"line\", new LineDrawer());\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Shapes/Line/index.js"], "names": ["LineDrawer", "loadLineShape", "engine", "addShape"], "mappings": "AAAA,SAASA,UAAT,QAA2B,cAA3B;AACA,OAAO,eAAeC,aAAf,CAA6BC,MAA7B,EAAqC;AACxC,QAAMA,MAAM,CAACC,QAAP,CAAgB,MAAhB,EAAwB,IAAIH,UAAJ,EAAxB,CAAN;AACH", "sourcesContent": ["import { LineDrawer } from \"./LineDrawer\";\nexport async function loadLineShape(engine) {\n    await engine.addShape(\"line\", new LineDrawer());\n}\n"]}, "metadata": {}, "sourceType": "module"}
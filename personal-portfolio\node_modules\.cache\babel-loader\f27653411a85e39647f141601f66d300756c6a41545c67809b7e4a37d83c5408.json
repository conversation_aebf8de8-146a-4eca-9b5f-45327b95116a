{"ast": null, "code": "import { SizeUpdater } from \"./SizeUpdater.js\";\nexport async function loadSizeUpdater(engine, refresh = true) {\n  await engine.addParticleUpdater(\"size\", () => {\n    return Promise.resolve(new SizeUpdater());\n  }, refresh);\n}", "map": {"version": 3, "names": ["SizeUpdater", "loadSizeUpdater", "engine", "refresh", "addParticleUpdater", "Promise", "resolve"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/updater-size/browser/index.js"], "sourcesContent": ["import { SizeUpdater } from \"./SizeUpdater.js\";\nexport async function loadSizeUpdater(engine, refresh = true) {\n    await engine.addParticleUpdater(\"size\", () => {\n        return Promise.resolve(new SizeUpdater());\n    }, refresh);\n}\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,kBAAkB;AAC9C,OAAO,eAAeC,eAAeA,CAACC,MAAM,EAAEC,OAAO,GAAG,IAAI,EAAE;EAC1D,MAAMD,MAAM,CAACE,kBAAkB,CAAC,MAAM,EAAE,MAAM;IAC1C,OAAOC,OAAO,CAACC,OAAO,CAAC,IAAIN,WAAW,CAAC,CAAC,CAAC;EAC7C,CAAC,EAAEG,OAAO,CAAC;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { getRangeValue, percentDenominator } from \"@tsparticles/engine\";\nimport { Destroy } from \"./Options/Classes/Destroy.js\";\nimport { DestroyMode } from \"./Enums/DestroyMode.js\";\nimport { split } from \"./Utils.js\";\nexport class DestroyUpdater {\n  constructor(engine, container) {\n    this.container = container;\n    this.engine = engine;\n  }\n  init(particle) {\n    const container = this.container,\n      particlesOptions = particle.options,\n      destroyOptions = particlesOptions.destroy;\n    if (!destroyOptions) {\n      return;\n    }\n    particle.splitCount = 0;\n    const destroyBoundsOptions = destroyOptions.bounds;\n    if (!particle.destroyBounds) {\n      particle.destroyBounds = {};\n    }\n    const {\n        bottom,\n        left,\n        right,\n        top\n      } = destroyBoundsOptions,\n      {\n        destroyBounds\n      } = particle,\n      canvasSize = container.canvas.size;\n    if (bottom) {\n      destroyBounds.bottom = getRangeValue(bottom) * canvasSize.height / percentDenominator;\n    }\n    if (left) {\n      destroyBounds.left = getRangeValue(left) * canvasSize.width / percentDenominator;\n    }\n    if (right) {\n      destroyBounds.right = getRangeValue(right) * canvasSize.width / percentDenominator;\n    }\n    if (top) {\n      destroyBounds.top = getRangeValue(top) * canvasSize.height / percentDenominator;\n    }\n  }\n  isEnabled(particle) {\n    return !particle.destroyed;\n  }\n  loadOptions(options, ...sources) {\n    if (!options.destroy) {\n      options.destroy = new Destroy();\n    }\n    for (const source of sources) {\n      options.destroy.load(source?.destroy);\n    }\n  }\n  particleDestroyed(particle, override) {\n    if (override) {\n      return;\n    }\n    const destroyOptions = particle.options.destroy;\n    if (destroyOptions && destroyOptions.mode === DestroyMode.split) {\n      split(this.engine, this.container, particle);\n    }\n  }\n  update(particle) {\n    if (!this.isEnabled(particle)) {\n      return;\n    }\n    const position = particle.getPosition(),\n      bounds = particle.destroyBounds;\n    if (!bounds) {\n      return;\n    }\n    if (bounds.bottom !== undefined && position.y >= bounds.bottom || bounds.left !== undefined && position.x <= bounds.left || bounds.right !== undefined && position.x >= bounds.right || bounds.top !== undefined && position.y <= bounds.top) {\n      particle.destroy();\n    }\n  }\n}", "map": {"version": 3, "names": ["getRangeValue", "percentDenominator", "Destroy", "DestroyMode", "split", "DestroyUpdater", "constructor", "engine", "container", "init", "particle", "particlesOptions", "options", "destroyOptions", "destroy", "splitCount", "destroyBoundsOptions", "bounds", "destroyBounds", "bottom", "left", "right", "top", "canvasSize", "canvas", "size", "height", "width", "isEnabled", "destroyed", "loadOptions", "sources", "source", "load", "particleDestroyed", "override", "mode", "update", "position", "getPosition", "undefined", "y", "x"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/updater-destroy/browser/DestroyUpdater.js"], "sourcesContent": ["import { getRangeValue, percentDenominator, } from \"@tsparticles/engine\";\nimport { Destroy } from \"./Options/Classes/Destroy.js\";\nimport { DestroyMode } from \"./Enums/DestroyMode.js\";\nimport { split } from \"./Utils.js\";\nexport class DestroyUpdater {\n    constructor(engine, container) {\n        this.container = container;\n        this.engine = engine;\n    }\n    init(particle) {\n        const container = this.container, particlesOptions = particle.options, destroyOptions = particlesOptions.destroy;\n        if (!destroyOptions) {\n            return;\n        }\n        particle.splitCount = 0;\n        const destroyBoundsOptions = destroyOptions.bounds;\n        if (!particle.destroyBounds) {\n            particle.destroyBounds = {};\n        }\n        const { bottom, left, right, top } = destroyBoundsOptions, { destroyBounds } = particle, canvasSize = container.canvas.size;\n        if (bottom) {\n            destroyBounds.bottom = (getRangeValue(bottom) * canvasSize.height) / percentDenominator;\n        }\n        if (left) {\n            destroyBounds.left = (getRangeValue(left) * canvasSize.width) / percentDenominator;\n        }\n        if (right) {\n            destroyBounds.right = (getRangeValue(right) * canvasSize.width) / percentDenominator;\n        }\n        if (top) {\n            destroyBounds.top = (getRangeValue(top) * canvasSize.height) / percentDenominator;\n        }\n    }\n    isEnabled(particle) {\n        return !particle.destroyed;\n    }\n    loadOptions(options, ...sources) {\n        if (!options.destroy) {\n            options.destroy = new Destroy();\n        }\n        for (const source of sources) {\n            options.destroy.load(source?.destroy);\n        }\n    }\n    particleDestroyed(particle, override) {\n        if (override) {\n            return;\n        }\n        const destroyOptions = particle.options.destroy;\n        if (destroyOptions && destroyOptions.mode === DestroyMode.split) {\n            split(this.engine, this.container, particle);\n        }\n    }\n    update(particle) {\n        if (!this.isEnabled(particle)) {\n            return;\n        }\n        const position = particle.getPosition(), bounds = particle.destroyBounds;\n        if (!bounds) {\n            return;\n        }\n        if ((bounds.bottom !== undefined && position.y >= bounds.bottom) ||\n            (bounds.left !== undefined && position.x <= bounds.left) ||\n            (bounds.right !== undefined && position.x >= bounds.right) ||\n            (bounds.top !== undefined && position.y <= bounds.top)) {\n            particle.destroy();\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,aAAa,EAAEC,kBAAkB,QAAS,qBAAqB;AACxE,SAASC,OAAO,QAAQ,8BAA8B;AACtD,SAASC,WAAW,QAAQ,wBAAwB;AACpD,SAASC,KAAK,QAAQ,YAAY;AAClC,OAAO,MAAMC,cAAc,CAAC;EACxBC,WAAWA,CAACC,MAAM,EAAEC,SAAS,EAAE;IAC3B,IAAI,CAACA,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACD,MAAM,GAAGA,MAAM;EACxB;EACAE,IAAIA,CAACC,QAAQ,EAAE;IACX,MAAMF,SAAS,GAAG,IAAI,CAACA,SAAS;MAAEG,gBAAgB,GAAGD,QAAQ,CAACE,OAAO;MAAEC,cAAc,GAAGF,gBAAgB,CAACG,OAAO;IAChH,IAAI,CAACD,cAAc,EAAE;MACjB;IACJ;IACAH,QAAQ,CAACK,UAAU,GAAG,CAAC;IACvB,MAAMC,oBAAoB,GAAGH,cAAc,CAACI,MAAM;IAClD,IAAI,CAACP,QAAQ,CAACQ,aAAa,EAAE;MACzBR,QAAQ,CAACQ,aAAa,GAAG,CAAC,CAAC;IAC/B;IACA,MAAM;QAAEC,MAAM;QAAEC,IAAI;QAAEC,KAAK;QAAEC;MAAI,CAAC,GAAGN,oBAAoB;MAAE;QAAEE;MAAc,CAAC,GAAGR,QAAQ;MAAEa,UAAU,GAAGf,SAAS,CAACgB,MAAM,CAACC,IAAI;IAC3H,IAAIN,MAAM,EAAE;MACRD,aAAa,CAACC,MAAM,GAAInB,aAAa,CAACmB,MAAM,CAAC,GAAGI,UAAU,CAACG,MAAM,GAAIzB,kBAAkB;IAC3F;IACA,IAAImB,IAAI,EAAE;MACNF,aAAa,CAACE,IAAI,GAAIpB,aAAa,CAACoB,IAAI,CAAC,GAAGG,UAAU,CAACI,KAAK,GAAI1B,kBAAkB;IACtF;IACA,IAAIoB,KAAK,EAAE;MACPH,aAAa,CAACG,KAAK,GAAIrB,aAAa,CAACqB,KAAK,CAAC,GAAGE,UAAU,CAACI,KAAK,GAAI1B,kBAAkB;IACxF;IACA,IAAIqB,GAAG,EAAE;MACLJ,aAAa,CAACI,GAAG,GAAItB,aAAa,CAACsB,GAAG,CAAC,GAAGC,UAAU,CAACG,MAAM,GAAIzB,kBAAkB;IACrF;EACJ;EACA2B,SAASA,CAAClB,QAAQ,EAAE;IAChB,OAAO,CAACA,QAAQ,CAACmB,SAAS;EAC9B;EACAC,WAAWA,CAAClB,OAAO,EAAE,GAAGmB,OAAO,EAAE;IAC7B,IAAI,CAACnB,OAAO,CAACE,OAAO,EAAE;MAClBF,OAAO,CAACE,OAAO,GAAG,IAAIZ,OAAO,CAAC,CAAC;IACnC;IACA,KAAK,MAAM8B,MAAM,IAAID,OAAO,EAAE;MAC1BnB,OAAO,CAACE,OAAO,CAACmB,IAAI,CAACD,MAAM,EAAElB,OAAO,CAAC;IACzC;EACJ;EACAoB,iBAAiBA,CAACxB,QAAQ,EAAEyB,QAAQ,EAAE;IAClC,IAAIA,QAAQ,EAAE;MACV;IACJ;IACA,MAAMtB,cAAc,GAAGH,QAAQ,CAACE,OAAO,CAACE,OAAO;IAC/C,IAAID,cAAc,IAAIA,cAAc,CAACuB,IAAI,KAAKjC,WAAW,CAACC,KAAK,EAAE;MAC7DA,KAAK,CAAC,IAAI,CAACG,MAAM,EAAE,IAAI,CAACC,SAAS,EAAEE,QAAQ,CAAC;IAChD;EACJ;EACA2B,MAAMA,CAAC3B,QAAQ,EAAE;IACb,IAAI,CAAC,IAAI,CAACkB,SAAS,CAAClB,QAAQ,CAAC,EAAE;MAC3B;IACJ;IACA,MAAM4B,QAAQ,GAAG5B,QAAQ,CAAC6B,WAAW,CAAC,CAAC;MAAEtB,MAAM,GAAGP,QAAQ,CAACQ,aAAa;IACxE,IAAI,CAACD,MAAM,EAAE;MACT;IACJ;IACA,IAAKA,MAAM,CAACE,MAAM,KAAKqB,SAAS,IAAIF,QAAQ,CAACG,CAAC,IAAIxB,MAAM,CAACE,MAAM,IAC1DF,MAAM,CAACG,IAAI,KAAKoB,SAAS,IAAIF,QAAQ,CAACI,CAAC,IAAIzB,MAAM,CAACG,IAAK,IACvDH,MAAM,CAACI,KAAK,KAAKmB,SAAS,IAAIF,QAAQ,CAACI,CAAC,IAAIzB,MAAM,CAACI,KAAM,IACzDJ,MAAM,CAACK,GAAG,KAAKkB,SAAS,IAAIF,QAAQ,CAACG,CAAC,IAAIxB,MAAM,CAACK,GAAI,EAAE;MACxDZ,QAAQ,CAACI,OAAO,CAAC,CAAC;IACtB;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
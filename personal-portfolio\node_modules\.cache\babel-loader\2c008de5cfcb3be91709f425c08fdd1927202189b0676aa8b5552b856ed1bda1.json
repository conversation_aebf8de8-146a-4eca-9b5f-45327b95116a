{"ast": null, "code": "import { Circle, Rectangle } from \"./Ranges.js\";\nimport { getDistance } from \"../../Utils/NumberUtils.js\";\nconst half = 0.5,\n  double = 2,\n  subdivideCount = 4;\nexport class QuadTree {\n  constructor(rectangle, capacity) {\n    this.rectangle = rectangle;\n    this.capacity = capacity;\n    this._subdivide = () => {\n      const {\n          x,\n          y\n        } = this.rectangle.position,\n        {\n          width,\n          height\n        } = this.rectangle.size,\n        {\n          capacity\n        } = this;\n      for (let i = 0; i < subdivideCount; i++) {\n        const fixedIndex = i % double;\n        this._subs.push(new QuadTree(new Rectangle(x + width * half * fixedIndex, y + height * half * (Math.round(i * half) - fixedIndex), width * half, height * half), capacity));\n      }\n      this._divided = true;\n    };\n    this._points = [];\n    this._divided = false;\n    this._subs = [];\n  }\n  insert(point) {\n    if (!this.rectangle.contains(point.position)) {\n      return false;\n    }\n    if (this._points.length < this.capacity) {\n      this._points.push(point);\n      return true;\n    }\n    if (!this._divided) {\n      this._subdivide();\n    }\n    return this._subs.some(sub => sub.insert(point));\n  }\n  query(range, check) {\n    const res = [];\n    if (!range.intersects(this.rectangle)) {\n      return [];\n    }\n    for (const p of this._points) {\n      if (!range.contains(p.position) && getDistance(range.position, p.position) > p.particle.getRadius() && (!check || check(p.particle))) {\n        continue;\n      }\n      res.push(p.particle);\n    }\n    if (this._divided) {\n      for (const sub of this._subs) {\n        res.push(...sub.query(range, check));\n      }\n    }\n    return res;\n  }\n  queryCircle(position, radius, check) {\n    return this.query(new Circle(position.x, position.y, radius), check);\n  }\n  queryRectangle(position, size, check) {\n    return this.query(new Rectangle(position.x, position.y, size.width, size.height), check);\n  }\n}", "map": {"version": 3, "names": ["Circle", "Rectangle", "getDistance", "half", "double", "subdivideCount", "QuadTree", "constructor", "rectangle", "capacity", "_subdivide", "x", "y", "position", "width", "height", "size", "i", "fixedIndex", "_subs", "push", "Math", "round", "_divided", "_points", "insert", "point", "contains", "length", "some", "sub", "query", "range", "check", "res", "intersects", "p", "particle", "getRadius", "queryCircle", "radius", "queryRectangle"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/engine/browser/Core/Utils/QuadTree.js"], "sourcesContent": ["import { Circle, Rectangle } from \"./Ranges.js\";\nimport { getDistance } from \"../../Utils/NumberUtils.js\";\nconst half = 0.5, double = 2, subdivideCount = 4;\nexport class QuadTree {\n    constructor(rectangle, capacity) {\n        this.rectangle = rectangle;\n        this.capacity = capacity;\n        this._subdivide = () => {\n            const { x, y } = this.rectangle.position, { width, height } = this.rectangle.size, { capacity } = this;\n            for (let i = 0; i < subdivideCount; i++) {\n                const fixedIndex = i % double;\n                this._subs.push(new QuadTree(new Rectangle(x + width * half * fixedIndex, y + height * half * (Math.round(i * half) - fixedIndex), width * half, height * half), capacity));\n            }\n            this._divided = true;\n        };\n        this._points = [];\n        this._divided = false;\n        this._subs = [];\n    }\n    insert(point) {\n        if (!this.rectangle.contains(point.position)) {\n            return false;\n        }\n        if (this._points.length < this.capacity) {\n            this._points.push(point);\n            return true;\n        }\n        if (!this._divided) {\n            this._subdivide();\n        }\n        return this._subs.some(sub => sub.insert(point));\n    }\n    query(range, check) {\n        const res = [];\n        if (!range.intersects(this.rectangle)) {\n            return [];\n        }\n        for (const p of this._points) {\n            if (!range.contains(p.position) &&\n                getDistance(range.position, p.position) > p.particle.getRadius() &&\n                (!check || check(p.particle))) {\n                continue;\n            }\n            res.push(p.particle);\n        }\n        if (this._divided) {\n            for (const sub of this._subs) {\n                res.push(...sub.query(range, check));\n            }\n        }\n        return res;\n    }\n    queryCircle(position, radius, check) {\n        return this.query(new Circle(position.x, position.y, radius), check);\n    }\n    queryRectangle(position, size, check) {\n        return this.query(new Rectangle(position.x, position.y, size.width, size.height), check);\n    }\n}\n"], "mappings": "AAAA,SAASA,MAAM,EAAEC,SAAS,QAAQ,aAAa;AAC/C,SAASC,WAAW,QAAQ,4BAA4B;AACxD,MAAMC,IAAI,GAAG,GAAG;EAAEC,MAAM,GAAG,CAAC;EAAEC,cAAc,GAAG,CAAC;AAChD,OAAO,MAAMC,QAAQ,CAAC;EAClBC,WAAWA,CAACC,SAAS,EAAEC,QAAQ,EAAE;IAC7B,IAAI,CAACD,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,UAAU,GAAG,MAAM;MACpB,MAAM;UAAEC,CAAC;UAAEC;QAAE,CAAC,GAAG,IAAI,CAACJ,SAAS,CAACK,QAAQ;QAAE;UAAEC,KAAK;UAAEC;QAAO,CAAC,GAAG,IAAI,CAACP,SAAS,CAACQ,IAAI;QAAE;UAAEP;QAAS,CAAC,GAAG,IAAI;MACtG,KAAK,IAAIQ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGZ,cAAc,EAAEY,CAAC,EAAE,EAAE;QACrC,MAAMC,UAAU,GAAGD,CAAC,GAAGb,MAAM;QAC7B,IAAI,CAACe,KAAK,CAACC,IAAI,CAAC,IAAId,QAAQ,CAAC,IAAIL,SAAS,CAACU,CAAC,GAAGG,KAAK,GAAGX,IAAI,GAAGe,UAAU,EAAEN,CAAC,GAAGG,MAAM,GAAGZ,IAAI,IAAIkB,IAAI,CAACC,KAAK,CAACL,CAAC,GAAGd,IAAI,CAAC,GAAGe,UAAU,CAAC,EAAEJ,KAAK,GAAGX,IAAI,EAAEY,MAAM,GAAGZ,IAAI,CAAC,EAAEM,QAAQ,CAAC,CAAC;MAC/K;MACA,IAAI,CAACc,QAAQ,GAAG,IAAI;IACxB,CAAC;IACD,IAAI,CAACC,OAAO,GAAG,EAAE;IACjB,IAAI,CAACD,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACJ,KAAK,GAAG,EAAE;EACnB;EACAM,MAAMA,CAACC,KAAK,EAAE;IACV,IAAI,CAAC,IAAI,CAAClB,SAAS,CAACmB,QAAQ,CAACD,KAAK,CAACb,QAAQ,CAAC,EAAE;MAC1C,OAAO,KAAK;IAChB;IACA,IAAI,IAAI,CAACW,OAAO,CAACI,MAAM,GAAG,IAAI,CAACnB,QAAQ,EAAE;MACrC,IAAI,CAACe,OAAO,CAACJ,IAAI,CAACM,KAAK,CAAC;MACxB,OAAO,IAAI;IACf;IACA,IAAI,CAAC,IAAI,CAACH,QAAQ,EAAE;MAChB,IAAI,CAACb,UAAU,CAAC,CAAC;IACrB;IACA,OAAO,IAAI,CAACS,KAAK,CAACU,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACL,MAAM,CAACC,KAAK,CAAC,CAAC;EACpD;EACAK,KAAKA,CAACC,KAAK,EAAEC,KAAK,EAAE;IAChB,MAAMC,GAAG,GAAG,EAAE;IACd,IAAI,CAACF,KAAK,CAACG,UAAU,CAAC,IAAI,CAAC3B,SAAS,CAAC,EAAE;MACnC,OAAO,EAAE;IACb;IACA,KAAK,MAAM4B,CAAC,IAAI,IAAI,CAACZ,OAAO,EAAE;MAC1B,IAAI,CAACQ,KAAK,CAACL,QAAQ,CAACS,CAAC,CAACvB,QAAQ,CAAC,IAC3BX,WAAW,CAAC8B,KAAK,CAACnB,QAAQ,EAAEuB,CAAC,CAACvB,QAAQ,CAAC,GAAGuB,CAAC,CAACC,QAAQ,CAACC,SAAS,CAAC,CAAC,KAC/D,CAACL,KAAK,IAAIA,KAAK,CAACG,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE;QAC/B;MACJ;MACAH,GAAG,CAACd,IAAI,CAACgB,CAAC,CAACC,QAAQ,CAAC;IACxB;IACA,IAAI,IAAI,CAACd,QAAQ,EAAE;MACf,KAAK,MAAMO,GAAG,IAAI,IAAI,CAACX,KAAK,EAAE;QAC1Be,GAAG,CAACd,IAAI,CAAC,GAAGU,GAAG,CAACC,KAAK,CAACC,KAAK,EAAEC,KAAK,CAAC,CAAC;MACxC;IACJ;IACA,OAAOC,GAAG;EACd;EACAK,WAAWA,CAAC1B,QAAQ,EAAE2B,MAAM,EAAEP,KAAK,EAAE;IACjC,OAAO,IAAI,CAACF,KAAK,CAAC,IAAI/B,MAAM,CAACa,QAAQ,CAACF,CAAC,EAAEE,QAAQ,CAACD,CAAC,EAAE4B,MAAM,CAAC,EAAEP,KAAK,CAAC;EACxE;EACAQ,cAAcA,CAAC5B,QAAQ,EAAEG,IAAI,EAAEiB,KAAK,EAAE;IAClC,OAAO,IAAI,CAACF,KAAK,CAAC,IAAI9B,SAAS,CAACY,QAAQ,CAACF,CAAC,EAAEE,QAAQ,CAACD,CAAC,EAAEI,IAAI,CAACF,KAAK,EAAEE,IAAI,CAACD,MAAM,CAAC,EAAEkB,KAAK,CAAC;EAC5F;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import getParentNode from \"./getParentNode.js\";\nimport isScrollParent from \"./isScrollParent.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport { isHTMLElement } from \"./instanceOf.js\";\nexport default function getScrollParent(node) {\n  if (['html', 'body', '#document'].indexOf(getNodeName(node)) >= 0) {\n    // $FlowFixMe[incompatible-return]: assume body is always available\n    return node.ownerDocument.body;\n  }\n\n  if (isHTMLElement(node) && isScrollParent(node)) {\n    return node;\n  }\n\n  return getScrollParent(getParentNode(node));\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/@popperjs/core/lib/dom-utils/getScrollParent.js"], "names": ["getParentNode", "isScrollParent", "getNodeName", "isHTMLElement", "getScrollParent", "node", "indexOf", "ownerDocument", "body"], "mappings": "AAAA,OAAOA,aAAP,MAA0B,oBAA1B;AACA,OAAOC,cAAP,MAA2B,qBAA3B;AACA,OAAOC,WAAP,MAAwB,kBAAxB;AACA,SAASC,aAAT,QAA8B,iBAA9B;AACA,eAAe,SAASC,eAAT,CAAyBC,IAAzB,EAA+B;AAC5C,MAAI,CAAC,MAAD,EAAS,MAAT,EAAiB,WAAjB,EAA8BC,OAA9B,CAAsCJ,WAAW,CAACG,IAAD,CAAjD,KAA4D,CAAhE,EAAmE;AACjE;AACA,WAAOA,IAAI,CAACE,aAAL,CAAmBC,IAA1B;AACD;;AAED,MAAIL,aAAa,CAACE,IAAD,CAAb,IAAuBJ,cAAc,CAACI,IAAD,CAAzC,EAAiD;AAC/C,WAAOA,IAAP;AACD;;AAED,SAAOD,eAAe,CAACJ,aAAa,CAACK,IAAD,CAAd,CAAtB;AACD", "sourcesContent": ["import getParentNode from \"./getParentNode.js\";\nimport isScrollParent from \"./isScrollParent.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport { isHTMLElement } from \"./instanceOf.js\";\nexport default function getScrollParent(node) {\n  if (['html', 'body', '#document'].indexOf(getNodeName(node)) >= 0) {\n    // $FlowFixMe[incompatible-return]: assume body is always available\n    return node.ownerDocument.body;\n  }\n\n  if (isHTMLElement(node) && isScrollParent(node)) {\n    return node;\n  }\n\n  return getScrollParent(getParentNode(node));\n}"]}, "metadata": {}, "sourceType": "module"}
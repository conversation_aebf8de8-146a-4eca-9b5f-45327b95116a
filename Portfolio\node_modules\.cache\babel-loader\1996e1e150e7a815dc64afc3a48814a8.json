{"ast": null, "code": "import { BubbleBase } from \"./BubbleBase\";\nexport class BubbleDiv extends BubbleBase {\n  constructor() {\n    super();\n    this.selectors = [];\n  }\n\n  get ids() {\n    return this.selectors instanceof Array ? this.selectors.map(t => t.replace(\"#\", \"\")) : this.selectors.replace(\"#\", \"\");\n  }\n\n  set ids(value) {\n    this.selectors = value instanceof Array ? value.map(t => `#${t}`) : `#${value}`;\n  }\n\n  load(data) {\n    super.load(data);\n\n    if (data === undefined) {\n      return;\n    }\n\n    if (data.ids !== undefined) {\n      this.ids = data.ids;\n    }\n\n    if (data.selectors !== undefined) {\n      this.selectors = data.selectors;\n    }\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Options/Classes/Interactivity/Modes/BubbleDiv.js"], "names": ["BubbleBase", "BubbleDiv", "constructor", "selectors", "ids", "Array", "map", "t", "replace", "value", "load", "data", "undefined"], "mappings": "AAAA,SAASA,UAAT,QAA2B,cAA3B;AACA,OAAO,MAAMC,SAAN,SAAwBD,UAAxB,CAAmC;AACtCE,EAAAA,WAAW,GAAG;AACV;AACA,SAAKC,SAAL,GAAiB,EAAjB;AACH;;AACM,MAAHC,GAAG,GAAG;AACN,WAAO,KAAKD,SAAL,YAA0BE,KAA1B,GACD,KAAKF,SAAL,CAAeG,GAAf,CAAoBC,CAAD,IAAOA,CAAC,CAACC,OAAF,CAAU,GAAV,EAAe,EAAf,CAA1B,CADC,GAED,KAAKL,SAAL,CAAeK,OAAf,CAAuB,GAAvB,EAA4B,EAA5B,CAFN;AAGH;;AACM,MAAHJ,GAAG,CAACK,KAAD,EAAQ;AACX,SAAKN,SAAL,GAAiBM,KAAK,YAAYJ,KAAjB,GAAyBI,KAAK,CAACH,GAAN,CAAWC,CAAD,IAAQ,IAAGA,CAAE,EAAvB,CAAzB,GAAsD,IAAGE,KAAM,EAAhF;AACH;;AACDC,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,UAAMD,IAAN,CAAWC,IAAX;;AACA,QAAIA,IAAI,KAAKC,SAAb,EAAwB;AACpB;AACH;;AACD,QAAID,IAAI,CAACP,GAAL,KAAaQ,SAAjB,EAA4B;AACxB,WAAKR,GAAL,GAAWO,IAAI,CAACP,GAAhB;AACH;;AACD,QAAIO,IAAI,CAACR,SAAL,KAAmBS,SAAvB,EAAkC;AAC9B,WAAKT,SAAL,GAAiBQ,IAAI,CAACR,SAAtB;AACH;AACJ;;AAxBqC", "sourcesContent": ["import { BubbleBase } from \"./BubbleBase\";\nexport class BubbleDiv extends BubbleBase {\n    constructor() {\n        super();\n        this.selectors = [];\n    }\n    get ids() {\n        return this.selectors instanceof Array\n            ? this.selectors.map((t) => t.replace(\"#\", \"\"))\n            : this.selectors.replace(\"#\", \"\");\n    }\n    set ids(value) {\n        this.selectors = value instanceof Array ? value.map((t) => `#${t}`) : `#${value}`;\n    }\n    load(data) {\n        super.load(data);\n        if (data === undefined) {\n            return;\n        }\n        if (data.ids !== undefined) {\n            this.ids = data.ids;\n        }\n        if (data.selectors !== undefined) {\n            this.selectors = data.selectors;\n        }\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
{"ast": null, "code": "export class ResizeEvent {\n  constructor() {\n    this.delay = 0.5;\n    this.enable = true;\n  }\n  load(data) {\n    if (data === undefined) {\n      return;\n    }\n    if (data.delay !== undefined) {\n      this.delay = data.delay;\n    }\n    if (data.enable !== undefined) {\n      this.enable = data.enable;\n    }\n  }\n}", "map": {"version": 3, "names": ["ResizeEvent", "constructor", "delay", "enable", "load", "data", "undefined"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/engine/browser/Options/Classes/Interactivity/Events/ResizeEvent.js"], "sourcesContent": ["export class ResizeEvent {\n    constructor() {\n        this.delay = 0.5;\n        this.enable = true;\n    }\n    load(data) {\n        if (data === undefined) {\n            return;\n        }\n        if (data.delay !== undefined) {\n            this.delay = data.delay;\n        }\n        if (data.enable !== undefined) {\n            this.enable = data.enable;\n        }\n    }\n}\n"], "mappings": "AAAA,OAAO,MAAMA,WAAW,CAAC;EACrBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,KAAK,GAAG,GAAG;IAChB,IAAI,CAACC,MAAM,GAAG,IAAI;EACtB;EACAC,IAAIA,CAACC,IAAI,EAAE;IACP,IAAIA,IAAI,KAAKC,SAAS,EAAE;MACpB;IACJ;IACA,IAAID,IAAI,CAACH,KAAK,KAAKI,SAAS,EAAE;MAC1B,IAAI,CAACJ,KAAK,GAAGG,IAAI,CAACH,KAAK;IAC3B;IACA,IAAIG,IAAI,CAACF,MAAM,KAAKG,SAAS,EAAE;MAC3B,IAAI,CAACH,MAAM,GAAGE,IAAI,CAACF,MAAM;IAC7B;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
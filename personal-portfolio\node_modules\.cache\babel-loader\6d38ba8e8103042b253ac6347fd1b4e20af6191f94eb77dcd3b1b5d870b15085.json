{"ast": null, "code": "import { ExternalInteractorBase } from \"@tsparticles/engine\";\nconst pauseMode = \"pause\";\nexport class Pauser extends ExternalInteractorBase {\n  constructor(container) {\n    super(container);\n    this.handleClickMode = mode => {\n      if (mode !== pauseMode) {\n        return;\n      }\n      const container = this.container;\n      if (container.animationStatus) {\n        container.pause();\n      } else {\n        container.play();\n      }\n    };\n  }\n  clear() {}\n  init() {}\n  interact() {}\n  isEnabled() {\n    return true;\n  }\n  reset() {}\n}", "map": {"version": 3, "names": ["ExternalInteractorBase", "pauseMode", "Pa<PERSON>", "constructor", "container", "handleClickMode", "mode", "animationStatus", "pause", "play", "clear", "init", "interact", "isEnabled", "reset"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/interaction-external-pause/browser/Pauser.js"], "sourcesContent": ["import { ExternalInteractorBase } from \"@tsparticles/engine\";\nconst pauseMode = \"pause\";\nexport class Pauser extends ExternalInteractorBase {\n    constructor(container) {\n        super(container);\n        this.handleClickMode = (mode) => {\n            if (mode !== pauseMode) {\n                return;\n            }\n            const container = this.container;\n            if (container.animationStatus) {\n                container.pause();\n            }\n            else {\n                container.play();\n            }\n        };\n    }\n    clear() {\n    }\n    init() {\n    }\n    interact() {\n    }\n    isEnabled() {\n        return true;\n    }\n    reset() {\n    }\n}\n"], "mappings": "AAAA,SAASA,sBAAsB,QAAQ,qBAAqB;AAC5D,MAAMC,SAAS,GAAG,OAAO;AACzB,OAAO,MAAMC,MAAM,SAASF,sBAAsB,CAAC;EAC/CG,WAAWA,CAACC,SAAS,EAAE;IACnB,KAAK,CAACA,SAAS,CAAC;IAChB,IAAI,CAACC,eAAe,GAAIC,IAAI,IAAK;MAC7B,IAAIA,IAAI,KAAKL,SAAS,EAAE;QACpB;MACJ;MACA,MAAMG,SAAS,GAAG,IAAI,CAACA,SAAS;MAChC,IAAIA,SAAS,CAACG,eAAe,EAAE;QAC3BH,SAAS,CAACI,KAAK,CAAC,CAAC;MACrB,CAAC,MACI;QACDJ,SAAS,CAACK,IAAI,CAAC,CAAC;MACpB;IACJ,CAAC;EACL;EACAC,KAAKA,CAAA,EAAG,CACR;EACAC,IAAIA,CAAA,EAAG,CACP;EACAC,QAAQA,CAAA,EAAG,CACX;EACAC,SAASA,CAAA,EAAG;IACR,OAAO,IAAI;EACf;EACAC,KAAKA,CAAA,EAAG,CACR;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
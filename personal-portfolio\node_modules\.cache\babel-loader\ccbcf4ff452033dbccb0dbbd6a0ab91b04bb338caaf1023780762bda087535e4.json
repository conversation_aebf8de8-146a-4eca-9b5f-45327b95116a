{"ast": null, "code": "import { loadLinksInteraction } from \"./interaction.js\";\nimport { loadLinksPlugin } from \"./plugin.js\";\nexport async function loadParticlesLinksInteraction(engine, refresh = true) {\n  await loadLinksInteraction(engine, refresh);\n  await loadLinksPlugin(engine, refresh);\n}\nexport * from \"./Options/Classes/Links.js\";\nexport * from \"./Options/Classes/LinksShadow.js\";\nexport * from \"./Options/Classes/LinksTriangle.js\";\nexport * from \"./Options/Interfaces/ILinks.js\";\nexport * from \"./Options/Interfaces/ILinksShadow.js\";\nexport * from \"./Options/Interfaces/ILinksTriangle.js\";", "map": {"version": 3, "names": ["loadLinksInteraction", "loadLinksPlugin", "loadParticlesLinksInteraction", "engine", "refresh"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/interaction-particles-links/browser/index.js"], "sourcesContent": ["import { loadLinksInteraction } from \"./interaction.js\";\nimport { loadLinksPlugin } from \"./plugin.js\";\nexport async function loadParticlesLinksInteraction(engine, refresh = true) {\n    await loadLinksInteraction(engine, refresh);\n    await loadLinksPlugin(engine, refresh);\n}\nexport * from \"./Options/Classes/Links.js\";\nexport * from \"./Options/Classes/LinksShadow.js\";\nexport * from \"./Options/Classes/LinksTriangle.js\";\nexport * from \"./Options/Interfaces/ILinks.js\";\nexport * from \"./Options/Interfaces/ILinksShadow.js\";\nexport * from \"./Options/Interfaces/ILinksTriangle.js\";\n"], "mappings": "AAAA,SAASA,oBAAoB,QAAQ,kBAAkB;AACvD,SAASC,eAAe,QAAQ,aAAa;AAC7C,OAAO,eAAeC,6BAA6BA,CAACC,MAAM,EAAEC,OAAO,GAAG,IAAI,EAAE;EACxE,MAAMJ,oBAAoB,CAACG,MAAM,EAAEC,OAAO,CAAC;EAC3C,MAAMH,eAAe,CAACE,MAAM,EAAEC,OAAO,CAAC;AAC1C;AACA,cAAc,4BAA4B;AAC1C,cAAc,kCAAkC;AAChD,cAAc,oCAAoC;AAClD,cAAc,gCAAgC;AAC9C,cAAc,sCAAsC;AACpD,cAAc,wCAAwC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
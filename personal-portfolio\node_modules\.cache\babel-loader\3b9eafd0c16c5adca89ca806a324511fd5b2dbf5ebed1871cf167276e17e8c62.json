{"ast": null, "code": "import { LinksPlugin } from \"./LinksPlugin.js\";\nexport async function loadLinksPlugin(engine, refresh = true) {\n  const plugin = new LinksPlugin();\n  await engine.addPlugin(plugin, refresh);\n}", "map": {"version": 3, "names": ["LinksPlugin", "loadLinksPlugin", "engine", "refresh", "plugin", "addPlugin"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/interaction-particles-links/browser/plugin.js"], "sourcesContent": ["import { LinksPlugin } from \"./LinksPlugin.js\";\nexport async function loadLinksPlugin(engine, refresh = true) {\n    const plugin = new LinksPlugin();\n    await engine.addPlugin(plugin, refresh);\n}\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,kBAAkB;AAC9C,OAAO,eAAeC,eAAeA,CAACC,MAAM,EAAEC,OAAO,GAAG,IAAI,EAAE;EAC1D,MAAMC,MAAM,GAAG,IAAIJ,WAAW,CAAC,CAAC;EAChC,MAAME,MAAM,CAACG,SAAS,CAACD,MAAM,EAAED,OAAO,CAAC;AAC3C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
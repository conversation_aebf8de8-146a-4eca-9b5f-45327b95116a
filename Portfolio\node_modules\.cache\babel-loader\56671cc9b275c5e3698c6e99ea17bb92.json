{"ast": null, "code": "import { ClickEvent } from \"./ClickEvent\";\nimport { DivEvent } from \"./DivEvent\";\nimport { HoverEvent } from \"./HoverEvent\";\nexport class Events {\n  constructor() {\n    this.onClick = new ClickEvent();\n    this.onDiv = new DivEvent();\n    this.onHover = new HoverEvent();\n    this.resize = true;\n  }\n\n  get onclick() {\n    return this.onClick;\n  }\n\n  set onclick(value) {\n    this.onClick = value;\n  }\n\n  get ondiv() {\n    return this.onDiv;\n  }\n\n  set ondiv(value) {\n    this.onDiv = value;\n  }\n\n  get onhover() {\n    return this.onHover;\n  }\n\n  set onhover(value) {\n    this.onHover = value;\n  }\n\n  load(data) {\n    var _a, _b, _c;\n\n    if (data === undefined) {\n      return;\n    }\n\n    this.onClick.load((_a = data.onClick) !== null && _a !== void 0 ? _a : data.onclick);\n    const onDiv = (_b = data.onDiv) !== null && _b !== void 0 ? _b : data.ondiv;\n\n    if (onDiv !== undefined) {\n      if (onDiv instanceof Array) {\n        this.onDiv = onDiv.map(div => {\n          const tmp = new DivEvent();\n          tmp.load(div);\n          return tmp;\n        });\n      } else {\n        this.onDiv = new DivEvent();\n        this.onDiv.load(onDiv);\n      }\n    }\n\n    this.onHover.load((_c = data.onHover) !== null && _c !== void 0 ? _c : data.onhover);\n\n    if (data.resize !== undefined) {\n      this.resize = data.resize;\n    }\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Options/Classes/Interactivity/Events/Events.js"], "names": ["ClickEvent", "DivEvent", "HoverEvent", "Events", "constructor", "onClick", "onDiv", "onHover", "resize", "onclick", "value", "ondiv", "onhover", "load", "data", "_a", "_b", "_c", "undefined", "Array", "map", "div", "tmp"], "mappings": "AAAA,SAASA,UAAT,QAA2B,cAA3B;AACA,SAASC,QAAT,QAAyB,YAAzB;AACA,SAASC,UAAT,QAA2B,cAA3B;AACA,OAAO,MAAMC,MAAN,CAAa;AAChBC,EAAAA,WAAW,GAAG;AACV,SAAKC,OAAL,GAAe,IAAIL,UAAJ,EAAf;AACA,SAAKM,KAAL,GAAa,IAAIL,QAAJ,EAAb;AACA,SAAKM,OAAL,GAAe,IAAIL,UAAJ,EAAf;AACA,SAAKM,MAAL,GAAc,IAAd;AACH;;AACU,MAAPC,OAAO,GAAG;AACV,WAAO,KAAKJ,OAAZ;AACH;;AACU,MAAPI,OAAO,CAACC,KAAD,EAAQ;AACf,SAAKL,OAAL,GAAeK,KAAf;AACH;;AACQ,MAALC,KAAK,GAAG;AACR,WAAO,KAAKL,KAAZ;AACH;;AACQ,MAALK,KAAK,CAACD,KAAD,EAAQ;AACb,SAAKJ,KAAL,GAAaI,KAAb;AACH;;AACU,MAAPE,OAAO,GAAG;AACV,WAAO,KAAKL,OAAZ;AACH;;AACU,MAAPK,OAAO,CAACF,KAAD,EAAQ;AACf,SAAKH,OAAL,GAAeG,KAAf;AACH;;AACDG,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,QAAIC,EAAJ,EAAQC,EAAR,EAAYC,EAAZ;;AACA,QAAIH,IAAI,KAAKI,SAAb,EAAwB;AACpB;AACH;;AACD,SAAKb,OAAL,CAAaQ,IAAb,CAAkB,CAACE,EAAE,GAAGD,IAAI,CAACT,OAAX,MAAwB,IAAxB,IAAgCU,EAAE,KAAK,KAAK,CAA5C,GAAgDA,EAAhD,GAAqDD,IAAI,CAACL,OAA5E;AACA,UAAMH,KAAK,GAAG,CAACU,EAAE,GAAGF,IAAI,CAACR,KAAX,MAAsB,IAAtB,IAA8BU,EAAE,KAAK,KAAK,CAA1C,GAA8CA,EAA9C,GAAmDF,IAAI,CAACH,KAAtE;;AACA,QAAIL,KAAK,KAAKY,SAAd,EAAyB;AACrB,UAAIZ,KAAK,YAAYa,KAArB,EAA4B;AACxB,aAAKb,KAAL,GAAaA,KAAK,CAACc,GAAN,CAAWC,GAAD,IAAS;AAC5B,gBAAMC,GAAG,GAAG,IAAIrB,QAAJ,EAAZ;AACAqB,UAAAA,GAAG,CAACT,IAAJ,CAASQ,GAAT;AACA,iBAAOC,GAAP;AACH,SAJY,CAAb;AAKH,OAND,MAOK;AACD,aAAKhB,KAAL,GAAa,IAAIL,QAAJ,EAAb;AACA,aAAKK,KAAL,CAAWO,IAAX,CAAgBP,KAAhB;AACH;AACJ;;AACD,SAAKC,OAAL,CAAaM,IAAb,CAAkB,CAACI,EAAE,GAAGH,IAAI,CAACP,OAAX,MAAwB,IAAxB,IAAgCU,EAAE,KAAK,KAAK,CAA5C,GAAgDA,EAAhD,GAAqDH,IAAI,CAACF,OAA5E;;AACA,QAAIE,IAAI,CAACN,MAAL,KAAgBU,SAApB,EAA+B;AAC3B,WAAKV,MAAL,GAAcM,IAAI,CAACN,MAAnB;AACH;AACJ;;AAjDe", "sourcesContent": ["import { ClickEvent } from \"./ClickEvent\";\nimport { DivEvent } from \"./DivEvent\";\nimport { HoverEvent } from \"./HoverEvent\";\nexport class Events {\n    constructor() {\n        this.onClick = new ClickEvent();\n        this.onDiv = new DivEvent();\n        this.onHover = new HoverEvent();\n        this.resize = true;\n    }\n    get onclick() {\n        return this.onClick;\n    }\n    set onclick(value) {\n        this.onClick = value;\n    }\n    get ondiv() {\n        return this.onDiv;\n    }\n    set ondiv(value) {\n        this.onDiv = value;\n    }\n    get onhover() {\n        return this.onHover;\n    }\n    set onhover(value) {\n        this.onHover = value;\n    }\n    load(data) {\n        var _a, _b, _c;\n        if (data === undefined) {\n            return;\n        }\n        this.onClick.load((_a = data.onClick) !== null && _a !== void 0 ? _a : data.onclick);\n        const onDiv = (_b = data.onDiv) !== null && _b !== void 0 ? _b : data.ondiv;\n        if (onDiv !== undefined) {\n            if (onDiv instanceof Array) {\n                this.onDiv = onDiv.map((div) => {\n                    const tmp = new DivEvent();\n                    tmp.load(div);\n                    return tmp;\n                });\n            }\n            else {\n                this.onDiv = new DivEvent();\n                this.onDiv.load(onDiv);\n            }\n        }\n        this.onHover.load((_c = data.onHover) !== null && _c !== void 0 ? _c : data.onhover);\n        if (data.resize !== undefined) {\n            this.resize = data.resize;\n        }\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
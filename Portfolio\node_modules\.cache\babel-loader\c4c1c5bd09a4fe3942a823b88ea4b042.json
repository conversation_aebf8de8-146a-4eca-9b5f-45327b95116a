{"ast": null, "code": "import { Attract } from \"./Attract\";\nimport { Bounce } from \"./Bounce\";\nimport { Bubble } from \"./Bubble\";\nimport { Connect } from \"./Connect\";\nimport { Grab } from \"./Grab\";\nimport { Light } from \"./Light\";\nimport { Push } from \"./Push\";\nimport { Remove } from \"./Remove\";\nimport { Repulse } from \"./Repulse\";\nimport { Slow } from \"./Slow\";\nimport { Trail } from \"./Trail\";\nexport class Modes {\n  constructor() {\n    this.attract = new Attract();\n    this.bounce = new Bounce();\n    this.bubble = new Bubble();\n    this.connect = new Connect();\n    this.grab = new Grab();\n    this.light = new Light();\n    this.push = new Push();\n    this.remove = new Remove();\n    this.repulse = new Repulse();\n    this.slow = new Slow();\n    this.trail = new Trail();\n  }\n\n  load(data) {\n    if (data === undefined) {\n      return;\n    }\n\n    this.attract.load(data.attract);\n    this.bubble.load(data.bubble);\n    this.connect.load(data.connect);\n    this.grab.load(data.grab);\n    this.light.load(data.light);\n    this.push.load(data.push);\n    this.remove.load(data.remove);\n    this.repulse.load(data.repulse);\n    this.slow.load(data.slow);\n    this.trail.load(data.trail);\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Options/Classes/Interactivity/Modes/Modes.js"], "names": ["Attract", "<PERSON><PERSON><PERSON>", "Bubble", "Connect", "<PERSON>rab", "Light", "<PERSON><PERSON>", "Remove", "Repulse", "Slow", "Trail", "Modes", "constructor", "attract", "bounce", "bubble", "connect", "grab", "light", "push", "remove", "repulse", "slow", "trail", "load", "data", "undefined"], "mappings": "AAAA,SAASA,OAAT,QAAwB,WAAxB;AACA,SAASC,MAAT,QAAuB,UAAvB;AACA,SAASC,MAAT,QAAuB,UAAvB;AACA,SAASC,OAAT,QAAwB,WAAxB;AACA,SAASC,IAAT,QAAqB,QAArB;AACA,SAASC,KAAT,QAAsB,SAAtB;AACA,SAASC,IAAT,QAAqB,QAArB;AACA,SAASC,MAAT,QAAuB,UAAvB;AACA,SAASC,OAAT,QAAwB,WAAxB;AACA,SAASC,IAAT,QAAqB,QAArB;AACA,SAASC,KAAT,QAAsB,SAAtB;AACA,OAAO,MAAMC,KAAN,CAAY;AACfC,EAAAA,WAAW,GAAG;AACV,SAAKC,OAAL,GAAe,IAAIb,OAAJ,EAAf;AACA,SAAKc,MAAL,GAAc,IAAIb,MAAJ,EAAd;AACA,SAAKc,MAAL,GAAc,IAAIb,MAAJ,EAAd;AACA,SAAKc,OAAL,GAAe,IAAIb,OAAJ,EAAf;AACA,SAAKc,IAAL,GAAY,IAAIb,IAAJ,EAAZ;AACA,SAAKc,KAAL,GAAa,IAAIb,KAAJ,EAAb;AACA,SAAKc,IAAL,GAAY,IAAIb,IAAJ,EAAZ;AACA,SAAKc,MAAL,GAAc,IAAIb,MAAJ,EAAd;AACA,SAAKc,OAAL,GAAe,IAAIb,OAAJ,EAAf;AACA,SAAKc,IAAL,GAAY,IAAIb,IAAJ,EAAZ;AACA,SAAKc,KAAL,GAAa,IAAIb,KAAJ,EAAb;AACH;;AACDc,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,QAAIA,IAAI,KAAKC,SAAb,EAAwB;AACpB;AACH;;AACD,SAAKb,OAAL,CAAaW,IAAb,CAAkBC,IAAI,CAACZ,OAAvB;AACA,SAAKE,MAAL,CAAYS,IAAZ,CAAiBC,IAAI,CAACV,MAAtB;AACA,SAAKC,OAAL,CAAaQ,IAAb,CAAkBC,IAAI,CAACT,OAAvB;AACA,SAAKC,IAAL,CAAUO,IAAV,CAAeC,IAAI,CAACR,IAApB;AACA,SAAKC,KAAL,CAAWM,IAAX,CAAgBC,IAAI,CAACP,KAArB;AACA,SAAKC,IAAL,CAAUK,IAAV,CAAeC,IAAI,CAACN,IAApB;AACA,SAAKC,MAAL,CAAYI,IAAZ,CAAiBC,IAAI,CAACL,MAAtB;AACA,SAAKC,OAAL,CAAaG,IAAb,CAAkBC,IAAI,CAACJ,OAAvB;AACA,SAAKC,IAAL,CAAUE,IAAV,CAAeC,IAAI,CAACH,IAApB;AACA,SAAKC,KAAL,CAAWC,IAAX,CAAgBC,IAAI,CAACF,KAArB;AACH;;AA5Bc", "sourcesContent": ["import { Attract } from \"./Attract\";\nimport { Bounce } from \"./Bounce\";\nimport { Bubble } from \"./Bubble\";\nimport { Connect } from \"./Connect\";\nimport { Grab } from \"./Grab\";\nimport { Light } from \"./Light\";\nimport { Push } from \"./Push\";\nimport { Remove } from \"./Remove\";\nimport { Repulse } from \"./Repulse\";\nimport { Slow } from \"./Slow\";\nimport { Trail } from \"./Trail\";\nexport class Modes {\n    constructor() {\n        this.attract = new Attract();\n        this.bounce = new Bounce();\n        this.bubble = new Bubble();\n        this.connect = new Connect();\n        this.grab = new Grab();\n        this.light = new Light();\n        this.push = new Push();\n        this.remove = new Remove();\n        this.repulse = new Repulse();\n        this.slow = new Slow();\n        this.trail = new Trail();\n    }\n    load(data) {\n        if (data === undefined) {\n            return;\n        }\n        this.attract.load(data.attract);\n        this.bubble.load(data.bubble);\n        this.connect.load(data.connect);\n        this.grab.load(data.grab);\n        this.light.load(data.light);\n        this.push.load(data.push);\n        this.remove.load(data.remove);\n        this.repulse.load(data.repulse);\n        this.slow.load(data.slow);\n        this.trail.load(data.trail);\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
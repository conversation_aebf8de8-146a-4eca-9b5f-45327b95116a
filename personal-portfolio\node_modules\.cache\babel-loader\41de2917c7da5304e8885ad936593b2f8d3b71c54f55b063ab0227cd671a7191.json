{"ast": null, "code": "import { getRangeValue } from \"tsparticles-engine\";\nexport class StarDrawer {\n  draw(context, particle, radius) {\n    const sides = particle.sides,\n      inset = particle.starInset ?? 2;\n    context.moveTo(0, 0 - radius);\n    for (let i = 0; i < sides; i++) {\n      context.rotate(Math.PI / sides);\n      context.lineTo(0, 0 - radius * inset);\n      context.rotate(Math.PI / sides);\n      context.lineTo(0, 0 - radius);\n    }\n  }\n  getSidesCount(particle) {\n    const star = particle.shapeData;\n    return Math.round(getRangeValue(star?.sides ?? star?.nb_sides ?? 5));\n  }\n  particleInit(container, particle) {\n    const star = particle.shapeData,\n      inset = getRangeValue(star?.inset ?? 2);\n    particle.starInset = inset;\n  }\n}", "map": {"version": 3, "names": ["getRangeValue", "StarDrawer", "draw", "context", "particle", "radius", "sides", "inset", "starInset", "moveTo", "i", "rotate", "Math", "PI", "lineTo", "getSidesCount", "star", "shapeData", "round", "nb_sides", "particleInit", "container"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/tsparticles-shape-star/esm/StarDrawer.js"], "sourcesContent": ["import { getRangeValue } from \"tsparticles-engine\";\nexport class StarDrawer {\n    draw(context, particle, radius) {\n        const sides = particle.sides, inset = particle.starInset ?? 2;\n        context.moveTo(0, 0 - radius);\n        for (let i = 0; i < sides; i++) {\n            context.rotate(Math.PI / sides);\n            context.lineTo(0, 0 - radius * inset);\n            context.rotate(Math.PI / sides);\n            context.lineTo(0, 0 - radius);\n        }\n    }\n    getSidesCount(particle) {\n        const star = particle.shapeData;\n        return Math.round(getRangeValue(star?.sides ?? star?.nb_sides ?? 5));\n    }\n    particleInit(container, particle) {\n        const star = particle.shapeData, inset = getRangeValue(star?.inset ?? 2);\n        particle.starInset = inset;\n    }\n}\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,oBAAoB;AAClD,OAAO,MAAMC,UAAU,CAAC;EACpBC,IAAIA,CAACC,OAAO,EAAEC,QAAQ,EAAEC,MAAM,EAAE;IAC5B,MAAMC,KAAK,GAAGF,QAAQ,CAACE,KAAK;MAAEC,KAAK,GAAGH,QAAQ,CAACI,SAAS,IAAI,CAAC;IAC7DL,OAAO,CAACM,MAAM,CAAC,CAAC,EAAE,CAAC,GAAGJ,MAAM,CAAC;IAC7B,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,KAAK,EAAEI,CAAC,EAAE,EAAE;MAC5BP,OAAO,CAACQ,MAAM,CAACC,IAAI,CAACC,EAAE,GAAGP,KAAK,CAAC;MAC/BH,OAAO,CAACW,MAAM,CAAC,CAAC,EAAE,CAAC,GAAGT,MAAM,GAAGE,KAAK,CAAC;MACrCJ,OAAO,CAACQ,MAAM,CAACC,IAAI,CAACC,EAAE,GAAGP,KAAK,CAAC;MAC/BH,OAAO,CAACW,MAAM,CAAC,CAAC,EAAE,CAAC,GAAGT,MAAM,CAAC;IACjC;EACJ;EACAU,aAAaA,CAACX,QAAQ,EAAE;IACpB,MAAMY,IAAI,GAAGZ,QAAQ,CAACa,SAAS;IAC/B,OAAOL,IAAI,CAACM,KAAK,CAAClB,aAAa,CAACgB,IAAI,EAAEV,KAAK,IAAIU,IAAI,EAAEG,QAAQ,IAAI,CAAC,CAAC,CAAC;EACxE;EACAC,YAAYA,CAACC,SAAS,EAAEjB,QAAQ,EAAE;IAC9B,MAAMY,IAAI,GAAGZ,QAAQ,CAACa,SAAS;MAAEV,KAAK,GAAGP,aAAa,CAACgB,IAAI,EAAET,KAAK,IAAI,CAAC,CAAC;IACxEH,QAAQ,CAACI,SAAS,GAAGD,KAAK;EAC9B;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
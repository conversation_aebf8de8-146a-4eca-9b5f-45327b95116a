{"ast": null, "code": "export class PolygonMaskMove {\n  constructor() {\n    this.radius = 10;\n    this.type = \"path\";\n  }\n\n  load(data) {\n    if (!data) {\n      return;\n    }\n\n    if (data.radius !== undefined) {\n      this.radius = data.radius;\n    }\n\n    if (data.type !== undefined) {\n      this.type = data.type;\n    }\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Plugins/PolygonMask/Options/Classes/PolygonMaskMove.js"], "names": ["PolygonMaskMove", "constructor", "radius", "type", "load", "data", "undefined"], "mappings": "AAAA,OAAO,MAAMA,eAAN,CAAsB;AACzBC,EAAAA,WAAW,GAAG;AACV,SAAKC,MAAL,GAAc,EAAd;AACA,SAAKC,IAAL,GAAY,MAAZ;AACH;;AACDC,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,QAAI,CAACA,IAAL,EAAW;AACP;AACH;;AACD,QAAIA,IAAI,CAACH,MAAL,KAAgBI,SAApB,EAA+B;AAC3B,WAAKJ,MAAL,GAAcG,IAAI,CAACH,MAAnB;AACH;;AACD,QAAIG,IAAI,CAACF,IAAL,KAAcG,SAAlB,EAA6B;AACzB,WAAKH,IAAL,GAAYE,IAAI,CAACF,IAAjB;AACH;AACJ;;AAfwB", "sourcesContent": ["export class PolygonMaskMove {\n    constructor() {\n        this.radius = 10;\n        this.type = \"path\";\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.radius !== undefined) {\n            this.radius = data.radius;\n        }\n        if (data.type !== undefined) {\n            this.type = data.type;\n        }\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
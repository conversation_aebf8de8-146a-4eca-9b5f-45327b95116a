{"ast": null, "code": "import { clamp } from \"@tsparticles/engine\";\nconst half = 0.5,\n  absorbFactor = 10,\n  minAbsorbFactor = 0;\nfunction updateAbsorb(p1, r1, p2, r2, delta, pixelRatio) {\n  const factor = clamp(p1.options.collisions.absorb.speed * delta.factor / absorbFactor, minAbsorbFactor, r2);\n  p1.size.value += factor * half;\n  p2.size.value -= factor;\n  if (r2 <= pixelRatio) {\n    p2.size.value = 0;\n    p2.destroy();\n  }\n}\nexport function absorb(p1, p2, delta, pixelRatio) {\n  const r1 = p1.getRadius(),\n    r2 = p2.getRadius();\n  if (r1 === undefined && r2 !== undefined) {\n    p1.destroy();\n  } else if (r1 !== undefined && r2 === undefined) {\n    p2.destroy();\n  } else if (r1 !== undefined && r2 !== undefined) {\n    if (r1 >= r2) {\n      updateAbsorb(p1, r1, p2, r2, delta, pixelRatio);\n    } else {\n      updateAbsorb(p2, r2, p1, r1, delta, pixelRatio);\n    }\n  }\n}", "map": {"version": 3, "names": ["clamp", "half", "absorbFactor", "minAbsorbFactor", "updateAbsorb", "p1", "r1", "p2", "r2", "delta", "pixelRatio", "factor", "options", "collisions", "absorb", "speed", "size", "value", "destroy", "getRadius", "undefined"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/interaction-particles-collisions/browser/Absorb.js"], "sourcesContent": ["import { clamp } from \"@tsparticles/engine\";\nconst half = 0.5, absorbFactor = 10, minAbsorbFactor = 0;\nfunction updateAbsorb(p1, r1, p2, r2, delta, pixelRatio) {\n    const factor = clamp((p1.options.collisions.absorb.speed * delta.factor) / absorbFactor, minAbsorbFactor, r2);\n    p1.size.value += factor * half;\n    p2.size.value -= factor;\n    if (r2 <= pixelRatio) {\n        p2.size.value = 0;\n        p2.destroy();\n    }\n}\nexport function absorb(p1, p2, delta, pixelRatio) {\n    const r1 = p1.getRadius(), r2 = p2.getRadius();\n    if (r1 === undefined && r2 !== undefined) {\n        p1.destroy();\n    }\n    else if (r1 !== undefined && r2 === undefined) {\n        p2.destroy();\n    }\n    else if (r1 !== undefined && r2 !== undefined) {\n        if (r1 >= r2) {\n            updateAbsorb(p1, r1, p2, r2, delta, pixelRatio);\n        }\n        else {\n            updateAbsorb(p2, r2, p1, r1, delta, pixelRatio);\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,KAAK,QAAQ,qBAAqB;AAC3C,MAAMC,IAAI,GAAG,GAAG;EAAEC,YAAY,GAAG,EAAE;EAAEC,eAAe,GAAG,CAAC;AACxD,SAASC,YAAYA,CAACC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,KAAK,EAAEC,UAAU,EAAE;EACrD,MAAMC,MAAM,GAAGX,KAAK,CAAEK,EAAE,CAACO,OAAO,CAACC,UAAU,CAACC,MAAM,CAACC,KAAK,GAAGN,KAAK,CAACE,MAAM,GAAIT,YAAY,EAAEC,eAAe,EAAEK,EAAE,CAAC;EAC7GH,EAAE,CAACW,IAAI,CAACC,KAAK,IAAIN,MAAM,GAAGV,IAAI;EAC9BM,EAAE,CAACS,IAAI,CAACC,KAAK,IAAIN,MAAM;EACvB,IAAIH,EAAE,IAAIE,UAAU,EAAE;IAClBH,EAAE,CAACS,IAAI,CAACC,KAAK,GAAG,CAAC;IACjBV,EAAE,CAACW,OAAO,CAAC,CAAC;EAChB;AACJ;AACA,OAAO,SAASJ,MAAMA,CAACT,EAAE,EAAEE,EAAE,EAAEE,KAAK,EAAEC,UAAU,EAAE;EAC9C,MAAMJ,EAAE,GAAGD,EAAE,CAACc,SAAS,CAAC,CAAC;IAAEX,EAAE,GAAGD,EAAE,CAACY,SAAS,CAAC,CAAC;EAC9C,IAAIb,EAAE,KAAKc,SAAS,IAAIZ,EAAE,KAAKY,SAAS,EAAE;IACtCf,EAAE,CAACa,OAAO,CAAC,CAAC;EAChB,CAAC,MACI,IAAIZ,EAAE,KAAKc,SAAS,IAAIZ,EAAE,KAAKY,SAAS,EAAE;IAC3Cb,EAAE,CAACW,OAAO,CAAC,CAAC;EAChB,CAAC,MACI,IAAIZ,EAAE,KAAKc,SAAS,IAAIZ,EAAE,KAAKY,SAAS,EAAE;IAC3C,IAAId,EAAE,IAAIE,EAAE,EAAE;MACVJ,YAAY,CAACC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,KAAK,EAAEC,UAAU,CAAC;IACnD,CAAC,MACI;MACDN,YAAY,CAACG,EAAE,EAAEC,EAAE,EAAEH,EAAE,EAAEC,EAAE,EAAEG,KAAK,EAAEC,UAAU,CAAC;IACnD;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
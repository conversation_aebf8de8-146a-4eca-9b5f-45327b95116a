{"ast": null, "code": "import { OutMode, OutModeDirection } from \"@tsparticles/engine\";\nimport { BounceOutMode } from \"./BounceOutMode.js\";\nimport { DestroyOutMode } from \"./DestroyOutMode.js\";\nimport { NoneOutMode } from \"./NoneOutMode.js\";\nimport { OutOutMode } from \"./OutOutMode.js\";\nconst checkOutMode = (outModes, outMode) => {\n  return outModes.default === outMode || outModes.bottom === outMode || outModes.left === outMode || outModes.right === outMode || outModes.top === outMode;\n};\nexport class OutOfCanvasUpdater {\n  constructor(container) {\n    this._updateOutMode = (particle, delta, outMode, direction) => {\n      for (const updater of this.updaters) {\n        updater.update(particle, direction, delta, outMode);\n      }\n    };\n    this.container = container;\n    this.updaters = [];\n  }\n  init(particle) {\n    this.updaters = [];\n    const outModes = particle.options.move.outModes;\n    if (checkOutMode(outModes, OutMode.bounce)) {\n      this.updaters.push(new BounceOutMode(this.container));\n    } else if (checkOutMode(outModes, OutMode.out)) {\n      this.updaters.push(new OutOutMode(this.container));\n    } else if (checkOutMode(outModes, OutMode.destroy)) {\n      this.updaters.push(new DestroyOutMode(this.container));\n    } else if (checkOutMode(outModes, OutMode.none)) {\n      this.updaters.push(new NoneOutMode(this.container));\n    }\n  }\n  isEnabled(particle) {\n    return !particle.destroyed && !particle.spawning;\n  }\n  update(particle, delta) {\n    const outModes = particle.options.move.outModes;\n    this._updateOutMode(particle, delta, outModes.bottom ?? outModes.default, OutModeDirection.bottom);\n    this._updateOutMode(particle, delta, outModes.left ?? outModes.default, OutModeDirection.left);\n    this._updateOutMode(particle, delta, outModes.right ?? outModes.default, OutModeDirection.right);\n    this._updateOutMode(particle, delta, outModes.top ?? outModes.default, OutModeDirection.top);\n  }\n}", "map": {"version": 3, "names": ["OutMode", "OutModeDirection", "BounceOutMode", "DestroyOutMode", "NoneOutMode", "OutOutMode", "checkOutMode", "outModes", "outMode", "default", "bottom", "left", "right", "top", "OutOfCanvasUpdater", "constructor", "container", "_updateOutMode", "particle", "delta", "direction", "updater", "updaters", "update", "init", "options", "move", "bounce", "push", "out", "destroy", "none", "isEnabled", "destroyed", "spawning"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/updater-out-modes/browser/OutOfCanvasUpdater.js"], "sourcesContent": ["import { OutMode, OutModeDirection, } from \"@tsparticles/engine\";\nimport { BounceOutMode } from \"./BounceOutMode.js\";\nimport { DestroyOutMode } from \"./DestroyOutMode.js\";\nimport { NoneOutMode } from \"./NoneOutMode.js\";\nimport { OutOutMode } from \"./OutOutMode.js\";\nconst checkOutMode = (outModes, outMode) => {\n    return (outModes.default === outMode ||\n        outModes.bottom === outMode ||\n        outModes.left === outMode ||\n        outModes.right === outMode ||\n        outModes.top === outMode);\n};\nexport class OutOfCanvasUpdater {\n    constructor(container) {\n        this._updateOutMode = (particle, delta, outMode, direction) => {\n            for (const updater of this.updaters) {\n                updater.update(particle, direction, delta, outMode);\n            }\n        };\n        this.container = container;\n        this.updaters = [];\n    }\n    init(particle) {\n        this.updaters = [];\n        const outModes = particle.options.move.outModes;\n        if (checkOutMode(outModes, OutMode.bounce)) {\n            this.updaters.push(new BounceOutMode(this.container));\n        }\n        else if (checkOutMode(outModes, OutMode.out)) {\n            this.updaters.push(new OutOutMode(this.container));\n        }\n        else if (checkOutMode(outModes, OutMode.destroy)) {\n            this.updaters.push(new DestroyOutMode(this.container));\n        }\n        else if (checkOutMode(outModes, OutMode.none)) {\n            this.updaters.push(new NoneOutMode(this.container));\n        }\n    }\n    isEnabled(particle) {\n        return !particle.destroyed && !particle.spawning;\n    }\n    update(particle, delta) {\n        const outModes = particle.options.move.outModes;\n        this._updateOutMode(particle, delta, outModes.bottom ?? outModes.default, OutModeDirection.bottom);\n        this._updateOutMode(particle, delta, outModes.left ?? outModes.default, OutModeDirection.left);\n        this._updateOutMode(particle, delta, outModes.right ?? outModes.default, OutModeDirection.right);\n        this._updateOutMode(particle, delta, outModes.top ?? outModes.default, OutModeDirection.top);\n    }\n}\n"], "mappings": "AAAA,SAASA,OAAO,EAAEC,gBAAgB,QAAS,qBAAqB;AAChE,SAASC,aAAa,QAAQ,oBAAoB;AAClD,SAASC,cAAc,QAAQ,qBAAqB;AACpD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,UAAU,QAAQ,iBAAiB;AAC5C,MAAMC,YAAY,GAAGA,CAACC,QAAQ,EAAEC,OAAO,KAAK;EACxC,OAAQD,QAAQ,CAACE,OAAO,KAAKD,OAAO,IAChCD,QAAQ,CAACG,MAAM,KAAKF,OAAO,IAC3BD,QAAQ,CAACI,IAAI,KAAKH,OAAO,IACzBD,QAAQ,CAACK,KAAK,KAAKJ,OAAO,IAC1BD,QAAQ,CAACM,GAAG,KAAKL,OAAO;AAChC,CAAC;AACD,OAAO,MAAMM,kBAAkB,CAAC;EAC5BC,WAAWA,CAACC,SAAS,EAAE;IACnB,IAAI,CAACC,cAAc,GAAG,CAACC,QAAQ,EAAEC,KAAK,EAAEX,OAAO,EAAEY,SAAS,KAAK;MAC3D,KAAK,MAAMC,OAAO,IAAI,IAAI,CAACC,QAAQ,EAAE;QACjCD,OAAO,CAACE,MAAM,CAACL,QAAQ,EAAEE,SAAS,EAAED,KAAK,EAAEX,OAAO,CAAC;MACvD;IACJ,CAAC;IACD,IAAI,CAACQ,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACM,QAAQ,GAAG,EAAE;EACtB;EACAE,IAAIA,CAACN,QAAQ,EAAE;IACX,IAAI,CAACI,QAAQ,GAAG,EAAE;IAClB,MAAMf,QAAQ,GAAGW,QAAQ,CAACO,OAAO,CAACC,IAAI,CAACnB,QAAQ;IAC/C,IAAID,YAAY,CAACC,QAAQ,EAAEP,OAAO,CAAC2B,MAAM,CAAC,EAAE;MACxC,IAAI,CAACL,QAAQ,CAACM,IAAI,CAAC,IAAI1B,aAAa,CAAC,IAAI,CAACc,SAAS,CAAC,CAAC;IACzD,CAAC,MACI,IAAIV,YAAY,CAACC,QAAQ,EAAEP,OAAO,CAAC6B,GAAG,CAAC,EAAE;MAC1C,IAAI,CAACP,QAAQ,CAACM,IAAI,CAAC,IAAIvB,UAAU,CAAC,IAAI,CAACW,SAAS,CAAC,CAAC;IACtD,CAAC,MACI,IAAIV,YAAY,CAACC,QAAQ,EAAEP,OAAO,CAAC8B,OAAO,CAAC,EAAE;MAC9C,IAAI,CAACR,QAAQ,CAACM,IAAI,CAAC,IAAIzB,cAAc,CAAC,IAAI,CAACa,SAAS,CAAC,CAAC;IAC1D,CAAC,MACI,IAAIV,YAAY,CAACC,QAAQ,EAAEP,OAAO,CAAC+B,IAAI,CAAC,EAAE;MAC3C,IAAI,CAACT,QAAQ,CAACM,IAAI,CAAC,IAAIxB,WAAW,CAAC,IAAI,CAACY,SAAS,CAAC,CAAC;IACvD;EACJ;EACAgB,SAASA,CAACd,QAAQ,EAAE;IAChB,OAAO,CAACA,QAAQ,CAACe,SAAS,IAAI,CAACf,QAAQ,CAACgB,QAAQ;EACpD;EACAX,MAAMA,CAACL,QAAQ,EAAEC,KAAK,EAAE;IACpB,MAAMZ,QAAQ,GAAGW,QAAQ,CAACO,OAAO,CAACC,IAAI,CAACnB,QAAQ;IAC/C,IAAI,CAACU,cAAc,CAACC,QAAQ,EAAEC,KAAK,EAAEZ,QAAQ,CAACG,MAAM,IAAIH,QAAQ,CAACE,OAAO,EAAER,gBAAgB,CAACS,MAAM,CAAC;IAClG,IAAI,CAACO,cAAc,CAACC,QAAQ,EAAEC,KAAK,EAAEZ,QAAQ,CAACI,IAAI,IAAIJ,QAAQ,CAACE,OAAO,EAAER,gBAAgB,CAACU,IAAI,CAAC;IAC9F,IAAI,CAACM,cAAc,CAACC,QAAQ,EAAEC,KAAK,EAAEZ,QAAQ,CAACK,KAAK,IAAIL,QAAQ,CAACE,OAAO,EAAER,gBAAgB,CAACW,KAAK,CAAC;IAChG,IAAI,CAACK,cAAc,CAACC,QAAQ,EAAEC,KAAK,EAAEZ,QAAQ,CAACM,GAAG,IAAIN,QAAQ,CAACE,OAAO,EAAER,gBAAgB,CAACY,GAAG,CAAC;EAChG;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
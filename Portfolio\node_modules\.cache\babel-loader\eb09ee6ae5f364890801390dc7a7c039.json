{"ast": null, "code": "import { setRangeValue } from \"../../../../Utils\";\nexport class EmitterRate {\n  constructor() {\n    this.quantity = 1;\n    this.delay = 0.1;\n  }\n\n  load(data) {\n    if (data === undefined) {\n      return;\n    }\n\n    if (data.quantity !== undefined) {\n      this.quantity = setRangeValue(data.quantity);\n    }\n\n    if (data.delay !== undefined) {\n      this.delay = setRangeValue(data.delay);\n    }\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Plugins/Emitters/Options/Classes/EmitterRate.js"], "names": ["setRangeValue", "EmitterRate", "constructor", "quantity", "delay", "load", "data", "undefined"], "mappings": "AAAA,SAASA,aAAT,QAA8B,mBAA9B;AACA,OAAO,MAAMC,WAAN,CAAkB;AACrBC,EAAAA,WAAW,GAAG;AACV,SAAKC,QAAL,GAAgB,CAAhB;AACA,SAAKC,KAAL,GAAa,GAAb;AACH;;AACDC,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,QAAIA,IAAI,KAAKC,SAAb,EAAwB;AACpB;AACH;;AACD,QAAID,IAAI,CAACH,QAAL,KAAkBI,SAAtB,EAAiC;AAC7B,WAAKJ,QAAL,GAAgBH,aAAa,CAACM,IAAI,CAACH,QAAN,CAA7B;AACH;;AACD,QAAIG,IAAI,CAACF,KAAL,KAAeG,SAAnB,EAA8B;AAC1B,WAAKH,KAAL,GAAaJ,aAAa,CAACM,IAAI,CAACF,KAAN,CAA1B;AACH;AACJ;;AAfoB", "sourcesContent": ["import { setRangeValue } from \"../../../../Utils\";\nexport class EmitterRate {\n    constructor() {\n        this.quantity = 1;\n        this.delay = 0.1;\n    }\n    load(data) {\n        if (data === undefined) {\n            return;\n        }\n        if (data.quantity !== undefined) {\n            this.quantity = setRangeValue(data.quantity);\n        }\n        if (data.delay !== undefined) {\n            this.delay = setRangeValue(data.delay);\n        }\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
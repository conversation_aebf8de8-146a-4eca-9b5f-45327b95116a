{"ast": null, "code": "export const generatedAttribute = \"generated\";\nexport const mouseDownEvent = \"pointerdown\";\nexport const mouseUpEvent = \"pointerup\";\nexport const mouseLeaveEvent = \"pointerleave\";\nexport const mouseOutEvent = \"pointerout\";\nexport const mouseMoveEvent = \"pointermove\";\nexport const touchStartEvent = \"touchstart\";\nexport const touchEndEvent = \"touchend\";\nexport const touchMoveEvent = \"touchmove\";\nexport const touchCancelEvent = \"touchcancel\";\nexport const resizeEvent = \"resize\";\nexport const visibilityChangeEvent = \"visibilitychange\";\nexport const errorPrefix = \"tsParticles - Error\";\nexport const percentDenominator = 100;\nexport const halfRandom = 0.5;\nexport const millisecondsToSeconds = 1000;", "map": {"version": 3, "names": ["generatedAttribute", "mouseDownEvent", "mouseUpEvent", "mouseLeaveEvent", "mouseOutEvent", "mouseMoveEvent", "touchStartEvent", "touchEndEvent", "touchMoveEvent", "touchCancelEvent", "resizeEvent", "visibilityChangeEvent", "errorPrefix", "percentDenominator", "halfRandom", "millisecondsToSeconds"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/engine/browser/Core/Utils/Constants.js"], "sourcesContent": ["export const generatedAttribute = \"generated\";\nexport const mouseDownEvent = \"pointerdown\";\nexport const mouseUpEvent = \"pointerup\";\nexport const mouseLeaveEvent = \"pointerleave\";\nexport const mouseOutEvent = \"pointerout\";\nexport const mouseMoveEvent = \"pointermove\";\nexport const touchStartEvent = \"touchstart\";\nexport const touchEndEvent = \"touchend\";\nexport const touchMoveEvent = \"touchmove\";\nexport const touchCancelEvent = \"touchcancel\";\nexport const resizeEvent = \"resize\";\nexport const visibilityChangeEvent = \"visibilitychange\";\nexport const errorPrefix = \"tsParticles - Error\";\nexport const percentDenominator = 100;\nexport const halfRandom = 0.5;\nexport const millisecondsToSeconds = 1000;\n"], "mappings": "AAAA,OAAO,MAAMA,kBAAkB,GAAG,WAAW;AAC7C,OAAO,MAAMC,cAAc,GAAG,aAAa;AAC3C,OAAO,MAAMC,YAAY,GAAG,WAAW;AACvC,OAAO,MAAMC,eAAe,GAAG,cAAc;AAC7C,OAAO,MAAMC,aAAa,GAAG,YAAY;AACzC,OAAO,MAAMC,cAAc,GAAG,aAAa;AAC3C,OAAO,MAAMC,eAAe,GAAG,YAAY;AAC3C,OAAO,MAAMC,aAAa,GAAG,UAAU;AACvC,OAAO,MAAMC,cAAc,GAAG,WAAW;AACzC,OAAO,MAAMC,gBAAgB,GAAG,aAAa;AAC7C,OAAO,MAAMC,WAAW,GAAG,QAAQ;AACnC,OAAO,MAAMC,qBAAqB,GAAG,kBAAkB;AACvD,OAAO,MAAMC,WAAW,GAAG,qBAAqB;AAChD,OAAO,MAAMC,kBAAkB,GAAG,GAAG;AACrC,OAAO,MAAMC,UAAU,GAAG,GAAG;AAC7B,OAAO,MAAMC,qBAAqB,GAAG,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
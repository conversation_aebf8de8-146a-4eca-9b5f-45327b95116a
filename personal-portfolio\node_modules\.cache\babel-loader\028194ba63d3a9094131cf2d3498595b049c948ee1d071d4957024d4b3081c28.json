{"ast": null, "code": "import { OutOfCanvasUpdater } from \"./OutOfCanvasUpdater\";\nexport async function loadOutModesUpdater(engine, refresh = true) {\n  await engine.addParticleUpdater(\"outModes\", container => new OutOfCanvasUpdater(container), refresh);\n}", "map": {"version": 3, "names": ["OutOfCanvasUpdater", "loadOutModesUpdater", "engine", "refresh", "addParticleUpdater", "container"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/tsparticles-updater-out-modes/esm/index.js"], "sourcesContent": ["import { OutOfCanvasUpdater } from \"./OutOfCanvasUpdater\";\nexport async function loadOutModesUpdater(engine, refresh = true) {\n    await engine.addParticleUpdater(\"outModes\", (container) => new OutOfCanvasUpdater(container), refresh);\n}\n"], "mappings": "AAAA,SAASA,kBAAkB,QAAQ,sBAAsB;AACzD,OAAO,eAAeC,mBAAmBA,CAACC,MAAM,EAAEC,OAAO,GAAG,IAAI,EAAE;EAC9D,MAAMD,MAAM,CAACE,kBAAkB,CAAC,UAAU,EAAGC,SAAS,IAAK,IAAIL,kBAAkB,CAACK,SAAS,CAAC,EAAEF,OAAO,CAAC;AAC1G", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
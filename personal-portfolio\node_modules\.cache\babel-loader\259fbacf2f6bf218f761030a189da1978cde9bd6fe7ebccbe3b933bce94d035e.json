{"ast": null, "code": "import { getRandom, getRangeValue, getStyleFromHsl, rangeColorToHsl } from \"@tsparticles/engine\";\nimport { Twinkle } from \"./Options/Classes/Twinkle.js\";\nexport class TwinkleUpdater {\n  getColorStyles(particle, context, radius, opacity) {\n    const pOptions = particle.options,\n      twinkleOptions = pOptions.twinkle;\n    if (!twinkleOptions) {\n      return {};\n    }\n    const twinkle = twinkleOptions.particles,\n      twinkling = twinkle.enable && getRandom() < twinkle.frequency,\n      zIndexOptions = particle.options.zIndex,\n      zOffset = 1,\n      zOpacityFactor = (zOffset - particle.zIndexFactor) ** zIndexOptions.opacityRate,\n      twinklingOpacity = twinkling ? getRangeValue(twinkle.opacity) * zOpacityFactor : opacity,\n      twinkleRgb = rangeColorToHsl(twinkle.color),\n      twinkleStyle = twinkleRgb ? getStyleFromHsl(twinkleRgb, twinklingOpacity) : undefined,\n      res = {},\n      needsTwinkle = twinkling && twinkleStyle;\n    res.fill = needsTwinkle ? twinkleStyle : undefined;\n    res.stroke = needsTwinkle ? twinkleStyle : undefined;\n    return res;\n  }\n  async init() {\n    await Promise.resolve();\n  }\n  isEnabled(particle) {\n    const pOptions = particle.options,\n      twinkleOptions = pOptions.twinkle;\n    if (!twinkleOptions) {\n      return false;\n    }\n    return twinkleOptions.particles.enable;\n  }\n  loadOptions(options, ...sources) {\n    if (!options.twinkle) {\n      options.twinkle = new Twinkle();\n    }\n    for (const source of sources) {\n      options.twinkle.load(source?.twinkle);\n    }\n  }\n  async update() {\n    await Promise.resolve();\n  }\n}", "map": {"version": 3, "names": ["getRandom", "getRangeValue", "getStyleFromHsl", "rangeColorToHsl", "Twinkle", "TwinkleUpdater", "getColorStyles", "particle", "context", "radius", "opacity", "pOptions", "options", "twinkleOptions", "twinkle", "particles", "twinkling", "enable", "frequency", "zIndexOptions", "zIndex", "zOffset", "zOpacityFactor", "zIndexFactor", "opacityRate", "twinklingOpacity", "twinkleRgb", "color", "twinkleStyle", "undefined", "res", "needsTwinkle", "fill", "stroke", "init", "Promise", "resolve", "isEnabled", "loadOptions", "sources", "source", "load", "update"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/updater-twinkle/browser/TwinkleUpdater.js"], "sourcesContent": ["import { getRandom, getRangeValue, getStyleFromHsl, rangeColorToHsl, } from \"@tsparticles/engine\";\nimport { Twinkle } from \"./Options/Classes/Twinkle.js\";\nexport class TwinkleUpdater {\n    getColorStyles(particle, context, radius, opacity) {\n        const pOptions = particle.options, twinkleOptions = pOptions.twinkle;\n        if (!twinkleOptions) {\n            return {};\n        }\n        const twinkle = twinkleOptions.particles, twinkling = twinkle.enable && getRandom() < twinkle.frequency, zIndexOptions = particle.options.zIndex, zOffset = 1, zOpacityFactor = (zOffset - particle.zIndexFactor) ** zIndexOptions.opacityRate, twinklingOpacity = twinkling ? getRangeValue(twinkle.opacity) * zOpacityFactor : opacity, twinkleRgb = rangeColorToHsl(twinkle.color), twinkleStyle = twinkleRgb ? getStyleFromHsl(twinkleRgb, twinklingOpacity) : undefined, res = {}, needsTwinkle = twinkling && twinkleStyle;\n        res.fill = needsTwinkle ? twinkleStyle : undefined;\n        res.stroke = needsTwinkle ? twinkleStyle : undefined;\n        return res;\n    }\n    async init() {\n        await Promise.resolve();\n    }\n    isEnabled(particle) {\n        const pOptions = particle.options, twinkleOptions = pOptions.twinkle;\n        if (!twinkleOptions) {\n            return false;\n        }\n        return twinkleOptions.particles.enable;\n    }\n    loadOptions(options, ...sources) {\n        if (!options.twinkle) {\n            options.twinkle = new Twinkle();\n        }\n        for (const source of sources) {\n            options.twinkle.load(source?.twinkle);\n        }\n    }\n    async update() {\n        await Promise.resolve();\n    }\n}\n"], "mappings": "AAAA,SAASA,SAAS,EAAEC,aAAa,EAAEC,eAAe,EAAEC,eAAe,QAAS,qBAAqB;AACjG,SAASC,OAAO,QAAQ,8BAA8B;AACtD,OAAO,MAAMC,cAAc,CAAC;EACxBC,cAAcA,CAACC,QAAQ,EAAEC,OAAO,EAAEC,MAAM,EAAEC,OAAO,EAAE;IAC/C,MAAMC,QAAQ,GAAGJ,QAAQ,CAACK,OAAO;MAAEC,cAAc,GAAGF,QAAQ,CAACG,OAAO;IACpE,IAAI,CAACD,cAAc,EAAE;MACjB,OAAO,CAAC,CAAC;IACb;IACA,MAAMC,OAAO,GAAGD,cAAc,CAACE,SAAS;MAAEC,SAAS,GAAGF,OAAO,CAACG,MAAM,IAAIjB,SAAS,CAAC,CAAC,GAAGc,OAAO,CAACI,SAAS;MAAEC,aAAa,GAAGZ,QAAQ,CAACK,OAAO,CAACQ,MAAM;MAAEC,OAAO,GAAG,CAAC;MAAEC,cAAc,GAAG,CAACD,OAAO,GAAGd,QAAQ,CAACgB,YAAY,KAAKJ,aAAa,CAACK,WAAW;MAAEC,gBAAgB,GAAGT,SAAS,GAAGf,aAAa,CAACa,OAAO,CAACJ,OAAO,CAAC,GAAGY,cAAc,GAAGZ,OAAO;MAAEgB,UAAU,GAAGvB,eAAe,CAACW,OAAO,CAACa,KAAK,CAAC;MAAEC,YAAY,GAAGF,UAAU,GAAGxB,eAAe,CAACwB,UAAU,EAAED,gBAAgB,CAAC,GAAGI,SAAS;MAAEC,GAAG,GAAG,CAAC,CAAC;MAAEC,YAAY,GAAGf,SAAS,IAAIY,YAAY;IAChgBE,GAAG,CAACE,IAAI,GAAGD,YAAY,GAAGH,YAAY,GAAGC,SAAS;IAClDC,GAAG,CAACG,MAAM,GAAGF,YAAY,GAAGH,YAAY,GAAGC,SAAS;IACpD,OAAOC,GAAG;EACd;EACA,MAAMI,IAAIA,CAAA,EAAG;IACT,MAAMC,OAAO,CAACC,OAAO,CAAC,CAAC;EAC3B;EACAC,SAASA,CAAC9B,QAAQ,EAAE;IAChB,MAAMI,QAAQ,GAAGJ,QAAQ,CAACK,OAAO;MAAEC,cAAc,GAAGF,QAAQ,CAACG,OAAO;IACpE,IAAI,CAACD,cAAc,EAAE;MACjB,OAAO,KAAK;IAChB;IACA,OAAOA,cAAc,CAACE,SAAS,CAACE,MAAM;EAC1C;EACAqB,WAAWA,CAAC1B,OAAO,EAAE,GAAG2B,OAAO,EAAE;IAC7B,IAAI,CAAC3B,OAAO,CAACE,OAAO,EAAE;MAClBF,OAAO,CAACE,OAAO,GAAG,IAAIV,OAAO,CAAC,CAAC;IACnC;IACA,KAAK,MAAMoC,MAAM,IAAID,OAAO,EAAE;MAC1B3B,OAAO,CAACE,OAAO,CAAC2B,IAAI,CAACD,MAAM,EAAE1B,OAAO,CAAC;IACzC;EACJ;EACA,MAAM4B,MAAMA,CAAA,EAAG;IACX,MAAMP,OAAO,CAACC,OAAO,CAAC,CAAC;EAC3B;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
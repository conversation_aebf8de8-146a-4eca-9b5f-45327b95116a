{"ast": null, "code": "import { ParticlesInteractorBase, getDistance } from \"@tsparticles/engine\";\nimport { resolveCollision } from \"./ResolveCollision.js\";\nconst double = 2;\nexport class Collider extends ParticlesInteractorBase {\n  constructor(container) {\n    super(container);\n  }\n  clear() {}\n  init() {}\n  interact(p1, delta) {\n    if (p1.destroyed || p1.spawning) {\n      return;\n    }\n    const container = this.container,\n      pos1 = p1.getPosition(),\n      radius1 = p1.getRadius(),\n      query = container.particles.quadTree.queryCircle(pos1, radius1 * double);\n    for (const p2 of query) {\n      if (p1 === p2 || !p2.options.collisions.enable || p1.options.collisions.mode !== p2.options.collisions.mode || p2.destroyed || p2.spawning) {\n        continue;\n      }\n      const pos2 = p2.getPosition(),\n        radius2 = p2.getRadius();\n      if (Math.abs(Math.round(pos1.z) - Math.round(pos2.z)) > radius1 + radius2) {\n        continue;\n      }\n      const dist = getDistance(pos1, pos2),\n        distP = radius1 + radius2;\n      if (dist > distP) {\n        continue;\n      }\n      resolveCollision(p1, p2, delta, container.retina.pixelRatio);\n    }\n  }\n  isEnabled(particle) {\n    return particle.options.collisions.enable;\n  }\n  reset() {}\n}", "map": {"version": 3, "names": ["ParticlesInteractorBase", "getDistance", "resolveCollision", "double", "Collider", "constructor", "container", "clear", "init", "interact", "p1", "delta", "destroyed", "spawning", "pos1", "getPosition", "radius1", "getRadius", "query", "particles", "quadTree", "queryCircle", "p2", "options", "collisions", "enable", "mode", "pos2", "radius2", "Math", "abs", "round", "z", "dist", "distP", "retina", "pixelRatio", "isEnabled", "particle", "reset"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/interaction-particles-collisions/browser/Collider.js"], "sourcesContent": ["import { ParticlesInteractorBase, getDistance } from \"@tsparticles/engine\";\nimport { resolveCollision } from \"./ResolveCollision.js\";\nconst double = 2;\nexport class Collider extends ParticlesInteractorBase {\n    constructor(container) {\n        super(container);\n    }\n    clear() {\n    }\n    init() {\n    }\n    interact(p1, delta) {\n        if (p1.destroyed || p1.spawning) {\n            return;\n        }\n        const container = this.container, pos1 = p1.getPosition(), radius1 = p1.getRadius(), query = container.particles.quadTree.queryCircle(pos1, radius1 * double);\n        for (const p2 of query) {\n            if (p1 === p2 ||\n                !p2.options.collisions.enable ||\n                p1.options.collisions.mode !== p2.options.collisions.mode ||\n                p2.destroyed ||\n                p2.spawning) {\n                continue;\n            }\n            const pos2 = p2.getPosition(), radius2 = p2.getRadius();\n            if (Math.abs(Math.round(pos1.z) - Math.round(pos2.z)) > radius1 + radius2) {\n                continue;\n            }\n            const dist = getDistance(pos1, pos2), distP = radius1 + radius2;\n            if (dist > distP) {\n                continue;\n            }\n            resolveCollision(p1, p2, delta, container.retina.pixelRatio);\n        }\n    }\n    isEnabled(particle) {\n        return particle.options.collisions.enable;\n    }\n    reset() {\n    }\n}\n"], "mappings": "AAAA,SAASA,uBAAuB,EAAEC,WAAW,QAAQ,qBAAqB;AAC1E,SAASC,gBAAgB,QAAQ,uBAAuB;AACxD,MAAMC,MAAM,GAAG,CAAC;AAChB,OAAO,MAAMC,QAAQ,SAASJ,uBAAuB,CAAC;EAClDK,WAAWA,CAACC,SAAS,EAAE;IACnB,KAAK,CAACA,SAAS,CAAC;EACpB;EACAC,KAAKA,CAAA,EAAG,CACR;EACAC,IAAIA,CAAA,EAAG,CACP;EACAC,QAAQA,CAACC,EAAE,EAAEC,KAAK,EAAE;IAChB,IAAID,EAAE,CAACE,SAAS,IAAIF,EAAE,CAACG,QAAQ,EAAE;MAC7B;IACJ;IACA,MAAMP,SAAS,GAAG,IAAI,CAACA,SAAS;MAAEQ,IAAI,GAAGJ,EAAE,CAACK,WAAW,CAAC,CAAC;MAAEC,OAAO,GAAGN,EAAE,CAACO,SAAS,CAAC,CAAC;MAAEC,KAAK,GAAGZ,SAAS,CAACa,SAAS,CAACC,QAAQ,CAACC,WAAW,CAACP,IAAI,EAAEE,OAAO,GAAGb,MAAM,CAAC;IAC7J,KAAK,MAAMmB,EAAE,IAAIJ,KAAK,EAAE;MACpB,IAAIR,EAAE,KAAKY,EAAE,IACT,CAACA,EAAE,CAACC,OAAO,CAACC,UAAU,CAACC,MAAM,IAC7Bf,EAAE,CAACa,OAAO,CAACC,UAAU,CAACE,IAAI,KAAKJ,EAAE,CAACC,OAAO,CAACC,UAAU,CAACE,IAAI,IACzDJ,EAAE,CAACV,SAAS,IACZU,EAAE,CAACT,QAAQ,EAAE;QACb;MACJ;MACA,MAAMc,IAAI,GAAGL,EAAE,CAACP,WAAW,CAAC,CAAC;QAAEa,OAAO,GAAGN,EAAE,CAACL,SAAS,CAAC,CAAC;MACvD,IAAIY,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,KAAK,CAACjB,IAAI,CAACkB,CAAC,CAAC,GAAGH,IAAI,CAACE,KAAK,CAACJ,IAAI,CAACK,CAAC,CAAC,CAAC,GAAGhB,OAAO,GAAGY,OAAO,EAAE;QACvE;MACJ;MACA,MAAMK,IAAI,GAAGhC,WAAW,CAACa,IAAI,EAAEa,IAAI,CAAC;QAAEO,KAAK,GAAGlB,OAAO,GAAGY,OAAO;MAC/D,IAAIK,IAAI,GAAGC,KAAK,EAAE;QACd;MACJ;MACAhC,gBAAgB,CAACQ,EAAE,EAAEY,EAAE,EAAEX,KAAK,EAAEL,SAAS,CAAC6B,MAAM,CAACC,UAAU,CAAC;IAChE;EACJ;EACAC,SAASA,CAACC,QAAQ,EAAE;IAChB,OAAOA,QAAQ,CAACf,OAAO,CAACC,UAAU,CAACC,MAAM;EAC7C;EACAc,KAAKA,CAAA,EAAG,CACR;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
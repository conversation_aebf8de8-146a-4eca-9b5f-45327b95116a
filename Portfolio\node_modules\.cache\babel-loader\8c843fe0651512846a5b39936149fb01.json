{"ast": null, "code": "import { LightGradient } from \"./LightGradient\";\nexport class LightArea {\n  constructor() {\n    this.gradient = new LightGradient();\n    this.radius = 1000;\n  }\n\n  load(data) {\n    if (data === undefined) {\n      return;\n    }\n\n    this.gradient.load(data.gradient);\n\n    if (data.radius !== undefined) {\n      this.radius = data.radius;\n    }\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Options/Classes/Interactivity/Modes/LightArea.js"], "names": ["LightGradient", "LightArea", "constructor", "gradient", "radius", "load", "data", "undefined"], "mappings": "AAAA,SAASA,aAAT,QAA8B,iBAA9B;AACA,OAAO,MAAMC,SAAN,CAAgB;AACnBC,EAAAA,WAAW,GAAG;AACV,SAAKC,QAAL,GAAgB,IAAIH,aAAJ,EAAhB;AACA,SAAKI,MAAL,GAAc,IAAd;AACH;;AACDC,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,QAAIA,IAAI,KAAKC,SAAb,EAAwB;AACpB;AACH;;AACD,SAAKJ,QAAL,CAAcE,IAAd,CAAmBC,IAAI,CAACH,QAAxB;;AACA,QAAIG,IAAI,CAACF,MAAL,KAAgBG,SAApB,EAA+B;AAC3B,WAAKH,MAAL,GAAcE,IAAI,CAACF,MAAnB;AACH;AACJ;;AAbkB", "sourcesContent": ["import { LightGradient } from \"./LightGradient\";\nexport class LightArea {\n    constructor() {\n        this.gradient = new LightGradient();\n        this.radius = 1000;\n    }\n    load(data) {\n        if (data === undefined) {\n            return;\n        }\n        this.gradient.load(data.gradient);\n        if (data.radius !== undefined) {\n            this.radius = data.radius;\n        }\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
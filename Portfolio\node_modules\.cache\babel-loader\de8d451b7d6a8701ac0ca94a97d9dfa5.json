{"ast": null, "code": "import { Connector } from \"./Connector\";\nexport async function loadExternalConnectInteraction(engine) {\n  await engine.addInteractor(\"externalConnect\", container => new Connector(container));\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Interactions/External/Connect/index.js"], "names": ["Connector", "loadExternalConnectInteraction", "engine", "addInteractor", "container"], "mappings": "AAAA,SAASA,SAAT,QAA0B,aAA1B;AACA,OAAO,eAAeC,8BAAf,CAA8CC,MAA9C,EAAsD;AACzD,QAAMA,MAAM,CAACC,aAAP,CAAqB,iBAArB,EAAyCC,SAAD,IAAe,IAAIJ,SAAJ,CAAcI,SAAd,CAAvD,CAAN;AACH", "sourcesContent": ["import { Connector } from \"./Connector\";\nexport async function loadExternalConnectInteraction(engine) {\n    await engine.addInteractor(\"externalConnect\", (container) => new Connector(container));\n}\n"]}, "metadata": {}, "sourceType": "module"}
{"ast": null, "code": "import addLeadingZeros from \"../../addLeadingZeros/index.js\";\n/*\n * |     | Unit                           |     | Unit                           |\n * |-----|--------------------------------|-----|--------------------------------|\n * |  a  | AM, PM                         |  A* |                                |\n * |  d  | Day of month                   |  D  |                                |\n * |  h  | Hour [1-12]                    |  H  | Hour [0-23]                    |\n * |  m  | Minute                         |  M  | Month                          |\n * |  s  | Second                         |  S  | Fraction of second             |\n * |  y  | Year (abs)                     |  Y  |                                |\n *\n * Letters marked by * are not implemented but reserved by Unicode standard.\n */\n\nvar formatters = {\n  // Year\n  y: function (date, token) {\n    // From http://www.unicode.org/reports/tr35/tr35-31/tr35-dates.html#Date_Format_tokens\n    // | Year     |     y | yy |   yyy |  yyyy | yyyyy |\n    // |----------|-------|----|-------|-------|-------|\n    // | AD 1     |     1 | 01 |   001 |  0001 | 00001 |\n    // | AD 12    |    12 | 12 |   012 |  0012 | 00012 |\n    // | AD 123   |   123 | 23 |   123 |  0123 | 00123 |\n    // | AD 1234  |  1234 | 34 |  1234 |  1234 | 01234 |\n    // | AD 12345 | 12345 | 45 | 12345 | 12345 | 12345 |\n    var signedYear = date.getUTCFullYear(); // Returns 1 for 1 BC (which is year 0 in JavaScript)\n\n    var year = signedYear > 0 ? signedYear : 1 - signedYear;\n    return addLeadingZeros(token === 'yy' ? year % 100 : year, token.length);\n  },\n  // Month\n  M: function (date, token) {\n    var month = date.getUTCMonth();\n    return token === 'M' ? String(month + 1) : addLeadingZeros(month + 1, 2);\n  },\n  // Day of the month\n  d: function (date, token) {\n    return addLeadingZeros(date.getUTCDate(), token.length);\n  },\n  // AM or PM\n  a: function (date, token) {\n    var dayPeriodEnumValue = date.getUTCHours() / 12 >= 1 ? 'pm' : 'am';\n\n    switch (token) {\n      case 'a':\n      case 'aa':\n        return dayPeriodEnumValue.toUpperCase();\n\n      case 'aaa':\n        return dayPeriodEnumValue;\n\n      case 'aaaaa':\n        return dayPeriodEnumValue[0];\n\n      case 'aaaa':\n      default:\n        return dayPeriodEnumValue === 'am' ? 'a.m.' : 'p.m.';\n    }\n  },\n  // Hour [1-12]\n  h: function (date, token) {\n    return addLeadingZeros(date.getUTCHours() % 12 || 12, token.length);\n  },\n  // Hour [0-23]\n  H: function (date, token) {\n    return addLeadingZeros(date.getUTCHours(), token.length);\n  },\n  // Minute\n  m: function (date, token) {\n    return addLeadingZeros(date.getUTCMinutes(), token.length);\n  },\n  // Second\n  s: function (date, token) {\n    return addLeadingZeros(date.getUTCSeconds(), token.length);\n  },\n  // Fraction of second\n  S: function (date, token) {\n    var numberOfDigits = token.length;\n    var milliseconds = date.getUTCMilliseconds();\n    var fractionalSeconds = Math.floor(milliseconds * Math.pow(10, numberOfDigits - 3));\n    return addLeadingZeros(fractionalSeconds, token.length);\n  }\n};\nexport default formatters;", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/date-fns/esm/_lib/format/lightFormatters/index.js"], "names": ["addLeadingZeros", "formatters", "y", "date", "token", "signedYear", "getUTCFullYear", "year", "length", "M", "month", "getUTCMonth", "String", "d", "getUTCDate", "a", "dayPeriodEnumValue", "getUTCHours", "toUpperCase", "h", "H", "m", "getUTCMinutes", "s", "getUTCSeconds", "S", "numberOfDigits", "milliseconds", "getUTCMilliseconds", "fractionalSeconds", "Math", "floor", "pow"], "mappings": "AAAA,OAAOA,eAAP,MAA4B,gCAA5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,IAAIC,UAAU,GAAG;AACf;AACAC,EAAAA,CAAC,EAAE,UAAUC,IAAV,EAAgBC,KAAhB,EAAuB;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAIC,UAAU,GAAGF,IAAI,CAACG,cAAL,EAAjB,CATwB,CASgB;;AAExC,QAAIC,IAAI,GAAGF,UAAU,GAAG,CAAb,GAAiBA,UAAjB,GAA8B,IAAIA,UAA7C;AACA,WAAOL,eAAe,CAACI,KAAK,KAAK,IAAV,GAAiBG,IAAI,GAAG,GAAxB,GAA8BA,IAA/B,EAAqCH,KAAK,CAACI,MAA3C,CAAtB;AACD,GAfc;AAgBf;AACAC,EAAAA,CAAC,EAAE,UAAUN,IAAV,EAAgBC,KAAhB,EAAuB;AACxB,QAAIM,KAAK,GAAGP,IAAI,CAACQ,WAAL,EAAZ;AACA,WAAOP,KAAK,KAAK,GAAV,GAAgBQ,MAAM,CAACF,KAAK,GAAG,CAAT,CAAtB,GAAoCV,eAAe,CAACU,KAAK,GAAG,CAAT,EAAY,CAAZ,CAA1D;AACD,GApBc;AAqBf;AACAG,EAAAA,CAAC,EAAE,UAAUV,IAAV,EAAgBC,KAAhB,EAAuB;AACxB,WAAOJ,eAAe,CAACG,IAAI,CAACW,UAAL,EAAD,EAAoBV,KAAK,CAACI,MAA1B,CAAtB;AACD,GAxBc;AAyBf;AACAO,EAAAA,CAAC,EAAE,UAAUZ,IAAV,EAAgBC,KAAhB,EAAuB;AACxB,QAAIY,kBAAkB,GAAGb,IAAI,CAACc,WAAL,KAAqB,EAArB,IAA2B,CAA3B,GAA+B,IAA/B,GAAsC,IAA/D;;AAEA,YAAQb,KAAR;AACE,WAAK,GAAL;AACA,WAAK,IAAL;AACE,eAAOY,kBAAkB,CAACE,WAAnB,EAAP;;AAEF,WAAK,KAAL;AACE,eAAOF,kBAAP;;AAEF,WAAK,OAAL;AACE,eAAOA,kBAAkB,CAAC,CAAD,CAAzB;;AAEF,WAAK,MAAL;AACA;AACE,eAAOA,kBAAkB,KAAK,IAAvB,GAA8B,MAA9B,GAAuC,MAA9C;AAbJ;AAeD,GA5Cc;AA6Cf;AACAG,EAAAA,CAAC,EAAE,UAAUhB,IAAV,EAAgBC,KAAhB,EAAuB;AACxB,WAAOJ,eAAe,CAACG,IAAI,CAACc,WAAL,KAAqB,EAArB,IAA2B,EAA5B,EAAgCb,KAAK,CAACI,MAAtC,CAAtB;AACD,GAhDc;AAiDf;AACAY,EAAAA,CAAC,EAAE,UAAUjB,IAAV,EAAgBC,KAAhB,EAAuB;AACxB,WAAOJ,eAAe,CAACG,IAAI,CAACc,WAAL,EAAD,EAAqBb,KAAK,CAACI,MAA3B,CAAtB;AACD,GApDc;AAqDf;AACAa,EAAAA,CAAC,EAAE,UAAUlB,IAAV,EAAgBC,KAAhB,EAAuB;AACxB,WAAOJ,eAAe,CAACG,IAAI,CAACmB,aAAL,EAAD,EAAuBlB,KAAK,CAACI,MAA7B,CAAtB;AACD,GAxDc;AAyDf;AACAe,EAAAA,CAAC,EAAE,UAAUpB,IAAV,EAAgBC,KAAhB,EAAuB;AACxB,WAAOJ,eAAe,CAACG,IAAI,CAACqB,aAAL,EAAD,EAAuBpB,KAAK,CAACI,MAA7B,CAAtB;AACD,GA5Dc;AA6Df;AACAiB,EAAAA,CAAC,EAAE,UAAUtB,IAAV,EAAgBC,KAAhB,EAAuB;AACxB,QAAIsB,cAAc,GAAGtB,KAAK,CAACI,MAA3B;AACA,QAAImB,YAAY,GAAGxB,IAAI,CAACyB,kBAAL,EAAnB;AACA,QAAIC,iBAAiB,GAAGC,IAAI,CAACC,KAAL,CAAWJ,YAAY,GAAGG,IAAI,CAACE,GAAL,CAAS,EAAT,EAAaN,cAAc,GAAG,CAA9B,CAA1B,CAAxB;AACA,WAAO1B,eAAe,CAAC6B,iBAAD,EAAoBzB,KAAK,CAACI,MAA1B,CAAtB;AACD;AAnEc,CAAjB;AAqEA,eAAeP,UAAf", "sourcesContent": ["import addLeadingZeros from \"../../addLeadingZeros/index.js\";\n/*\n * |     | Unit                           |     | Unit                           |\n * |-----|--------------------------------|-----|--------------------------------|\n * |  a  | AM, PM                         |  A* |                                |\n * |  d  | Day of month                   |  D  |                                |\n * |  h  | Hour [1-12]                    |  H  | Hour [0-23]                    |\n * |  m  | Minute                         |  M  | Month                          |\n * |  s  | Second                         |  S  | Fraction of second             |\n * |  y  | Year (abs)                     |  Y  |                                |\n *\n * Letters marked by * are not implemented but reserved by Unicode standard.\n */\n\nvar formatters = {\n  // Year\n  y: function (date, token) {\n    // From http://www.unicode.org/reports/tr35/tr35-31/tr35-dates.html#Date_Format_tokens\n    // | Year     |     y | yy |   yyy |  yyyy | yyyyy |\n    // |----------|-------|----|-------|-------|-------|\n    // | AD 1     |     1 | 01 |   001 |  0001 | 00001 |\n    // | AD 12    |    12 | 12 |   012 |  0012 | 00012 |\n    // | AD 123   |   123 | 23 |   123 |  0123 | 00123 |\n    // | AD 1234  |  1234 | 34 |  1234 |  1234 | 01234 |\n    // | AD 12345 | 12345 | 45 | 12345 | 12345 | 12345 |\n    var signedYear = date.getUTCFullYear(); // Returns 1 for 1 BC (which is year 0 in JavaScript)\n\n    var year = signedYear > 0 ? signedYear : 1 - signedYear;\n    return addLeadingZeros(token === 'yy' ? year % 100 : year, token.length);\n  },\n  // Month\n  M: function (date, token) {\n    var month = date.getUTCMonth();\n    return token === 'M' ? String(month + 1) : addLeadingZeros(month + 1, 2);\n  },\n  // Day of the month\n  d: function (date, token) {\n    return addLeadingZeros(date.getUTCDate(), token.length);\n  },\n  // AM or PM\n  a: function (date, token) {\n    var dayPeriodEnumValue = date.getUTCHours() / 12 >= 1 ? 'pm' : 'am';\n\n    switch (token) {\n      case 'a':\n      case 'aa':\n        return dayPeriodEnumValue.toUpperCase();\n\n      case 'aaa':\n        return dayPeriodEnumValue;\n\n      case 'aaaaa':\n        return dayPeriodEnumValue[0];\n\n      case 'aaaa':\n      default:\n        return dayPeriodEnumValue === 'am' ? 'a.m.' : 'p.m.';\n    }\n  },\n  // Hour [1-12]\n  h: function (date, token) {\n    return addLeadingZeros(date.getUTCHours() % 12 || 12, token.length);\n  },\n  // Hour [0-23]\n  H: function (date, token) {\n    return addLeadingZeros(date.getUTCHours(), token.length);\n  },\n  // Minute\n  m: function (date, token) {\n    return addLeadingZeros(date.getUTCMinutes(), token.length);\n  },\n  // Second\n  s: function (date, token) {\n    return addLeadingZeros(date.getUTCSeconds(), token.length);\n  },\n  // Fraction of second\n  S: function (date, token) {\n    var numberOfDigits = token.length;\n    var milliseconds = date.getUTCMilliseconds();\n    var fractionalSeconds = Math.floor(milliseconds * Math.pow(10, numberOfDigits - 3));\n    return addLeadingZeros(fractionalSeconds, token.length);\n  }\n};\nexport default formatters;"]}, "metadata": {}, "sourceType": "module"}
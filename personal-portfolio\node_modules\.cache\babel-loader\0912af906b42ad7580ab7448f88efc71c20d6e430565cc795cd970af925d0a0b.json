{"ast": null, "code": "import { clear, drawParticle, drawParticlePlugin, drawPlugin, paintBase, paintImage } from \"../Utils/CanvasUtils.js\";\nimport { deepExtend, getLogger, safeMutationObserver } from \"../Utils/Utils.js\";\nimport { getStyleFromHsl, getStyleFromRgb, rangeColorToHsl, rangeColorToRgb } from \"../Utils/ColorUtils.js\";\nimport { generatedAttribute } from \"./Utils/Constants.js\";\nfunction setTransformValue(factor, newFactor, key) {\n  const newValue = newFactor[key],\n    defaultValue = 1;\n  if (newValue !== undefined) {\n    factor[key] = (factor[key] ?? defaultValue) * newValue;\n  }\n}\nfunction setStyle(canvas, style, important = false) {\n  if (!style) {\n    return;\n  }\n  const element = canvas;\n  if (!element) {\n    return;\n  }\n  const elementStyle = element.style;\n  if (!elementStyle) {\n    return;\n  }\n  for (const key in style) {\n    const value = style[key];\n    if (!value) {\n      continue;\n    }\n    elementStyle.setProperty(key, value, important ? \"important\" : \"\");\n  }\n}\nexport class Canvas {\n  constructor(container) {\n    this.container = container;\n    this._applyPostDrawUpdaters = particle => {\n      for (const updater of this._postDrawUpdaters) {\n        updater.afterDraw?.(particle);\n      }\n    };\n    this._applyPreDrawUpdaters = (ctx, particle, radius, zOpacity, colorStyles, transform) => {\n      for (const updater of this._preDrawUpdaters) {\n        if (updater.getColorStyles) {\n          const {\n            fill,\n            stroke\n          } = updater.getColorStyles(particle, ctx, radius, zOpacity);\n          if (fill) {\n            colorStyles.fill = fill;\n          }\n          if (stroke) {\n            colorStyles.stroke = stroke;\n          }\n        }\n        if (updater.getTransformValues) {\n          const updaterTransform = updater.getTransformValues(particle);\n          for (const key in updaterTransform) {\n            setTransformValue(transform, updaterTransform, key);\n          }\n        }\n        updater.beforeDraw?.(particle);\n      }\n    };\n    this._applyResizePlugins = () => {\n      for (const plugin of this._resizePlugins) {\n        plugin.resize?.();\n      }\n    };\n    this._getPluginParticleColors = particle => {\n      let fColor, sColor;\n      for (const plugin of this._colorPlugins) {\n        if (!fColor && plugin.particleFillColor) {\n          fColor = rangeColorToHsl(plugin.particleFillColor(particle));\n        }\n        if (!sColor && plugin.particleStrokeColor) {\n          sColor = rangeColorToHsl(plugin.particleStrokeColor(particle));\n        }\n        if (fColor && sColor) {\n          break;\n        }\n      }\n      return [fColor, sColor];\n    };\n    this._initCover = async () => {\n      const options = this.container.actualOptions,\n        cover = options.backgroundMask.cover,\n        color = cover.color;\n      if (color) {\n        const coverRgb = rangeColorToRgb(color);\n        if (coverRgb) {\n          const coverColor = {\n            ...coverRgb,\n            a: cover.opacity\n          };\n          this._coverColorStyle = getStyleFromRgb(coverColor, coverColor.a);\n        }\n      } else {\n        await new Promise((resolve, reject) => {\n          if (!cover.image) {\n            return;\n          }\n          const img = document.createElement(\"img\");\n          img.addEventListener(\"load\", () => {\n            this._coverImage = {\n              image: img,\n              opacity: cover.opacity\n            };\n            resolve();\n          });\n          img.addEventListener(\"error\", evt => {\n            reject(evt.error);\n          });\n          img.src = cover.image;\n        });\n      }\n    };\n    this._initStyle = () => {\n      const element = this.element,\n        options = this.container.actualOptions;\n      if (!element) {\n        return;\n      }\n      if (this._fullScreen) {\n        this._originalStyle = deepExtend({}, element.style);\n        this._setFullScreenStyle();\n      } else {\n        this._resetOriginalStyle();\n      }\n      for (const key in options.style) {\n        if (!key || !options.style) {\n          continue;\n        }\n        const value = options.style[key];\n        if (!value) {\n          continue;\n        }\n        element.style.setProperty(key, value, \"important\");\n      }\n    };\n    this._initTrail = async () => {\n      const options = this.container.actualOptions,\n        trail = options.particles.move.trail,\n        trailFill = trail.fill;\n      if (!trail.enable) {\n        return;\n      }\n      const factorNumerator = 1,\n        opacity = factorNumerator / trail.length;\n      if (trailFill.color) {\n        const fillColor = rangeColorToRgb(trailFill.color);\n        if (!fillColor) {\n          return;\n        }\n        this._trailFill = {\n          color: {\n            ...fillColor\n          },\n          opacity\n        };\n      } else {\n        await new Promise((resolve, reject) => {\n          if (!trailFill.image) {\n            return;\n          }\n          const img = document.createElement(\"img\");\n          img.addEventListener(\"load\", () => {\n            this._trailFill = {\n              image: img,\n              opacity\n            };\n            resolve();\n          });\n          img.addEventListener(\"error\", evt => {\n            reject(evt.error);\n          });\n          img.src = trailFill.image;\n        });\n      }\n    };\n    this._paintBase = baseColor => {\n      this.draw(ctx => paintBase(ctx, this.size, baseColor));\n    };\n    this._paintImage = (image, opacity) => {\n      this.draw(ctx => paintImage(ctx, this.size, image, opacity));\n    };\n    this._repairStyle = () => {\n      const element = this.element;\n      if (!element) {\n        return;\n      }\n      this._safeMutationObserver(observer => observer.disconnect());\n      this._initStyle();\n      this.initBackground();\n      this._safeMutationObserver(observer => {\n        if (!element || !(element instanceof Node)) {\n          return;\n        }\n        observer.observe(element, {\n          attributes: true\n        });\n      });\n    };\n    this._resetOriginalStyle = () => {\n      const element = this.element,\n        originalStyle = this._originalStyle;\n      if (!(element && originalStyle)) {\n        return;\n      }\n      setStyle(element, originalStyle);\n    };\n    this._safeMutationObserver = callback => {\n      if (!this._mutationObserver) {\n        return;\n      }\n      callback(this._mutationObserver);\n    };\n    this._setFullScreenStyle = () => {\n      const element = this.element;\n      if (!element) {\n        return;\n      }\n      const radix = 10;\n      setStyle(element, {\n        position: \"fixed\",\n        zIndex: this.container.actualOptions.fullScreen.zIndex.toString(radix),\n        top: \"0\",\n        left: \"0\",\n        width: \"100%\",\n        height: \"100%\"\n      }, true);\n    };\n    this.size = {\n      height: 0,\n      width: 0\n    };\n    this._context = null;\n    this._generated = false;\n    this._preDrawUpdaters = [];\n    this._postDrawUpdaters = [];\n    this._resizePlugins = [];\n    this._colorPlugins = [];\n  }\n  get _fullScreen() {\n    return this.container.actualOptions.fullScreen.enable;\n  }\n  clear() {\n    const options = this.container.actualOptions,\n      trail = options.particles.move.trail,\n      trailFill = this._trailFill,\n      minimumLength = 0;\n    if (options.backgroundMask.enable) {\n      this.paint();\n    } else if (trail.enable && trail.length > minimumLength && trailFill) {\n      if (trailFill.color) {\n        this._paintBase(getStyleFromRgb(trailFill.color, trailFill.opacity));\n      } else if (trailFill.image) {\n        this._paintImage(trailFill.image, trailFill.opacity);\n      }\n    } else if (options.clear) {\n      this.draw(ctx => {\n        clear(ctx, this.size);\n      });\n    }\n  }\n  destroy() {\n    this.stop();\n    if (this._generated) {\n      const element = this.element;\n      element?.remove();\n    } else {\n      this._resetOriginalStyle();\n    }\n    this._preDrawUpdaters = [];\n    this._postDrawUpdaters = [];\n    this._resizePlugins = [];\n    this._colorPlugins = [];\n  }\n  draw(cb) {\n    const ctx = this._context;\n    if (!ctx) {\n      return;\n    }\n    return cb(ctx);\n  }\n  drawAsync(cb) {\n    const ctx = this._context;\n    if (!ctx) {\n      return undefined;\n    }\n    return cb(ctx);\n  }\n  drawParticle(particle, delta) {\n    if (particle.spawning || particle.destroyed) {\n      return;\n    }\n    const radius = particle.getRadius(),\n      minimumSize = 0;\n    if (radius <= minimumSize) {\n      return;\n    }\n    const pfColor = particle.getFillColor(),\n      psColor = particle.getStrokeColor() ?? pfColor;\n    let [fColor, sColor] = this._getPluginParticleColors(particle);\n    if (!fColor) {\n      fColor = pfColor;\n    }\n    if (!sColor) {\n      sColor = psColor;\n    }\n    if (!fColor && !sColor) {\n      return;\n    }\n    this.draw(ctx => {\n      const container = this.container,\n        options = container.actualOptions,\n        zIndexOptions = particle.options.zIndex,\n        zIndexFactorOffset = 1,\n        zIndexFactor = zIndexFactorOffset - particle.zIndexFactor,\n        zOpacityFactor = zIndexFactor ** zIndexOptions.opacityRate,\n        defaultOpacity = 1,\n        opacity = particle.bubble.opacity ?? particle.opacity?.value ?? defaultOpacity,\n        strokeOpacity = particle.strokeOpacity ?? opacity,\n        zOpacity = opacity * zOpacityFactor,\n        zStrokeOpacity = strokeOpacity * zOpacityFactor,\n        transform = {},\n        colorStyles = {\n          fill: fColor ? getStyleFromHsl(fColor, zOpacity) : undefined\n        };\n      colorStyles.stroke = sColor ? getStyleFromHsl(sColor, zStrokeOpacity) : colorStyles.fill;\n      this._applyPreDrawUpdaters(ctx, particle, radius, zOpacity, colorStyles, transform);\n      drawParticle({\n        container,\n        context: ctx,\n        particle,\n        delta,\n        colorStyles,\n        backgroundMask: options.backgroundMask.enable,\n        composite: options.backgroundMask.composite,\n        radius: radius * zIndexFactor ** zIndexOptions.sizeRate,\n        opacity: zOpacity,\n        shadow: particle.options.shadow,\n        transform\n      });\n      this._applyPostDrawUpdaters(particle);\n    });\n  }\n  drawParticlePlugin(plugin, particle, delta) {\n    this.draw(ctx => drawParticlePlugin(ctx, plugin, particle, delta));\n  }\n  drawPlugin(plugin, delta) {\n    this.draw(ctx => drawPlugin(ctx, plugin, delta));\n  }\n  async init() {\n    this._safeMutationObserver(obs => obs.disconnect());\n    this._mutationObserver = safeMutationObserver(records => {\n      for (const record of records) {\n        if (record.type === \"attributes\" && record.attributeName === \"style\") {\n          this._repairStyle();\n        }\n      }\n    });\n    this.resize();\n    this._initStyle();\n    await this._initCover();\n    try {\n      await this._initTrail();\n    } catch (e) {\n      getLogger().error(e);\n    }\n    this.initBackground();\n    this._safeMutationObserver(obs => {\n      if (!this.element || !(this.element instanceof Node)) {\n        return;\n      }\n      obs.observe(this.element, {\n        attributes: true\n      });\n    });\n    this.initUpdaters();\n    this.initPlugins();\n    this.paint();\n  }\n  initBackground() {\n    const options = this.container.actualOptions,\n      background = options.background,\n      element = this.element;\n    if (!element) {\n      return;\n    }\n    const elementStyle = element.style;\n    if (!elementStyle) {\n      return;\n    }\n    if (background.color) {\n      const color = rangeColorToRgb(background.color);\n      elementStyle.backgroundColor = color ? getStyleFromRgb(color, background.opacity) : \"\";\n    } else {\n      elementStyle.backgroundColor = \"\";\n    }\n    elementStyle.backgroundImage = background.image || \"\";\n    elementStyle.backgroundPosition = background.position || \"\";\n    elementStyle.backgroundRepeat = background.repeat || \"\";\n    elementStyle.backgroundSize = background.size || \"\";\n  }\n  initPlugins() {\n    this._resizePlugins = [];\n    for (const [, plugin] of this.container.plugins) {\n      if (plugin.resize) {\n        this._resizePlugins.push(plugin);\n      }\n      if (plugin.particleFillColor ?? plugin.particleStrokeColor) {\n        this._colorPlugins.push(plugin);\n      }\n    }\n  }\n  initUpdaters() {\n    this._preDrawUpdaters = [];\n    this._postDrawUpdaters = [];\n    for (const updater of this.container.particles.updaters) {\n      if (updater.afterDraw) {\n        this._postDrawUpdaters.push(updater);\n      }\n      if (updater.getColorStyles ?? updater.getTransformValues ?? updater.beforeDraw) {\n        this._preDrawUpdaters.push(updater);\n      }\n    }\n  }\n  loadCanvas(canvas) {\n    if (this._generated && this.element) {\n      this.element.remove();\n    }\n    this._generated = canvas.dataset && generatedAttribute in canvas.dataset ? canvas.dataset[generatedAttribute] === \"true\" : this._generated;\n    this.element = canvas;\n    this.element.ariaHidden = \"true\";\n    this._originalStyle = deepExtend({}, this.element.style);\n    this.size.height = canvas.offsetHeight;\n    this.size.width = canvas.offsetWidth;\n    this._context = this.element.getContext(\"2d\");\n    this._safeMutationObserver(obs => {\n      if (!this.element || !(this.element instanceof Node)) {\n        return;\n      }\n      obs.observe(this.element, {\n        attributes: true\n      });\n    });\n    this.container.retina.init();\n    this.initBackground();\n  }\n  paint() {\n    const options = this.container.actualOptions;\n    this.draw(ctx => {\n      if (options.backgroundMask.enable && options.backgroundMask.cover) {\n        clear(ctx, this.size);\n        if (this._coverImage) {\n          this._paintImage(this._coverImage.image, this._coverImage.opacity);\n        } else if (this._coverColorStyle) {\n          this._paintBase(this._coverColorStyle);\n        } else {\n          this._paintBase();\n        }\n      } else {\n        this._paintBase();\n      }\n    });\n  }\n  resize() {\n    if (!this.element) {\n      return false;\n    }\n    const container = this.container,\n      pxRatio = container.retina.pixelRatio,\n      size = container.canvas.size,\n      newSize = {\n        width: this.element.offsetWidth * pxRatio,\n        height: this.element.offsetHeight * pxRatio\n      };\n    if (newSize.height === size.height && newSize.width === size.width && newSize.height === this.element.height && newSize.width === this.element.width) {\n      return false;\n    }\n    const oldSize = {\n      ...size\n    };\n    this.element.width = size.width = this.element.offsetWidth * pxRatio;\n    this.element.height = size.height = this.element.offsetHeight * pxRatio;\n    if (this.container.started) {\n      container.particles.setResizeFactor({\n        width: size.width / oldSize.width,\n        height: size.height / oldSize.height\n      });\n    }\n    return true;\n  }\n  stop() {\n    this._safeMutationObserver(obs => obs.disconnect());\n    this._mutationObserver = undefined;\n    this.draw(ctx => clear(ctx, this.size));\n  }\n  async windowResize() {\n    if (!this.element || !this.resize()) {\n      return;\n    }\n    const container = this.container,\n      needsRefresh = container.updateActualOptions();\n    container.particles.setDensity();\n    this._applyResizePlugins();\n    if (needsRefresh) {\n      await container.refresh();\n    }\n  }\n}", "map": {"version": 3, "names": ["clear", "drawParticle", "drawParticlePlugin", "drawPlugin", "paintBase", "paintImage", "deepExtend", "<PERSON><PERSON><PERSON><PERSON>", "safeMutationObserver", "getStyleFromHsl", "getStyleFromRgb", "rangeColorToHsl", "rangeColorToRgb", "generatedAttribute", "setTransformValue", "factor", "newFactor", "key", "newValue", "defaultValue", "undefined", "setStyle", "canvas", "style", "important", "element", "elementStyle", "value", "setProperty", "<PERSON><PERSON>", "constructor", "container", "_applyPostDrawUpdaters", "particle", "updater", "_postDrawUpdaters", "afterDraw", "_applyPreDrawUpdaters", "ctx", "radius", "zOpacity", "colorStyles", "transform", "_preDrawUpdaters", "getColorStyles", "fill", "stroke", "getTransformValues", "updaterTransform", "beforeDraw", "_applyResizePlugins", "plugin", "_resizePlugins", "resize", "_getPluginParticleColors", "fColor", "sColor", "_colorPlugins", "particleFillColor", "particleStrokeColor", "_initCover", "options", "actualOptions", "cover", "backgroundMask", "color", "coverRgb", "coverColor", "a", "opacity", "_coverColorStyle", "Promise", "resolve", "reject", "image", "img", "document", "createElement", "addEventListener", "_coverImage", "evt", "error", "src", "_initStyle", "_fullScreen", "_originalStyle", "_setFullScreenStyle", "_resetOriginalStyle", "_initTrail", "trail", "particles", "move", "trailFill", "enable", "factorNumerator", "length", "fillColor", "_trailFill", "_paintBase", "baseColor", "draw", "size", "_paintImage", "_repairStyle", "_safeMutationObserver", "observer", "disconnect", "initBackground", "Node", "observe", "attributes", "originalStyle", "callback", "_mutationObserver", "radix", "position", "zIndex", "fullScreen", "toString", "top", "left", "width", "height", "_context", "_generated", "minimumLength", "paint", "destroy", "stop", "remove", "cb", "drawAsync", "delta", "spawning", "destroyed", "getRadius", "minimumSize", "pfColor", "getFillColor", "psColor", "getStrokeColor", "zIndexOptions", "zIndexFactorOffset", "zIndexFactor", "zOpacityFactor", "opacityRate", "defaultOpacity", "bubble", "strokeOpacity", "zStrokeOpacity", "context", "composite", "sizeRate", "shadow", "init", "obs", "records", "record", "type", "attributeName", "e", "initUpdaters", "initPlugins", "background", "backgroundColor", "backgroundImage", "backgroundPosition", "backgroundRepeat", "repeat", "backgroundSize", "plugins", "push", "updaters", "loadCanvas", "dataset", "ariaHidden", "offsetHeight", "offsetWidth", "getContext", "retina", "pxRatio", "pixelRatio", "newSize", "oldSize", "started", "setResizeFactor", "windowResize", "needsRefresh", "updateActualOptions", "setDensity", "refresh"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/engine/browser/Core/Canvas.js"], "sourcesContent": ["import { clear, drawParticle, drawParticlePlugin, drawPlugin, paintBase, paintImage } from \"../Utils/CanvasUtils.js\";\nimport { deepExtend, getLogger, safeMutationObserver } from \"../Utils/Utils.js\";\nimport { getStyleFromHsl, getStyleFromRgb, rangeColorToHsl, rangeColorToRgb } from \"../Utils/ColorUtils.js\";\nimport { generatedAttribute } from \"./Utils/Constants.js\";\nfunction setTransformValue(factor, newFactor, key) {\n    const newValue = newFactor[key], defaultValue = 1;\n    if (newValue !== undefined) {\n        factor[key] = (factor[key] ?? defaultValue) * newValue;\n    }\n}\nfunction setStyle(canvas, style, important = false) {\n    if (!style) {\n        return;\n    }\n    const element = canvas;\n    if (!element) {\n        return;\n    }\n    const elementStyle = element.style;\n    if (!elementStyle) {\n        return;\n    }\n    for (const key in style) {\n        const value = style[key];\n        if (!value) {\n            continue;\n        }\n        elementStyle.setProperty(key, value, important ? \"important\" : \"\");\n    }\n}\nexport class Canvas {\n    constructor(container) {\n        this.container = container;\n        this._applyPostDrawUpdaters = particle => {\n            for (const updater of this._postDrawUpdaters) {\n                updater.afterDraw?.(particle);\n            }\n        };\n        this._applyPreDrawUpdaters = (ctx, particle, radius, zOpacity, colorStyles, transform) => {\n            for (const updater of this._preDrawUpdaters) {\n                if (updater.getColorStyles) {\n                    const { fill, stroke } = updater.getColorStyles(particle, ctx, radius, zOpacity);\n                    if (fill) {\n                        colorStyles.fill = fill;\n                    }\n                    if (stroke) {\n                        colorStyles.stroke = stroke;\n                    }\n                }\n                if (updater.getTransformValues) {\n                    const updaterTransform = updater.getTransformValues(particle);\n                    for (const key in updaterTransform) {\n                        setTransformValue(transform, updaterTransform, key);\n                    }\n                }\n                updater.beforeDraw?.(particle);\n            }\n        };\n        this._applyResizePlugins = () => {\n            for (const plugin of this._resizePlugins) {\n                plugin.resize?.();\n            }\n        };\n        this._getPluginParticleColors = particle => {\n            let fColor, sColor;\n            for (const plugin of this._colorPlugins) {\n                if (!fColor && plugin.particleFillColor) {\n                    fColor = rangeColorToHsl(plugin.particleFillColor(particle));\n                }\n                if (!sColor && plugin.particleStrokeColor) {\n                    sColor = rangeColorToHsl(plugin.particleStrokeColor(particle));\n                }\n                if (fColor && sColor) {\n                    break;\n                }\n            }\n            return [fColor, sColor];\n        };\n        this._initCover = async () => {\n            const options = this.container.actualOptions, cover = options.backgroundMask.cover, color = cover.color;\n            if (color) {\n                const coverRgb = rangeColorToRgb(color);\n                if (coverRgb) {\n                    const coverColor = {\n                        ...coverRgb,\n                        a: cover.opacity,\n                    };\n                    this._coverColorStyle = getStyleFromRgb(coverColor, coverColor.a);\n                }\n            }\n            else {\n                await new Promise((resolve, reject) => {\n                    if (!cover.image) {\n                        return;\n                    }\n                    const img = document.createElement(\"img\");\n                    img.addEventListener(\"load\", () => {\n                        this._coverImage = {\n                            image: img,\n                            opacity: cover.opacity,\n                        };\n                        resolve();\n                    });\n                    img.addEventListener(\"error\", evt => {\n                        reject(evt.error);\n                    });\n                    img.src = cover.image;\n                });\n            }\n        };\n        this._initStyle = () => {\n            const element = this.element, options = this.container.actualOptions;\n            if (!element) {\n                return;\n            }\n            if (this._fullScreen) {\n                this._originalStyle = deepExtend({}, element.style);\n                this._setFullScreenStyle();\n            }\n            else {\n                this._resetOriginalStyle();\n            }\n            for (const key in options.style) {\n                if (!key || !options.style) {\n                    continue;\n                }\n                const value = options.style[key];\n                if (!value) {\n                    continue;\n                }\n                element.style.setProperty(key, value, \"important\");\n            }\n        };\n        this._initTrail = async () => {\n            const options = this.container.actualOptions, trail = options.particles.move.trail, trailFill = trail.fill;\n            if (!trail.enable) {\n                return;\n            }\n            const factorNumerator = 1, opacity = factorNumerator / trail.length;\n            if (trailFill.color) {\n                const fillColor = rangeColorToRgb(trailFill.color);\n                if (!fillColor) {\n                    return;\n                }\n                this._trailFill = {\n                    color: {\n                        ...fillColor,\n                    },\n                    opacity,\n                };\n            }\n            else {\n                await new Promise((resolve, reject) => {\n                    if (!trailFill.image) {\n                        return;\n                    }\n                    const img = document.createElement(\"img\");\n                    img.addEventListener(\"load\", () => {\n                        this._trailFill = {\n                            image: img,\n                            opacity,\n                        };\n                        resolve();\n                    });\n                    img.addEventListener(\"error\", evt => {\n                        reject(evt.error);\n                    });\n                    img.src = trailFill.image;\n                });\n            }\n        };\n        this._paintBase = baseColor => {\n            this.draw(ctx => paintBase(ctx, this.size, baseColor));\n        };\n        this._paintImage = (image, opacity) => {\n            this.draw(ctx => paintImage(ctx, this.size, image, opacity));\n        };\n        this._repairStyle = () => {\n            const element = this.element;\n            if (!element) {\n                return;\n            }\n            this._safeMutationObserver(observer => observer.disconnect());\n            this._initStyle();\n            this.initBackground();\n            this._safeMutationObserver(observer => {\n                if (!element || !(element instanceof Node)) {\n                    return;\n                }\n                observer.observe(element, { attributes: true });\n            });\n        };\n        this._resetOriginalStyle = () => {\n            const element = this.element, originalStyle = this._originalStyle;\n            if (!(element && originalStyle)) {\n                return;\n            }\n            setStyle(element, originalStyle);\n        };\n        this._safeMutationObserver = callback => {\n            if (!this._mutationObserver) {\n                return;\n            }\n            callback(this._mutationObserver);\n        };\n        this._setFullScreenStyle = () => {\n            const element = this.element;\n            if (!element) {\n                return;\n            }\n            const radix = 10;\n            setStyle(element, {\n                position: \"fixed\",\n                zIndex: this.container.actualOptions.fullScreen.zIndex.toString(radix),\n                top: \"0\",\n                left: \"0\",\n                width: \"100%\",\n                height: \"100%\",\n            }, true);\n        };\n        this.size = {\n            height: 0,\n            width: 0,\n        };\n        this._context = null;\n        this._generated = false;\n        this._preDrawUpdaters = [];\n        this._postDrawUpdaters = [];\n        this._resizePlugins = [];\n        this._colorPlugins = [];\n    }\n    get _fullScreen() {\n        return this.container.actualOptions.fullScreen.enable;\n    }\n    clear() {\n        const options = this.container.actualOptions, trail = options.particles.move.trail, trailFill = this._trailFill, minimumLength = 0;\n        if (options.backgroundMask.enable) {\n            this.paint();\n        }\n        else if (trail.enable && trail.length > minimumLength && trailFill) {\n            if (trailFill.color) {\n                this._paintBase(getStyleFromRgb(trailFill.color, trailFill.opacity));\n            }\n            else if (trailFill.image) {\n                this._paintImage(trailFill.image, trailFill.opacity);\n            }\n        }\n        else if (options.clear) {\n            this.draw(ctx => {\n                clear(ctx, this.size);\n            });\n        }\n    }\n    destroy() {\n        this.stop();\n        if (this._generated) {\n            const element = this.element;\n            element?.remove();\n        }\n        else {\n            this._resetOriginalStyle();\n        }\n        this._preDrawUpdaters = [];\n        this._postDrawUpdaters = [];\n        this._resizePlugins = [];\n        this._colorPlugins = [];\n    }\n    draw(cb) {\n        const ctx = this._context;\n        if (!ctx) {\n            return;\n        }\n        return cb(ctx);\n    }\n    drawAsync(cb) {\n        const ctx = this._context;\n        if (!ctx) {\n            return undefined;\n        }\n        return cb(ctx);\n    }\n    drawParticle(particle, delta) {\n        if (particle.spawning || particle.destroyed) {\n            return;\n        }\n        const radius = particle.getRadius(), minimumSize = 0;\n        if (radius <= minimumSize) {\n            return;\n        }\n        const pfColor = particle.getFillColor(), psColor = particle.getStrokeColor() ?? pfColor;\n        let [fColor, sColor] = this._getPluginParticleColors(particle);\n        if (!fColor) {\n            fColor = pfColor;\n        }\n        if (!sColor) {\n            sColor = psColor;\n        }\n        if (!fColor && !sColor) {\n            return;\n        }\n        this.draw((ctx) => {\n            const container = this.container, options = container.actualOptions, zIndexOptions = particle.options.zIndex, zIndexFactorOffset = 1, zIndexFactor = zIndexFactorOffset - particle.zIndexFactor, zOpacityFactor = zIndexFactor ** zIndexOptions.opacityRate, defaultOpacity = 1, opacity = particle.bubble.opacity ?? particle.opacity?.value ?? defaultOpacity, strokeOpacity = particle.strokeOpacity ?? opacity, zOpacity = opacity * zOpacityFactor, zStrokeOpacity = strokeOpacity * zOpacityFactor, transform = {}, colorStyles = {\n                fill: fColor ? getStyleFromHsl(fColor, zOpacity) : undefined,\n            };\n            colorStyles.stroke = sColor ? getStyleFromHsl(sColor, zStrokeOpacity) : colorStyles.fill;\n            this._applyPreDrawUpdaters(ctx, particle, radius, zOpacity, colorStyles, transform);\n            drawParticle({\n                container,\n                context: ctx,\n                particle,\n                delta,\n                colorStyles,\n                backgroundMask: options.backgroundMask.enable,\n                composite: options.backgroundMask.composite,\n                radius: radius * zIndexFactor ** zIndexOptions.sizeRate,\n                opacity: zOpacity,\n                shadow: particle.options.shadow,\n                transform,\n            });\n            this._applyPostDrawUpdaters(particle);\n        });\n    }\n    drawParticlePlugin(plugin, particle, delta) {\n        this.draw(ctx => drawParticlePlugin(ctx, plugin, particle, delta));\n    }\n    drawPlugin(plugin, delta) {\n        this.draw(ctx => drawPlugin(ctx, plugin, delta));\n    }\n    async init() {\n        this._safeMutationObserver(obs => obs.disconnect());\n        this._mutationObserver = safeMutationObserver(records => {\n            for (const record of records) {\n                if (record.type === \"attributes\" && record.attributeName === \"style\") {\n                    this._repairStyle();\n                }\n            }\n        });\n        this.resize();\n        this._initStyle();\n        await this._initCover();\n        try {\n            await this._initTrail();\n        }\n        catch (e) {\n            getLogger().error(e);\n        }\n        this.initBackground();\n        this._safeMutationObserver(obs => {\n            if (!this.element || !(this.element instanceof Node)) {\n                return;\n            }\n            obs.observe(this.element, { attributes: true });\n        });\n        this.initUpdaters();\n        this.initPlugins();\n        this.paint();\n    }\n    initBackground() {\n        const options = this.container.actualOptions, background = options.background, element = this.element;\n        if (!element) {\n            return;\n        }\n        const elementStyle = element.style;\n        if (!elementStyle) {\n            return;\n        }\n        if (background.color) {\n            const color = rangeColorToRgb(background.color);\n            elementStyle.backgroundColor = color ? getStyleFromRgb(color, background.opacity) : \"\";\n        }\n        else {\n            elementStyle.backgroundColor = \"\";\n        }\n        elementStyle.backgroundImage = background.image || \"\";\n        elementStyle.backgroundPosition = background.position || \"\";\n        elementStyle.backgroundRepeat = background.repeat || \"\";\n        elementStyle.backgroundSize = background.size || \"\";\n    }\n    initPlugins() {\n        this._resizePlugins = [];\n        for (const [, plugin] of this.container.plugins) {\n            if (plugin.resize) {\n                this._resizePlugins.push(plugin);\n            }\n            if (plugin.particleFillColor ?? plugin.particleStrokeColor) {\n                this._colorPlugins.push(plugin);\n            }\n        }\n    }\n    initUpdaters() {\n        this._preDrawUpdaters = [];\n        this._postDrawUpdaters = [];\n        for (const updater of this.container.particles.updaters) {\n            if (updater.afterDraw) {\n                this._postDrawUpdaters.push(updater);\n            }\n            if (updater.getColorStyles ?? updater.getTransformValues ?? updater.beforeDraw) {\n                this._preDrawUpdaters.push(updater);\n            }\n        }\n    }\n    loadCanvas(canvas) {\n        if (this._generated && this.element) {\n            this.element.remove();\n        }\n        this._generated =\n            canvas.dataset && generatedAttribute in canvas.dataset\n                ? canvas.dataset[generatedAttribute] === \"true\"\n                : this._generated;\n        this.element = canvas;\n        this.element.ariaHidden = \"true\";\n        this._originalStyle = deepExtend({}, this.element.style);\n        this.size.height = canvas.offsetHeight;\n        this.size.width = canvas.offsetWidth;\n        this._context = this.element.getContext(\"2d\");\n        this._safeMutationObserver(obs => {\n            if (!this.element || !(this.element instanceof Node)) {\n                return;\n            }\n            obs.observe(this.element, { attributes: true });\n        });\n        this.container.retina.init();\n        this.initBackground();\n    }\n    paint() {\n        const options = this.container.actualOptions;\n        this.draw(ctx => {\n            if (options.backgroundMask.enable && options.backgroundMask.cover) {\n                clear(ctx, this.size);\n                if (this._coverImage) {\n                    this._paintImage(this._coverImage.image, this._coverImage.opacity);\n                }\n                else if (this._coverColorStyle) {\n                    this._paintBase(this._coverColorStyle);\n                }\n                else {\n                    this._paintBase();\n                }\n            }\n            else {\n                this._paintBase();\n            }\n        });\n    }\n    resize() {\n        if (!this.element) {\n            return false;\n        }\n        const container = this.container, pxRatio = container.retina.pixelRatio, size = container.canvas.size, newSize = {\n            width: this.element.offsetWidth * pxRatio,\n            height: this.element.offsetHeight * pxRatio,\n        };\n        if (newSize.height === size.height &&\n            newSize.width === size.width &&\n            newSize.height === this.element.height &&\n            newSize.width === this.element.width) {\n            return false;\n        }\n        const oldSize = { ...size };\n        this.element.width = size.width = this.element.offsetWidth * pxRatio;\n        this.element.height = size.height = this.element.offsetHeight * pxRatio;\n        if (this.container.started) {\n            container.particles.setResizeFactor({\n                width: size.width / oldSize.width,\n                height: size.height / oldSize.height,\n            });\n        }\n        return true;\n    }\n    stop() {\n        this._safeMutationObserver(obs => obs.disconnect());\n        this._mutationObserver = undefined;\n        this.draw(ctx => clear(ctx, this.size));\n    }\n    async windowResize() {\n        if (!this.element || !this.resize()) {\n            return;\n        }\n        const container = this.container, needsRefresh = container.updateActualOptions();\n        container.particles.setDensity();\n        this._applyResizePlugins();\n        if (needsRefresh) {\n            await container.refresh();\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,KAAK,EAAEC,YAAY,EAAEC,kBAAkB,EAAEC,UAAU,EAAEC,SAAS,EAAEC,UAAU,QAAQ,yBAAyB;AACpH,SAASC,UAAU,EAAEC,SAAS,EAAEC,oBAAoB,QAAQ,mBAAmB;AAC/E,SAASC,eAAe,EAAEC,eAAe,EAAEC,eAAe,EAAEC,eAAe,QAAQ,wBAAwB;AAC3G,SAASC,kBAAkB,QAAQ,sBAAsB;AACzD,SAASC,iBAAiBA,CAACC,MAAM,EAAEC,SAAS,EAAEC,GAAG,EAAE;EAC/C,MAAMC,QAAQ,GAAGF,SAAS,CAACC,GAAG,CAAC;IAAEE,YAAY,GAAG,CAAC;EACjD,IAAID,QAAQ,KAAKE,SAAS,EAAE;IACxBL,MAAM,CAACE,GAAG,CAAC,GAAG,CAACF,MAAM,CAACE,GAAG,CAAC,IAAIE,YAAY,IAAID,QAAQ;EAC1D;AACJ;AACA,SAASG,QAAQA,CAACC,MAAM,EAAEC,KAAK,EAAEC,SAAS,GAAG,KAAK,EAAE;EAChD,IAAI,CAACD,KAAK,EAAE;IACR;EACJ;EACA,MAAME,OAAO,GAAGH,MAAM;EACtB,IAAI,CAACG,OAAO,EAAE;IACV;EACJ;EACA,MAAMC,YAAY,GAAGD,OAAO,CAACF,KAAK;EAClC,IAAI,CAACG,YAAY,EAAE;IACf;EACJ;EACA,KAAK,MAAMT,GAAG,IAAIM,KAAK,EAAE;IACrB,MAAMI,KAAK,GAAGJ,KAAK,CAACN,GAAG,CAAC;IACxB,IAAI,CAACU,KAAK,EAAE;MACR;IACJ;IACAD,YAAY,CAACE,WAAW,CAACX,GAAG,EAAEU,KAAK,EAAEH,SAAS,GAAG,WAAW,GAAG,EAAE,CAAC;EACtE;AACJ;AACA,OAAO,MAAMK,MAAM,CAAC;EAChBC,WAAWA,CAACC,SAAS,EAAE;IACnB,IAAI,CAACA,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,sBAAsB,GAAGC,QAAQ,IAAI;MACtC,KAAK,MAAMC,OAAO,IAAI,IAAI,CAACC,iBAAiB,EAAE;QAC1CD,OAAO,CAACE,SAAS,GAAGH,QAAQ,CAAC;MACjC;IACJ,CAAC;IACD,IAAI,CAACI,qBAAqB,GAAG,CAACC,GAAG,EAAEL,QAAQ,EAAEM,MAAM,EAAEC,QAAQ,EAAEC,WAAW,EAAEC,SAAS,KAAK;MACtF,KAAK,MAAMR,OAAO,IAAI,IAAI,CAACS,gBAAgB,EAAE;QACzC,IAAIT,OAAO,CAACU,cAAc,EAAE;UACxB,MAAM;YAAEC,IAAI;YAAEC;UAAO,CAAC,GAAGZ,OAAO,CAACU,cAAc,CAACX,QAAQ,EAAEK,GAAG,EAAEC,MAAM,EAAEC,QAAQ,CAAC;UAChF,IAAIK,IAAI,EAAE;YACNJ,WAAW,CAACI,IAAI,GAAGA,IAAI;UAC3B;UACA,IAAIC,MAAM,EAAE;YACRL,WAAW,CAACK,MAAM,GAAGA,MAAM;UAC/B;QACJ;QACA,IAAIZ,OAAO,CAACa,kBAAkB,EAAE;UAC5B,MAAMC,gBAAgB,GAAGd,OAAO,CAACa,kBAAkB,CAACd,QAAQ,CAAC;UAC7D,KAAK,MAAMhB,GAAG,IAAI+B,gBAAgB,EAAE;YAChClC,iBAAiB,CAAC4B,SAAS,EAAEM,gBAAgB,EAAE/B,GAAG,CAAC;UACvD;QACJ;QACAiB,OAAO,CAACe,UAAU,GAAGhB,QAAQ,CAAC;MAClC;IACJ,CAAC;IACD,IAAI,CAACiB,mBAAmB,GAAG,MAAM;MAC7B,KAAK,MAAMC,MAAM,IAAI,IAAI,CAACC,cAAc,EAAE;QACtCD,MAAM,CAACE,MAAM,GAAG,CAAC;MACrB;IACJ,CAAC;IACD,IAAI,CAACC,wBAAwB,GAAGrB,QAAQ,IAAI;MACxC,IAAIsB,MAAM,EAAEC,MAAM;MAClB,KAAK,MAAML,MAAM,IAAI,IAAI,CAACM,aAAa,EAAE;QACrC,IAAI,CAACF,MAAM,IAAIJ,MAAM,CAACO,iBAAiB,EAAE;UACrCH,MAAM,GAAG5C,eAAe,CAACwC,MAAM,CAACO,iBAAiB,CAACzB,QAAQ,CAAC,CAAC;QAChE;QACA,IAAI,CAACuB,MAAM,IAAIL,MAAM,CAACQ,mBAAmB,EAAE;UACvCH,MAAM,GAAG7C,eAAe,CAACwC,MAAM,CAACQ,mBAAmB,CAAC1B,QAAQ,CAAC,CAAC;QAClE;QACA,IAAIsB,MAAM,IAAIC,MAAM,EAAE;UAClB;QACJ;MACJ;MACA,OAAO,CAACD,MAAM,EAAEC,MAAM,CAAC;IAC3B,CAAC;IACD,IAAI,CAACI,UAAU,GAAG,YAAY;MAC1B,MAAMC,OAAO,GAAG,IAAI,CAAC9B,SAAS,CAAC+B,aAAa;QAAEC,KAAK,GAAGF,OAAO,CAACG,cAAc,CAACD,KAAK;QAAEE,KAAK,GAAGF,KAAK,CAACE,KAAK;MACvG,IAAIA,KAAK,EAAE;QACP,MAAMC,QAAQ,GAAGtD,eAAe,CAACqD,KAAK,CAAC;QACvC,IAAIC,QAAQ,EAAE;UACV,MAAMC,UAAU,GAAG;YACf,GAAGD,QAAQ;YACXE,CAAC,EAAEL,KAAK,CAACM;UACb,CAAC;UACD,IAAI,CAACC,gBAAgB,GAAG5D,eAAe,CAACyD,UAAU,EAAEA,UAAU,CAACC,CAAC,CAAC;QACrE;MACJ,CAAC,MACI;QACD,MAAM,IAAIG,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;UACnC,IAAI,CAACV,KAAK,CAACW,KAAK,EAAE;YACd;UACJ;UACA,MAAMC,GAAG,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;UACzCF,GAAG,CAACG,gBAAgB,CAAC,MAAM,EAAE,MAAM;YAC/B,IAAI,CAACC,WAAW,GAAG;cACfL,KAAK,EAAEC,GAAG;cACVN,OAAO,EAAEN,KAAK,CAACM;YACnB,CAAC;YACDG,OAAO,CAAC,CAAC;UACb,CAAC,CAAC;UACFG,GAAG,CAACG,gBAAgB,CAAC,OAAO,EAAEE,GAAG,IAAI;YACjCP,MAAM,CAACO,GAAG,CAACC,KAAK,CAAC;UACrB,CAAC,CAAC;UACFN,GAAG,CAACO,GAAG,GAAGnB,KAAK,CAACW,KAAK;QACzB,CAAC,CAAC;MACN;IACJ,CAAC;IACD,IAAI,CAACS,UAAU,GAAG,MAAM;MACpB,MAAM1D,OAAO,GAAG,IAAI,CAACA,OAAO;QAAEoC,OAAO,GAAG,IAAI,CAAC9B,SAAS,CAAC+B,aAAa;MACpE,IAAI,CAACrC,OAAO,EAAE;QACV;MACJ;MACA,IAAI,IAAI,CAAC2D,WAAW,EAAE;QAClB,IAAI,CAACC,cAAc,GAAG/E,UAAU,CAAC,CAAC,CAAC,EAAEmB,OAAO,CAACF,KAAK,CAAC;QACnD,IAAI,CAAC+D,mBAAmB,CAAC,CAAC;MAC9B,CAAC,MACI;QACD,IAAI,CAACC,mBAAmB,CAAC,CAAC;MAC9B;MACA,KAAK,MAAMtE,GAAG,IAAI4C,OAAO,CAACtC,KAAK,EAAE;QAC7B,IAAI,CAACN,GAAG,IAAI,CAAC4C,OAAO,CAACtC,KAAK,EAAE;UACxB;QACJ;QACA,MAAMI,KAAK,GAAGkC,OAAO,CAACtC,KAAK,CAACN,GAAG,CAAC;QAChC,IAAI,CAACU,KAAK,EAAE;UACR;QACJ;QACAF,OAAO,CAACF,KAAK,CAACK,WAAW,CAACX,GAAG,EAAEU,KAAK,EAAE,WAAW,CAAC;MACtD;IACJ,CAAC;IACD,IAAI,CAAC6D,UAAU,GAAG,YAAY;MAC1B,MAAM3B,OAAO,GAAG,IAAI,CAAC9B,SAAS,CAAC+B,aAAa;QAAE2B,KAAK,GAAG5B,OAAO,CAAC6B,SAAS,CAACC,IAAI,CAACF,KAAK;QAAEG,SAAS,GAAGH,KAAK,CAAC5C,IAAI;MAC1G,IAAI,CAAC4C,KAAK,CAACI,MAAM,EAAE;QACf;MACJ;MACA,MAAMC,eAAe,GAAG,CAAC;QAAEzB,OAAO,GAAGyB,eAAe,GAAGL,KAAK,CAACM,MAAM;MACnE,IAAIH,SAAS,CAAC3B,KAAK,EAAE;QACjB,MAAM+B,SAAS,GAAGpF,eAAe,CAACgF,SAAS,CAAC3B,KAAK,CAAC;QAClD,IAAI,CAAC+B,SAAS,EAAE;UACZ;QACJ;QACA,IAAI,CAACC,UAAU,GAAG;UACdhC,KAAK,EAAE;YACH,GAAG+B;UACP,CAAC;UACD3B;QACJ,CAAC;MACL,CAAC,MACI;QACD,MAAM,IAAIE,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;UACnC,IAAI,CAACmB,SAAS,CAAClB,KAAK,EAAE;YAClB;UACJ;UACA,MAAMC,GAAG,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;UACzCF,GAAG,CAACG,gBAAgB,CAAC,MAAM,EAAE,MAAM;YAC/B,IAAI,CAACmB,UAAU,GAAG;cACdvB,KAAK,EAAEC,GAAG;cACVN;YACJ,CAAC;YACDG,OAAO,CAAC,CAAC;UACb,CAAC,CAAC;UACFG,GAAG,CAACG,gBAAgB,CAAC,OAAO,EAAEE,GAAG,IAAI;YACjCP,MAAM,CAACO,GAAG,CAACC,KAAK,CAAC;UACrB,CAAC,CAAC;UACFN,GAAG,CAACO,GAAG,GAAGU,SAAS,CAAClB,KAAK;QAC7B,CAAC,CAAC;MACN;IACJ,CAAC;IACD,IAAI,CAACwB,UAAU,GAAGC,SAAS,IAAI;MAC3B,IAAI,CAACC,IAAI,CAAC9D,GAAG,IAAIlC,SAAS,CAACkC,GAAG,EAAE,IAAI,CAAC+D,IAAI,EAAEF,SAAS,CAAC,CAAC;IAC1D,CAAC;IACD,IAAI,CAACG,WAAW,GAAG,CAAC5B,KAAK,EAAEL,OAAO,KAAK;MACnC,IAAI,CAAC+B,IAAI,CAAC9D,GAAG,IAAIjC,UAAU,CAACiC,GAAG,EAAE,IAAI,CAAC+D,IAAI,EAAE3B,KAAK,EAAEL,OAAO,CAAC,CAAC;IAChE,CAAC;IACD,IAAI,CAACkC,YAAY,GAAG,MAAM;MACtB,MAAM9E,OAAO,GAAG,IAAI,CAACA,OAAO;MAC5B,IAAI,CAACA,OAAO,EAAE;QACV;MACJ;MACA,IAAI,CAAC+E,qBAAqB,CAACC,QAAQ,IAAIA,QAAQ,CAACC,UAAU,CAAC,CAAC,CAAC;MAC7D,IAAI,CAACvB,UAAU,CAAC,CAAC;MACjB,IAAI,CAACwB,cAAc,CAAC,CAAC;MACrB,IAAI,CAACH,qBAAqB,CAACC,QAAQ,IAAI;QACnC,IAAI,CAAChF,OAAO,IAAI,EAAEA,OAAO,YAAYmF,IAAI,CAAC,EAAE;UACxC;QACJ;QACAH,QAAQ,CAACI,OAAO,CAACpF,OAAO,EAAE;UAAEqF,UAAU,EAAE;QAAK,CAAC,CAAC;MACnD,CAAC,CAAC;IACN,CAAC;IACD,IAAI,CAACvB,mBAAmB,GAAG,MAAM;MAC7B,MAAM9D,OAAO,GAAG,IAAI,CAACA,OAAO;QAAEsF,aAAa,GAAG,IAAI,CAAC1B,cAAc;MACjE,IAAI,EAAE5D,OAAO,IAAIsF,aAAa,CAAC,EAAE;QAC7B;MACJ;MACA1F,QAAQ,CAACI,OAAO,EAAEsF,aAAa,CAAC;IACpC,CAAC;IACD,IAAI,CAACP,qBAAqB,GAAGQ,QAAQ,IAAI;MACrC,IAAI,CAAC,IAAI,CAACC,iBAAiB,EAAE;QACzB;MACJ;MACAD,QAAQ,CAAC,IAAI,CAACC,iBAAiB,CAAC;IACpC,CAAC;IACD,IAAI,CAAC3B,mBAAmB,GAAG,MAAM;MAC7B,MAAM7D,OAAO,GAAG,IAAI,CAACA,OAAO;MAC5B,IAAI,CAACA,OAAO,EAAE;QACV;MACJ;MACA,MAAMyF,KAAK,GAAG,EAAE;MAChB7F,QAAQ,CAACI,OAAO,EAAE;QACd0F,QAAQ,EAAE,OAAO;QACjBC,MAAM,EAAE,IAAI,CAACrF,SAAS,CAAC+B,aAAa,CAACuD,UAAU,CAACD,MAAM,CAACE,QAAQ,CAACJ,KAAK,CAAC;QACtEK,GAAG,EAAE,GAAG;QACRC,IAAI,EAAE,GAAG;QACTC,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE;MACZ,CAAC,EAAE,IAAI,CAAC;IACZ,CAAC;IACD,IAAI,CAACrB,IAAI,GAAG;MACRqB,MAAM,EAAE,CAAC;MACTD,KAAK,EAAE;IACX,CAAC;IACD,IAAI,CAACE,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,UAAU,GAAG,KAAK;IACvB,IAAI,CAACjF,gBAAgB,GAAG,EAAE;IAC1B,IAAI,CAACR,iBAAiB,GAAG,EAAE;IAC3B,IAAI,CAACiB,cAAc,GAAG,EAAE;IACxB,IAAI,CAACK,aAAa,GAAG,EAAE;EAC3B;EACA,IAAI2B,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACrD,SAAS,CAAC+B,aAAa,CAACuD,UAAU,CAACxB,MAAM;EACzD;EACA7F,KAAKA,CAAA,EAAG;IACJ,MAAM6D,OAAO,GAAG,IAAI,CAAC9B,SAAS,CAAC+B,aAAa;MAAE2B,KAAK,GAAG5B,OAAO,CAAC6B,SAAS,CAACC,IAAI,CAACF,KAAK;MAAEG,SAAS,GAAG,IAAI,CAACK,UAAU;MAAE4B,aAAa,GAAG,CAAC;IAClI,IAAIhE,OAAO,CAACG,cAAc,CAAC6B,MAAM,EAAE;MAC/B,IAAI,CAACiC,KAAK,CAAC,CAAC;IAChB,CAAC,MACI,IAAIrC,KAAK,CAACI,MAAM,IAAIJ,KAAK,CAACM,MAAM,GAAG8B,aAAa,IAAIjC,SAAS,EAAE;MAChE,IAAIA,SAAS,CAAC3B,KAAK,EAAE;QACjB,IAAI,CAACiC,UAAU,CAACxF,eAAe,CAACkF,SAAS,CAAC3B,KAAK,EAAE2B,SAAS,CAACvB,OAAO,CAAC,CAAC;MACxE,CAAC,MACI,IAAIuB,SAAS,CAAClB,KAAK,EAAE;QACtB,IAAI,CAAC4B,WAAW,CAACV,SAAS,CAAClB,KAAK,EAAEkB,SAAS,CAACvB,OAAO,CAAC;MACxD;IACJ,CAAC,MACI,IAAIR,OAAO,CAAC7D,KAAK,EAAE;MACpB,IAAI,CAACoG,IAAI,CAAC9D,GAAG,IAAI;QACbtC,KAAK,CAACsC,GAAG,EAAE,IAAI,CAAC+D,IAAI,CAAC;MACzB,CAAC,CAAC;IACN;EACJ;EACA0B,OAAOA,CAAA,EAAG;IACN,IAAI,CAACC,IAAI,CAAC,CAAC;IACX,IAAI,IAAI,CAACJ,UAAU,EAAE;MACjB,MAAMnG,OAAO,GAAG,IAAI,CAACA,OAAO;MAC5BA,OAAO,EAAEwG,MAAM,CAAC,CAAC;IACrB,CAAC,MACI;MACD,IAAI,CAAC1C,mBAAmB,CAAC,CAAC;IAC9B;IACA,IAAI,CAAC5C,gBAAgB,GAAG,EAAE;IAC1B,IAAI,CAACR,iBAAiB,GAAG,EAAE;IAC3B,IAAI,CAACiB,cAAc,GAAG,EAAE;IACxB,IAAI,CAACK,aAAa,GAAG,EAAE;EAC3B;EACA2C,IAAIA,CAAC8B,EAAE,EAAE;IACL,MAAM5F,GAAG,GAAG,IAAI,CAACqF,QAAQ;IACzB,IAAI,CAACrF,GAAG,EAAE;MACN;IACJ;IACA,OAAO4F,EAAE,CAAC5F,GAAG,CAAC;EAClB;EACA6F,SAASA,CAACD,EAAE,EAAE;IACV,MAAM5F,GAAG,GAAG,IAAI,CAACqF,QAAQ;IACzB,IAAI,CAACrF,GAAG,EAAE;MACN,OAAOlB,SAAS;IACpB;IACA,OAAO8G,EAAE,CAAC5F,GAAG,CAAC;EAClB;EACArC,YAAYA,CAACgC,QAAQ,EAAEmG,KAAK,EAAE;IAC1B,IAAInG,QAAQ,CAACoG,QAAQ,IAAIpG,QAAQ,CAACqG,SAAS,EAAE;MACzC;IACJ;IACA,MAAM/F,MAAM,GAAGN,QAAQ,CAACsG,SAAS,CAAC,CAAC;MAAEC,WAAW,GAAG,CAAC;IACpD,IAAIjG,MAAM,IAAIiG,WAAW,EAAE;MACvB;IACJ;IACA,MAAMC,OAAO,GAAGxG,QAAQ,CAACyG,YAAY,CAAC,CAAC;MAAEC,OAAO,GAAG1G,QAAQ,CAAC2G,cAAc,CAAC,CAAC,IAAIH,OAAO;IACvF,IAAI,CAAClF,MAAM,EAAEC,MAAM,CAAC,GAAG,IAAI,CAACF,wBAAwB,CAACrB,QAAQ,CAAC;IAC9D,IAAI,CAACsB,MAAM,EAAE;MACTA,MAAM,GAAGkF,OAAO;IACpB;IACA,IAAI,CAACjF,MAAM,EAAE;MACTA,MAAM,GAAGmF,OAAO;IACpB;IACA,IAAI,CAACpF,MAAM,IAAI,CAACC,MAAM,EAAE;MACpB;IACJ;IACA,IAAI,CAAC4C,IAAI,CAAE9D,GAAG,IAAK;MACf,MAAMP,SAAS,GAAG,IAAI,CAACA,SAAS;QAAE8B,OAAO,GAAG9B,SAAS,CAAC+B,aAAa;QAAE+E,aAAa,GAAG5G,QAAQ,CAAC4B,OAAO,CAACuD,MAAM;QAAE0B,kBAAkB,GAAG,CAAC;QAAEC,YAAY,GAAGD,kBAAkB,GAAG7G,QAAQ,CAAC8G,YAAY;QAAEC,cAAc,GAAGD,YAAY,IAAIF,aAAa,CAACI,WAAW;QAAEC,cAAc,GAAG,CAAC;QAAE7E,OAAO,GAAGpC,QAAQ,CAACkH,MAAM,CAAC9E,OAAO,IAAIpC,QAAQ,CAACoC,OAAO,EAAE1C,KAAK,IAAIuH,cAAc;QAAEE,aAAa,GAAGnH,QAAQ,CAACmH,aAAa,IAAI/E,OAAO;QAAE7B,QAAQ,GAAG6B,OAAO,GAAG2E,cAAc;QAAEK,cAAc,GAAGD,aAAa,GAAGJ,cAAc;QAAEtG,SAAS,GAAG,CAAC,CAAC;QAAED,WAAW,GAAG;UACpgBI,IAAI,EAAEU,MAAM,GAAG9C,eAAe,CAAC8C,MAAM,EAAEf,QAAQ,CAAC,GAAGpB;QACvD,CAAC;MACDqB,WAAW,CAACK,MAAM,GAAGU,MAAM,GAAG/C,eAAe,CAAC+C,MAAM,EAAE6F,cAAc,CAAC,GAAG5G,WAAW,CAACI,IAAI;MACxF,IAAI,CAACR,qBAAqB,CAACC,GAAG,EAAEL,QAAQ,EAAEM,MAAM,EAAEC,QAAQ,EAAEC,WAAW,EAAEC,SAAS,CAAC;MACnFzC,YAAY,CAAC;QACT8B,SAAS;QACTuH,OAAO,EAAEhH,GAAG;QACZL,QAAQ;QACRmG,KAAK;QACL3F,WAAW;QACXuB,cAAc,EAAEH,OAAO,CAACG,cAAc,CAAC6B,MAAM;QAC7C0D,SAAS,EAAE1F,OAAO,CAACG,cAAc,CAACuF,SAAS;QAC3ChH,MAAM,EAAEA,MAAM,GAAGwG,YAAY,IAAIF,aAAa,CAACW,QAAQ;QACvDnF,OAAO,EAAE7B,QAAQ;QACjBiH,MAAM,EAAExH,QAAQ,CAAC4B,OAAO,CAAC4F,MAAM;QAC/B/G;MACJ,CAAC,CAAC;MACF,IAAI,CAACV,sBAAsB,CAACC,QAAQ,CAAC;IACzC,CAAC,CAAC;EACN;EACA/B,kBAAkBA,CAACiD,MAAM,EAAElB,QAAQ,EAAEmG,KAAK,EAAE;IACxC,IAAI,CAAChC,IAAI,CAAC9D,GAAG,IAAIpC,kBAAkB,CAACoC,GAAG,EAAEa,MAAM,EAAElB,QAAQ,EAAEmG,KAAK,CAAC,CAAC;EACtE;EACAjI,UAAUA,CAACgD,MAAM,EAAEiF,KAAK,EAAE;IACtB,IAAI,CAAChC,IAAI,CAAC9D,GAAG,IAAInC,UAAU,CAACmC,GAAG,EAAEa,MAAM,EAAEiF,KAAK,CAAC,CAAC;EACpD;EACA,MAAMsB,IAAIA,CAAA,EAAG;IACT,IAAI,CAAClD,qBAAqB,CAACmD,GAAG,IAAIA,GAAG,CAACjD,UAAU,CAAC,CAAC,CAAC;IACnD,IAAI,CAACO,iBAAiB,GAAGzG,oBAAoB,CAACoJ,OAAO,IAAI;MACrD,KAAK,MAAMC,MAAM,IAAID,OAAO,EAAE;QAC1B,IAAIC,MAAM,CAACC,IAAI,KAAK,YAAY,IAAID,MAAM,CAACE,aAAa,KAAK,OAAO,EAAE;UAClE,IAAI,CAACxD,YAAY,CAAC,CAAC;QACvB;MACJ;IACJ,CAAC,CAAC;IACF,IAAI,CAAClD,MAAM,CAAC,CAAC;IACb,IAAI,CAAC8B,UAAU,CAAC,CAAC;IACjB,MAAM,IAAI,CAACvB,UAAU,CAAC,CAAC;IACvB,IAAI;MACA,MAAM,IAAI,CAAC4B,UAAU,CAAC,CAAC;IAC3B,CAAC,CACD,OAAOwE,CAAC,EAAE;MACNzJ,SAAS,CAAC,CAAC,CAAC0E,KAAK,CAAC+E,CAAC,CAAC;IACxB;IACA,IAAI,CAACrD,cAAc,CAAC,CAAC;IACrB,IAAI,CAACH,qBAAqB,CAACmD,GAAG,IAAI;MAC9B,IAAI,CAAC,IAAI,CAAClI,OAAO,IAAI,EAAE,IAAI,CAACA,OAAO,YAAYmF,IAAI,CAAC,EAAE;QAClD;MACJ;MACA+C,GAAG,CAAC9C,OAAO,CAAC,IAAI,CAACpF,OAAO,EAAE;QAAEqF,UAAU,EAAE;MAAK,CAAC,CAAC;IACnD,CAAC,CAAC;IACF,IAAI,CAACmD,YAAY,CAAC,CAAC;IACnB,IAAI,CAACC,WAAW,CAAC,CAAC;IAClB,IAAI,CAACpC,KAAK,CAAC,CAAC;EAChB;EACAnB,cAAcA,CAAA,EAAG;IACb,MAAM9C,OAAO,GAAG,IAAI,CAAC9B,SAAS,CAAC+B,aAAa;MAAEqG,UAAU,GAAGtG,OAAO,CAACsG,UAAU;MAAE1I,OAAO,GAAG,IAAI,CAACA,OAAO;IACrG,IAAI,CAACA,OAAO,EAAE;MACV;IACJ;IACA,MAAMC,YAAY,GAAGD,OAAO,CAACF,KAAK;IAClC,IAAI,CAACG,YAAY,EAAE;MACf;IACJ;IACA,IAAIyI,UAAU,CAAClG,KAAK,EAAE;MAClB,MAAMA,KAAK,GAAGrD,eAAe,CAACuJ,UAAU,CAAClG,KAAK,CAAC;MAC/CvC,YAAY,CAAC0I,eAAe,GAAGnG,KAAK,GAAGvD,eAAe,CAACuD,KAAK,EAAEkG,UAAU,CAAC9F,OAAO,CAAC,GAAG,EAAE;IAC1F,CAAC,MACI;MACD3C,YAAY,CAAC0I,eAAe,GAAG,EAAE;IACrC;IACA1I,YAAY,CAAC2I,eAAe,GAAGF,UAAU,CAACzF,KAAK,IAAI,EAAE;IACrDhD,YAAY,CAAC4I,kBAAkB,GAAGH,UAAU,CAAChD,QAAQ,IAAI,EAAE;IAC3DzF,YAAY,CAAC6I,gBAAgB,GAAGJ,UAAU,CAACK,MAAM,IAAI,EAAE;IACvD9I,YAAY,CAAC+I,cAAc,GAAGN,UAAU,CAAC9D,IAAI,IAAI,EAAE;EACvD;EACA6D,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC9G,cAAc,GAAG,EAAE;IACxB,KAAK,MAAM,GAAGD,MAAM,CAAC,IAAI,IAAI,CAACpB,SAAS,CAAC2I,OAAO,EAAE;MAC7C,IAAIvH,MAAM,CAACE,MAAM,EAAE;QACf,IAAI,CAACD,cAAc,CAACuH,IAAI,CAACxH,MAAM,CAAC;MACpC;MACA,IAAIA,MAAM,CAACO,iBAAiB,IAAIP,MAAM,CAACQ,mBAAmB,EAAE;QACxD,IAAI,CAACF,aAAa,CAACkH,IAAI,CAACxH,MAAM,CAAC;MACnC;IACJ;EACJ;EACA8G,YAAYA,CAAA,EAAG;IACX,IAAI,CAACtH,gBAAgB,GAAG,EAAE;IAC1B,IAAI,CAACR,iBAAiB,GAAG,EAAE;IAC3B,KAAK,MAAMD,OAAO,IAAI,IAAI,CAACH,SAAS,CAAC2D,SAAS,CAACkF,QAAQ,EAAE;MACrD,IAAI1I,OAAO,CAACE,SAAS,EAAE;QACnB,IAAI,CAACD,iBAAiB,CAACwI,IAAI,CAACzI,OAAO,CAAC;MACxC;MACA,IAAIA,OAAO,CAACU,cAAc,IAAIV,OAAO,CAACa,kBAAkB,IAAIb,OAAO,CAACe,UAAU,EAAE;QAC5E,IAAI,CAACN,gBAAgB,CAACgI,IAAI,CAACzI,OAAO,CAAC;MACvC;IACJ;EACJ;EACA2I,UAAUA,CAACvJ,MAAM,EAAE;IACf,IAAI,IAAI,CAACsG,UAAU,IAAI,IAAI,CAACnG,OAAO,EAAE;MACjC,IAAI,CAACA,OAAO,CAACwG,MAAM,CAAC,CAAC;IACzB;IACA,IAAI,CAACL,UAAU,GACXtG,MAAM,CAACwJ,OAAO,IAAIjK,kBAAkB,IAAIS,MAAM,CAACwJ,OAAO,GAChDxJ,MAAM,CAACwJ,OAAO,CAACjK,kBAAkB,CAAC,KAAK,MAAM,GAC7C,IAAI,CAAC+G,UAAU;IACzB,IAAI,CAACnG,OAAO,GAAGH,MAAM;IACrB,IAAI,CAACG,OAAO,CAACsJ,UAAU,GAAG,MAAM;IAChC,IAAI,CAAC1F,cAAc,GAAG/E,UAAU,CAAC,CAAC,CAAC,EAAE,IAAI,CAACmB,OAAO,CAACF,KAAK,CAAC;IACxD,IAAI,CAAC8E,IAAI,CAACqB,MAAM,GAAGpG,MAAM,CAAC0J,YAAY;IACtC,IAAI,CAAC3E,IAAI,CAACoB,KAAK,GAAGnG,MAAM,CAAC2J,WAAW;IACpC,IAAI,CAACtD,QAAQ,GAAG,IAAI,CAAClG,OAAO,CAACyJ,UAAU,CAAC,IAAI,CAAC;IAC7C,IAAI,CAAC1E,qBAAqB,CAACmD,GAAG,IAAI;MAC9B,IAAI,CAAC,IAAI,CAAClI,OAAO,IAAI,EAAE,IAAI,CAACA,OAAO,YAAYmF,IAAI,CAAC,EAAE;QAClD;MACJ;MACA+C,GAAG,CAAC9C,OAAO,CAAC,IAAI,CAACpF,OAAO,EAAE;QAAEqF,UAAU,EAAE;MAAK,CAAC,CAAC;IACnD,CAAC,CAAC;IACF,IAAI,CAAC/E,SAAS,CAACoJ,MAAM,CAACzB,IAAI,CAAC,CAAC;IAC5B,IAAI,CAAC/C,cAAc,CAAC,CAAC;EACzB;EACAmB,KAAKA,CAAA,EAAG;IACJ,MAAMjE,OAAO,GAAG,IAAI,CAAC9B,SAAS,CAAC+B,aAAa;IAC5C,IAAI,CAACsC,IAAI,CAAC9D,GAAG,IAAI;MACb,IAAIuB,OAAO,CAACG,cAAc,CAAC6B,MAAM,IAAIhC,OAAO,CAACG,cAAc,CAACD,KAAK,EAAE;QAC/D/D,KAAK,CAACsC,GAAG,EAAE,IAAI,CAAC+D,IAAI,CAAC;QACrB,IAAI,IAAI,CAACtB,WAAW,EAAE;UAClB,IAAI,CAACuB,WAAW,CAAC,IAAI,CAACvB,WAAW,CAACL,KAAK,EAAE,IAAI,CAACK,WAAW,CAACV,OAAO,CAAC;QACtE,CAAC,MACI,IAAI,IAAI,CAACC,gBAAgB,EAAE;UAC5B,IAAI,CAAC4B,UAAU,CAAC,IAAI,CAAC5B,gBAAgB,CAAC;QAC1C,CAAC,MACI;UACD,IAAI,CAAC4B,UAAU,CAAC,CAAC;QACrB;MACJ,CAAC,MACI;QACD,IAAI,CAACA,UAAU,CAAC,CAAC;MACrB;IACJ,CAAC,CAAC;EACN;EACA7C,MAAMA,CAAA,EAAG;IACL,IAAI,CAAC,IAAI,CAAC5B,OAAO,EAAE;MACf,OAAO,KAAK;IAChB;IACA,MAAMM,SAAS,GAAG,IAAI,CAACA,SAAS;MAAEqJ,OAAO,GAAGrJ,SAAS,CAACoJ,MAAM,CAACE,UAAU;MAAEhF,IAAI,GAAGtE,SAAS,CAACT,MAAM,CAAC+E,IAAI;MAAEiF,OAAO,GAAG;QAC7G7D,KAAK,EAAE,IAAI,CAAChG,OAAO,CAACwJ,WAAW,GAAGG,OAAO;QACzC1D,MAAM,EAAE,IAAI,CAACjG,OAAO,CAACuJ,YAAY,GAAGI;MACxC,CAAC;IACD,IAAIE,OAAO,CAAC5D,MAAM,KAAKrB,IAAI,CAACqB,MAAM,IAC9B4D,OAAO,CAAC7D,KAAK,KAAKpB,IAAI,CAACoB,KAAK,IAC5B6D,OAAO,CAAC5D,MAAM,KAAK,IAAI,CAACjG,OAAO,CAACiG,MAAM,IACtC4D,OAAO,CAAC7D,KAAK,KAAK,IAAI,CAAChG,OAAO,CAACgG,KAAK,EAAE;MACtC,OAAO,KAAK;IAChB;IACA,MAAM8D,OAAO,GAAG;MAAE,GAAGlF;IAAK,CAAC;IAC3B,IAAI,CAAC5E,OAAO,CAACgG,KAAK,GAAGpB,IAAI,CAACoB,KAAK,GAAG,IAAI,CAAChG,OAAO,CAACwJ,WAAW,GAAGG,OAAO;IACpE,IAAI,CAAC3J,OAAO,CAACiG,MAAM,GAAGrB,IAAI,CAACqB,MAAM,GAAG,IAAI,CAACjG,OAAO,CAACuJ,YAAY,GAAGI,OAAO;IACvE,IAAI,IAAI,CAACrJ,SAAS,CAACyJ,OAAO,EAAE;MACxBzJ,SAAS,CAAC2D,SAAS,CAAC+F,eAAe,CAAC;QAChChE,KAAK,EAAEpB,IAAI,CAACoB,KAAK,GAAG8D,OAAO,CAAC9D,KAAK;QACjCC,MAAM,EAAErB,IAAI,CAACqB,MAAM,GAAG6D,OAAO,CAAC7D;MAClC,CAAC,CAAC;IACN;IACA,OAAO,IAAI;EACf;EACAM,IAAIA,CAAA,EAAG;IACH,IAAI,CAACxB,qBAAqB,CAACmD,GAAG,IAAIA,GAAG,CAACjD,UAAU,CAAC,CAAC,CAAC;IACnD,IAAI,CAACO,iBAAiB,GAAG7F,SAAS;IAClC,IAAI,CAACgF,IAAI,CAAC9D,GAAG,IAAItC,KAAK,CAACsC,GAAG,EAAE,IAAI,CAAC+D,IAAI,CAAC,CAAC;EAC3C;EACA,MAAMqF,YAAYA,CAAA,EAAG;IACjB,IAAI,CAAC,IAAI,CAACjK,OAAO,IAAI,CAAC,IAAI,CAAC4B,MAAM,CAAC,CAAC,EAAE;MACjC;IACJ;IACA,MAAMtB,SAAS,GAAG,IAAI,CAACA,SAAS;MAAE4J,YAAY,GAAG5J,SAAS,CAAC6J,mBAAmB,CAAC,CAAC;IAChF7J,SAAS,CAAC2D,SAAS,CAACmG,UAAU,CAAC,CAAC;IAChC,IAAI,CAAC3I,mBAAmB,CAAC,CAAC;IAC1B,IAAIyI,YAAY,EAAE;MACd,MAAM5J,SAAS,CAAC+J,OAAO,CAAC,CAAC;IAC7B;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
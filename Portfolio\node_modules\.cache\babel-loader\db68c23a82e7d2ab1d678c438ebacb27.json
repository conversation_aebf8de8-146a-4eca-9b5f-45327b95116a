{"ast": null, "code": "import { colorToHsl, getHslAnimationFromHsl, randomInRange } from \"../../Utils\";\n\nfunction updateColorValue(delta, value, valueAnimation, max, decrease) {\n  var _a;\n\n  const colorValue = value;\n\n  if (!colorValue || !valueAnimation.enable) {\n    return;\n  }\n\n  const offset = randomInRange(valueAnimation.offset);\n  const velocity = ((_a = value.velocity) !== null && _a !== void 0 ? _a : 0) * delta.factor + offset * 3.6;\n\n  if (!decrease || colorValue.status === 0) {\n    colorValue.value += velocity;\n\n    if (decrease && colorValue.value > max) {\n      colorValue.status = 1;\n      colorValue.value -= colorValue.value % max;\n    }\n  } else {\n    colorValue.value -= velocity;\n\n    if (colorValue.value < 0) {\n      colorValue.status = 0;\n      colorValue.value += colorValue.value;\n    }\n  }\n\n  if (colorValue.value > max) {\n    colorValue.value %= max;\n  }\n}\n\nfunction updateColor(particle, delta) {\n  var _a, _b, _c;\n\n  const animationOptions = particle.options.color.animation;\n\n  if (((_a = particle.color) === null || _a === void 0 ? void 0 : _a.h) !== undefined) {\n    updateColorValue(delta, particle.color.h, animationOptions.h, 360, false);\n  }\n\n  if (((_b = particle.color) === null || _b === void 0 ? void 0 : _b.s) !== undefined) {\n    updateColorValue(delta, particle.color.s, animationOptions.s, 100, true);\n  }\n\n  if (((_c = particle.color) === null || _c === void 0 ? void 0 : _c.l) !== undefined) {\n    updateColorValue(delta, particle.color.l, animationOptions.l, 100, true);\n  }\n}\n\nexport class ColorUpdater {\n  constructor(container) {\n    this.container = container;\n  }\n\n  init(particle) {\n    const hslColor = colorToHsl(particle.options.color, particle.id, particle.options.reduceDuplicates);\n\n    if (hslColor) {\n      particle.color = getHslAnimationFromHsl(hslColor, particle.options.color.animation, this.container.retina.reduceFactor);\n    }\n  }\n\n  isEnabled(particle) {\n    var _a, _b, _c;\n\n    const animationOptions = particle.options.color.animation;\n    return !particle.destroyed && !particle.spawning && (((_a = particle.color) === null || _a === void 0 ? void 0 : _a.h.value) !== undefined && animationOptions.h.enable || ((_b = particle.color) === null || _b === void 0 ? void 0 : _b.s.value) !== undefined && animationOptions.s.enable || ((_c = particle.color) === null || _c === void 0 ? void 0 : _c.l.value) !== undefined && animationOptions.l.enable);\n  }\n\n  update(particle, delta) {\n    updateColor(particle, delta);\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Updaters/Color/ColorUpdater.js"], "names": ["colorToHsl", "getHslAnimationFromHsl", "randomInRange", "updateColorValue", "delta", "value", "valueAnimation", "max", "decrease", "_a", "colorValue", "enable", "offset", "velocity", "factor", "status", "updateColor", "particle", "_b", "_c", "animationOptions", "options", "color", "animation", "h", "undefined", "s", "l", "ColorUpdater", "constructor", "container", "init", "hslColor", "id", "reduceDuplicates", "retina", "reduceFactor", "isEnabled", "destroyed", "spawning", "update"], "mappings": "AAAA,SAASA,UAAT,EAAqBC,sBAArB,EAA6CC,aAA7C,QAAkE,aAAlE;;AACA,SAASC,gBAAT,CAA0BC,KAA1B,EAAiCC,KAAjC,EAAwCC,cAAxC,EAAwDC,GAAxD,EAA6DC,QAA7D,EAAuE;AACnE,MAAIC,EAAJ;;AACA,QAAMC,UAAU,GAAGL,KAAnB;;AACA,MAAI,CAACK,UAAD,IAAe,CAACJ,cAAc,CAACK,MAAnC,EAA2C;AACvC;AACH;;AACD,QAAMC,MAAM,GAAGV,aAAa,CAACI,cAAc,CAACM,MAAhB,CAA5B;AACA,QAAMC,QAAQ,GAAG,CAAC,CAACJ,EAAE,GAAGJ,KAAK,CAACQ,QAAZ,MAA0B,IAA1B,IAAkCJ,EAAE,KAAK,KAAK,CAA9C,GAAkDA,EAAlD,GAAuD,CAAxD,IAA6DL,KAAK,CAACU,MAAnE,GAA4EF,MAAM,GAAG,GAAtG;;AACA,MAAI,CAACJ,QAAD,IAAaE,UAAU,CAACK,MAAX,KAAsB,CAAvC,EAA0C;AACtCL,IAAAA,UAAU,CAACL,KAAX,IAAoBQ,QAApB;;AACA,QAAIL,QAAQ,IAAIE,UAAU,CAACL,KAAX,GAAmBE,GAAnC,EAAwC;AACpCG,MAAAA,UAAU,CAACK,MAAX,GAAoB,CAApB;AACAL,MAAAA,UAAU,CAACL,KAAX,IAAoBK,UAAU,CAACL,KAAX,GAAmBE,GAAvC;AACH;AACJ,GAND,MAOK;AACDG,IAAAA,UAAU,CAACL,KAAX,IAAoBQ,QAApB;;AACA,QAAIH,UAAU,CAACL,KAAX,GAAmB,CAAvB,EAA0B;AACtBK,MAAAA,UAAU,CAACK,MAAX,GAAoB,CAApB;AACAL,MAAAA,UAAU,CAACL,KAAX,IAAoBK,UAAU,CAACL,KAA/B;AACH;AACJ;;AACD,MAAIK,UAAU,CAACL,KAAX,GAAmBE,GAAvB,EAA4B;AACxBG,IAAAA,UAAU,CAACL,KAAX,IAAoBE,GAApB;AACH;AACJ;;AACD,SAASS,WAAT,CAAqBC,QAArB,EAA+Bb,KAA/B,EAAsC;AAClC,MAAIK,EAAJ,EAAQS,EAAR,EAAYC,EAAZ;;AACA,QAAMC,gBAAgB,GAAGH,QAAQ,CAACI,OAAT,CAAiBC,KAAjB,CAAuBC,SAAhD;;AACA,MAAI,CAAC,CAACd,EAAE,GAAGQ,QAAQ,CAACK,KAAf,MAA0B,IAA1B,IAAkCb,EAAE,KAAK,KAAK,CAA9C,GAAkD,KAAK,CAAvD,GAA2DA,EAAE,CAACe,CAA/D,MAAsEC,SAA1E,EAAqF;AACjFtB,IAAAA,gBAAgB,CAACC,KAAD,EAAQa,QAAQ,CAACK,KAAT,CAAeE,CAAvB,EAA0BJ,gBAAgB,CAACI,CAA3C,EAA8C,GAA9C,EAAmD,KAAnD,CAAhB;AACH;;AACD,MAAI,CAAC,CAACN,EAAE,GAAGD,QAAQ,CAACK,KAAf,MAA0B,IAA1B,IAAkCJ,EAAE,KAAK,KAAK,CAA9C,GAAkD,KAAK,CAAvD,GAA2DA,EAAE,CAACQ,CAA/D,MAAsED,SAA1E,EAAqF;AACjFtB,IAAAA,gBAAgB,CAACC,KAAD,EAAQa,QAAQ,CAACK,KAAT,CAAeI,CAAvB,EAA0BN,gBAAgB,CAACM,CAA3C,EAA8C,GAA9C,EAAmD,IAAnD,CAAhB;AACH;;AACD,MAAI,CAAC,CAACP,EAAE,GAAGF,QAAQ,CAACK,KAAf,MAA0B,IAA1B,IAAkCH,EAAE,KAAK,KAAK,CAA9C,GAAkD,KAAK,CAAvD,GAA2DA,EAAE,CAACQ,CAA/D,MAAsEF,SAA1E,EAAqF;AACjFtB,IAAAA,gBAAgB,CAACC,KAAD,EAAQa,QAAQ,CAACK,KAAT,CAAeK,CAAvB,EAA0BP,gBAAgB,CAACO,CAA3C,EAA8C,GAA9C,EAAmD,IAAnD,CAAhB;AACH;AACJ;;AACD,OAAO,MAAMC,YAAN,CAAmB;AACtBC,EAAAA,WAAW,CAACC,SAAD,EAAY;AACnB,SAAKA,SAAL,GAAiBA,SAAjB;AACH;;AACDC,EAAAA,IAAI,CAACd,QAAD,EAAW;AACX,UAAMe,QAAQ,GAAGhC,UAAU,CAACiB,QAAQ,CAACI,OAAT,CAAiBC,KAAlB,EAAyBL,QAAQ,CAACgB,EAAlC,EAAsChB,QAAQ,CAACI,OAAT,CAAiBa,gBAAvD,CAA3B;;AACA,QAAIF,QAAJ,EAAc;AACVf,MAAAA,QAAQ,CAACK,KAAT,GAAiBrB,sBAAsB,CAAC+B,QAAD,EAAWf,QAAQ,CAACI,OAAT,CAAiBC,KAAjB,CAAuBC,SAAlC,EAA6C,KAAKO,SAAL,CAAeK,MAAf,CAAsBC,YAAnE,CAAvC;AACH;AACJ;;AACDC,EAAAA,SAAS,CAACpB,QAAD,EAAW;AAChB,QAAIR,EAAJ,EAAQS,EAAR,EAAYC,EAAZ;;AACA,UAAMC,gBAAgB,GAAGH,QAAQ,CAACI,OAAT,CAAiBC,KAAjB,CAAuBC,SAAhD;AACA,WAAQ,CAACN,QAAQ,CAACqB,SAAV,IACJ,CAACrB,QAAQ,CAACsB,QADN,KAEF,CAAC,CAAC9B,EAAE,GAAGQ,QAAQ,CAACK,KAAf,MAA0B,IAA1B,IAAkCb,EAAE,KAAK,KAAK,CAA9C,GAAkD,KAAK,CAAvD,GAA2DA,EAAE,CAACe,CAAH,CAAKnB,KAAjE,MAA4EoB,SAA5E,IAAyFL,gBAAgB,CAACI,CAAjB,CAAmBb,MAA7G,IACI,CAAC,CAACO,EAAE,GAAGD,QAAQ,CAACK,KAAf,MAA0B,IAA1B,IAAkCJ,EAAE,KAAK,KAAK,CAA9C,GAAkD,KAAK,CAAvD,GAA2DA,EAAE,CAACQ,CAAH,CAAKrB,KAAjE,MAA4EoB,SAA5E,IAAyFL,gBAAgB,CAACM,CAAjB,CAAmBf,MADhH,IAEI,CAAC,CAACQ,EAAE,GAAGF,QAAQ,CAACK,KAAf,MAA0B,IAA1B,IAAkCH,EAAE,KAAK,KAAK,CAA9C,GAAkD,KAAK,CAAvD,GAA2DA,EAAE,CAACQ,CAAH,CAAKtB,KAAjE,MAA4EoB,SAA5E,IAAyFL,gBAAgB,CAACO,CAAjB,CAAmBhB,MAJ7G,CAAR;AAKH;;AACD6B,EAAAA,MAAM,CAACvB,QAAD,EAAWb,KAAX,EAAkB;AACpBY,IAAAA,WAAW,CAACC,QAAD,EAAWb,KAAX,CAAX;AACH;;AArBqB", "sourcesContent": ["import { colorToHsl, getHslAnimationFromHsl, randomInRange } from \"../../Utils\";\nfunction updateColorValue(delta, value, valueAnimation, max, decrease) {\n    var _a;\n    const colorValue = value;\n    if (!colorValue || !valueAnimation.enable) {\n        return;\n    }\n    const offset = randomInRange(valueAnimation.offset);\n    const velocity = ((_a = value.velocity) !== null && _a !== void 0 ? _a : 0) * delta.factor + offset * 3.6;\n    if (!decrease || colorValue.status === 0) {\n        colorValue.value += velocity;\n        if (decrease && colorValue.value > max) {\n            colorValue.status = 1;\n            colorValue.value -= colorValue.value % max;\n        }\n    }\n    else {\n        colorValue.value -= velocity;\n        if (colorValue.value < 0) {\n            colorValue.status = 0;\n            colorValue.value += colorValue.value;\n        }\n    }\n    if (colorValue.value > max) {\n        colorValue.value %= max;\n    }\n}\nfunction updateColor(particle, delta) {\n    var _a, _b, _c;\n    const animationOptions = particle.options.color.animation;\n    if (((_a = particle.color) === null || _a === void 0 ? void 0 : _a.h) !== undefined) {\n        updateColorValue(delta, particle.color.h, animationOptions.h, 360, false);\n    }\n    if (((_b = particle.color) === null || _b === void 0 ? void 0 : _b.s) !== undefined) {\n        updateColorValue(delta, particle.color.s, animationOptions.s, 100, true);\n    }\n    if (((_c = particle.color) === null || _c === void 0 ? void 0 : _c.l) !== undefined) {\n        updateColorValue(delta, particle.color.l, animationOptions.l, 100, true);\n    }\n}\nexport class ColorUpdater {\n    constructor(container) {\n        this.container = container;\n    }\n    init(particle) {\n        const hslColor = colorToHsl(particle.options.color, particle.id, particle.options.reduceDuplicates);\n        if (hslColor) {\n            particle.color = getHslAnimationFromHsl(hslColor, particle.options.color.animation, this.container.retina.reduceFactor);\n        }\n    }\n    isEnabled(particle) {\n        var _a, _b, _c;\n        const animationOptions = particle.options.color.animation;\n        return (!particle.destroyed &&\n            !particle.spawning &&\n            ((((_a = particle.color) === null || _a === void 0 ? void 0 : _a.h.value) !== undefined && animationOptions.h.enable) ||\n                (((_b = particle.color) === null || _b === void 0 ? void 0 : _b.s.value) !== undefined && animationOptions.s.enable) ||\n                (((_c = particle.color) === null || _c === void 0 ? void 0 : _c.l.value) !== undefined && animationOptions.l.enable)));\n    }\n    update(particle, delta) {\n        updateColor(particle, delta);\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
{"ast": null, "code": "import { deepExtend, executeOnSingleOrMultiple, safeMatchMedia } from \"../../Utils/Utils.js\";\nimport { Background } from \"./Background/Background.js\";\nimport { BackgroundMask } from \"./BackgroundMask/BackgroundMask.js\";\nimport { FullScreen } from \"./FullScreen/FullScreen.js\";\nimport { Interactivity } from \"./Interactivity/Interactivity.js\";\nimport { ManualParticle } from \"./ManualParticle.js\";\nimport { Responsive } from \"./Responsive.js\";\nimport { ResponsiveMode } from \"../../Enums/Modes/ResponsiveMode.js\";\nimport { Theme } from \"./Theme/Theme.js\";\nimport { ThemeMode } from \"../../Enums/Modes/ThemeMode.js\";\nimport { isBoolean } from \"../../Utils/TypeUtils.js\";\nimport { loadParticlesOptions } from \"../../Utils/OptionsUtils.js\";\nimport { setRangeValue } from \"../../Utils/NumberUtils.js\";\nexport class Options {\n  constructor(engine, container) {\n    this._findDefaultTheme = mode => {\n      return this.themes.find(theme => theme.default.value && theme.default.mode === mode) ?? this.themes.find(theme => theme.default.value && theme.default.mode === ThemeMode.any);\n    };\n    this._importPreset = preset => {\n      this.load(this._engine.getPreset(preset));\n    };\n    this._engine = engine;\n    this._container = container;\n    this.autoPlay = true;\n    this.background = new Background();\n    this.backgroundMask = new BackgroundMask();\n    this.clear = true;\n    this.defaultThemes = {};\n    this.delay = 0;\n    this.fullScreen = new FullScreen();\n    this.detectRetina = true;\n    this.duration = 0;\n    this.fpsLimit = 120;\n    this.interactivity = new Interactivity(engine, container);\n    this.manualParticles = [];\n    this.particles = loadParticlesOptions(this._engine, this._container);\n    this.pauseOnBlur = true;\n    this.pauseOnOutsideViewport = true;\n    this.responsive = [];\n    this.smooth = false;\n    this.style = {};\n    this.themes = [];\n    this.zLayers = 100;\n  }\n  load(data) {\n    if (!data) {\n      return;\n    }\n    if (data.preset !== undefined) {\n      executeOnSingleOrMultiple(data.preset, preset => this._importPreset(preset));\n    }\n    if (data.autoPlay !== undefined) {\n      this.autoPlay = data.autoPlay;\n    }\n    if (data.clear !== undefined) {\n      this.clear = data.clear;\n    }\n    if (data.key !== undefined) {\n      this.key = data.key;\n    }\n    if (data.name !== undefined) {\n      this.name = data.name;\n    }\n    if (data.delay !== undefined) {\n      this.delay = setRangeValue(data.delay);\n    }\n    const detectRetina = data.detectRetina;\n    if (detectRetina !== undefined) {\n      this.detectRetina = detectRetina;\n    }\n    if (data.duration !== undefined) {\n      this.duration = setRangeValue(data.duration);\n    }\n    const fpsLimit = data.fpsLimit;\n    if (fpsLimit !== undefined) {\n      this.fpsLimit = fpsLimit;\n    }\n    if (data.pauseOnBlur !== undefined) {\n      this.pauseOnBlur = data.pauseOnBlur;\n    }\n    if (data.pauseOnOutsideViewport !== undefined) {\n      this.pauseOnOutsideViewport = data.pauseOnOutsideViewport;\n    }\n    if (data.zLayers !== undefined) {\n      this.zLayers = data.zLayers;\n    }\n    this.background.load(data.background);\n    const fullScreen = data.fullScreen;\n    if (isBoolean(fullScreen)) {\n      this.fullScreen.enable = fullScreen;\n    } else {\n      this.fullScreen.load(fullScreen);\n    }\n    this.backgroundMask.load(data.backgroundMask);\n    this.interactivity.load(data.interactivity);\n    if (data.manualParticles) {\n      this.manualParticles = data.manualParticles.map(t => {\n        const tmp = new ManualParticle();\n        tmp.load(t);\n        return tmp;\n      });\n    }\n    this.particles.load(data.particles);\n    this.style = deepExtend(this.style, data.style);\n    this._engine.loadOptions(this, data);\n    if (data.smooth !== undefined) {\n      this.smooth = data.smooth;\n    }\n    const interactors = this._engine.interactors.get(this._container);\n    if (interactors) {\n      for (const interactor of interactors) {\n        if (interactor.loadOptions) {\n          interactor.loadOptions(this, data);\n        }\n      }\n    }\n    if (data.responsive !== undefined) {\n      for (const responsive of data.responsive) {\n        const optResponsive = new Responsive();\n        optResponsive.load(responsive);\n        this.responsive.push(optResponsive);\n      }\n    }\n    this.responsive.sort((a, b) => a.maxWidth - b.maxWidth);\n    if (data.themes !== undefined) {\n      for (const theme of data.themes) {\n        const existingTheme = this.themes.find(t => t.name === theme.name);\n        if (!existingTheme) {\n          const optTheme = new Theme();\n          optTheme.load(theme);\n          this.themes.push(optTheme);\n        } else {\n          existingTheme.load(theme);\n        }\n      }\n    }\n    this.defaultThemes.dark = this._findDefaultTheme(ThemeMode.dark)?.name;\n    this.defaultThemes.light = this._findDefaultTheme(ThemeMode.light)?.name;\n  }\n  setResponsive(width, pxRatio, defaultOptions) {\n    this.load(defaultOptions);\n    const responsiveOptions = this.responsive.find(t => t.mode === ResponsiveMode.screen && screen ? t.maxWidth > screen.availWidth : t.maxWidth * pxRatio > width);\n    this.load(responsiveOptions?.options);\n    return responsiveOptions?.maxWidth;\n  }\n  setTheme(name) {\n    if (name) {\n      const chosenTheme = this.themes.find(theme => theme.name === name);\n      if (chosenTheme) {\n        this.load(chosenTheme.options);\n      }\n    } else {\n      const mediaMatch = safeMatchMedia(\"(prefers-color-scheme: dark)\"),\n        clientDarkMode = mediaMatch?.matches,\n        defaultTheme = this._findDefaultTheme(clientDarkMode ? ThemeMode.dark : ThemeMode.light);\n      if (defaultTheme) {\n        this.load(defaultTheme.options);\n      }\n    }\n  }\n}", "map": {"version": 3, "names": ["deepExtend", "executeOnSingleOrMultiple", "safeMatchMedia", "Background", "BackgroundMask", "FullScreen", "Interactivity", "ManualParticle", "Responsive", "ResponsiveMode", "Theme", "ThemeMode", "isBoolean", "loadParticlesOptions", "setRangeValue", "Options", "constructor", "engine", "container", "_findDefaultTheme", "mode", "themes", "find", "theme", "default", "value", "any", "_importPreset", "preset", "load", "_engine", "getPreset", "_container", "autoPlay", "background", "backgroundMask", "clear", "defaultThemes", "delay", "fullScreen", "detectRetina", "duration", "fpsLimit", "interactivity", "manualParticles", "particles", "pauseOnBlur", "pauseOnOutsideViewport", "responsive", "smooth", "style", "zLayers", "data", "undefined", "key", "name", "enable", "map", "t", "tmp", "loadOptions", "interactors", "get", "interactor", "optResponsive", "push", "sort", "a", "b", "max<PERSON><PERSON><PERSON>", "existingTheme", "optTheme", "dark", "light", "setResponsive", "width", "pxRatio", "defaultOptions", "responsiveOptions", "screen", "availWidth", "options", "setTheme", "chosenTheme", "mediaMatch", "clientDarkMode", "matches", "defaultTheme"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/engine/browser/Options/Classes/Options.js"], "sourcesContent": ["import { deepExtend, executeOnSingleOrMultiple, safeMatchMedia } from \"../../Utils/Utils.js\";\nimport { Background } from \"./Background/Background.js\";\nimport { BackgroundMask } from \"./BackgroundMask/BackgroundMask.js\";\nimport { FullScreen } from \"./FullScreen/FullScreen.js\";\nimport { Interactivity } from \"./Interactivity/Interactivity.js\";\nimport { ManualParticle } from \"./ManualParticle.js\";\nimport { Responsive } from \"./Responsive.js\";\nimport { ResponsiveMode } from \"../../Enums/Modes/ResponsiveMode.js\";\nimport { Theme } from \"./Theme/Theme.js\";\nimport { ThemeMode } from \"../../Enums/Modes/ThemeMode.js\";\nimport { isBoolean } from \"../../Utils/TypeUtils.js\";\nimport { loadParticlesOptions } from \"../../Utils/OptionsUtils.js\";\nimport { setRangeValue } from \"../../Utils/NumberUtils.js\";\nexport class Options {\n    constructor(engine, container) {\n        this._findDefaultTheme = mode => {\n            return (this.themes.find(theme => theme.default.value && theme.default.mode === mode) ??\n                this.themes.find(theme => theme.default.value && theme.default.mode === ThemeMode.any));\n        };\n        this._importPreset = preset => {\n            this.load(this._engine.getPreset(preset));\n        };\n        this._engine = engine;\n        this._container = container;\n        this.autoPlay = true;\n        this.background = new Background();\n        this.backgroundMask = new BackgroundMask();\n        this.clear = true;\n        this.defaultThemes = {};\n        this.delay = 0;\n        this.fullScreen = new FullScreen();\n        this.detectRetina = true;\n        this.duration = 0;\n        this.fpsLimit = 120;\n        this.interactivity = new Interactivity(engine, container);\n        this.manualParticles = [];\n        this.particles = loadParticlesOptions(this._engine, this._container);\n        this.pauseOnBlur = true;\n        this.pauseOnOutsideViewport = true;\n        this.responsive = [];\n        this.smooth = false;\n        this.style = {};\n        this.themes = [];\n        this.zLayers = 100;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.preset !== undefined) {\n            executeOnSingleOrMultiple(data.preset, preset => this._importPreset(preset));\n        }\n        if (data.autoPlay !== undefined) {\n            this.autoPlay = data.autoPlay;\n        }\n        if (data.clear !== undefined) {\n            this.clear = data.clear;\n        }\n        if (data.key !== undefined) {\n            this.key = data.key;\n        }\n        if (data.name !== undefined) {\n            this.name = data.name;\n        }\n        if (data.delay !== undefined) {\n            this.delay = setRangeValue(data.delay);\n        }\n        const detectRetina = data.detectRetina;\n        if (detectRetina !== undefined) {\n            this.detectRetina = detectRetina;\n        }\n        if (data.duration !== undefined) {\n            this.duration = setRangeValue(data.duration);\n        }\n        const fpsLimit = data.fpsLimit;\n        if (fpsLimit !== undefined) {\n            this.fpsLimit = fpsLimit;\n        }\n        if (data.pauseOnBlur !== undefined) {\n            this.pauseOnBlur = data.pauseOnBlur;\n        }\n        if (data.pauseOnOutsideViewport !== undefined) {\n            this.pauseOnOutsideViewport = data.pauseOnOutsideViewport;\n        }\n        if (data.zLayers !== undefined) {\n            this.zLayers = data.zLayers;\n        }\n        this.background.load(data.background);\n        const fullScreen = data.fullScreen;\n        if (isBoolean(fullScreen)) {\n            this.fullScreen.enable = fullScreen;\n        }\n        else {\n            this.fullScreen.load(fullScreen);\n        }\n        this.backgroundMask.load(data.backgroundMask);\n        this.interactivity.load(data.interactivity);\n        if (data.manualParticles) {\n            this.manualParticles = data.manualParticles.map(t => {\n                const tmp = new ManualParticle();\n                tmp.load(t);\n                return tmp;\n            });\n        }\n        this.particles.load(data.particles);\n        this.style = deepExtend(this.style, data.style);\n        this._engine.loadOptions(this, data);\n        if (data.smooth !== undefined) {\n            this.smooth = data.smooth;\n        }\n        const interactors = this._engine.interactors.get(this._container);\n        if (interactors) {\n            for (const interactor of interactors) {\n                if (interactor.loadOptions) {\n                    interactor.loadOptions(this, data);\n                }\n            }\n        }\n        if (data.responsive !== undefined) {\n            for (const responsive of data.responsive) {\n                const optResponsive = new Responsive();\n                optResponsive.load(responsive);\n                this.responsive.push(optResponsive);\n            }\n        }\n        this.responsive.sort((a, b) => a.maxWidth - b.maxWidth);\n        if (data.themes !== undefined) {\n            for (const theme of data.themes) {\n                const existingTheme = this.themes.find(t => t.name === theme.name);\n                if (!existingTheme) {\n                    const optTheme = new Theme();\n                    optTheme.load(theme);\n                    this.themes.push(optTheme);\n                }\n                else {\n                    existingTheme.load(theme);\n                }\n            }\n        }\n        this.defaultThemes.dark = this._findDefaultTheme(ThemeMode.dark)?.name;\n        this.defaultThemes.light = this._findDefaultTheme(ThemeMode.light)?.name;\n    }\n    setResponsive(width, pxRatio, defaultOptions) {\n        this.load(defaultOptions);\n        const responsiveOptions = this.responsive.find(t => t.mode === ResponsiveMode.screen && screen ? t.maxWidth > screen.availWidth : t.maxWidth * pxRatio > width);\n        this.load(responsiveOptions?.options);\n        return responsiveOptions?.maxWidth;\n    }\n    setTheme(name) {\n        if (name) {\n            const chosenTheme = this.themes.find(theme => theme.name === name);\n            if (chosenTheme) {\n                this.load(chosenTheme.options);\n            }\n        }\n        else {\n            const mediaMatch = safeMatchMedia(\"(prefers-color-scheme: dark)\"), clientDarkMode = mediaMatch?.matches, defaultTheme = this._findDefaultTheme(clientDarkMode ? ThemeMode.dark : ThemeMode.light);\n            if (defaultTheme) {\n                this.load(defaultTheme.options);\n            }\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,UAAU,EAAEC,yBAAyB,EAAEC,cAAc,QAAQ,sBAAsB;AAC5F,SAASC,UAAU,QAAQ,4BAA4B;AACvD,SAASC,cAAc,QAAQ,oCAAoC;AACnE,SAASC,UAAU,QAAQ,4BAA4B;AACvD,SAASC,aAAa,QAAQ,kCAAkC;AAChE,SAASC,cAAc,QAAQ,qBAAqB;AACpD,SAASC,UAAU,QAAQ,iBAAiB;AAC5C,SAASC,cAAc,QAAQ,qCAAqC;AACpE,SAASC,KAAK,QAAQ,kBAAkB;AACxC,SAASC,SAAS,QAAQ,gCAAgC;AAC1D,SAASC,SAAS,QAAQ,0BAA0B;AACpD,SAASC,oBAAoB,QAAQ,6BAA6B;AAClE,SAASC,aAAa,QAAQ,4BAA4B;AAC1D,OAAO,MAAMC,OAAO,CAAC;EACjBC,WAAWA,CAACC,MAAM,EAAEC,SAAS,EAAE;IAC3B,IAAI,CAACC,iBAAiB,GAAGC,IAAI,IAAI;MAC7B,OAAQ,IAAI,CAACC,MAAM,CAACC,IAAI,CAACC,KAAK,IAAIA,KAAK,CAACC,OAAO,CAACC,KAAK,IAAIF,KAAK,CAACC,OAAO,CAACJ,IAAI,KAAKA,IAAI,CAAC,IACjF,IAAI,CAACC,MAAM,CAACC,IAAI,CAACC,KAAK,IAAIA,KAAK,CAACC,OAAO,CAACC,KAAK,IAAIF,KAAK,CAACC,OAAO,CAACJ,IAAI,KAAKT,SAAS,CAACe,GAAG,CAAC;IAC9F,CAAC;IACD,IAAI,CAACC,aAAa,GAAGC,MAAM,IAAI;MAC3B,IAAI,CAACC,IAAI,CAAC,IAAI,CAACC,OAAO,CAACC,SAAS,CAACH,MAAM,CAAC,CAAC;IAC7C,CAAC;IACD,IAAI,CAACE,OAAO,GAAGb,MAAM;IACrB,IAAI,CAACe,UAAU,GAAGd,SAAS;IAC3B,IAAI,CAACe,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,UAAU,GAAG,IAAI/B,UAAU,CAAC,CAAC;IAClC,IAAI,CAACgC,cAAc,GAAG,IAAI/B,cAAc,CAAC,CAAC;IAC1C,IAAI,CAACgC,KAAK,GAAG,IAAI;IACjB,IAAI,CAACC,aAAa,GAAG,CAAC,CAAC;IACvB,IAAI,CAACC,KAAK,GAAG,CAAC;IACd,IAAI,CAACC,UAAU,GAAG,IAAIlC,UAAU,CAAC,CAAC;IAClC,IAAI,CAACmC,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,QAAQ,GAAG,CAAC;IACjB,IAAI,CAACC,QAAQ,GAAG,GAAG;IACnB,IAAI,CAACC,aAAa,GAAG,IAAIrC,aAAa,CAACW,MAAM,EAAEC,SAAS,CAAC;IACzD,IAAI,CAAC0B,eAAe,GAAG,EAAE;IACzB,IAAI,CAACC,SAAS,GAAGhC,oBAAoB,CAAC,IAAI,CAACiB,OAAO,EAAE,IAAI,CAACE,UAAU,CAAC;IACpE,IAAI,CAACc,WAAW,GAAG,IAAI;IACvB,IAAI,CAACC,sBAAsB,GAAG,IAAI;IAClC,IAAI,CAACC,UAAU,GAAG,EAAE;IACpB,IAAI,CAACC,MAAM,GAAG,KAAK;IACnB,IAAI,CAACC,KAAK,GAAG,CAAC,CAAC;IACf,IAAI,CAAC7B,MAAM,GAAG,EAAE;IAChB,IAAI,CAAC8B,OAAO,GAAG,GAAG;EACtB;EACAtB,IAAIA,CAACuB,IAAI,EAAE;IACP,IAAI,CAACA,IAAI,EAAE;MACP;IACJ;IACA,IAAIA,IAAI,CAACxB,MAAM,KAAKyB,SAAS,EAAE;MAC3BpD,yBAAyB,CAACmD,IAAI,CAACxB,MAAM,EAAEA,MAAM,IAAI,IAAI,CAACD,aAAa,CAACC,MAAM,CAAC,CAAC;IAChF;IACA,IAAIwB,IAAI,CAACnB,QAAQ,KAAKoB,SAAS,EAAE;MAC7B,IAAI,CAACpB,QAAQ,GAAGmB,IAAI,CAACnB,QAAQ;IACjC;IACA,IAAImB,IAAI,CAAChB,KAAK,KAAKiB,SAAS,EAAE;MAC1B,IAAI,CAACjB,KAAK,GAAGgB,IAAI,CAAChB,KAAK;IAC3B;IACA,IAAIgB,IAAI,CAACE,GAAG,KAAKD,SAAS,EAAE;MACxB,IAAI,CAACC,GAAG,GAAGF,IAAI,CAACE,GAAG;IACvB;IACA,IAAIF,IAAI,CAACG,IAAI,KAAKF,SAAS,EAAE;MACzB,IAAI,CAACE,IAAI,GAAGH,IAAI,CAACG,IAAI;IACzB;IACA,IAAIH,IAAI,CAACd,KAAK,KAAKe,SAAS,EAAE;MAC1B,IAAI,CAACf,KAAK,GAAGxB,aAAa,CAACsC,IAAI,CAACd,KAAK,CAAC;IAC1C;IACA,MAAME,YAAY,GAAGY,IAAI,CAACZ,YAAY;IACtC,IAAIA,YAAY,KAAKa,SAAS,EAAE;MAC5B,IAAI,CAACb,YAAY,GAAGA,YAAY;IACpC;IACA,IAAIY,IAAI,CAACX,QAAQ,KAAKY,SAAS,EAAE;MAC7B,IAAI,CAACZ,QAAQ,GAAG3B,aAAa,CAACsC,IAAI,CAACX,QAAQ,CAAC;IAChD;IACA,MAAMC,QAAQ,GAAGU,IAAI,CAACV,QAAQ;IAC9B,IAAIA,QAAQ,KAAKW,SAAS,EAAE;MACxB,IAAI,CAACX,QAAQ,GAAGA,QAAQ;IAC5B;IACA,IAAIU,IAAI,CAACN,WAAW,KAAKO,SAAS,EAAE;MAChC,IAAI,CAACP,WAAW,GAAGM,IAAI,CAACN,WAAW;IACvC;IACA,IAAIM,IAAI,CAACL,sBAAsB,KAAKM,SAAS,EAAE;MAC3C,IAAI,CAACN,sBAAsB,GAAGK,IAAI,CAACL,sBAAsB;IAC7D;IACA,IAAIK,IAAI,CAACD,OAAO,KAAKE,SAAS,EAAE;MAC5B,IAAI,CAACF,OAAO,GAAGC,IAAI,CAACD,OAAO;IAC/B;IACA,IAAI,CAACjB,UAAU,CAACL,IAAI,CAACuB,IAAI,CAAClB,UAAU,CAAC;IACrC,MAAMK,UAAU,GAAGa,IAAI,CAACb,UAAU;IAClC,IAAI3B,SAAS,CAAC2B,UAAU,CAAC,EAAE;MACvB,IAAI,CAACA,UAAU,CAACiB,MAAM,GAAGjB,UAAU;IACvC,CAAC,MACI;MACD,IAAI,CAACA,UAAU,CAACV,IAAI,CAACU,UAAU,CAAC;IACpC;IACA,IAAI,CAACJ,cAAc,CAACN,IAAI,CAACuB,IAAI,CAACjB,cAAc,CAAC;IAC7C,IAAI,CAACQ,aAAa,CAACd,IAAI,CAACuB,IAAI,CAACT,aAAa,CAAC;IAC3C,IAAIS,IAAI,CAACR,eAAe,EAAE;MACtB,IAAI,CAACA,eAAe,GAAGQ,IAAI,CAACR,eAAe,CAACa,GAAG,CAACC,CAAC,IAAI;QACjD,MAAMC,GAAG,GAAG,IAAIpD,cAAc,CAAC,CAAC;QAChCoD,GAAG,CAAC9B,IAAI,CAAC6B,CAAC,CAAC;QACX,OAAOC,GAAG;MACd,CAAC,CAAC;IACN;IACA,IAAI,CAACd,SAAS,CAAChB,IAAI,CAACuB,IAAI,CAACP,SAAS,CAAC;IACnC,IAAI,CAACK,KAAK,GAAGlD,UAAU,CAAC,IAAI,CAACkD,KAAK,EAAEE,IAAI,CAACF,KAAK,CAAC;IAC/C,IAAI,CAACpB,OAAO,CAAC8B,WAAW,CAAC,IAAI,EAAER,IAAI,CAAC;IACpC,IAAIA,IAAI,CAACH,MAAM,KAAKI,SAAS,EAAE;MAC3B,IAAI,CAACJ,MAAM,GAAGG,IAAI,CAACH,MAAM;IAC7B;IACA,MAAMY,WAAW,GAAG,IAAI,CAAC/B,OAAO,CAAC+B,WAAW,CAACC,GAAG,CAAC,IAAI,CAAC9B,UAAU,CAAC;IACjE,IAAI6B,WAAW,EAAE;MACb,KAAK,MAAME,UAAU,IAAIF,WAAW,EAAE;QAClC,IAAIE,UAAU,CAACH,WAAW,EAAE;UACxBG,UAAU,CAACH,WAAW,CAAC,IAAI,EAAER,IAAI,CAAC;QACtC;MACJ;IACJ;IACA,IAAIA,IAAI,CAACJ,UAAU,KAAKK,SAAS,EAAE;MAC/B,KAAK,MAAML,UAAU,IAAII,IAAI,CAACJ,UAAU,EAAE;QACtC,MAAMgB,aAAa,GAAG,IAAIxD,UAAU,CAAC,CAAC;QACtCwD,aAAa,CAACnC,IAAI,CAACmB,UAAU,CAAC;QAC9B,IAAI,CAACA,UAAU,CAACiB,IAAI,CAACD,aAAa,CAAC;MACvC;IACJ;IACA,IAAI,CAAChB,UAAU,CAACkB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACE,QAAQ,GAAGD,CAAC,CAACC,QAAQ,CAAC;IACvD,IAAIjB,IAAI,CAAC/B,MAAM,KAAKgC,SAAS,EAAE;MAC3B,KAAK,MAAM9B,KAAK,IAAI6B,IAAI,CAAC/B,MAAM,EAAE;QAC7B,MAAMiD,aAAa,GAAG,IAAI,CAACjD,MAAM,CAACC,IAAI,CAACoC,CAAC,IAAIA,CAAC,CAACH,IAAI,KAAKhC,KAAK,CAACgC,IAAI,CAAC;QAClE,IAAI,CAACe,aAAa,EAAE;UAChB,MAAMC,QAAQ,GAAG,IAAI7D,KAAK,CAAC,CAAC;UAC5B6D,QAAQ,CAAC1C,IAAI,CAACN,KAAK,CAAC;UACpB,IAAI,CAACF,MAAM,CAAC4C,IAAI,CAACM,QAAQ,CAAC;QAC9B,CAAC,MACI;UACDD,aAAa,CAACzC,IAAI,CAACN,KAAK,CAAC;QAC7B;MACJ;IACJ;IACA,IAAI,CAACc,aAAa,CAACmC,IAAI,GAAG,IAAI,CAACrD,iBAAiB,CAACR,SAAS,CAAC6D,IAAI,CAAC,EAAEjB,IAAI;IACtE,IAAI,CAAClB,aAAa,CAACoC,KAAK,GAAG,IAAI,CAACtD,iBAAiB,CAACR,SAAS,CAAC8D,KAAK,CAAC,EAAElB,IAAI;EAC5E;EACAmB,aAAaA,CAACC,KAAK,EAAEC,OAAO,EAAEC,cAAc,EAAE;IAC1C,IAAI,CAAChD,IAAI,CAACgD,cAAc,CAAC;IACzB,MAAMC,iBAAiB,GAAG,IAAI,CAAC9B,UAAU,CAAC1B,IAAI,CAACoC,CAAC,IAAIA,CAAC,CAACtC,IAAI,KAAKX,cAAc,CAACsE,MAAM,IAAIA,MAAM,GAAGrB,CAAC,CAACW,QAAQ,GAAGU,MAAM,CAACC,UAAU,GAAGtB,CAAC,CAACW,QAAQ,GAAGO,OAAO,GAAGD,KAAK,CAAC;IAC/J,IAAI,CAAC9C,IAAI,CAACiD,iBAAiB,EAAEG,OAAO,CAAC;IACrC,OAAOH,iBAAiB,EAAET,QAAQ;EACtC;EACAa,QAAQA,CAAC3B,IAAI,EAAE;IACX,IAAIA,IAAI,EAAE;MACN,MAAM4B,WAAW,GAAG,IAAI,CAAC9D,MAAM,CAACC,IAAI,CAACC,KAAK,IAAIA,KAAK,CAACgC,IAAI,KAAKA,IAAI,CAAC;MAClE,IAAI4B,WAAW,EAAE;QACb,IAAI,CAACtD,IAAI,CAACsD,WAAW,CAACF,OAAO,CAAC;MAClC;IACJ,CAAC,MACI;MACD,MAAMG,UAAU,GAAGlF,cAAc,CAAC,8BAA8B,CAAC;QAAEmF,cAAc,GAAGD,UAAU,EAAEE,OAAO;QAAEC,YAAY,GAAG,IAAI,CAACpE,iBAAiB,CAACkE,cAAc,GAAG1E,SAAS,CAAC6D,IAAI,GAAG7D,SAAS,CAAC8D,KAAK,CAAC;MACjM,IAAIc,YAAY,EAAE;QACd,IAAI,CAAC1D,IAAI,CAAC0D,YAAY,CAACN,OAAO,CAAC;MACnC;IACJ;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { ConnectLinks } from \"./ConnectLinks.js\";\nexport class Connect {\n  constructor() {\n    this.distance = 80;\n    this.links = new ConnectLinks();\n    this.radius = 60;\n  }\n  load(data) {\n    if (!data) {\n      return;\n    }\n    if (data.distance !== undefined) {\n      this.distance = data.distance;\n    }\n    this.links.load(data.links);\n    if (data.radius !== undefined) {\n      this.radius = data.radius;\n    }\n  }\n}", "map": {"version": 3, "names": ["ConnectLinks", "Connect", "constructor", "distance", "links", "radius", "load", "data", "undefined"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/interaction-external-connect/browser/Options/Classes/Connect.js"], "sourcesContent": ["import { ConnectLinks } from \"./ConnectLinks.js\";\nexport class Connect {\n    constructor() {\n        this.distance = 80;\n        this.links = new ConnectLinks();\n        this.radius = 60;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.distance !== undefined) {\n            this.distance = data.distance;\n        }\n        this.links.load(data.links);\n        if (data.radius !== undefined) {\n            this.radius = data.radius;\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,mBAAmB;AAChD,OAAO,MAAMC,OAAO,CAAC;EACjBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACC,KAAK,GAAG,IAAIJ,YAAY,CAAC,CAAC;IAC/B,IAAI,CAACK,MAAM,GAAG,EAAE;EACpB;EACAC,IAAIA,CAACC,IAAI,EAAE;IACP,IAAI,CAACA,IAAI,EAAE;MACP;IACJ;IACA,IAAIA,IAAI,CAACJ,QAAQ,KAAKK,SAAS,EAAE;MAC7B,IAAI,CAACL,QAAQ,GAAGI,IAAI,CAACJ,QAAQ;IACjC;IACA,IAAI,CAACC,KAAK,CAACE,IAAI,CAACC,IAAI,CAACH,KAAK,CAAC;IAC3B,IAAIG,IAAI,CAACF,MAAM,KAAKG,SAAS,EAAE;MAC3B,IAAI,CAACH,MAAM,GAAGE,IAAI,CAACF,MAAM;IAC7B;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
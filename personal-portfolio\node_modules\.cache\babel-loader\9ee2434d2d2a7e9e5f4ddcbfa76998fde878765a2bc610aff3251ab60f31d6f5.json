{"ast": null, "code": "const shapeGeneratorss = new Map();\nexport class ShapeManager {\n  constructor(engine) {\n    this._engine = engine;\n  }\n  addShapeGenerator(name, generator) {\n    if (!this.getShapeGenerator(name)) {\n      shapeGeneratorss.set(name, generator);\n    }\n  }\n  getShapeGenerator(name) {\n    return shapeGeneratorss.get(name);\n  }\n  getSupportedShapeGenerators() {\n    return shapeGeneratorss.keys();\n  }\n}", "map": {"version": 3, "names": ["shapeGeneratorss", "Map", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "constructor", "engine", "_engine", "addShapeGenerator", "name", "generator", "getShapeGenerator", "set", "get", "getSupportedShapeGenerators", "keys"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/plugin-emitters/browser/ShapeManager.js"], "sourcesContent": ["const shapeGeneratorss = new Map();\nexport class ShapeManager {\n    constructor(engine) {\n        this._engine = engine;\n    }\n    addShapeGenerator(name, generator) {\n        if (!this.getShapeGenerator(name)) {\n            shapeGeneratorss.set(name, generator);\n        }\n    }\n    getShapeGenerator(name) {\n        return shapeGeneratorss.get(name);\n    }\n    getSupportedShapeGenerators() {\n        return shapeGeneratorss.keys();\n    }\n}\n"], "mappings": "AAAA,MAAMA,gBAAgB,GAAG,IAAIC,GAAG,CAAC,CAAC;AAClC,OAAO,MAAMC,YAAY,CAAC;EACtBC,WAAWA,CAACC,MAAM,EAAE;IAChB,IAAI,CAACC,OAAO,GAAGD,MAAM;EACzB;EACAE,iBAAiBA,CAACC,IAAI,EAAEC,SAAS,EAAE;IAC/B,IAAI,CAAC,IAAI,CAACC,iBAAiB,CAACF,IAAI,CAAC,EAAE;MAC/BP,gBAAgB,CAACU,GAAG,CAACH,IAAI,EAAEC,SAAS,CAAC;IACzC;EACJ;EACAC,iBAAiBA,CAACF,IAAI,EAAE;IACpB,OAAOP,gBAAgB,CAACW,GAAG,CAACJ,IAAI,CAAC;EACrC;EACAK,2BAA2BA,CAAA,EAAG;IAC1B,OAAOZ,gBAAgB,CAACa,IAAI,CAAC,CAAC;EAClC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
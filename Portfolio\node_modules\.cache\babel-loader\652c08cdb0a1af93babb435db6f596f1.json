{"ast": null, "code": "import requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name isDate\n * @category Common Helpers\n * @summary Is the given value a date?\n *\n * @description\n * Returns true if the given value is an instance of Date. The function works for dates transferred across iframes.\n *\n * ### v2.0.0 breaking changes:\n *\n * - [Changes that are common for the whole library](https://github.com/date-fns/date-fns/blob/master/docs/upgradeGuide.md#Common-Changes).\n *\n * @param {*} value - the value to check\n * @returns {boolean} true if the given value is a date\n * @throws {TypeError} 1 arguments required\n *\n * @example\n * // For a valid date:\n * const result = isDate(new Date())\n * //=> true\n *\n * @example\n * // For an invalid date:\n * const result = isDate(new Date(NaN))\n * //=> true\n *\n * @example\n * // For some value:\n * const result = isDate('2014-02-31')\n * //=> false\n *\n * @example\n * // For an object:\n * const result = isDate({})\n * //=> false\n */\n\nexport default function isDate(value) {\n  requiredArgs(1, arguments);\n  return value instanceof Date || typeof value === 'object' && Object.prototype.toString.call(value) === '[object Date]';\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/date-fns/esm/isDate/index.js"], "names": ["requiredArgs", "isDate", "value", "arguments", "Date", "Object", "prototype", "toString", "call"], "mappings": "AAAA,OAAOA,YAAP,MAAyB,+BAAzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,eAAe,SAASC,MAAT,CAAgBC,KAAhB,EAAuB;AACpCF,EAAAA,YAAY,CAAC,CAAD,EAAIG,SAAJ,CAAZ;AACA,SAAOD,KAAK,YAAYE,IAAjB,IAAyB,OAAOF,KAAP,KAAiB,QAAjB,IAA6BG,MAAM,CAACC,SAAP,CAAiBC,QAAjB,CAA0BC,IAA1B,CAA+BN,KAA/B,MAA0C,eAAvG;AACD", "sourcesContent": ["import requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name isDate\n * @category Common Helpers\n * @summary Is the given value a date?\n *\n * @description\n * Returns true if the given value is an instance of Date. The function works for dates transferred across iframes.\n *\n * ### v2.0.0 breaking changes:\n *\n * - [Changes that are common for the whole library](https://github.com/date-fns/date-fns/blob/master/docs/upgradeGuide.md#Common-Changes).\n *\n * @param {*} value - the value to check\n * @returns {boolean} true if the given value is a date\n * @throws {TypeError} 1 arguments required\n *\n * @example\n * // For a valid date:\n * const result = isDate(new Date())\n * //=> true\n *\n * @example\n * // For an invalid date:\n * const result = isDate(new Date(NaN))\n * //=> true\n *\n * @example\n * // For some value:\n * const result = isDate('2014-02-31')\n * //=> false\n *\n * @example\n * // For an object:\n * const result = isDate({})\n * //=> false\n */\n\nexport default function isDate(value) {\n  requiredArgs(1, arguments);\n  return value instanceof Date || typeof value === 'object' && Object.prototype.toString.call(value) === '[object Date]';\n}"]}, "metadata": {}, "sourceType": "module"}
{"ast": null, "code": "import { OptionsColor } from \"../../OptionsColor\";\nimport { setRangeValue } from \"../../../../Utils\";\nexport class TwinkleValues {\n  constructor() {\n    this.enable = false;\n    this.frequency = 0.05;\n    this.opacity = 1;\n  }\n\n  load(data) {\n    if (data === undefined) {\n      return;\n    }\n\n    if (data.color !== undefined) {\n      this.color = OptionsColor.create(this.color, data.color);\n    }\n\n    if (data.enable !== undefined) {\n      this.enable = data.enable;\n    }\n\n    if (data.frequency !== undefined) {\n      this.frequency = data.frequency;\n    }\n\n    if (data.opacity !== undefined) {\n      this.opacity = setRangeValue(data.opacity);\n    }\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Options/Classes/Particles/Twinkle/TwinkleValues.js"], "names": ["OptionsColor", "setRangeValue", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "constructor", "enable", "frequency", "opacity", "load", "data", "undefined", "color", "create"], "mappings": "AAAA,SAASA,YAAT,QAA6B,oBAA7B;AACA,SAASC,aAAT,QAA8B,mBAA9B;AACA,OAAO,MAAMC,aAAN,CAAoB;AACvBC,EAAAA,WAAW,GAAG;AACV,SAAKC,MAAL,GAAc,KAAd;AACA,SAAKC,SAAL,GAAiB,IAAjB;AACA,SAAKC,OAAL,GAAe,CAAf;AACH;;AACDC,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,QAAIA,IAAI,KAAKC,SAAb,EAAwB;AACpB;AACH;;AACD,QAAID,IAAI,CAACE,KAAL,KAAeD,SAAnB,EAA8B;AAC1B,WAAKC,KAAL,GAAaV,YAAY,CAACW,MAAb,CAAoB,KAAKD,KAAzB,EAAgCF,IAAI,CAACE,KAArC,CAAb;AACH;;AACD,QAAIF,IAAI,CAACJ,MAAL,KAAgBK,SAApB,EAA+B;AAC3B,WAAKL,MAAL,GAAcI,IAAI,CAACJ,MAAnB;AACH;;AACD,QAAII,IAAI,CAACH,SAAL,KAAmBI,SAAvB,EAAkC;AAC9B,WAAKJ,SAAL,GAAiBG,IAAI,CAACH,SAAtB;AACH;;AACD,QAAIG,IAAI,CAACF,OAAL,KAAiBG,SAArB,EAAgC;AAC5B,WAAKH,OAAL,GAAeL,aAAa,CAACO,IAAI,CAACF,OAAN,CAA5B;AACH;AACJ;;AAtBsB", "sourcesContent": ["import { OptionsColor } from \"../../OptionsColor\";\nimport { setRangeValue } from \"../../../../Utils\";\nexport class TwinkleValues {\n    constructor() {\n        this.enable = false;\n        this.frequency = 0.05;\n        this.opacity = 1;\n    }\n    load(data) {\n        if (data === undefined) {\n            return;\n        }\n        if (data.color !== undefined) {\n            this.color = OptionsColor.create(this.color, data.color);\n        }\n        if (data.enable !== undefined) {\n            this.enable = data.enable;\n        }\n        if (data.frequency !== undefined) {\n            this.frequency = data.frequency;\n        }\n        if (data.opacity !== undefined) {\n            this.opacity = setRangeValue(data.opacity);\n        }\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
{"ast": null, "code": "import getWindow from \"./getWindow.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport getWindowScrollBarX from \"./getWindowScrollBarX.js\";\nexport default function getViewportRect(element) {\n  var win = getWindow(element);\n  var html = getDocumentElement(element);\n  var visualViewport = win.visualViewport;\n  var width = html.clientWidth;\n  var height = html.clientHeight;\n  var x = 0;\n  var y = 0; // NB: This isn't supported on iOS <= 12. If the keyboard is open, the popper\n  // can be obscured underneath it.\n  // Also, `html.clientHeight` adds the bottom bar height in Safari iOS, even\n  // if it isn't open, so if this isn't available, the popper will be detected\n  // to overflow the bottom of the screen too early.\n\n  if (visualViewport) {\n    width = visualViewport.width;\n    height = visualViewport.height; // Uses Layout Viewport (like Chrome; Safari does not currently)\n    // In Chrome, it returns a value very close to 0 (+/-) but contains rounding\n    // errors due to floating point numbers, so we need to check precision.\n    // Safari returns a number <= 0, usually < -1 when pinch-zoomed\n    // Feature detection fails in mobile emulation mode in Chrome.\n    // Math.abs(win.innerWidth / visualViewport.scale - visualViewport.width) <\n    // 0.001\n    // Fallback here: \"Not Safari\" userAgent\n\n    if (!/^((?!chrome|android).)*safari/i.test(navigator.userAgent)) {\n      x = visualViewport.offsetLeft;\n      y = visualViewport.offsetTop;\n    }\n  }\n\n  return {\n    width: width,\n    height: height,\n    x: x + getWindowScrollBarX(element),\n    y: y\n  };\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/@popperjs/core/lib/dom-utils/getViewportRect.js"], "names": ["getWindow", "getDocumentElement", "getWindowScrollBarX", "getViewportRect", "element", "win", "html", "visualViewport", "width", "clientWidth", "height", "clientHeight", "x", "y", "test", "navigator", "userAgent", "offsetLeft", "offsetTop"], "mappings": "AAAA,OAAOA,SAAP,MAAsB,gBAAtB;AACA,OAAOC,kBAAP,MAA+B,yBAA/B;AACA,OAAOC,mBAAP,MAAgC,0BAAhC;AACA,eAAe,SAASC,eAAT,CAAyBC,OAAzB,EAAkC;AAC/C,MAAIC,GAAG,GAAGL,SAAS,CAACI,OAAD,CAAnB;AACA,MAAIE,IAAI,GAAGL,kBAAkB,CAACG,OAAD,CAA7B;AACA,MAAIG,cAAc,GAAGF,GAAG,CAACE,cAAzB;AACA,MAAIC,KAAK,GAAGF,IAAI,CAACG,WAAjB;AACA,MAAIC,MAAM,GAAGJ,IAAI,CAACK,YAAlB;AACA,MAAIC,CAAC,GAAG,CAAR;AACA,MAAIC,CAAC,GAAG,CAAR,CAP+C,CAOpC;AACX;AACA;AACA;AACA;;AAEA,MAAIN,cAAJ,EAAoB;AAClBC,IAAAA,KAAK,GAAGD,cAAc,CAACC,KAAvB;AACAE,IAAAA,MAAM,GAAGH,cAAc,CAACG,MAAxB,CAFkB,CAEc;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,QAAI,CAAC,iCAAiCI,IAAjC,CAAsCC,SAAS,CAACC,SAAhD,CAAL,EAAiE;AAC/DJ,MAAAA,CAAC,GAAGL,cAAc,CAACU,UAAnB;AACAJ,MAAAA,CAAC,GAAGN,cAAc,CAACW,SAAnB;AACD;AACF;;AAED,SAAO;AACLV,IAAAA,KAAK,EAAEA,KADF;AAELE,IAAAA,MAAM,EAAEA,MAFH;AAGLE,IAAAA,CAAC,EAAEA,CAAC,GAAGV,mBAAmB,CAACE,OAAD,CAHrB;AAILS,IAAAA,CAAC,EAAEA;AAJE,GAAP;AAMD", "sourcesContent": ["import getWindow from \"./getWindow.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport getWindowScrollBarX from \"./getWindowScrollBarX.js\";\nexport default function getViewportRect(element) {\n  var win = getWindow(element);\n  var html = getDocumentElement(element);\n  var visualViewport = win.visualViewport;\n  var width = html.clientWidth;\n  var height = html.clientHeight;\n  var x = 0;\n  var y = 0; // NB: This isn't supported on iOS <= 12. If the keyboard is open, the popper\n  // can be obscured underneath it.\n  // Also, `html.clientHeight` adds the bottom bar height in Safari iOS, even\n  // if it isn't open, so if this isn't available, the popper will be detected\n  // to overflow the bottom of the screen too early.\n\n  if (visualViewport) {\n    width = visualViewport.width;\n    height = visualViewport.height; // Uses Layout Viewport (like Chrome; Safari does not currently)\n    // In Chrome, it returns a value very close to 0 (+/-) but contains rounding\n    // errors due to floating point numbers, so we need to check precision.\n    // Safari returns a number <= 0, usually < -1 when pinch-zoomed\n    // Feature detection fails in mobile emulation mode in Chrome.\n    // Math.abs(win.innerWidth / visualViewport.scale - visualViewport.width) <\n    // 0.001\n    // Fallback here: \"Not Safari\" userAgent\n\n    if (!/^((?!chrome|android).)*safari/i.test(navigator.userAgent)) {\n      x = visualViewport.offsetLeft;\n      y = visualViewport.offsetTop;\n    }\n  }\n\n  return {\n    width: width,\n    height: height,\n    x: x + getWindowScrollBarX(element),\n    y: y\n  };\n}"]}, "metadata": {}, "sourceType": "module"}
{"ast": null, "code": "export function drawEmoji(data) {\n  const {\n      context,\n      particle,\n      radius,\n      opacity\n    } = data,\n    emojiData = particle.emojiData,\n    double = 2,\n    diameter = radius * double,\n    previousAlpha = context.globalAlpha;\n  if (!emojiData) {\n    return;\n  }\n  context.globalAlpha = opacity;\n  context.drawImage(emojiData, -radius, -radius, diameter, diameter);\n  context.globalAlpha = previousAlpha;\n}", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "data", "context", "particle", "radius", "opacity", "emojiData", "double", "diameter", "previousAlpha", "globalAlpha", "drawImage"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/shape-emoji/browser/Utils.js"], "sourcesContent": ["export function drawEmoji(data) {\n    const { context, particle, radius, opacity } = data, emojiData = particle.emojiData, double = 2, diameter = radius * double, previousAlpha = context.globalAlpha;\n    if (!emojiData) {\n        return;\n    }\n    context.globalAlpha = opacity;\n    context.drawImage(emojiData, -radius, -radius, diameter, diameter);\n    context.globalAlpha = previousAlpha;\n}\n"], "mappings": "AAAA,OAAO,SAASA,SAASA,CAACC,IAAI,EAAE;EAC5B,MAAM;MAAEC,OAAO;MAAEC,QAAQ;MAAEC,MAAM;MAAEC;IAAQ,CAAC,GAAGJ,IAAI;IAAEK,SAAS,GAAGH,QAAQ,CAACG,SAAS;IAAEC,MAAM,GAAG,CAAC;IAAEC,QAAQ,GAAGJ,MAAM,GAAGG,MAAM;IAAEE,aAAa,GAAGP,OAAO,CAACQ,WAAW;EAChK,IAAI,CAACJ,SAAS,EAAE;IACZ;EACJ;EACAJ,OAAO,CAACQ,WAAW,GAAGL,OAAO;EAC7BH,OAAO,CAACS,SAAS,CAACL,SAAS,EAAE,CAACF,MAAM,EAAE,CAACA,MAAM,EAAEI,QAAQ,EAAEA,QAAQ,CAAC;EAClEN,OAAO,CAACQ,WAAW,GAAGD,aAAa;AACvC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
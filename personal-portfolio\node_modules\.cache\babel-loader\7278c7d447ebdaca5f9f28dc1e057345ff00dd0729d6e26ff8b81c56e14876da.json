{"ast": null, "code": "import { RotateDirection, Vector, calcPositionOrRandomFromSize, calcPositionOrRandomFromSizeRanged, getDistance, getDistances, getRandom, getRangeValue, getStyleFromRgb, isPointInside, percentDenominator, rangeColorToRgb } from \"@tsparticles/engine\";\nimport { Absorber } from \"./Options/Classes/Absorber.js\";\nconst squareExp = 2,\n  absorbFactor = 0.033,\n  minOrbitLength = 0,\n  minRadius = 0,\n  minMass = 0,\n  origin = {\n    x: 0,\n    y: 0\n  },\n  minAngle = 0,\n  double = 2,\n  maxAngle = Math.PI * double,\n  minVelocity = 0;\nexport class AbsorberInstance {\n  constructor(absorbers, container, options, position) {\n    this.absorbers = absorbers;\n    this.container = container;\n    this._calcPosition = () => {\n      const exactPosition = calcPositionOrRandomFromSizeRanged({\n        size: this.container.canvas.size,\n        position: this.options.position\n      });\n      return Vector.create(exactPosition.x, exactPosition.y);\n    };\n    this._updateParticlePosition = (particle, v) => {\n      if (particle.destroyed) {\n        return;\n      }\n      const container = this.container,\n        canvasSize = container.canvas.size;\n      if (particle.needsNewPosition) {\n        const newPosition = calcPositionOrRandomFromSize({\n          size: canvasSize\n        });\n        particle.position.setTo(newPosition);\n        particle.velocity.setTo(particle.initialVelocity);\n        particle.absorberOrbit = undefined;\n        particle.needsNewPosition = false;\n      }\n      if (this.options.orbits) {\n        if (particle.absorberOrbit === undefined) {\n          particle.absorberOrbit = Vector.origin;\n          particle.absorberOrbit.length = getDistance(particle.getPosition(), this.position);\n          particle.absorberOrbit.angle = getRandom() * maxAngle;\n        }\n        if (particle.absorberOrbit.length <= this.size && !this.options.destroy) {\n          const minSize = Math.min(canvasSize.width, canvasSize.height),\n            offset = 1,\n            randomOffset = 0.1,\n            randomFactor = 0.2;\n          particle.absorberOrbit.length = minSize * (offset + (getRandom() * randomFactor - randomOffset));\n        }\n        if (particle.absorberOrbitDirection === undefined) {\n          particle.absorberOrbitDirection = particle.velocity.x >= minVelocity ? RotateDirection.clockwise : RotateDirection.counterClockwise;\n        }\n        const orbitRadius = particle.absorberOrbit.length,\n          orbitAngle = particle.absorberOrbit.angle,\n          orbitDirection = particle.absorberOrbitDirection;\n        particle.velocity.setTo(Vector.origin);\n        const updateFunc = {\n          x: orbitDirection === RotateDirection.clockwise ? Math.cos : Math.sin,\n          y: orbitDirection === RotateDirection.clockwise ? Math.sin : Math.cos\n        };\n        particle.position.x = this.position.x + orbitRadius * updateFunc.x(orbitAngle);\n        particle.position.y = this.position.y + orbitRadius * updateFunc.y(orbitAngle);\n        particle.absorberOrbit.length -= v.length;\n        particle.absorberOrbit.angle += (particle.retina.moveSpeed ?? minVelocity) * container.retina.pixelRatio / percentDenominator * container.retina.reduceFactor;\n      } else {\n        const addV = Vector.origin;\n        addV.length = v.length;\n        addV.angle = v.angle;\n        particle.velocity.addTo(addV);\n      }\n    };\n    this.initialPosition = position ? Vector.create(position.x, position.y) : undefined;\n    if (options instanceof Absorber) {\n      this.options = options;\n    } else {\n      this.options = new Absorber();\n      this.options.load(options);\n    }\n    this.dragging = false;\n    this.name = this.options.name;\n    this.opacity = this.options.opacity;\n    this.size = getRangeValue(this.options.size.value) * container.retina.pixelRatio;\n    this.mass = this.size * this.options.size.density * container.retina.reduceFactor;\n    const limit = this.options.size.limit;\n    this.limit = {\n      radius: limit.radius * container.retina.pixelRatio * container.retina.reduceFactor,\n      mass: limit.mass\n    };\n    this.color = rangeColorToRgb(this.options.color) ?? {\n      b: 0,\n      g: 0,\n      r: 0\n    };\n    this.position = this.initialPosition?.copy() ?? this._calcPosition();\n  }\n  attract(particle) {\n    const container = this.container,\n      options = this.options;\n    if (options.draggable) {\n      const mouse = container.interactivity.mouse;\n      if (mouse.clicking && mouse.downPosition) {\n        const mouseDist = getDistance(this.position, mouse.downPosition);\n        if (mouseDist <= this.size) {\n          this.dragging = true;\n        }\n      } else {\n        this.dragging = false;\n      }\n      if (this.dragging && mouse.position) {\n        this.position.x = mouse.position.x;\n        this.position.y = mouse.position.y;\n      }\n    }\n    const pos = particle.getPosition(),\n      {\n        dx,\n        dy,\n        distance\n      } = getDistances(this.position, pos),\n      v = Vector.create(dx, dy);\n    v.length = this.mass / Math.pow(distance, squareExp) * container.retina.reduceFactor;\n    if (distance < this.size + particle.getRadius()) {\n      const sizeFactor = particle.getRadius() * absorbFactor * container.retina.pixelRatio;\n      if (this.size > particle.getRadius() && distance < this.size - particle.getRadius() || particle.absorberOrbit !== undefined && particle.absorberOrbit.length < minOrbitLength) {\n        if (options.destroy) {\n          particle.destroy();\n        } else {\n          particle.needsNewPosition = true;\n          this._updateParticlePosition(particle, v);\n        }\n      } else {\n        if (options.destroy) {\n          particle.size.value -= sizeFactor;\n        }\n        this._updateParticlePosition(particle, v);\n      }\n      if (this.limit.radius <= minRadius || this.size < this.limit.radius) {\n        this.size += sizeFactor;\n      }\n      if (this.limit.mass <= minMass || this.mass < this.limit.mass) {\n        this.mass += sizeFactor * this.options.size.density * container.retina.reduceFactor;\n      }\n    } else {\n      this._updateParticlePosition(particle, v);\n    }\n  }\n  draw(context) {\n    context.translate(this.position.x, this.position.y);\n    context.beginPath();\n    context.arc(origin.x, origin.y, this.size, minAngle, maxAngle, false);\n    context.closePath();\n    context.fillStyle = getStyleFromRgb(this.color, this.opacity);\n    context.fill();\n  }\n  resize() {\n    const initialPosition = this.initialPosition;\n    this.position = initialPosition && isPointInside(initialPosition, this.container.canvas.size, Vector.origin) ? initialPosition : this._calcPosition();\n  }\n}", "map": {"version": 3, "names": ["RotateDirection", "Vector", "calcPositionOrRandomFromSize", "calcPositionOrRandomFromSizeRanged", "getDistance", "getDistances", "getRandom", "getRangeValue", "getStyleFromRgb", "isPointInside", "percentDenominator", "rangeColorToRgb", "Absorber", "squareExp", "absorbFactor", "minOrbitLength", "minRadius", "minMass", "origin", "x", "y", "minAngle", "double", "maxAngle", "Math", "PI", "minVelocity", "AbsorberInstance", "constructor", "absorbers", "container", "options", "position", "_calcPosition", "exactPosition", "size", "canvas", "create", "_updateParticlePosition", "particle", "v", "destroyed", "canvasSize", "needsNewPosition", "newPosition", "setTo", "velocity", "initialVelocity", "absorberOrbit", "undefined", "orbits", "length", "getPosition", "angle", "destroy", "minSize", "min", "width", "height", "offset", "randomOffset", "randomFactor", "absorberOrbitDirection", "clockwise", "counterClockwise", "orbitRadius", "orbitAngle", "orbitDirection", "updateFunc", "cos", "sin", "retina", "moveSpeed", "pixelRatio", "reduceFactor", "addV", "addTo", "initialPosition", "load", "dragging", "name", "opacity", "value", "mass", "density", "limit", "radius", "color", "b", "g", "r", "copy", "attract", "draggable", "mouse", "interactivity", "clicking", "downPosition", "mouseDist", "pos", "dx", "dy", "distance", "pow", "getRadius", "sizeFactor", "draw", "context", "translate", "beginPath", "arc", "closePath", "fillStyle", "fill", "resize"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/plugin-absorbers/browser/AbsorberInstance.js"], "sourcesContent": ["import { RotateDirection, Vector, calcPositionOrRandomFromSize, calcPositionOrRandomFromSizeRanged, getDistance, getDistances, getRandom, getRangeValue, getStyleFromRgb, isPointInside, percentDenominator, rangeColorToRgb, } from \"@tsparticles/engine\";\nimport { Absorber } from \"./Options/Classes/Absorber.js\";\nconst squareExp = 2, absorbFactor = 0.033, minOrbitLength = 0, minRadius = 0, minMass = 0, origin = {\n    x: 0,\n    y: 0,\n}, minAngle = 0, double = 2, maxAngle = Math.PI * double, minVelocity = 0;\nexport class AbsorberInstance {\n    constructor(absorbers, container, options, position) {\n        this.absorbers = absorbers;\n        this.container = container;\n        this._calcPosition = () => {\n            const exactPosition = calcPositionOrRandomFromSizeRanged({\n                size: this.container.canvas.size,\n                position: this.options.position,\n            });\n            return Vector.create(exactPosition.x, exactPosition.y);\n        };\n        this._updateParticlePosition = (particle, v) => {\n            if (particle.destroyed) {\n                return;\n            }\n            const container = this.container, canvasSize = container.canvas.size;\n            if (particle.needsNewPosition) {\n                const newPosition = calcPositionOrRandomFromSize({ size: canvasSize });\n                particle.position.setTo(newPosition);\n                particle.velocity.setTo(particle.initialVelocity);\n                particle.absorberOrbit = undefined;\n                particle.needsNewPosition = false;\n            }\n            if (this.options.orbits) {\n                if (particle.absorberOrbit === undefined) {\n                    particle.absorberOrbit = Vector.origin;\n                    particle.absorberOrbit.length = getDistance(particle.getPosition(), this.position);\n                    particle.absorberOrbit.angle = getRandom() * maxAngle;\n                }\n                if (particle.absorberOrbit.length <= this.size && !this.options.destroy) {\n                    const minSize = Math.min(canvasSize.width, canvasSize.height), offset = 1, randomOffset = 0.1, randomFactor = 0.2;\n                    particle.absorberOrbit.length = minSize * (offset + (getRandom() * randomFactor - randomOffset));\n                }\n                if (particle.absorberOrbitDirection === undefined) {\n                    particle.absorberOrbitDirection =\n                        particle.velocity.x >= minVelocity ? RotateDirection.clockwise : RotateDirection.counterClockwise;\n                }\n                const orbitRadius = particle.absorberOrbit.length, orbitAngle = particle.absorberOrbit.angle, orbitDirection = particle.absorberOrbitDirection;\n                particle.velocity.setTo(Vector.origin);\n                const updateFunc = {\n                    x: orbitDirection === RotateDirection.clockwise ? Math.cos : Math.sin,\n                    y: orbitDirection === RotateDirection.clockwise ? Math.sin : Math.cos,\n                };\n                particle.position.x = this.position.x + orbitRadius * updateFunc.x(orbitAngle);\n                particle.position.y = this.position.y + orbitRadius * updateFunc.y(orbitAngle);\n                particle.absorberOrbit.length -= v.length;\n                particle.absorberOrbit.angle +=\n                    (((particle.retina.moveSpeed ?? minVelocity) * container.retina.pixelRatio) / percentDenominator) *\n                        container.retina.reduceFactor;\n            }\n            else {\n                const addV = Vector.origin;\n                addV.length = v.length;\n                addV.angle = v.angle;\n                particle.velocity.addTo(addV);\n            }\n        };\n        this.initialPosition = position ? Vector.create(position.x, position.y) : undefined;\n        if (options instanceof Absorber) {\n            this.options = options;\n        }\n        else {\n            this.options = new Absorber();\n            this.options.load(options);\n        }\n        this.dragging = false;\n        this.name = this.options.name;\n        this.opacity = this.options.opacity;\n        this.size = getRangeValue(this.options.size.value) * container.retina.pixelRatio;\n        this.mass = this.size * this.options.size.density * container.retina.reduceFactor;\n        const limit = this.options.size.limit;\n        this.limit = {\n            radius: limit.radius * container.retina.pixelRatio * container.retina.reduceFactor,\n            mass: limit.mass,\n        };\n        this.color = rangeColorToRgb(this.options.color) ?? {\n            b: 0,\n            g: 0,\n            r: 0,\n        };\n        this.position = this.initialPosition?.copy() ?? this._calcPosition();\n    }\n    attract(particle) {\n        const container = this.container, options = this.options;\n        if (options.draggable) {\n            const mouse = container.interactivity.mouse;\n            if (mouse.clicking && mouse.downPosition) {\n                const mouseDist = getDistance(this.position, mouse.downPosition);\n                if (mouseDist <= this.size) {\n                    this.dragging = true;\n                }\n            }\n            else {\n                this.dragging = false;\n            }\n            if (this.dragging && mouse.position) {\n                this.position.x = mouse.position.x;\n                this.position.y = mouse.position.y;\n            }\n        }\n        const pos = particle.getPosition(), { dx, dy, distance } = getDistances(this.position, pos), v = Vector.create(dx, dy);\n        v.length = (this.mass / Math.pow(distance, squareExp)) * container.retina.reduceFactor;\n        if (distance < this.size + particle.getRadius()) {\n            const sizeFactor = particle.getRadius() * absorbFactor * container.retina.pixelRatio;\n            if ((this.size > particle.getRadius() && distance < this.size - particle.getRadius()) ||\n                (particle.absorberOrbit !== undefined && particle.absorberOrbit.length < minOrbitLength)) {\n                if (options.destroy) {\n                    particle.destroy();\n                }\n                else {\n                    particle.needsNewPosition = true;\n                    this._updateParticlePosition(particle, v);\n                }\n            }\n            else {\n                if (options.destroy) {\n                    particle.size.value -= sizeFactor;\n                }\n                this._updateParticlePosition(particle, v);\n            }\n            if (this.limit.radius <= minRadius || this.size < this.limit.radius) {\n                this.size += sizeFactor;\n            }\n            if (this.limit.mass <= minMass || this.mass < this.limit.mass) {\n                this.mass += sizeFactor * this.options.size.density * container.retina.reduceFactor;\n            }\n        }\n        else {\n            this._updateParticlePosition(particle, v);\n        }\n    }\n    draw(context) {\n        context.translate(this.position.x, this.position.y);\n        context.beginPath();\n        context.arc(origin.x, origin.y, this.size, minAngle, maxAngle, false);\n        context.closePath();\n        context.fillStyle = getStyleFromRgb(this.color, this.opacity);\n        context.fill();\n    }\n    resize() {\n        const initialPosition = this.initialPosition;\n        this.position =\n            initialPosition && isPointInside(initialPosition, this.container.canvas.size, Vector.origin)\n                ? initialPosition\n                : this._calcPosition();\n    }\n}\n"], "mappings": "AAAA,SAASA,eAAe,EAAEC,MAAM,EAAEC,4BAA4B,EAAEC,kCAAkC,EAAEC,WAAW,EAAEC,YAAY,EAAEC,SAAS,EAAEC,aAAa,EAAEC,eAAe,EAAEC,aAAa,EAAEC,kBAAkB,EAAEC,eAAe,QAAS,qBAAqB;AAC1P,SAASC,QAAQ,QAAQ,+BAA+B;AACxD,MAAMC,SAAS,GAAG,CAAC;EAAEC,YAAY,GAAG,KAAK;EAAEC,cAAc,GAAG,CAAC;EAAEC,SAAS,GAAG,CAAC;EAAEC,OAAO,GAAG,CAAC;EAAEC,MAAM,GAAG;IAChGC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE;EACP,CAAC;EAAEC,QAAQ,GAAG,CAAC;EAAEC,MAAM,GAAG,CAAC;EAAEC,QAAQ,GAAGC,IAAI,CAACC,EAAE,GAAGH,MAAM;EAAEI,WAAW,GAAG,CAAC;AACzE,OAAO,MAAMC,gBAAgB,CAAC;EAC1BC,WAAWA,CAACC,SAAS,EAAEC,SAAS,EAAEC,OAAO,EAAEC,QAAQ,EAAE;IACjD,IAAI,CAACH,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACG,aAAa,GAAG,MAAM;MACvB,MAAMC,aAAa,GAAG/B,kCAAkC,CAAC;QACrDgC,IAAI,EAAE,IAAI,CAACL,SAAS,CAACM,MAAM,CAACD,IAAI;QAChCH,QAAQ,EAAE,IAAI,CAACD,OAAO,CAACC;MAC3B,CAAC,CAAC;MACF,OAAO/B,MAAM,CAACoC,MAAM,CAACH,aAAa,CAACf,CAAC,EAAEe,aAAa,CAACd,CAAC,CAAC;IAC1D,CAAC;IACD,IAAI,CAACkB,uBAAuB,GAAG,CAACC,QAAQ,EAAEC,CAAC,KAAK;MAC5C,IAAID,QAAQ,CAACE,SAAS,EAAE;QACpB;MACJ;MACA,MAAMX,SAAS,GAAG,IAAI,CAACA,SAAS;QAAEY,UAAU,GAAGZ,SAAS,CAACM,MAAM,CAACD,IAAI;MACpE,IAAII,QAAQ,CAACI,gBAAgB,EAAE;QAC3B,MAAMC,WAAW,GAAG1C,4BAA4B,CAAC;UAAEiC,IAAI,EAAEO;QAAW,CAAC,CAAC;QACtEH,QAAQ,CAACP,QAAQ,CAACa,KAAK,CAACD,WAAW,CAAC;QACpCL,QAAQ,CAACO,QAAQ,CAACD,KAAK,CAACN,QAAQ,CAACQ,eAAe,CAAC;QACjDR,QAAQ,CAACS,aAAa,GAAGC,SAAS;QAClCV,QAAQ,CAACI,gBAAgB,GAAG,KAAK;MACrC;MACA,IAAI,IAAI,CAACZ,OAAO,CAACmB,MAAM,EAAE;QACrB,IAAIX,QAAQ,CAACS,aAAa,KAAKC,SAAS,EAAE;UACtCV,QAAQ,CAACS,aAAa,GAAG/C,MAAM,CAACiB,MAAM;UACtCqB,QAAQ,CAACS,aAAa,CAACG,MAAM,GAAG/C,WAAW,CAACmC,QAAQ,CAACa,WAAW,CAAC,CAAC,EAAE,IAAI,CAACpB,QAAQ,CAAC;UAClFO,QAAQ,CAACS,aAAa,CAACK,KAAK,GAAG/C,SAAS,CAAC,CAAC,GAAGiB,QAAQ;QACzD;QACA,IAAIgB,QAAQ,CAACS,aAAa,CAACG,MAAM,IAAI,IAAI,CAAChB,IAAI,IAAI,CAAC,IAAI,CAACJ,OAAO,CAACuB,OAAO,EAAE;UACrE,MAAMC,OAAO,GAAG/B,IAAI,CAACgC,GAAG,CAACd,UAAU,CAACe,KAAK,EAAEf,UAAU,CAACgB,MAAM,CAAC;YAAEC,MAAM,GAAG,CAAC;YAAEC,YAAY,GAAG,GAAG;YAAEC,YAAY,GAAG,GAAG;UACjHtB,QAAQ,CAACS,aAAa,CAACG,MAAM,GAAGI,OAAO,IAAII,MAAM,IAAIrD,SAAS,CAAC,CAAC,GAAGuD,YAAY,GAAGD,YAAY,CAAC,CAAC;QACpG;QACA,IAAIrB,QAAQ,CAACuB,sBAAsB,KAAKb,SAAS,EAAE;UAC/CV,QAAQ,CAACuB,sBAAsB,GAC3BvB,QAAQ,CAACO,QAAQ,CAAC3B,CAAC,IAAIO,WAAW,GAAG1B,eAAe,CAAC+D,SAAS,GAAG/D,eAAe,CAACgE,gBAAgB;QACzG;QACA,MAAMC,WAAW,GAAG1B,QAAQ,CAACS,aAAa,CAACG,MAAM;UAAEe,UAAU,GAAG3B,QAAQ,CAACS,aAAa,CAACK,KAAK;UAAEc,cAAc,GAAG5B,QAAQ,CAACuB,sBAAsB;QAC9IvB,QAAQ,CAACO,QAAQ,CAACD,KAAK,CAAC5C,MAAM,CAACiB,MAAM,CAAC;QACtC,MAAMkD,UAAU,GAAG;UACfjD,CAAC,EAAEgD,cAAc,KAAKnE,eAAe,CAAC+D,SAAS,GAAGvC,IAAI,CAAC6C,GAAG,GAAG7C,IAAI,CAAC8C,GAAG;UACrElD,CAAC,EAAE+C,cAAc,KAAKnE,eAAe,CAAC+D,SAAS,GAAGvC,IAAI,CAAC8C,GAAG,GAAG9C,IAAI,CAAC6C;QACtE,CAAC;QACD9B,QAAQ,CAACP,QAAQ,CAACb,CAAC,GAAG,IAAI,CAACa,QAAQ,CAACb,CAAC,GAAG8C,WAAW,GAAGG,UAAU,CAACjD,CAAC,CAAC+C,UAAU,CAAC;QAC9E3B,QAAQ,CAACP,QAAQ,CAACZ,CAAC,GAAG,IAAI,CAACY,QAAQ,CAACZ,CAAC,GAAG6C,WAAW,GAAGG,UAAU,CAAChD,CAAC,CAAC8C,UAAU,CAAC;QAC9E3B,QAAQ,CAACS,aAAa,CAACG,MAAM,IAAIX,CAAC,CAACW,MAAM;QACzCZ,QAAQ,CAACS,aAAa,CAACK,KAAK,IACtB,CAACd,QAAQ,CAACgC,MAAM,CAACC,SAAS,IAAI9C,WAAW,IAAII,SAAS,CAACyC,MAAM,CAACE,UAAU,GAAI/D,kBAAkB,GAC5FoB,SAAS,CAACyC,MAAM,CAACG,YAAY;MACzC,CAAC,MACI;QACD,MAAMC,IAAI,GAAG1E,MAAM,CAACiB,MAAM;QAC1ByD,IAAI,CAACxB,MAAM,GAAGX,CAAC,CAACW,MAAM;QACtBwB,IAAI,CAACtB,KAAK,GAAGb,CAAC,CAACa,KAAK;QACpBd,QAAQ,CAACO,QAAQ,CAAC8B,KAAK,CAACD,IAAI,CAAC;MACjC;IACJ,CAAC;IACD,IAAI,CAACE,eAAe,GAAG7C,QAAQ,GAAG/B,MAAM,CAACoC,MAAM,CAACL,QAAQ,CAACb,CAAC,EAAEa,QAAQ,CAACZ,CAAC,CAAC,GAAG6B,SAAS;IACnF,IAAIlB,OAAO,YAAYnB,QAAQ,EAAE;MAC7B,IAAI,CAACmB,OAAO,GAAGA,OAAO;IAC1B,CAAC,MACI;MACD,IAAI,CAACA,OAAO,GAAG,IAAInB,QAAQ,CAAC,CAAC;MAC7B,IAAI,CAACmB,OAAO,CAAC+C,IAAI,CAAC/C,OAAO,CAAC;IAC9B;IACA,IAAI,CAACgD,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,IAAI,GAAG,IAAI,CAACjD,OAAO,CAACiD,IAAI;IAC7B,IAAI,CAACC,OAAO,GAAG,IAAI,CAAClD,OAAO,CAACkD,OAAO;IACnC,IAAI,CAAC9C,IAAI,GAAG5B,aAAa,CAAC,IAAI,CAACwB,OAAO,CAACI,IAAI,CAAC+C,KAAK,CAAC,GAAGpD,SAAS,CAACyC,MAAM,CAACE,UAAU;IAChF,IAAI,CAACU,IAAI,GAAG,IAAI,CAAChD,IAAI,GAAG,IAAI,CAACJ,OAAO,CAACI,IAAI,CAACiD,OAAO,GAAGtD,SAAS,CAACyC,MAAM,CAACG,YAAY;IACjF,MAAMW,KAAK,GAAG,IAAI,CAACtD,OAAO,CAACI,IAAI,CAACkD,KAAK;IACrC,IAAI,CAACA,KAAK,GAAG;MACTC,MAAM,EAAED,KAAK,CAACC,MAAM,GAAGxD,SAAS,CAACyC,MAAM,CAACE,UAAU,GAAG3C,SAAS,CAACyC,MAAM,CAACG,YAAY;MAClFS,IAAI,EAAEE,KAAK,CAACF;IAChB,CAAC;IACD,IAAI,CAACI,KAAK,GAAG5E,eAAe,CAAC,IAAI,CAACoB,OAAO,CAACwD,KAAK,CAAC,IAAI;MAChDC,CAAC,EAAE,CAAC;MACJC,CAAC,EAAE,CAAC;MACJC,CAAC,EAAE;IACP,CAAC;IACD,IAAI,CAAC1D,QAAQ,GAAG,IAAI,CAAC6C,eAAe,EAAEc,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC1D,aAAa,CAAC,CAAC;EACxE;EACA2D,OAAOA,CAACrD,QAAQ,EAAE;IACd,MAAMT,SAAS,GAAG,IAAI,CAACA,SAAS;MAAEC,OAAO,GAAG,IAAI,CAACA,OAAO;IACxD,IAAIA,OAAO,CAAC8D,SAAS,EAAE;MACnB,MAAMC,KAAK,GAAGhE,SAAS,CAACiE,aAAa,CAACD,KAAK;MAC3C,IAAIA,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACG,YAAY,EAAE;QACtC,MAAMC,SAAS,GAAG9F,WAAW,CAAC,IAAI,CAAC4B,QAAQ,EAAE8D,KAAK,CAACG,YAAY,CAAC;QAChE,IAAIC,SAAS,IAAI,IAAI,CAAC/D,IAAI,EAAE;UACxB,IAAI,CAAC4C,QAAQ,GAAG,IAAI;QACxB;MACJ,CAAC,MACI;QACD,IAAI,CAACA,QAAQ,GAAG,KAAK;MACzB;MACA,IAAI,IAAI,CAACA,QAAQ,IAAIe,KAAK,CAAC9D,QAAQ,EAAE;QACjC,IAAI,CAACA,QAAQ,CAACb,CAAC,GAAG2E,KAAK,CAAC9D,QAAQ,CAACb,CAAC;QAClC,IAAI,CAACa,QAAQ,CAACZ,CAAC,GAAG0E,KAAK,CAAC9D,QAAQ,CAACZ,CAAC;MACtC;IACJ;IACA,MAAM+E,GAAG,GAAG5D,QAAQ,CAACa,WAAW,CAAC,CAAC;MAAE;QAAEgD,EAAE;QAAEC,EAAE;QAAEC;MAAS,CAAC,GAAGjG,YAAY,CAAC,IAAI,CAAC2B,QAAQ,EAAEmE,GAAG,CAAC;MAAE3D,CAAC,GAAGvC,MAAM,CAACoC,MAAM,CAAC+D,EAAE,EAAEC,EAAE,CAAC;IACtH7D,CAAC,CAACW,MAAM,GAAI,IAAI,CAACgC,IAAI,GAAG3D,IAAI,CAAC+E,GAAG,CAACD,QAAQ,EAAEzF,SAAS,CAAC,GAAIiB,SAAS,CAACyC,MAAM,CAACG,YAAY;IACtF,IAAI4B,QAAQ,GAAG,IAAI,CAACnE,IAAI,GAAGI,QAAQ,CAACiE,SAAS,CAAC,CAAC,EAAE;MAC7C,MAAMC,UAAU,GAAGlE,QAAQ,CAACiE,SAAS,CAAC,CAAC,GAAG1F,YAAY,GAAGgB,SAAS,CAACyC,MAAM,CAACE,UAAU;MACpF,IAAK,IAAI,CAACtC,IAAI,GAAGI,QAAQ,CAACiE,SAAS,CAAC,CAAC,IAAIF,QAAQ,GAAG,IAAI,CAACnE,IAAI,GAAGI,QAAQ,CAACiE,SAAS,CAAC,CAAC,IAC/EjE,QAAQ,CAACS,aAAa,KAAKC,SAAS,IAAIV,QAAQ,CAACS,aAAa,CAACG,MAAM,GAAGpC,cAAe,EAAE;QAC1F,IAAIgB,OAAO,CAACuB,OAAO,EAAE;UACjBf,QAAQ,CAACe,OAAO,CAAC,CAAC;QACtB,CAAC,MACI;UACDf,QAAQ,CAACI,gBAAgB,GAAG,IAAI;UAChC,IAAI,CAACL,uBAAuB,CAACC,QAAQ,EAAEC,CAAC,CAAC;QAC7C;MACJ,CAAC,MACI;QACD,IAAIT,OAAO,CAACuB,OAAO,EAAE;UACjBf,QAAQ,CAACJ,IAAI,CAAC+C,KAAK,IAAIuB,UAAU;QACrC;QACA,IAAI,CAACnE,uBAAuB,CAACC,QAAQ,EAAEC,CAAC,CAAC;MAC7C;MACA,IAAI,IAAI,CAAC6C,KAAK,CAACC,MAAM,IAAItE,SAAS,IAAI,IAAI,CAACmB,IAAI,GAAG,IAAI,CAACkD,KAAK,CAACC,MAAM,EAAE;QACjE,IAAI,CAACnD,IAAI,IAAIsE,UAAU;MAC3B;MACA,IAAI,IAAI,CAACpB,KAAK,CAACF,IAAI,IAAIlE,OAAO,IAAI,IAAI,CAACkE,IAAI,GAAG,IAAI,CAACE,KAAK,CAACF,IAAI,EAAE;QAC3D,IAAI,CAACA,IAAI,IAAIsB,UAAU,GAAG,IAAI,CAAC1E,OAAO,CAACI,IAAI,CAACiD,OAAO,GAAGtD,SAAS,CAACyC,MAAM,CAACG,YAAY;MACvF;IACJ,CAAC,MACI;MACD,IAAI,CAACpC,uBAAuB,CAACC,QAAQ,EAAEC,CAAC,CAAC;IAC7C;EACJ;EACAkE,IAAIA,CAACC,OAAO,EAAE;IACVA,OAAO,CAACC,SAAS,CAAC,IAAI,CAAC5E,QAAQ,CAACb,CAAC,EAAE,IAAI,CAACa,QAAQ,CAACZ,CAAC,CAAC;IACnDuF,OAAO,CAACE,SAAS,CAAC,CAAC;IACnBF,OAAO,CAACG,GAAG,CAAC5F,MAAM,CAACC,CAAC,EAAED,MAAM,CAACE,CAAC,EAAE,IAAI,CAACe,IAAI,EAAEd,QAAQ,EAAEE,QAAQ,EAAE,KAAK,CAAC;IACrEoF,OAAO,CAACI,SAAS,CAAC,CAAC;IACnBJ,OAAO,CAACK,SAAS,GAAGxG,eAAe,CAAC,IAAI,CAAC+E,KAAK,EAAE,IAAI,CAACN,OAAO,CAAC;IAC7D0B,OAAO,CAACM,IAAI,CAAC,CAAC;EAClB;EACAC,MAAMA,CAAA,EAAG;IACL,MAAMrC,eAAe,GAAG,IAAI,CAACA,eAAe;IAC5C,IAAI,CAAC7C,QAAQ,GACT6C,eAAe,IAAIpE,aAAa,CAACoE,eAAe,EAAE,IAAI,CAAC/C,SAAS,CAACM,MAAM,CAACD,IAAI,EAAElC,MAAM,CAACiB,MAAM,CAAC,GACtF2D,eAAe,GACf,IAAI,CAAC5C,aAAa,CAAC,CAAC;EAClC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { LinkInstance } from \"./LinkInstance.js\";\nexport class LinksPlugin {\n  constructor() {\n    this.id = \"links\";\n  }\n  getPlugin(container) {\n    return Promise.resolve(new LinkInstance(container));\n  }\n  loadOptions() {}\n  needsPlugin() {\n    return true;\n  }\n}", "map": {"version": 3, "names": ["LinkInstance", "LinksPlugin", "constructor", "id", "getPlugin", "container", "Promise", "resolve", "loadOptions", "needsPlugin"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/interaction-particles-links/browser/LinksPlugin.js"], "sourcesContent": ["import { LinkInstance } from \"./LinkInstance.js\";\nexport class LinksPlugin {\n    constructor() {\n        this.id = \"links\";\n    }\n    getPlugin(container) {\n        return Promise.resolve(new LinkInstance(container));\n    }\n    loadOptions() {\n    }\n    needsPlugin() {\n        return true;\n    }\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,mBAAmB;AAChD,OAAO,MAAMC,WAAW,CAAC;EACrBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,EAAE,GAAG,OAAO;EACrB;EACAC,SAASA,CAACC,SAAS,EAAE;IACjB,OAAOC,OAAO,CAACC,OAAO,CAAC,IAAIP,YAAY,CAACK,SAAS,CAAC,CAAC;EACvD;EACAG,WAAWA,CAAA,EAAG,CACd;EACAC,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI;EACf;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
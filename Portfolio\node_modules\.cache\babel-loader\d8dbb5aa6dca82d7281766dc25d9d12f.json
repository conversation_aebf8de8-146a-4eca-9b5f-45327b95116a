{"ast": null, "code": "function randomSquareCoordinate(position, offset) {\n  return position + offset * (Math.random() - 0.5);\n}\n\nexport class SquareShape {\n  randomPosition(position, size, fill) {\n    if (fill) {\n      return {\n        x: randomSquareCoordinate(position.x, size.width),\n        y: randomSquareCoordinate(position.y, size.height)\n      };\n    } else {\n      const halfW = size.width / 2,\n            halfH = size.height / 2,\n            side = Math.floor(Math.random() * 4),\n            v = (Math.random() - 0.5) * 2;\n\n      switch (side) {\n        case 0:\n          return {\n            x: position.x + v * halfW,\n            y: position.y - halfH\n          };\n\n        case 1:\n          return {\n            x: position.x - halfW,\n            y: position.y + v * halfH\n          };\n\n        case 2:\n          return {\n            x: position.x + v * halfW,\n            y: position.y + halfH\n          };\n\n        case 3:\n        default:\n          return {\n            x: position.x + halfW,\n            y: position.y + v * halfH\n          };\n      }\n    }\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Plugins/Emitters/Shapes/Square/SquareShape.js"], "names": ["randomSquareCoordinate", "position", "offset", "Math", "random", "SquareShape", "randomPosition", "size", "fill", "x", "width", "y", "height", "halfW", "halfH", "side", "floor", "v"], "mappings": "AAAA,SAASA,sBAAT,CAAgCC,QAAhC,EAA0CC,MAA1C,EAAkD;AAC9C,SAAOD,QAAQ,GAAGC,MAAM,IAAIC,IAAI,CAACC,MAAL,KAAgB,GAApB,CAAxB;AACH;;AACD,OAAO,MAAMC,WAAN,CAAkB;AACrBC,EAAAA,cAAc,CAACL,QAAD,EAAWM,IAAX,EAAiBC,IAAjB,EAAuB;AACjC,QAAIA,IAAJ,EAAU;AACN,aAAO;AACHC,QAAAA,CAAC,EAAET,sBAAsB,CAACC,QAAQ,CAACQ,CAAV,EAAaF,IAAI,CAACG,KAAlB,CADtB;AAEHC,QAAAA,CAAC,EAAEX,sBAAsB,CAACC,QAAQ,CAACU,CAAV,EAAaJ,IAAI,CAACK,MAAlB;AAFtB,OAAP;AAIH,KALD,MAMK;AACD,YAAMC,KAAK,GAAGN,IAAI,CAACG,KAAL,GAAa,CAA3B;AAAA,YAA8BI,KAAK,GAAGP,IAAI,CAACK,MAAL,GAAc,CAApD;AAAA,YAAuDG,IAAI,GAAGZ,IAAI,CAACa,KAAL,CAAWb,IAAI,CAACC,MAAL,KAAgB,CAA3B,CAA9D;AAAA,YAA6Fa,CAAC,GAAG,CAACd,IAAI,CAACC,MAAL,KAAgB,GAAjB,IAAwB,CAAzH;;AACA,cAAQW,IAAR;AACI,aAAK,CAAL;AACI,iBAAO;AACHN,YAAAA,CAAC,EAAER,QAAQ,CAACQ,CAAT,GAAaQ,CAAC,GAAGJ,KADjB;AAEHF,YAAAA,CAAC,EAAEV,QAAQ,CAACU,CAAT,GAAaG;AAFb,WAAP;;AAIJ,aAAK,CAAL;AACI,iBAAO;AACHL,YAAAA,CAAC,EAAER,QAAQ,CAACQ,CAAT,GAAaI,KADb;AAEHF,YAAAA,CAAC,EAAEV,QAAQ,CAACU,CAAT,GAAaM,CAAC,GAAGH;AAFjB,WAAP;;AAIJ,aAAK,CAAL;AACI,iBAAO;AACHL,YAAAA,CAAC,EAAER,QAAQ,CAACQ,CAAT,GAAaQ,CAAC,GAAGJ,KADjB;AAEHF,YAAAA,CAAC,EAAEV,QAAQ,CAACU,CAAT,GAAaG;AAFb,WAAP;;AAIJ,aAAK,CAAL;AACA;AACI,iBAAO;AACHL,YAAAA,CAAC,EAAER,QAAQ,CAACQ,CAAT,GAAaI,KADb;AAEHF,YAAAA,CAAC,EAAEV,QAAQ,CAACU,CAAT,GAAaM,CAAC,GAAGH;AAFjB,WAAP;AAlBR;AAuBH;AACJ;;AAlCoB", "sourcesContent": ["function randomSquareCoordinate(position, offset) {\n    return position + offset * (Math.random() - 0.5);\n}\nexport class SquareShape {\n    randomPosition(position, size, fill) {\n        if (fill) {\n            return {\n                x: randomSquareCoordinate(position.x, size.width),\n                y: randomSquareCoordinate(position.y, size.height),\n            };\n        }\n        else {\n            const halfW = size.width / 2, halfH = size.height / 2, side = Math.floor(Math.random() * 4), v = (Math.random() - 0.5) * 2;\n            switch (side) {\n                case 0:\n                    return {\n                        x: position.x + v * halfW,\n                        y: position.y - halfH,\n                    };\n                case 1:\n                    return {\n                        x: position.x - halfW,\n                        y: position.y + v * halfH,\n                    };\n                case 2:\n                    return {\n                        x: position.x + v * halfW,\n                        y: position.y + halfH,\n                    };\n                case 3:\n                default:\n                    return {\n                        x: position.x + halfW,\n                        y: position.y + v * halfH,\n                    };\n            }\n        }\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
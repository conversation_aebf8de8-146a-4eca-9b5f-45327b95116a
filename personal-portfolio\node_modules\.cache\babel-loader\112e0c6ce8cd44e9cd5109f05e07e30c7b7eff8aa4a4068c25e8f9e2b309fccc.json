{"ast": null, "code": "import { PixelMode, getRangeValue, isNumber, itemFromSingleOrMultiple, loadParticlesOptions, randomInRange, setRangeValue } from \"@tsparticles/engine\";\nconst defaultOffset = 0,\n  minDestroySize = 0.5,\n  defaultSplitCount = 0,\n  increment = 1,\n  unbreakableTime = 500,\n  minSplitCount = 0;\nfunction addSplitParticle(engine, container, parent, splitParticlesOptions) {\n  const destroyOptions = parent.options.destroy;\n  if (!destroyOptions) {\n    return;\n  }\n  const splitOptions = destroyOptions.split,\n    options = loadParticlesOptions(engine, container, parent.options),\n    factor = getRangeValue(splitOptions.factor.value),\n    parentColor = parent.getFillColor();\n  if (splitOptions.color) {\n    options.color.load(splitOptions.color);\n  } else if (splitOptions.colorOffset && parentColor) {\n    options.color.load({\n      value: {\n        hsl: {\n          h: parentColor.h + getRangeValue(splitOptions.colorOffset.h ?? defaultOffset),\n          s: parentColor.s + getRangeValue(splitOptions.colorOffset.s ?? defaultOffset),\n          l: parentColor.l + getRangeValue(splitOptions.colorOffset.l ?? defaultOffset)\n        }\n      }\n    });\n  } else {\n    options.color.load({\n      value: {\n        hsl: parent.getFillColor()\n      }\n    });\n  }\n  options.move.load({\n    center: {\n      x: parent.position.x,\n      y: parent.position.y,\n      mode: PixelMode.precise\n    }\n  });\n  if (isNumber(options.size.value)) {\n    options.size.value /= factor;\n  } else {\n    options.size.value.min /= factor;\n    options.size.value.max /= factor;\n  }\n  options.load(splitParticlesOptions);\n  const offset = splitOptions.sizeOffset ? setRangeValue(-parent.size.value, parent.size.value) : defaultOffset,\n    position = {\n      x: parent.position.x + randomInRange(offset),\n      y: parent.position.y + randomInRange(offset)\n    };\n  return container.particles.addParticle(position, options, parent.group, particle => {\n    if (particle.size.value < minDestroySize) {\n      return false;\n    }\n    particle.velocity.length = randomInRange(setRangeValue(parent.velocity.length, particle.velocity.length));\n    particle.splitCount = (parent.splitCount ?? defaultSplitCount) + increment;\n    particle.unbreakable = true;\n    setTimeout(() => {\n      particle.unbreakable = false;\n    }, unbreakableTime);\n    return true;\n  });\n}\nexport function split(engine, container, particle) {\n  const destroyOptions = particle.options.destroy;\n  if (!destroyOptions) {\n    return;\n  }\n  const splitOptions = destroyOptions.split;\n  if (splitOptions.count >= minSplitCount && (particle.splitCount === undefined || particle.splitCount++ > splitOptions.count)) {\n    return;\n  }\n  const rate = getRangeValue(splitOptions.rate.value),\n    particlesSplitOptions = itemFromSingleOrMultiple(splitOptions.particles);\n  for (let i = 0; i < rate; i++) {\n    addSplitParticle(engine, container, particle, particlesSplitOptions);\n  }\n}", "map": {"version": 3, "names": ["PixelMode", "getRangeValue", "isNumber", "itemFromSingleOrMultiple", "loadParticlesOptions", "randomInRange", "setRangeValue", "defaultOffset", "minDestroySize", "defaultSplitCount", "increment", "unbreakableTime", "minSplitCount", "addSplitParticle", "engine", "container", "parent", "splitParticlesOptions", "destroyOptions", "options", "destroy", "splitOptions", "split", "factor", "value", "parentColor", "getFillColor", "color", "load", "colorOffset", "hsl", "h", "s", "l", "move", "center", "x", "position", "y", "mode", "precise", "size", "min", "max", "offset", "sizeOffset", "particles", "addParticle", "group", "particle", "velocity", "length", "splitCount", "unbreakable", "setTimeout", "count", "undefined", "rate", "particlesSplitOptions", "i"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/updater-destroy/browser/Utils.js"], "sourcesContent": ["import { PixelMode, getRangeValue, isNumber, itemFromSingleOrMultiple, loadParticlesOptions, randomInRange, setRangeValue, } from \"@tsparticles/engine\";\nconst defaultOffset = 0, minDestroySize = 0.5, defaultSplitCount = 0, increment = 1, unbreakableTime = 500, minSplitCount = 0;\nfunction addSplitParticle(engine, container, parent, splitParticlesOptions) {\n    const destroyOptions = parent.options.destroy;\n    if (!destroyOptions) {\n        return;\n    }\n    const splitOptions = destroyOptions.split, options = loadParticlesOptions(engine, container, parent.options), factor = getRangeValue(splitOptions.factor.value), parentColor = parent.getFillColor();\n    if (splitOptions.color) {\n        options.color.load(splitOptions.color);\n    }\n    else if (splitOptions.colorOffset && parentColor) {\n        options.color.load({\n            value: {\n                hsl: {\n                    h: parentColor.h + getRangeValue(splitOptions.colorOffset.h ?? defaultOffset),\n                    s: parentColor.s + getRangeValue(splitOptions.colorOffset.s ?? defaultOffset),\n                    l: parentColor.l + getRangeValue(splitOptions.colorOffset.l ?? defaultOffset),\n                },\n            },\n        });\n    }\n    else {\n        options.color.load({\n            value: {\n                hsl: parent.getFillColor(),\n            },\n        });\n    }\n    options.move.load({\n        center: {\n            x: parent.position.x,\n            y: parent.position.y,\n            mode: PixelMode.precise,\n        },\n    });\n    if (isNumber(options.size.value)) {\n        options.size.value /= factor;\n    }\n    else {\n        options.size.value.min /= factor;\n        options.size.value.max /= factor;\n    }\n    options.load(splitParticlesOptions);\n    const offset = splitOptions.sizeOffset ? setRangeValue(-parent.size.value, parent.size.value) : defaultOffset, position = {\n        x: parent.position.x + randomInRange(offset),\n        y: parent.position.y + randomInRange(offset),\n    };\n    return container.particles.addParticle(position, options, parent.group, (particle) => {\n        if (particle.size.value < minDestroySize) {\n            return false;\n        }\n        particle.velocity.length = randomInRange(setRangeValue(parent.velocity.length, particle.velocity.length));\n        particle.splitCount = (parent.splitCount ?? defaultSplitCount) + increment;\n        particle.unbreakable = true;\n        setTimeout(() => {\n            particle.unbreakable = false;\n        }, unbreakableTime);\n        return true;\n    });\n}\nexport function split(engine, container, particle) {\n    const destroyOptions = particle.options.destroy;\n    if (!destroyOptions) {\n        return;\n    }\n    const splitOptions = destroyOptions.split;\n    if (splitOptions.count >= minSplitCount &&\n        (particle.splitCount === undefined || particle.splitCount++ > splitOptions.count)) {\n        return;\n    }\n    const rate = getRangeValue(splitOptions.rate.value), particlesSplitOptions = itemFromSingleOrMultiple(splitOptions.particles);\n    for (let i = 0; i < rate; i++) {\n        addSplitParticle(engine, container, particle, particlesSplitOptions);\n    }\n}\n"], "mappings": "AAAA,SAASA,SAAS,EAAEC,aAAa,EAAEC,QAAQ,EAAEC,wBAAwB,EAAEC,oBAAoB,EAAEC,aAAa,EAAEC,aAAa,QAAS,qBAAqB;AACvJ,MAAMC,aAAa,GAAG,CAAC;EAAEC,cAAc,GAAG,GAAG;EAAEC,iBAAiB,GAAG,CAAC;EAAEC,SAAS,GAAG,CAAC;EAAEC,eAAe,GAAG,GAAG;EAAEC,aAAa,GAAG,CAAC;AAC7H,SAASC,gBAAgBA,CAACC,MAAM,EAAEC,SAAS,EAAEC,MAAM,EAAEC,qBAAqB,EAAE;EACxE,MAAMC,cAAc,GAAGF,MAAM,CAACG,OAAO,CAACC,OAAO;EAC7C,IAAI,CAACF,cAAc,EAAE;IACjB;EACJ;EACA,MAAMG,YAAY,GAAGH,cAAc,CAACI,KAAK;IAAEH,OAAO,GAAGf,oBAAoB,CAACU,MAAM,EAAEC,SAAS,EAAEC,MAAM,CAACG,OAAO,CAAC;IAAEI,MAAM,GAAGtB,aAAa,CAACoB,YAAY,CAACE,MAAM,CAACC,KAAK,CAAC;IAAEC,WAAW,GAAGT,MAAM,CAACU,YAAY,CAAC,CAAC;EACpM,IAAIL,YAAY,CAACM,KAAK,EAAE;IACpBR,OAAO,CAACQ,KAAK,CAACC,IAAI,CAACP,YAAY,CAACM,KAAK,CAAC;EAC1C,CAAC,MACI,IAAIN,YAAY,CAACQ,WAAW,IAAIJ,WAAW,EAAE;IAC9CN,OAAO,CAACQ,KAAK,CAACC,IAAI,CAAC;MACfJ,KAAK,EAAE;QACHM,GAAG,EAAE;UACDC,CAAC,EAAEN,WAAW,CAACM,CAAC,GAAG9B,aAAa,CAACoB,YAAY,CAACQ,WAAW,CAACE,CAAC,IAAIxB,aAAa,CAAC;UAC7EyB,CAAC,EAAEP,WAAW,CAACO,CAAC,GAAG/B,aAAa,CAACoB,YAAY,CAACQ,WAAW,CAACG,CAAC,IAAIzB,aAAa,CAAC;UAC7E0B,CAAC,EAAER,WAAW,CAACQ,CAAC,GAAGhC,aAAa,CAACoB,YAAY,CAACQ,WAAW,CAACI,CAAC,IAAI1B,aAAa;QAChF;MACJ;IACJ,CAAC,CAAC;EACN,CAAC,MACI;IACDY,OAAO,CAACQ,KAAK,CAACC,IAAI,CAAC;MACfJ,KAAK,EAAE;QACHM,GAAG,EAAEd,MAAM,CAACU,YAAY,CAAC;MAC7B;IACJ,CAAC,CAAC;EACN;EACAP,OAAO,CAACe,IAAI,CAACN,IAAI,CAAC;IACdO,MAAM,EAAE;MACJC,CAAC,EAAEpB,MAAM,CAACqB,QAAQ,CAACD,CAAC;MACpBE,CAAC,EAAEtB,MAAM,CAACqB,QAAQ,CAACC,CAAC;MACpBC,IAAI,EAAEvC,SAAS,CAACwC;IACpB;EACJ,CAAC,CAAC;EACF,IAAItC,QAAQ,CAACiB,OAAO,CAACsB,IAAI,CAACjB,KAAK,CAAC,EAAE;IAC9BL,OAAO,CAACsB,IAAI,CAACjB,KAAK,IAAID,MAAM;EAChC,CAAC,MACI;IACDJ,OAAO,CAACsB,IAAI,CAACjB,KAAK,CAACkB,GAAG,IAAInB,MAAM;IAChCJ,OAAO,CAACsB,IAAI,CAACjB,KAAK,CAACmB,GAAG,IAAIpB,MAAM;EACpC;EACAJ,OAAO,CAACS,IAAI,CAACX,qBAAqB,CAAC;EACnC,MAAM2B,MAAM,GAAGvB,YAAY,CAACwB,UAAU,GAAGvC,aAAa,CAAC,CAACU,MAAM,CAACyB,IAAI,CAACjB,KAAK,EAAER,MAAM,CAACyB,IAAI,CAACjB,KAAK,CAAC,GAAGjB,aAAa;IAAE8B,QAAQ,GAAG;MACtHD,CAAC,EAAEpB,MAAM,CAACqB,QAAQ,CAACD,CAAC,GAAG/B,aAAa,CAACuC,MAAM,CAAC;MAC5CN,CAAC,EAAEtB,MAAM,CAACqB,QAAQ,CAACC,CAAC,GAAGjC,aAAa,CAACuC,MAAM;IAC/C,CAAC;EACD,OAAO7B,SAAS,CAAC+B,SAAS,CAACC,WAAW,CAACV,QAAQ,EAAElB,OAAO,EAAEH,MAAM,CAACgC,KAAK,EAAGC,QAAQ,IAAK;IAClF,IAAIA,QAAQ,CAACR,IAAI,CAACjB,KAAK,GAAGhB,cAAc,EAAE;MACtC,OAAO,KAAK;IAChB;IACAyC,QAAQ,CAACC,QAAQ,CAACC,MAAM,GAAG9C,aAAa,CAACC,aAAa,CAACU,MAAM,CAACkC,QAAQ,CAACC,MAAM,EAAEF,QAAQ,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;IACzGF,QAAQ,CAACG,UAAU,GAAG,CAACpC,MAAM,CAACoC,UAAU,IAAI3C,iBAAiB,IAAIC,SAAS;IAC1EuC,QAAQ,CAACI,WAAW,GAAG,IAAI;IAC3BC,UAAU,CAAC,MAAM;MACbL,QAAQ,CAACI,WAAW,GAAG,KAAK;IAChC,CAAC,EAAE1C,eAAe,CAAC;IACnB,OAAO,IAAI;EACf,CAAC,CAAC;AACN;AACA,OAAO,SAASW,KAAKA,CAACR,MAAM,EAAEC,SAAS,EAAEkC,QAAQ,EAAE;EAC/C,MAAM/B,cAAc,GAAG+B,QAAQ,CAAC9B,OAAO,CAACC,OAAO;EAC/C,IAAI,CAACF,cAAc,EAAE;IACjB;EACJ;EACA,MAAMG,YAAY,GAAGH,cAAc,CAACI,KAAK;EACzC,IAAID,YAAY,CAACkC,KAAK,IAAI3C,aAAa,KAClCqC,QAAQ,CAACG,UAAU,KAAKI,SAAS,IAAIP,QAAQ,CAACG,UAAU,EAAE,GAAG/B,YAAY,CAACkC,KAAK,CAAC,EAAE;IACnF;EACJ;EACA,MAAME,IAAI,GAAGxD,aAAa,CAACoB,YAAY,CAACoC,IAAI,CAACjC,KAAK,CAAC;IAAEkC,qBAAqB,GAAGvD,wBAAwB,CAACkB,YAAY,CAACyB,SAAS,CAAC;EAC7H,KAAK,IAAIa,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,IAAI,EAAEE,CAAC,EAAE,EAAE;IAC3B9C,gBAAgB,CAACC,MAAM,EAAEC,SAAS,EAAEkC,QAAQ,EAAES,qBAAqB,CAAC;EACxE;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
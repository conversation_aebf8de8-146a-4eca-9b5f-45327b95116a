{"ast": null, "code": "import { ValueWithRandom } from \"../../ValueWithRandom.js\";\nexport class ParticlesBounceFactor extends ValueWithRandom {\n  constructor() {\n    super();\n    this.value = 1;\n  }\n}", "map": {"version": 3, "names": ["ValueWithRandom", "ParticlesBounceFactor", "constructor", "value"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/engine/browser/Options/Classes/Particles/Bounce/ParticlesBounceFactor.js"], "sourcesContent": ["import { ValueWithRandom } from \"../../ValueWithRandom.js\";\nexport class ParticlesBounceFactor extends ValueWithRandom {\n    constructor() {\n        super();\n        this.value = 1;\n    }\n}\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,0BAA0B;AAC1D,OAAO,MAAMC,qBAAqB,SAASD,eAAe,CAAC;EACvDE,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,KAAK,GAAG,CAAC;EAClB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { ClickEvent } from \"./ClickEvent.js\";\nimport { DivEvent } from \"./DivEvent.js\";\nimport { HoverEvent } from \"./HoverEvent.js\";\nimport { ResizeEvent } from \"./ResizeEvent.js\";\nimport { executeOnSingleOrMultiple } from \"../../../../Utils/Utils.js\";\nexport class Events {\n  constructor() {\n    this.onClick = new ClickEvent();\n    this.onDiv = new DivEvent();\n    this.onHover = new HoverEvent();\n    this.resize = new ResizeEvent();\n  }\n  load(data) {\n    if (!data) {\n      return;\n    }\n    this.onClick.load(data.onClick);\n    const onDiv = data.onDiv;\n    if (onDiv !== undefined) {\n      this.onDiv = executeOnSingleOrMultiple(onDiv, t => {\n        const tmp = new DivEvent();\n        tmp.load(t);\n        return tmp;\n      });\n    }\n    this.onHover.load(data.onHover);\n    this.resize.load(data.resize);\n  }\n}", "map": {"version": 3, "names": ["ClickEvent", "DivEvent", "HoverEvent", "ResizeEvent", "executeOnSingleOrMultiple", "Events", "constructor", "onClick", "onDiv", "onHover", "resize", "load", "data", "undefined", "t", "tmp"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/engine/browser/Options/Classes/Interactivity/Events/Events.js"], "sourcesContent": ["import { ClickEvent } from \"./ClickEvent.js\";\nimport { DivEvent } from \"./DivEvent.js\";\nimport { HoverEvent } from \"./HoverEvent.js\";\nimport { ResizeEvent } from \"./ResizeEvent.js\";\nimport { executeOnSingleOrMultiple } from \"../../../../Utils/Utils.js\";\nexport class Events {\n    constructor() {\n        this.onClick = new ClickEvent();\n        this.onDiv = new DivEvent();\n        this.onHover = new HoverEvent();\n        this.resize = new ResizeEvent();\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        this.onClick.load(data.onClick);\n        const onDiv = data.onDiv;\n        if (onDiv !== undefined) {\n            this.onDiv = executeOnSingleOrMultiple(onDiv, t => {\n                const tmp = new DivEvent();\n                tmp.load(t);\n                return tmp;\n            });\n        }\n        this.onHover.load(data.onHover);\n        this.resize.load(data.resize);\n    }\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,iBAAiB;AAC5C,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,UAAU,QAAQ,iBAAiB;AAC5C,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,yBAAyB,QAAQ,4BAA4B;AACtE,OAAO,MAAMC,MAAM,CAAC;EAChBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,OAAO,GAAG,IAAIP,UAAU,CAAC,CAAC;IAC/B,IAAI,CAACQ,KAAK,GAAG,IAAIP,QAAQ,CAAC,CAAC;IAC3B,IAAI,CAACQ,OAAO,GAAG,IAAIP,UAAU,CAAC,CAAC;IAC/B,IAAI,CAACQ,MAAM,GAAG,IAAIP,WAAW,CAAC,CAAC;EACnC;EACAQ,IAAIA,CAACC,IAAI,EAAE;IACP,IAAI,CAACA,IAAI,EAAE;MACP;IACJ;IACA,IAAI,CAACL,OAAO,CAACI,IAAI,CAACC,IAAI,CAACL,OAAO,CAAC;IAC/B,MAAMC,KAAK,GAAGI,IAAI,CAACJ,KAAK;IACxB,IAAIA,KAAK,KAAKK,SAAS,EAAE;MACrB,IAAI,CAACL,KAAK,GAAGJ,yBAAyB,CAACI,KAAK,EAAEM,CAAC,IAAI;QAC/C,MAAMC,GAAG,GAAG,IAAId,QAAQ,CAAC,CAAC;QAC1Bc,GAAG,CAACJ,IAAI,CAACG,CAAC,CAAC;QACX,OAAOC,GAAG;MACd,CAAC,CAAC;IACN;IACA,IAAI,CAACN,OAAO,CAACE,IAAI,CAACC,IAAI,CAACH,OAAO,CAAC;IAC/B,IAAI,CAACC,MAAM,CAACC,IAAI,CAACC,IAAI,CAACF,MAAM,CAAC;EACjC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
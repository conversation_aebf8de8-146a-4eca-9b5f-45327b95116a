{"ast": null, "code": "import { getRandom, getRangeValue } from \"@tsparticles/engine\";\nimport { Wobble } from \"./Options/Classes/Wobble.js\";\nimport { updateWobble } from \"./Utils.js\";\nconst double = 2,\n  doublePI = Math.PI * double,\n  maxAngle = 360,\n  moveSpeedFactor = 10,\n  defaultDistance = 0;\nexport class WobbleUpdater {\n  constructor(container) {\n    this.container = container;\n  }\n  init(particle) {\n    const wobbleOpt = particle.options.wobble;\n    if (wobbleOpt?.enable) {\n      particle.wobble = {\n        angle: getRandom() * doublePI,\n        angleSpeed: getRangeValue(wobbleOpt.speed.angle) / maxAngle,\n        moveSpeed: getRangeValue(wobbleOpt.speed.move) / moveSpeedFactor\n      };\n    } else {\n      particle.wobble = {\n        angle: 0,\n        angleSpeed: 0,\n        moveSpeed: 0\n      };\n    }\n    particle.retina.wobbleDistance = getRangeValue(wobbleOpt?.distance ?? defaultDistance) * this.container.retina.pixelRatio;\n  }\n  isEnabled(particle) {\n    return !particle.destroyed && !particle.spawning && !!particle.options.wobble?.enable;\n  }\n  loadOptions(options, ...sources) {\n    if (!options.wobble) {\n      options.wobble = new Wobble();\n    }\n    for (const source of sources) {\n      options.wobble.load(source?.wobble);\n    }\n  }\n  update(particle, delta) {\n    if (!this.isEnabled(particle)) {\n      return;\n    }\n    updateWobble(particle, delta);\n  }\n}", "map": {"version": 3, "names": ["getRandom", "getRangeValue", "Wobble", "updateWobble", "double", "doublePI", "Math", "PI", "maxAngle", "moveSpeedFactor", "defaultDistance", "WobbleUpdater", "constructor", "container", "init", "particle", "wobbleOpt", "options", "wobble", "enable", "angle", "angleSpeed", "speed", "moveSpeed", "move", "retina", "wobbleDistance", "distance", "pixelRatio", "isEnabled", "destroyed", "spawning", "loadOptions", "sources", "source", "load", "update", "delta"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/updater-wobble/browser/WobbleUpdater.js"], "sourcesContent": ["import { getRandom, getRangeValue, } from \"@tsparticles/engine\";\nimport { Wobble } from \"./Options/Classes/Wobble.js\";\nimport { updateWobble } from \"./Utils.js\";\nconst double = 2, doublePI = Math.PI * double, maxAngle = 360, moveSpeedFactor = 10, defaultDistance = 0;\nexport class WobbleUpdater {\n    constructor(container) {\n        this.container = container;\n    }\n    init(particle) {\n        const wobbleOpt = particle.options.wobble;\n        if (wobbleOpt?.enable) {\n            particle.wobble = {\n                angle: getRandom() * doublePI,\n                angleSpeed: getRangeValue(wobbleOpt.speed.angle) / maxAngle,\n                moveSpeed: getRangeValue(wobbleOpt.speed.move) / moveSpeedFactor,\n            };\n        }\n        else {\n            particle.wobble = {\n                angle: 0,\n                angleSpeed: 0,\n                moveSpeed: 0,\n            };\n        }\n        particle.retina.wobbleDistance =\n            getRangeValue(wobbleOpt?.distance ?? defaultDistance) * this.container.retina.pixelRatio;\n    }\n    isEnabled(particle) {\n        return !particle.destroyed && !particle.spawning && !!particle.options.wobble?.enable;\n    }\n    loadOptions(options, ...sources) {\n        if (!options.wobble) {\n            options.wobble = new Wobble();\n        }\n        for (const source of sources) {\n            options.wobble.load(source?.wobble);\n        }\n    }\n    update(particle, delta) {\n        if (!this.isEnabled(particle)) {\n            return;\n        }\n        updateWobble(particle, delta);\n    }\n}\n"], "mappings": "AAAA,SAASA,SAAS,EAAEC,aAAa,QAAS,qBAAqB;AAC/D,SAASC,MAAM,QAAQ,6BAA6B;AACpD,SAASC,YAAY,QAAQ,YAAY;AACzC,MAAMC,MAAM,GAAG,CAAC;EAAEC,QAAQ,GAAGC,IAAI,CAACC,EAAE,GAAGH,MAAM;EAAEI,QAAQ,GAAG,GAAG;EAAEC,eAAe,GAAG,EAAE;EAAEC,eAAe,GAAG,CAAC;AACxG,OAAO,MAAMC,aAAa,CAAC;EACvBC,WAAWA,CAACC,SAAS,EAAE;IACnB,IAAI,CAACA,SAAS,GAAGA,SAAS;EAC9B;EACAC,IAAIA,CAACC,QAAQ,EAAE;IACX,MAAMC,SAAS,GAAGD,QAAQ,CAACE,OAAO,CAACC,MAAM;IACzC,IAAIF,SAAS,EAAEG,MAAM,EAAE;MACnBJ,QAAQ,CAACG,MAAM,GAAG;QACdE,KAAK,EAAEpB,SAAS,CAAC,CAAC,GAAGK,QAAQ;QAC7BgB,UAAU,EAAEpB,aAAa,CAACe,SAAS,CAACM,KAAK,CAACF,KAAK,CAAC,GAAGZ,QAAQ;QAC3De,SAAS,EAAEtB,aAAa,CAACe,SAAS,CAACM,KAAK,CAACE,IAAI,CAAC,GAAGf;MACrD,CAAC;IACL,CAAC,MACI;MACDM,QAAQ,CAACG,MAAM,GAAG;QACdE,KAAK,EAAE,CAAC;QACRC,UAAU,EAAE,CAAC;QACbE,SAAS,EAAE;MACf,CAAC;IACL;IACAR,QAAQ,CAACU,MAAM,CAACC,cAAc,GAC1BzB,aAAa,CAACe,SAAS,EAAEW,QAAQ,IAAIjB,eAAe,CAAC,GAAG,IAAI,CAACG,SAAS,CAACY,MAAM,CAACG,UAAU;EAChG;EACAC,SAASA,CAACd,QAAQ,EAAE;IAChB,OAAO,CAACA,QAAQ,CAACe,SAAS,IAAI,CAACf,QAAQ,CAACgB,QAAQ,IAAI,CAAC,CAAChB,QAAQ,CAACE,OAAO,CAACC,MAAM,EAAEC,MAAM;EACzF;EACAa,WAAWA,CAACf,OAAO,EAAE,GAAGgB,OAAO,EAAE;IAC7B,IAAI,CAAChB,OAAO,CAACC,MAAM,EAAE;MACjBD,OAAO,CAACC,MAAM,GAAG,IAAIhB,MAAM,CAAC,CAAC;IACjC;IACA,KAAK,MAAMgC,MAAM,IAAID,OAAO,EAAE;MAC1BhB,OAAO,CAACC,MAAM,CAACiB,IAAI,CAACD,MAAM,EAAEhB,MAAM,CAAC;IACvC;EACJ;EACAkB,MAAMA,CAACrB,QAAQ,EAAEsB,KAAK,EAAE;IACpB,IAAI,CAAC,IAAI,CAACR,SAAS,CAACd,QAAQ,CAAC,EAAE;MAC3B;IACJ;IACAZ,YAAY,CAACY,QAAQ,EAAEsB,KAAK,CAAC;EACjC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
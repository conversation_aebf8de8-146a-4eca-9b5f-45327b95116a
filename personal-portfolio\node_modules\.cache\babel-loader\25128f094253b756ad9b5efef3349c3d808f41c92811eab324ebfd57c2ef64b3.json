{"ast": null, "code": "export var MoveDirection;\n(function (MoveDirection) {\n  MoveDirection[\"bottom\"] = \"bottom\";\n  MoveDirection[\"bottomLeft\"] = \"bottom-left\";\n  MoveDirection[\"bottomRight\"] = \"bottom-right\";\n  MoveDirection[\"left\"] = \"left\";\n  MoveDirection[\"none\"] = \"none\";\n  MoveDirection[\"right\"] = \"right\";\n  MoveDirection[\"top\"] = \"top\";\n  MoveDirection[\"topLeft\"] = \"top-left\";\n  MoveDirection[\"topRight\"] = \"top-right\";\n  MoveDirection[\"outside\"] = \"outside\";\n  MoveDirection[\"inside\"] = \"inside\";\n})(MoveDirection || (MoveDirection = {}));", "map": {"version": 3, "names": ["MoveDirection"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/engine/browser/Enums/Directions/MoveDirection.js"], "sourcesContent": ["export var MoveDirection;\n(function (MoveDirection) {\n    MoveDirection[\"bottom\"] = \"bottom\";\n    MoveDirection[\"bottomLeft\"] = \"bottom-left\";\n    MoveDirection[\"bottomRight\"] = \"bottom-right\";\n    MoveDirection[\"left\"] = \"left\";\n    MoveDirection[\"none\"] = \"none\";\n    MoveDirection[\"right\"] = \"right\";\n    MoveDirection[\"top\"] = \"top\";\n    MoveDirection[\"topLeft\"] = \"top-left\";\n    MoveDirection[\"topRight\"] = \"top-right\";\n    MoveDirection[\"outside\"] = \"outside\";\n    MoveDirection[\"inside\"] = \"inside\";\n})(MoveDirection || (MoveDirection = {}));\n"], "mappings": "AAAA,OAAO,IAAIA,aAAa;AACxB,CAAC,UAAUA,aAAa,EAAE;EACtBA,aAAa,CAAC,QAAQ,CAAC,GAAG,QAAQ;EAClCA,aAAa,CAAC,YAAY,CAAC,GAAG,aAAa;EAC3CA,aAAa,CAAC,aAAa,CAAC,GAAG,cAAc;EAC7CA,aAAa,CAAC,MAAM,CAAC,GAAG,MAAM;EAC9BA,aAAa,CAAC,MAAM,CAAC,GAAG,MAAM;EAC9BA,aAAa,CAAC,OAAO,CAAC,GAAG,OAAO;EAChCA,aAAa,CAAC,KAAK,CAAC,GAAG,KAAK;EAC5BA,aAAa,CAAC,SAAS,CAAC,GAAG,UAAU;EACrCA,aAAa,CAAC,UAAU,CAAC,GAAG,WAAW;EACvCA,aAAa,CAAC,SAAS,CAAC,GAAG,SAAS;EACpCA,aAAa,CAAC,QAAQ,CAAC,GAAG,QAAQ;AACtC,CAAC,EAAEA,aAAa,KAAKA,aAAa,GAAG,CAAC,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
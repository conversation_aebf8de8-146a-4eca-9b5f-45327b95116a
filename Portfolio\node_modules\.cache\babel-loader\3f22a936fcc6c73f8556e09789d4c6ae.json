{"ast": null, "code": "import { PolygonDrawerBase } from \"./PolygonDrawerBase\";\nexport class TriangleDrawer extends PolygonDrawerBase {\n  getSidesCount() {\n    return 3;\n  }\n\n  getSidesData(particle, radius) {\n    return {\n      count: {\n        denominator: 2,\n        numerator: 3\n      },\n      length: radius * 2\n    };\n  }\n\n  getCenter(particle, radius) {\n    return {\n      x: -radius,\n      y: radius / 1.66\n    };\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Shapes/Polygon/TriangleDrawer.js"], "names": ["PolygonDrawerBase", "TriangleDrawer", "getSidesCount", "getSidesData", "particle", "radius", "count", "denominator", "numerator", "length", "getCenter", "x", "y"], "mappings": "AAAA,SAASA,iBAAT,QAAkC,qBAAlC;AACA,OAAO,MAAMC,cAAN,SAA6BD,iBAA7B,CAA+C;AAClDE,EAAAA,aAAa,GAAG;AACZ,WAAO,CAAP;AACH;;AACDC,EAAAA,YAAY,CAACC,QAAD,EAAWC,MAAX,EAAmB;AAC3B,WAAO;AACHC,MAAAA,KAAK,EAAE;AACHC,QAAAA,WAAW,EAAE,CADV;AAEHC,QAAAA,SAAS,EAAE;AAFR,OADJ;AAKHC,MAAAA,MAAM,EAAEJ,MAAM,GAAG;AALd,KAAP;AAOH;;AACDK,EAAAA,SAAS,CAACN,QAAD,EAAWC,MAAX,EAAmB;AACxB,WAAO;AACHM,MAAAA,CAAC,EAAE,CAACN,MADD;AAEHO,MAAAA,CAAC,EAAEP,MAAM,GAAG;AAFT,KAAP;AAIH;;AAlBiD", "sourcesContent": ["import { PolygonDrawerBase } from \"./PolygonDrawerBase\";\nexport class TriangleDrawer extends PolygonDrawerBase {\n    getSidesCount() {\n        return 3;\n    }\n    getSidesData(particle, radius) {\n        return {\n            count: {\n                denominator: 2,\n                numerator: 3,\n            },\n            length: radius * 2,\n        };\n    }\n    getCenter(particle, radius) {\n        return {\n            x: -radius,\n            y: radius / 1.66,\n        };\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
{"ast": null, "code": "import { LinksShadow } from \"./LinksShadow\";\nimport { LinksTriangle } from \"./LinksTriangle\";\nimport { OptionsColor } from \"../../OptionsColor\";\nexport class Links {\n  constructor() {\n    this.blink = false;\n    this.color = new OptionsColor();\n    this.consent = false;\n    this.distance = 100;\n    this.enable = false;\n    this.frequency = 1;\n    this.opacity = 1;\n    this.shadow = new LinksShadow();\n    this.triangles = new LinksTriangle();\n    this.width = 1;\n    this.warp = false;\n  }\n\n  load(data) {\n    if (data === undefined) {\n      return;\n    }\n\n    if (data.id !== undefined) {\n      this.id = data.id;\n    }\n\n    if (data.blink !== undefined) {\n      this.blink = data.blink;\n    }\n\n    this.color = OptionsColor.create(this.color, data.color);\n\n    if (data.consent !== undefined) {\n      this.consent = data.consent;\n    }\n\n    if (data.distance !== undefined) {\n      this.distance = data.distance;\n    }\n\n    if (data.enable !== undefined) {\n      this.enable = data.enable;\n    }\n\n    if (data.frequency !== undefined) {\n      this.frequency = data.frequency;\n    }\n\n    if (data.opacity !== undefined) {\n      this.opacity = data.opacity;\n    }\n\n    this.shadow.load(data.shadow);\n    this.triangles.load(data.triangles);\n\n    if (data.width !== undefined) {\n      this.width = data.width;\n    }\n\n    if (data.warp !== undefined) {\n      this.warp = data.warp;\n    }\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Options/Classes/Particles/Links/Links.js"], "names": ["LinksShadow", "LinksTriangle", "OptionsColor", "Links", "constructor", "blink", "color", "consent", "distance", "enable", "frequency", "opacity", "shadow", "triangles", "width", "warp", "load", "data", "undefined", "id", "create"], "mappings": "AAAA,SAASA,WAAT,QAA4B,eAA5B;AACA,SAASC,aAAT,QAA8B,iBAA9B;AACA,SAASC,YAAT,QAA6B,oBAA7B;AACA,OAAO,MAAMC,KAAN,CAAY;AACfC,EAAAA,WAAW,GAAG;AACV,SAAKC,KAAL,GAAa,KAAb;AACA,SAAKC,KAAL,GAAa,IAAIJ,YAAJ,EAAb;AACA,SAAKK,OAAL,GAAe,KAAf;AACA,SAAKC,QAAL,GAAgB,GAAhB;AACA,SAAKC,MAAL,GAAc,KAAd;AACA,SAAKC,SAAL,GAAiB,CAAjB;AACA,SAAKC,OAAL,GAAe,CAAf;AACA,SAAKC,MAAL,GAAc,IAAIZ,WAAJ,EAAd;AACA,SAAKa,SAAL,GAAiB,IAAIZ,aAAJ,EAAjB;AACA,SAAKa,KAAL,GAAa,CAAb;AACA,SAAKC,IAAL,GAAY,KAAZ;AACH;;AACDC,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,QAAIA,IAAI,KAAKC,SAAb,EAAwB;AACpB;AACH;;AACD,QAAID,IAAI,CAACE,EAAL,KAAYD,SAAhB,EAA2B;AACvB,WAAKC,EAAL,GAAUF,IAAI,CAACE,EAAf;AACH;;AACD,QAAIF,IAAI,CAACZ,KAAL,KAAea,SAAnB,EAA8B;AAC1B,WAAKb,KAAL,GAAaY,IAAI,CAACZ,KAAlB;AACH;;AACD,SAAKC,KAAL,GAAaJ,YAAY,CAACkB,MAAb,CAAoB,KAAKd,KAAzB,EAAgCW,IAAI,CAACX,KAArC,CAAb;;AACA,QAAIW,IAAI,CAACV,OAAL,KAAiBW,SAArB,EAAgC;AAC5B,WAAKX,OAAL,GAAeU,IAAI,CAACV,OAApB;AACH;;AACD,QAAIU,IAAI,CAACT,QAAL,KAAkBU,SAAtB,EAAiC;AAC7B,WAAKV,QAAL,GAAgBS,IAAI,CAACT,QAArB;AACH;;AACD,QAAIS,IAAI,CAACR,MAAL,KAAgBS,SAApB,EAA+B;AAC3B,WAAKT,MAAL,GAAcQ,IAAI,CAACR,MAAnB;AACH;;AACD,QAAIQ,IAAI,CAACP,SAAL,KAAmBQ,SAAvB,EAAkC;AAC9B,WAAKR,SAAL,GAAiBO,IAAI,CAACP,SAAtB;AACH;;AACD,QAAIO,IAAI,CAACN,OAAL,KAAiBO,SAArB,EAAgC;AAC5B,WAAKP,OAAL,GAAeM,IAAI,CAACN,OAApB;AACH;;AACD,SAAKC,MAAL,CAAYI,IAAZ,CAAiBC,IAAI,CAACL,MAAtB;AACA,SAAKC,SAAL,CAAeG,IAAf,CAAoBC,IAAI,CAACJ,SAAzB;;AACA,QAAII,IAAI,CAACH,KAAL,KAAeI,SAAnB,EAA8B;AAC1B,WAAKJ,KAAL,GAAaG,IAAI,CAACH,KAAlB;AACH;;AACD,QAAIG,IAAI,CAACF,IAAL,KAAcG,SAAlB,EAA6B;AACzB,WAAKH,IAAL,GAAYE,IAAI,CAACF,IAAjB;AACH;AACJ;;AAhDc", "sourcesContent": ["import { LinksShadow } from \"./LinksShadow\";\nimport { LinksTriangle } from \"./LinksTriangle\";\nimport { OptionsColor } from \"../../OptionsColor\";\nexport class Links {\n    constructor() {\n        this.blink = false;\n        this.color = new OptionsColor();\n        this.consent = false;\n        this.distance = 100;\n        this.enable = false;\n        this.frequency = 1;\n        this.opacity = 1;\n        this.shadow = new LinksShadow();\n        this.triangles = new LinksTriangle();\n        this.width = 1;\n        this.warp = false;\n    }\n    load(data) {\n        if (data === undefined) {\n            return;\n        }\n        if (data.id !== undefined) {\n            this.id = data.id;\n        }\n        if (data.blink !== undefined) {\n            this.blink = data.blink;\n        }\n        this.color = OptionsColor.create(this.color, data.color);\n        if (data.consent !== undefined) {\n            this.consent = data.consent;\n        }\n        if (data.distance !== undefined) {\n            this.distance = data.distance;\n        }\n        if (data.enable !== undefined) {\n            this.enable = data.enable;\n        }\n        if (data.frequency !== undefined) {\n            this.frequency = data.frequency;\n        }\n        if (data.opacity !== undefined) {\n            this.opacity = data.opacity;\n        }\n        this.shadow.load(data.shadow);\n        this.triangles.load(data.triangles);\n        if (data.width !== undefined) {\n            this.width = data.width;\n        }\n        if (data.warp !== undefined) {\n            this.warp = data.warp;\n        }\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
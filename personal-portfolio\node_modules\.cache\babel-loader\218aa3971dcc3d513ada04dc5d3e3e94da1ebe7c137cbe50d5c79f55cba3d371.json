{"ast": null, "code": "import { Pauser } from \"./Pauser.js\";\nexport async function loadExternalPauseInteraction(engine, refresh = true) {\n  await engine.addInteractor(\"externalPause\", container => {\n    return Promise.resolve(new Pauser(container));\n  }, refresh);\n}", "map": {"version": 3, "names": ["Pa<PERSON>", "loadExternalPauseInteraction", "engine", "refresh", "addInteractor", "container", "Promise", "resolve"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/interaction-external-pause/browser/index.js"], "sourcesContent": ["import { Pauser } from \"./Pauser.js\";\nexport async function loadExternalPauseInteraction(engine, refresh = true) {\n    await engine.addInteractor(\"externalPause\", container => {\n        return Promise.resolve(new Pauser(container));\n    }, refresh);\n}\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,aAAa;AACpC,OAAO,eAAeC,4BAA4BA,CAACC,MAAM,EAAEC,OAAO,GAAG,IAAI,EAAE;EACvE,MAAMD,MAAM,CAACE,aAAa,CAAC,eAAe,EAAEC,SAAS,IAAI;IACrD,OAAOC,OAAO,CAACC,OAAO,CAAC,IAAIP,MAAM,CAACK,SAAS,CAAC,CAAC;EACjD,CAAC,EAAEF,OAAO,CAAC;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
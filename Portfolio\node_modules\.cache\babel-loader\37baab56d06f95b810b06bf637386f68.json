{"ast": null, "code": "import { OptionsColor } from \"../../OptionsColor\";\nexport class LightShadow {\n  constructor() {\n    this.color = new OptionsColor();\n    this.color.value = \"#000000\";\n    this.length = 2000;\n  }\n\n  load(data) {\n    if (data === undefined) {\n      return;\n    }\n\n    this.color = OptionsColor.create(this.color, data.color);\n\n    if (data.length !== undefined) {\n      this.length = data.length;\n    }\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Options/Classes/Interactivity/Modes/LightShadow.js"], "names": ["OptionsColor", "LightShadow", "constructor", "color", "value", "length", "load", "data", "undefined", "create"], "mappings": "AAAA,SAASA,YAAT,QAA6B,oBAA7B;AACA,OAAO,MAAMC,WAAN,CAAkB;AACrBC,EAAAA,WAAW,GAAG;AACV,SAAKC,KAAL,GAAa,IAAIH,YAAJ,EAAb;AACA,SAAKG,KAAL,CAAWC,KAAX,GAAmB,SAAnB;AACA,SAAKC,MAAL,GAAc,IAAd;AACH;;AACDC,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,QAAIA,IAAI,KAAKC,SAAb,EAAwB;AACpB;AACH;;AACD,SAAKL,KAAL,GAAaH,YAAY,CAACS,MAAb,CAAoB,KAAKN,KAAzB,EAAgCI,IAAI,CAACJ,KAArC,CAAb;;AACA,QAAII,IAAI,CAACF,MAAL,KAAgBG,SAApB,EAA+B;AAC3B,WAAKH,MAAL,GAAcE,IAAI,CAACF,MAAnB;AACH;AACJ;;AAdoB", "sourcesContent": ["import { OptionsColor } from \"../../OptionsColor\";\nexport class LightShadow {\n    constructor() {\n        this.color = new OptionsColor();\n        this.color.value = \"#000000\";\n        this.length = 2000;\n    }\n    load(data) {\n        if (data === undefined) {\n            return;\n        }\n        this.color = OptionsColor.create(this.color, data.color);\n        if (data.length !== undefined) {\n            this.length = data.length;\n        }\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
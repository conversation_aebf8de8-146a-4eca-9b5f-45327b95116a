{"ast": null, "code": "import { errorPrefix } from \"@tsparticles/engine\";\nimport { replaceImageColor } from \"./Utils.js\";\nimport { drawGif } from \"./GifUtils/Utils.js\";\nconst double = 2,\n  defaultAlpha = 1,\n  sides = 12,\n  defaultRatio = 1;\nexport class ImageDrawer {\n  constructor(engine) {\n    this.validTypes = [\"image\", \"images\"];\n    this.loadImageShape = async imageShape => {\n      if (!this._engine.loadImage) {\n        throw new Error(`${errorPrefix} image shape not initialized`);\n      }\n      await this._engine.loadImage({\n        gif: imageShape.gif,\n        name: imageShape.name,\n        replaceColor: imageShape.replaceColor ?? false,\n        src: imageShape.src\n      });\n    };\n    this._engine = engine;\n  }\n  addImage(image) {\n    if (!this._engine.images) {\n      this._engine.images = [];\n    }\n    this._engine.images.push(image);\n  }\n  draw(data) {\n    const {\n        context,\n        radius,\n        particle,\n        opacity\n      } = data,\n      image = particle.image,\n      element = image?.element;\n    if (!image) {\n      return;\n    }\n    context.globalAlpha = opacity;\n    if (image.gif && image.gifData) {\n      drawGif(data);\n    } else if (element) {\n      const ratio = image.ratio,\n        pos = {\n          x: -radius,\n          y: -radius\n        },\n        diameter = radius * double;\n      context.drawImage(element, pos.x, pos.y, diameter, diameter / ratio);\n    }\n    context.globalAlpha = defaultAlpha;\n  }\n  getSidesCount() {\n    return sides;\n  }\n  async init(container) {\n    const options = container.actualOptions;\n    if (!options.preload || !this._engine.loadImage) {\n      return;\n    }\n    for (const imageData of options.preload) {\n      await this._engine.loadImage(imageData);\n    }\n  }\n  loadShape(particle) {\n    if (particle.shape !== \"image\" && particle.shape !== \"images\") {\n      return;\n    }\n    if (!this._engine.images) {\n      this._engine.images = [];\n    }\n    const imageData = particle.shapeData;\n    if (!imageData) {\n      return;\n    }\n    const image = this._engine.images.find(t => t.name === imageData.name || t.source === imageData.src);\n    if (!image) {\n      void this.loadImageShape(imageData).then(() => {\n        this.loadShape(particle);\n      });\n    }\n  }\n  particleInit(container, particle) {\n    if (particle.shape !== \"image\" && particle.shape !== \"images\") {\n      return;\n    }\n    if (!this._engine.images) {\n      this._engine.images = [];\n    }\n    const images = this._engine.images,\n      imageData = particle.shapeData;\n    if (!imageData) {\n      return;\n    }\n    const color = particle.getFillColor(),\n      image = images.find(t => t.name === imageData.name || t.source === imageData.src);\n    if (!image) {\n      return;\n    }\n    const replaceColor = imageData.replaceColor ?? image.replaceColor;\n    if (image.loading) {\n      setTimeout(() => {\n        this.particleInit(container, particle);\n      });\n      return;\n    }\n    void (async () => {\n      let imageRes;\n      if (image.svgData && color) {\n        imageRes = await replaceImageColor(image, imageData, color, particle);\n      } else {\n        imageRes = {\n          color,\n          data: image,\n          element: image.element,\n          gif: image.gif,\n          gifData: image.gifData,\n          gifLoopCount: image.gifLoopCount,\n          loaded: true,\n          ratio: imageData.width && imageData.height ? imageData.width / imageData.height : image.ratio ?? defaultRatio,\n          replaceColor: replaceColor,\n          source: imageData.src\n        };\n      }\n      if (!imageRes.ratio) {\n        imageRes.ratio = 1;\n      }\n      const fill = imageData.fill ?? particle.shapeFill,\n        close = imageData.close ?? particle.shapeClose,\n        imageShape = {\n          image: imageRes,\n          fill,\n          close\n        };\n      particle.image = imageShape.image;\n      particle.shapeFill = imageShape.fill;\n      particle.shapeClose = imageShape.close;\n    })();\n  }\n}", "map": {"version": 3, "names": ["errorPrefix", "replaceImageColor", "drawGif", "double", "defaultAlpha", "sides", "defaultRatio", "ImageDrawer", "constructor", "engine", "validTypes", "loadImageShape", "imageShape", "_engine", "loadImage", "Error", "gif", "name", "replaceColor", "src", "addImage", "image", "images", "push", "draw", "data", "context", "radius", "particle", "opacity", "element", "globalAlpha", "gifData", "ratio", "pos", "x", "y", "diameter", "drawImage", "getSidesCount", "init", "container", "options", "actualOptions", "preload", "imageData", "loadShape", "shape", "shapeData", "find", "t", "source", "then", "particleInit", "color", "getFillColor", "loading", "setTimeout", "imageRes", "svgData", "gifLoopCount", "loaded", "width", "height", "fill", "shapeFill", "close", "shapeClose"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/shape-image/browser/ImageDrawer.js"], "sourcesContent": ["import { errorPrefix } from \"@tsparticles/engine\";\nimport { replaceImageColor } from \"./Utils.js\";\nimport { drawGif } from \"./GifUtils/Utils.js\";\nconst double = 2, defaultAlpha = 1, sides = 12, defaultRatio = 1;\nexport class ImageDrawer {\n    constructor(engine) {\n        this.validTypes = [\"image\", \"images\"];\n        this.loadImageShape = async (imageShape) => {\n            if (!this._engine.loadImage) {\n                throw new Error(`${errorPrefix} image shape not initialized`);\n            }\n            await this._engine.loadImage({\n                gif: imageShape.gif,\n                name: imageShape.name,\n                replaceColor: imageShape.replaceColor ?? false,\n                src: imageShape.src,\n            });\n        };\n        this._engine = engine;\n    }\n    addImage(image) {\n        if (!this._engine.images) {\n            this._engine.images = [];\n        }\n        this._engine.images.push(image);\n    }\n    draw(data) {\n        const { context, radius, particle, opacity } = data, image = particle.image, element = image?.element;\n        if (!image) {\n            return;\n        }\n        context.globalAlpha = opacity;\n        if (image.gif && image.gifData) {\n            drawGif(data);\n        }\n        else if (element) {\n            const ratio = image.ratio, pos = {\n                x: -radius,\n                y: -radius,\n            }, diameter = radius * double;\n            context.drawImage(element, pos.x, pos.y, diameter, diameter / ratio);\n        }\n        context.globalAlpha = defaultAlpha;\n    }\n    getSidesCount() {\n        return sides;\n    }\n    async init(container) {\n        const options = container.actualOptions;\n        if (!options.preload || !this._engine.loadImage) {\n            return;\n        }\n        for (const imageData of options.preload) {\n            await this._engine.loadImage(imageData);\n        }\n    }\n    loadShape(particle) {\n        if (particle.shape !== \"image\" && particle.shape !== \"images\") {\n            return;\n        }\n        if (!this._engine.images) {\n            this._engine.images = [];\n        }\n        const imageData = particle.shapeData;\n        if (!imageData) {\n            return;\n        }\n        const image = this._engine.images.find((t) => t.name === imageData.name || t.source === imageData.src);\n        if (!image) {\n            void this.loadImageShape(imageData).then(() => {\n                this.loadShape(particle);\n            });\n        }\n    }\n    particleInit(container, particle) {\n        if (particle.shape !== \"image\" && particle.shape !== \"images\") {\n            return;\n        }\n        if (!this._engine.images) {\n            this._engine.images = [];\n        }\n        const images = this._engine.images, imageData = particle.shapeData;\n        if (!imageData) {\n            return;\n        }\n        const color = particle.getFillColor(), image = images.find((t) => t.name === imageData.name || t.source === imageData.src);\n        if (!image) {\n            return;\n        }\n        const replaceColor = imageData.replaceColor ?? image.replaceColor;\n        if (image.loading) {\n            setTimeout(() => {\n                this.particleInit(container, particle);\n            });\n            return;\n        }\n        void (async () => {\n            let imageRes;\n            if (image.svgData && color) {\n                imageRes = await replaceImageColor(image, imageData, color, particle);\n            }\n            else {\n                imageRes = {\n                    color,\n                    data: image,\n                    element: image.element,\n                    gif: image.gif,\n                    gifData: image.gifData,\n                    gifLoopCount: image.gifLoopCount,\n                    loaded: true,\n                    ratio: imageData.width && imageData.height\n                        ? imageData.width / imageData.height\n                        : image.ratio ?? defaultRatio,\n                    replaceColor: replaceColor,\n                    source: imageData.src,\n                };\n            }\n            if (!imageRes.ratio) {\n                imageRes.ratio = 1;\n            }\n            const fill = imageData.fill ?? particle.shapeFill, close = imageData.close ?? particle.shapeClose, imageShape = {\n                image: imageRes,\n                fill,\n                close,\n            };\n            particle.image = imageShape.image;\n            particle.shapeFill = imageShape.fill;\n            particle.shapeClose = imageShape.close;\n        })();\n    }\n}\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,qBAAqB;AACjD,SAASC,iBAAiB,QAAQ,YAAY;AAC9C,SAASC,OAAO,QAAQ,qBAAqB;AAC7C,MAAMC,MAAM,GAAG,CAAC;EAAEC,YAAY,GAAG,CAAC;EAAEC,KAAK,GAAG,EAAE;EAAEC,YAAY,GAAG,CAAC;AAChE,OAAO,MAAMC,WAAW,CAAC;EACrBC,WAAWA,CAACC,MAAM,EAAE;IAChB,IAAI,CAACC,UAAU,GAAG,CAAC,OAAO,EAAE,QAAQ,CAAC;IACrC,IAAI,CAACC,cAAc,GAAG,MAAOC,UAAU,IAAK;MACxC,IAAI,CAAC,IAAI,CAACC,OAAO,CAACC,SAAS,EAAE;QACzB,MAAM,IAAIC,KAAK,CAAC,GAAGf,WAAW,8BAA8B,CAAC;MACjE;MACA,MAAM,IAAI,CAACa,OAAO,CAACC,SAAS,CAAC;QACzBE,GAAG,EAAEJ,UAAU,CAACI,GAAG;QACnBC,IAAI,EAAEL,UAAU,CAACK,IAAI;QACrBC,YAAY,EAAEN,UAAU,CAACM,YAAY,IAAI,KAAK;QAC9CC,GAAG,EAAEP,UAAU,CAACO;MACpB,CAAC,CAAC;IACN,CAAC;IACD,IAAI,CAACN,OAAO,GAAGJ,MAAM;EACzB;EACAW,QAAQA,CAACC,KAAK,EAAE;IACZ,IAAI,CAAC,IAAI,CAACR,OAAO,CAACS,MAAM,EAAE;MACtB,IAAI,CAACT,OAAO,CAACS,MAAM,GAAG,EAAE;IAC5B;IACA,IAAI,CAACT,OAAO,CAACS,MAAM,CAACC,IAAI,CAACF,KAAK,CAAC;EACnC;EACAG,IAAIA,CAACC,IAAI,EAAE;IACP,MAAM;QAAEC,OAAO;QAAEC,MAAM;QAAEC,QAAQ;QAAEC;MAAQ,CAAC,GAAGJ,IAAI;MAAEJ,KAAK,GAAGO,QAAQ,CAACP,KAAK;MAAES,OAAO,GAAGT,KAAK,EAAES,OAAO;IACrG,IAAI,CAACT,KAAK,EAAE;MACR;IACJ;IACAK,OAAO,CAACK,WAAW,GAAGF,OAAO;IAC7B,IAAIR,KAAK,CAACL,GAAG,IAAIK,KAAK,CAACW,OAAO,EAAE;MAC5B9B,OAAO,CAACuB,IAAI,CAAC;IACjB,CAAC,MACI,IAAIK,OAAO,EAAE;MACd,MAAMG,KAAK,GAAGZ,KAAK,CAACY,KAAK;QAAEC,GAAG,GAAG;UAC7BC,CAAC,EAAE,CAACR,MAAM;UACVS,CAAC,EAAE,CAACT;QACR,CAAC;QAAEU,QAAQ,GAAGV,MAAM,GAAGxB,MAAM;MAC7BuB,OAAO,CAACY,SAAS,CAACR,OAAO,EAAEI,GAAG,CAACC,CAAC,EAAED,GAAG,CAACE,CAAC,EAAEC,QAAQ,EAAEA,QAAQ,GAAGJ,KAAK,CAAC;IACxE;IACAP,OAAO,CAACK,WAAW,GAAG3B,YAAY;EACtC;EACAmC,aAAaA,CAAA,EAAG;IACZ,OAAOlC,KAAK;EAChB;EACA,MAAMmC,IAAIA,CAACC,SAAS,EAAE;IAClB,MAAMC,OAAO,GAAGD,SAAS,CAACE,aAAa;IACvC,IAAI,CAACD,OAAO,CAACE,OAAO,IAAI,CAAC,IAAI,CAAC/B,OAAO,CAACC,SAAS,EAAE;MAC7C;IACJ;IACA,KAAK,MAAM+B,SAAS,IAAIH,OAAO,CAACE,OAAO,EAAE;MACrC,MAAM,IAAI,CAAC/B,OAAO,CAACC,SAAS,CAAC+B,SAAS,CAAC;IAC3C;EACJ;EACAC,SAASA,CAAClB,QAAQ,EAAE;IAChB,IAAIA,QAAQ,CAACmB,KAAK,KAAK,OAAO,IAAInB,QAAQ,CAACmB,KAAK,KAAK,QAAQ,EAAE;MAC3D;IACJ;IACA,IAAI,CAAC,IAAI,CAAClC,OAAO,CAACS,MAAM,EAAE;MACtB,IAAI,CAACT,OAAO,CAACS,MAAM,GAAG,EAAE;IAC5B;IACA,MAAMuB,SAAS,GAAGjB,QAAQ,CAACoB,SAAS;IACpC,IAAI,CAACH,SAAS,EAAE;MACZ;IACJ;IACA,MAAMxB,KAAK,GAAG,IAAI,CAACR,OAAO,CAACS,MAAM,CAAC2B,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACjC,IAAI,KAAK4B,SAAS,CAAC5B,IAAI,IAAIiC,CAAC,CAACC,MAAM,KAAKN,SAAS,CAAC1B,GAAG,CAAC;IACtG,IAAI,CAACE,KAAK,EAAE;MACR,KAAK,IAAI,CAACV,cAAc,CAACkC,SAAS,CAAC,CAACO,IAAI,CAAC,MAAM;QAC3C,IAAI,CAACN,SAAS,CAAClB,QAAQ,CAAC;MAC5B,CAAC,CAAC;IACN;EACJ;EACAyB,YAAYA,CAACZ,SAAS,EAAEb,QAAQ,EAAE;IAC9B,IAAIA,QAAQ,CAACmB,KAAK,KAAK,OAAO,IAAInB,QAAQ,CAACmB,KAAK,KAAK,QAAQ,EAAE;MAC3D;IACJ;IACA,IAAI,CAAC,IAAI,CAAClC,OAAO,CAACS,MAAM,EAAE;MACtB,IAAI,CAACT,OAAO,CAACS,MAAM,GAAG,EAAE;IAC5B;IACA,MAAMA,MAAM,GAAG,IAAI,CAACT,OAAO,CAACS,MAAM;MAAEuB,SAAS,GAAGjB,QAAQ,CAACoB,SAAS;IAClE,IAAI,CAACH,SAAS,EAAE;MACZ;IACJ;IACA,MAAMS,KAAK,GAAG1B,QAAQ,CAAC2B,YAAY,CAAC,CAAC;MAAElC,KAAK,GAAGC,MAAM,CAAC2B,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACjC,IAAI,KAAK4B,SAAS,CAAC5B,IAAI,IAAIiC,CAAC,CAACC,MAAM,KAAKN,SAAS,CAAC1B,GAAG,CAAC;IAC1H,IAAI,CAACE,KAAK,EAAE;MACR;IACJ;IACA,MAAMH,YAAY,GAAG2B,SAAS,CAAC3B,YAAY,IAAIG,KAAK,CAACH,YAAY;IACjE,IAAIG,KAAK,CAACmC,OAAO,EAAE;MACfC,UAAU,CAAC,MAAM;QACb,IAAI,CAACJ,YAAY,CAACZ,SAAS,EAAEb,QAAQ,CAAC;MAC1C,CAAC,CAAC;MACF;IACJ;IACA,KAAK,CAAC,YAAY;MACd,IAAI8B,QAAQ;MACZ,IAAIrC,KAAK,CAACsC,OAAO,IAAIL,KAAK,EAAE;QACxBI,QAAQ,GAAG,MAAMzD,iBAAiB,CAACoB,KAAK,EAAEwB,SAAS,EAAES,KAAK,EAAE1B,QAAQ,CAAC;MACzE,CAAC,MACI;QACD8B,QAAQ,GAAG;UACPJ,KAAK;UACL7B,IAAI,EAAEJ,KAAK;UACXS,OAAO,EAAET,KAAK,CAACS,OAAO;UACtBd,GAAG,EAAEK,KAAK,CAACL,GAAG;UACdgB,OAAO,EAAEX,KAAK,CAACW,OAAO;UACtB4B,YAAY,EAAEvC,KAAK,CAACuC,YAAY;UAChCC,MAAM,EAAE,IAAI;UACZ5B,KAAK,EAAEY,SAAS,CAACiB,KAAK,IAAIjB,SAAS,CAACkB,MAAM,GACpClB,SAAS,CAACiB,KAAK,GAAGjB,SAAS,CAACkB,MAAM,GAClC1C,KAAK,CAACY,KAAK,IAAI3B,YAAY;UACjCY,YAAY,EAAEA,YAAY;UAC1BiC,MAAM,EAAEN,SAAS,CAAC1B;QACtB,CAAC;MACL;MACA,IAAI,CAACuC,QAAQ,CAACzB,KAAK,EAAE;QACjByB,QAAQ,CAACzB,KAAK,GAAG,CAAC;MACtB;MACA,MAAM+B,IAAI,GAAGnB,SAAS,CAACmB,IAAI,IAAIpC,QAAQ,CAACqC,SAAS;QAAEC,KAAK,GAAGrB,SAAS,CAACqB,KAAK,IAAItC,QAAQ,CAACuC,UAAU;QAAEvD,UAAU,GAAG;UAC5GS,KAAK,EAAEqC,QAAQ;UACfM,IAAI;UACJE;QACJ,CAAC;MACDtC,QAAQ,CAACP,KAAK,GAAGT,UAAU,CAACS,KAAK;MACjCO,QAAQ,CAACqC,SAAS,GAAGrD,UAAU,CAACoD,IAAI;MACpCpC,QAAQ,CAACuC,UAAU,GAAGvD,UAAU,CAACsD,KAAK;IAC1C,CAAC,EAAE,CAAC;EACR;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
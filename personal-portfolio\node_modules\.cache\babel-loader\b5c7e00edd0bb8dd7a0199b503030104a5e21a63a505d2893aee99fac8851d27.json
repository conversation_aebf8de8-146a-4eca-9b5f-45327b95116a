{"ast": null, "code": "import { getRangeValue } from \"../Utils/NumberUtils.js\";\nimport { isSsr } from \"../Utils/Utils.js\";\nconst defaultRatio = 1,\n  defaultReduceFactor = 1;\nexport class Retina {\n  constructor(container) {\n    this.container = container;\n    this.pixelRatio = defaultRatio;\n    this.reduceFactor = defaultReduceFactor;\n  }\n  init() {\n    const container = this.container,\n      options = container.actualOptions;\n    this.pixelRatio = !options.detectRetina || isSsr() ? defaultRatio : window.devicePixelRatio;\n    this.reduceFactor = defaultReduceFactor;\n    const ratio = this.pixelRatio,\n      canvas = container.canvas;\n    if (canvas.element) {\n      const element = canvas.element;\n      canvas.size.width = element.offsetWidth * ratio;\n      canvas.size.height = element.offsetHeight * ratio;\n    }\n    const particles = options.particles,\n      moveOptions = particles.move;\n    this.maxSpeed = getRangeValue(moveOptions.gravity.maxSpeed) * ratio;\n    this.sizeAnimationSpeed = getRangeValue(particles.size.animation.speed) * ratio;\n  }\n  initParticle(particle) {\n    const options = particle.options,\n      ratio = this.pixelRatio,\n      moveOptions = options.move,\n      moveDistance = moveOptions.distance,\n      props = particle.retina;\n    props.moveDrift = getRangeValue(moveOptions.drift) * ratio;\n    props.moveSpeed = getRangeValue(moveOptions.speed) * ratio;\n    props.sizeAnimationSpeed = getRangeValue(options.size.animation.speed) * ratio;\n    const maxDistance = props.maxDistance;\n    maxDistance.horizontal = moveDistance.horizontal !== undefined ? moveDistance.horizontal * ratio : undefined;\n    maxDistance.vertical = moveDistance.vertical !== undefined ? moveDistance.vertical * ratio : undefined;\n    props.maxSpeed = getRangeValue(moveOptions.gravity.maxSpeed) * ratio;\n  }\n}", "map": {"version": 3, "names": ["getRangeValue", "isSsr", "defaultRatio", "defaultReduceFactor", "Retina", "constructor", "container", "pixelRatio", "reduceFactor", "init", "options", "actualOptions", "detectRetina", "window", "devicePixelRatio", "ratio", "canvas", "element", "size", "width", "offsetWidth", "height", "offsetHeight", "particles", "moveOptions", "move", "maxSpeed", "gravity", "sizeAnimationSpeed", "animation", "speed", "initParticle", "particle", "moveDistance", "distance", "props", "retina", "moveDrift", "drift", "moveSpeed", "maxDistance", "horizontal", "undefined", "vertical"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/engine/browser/Core/Retina.js"], "sourcesContent": ["import { getRangeValue } from \"../Utils/NumberUtils.js\";\nimport { isSsr } from \"../Utils/Utils.js\";\nconst defaultRatio = 1, defaultReduceFactor = 1;\nexport class Retina {\n    constructor(container) {\n        this.container = container;\n        this.pixelRatio = defaultRatio;\n        this.reduceFactor = defaultReduceFactor;\n    }\n    init() {\n        const container = this.container, options = container.actualOptions;\n        this.pixelRatio = !options.detectRetina || isSsr() ? defaultRatio : window.devicePixelRatio;\n        this.reduceFactor = defaultReduceFactor;\n        const ratio = this.pixelRatio, canvas = container.canvas;\n        if (canvas.element) {\n            const element = canvas.element;\n            canvas.size.width = element.offsetWidth * ratio;\n            canvas.size.height = element.offsetHeight * ratio;\n        }\n        const particles = options.particles, moveOptions = particles.move;\n        this.maxSpeed = getRangeValue(moveOptions.gravity.maxSpeed) * ratio;\n        this.sizeAnimationSpeed = getRangeValue(particles.size.animation.speed) * ratio;\n    }\n    initParticle(particle) {\n        const options = particle.options, ratio = this.pixelRatio, moveOptions = options.move, moveDistance = moveOptions.distance, props = particle.retina;\n        props.moveDrift = getRangeValue(moveOptions.drift) * ratio;\n        props.moveSpeed = getRangeValue(moveOptions.speed) * ratio;\n        props.sizeAnimationSpeed = getRangeValue(options.size.animation.speed) * ratio;\n        const maxDistance = props.maxDistance;\n        maxDistance.horizontal = moveDistance.horizontal !== undefined ? moveDistance.horizontal * ratio : undefined;\n        maxDistance.vertical = moveDistance.vertical !== undefined ? moveDistance.vertical * ratio : undefined;\n        props.maxSpeed = getRangeValue(moveOptions.gravity.maxSpeed) * ratio;\n    }\n}\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,yBAAyB;AACvD,SAASC,KAAK,QAAQ,mBAAmB;AACzC,MAAMC,YAAY,GAAG,CAAC;EAAEC,mBAAmB,GAAG,CAAC;AAC/C,OAAO,MAAMC,MAAM,CAAC;EAChBC,WAAWA,CAACC,SAAS,EAAE;IACnB,IAAI,CAACA,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,UAAU,GAAGL,YAAY;IAC9B,IAAI,CAACM,YAAY,GAAGL,mBAAmB;EAC3C;EACAM,IAAIA,CAAA,EAAG;IACH,MAAMH,SAAS,GAAG,IAAI,CAACA,SAAS;MAAEI,OAAO,GAAGJ,SAAS,CAACK,aAAa;IACnE,IAAI,CAACJ,UAAU,GAAG,CAACG,OAAO,CAACE,YAAY,IAAIX,KAAK,CAAC,CAAC,GAAGC,YAAY,GAAGW,MAAM,CAACC,gBAAgB;IAC3F,IAAI,CAACN,YAAY,GAAGL,mBAAmB;IACvC,MAAMY,KAAK,GAAG,IAAI,CAACR,UAAU;MAAES,MAAM,GAAGV,SAAS,CAACU,MAAM;IACxD,IAAIA,MAAM,CAACC,OAAO,EAAE;MAChB,MAAMA,OAAO,GAAGD,MAAM,CAACC,OAAO;MAC9BD,MAAM,CAACE,IAAI,CAACC,KAAK,GAAGF,OAAO,CAACG,WAAW,GAAGL,KAAK;MAC/CC,MAAM,CAACE,IAAI,CAACG,MAAM,GAAGJ,OAAO,CAACK,YAAY,GAAGP,KAAK;IACrD;IACA,MAAMQ,SAAS,GAAGb,OAAO,CAACa,SAAS;MAAEC,WAAW,GAAGD,SAAS,CAACE,IAAI;IACjE,IAAI,CAACC,QAAQ,GAAG1B,aAAa,CAACwB,WAAW,CAACG,OAAO,CAACD,QAAQ,CAAC,GAAGX,KAAK;IACnE,IAAI,CAACa,kBAAkB,GAAG5B,aAAa,CAACuB,SAAS,CAACL,IAAI,CAACW,SAAS,CAACC,KAAK,CAAC,GAAGf,KAAK;EACnF;EACAgB,YAAYA,CAACC,QAAQ,EAAE;IACnB,MAAMtB,OAAO,GAAGsB,QAAQ,CAACtB,OAAO;MAAEK,KAAK,GAAG,IAAI,CAACR,UAAU;MAAEiB,WAAW,GAAGd,OAAO,CAACe,IAAI;MAAEQ,YAAY,GAAGT,WAAW,CAACU,QAAQ;MAAEC,KAAK,GAAGH,QAAQ,CAACI,MAAM;IACnJD,KAAK,CAACE,SAAS,GAAGrC,aAAa,CAACwB,WAAW,CAACc,KAAK,CAAC,GAAGvB,KAAK;IAC1DoB,KAAK,CAACI,SAAS,GAAGvC,aAAa,CAACwB,WAAW,CAACM,KAAK,CAAC,GAAGf,KAAK;IAC1DoB,KAAK,CAACP,kBAAkB,GAAG5B,aAAa,CAACU,OAAO,CAACQ,IAAI,CAACW,SAAS,CAACC,KAAK,CAAC,GAAGf,KAAK;IAC9E,MAAMyB,WAAW,GAAGL,KAAK,CAACK,WAAW;IACrCA,WAAW,CAACC,UAAU,GAAGR,YAAY,CAACQ,UAAU,KAAKC,SAAS,GAAGT,YAAY,CAACQ,UAAU,GAAG1B,KAAK,GAAG2B,SAAS;IAC5GF,WAAW,CAACG,QAAQ,GAAGV,YAAY,CAACU,QAAQ,KAAKD,SAAS,GAAGT,YAAY,CAACU,QAAQ,GAAG5B,KAAK,GAAG2B,SAAS;IACtGP,KAAK,CAACT,QAAQ,GAAG1B,aAAa,CAACwB,WAAW,CAACG,OAAO,CAACD,QAAQ,CAAC,GAAGX,KAAK;EACxE;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
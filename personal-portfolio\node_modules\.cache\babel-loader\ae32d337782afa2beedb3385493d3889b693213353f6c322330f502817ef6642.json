{"ast": null, "code": "import { getHslAnimationFromHsl, rangeColorToHsl, updateColor } from \"@tsparticles/engine\";\nexport class ColorUpdater {\n  constructor(container) {\n    this.container = container;\n  }\n  init(particle) {\n    const hslColor = rangeColorToHsl(particle.options.color, particle.id, particle.options.reduceDuplicates);\n    if (hslColor) {\n      particle.color = getHslAnimationFromHsl(hslColor, particle.options.color.animation, this.container.retina.reduceFactor);\n    }\n  }\n  isEnabled(particle) {\n    const {\n        h: hAnimation,\n        s: sAnimation,\n        l: lAnimation\n      } = particle.options.color.animation,\n      {\n        color\n      } = particle;\n    return !particle.destroyed && !particle.spawning && (color?.h.value !== undefined && hAnimation.enable || color?.s.value !== undefined && sAnimation.enable || color?.l.value !== undefined && lAnimation.enable);\n  }\n  update(particle, delta) {\n    updateColor(particle.color, delta);\n  }\n}", "map": {"version": 3, "names": ["getHslAnimationFromHsl", "rangeColorToHsl", "updateColor", "ColorUpdater", "constructor", "container", "init", "particle", "hslColor", "options", "color", "id", "reduceDuplicates", "animation", "retina", "reduceFactor", "isEnabled", "h", "hAnimation", "s", "sAnimation", "l", "lAnimation", "destroyed", "spawning", "value", "undefined", "enable", "update", "delta"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/updater-color/browser/ColorUpdater.js"], "sourcesContent": ["import { getHslAnimationFromHsl, rangeColorToHsl, updateColor, } from \"@tsparticles/engine\";\nexport class ColorUpdater {\n    constructor(container) {\n        this.container = container;\n    }\n    init(particle) {\n        const hslColor = rangeColorToHsl(particle.options.color, particle.id, particle.options.reduceDuplicates);\n        if (hslColor) {\n            particle.color = getHslAnimationFromHsl(hslColor, particle.options.color.animation, this.container.retina.reduceFactor);\n        }\n    }\n    isEnabled(particle) {\n        const { h: hAnimation, s: sAnimation, l: lAnimation } = particle.options.color.animation, { color } = particle;\n        return (!particle.destroyed &&\n            !particle.spawning &&\n            ((color?.h.value !== undefined && hAnimation.enable) ||\n                (color?.s.value !== undefined && sAnimation.enable) ||\n                (color?.l.value !== undefined && lAnimation.enable)));\n    }\n    update(particle, delta) {\n        updateColor(particle.color, delta);\n    }\n}\n"], "mappings": "AAAA,SAASA,sBAAsB,EAAEC,eAAe,EAAEC,WAAW,QAAS,qBAAqB;AAC3F,OAAO,MAAMC,YAAY,CAAC;EACtBC,WAAWA,CAACC,SAAS,EAAE;IACnB,IAAI,CAACA,SAAS,GAAGA,SAAS;EAC9B;EACAC,IAAIA,CAACC,QAAQ,EAAE;IACX,MAAMC,QAAQ,GAAGP,eAAe,CAACM,QAAQ,CAACE,OAAO,CAACC,KAAK,EAAEH,QAAQ,CAACI,EAAE,EAAEJ,QAAQ,CAACE,OAAO,CAACG,gBAAgB,CAAC;IACxG,IAAIJ,QAAQ,EAAE;MACVD,QAAQ,CAACG,KAAK,GAAGV,sBAAsB,CAACQ,QAAQ,EAAED,QAAQ,CAACE,OAAO,CAACC,KAAK,CAACG,SAAS,EAAE,IAAI,CAACR,SAAS,CAACS,MAAM,CAACC,YAAY,CAAC;IAC3H;EACJ;EACAC,SAASA,CAACT,QAAQ,EAAE;IAChB,MAAM;QAAEU,CAAC,EAAEC,UAAU;QAAEC,CAAC,EAAEC,UAAU;QAAEC,CAAC,EAAEC;MAAW,CAAC,GAAGf,QAAQ,CAACE,OAAO,CAACC,KAAK,CAACG,SAAS;MAAE;QAAEH;MAAM,CAAC,GAAGH,QAAQ;IAC9G,OAAQ,CAACA,QAAQ,CAACgB,SAAS,IACvB,CAAChB,QAAQ,CAACiB,QAAQ,KAChBd,KAAK,EAAEO,CAAC,CAACQ,KAAK,KAAKC,SAAS,IAAIR,UAAU,CAACS,MAAM,IAC9CjB,KAAK,EAAES,CAAC,CAACM,KAAK,KAAKC,SAAS,IAAIN,UAAU,CAACO,MAAO,IAClDjB,KAAK,EAAEW,CAAC,CAACI,KAAK,KAAKC,SAAS,IAAIJ,UAAU,CAACK,MAAO,CAAC;EAChE;EACAC,MAAMA,CAACrB,QAAQ,EAAEsB,KAAK,EAAE;IACpB3B,WAAW,CAACK,QAAQ,CAACG,KAAK,EAAEmB,KAAK,CAAC;EACtC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { ParticlesBounceFactor } from \"./ParticlesBounceFactor.js\";\nexport class ParticlesBounce {\n  constructor() {\n    this.horizontal = new ParticlesBounceFactor();\n    this.vertical = new ParticlesBounceFactor();\n  }\n  load(data) {\n    if (!data) {\n      return;\n    }\n    this.horizontal.load(data.horizontal);\n    this.vertical.load(data.vertical);\n  }\n}", "map": {"version": 3, "names": ["ParticlesBounceFactor", "ParticlesBounce", "constructor", "horizontal", "vertical", "load", "data"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/engine/browser/Options/Classes/Particles/Bounce/ParticlesBounce.js"], "sourcesContent": ["import { ParticlesBounceFactor } from \"./ParticlesBounceFactor.js\";\nexport class ParticlesBounce {\n    constructor() {\n        this.horizontal = new ParticlesBounceFactor();\n        this.vertical = new ParticlesBounceFactor();\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        this.horizontal.load(data.horizontal);\n        this.vertical.load(data.vertical);\n    }\n}\n"], "mappings": "AAAA,SAASA,qBAAqB,QAAQ,4BAA4B;AAClE,OAAO,MAAMC,eAAe,CAAC;EACzBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,UAAU,GAAG,IAAIH,qBAAqB,CAAC,CAAC;IAC7C,IAAI,CAACI,QAAQ,GAAG,IAAIJ,qBAAqB,CAAC,CAAC;EAC/C;EACAK,IAAIA,CAACC,IAAI,EAAE;IACP,IAAI,CAACA,IAAI,EAAE;MACP;IACJ;IACA,IAAI,CAACH,UAAU,CAACE,IAAI,CAACC,IAAI,CAACH,UAAU,CAAC;IACrC,IAAI,CAACC,QAAQ,CAACC,IAAI,CAACC,IAAI,CAACF,QAAQ,CAAC;EACrC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { LimitMode } from \"../../../../Enums/Modes/LimitMode.js\";\nexport class ParticlesNumberLimit {\n  constructor() {\n    this.mode = LimitMode.delete;\n    this.value = 0;\n  }\n  load(data) {\n    if (!data) {\n      return;\n    }\n    if (data.mode !== undefined) {\n      this.mode = data.mode;\n    }\n    if (data.value !== undefined) {\n      this.value = data.value;\n    }\n  }\n}", "map": {"version": 3, "names": ["LimitMode", "ParticlesNumberLimit", "constructor", "mode", "delete", "value", "load", "data", "undefined"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/engine/browser/Options/Classes/Particles/Number/ParticlesNumberLimit.js"], "sourcesContent": ["import { LimitMode } from \"../../../../Enums/Modes/LimitMode.js\";\nexport class ParticlesNumberLimit {\n    constructor() {\n        this.mode = LimitMode.delete;\n        this.value = 0;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.mode !== undefined) {\n            this.mode = data.mode;\n        }\n        if (data.value !== undefined) {\n            this.value = data.value;\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,sCAAsC;AAChE,OAAO,MAAMC,oBAAoB,CAAC;EAC9BC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,IAAI,GAAGH,SAAS,CAACI,MAAM;IAC5B,IAAI,CAACC,KAAK,GAAG,CAAC;EAClB;EACAC,IAAIA,CAACC,IAAI,EAAE;IACP,IAAI,CAACA,IAAI,EAAE;MACP;IACJ;IACA,IAAIA,IAAI,CAACJ,IAAI,KAAKK,SAAS,EAAE;MACzB,IAAI,CAACL,IAAI,GAAGI,IAAI,CAACJ,IAAI;IACzB;IACA,IAAII,IAAI,CAACF,KAAK,KAAKG,SAAS,EAAE;MAC1B,IAAI,CAACH,KAAK,GAAGE,IAAI,CAACF,KAAK;IAC3B;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
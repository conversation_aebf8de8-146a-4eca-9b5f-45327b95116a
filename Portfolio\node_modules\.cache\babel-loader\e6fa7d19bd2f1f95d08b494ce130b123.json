{"ast": null, "code": "import { StarDrawer } from \"./StarDrawer\";\nexport async function loadStarShape(engine) {\n  await engine.addShape(\"star\", new StarDrawer());\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Shapes/Star/index.js"], "names": ["StarDrawer", "loadStarShape", "engine", "addShape"], "mappings": "AAAA,SAASA,UAAT,QAA2B,cAA3B;AACA,OAAO,eAAeC,aAAf,CAA6BC,MAA7B,EAAqC;AACxC,QAAMA,MAAM,CAACC,QAAP,CAAgB,MAAhB,EAAwB,IAAIH,UAAJ,EAAxB,CAAN;AACH", "sourcesContent": ["import { StarDrawer } from \"./StarDrawer\";\nexport async function loadStarShape(engine) {\n    await engine.addShape(\"star\", new StarDrawer());\n}\n"]}, "metadata": {}, "sourceType": "module"}
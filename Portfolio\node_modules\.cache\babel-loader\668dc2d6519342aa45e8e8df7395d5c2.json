{"ast": null, "code": "import toDate from \"../../toDate/index.js\";\nimport requiredArgs from \"../requiredArgs/index.js\";\nimport startOfUTCWeek from \"../startOfUTCWeek/index.js\";\nimport toInteger from \"../toInteger/index.js\"; // This function will be a part of public API when UTC function will be implemented.\n// See issue: https://github.com/date-fns/date-fns/issues/376\n\nexport default function getUTCWeekYear(dirtyDate, dirtyOptions) {\n  requiredArgs(1, arguments);\n  var date = toDate(dirtyDate);\n  var year = date.getUTCFullYear();\n  var options = dirtyOptions || {};\n  var locale = options.locale;\n  var localeFirstWeekContainsDate = locale && locale.options && locale.options.firstWeekContainsDate;\n  var defaultFirstWeekContainsDate = localeFirstWeekContainsDate == null ? 1 : toInteger(localeFirstWeekContainsDate);\n  var firstWeekContainsDate = options.firstWeekContainsDate == null ? defaultFirstWeekContainsDate : toInteger(options.firstWeekContainsDate); // Test if weekStartsOn is between 1 and 7 _and_ is not NaN\n\n  if (!(firstWeekContainsDate >= 1 && firstWeekContainsDate <= 7)) {\n    throw new RangeError('firstWeekContainsDate must be between 1 and 7 inclusively');\n  }\n\n  var firstWeekOfNextYear = new Date(0);\n  firstWeekOfNextYear.setUTCFullYear(year + 1, 0, firstWeekContainsDate);\n  firstWeekOfNextYear.setUTCHours(0, 0, 0, 0);\n  var startOfNextYear = startOfUTCWeek(firstWeekOfNextYear, dirtyOptions);\n  var firstWeekOfThisYear = new Date(0);\n  firstWeekOfThisYear.setUTCFullYear(year, 0, firstWeekContainsDate);\n  firstWeekOfThisYear.setUTCHours(0, 0, 0, 0);\n  var startOfThisYear = startOfUTCWeek(firstWeekOfThisYear, dirtyOptions);\n\n  if (date.getTime() >= startOfNextYear.getTime()) {\n    return year + 1;\n  } else if (date.getTime() >= startOfThisYear.getTime()) {\n    return year;\n  } else {\n    return year - 1;\n  }\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/date-fns/esm/_lib/getUTCWeekYear/index.js"], "names": ["toDate", "requiredArgs", "startOfUTCWeek", "toInteger", "getUTCWeekYear", "dirtyDate", "dirtyOptions", "arguments", "date", "year", "getUTCFullYear", "options", "locale", "localeFirstWeekContainsDate", "firstWeekContainsDate", "defaultFirstWeekContainsDate", "RangeError", "firstWeekOfNextYear", "Date", "setUTCFullYear", "setUTCHours", "startOfNextYear", "firstWeekOfThisYear", "startOfThisYear", "getTime"], "mappings": "AAAA,OAAOA,MAAP,MAAmB,uBAAnB;AACA,OAAOC,YAAP,MAAyB,0BAAzB;AACA,OAAOC,cAAP,MAA2B,4BAA3B;AACA,OAAOC,SAAP,MAAsB,uBAAtB,C,CAA+C;AAC/C;;AAEA,eAAe,SAASC,cAAT,CAAwBC,SAAxB,EAAmCC,YAAnC,EAAiD;AAC9DL,EAAAA,YAAY,CAAC,CAAD,EAAIM,SAAJ,CAAZ;AACA,MAAIC,IAAI,GAAGR,MAAM,CAACK,SAAD,CAAjB;AACA,MAAII,IAAI,GAAGD,IAAI,CAACE,cAAL,EAAX;AACA,MAAIC,OAAO,GAAGL,YAAY,IAAI,EAA9B;AACA,MAAIM,MAAM,GAAGD,OAAO,CAACC,MAArB;AACA,MAAIC,2BAA2B,GAAGD,MAAM,IAAIA,MAAM,CAACD,OAAjB,IAA4BC,MAAM,CAACD,OAAP,CAAeG,qBAA7E;AACA,MAAIC,4BAA4B,GAAGF,2BAA2B,IAAI,IAA/B,GAAsC,CAAtC,GAA0CV,SAAS,CAACU,2BAAD,CAAtF;AACA,MAAIC,qBAAqB,GAAGH,OAAO,CAACG,qBAAR,IAAiC,IAAjC,GAAwCC,4BAAxC,GAAuEZ,SAAS,CAACQ,OAAO,CAACG,qBAAT,CAA5G,CAR8D,CAQ+E;;AAE7I,MAAI,EAAEA,qBAAqB,IAAI,CAAzB,IAA8BA,qBAAqB,IAAI,CAAzD,CAAJ,EAAiE;AAC/D,UAAM,IAAIE,UAAJ,CAAe,2DAAf,CAAN;AACD;;AAED,MAAIC,mBAAmB,GAAG,IAAIC,IAAJ,CAAS,CAAT,CAA1B;AACAD,EAAAA,mBAAmB,CAACE,cAApB,CAAmCV,IAAI,GAAG,CAA1C,EAA6C,CAA7C,EAAgDK,qBAAhD;AACAG,EAAAA,mBAAmB,CAACG,WAApB,CAAgC,CAAhC,EAAmC,CAAnC,EAAsC,CAAtC,EAAyC,CAAzC;AACA,MAAIC,eAAe,GAAGnB,cAAc,CAACe,mBAAD,EAAsBX,YAAtB,CAApC;AACA,MAAIgB,mBAAmB,GAAG,IAAIJ,IAAJ,CAAS,CAAT,CAA1B;AACAI,EAAAA,mBAAmB,CAACH,cAApB,CAAmCV,IAAnC,EAAyC,CAAzC,EAA4CK,qBAA5C;AACAQ,EAAAA,mBAAmB,CAACF,WAApB,CAAgC,CAAhC,EAAmC,CAAnC,EAAsC,CAAtC,EAAyC,CAAzC;AACA,MAAIG,eAAe,GAAGrB,cAAc,CAACoB,mBAAD,EAAsBhB,YAAtB,CAApC;;AAEA,MAAIE,IAAI,CAACgB,OAAL,MAAkBH,eAAe,CAACG,OAAhB,EAAtB,EAAiD;AAC/C,WAAOf,IAAI,GAAG,CAAd;AACD,GAFD,MAEO,IAAID,IAAI,CAACgB,OAAL,MAAkBD,eAAe,CAACC,OAAhB,EAAtB,EAAiD;AACtD,WAAOf,IAAP;AACD,GAFM,MAEA;AACL,WAAOA,IAAI,GAAG,CAAd;AACD;AACF", "sourcesContent": ["import toDate from \"../../toDate/index.js\";\nimport requiredArgs from \"../requiredArgs/index.js\";\nimport startOfUTCWeek from \"../startOfUTCWeek/index.js\";\nimport toInteger from \"../toInteger/index.js\"; // This function will be a part of public API when UTC function will be implemented.\n// See issue: https://github.com/date-fns/date-fns/issues/376\n\nexport default function getUTCWeekYear(dirtyDate, dirtyOptions) {\n  requiredArgs(1, arguments);\n  var date = toDate(dirtyDate);\n  var year = date.getUTCFullYear();\n  var options = dirtyOptions || {};\n  var locale = options.locale;\n  var localeFirstWeekContainsDate = locale && locale.options && locale.options.firstWeekContainsDate;\n  var defaultFirstWeekContainsDate = localeFirstWeekContainsDate == null ? 1 : toInteger(localeFirstWeekContainsDate);\n  var firstWeekContainsDate = options.firstWeekContainsDate == null ? defaultFirstWeekContainsDate : toInteger(options.firstWeekContainsDate); // Test if weekStartsOn is between 1 and 7 _and_ is not NaN\n\n  if (!(firstWeekContainsDate >= 1 && firstWeekContainsDate <= 7)) {\n    throw new RangeError('firstWeekContainsDate must be between 1 and 7 inclusively');\n  }\n\n  var firstWeekOfNextYear = new Date(0);\n  firstWeekOfNextYear.setUTCFullYear(year + 1, 0, firstWeekContainsDate);\n  firstWeekOfNextYear.setUTCHours(0, 0, 0, 0);\n  var startOfNextYear = startOfUTCWeek(firstWeekOfNextYear, dirtyOptions);\n  var firstWeekOfThisYear = new Date(0);\n  firstWeekOfThisYear.setUTCFullYear(year, 0, firstWeekContainsDate);\n  firstWeekOfThisYear.setUTCHours(0, 0, 0, 0);\n  var startOfThisYear = startOfUTCWeek(firstWeekOfThisYear, dirtyOptions);\n\n  if (date.getTime() >= startOfNextYear.getTime()) {\n    return year + 1;\n  } else if (date.getTime() >= startOfThisYear.getTime()) {\n    return year;\n  } else {\n    return year - 1;\n  }\n}"]}, "metadata": {}, "sourceType": "module"}
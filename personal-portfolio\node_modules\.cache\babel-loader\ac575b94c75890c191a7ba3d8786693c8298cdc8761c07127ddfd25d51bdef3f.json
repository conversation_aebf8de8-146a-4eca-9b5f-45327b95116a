{"ast": null, "code": "import { EmittersCircleShapeGenerator } from \"./EmittersCircleShapeGenerator.js\";\nexport async function loadEmittersShapeCircle(engine, refresh = true) {\n  const emittersEngine = engine;\n  emittersEngine.addEmitterShapeGenerator?.(\"circle\", new EmittersCircleShapeGenerator());\n  await emittersEngine.refresh(refresh);\n}", "map": {"version": 3, "names": ["EmittersCircleShapeGenerator", "loadEmittersShapeCircle", "engine", "refresh", "emittersEngine", "addEmitterShapeGenerator"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/plugin-emitters-shape-circle/browser/index.js"], "sourcesContent": ["import { EmittersCircleShapeGenerator } from \"./EmittersCircleShapeGenerator.js\";\nexport async function loadEmittersShapeCircle(engine, refresh = true) {\n    const emittersEngine = engine;\n    emittersEngine.addEmitterShapeGenerator?.(\"circle\", new EmittersCircleShapeGenerator());\n    await emittersEngine.refresh(refresh);\n}\n"], "mappings": "AAAA,SAASA,4BAA4B,QAAQ,mCAAmC;AAChF,OAAO,eAAeC,uBAAuBA,CAACC,MAAM,EAAEC,OAAO,GAAG,IAAI,EAAE;EAClE,MAAMC,cAAc,GAAGF,MAAM;EAC7BE,cAAc,CAACC,wBAAwB,GAAG,QAAQ,EAAE,IAAIL,4BAA4B,CAAC,CAAC,CAAC;EACvF,MAAMI,cAAc,CAACD,OAAO,CAACA,OAAO,CAAC;AACzC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
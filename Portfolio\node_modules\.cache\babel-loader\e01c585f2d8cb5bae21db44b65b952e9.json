{"ast": null, "code": "import getNodeName from \"./getNodeName.js\";\nexport default function isTableElement(element) {\n  return ['table', 'td', 'th'].indexOf(getNodeName(element)) >= 0;\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/@popperjs/core/lib/dom-utils/isTableElement.js"], "names": ["getNodeName", "isTableElement", "element", "indexOf"], "mappings": "AAAA,OAAOA,WAAP,MAAwB,kBAAxB;AACA,eAAe,SAASC,cAAT,CAAwBC,OAAxB,EAAiC;AAC9C,SAAO,CAAC,OAAD,EAAU,IAAV,EAAgB,IAAhB,EAAsBC,OAAtB,CAA8BH,WAAW,CAACE,OAAD,CAAzC,KAAuD,CAA9D;AACD", "sourcesContent": ["import getNodeName from \"./getNodeName.js\";\nexport default function isTableElement(element) {\n  return ['table', 'td', 'th'].indexOf(getNodeName(element)) >= 0;\n}"]}, "metadata": {}, "sourceType": "module"}
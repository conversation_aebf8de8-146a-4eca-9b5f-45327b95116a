{"ast": null, "code": "export class Preload {\n  constructor() {\n    this.src = \"\";\n    this.gif = false;\n  }\n  load(data) {\n    if (!data) {\n      return;\n    }\n    if (data.gif !== undefined) {\n      this.gif = data.gif;\n    }\n    if (data.height !== undefined) {\n      this.height = data.height;\n    }\n    if (data.name !== undefined) {\n      this.name = data.name;\n    }\n    if (data.replaceColor !== undefined) {\n      this.replaceColor = data.replaceColor;\n    }\n    if (data.src !== undefined) {\n      this.src = data.src;\n    }\n    if (data.width !== undefined) {\n      this.width = data.width;\n    }\n  }\n}", "map": {"version": 3, "names": ["Preload", "constructor", "src", "gif", "load", "data", "undefined", "height", "name", "replaceColor", "width"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/tsparticles-shape-image/esm/Options/Classes/Preload.js"], "sourcesContent": ["export class Preload {\n    constructor() {\n        this.src = \"\";\n        this.gif = false;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.gif !== undefined) {\n            this.gif = data.gif;\n        }\n        if (data.height !== undefined) {\n            this.height = data.height;\n        }\n        if (data.name !== undefined) {\n            this.name = data.name;\n        }\n        if (data.replaceColor !== undefined) {\n            this.replaceColor = data.replaceColor;\n        }\n        if (data.src !== undefined) {\n            this.src = data.src;\n        }\n        if (data.width !== undefined) {\n            this.width = data.width;\n        }\n    }\n}\n"], "mappings": "AAAA,OAAO,MAAMA,OAAO,CAAC;EACjBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,GAAG,GAAG,EAAE;IACb,IAAI,CAACC,GAAG,GAAG,KAAK;EACpB;EACAC,IAAIA,CAACC,IAAI,EAAE;IACP,IAAI,CAACA,IAAI,EAAE;MACP;IACJ;IACA,IAAIA,IAAI,CAACF,GAAG,KAAKG,SAAS,EAAE;MACxB,IAAI,CAACH,GAAG,GAAGE,IAAI,CAACF,GAAG;IACvB;IACA,IAAIE,IAAI,CAACE,MAAM,KAAKD,SAAS,EAAE;MAC3B,IAAI,CAACC,MAAM,GAAGF,IAAI,CAACE,MAAM;IAC7B;IACA,IAAIF,IAAI,CAACG,IAAI,KAAKF,SAAS,EAAE;MACzB,IAAI,CAACE,IAAI,GAAGH,IAAI,CAACG,IAAI;IACzB;IACA,IAAIH,IAAI,CAACI,YAAY,KAAKH,SAAS,EAAE;MACjC,IAAI,CAACG,YAAY,GAAGJ,IAAI,CAACI,YAAY;IACzC;IACA,IAAIJ,IAAI,CAACH,GAAG,KAAKI,SAAS,EAAE;MACxB,IAAI,CAACJ,GAAG,GAAGG,IAAI,CAACH,GAAG;IACvB;IACA,IAAIG,IAAI,CAACK,KAAK,KAAKJ,SAAS,EAAE;MAC1B,IAAI,CAACI,KAAK,GAAGL,IAAI,CAACK,KAAK;IAC3B;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { colorToHsl, getHslAnimationFromHsl, itemFromArray, randomInRange } from \"../../Utils\";\n\nfunction updateColorValue(delta, value, valueAnimation, max, decrease) {\n  var _a;\n\n  const colorValue = value;\n\n  if (!colorValue || !colorValue.enable) {\n    return;\n  }\n\n  const offset = randomInRange(valueAnimation.offset);\n  const velocity = ((_a = value.velocity) !== null && _a !== void 0 ? _a : 0) * delta.factor + offset * 3.6;\n\n  if (!decrease || colorValue.status === 0) {\n    colorValue.value += velocity;\n\n    if (decrease && colorValue.value > max) {\n      colorValue.status = 1;\n      colorValue.value -= colorValue.value % max;\n    }\n  } else {\n    colorValue.value -= velocity;\n\n    if (colorValue.value < 0) {\n      colorValue.status = 0;\n      colorValue.value += colorValue.value;\n    }\n  }\n\n  if (colorValue.value > max) {\n    colorValue.value %= max;\n  }\n}\n\nfunction updateStrokeColor(particle, delta) {\n  var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k;\n\n  if (!((_a = particle.stroke) === null || _a === void 0 ? void 0 : _a.color)) {\n    return;\n  }\n\n  const animationOptions = particle.stroke.color.animation;\n  const h = (_c = (_b = particle.strokeColor) === null || _b === void 0 ? void 0 : _b.h) !== null && _c !== void 0 ? _c : (_d = particle.color) === null || _d === void 0 ? void 0 : _d.h;\n\n  if (h) {\n    updateColorValue(delta, h, animationOptions.h, 360, false);\n  }\n\n  const s = (_f = (_e = particle.strokeColor) === null || _e === void 0 ? void 0 : _e.s) !== null && _f !== void 0 ? _f : (_g = particle.color) === null || _g === void 0 ? void 0 : _g.s;\n\n  if (s) {\n    updateColorValue(delta, s, animationOptions.s, 100, true);\n  }\n\n  const l = (_j = (_h = particle.strokeColor) === null || _h === void 0 ? void 0 : _h.l) !== null && _j !== void 0 ? _j : (_k = particle.color) === null || _k === void 0 ? void 0 : _k.l;\n\n  if (l) {\n    updateColorValue(delta, l, animationOptions.l, 100, true);\n  }\n}\n\nexport class StrokeColorUpdater {\n  constructor(container) {\n    this.container = container;\n  }\n\n  init(particle) {\n    var _a, _b;\n\n    const container = this.container;\n    particle.stroke = particle.options.stroke instanceof Array ? itemFromArray(particle.options.stroke, particle.id, particle.options.reduceDuplicates) : particle.options.stroke;\n    particle.strokeWidth = particle.stroke.width * container.retina.pixelRatio;\n    const strokeHslColor = (_a = colorToHsl(particle.stroke.color)) !== null && _a !== void 0 ? _a : particle.getFillColor();\n\n    if (strokeHslColor) {\n      particle.strokeColor = getHslAnimationFromHsl(strokeHslColor, (_b = particle.stroke.color) === null || _b === void 0 ? void 0 : _b.animation, container.retina.reduceFactor);\n    }\n  }\n\n  isEnabled(particle) {\n    var _a, _b, _c, _d;\n\n    const color = (_a = particle.stroke) === null || _a === void 0 ? void 0 : _a.color;\n    return !particle.destroyed && !particle.spawning && !!color && (((_b = particle.strokeColor) === null || _b === void 0 ? void 0 : _b.h.value) !== undefined && color.animation.h.enable || ((_c = particle.strokeColor) === null || _c === void 0 ? void 0 : _c.s.value) !== undefined && color.animation.s.enable || ((_d = particle.strokeColor) === null || _d === void 0 ? void 0 : _d.l.value) !== undefined && color.animation.l.enable);\n  }\n\n  update(particle, delta) {\n    if (!this.isEnabled(particle)) {\n      return;\n    }\n\n    updateStrokeColor(particle, delta);\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Updaters/StrokeColor/StrokeColorUpdater.js"], "names": ["colorToHsl", "getHslAnimationFromHsl", "itemFromArray", "randomInRange", "updateColorValue", "delta", "value", "valueAnimation", "max", "decrease", "_a", "colorValue", "enable", "offset", "velocity", "factor", "status", "updateStrokeColor", "particle", "_b", "_c", "_d", "_e", "_f", "_g", "_h", "_j", "_k", "stroke", "color", "animationOptions", "animation", "h", "strokeColor", "s", "l", "StrokeColorUpdater", "constructor", "container", "init", "options", "Array", "id", "reduceDuplicates", "strokeWidth", "width", "retina", "pixelRatio", "strokeHslColor", "getFillColor", "reduceFactor", "isEnabled", "destroyed", "spawning", "undefined", "update"], "mappings": "AAAA,SAASA,UAAT,EAAqBC,sBAArB,EAA6CC,aAA7C,EAA4DC,aAA5D,QAAiF,aAAjF;;AACA,SAASC,gBAAT,CAA0BC,KAA1B,EAAiCC,KAAjC,EAAwCC,cAAxC,EAAwDC,GAAxD,EAA6DC,QAA7D,EAAuE;AACnE,MAAIC,EAAJ;;AACA,QAAMC,UAAU,GAAGL,KAAnB;;AACA,MAAI,CAACK,UAAD,IAAe,CAACA,UAAU,CAACC,MAA/B,EAAuC;AACnC;AACH;;AACD,QAAMC,MAAM,GAAGV,aAAa,CAACI,cAAc,CAACM,MAAhB,CAA5B;AACA,QAAMC,QAAQ,GAAG,CAAC,CAACJ,EAAE,GAAGJ,KAAK,CAACQ,QAAZ,MAA0B,IAA1B,IAAkCJ,EAAE,KAAK,KAAK,CAA9C,GAAkDA,EAAlD,GAAuD,CAAxD,IAA6DL,KAAK,CAACU,MAAnE,GAA4EF,MAAM,GAAG,GAAtG;;AACA,MAAI,CAACJ,QAAD,IAAaE,UAAU,CAACK,MAAX,KAAsB,CAAvC,EAA0C;AACtCL,IAAAA,UAAU,CAACL,KAAX,IAAoBQ,QAApB;;AACA,QAAIL,QAAQ,IAAIE,UAAU,CAACL,KAAX,GAAmBE,GAAnC,EAAwC;AACpCG,MAAAA,UAAU,CAACK,MAAX,GAAoB,CAApB;AACAL,MAAAA,UAAU,CAACL,KAAX,IAAoBK,UAAU,CAACL,KAAX,GAAmBE,GAAvC;AACH;AACJ,GAND,MAOK;AACDG,IAAAA,UAAU,CAACL,KAAX,IAAoBQ,QAApB;;AACA,QAAIH,UAAU,CAACL,KAAX,GAAmB,CAAvB,EAA0B;AACtBK,MAAAA,UAAU,CAACK,MAAX,GAAoB,CAApB;AACAL,MAAAA,UAAU,CAACL,KAAX,IAAoBK,UAAU,CAACL,KAA/B;AACH;AACJ;;AACD,MAAIK,UAAU,CAACL,KAAX,GAAmBE,GAAvB,EAA4B;AACxBG,IAAAA,UAAU,CAACL,KAAX,IAAoBE,GAApB;AACH;AACJ;;AACD,SAASS,iBAAT,CAA2BC,QAA3B,EAAqCb,KAArC,EAA4C;AACxC,MAAIK,EAAJ,EAAQS,EAAR,EAAYC,EAAZ,EAAgBC,EAAhB,EAAoBC,EAApB,EAAwBC,EAAxB,EAA4BC,EAA5B,EAAgCC,EAAhC,EAAoCC,EAApC,EAAwCC,EAAxC;;AACA,MAAI,EAAE,CAACjB,EAAE,GAAGQ,QAAQ,CAACU,MAAf,MAA2B,IAA3B,IAAmClB,EAAE,KAAK,KAAK,CAA/C,GAAmD,KAAK,CAAxD,GAA4DA,EAAE,CAACmB,KAAjE,CAAJ,EAA6E;AACzE;AACH;;AACD,QAAMC,gBAAgB,GAAGZ,QAAQ,CAACU,MAAT,CAAgBC,KAAhB,CAAsBE,SAA/C;AACA,QAAMC,CAAC,GAAG,CAACZ,EAAE,GAAG,CAACD,EAAE,GAAGD,QAAQ,CAACe,WAAf,MAAgC,IAAhC,IAAwCd,EAAE,KAAK,KAAK,CAApD,GAAwD,KAAK,CAA7D,GAAiEA,EAAE,CAACa,CAA1E,MAAiF,IAAjF,IAAyFZ,EAAE,KAAK,KAAK,CAArG,GAAyGA,EAAzG,GAA8G,CAACC,EAAE,GAAGH,QAAQ,CAACW,KAAf,MAA0B,IAA1B,IAAkCR,EAAE,KAAK,KAAK,CAA9C,GAAkD,KAAK,CAAvD,GAA2DA,EAAE,CAACW,CAAtL;;AACA,MAAIA,CAAJ,EAAO;AACH5B,IAAAA,gBAAgB,CAACC,KAAD,EAAQ2B,CAAR,EAAWF,gBAAgB,CAACE,CAA5B,EAA+B,GAA/B,EAAoC,KAApC,CAAhB;AACH;;AACD,QAAME,CAAC,GAAG,CAACX,EAAE,GAAG,CAACD,EAAE,GAAGJ,QAAQ,CAACe,WAAf,MAAgC,IAAhC,IAAwCX,EAAE,KAAK,KAAK,CAApD,GAAwD,KAAK,CAA7D,GAAiEA,EAAE,CAACY,CAA1E,MAAiF,IAAjF,IAAyFX,EAAE,KAAK,KAAK,CAArG,GAAyGA,EAAzG,GAA8G,CAACC,EAAE,GAAGN,QAAQ,CAACW,KAAf,MAA0B,IAA1B,IAAkCL,EAAE,KAAK,KAAK,CAA9C,GAAkD,KAAK,CAAvD,GAA2DA,EAAE,CAACU,CAAtL;;AACA,MAAIA,CAAJ,EAAO;AACH9B,IAAAA,gBAAgB,CAACC,KAAD,EAAQ6B,CAAR,EAAWJ,gBAAgB,CAACI,CAA5B,EAA+B,GAA/B,EAAoC,IAApC,CAAhB;AACH;;AACD,QAAMC,CAAC,GAAG,CAACT,EAAE,GAAG,CAACD,EAAE,GAAGP,QAAQ,CAACe,WAAf,MAAgC,IAAhC,IAAwCR,EAAE,KAAK,KAAK,CAApD,GAAwD,KAAK,CAA7D,GAAiEA,EAAE,CAACU,CAA1E,MAAiF,IAAjF,IAAyFT,EAAE,KAAK,KAAK,CAArG,GAAyGA,EAAzG,GAA8G,CAACC,EAAE,GAAGT,QAAQ,CAACW,KAAf,MAA0B,IAA1B,IAAkCF,EAAE,KAAK,KAAK,CAA9C,GAAkD,KAAK,CAAvD,GAA2DA,EAAE,CAACQ,CAAtL;;AACA,MAAIA,CAAJ,EAAO;AACH/B,IAAAA,gBAAgB,CAACC,KAAD,EAAQ8B,CAAR,EAAWL,gBAAgB,CAACK,CAA5B,EAA+B,GAA/B,EAAoC,IAApC,CAAhB;AACH;AACJ;;AACD,OAAO,MAAMC,kBAAN,CAAyB;AAC5BC,EAAAA,WAAW,CAACC,SAAD,EAAY;AACnB,SAAKA,SAAL,GAAiBA,SAAjB;AACH;;AACDC,EAAAA,IAAI,CAACrB,QAAD,EAAW;AACX,QAAIR,EAAJ,EAAQS,EAAR;;AACA,UAAMmB,SAAS,GAAG,KAAKA,SAAvB;AACApB,IAAAA,QAAQ,CAACU,MAAT,GACIV,QAAQ,CAACsB,OAAT,CAAiBZ,MAAjB,YAAmCa,KAAnC,GACMvC,aAAa,CAACgB,QAAQ,CAACsB,OAAT,CAAiBZ,MAAlB,EAA0BV,QAAQ,CAACwB,EAAnC,EAAuCxB,QAAQ,CAACsB,OAAT,CAAiBG,gBAAxD,CADnB,GAEMzB,QAAQ,CAACsB,OAAT,CAAiBZ,MAH3B;AAIAV,IAAAA,QAAQ,CAAC0B,WAAT,GAAuB1B,QAAQ,CAACU,MAAT,CAAgBiB,KAAhB,GAAwBP,SAAS,CAACQ,MAAV,CAAiBC,UAAhE;AACA,UAAMC,cAAc,GAAG,CAACtC,EAAE,GAAGV,UAAU,CAACkB,QAAQ,CAACU,MAAT,CAAgBC,KAAjB,CAAhB,MAA6C,IAA7C,IAAqDnB,EAAE,KAAK,KAAK,CAAjE,GAAqEA,EAArE,GAA0EQ,QAAQ,CAAC+B,YAAT,EAAjG;;AACA,QAAID,cAAJ,EAAoB;AAChB9B,MAAAA,QAAQ,CAACe,WAAT,GAAuBhC,sBAAsB,CAAC+C,cAAD,EAAiB,CAAC7B,EAAE,GAAGD,QAAQ,CAACU,MAAT,CAAgBC,KAAtB,MAAiC,IAAjC,IAAyCV,EAAE,KAAK,KAAK,CAArD,GAAyD,KAAK,CAA9D,GAAkEA,EAAE,CAACY,SAAtF,EAAiGO,SAAS,CAACQ,MAAV,CAAiBI,YAAlH,CAA7C;AACH;AACJ;;AACDC,EAAAA,SAAS,CAACjC,QAAD,EAAW;AAChB,QAAIR,EAAJ,EAAQS,EAAR,EAAYC,EAAZ,EAAgBC,EAAhB;;AACA,UAAMQ,KAAK,GAAG,CAACnB,EAAE,GAAGQ,QAAQ,CAACU,MAAf,MAA2B,IAA3B,IAAmClB,EAAE,KAAK,KAAK,CAA/C,GAAmD,KAAK,CAAxD,GAA4DA,EAAE,CAACmB,KAA7E;AACA,WAAQ,CAACX,QAAQ,CAACkC,SAAV,IACJ,CAAClC,QAAQ,CAACmC,QADN,IAEJ,CAAC,CAACxB,KAFE,KAGF,CAAC,CAACV,EAAE,GAAGD,QAAQ,CAACe,WAAf,MAAgC,IAAhC,IAAwCd,EAAE,KAAK,KAAK,CAApD,GAAwD,KAAK,CAA7D,GAAiEA,EAAE,CAACa,CAAH,CAAK1B,KAAvE,MAAkFgD,SAAlF,IAA+FzB,KAAK,CAACE,SAAN,CAAgBC,CAAhB,CAAkBpB,MAAlH,IACI,CAAC,CAACQ,EAAE,GAAGF,QAAQ,CAACe,WAAf,MAAgC,IAAhC,IAAwCb,EAAE,KAAK,KAAK,CAApD,GAAwD,KAAK,CAA7D,GAAiEA,EAAE,CAACc,CAAH,CAAK5B,KAAvE,MAAkFgD,SAAlF,IAA+FzB,KAAK,CAACE,SAAN,CAAgBG,CAAhB,CAAkBtB,MADrH,IAEI,CAAC,CAACS,EAAE,GAAGH,QAAQ,CAACe,WAAf,MAAgC,IAAhC,IAAwCZ,EAAE,KAAK,KAAK,CAApD,GAAwD,KAAK,CAA7D,GAAiEA,EAAE,CAACc,CAAH,CAAK7B,KAAvE,MAAkFgD,SAAlF,IAA+FzB,KAAK,CAACE,SAAN,CAAgBI,CAAhB,CAAkBvB,MALlH,CAAR;AAMH;;AACD2C,EAAAA,MAAM,CAACrC,QAAD,EAAWb,KAAX,EAAkB;AACpB,QAAI,CAAC,KAAK8C,SAAL,CAAejC,QAAf,CAAL,EAA+B;AAC3B;AACH;;AACDD,IAAAA,iBAAiB,CAACC,QAAD,EAAWb,KAAX,CAAjB;AACH;;AAhC2B", "sourcesContent": ["import { colorToHsl, getHslAnimationFromHsl, itemFromArray, randomInRange } from \"../../Utils\";\nfunction updateColorValue(delta, value, valueAnimation, max, decrease) {\n    var _a;\n    const colorValue = value;\n    if (!colorValue || !colorValue.enable) {\n        return;\n    }\n    const offset = randomInRange(valueAnimation.offset);\n    const velocity = ((_a = value.velocity) !== null && _a !== void 0 ? _a : 0) * delta.factor + offset * 3.6;\n    if (!decrease || colorValue.status === 0) {\n        colorValue.value += velocity;\n        if (decrease && colorValue.value > max) {\n            colorValue.status = 1;\n            colorValue.value -= colorValue.value % max;\n        }\n    }\n    else {\n        colorValue.value -= velocity;\n        if (colorValue.value < 0) {\n            colorValue.status = 0;\n            colorValue.value += colorValue.value;\n        }\n    }\n    if (colorValue.value > max) {\n        colorValue.value %= max;\n    }\n}\nfunction updateStrokeColor(particle, delta) {\n    var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k;\n    if (!((_a = particle.stroke) === null || _a === void 0 ? void 0 : _a.color)) {\n        return;\n    }\n    const animationOptions = particle.stroke.color.animation;\n    const h = (_c = (_b = particle.strokeColor) === null || _b === void 0 ? void 0 : _b.h) !== null && _c !== void 0 ? _c : (_d = particle.color) === null || _d === void 0 ? void 0 : _d.h;\n    if (h) {\n        updateColorValue(delta, h, animationOptions.h, 360, false);\n    }\n    const s = (_f = (_e = particle.strokeColor) === null || _e === void 0 ? void 0 : _e.s) !== null && _f !== void 0 ? _f : (_g = particle.color) === null || _g === void 0 ? void 0 : _g.s;\n    if (s) {\n        updateColorValue(delta, s, animationOptions.s, 100, true);\n    }\n    const l = (_j = (_h = particle.strokeColor) === null || _h === void 0 ? void 0 : _h.l) !== null && _j !== void 0 ? _j : (_k = particle.color) === null || _k === void 0 ? void 0 : _k.l;\n    if (l) {\n        updateColorValue(delta, l, animationOptions.l, 100, true);\n    }\n}\nexport class StrokeColorUpdater {\n    constructor(container) {\n        this.container = container;\n    }\n    init(particle) {\n        var _a, _b;\n        const container = this.container;\n        particle.stroke =\n            particle.options.stroke instanceof Array\n                ? itemFromArray(particle.options.stroke, particle.id, particle.options.reduceDuplicates)\n                : particle.options.stroke;\n        particle.strokeWidth = particle.stroke.width * container.retina.pixelRatio;\n        const strokeHslColor = (_a = colorToHsl(particle.stroke.color)) !== null && _a !== void 0 ? _a : particle.getFillColor();\n        if (strokeHslColor) {\n            particle.strokeColor = getHslAnimationFromHsl(strokeHslColor, (_b = particle.stroke.color) === null || _b === void 0 ? void 0 : _b.animation, container.retina.reduceFactor);\n        }\n    }\n    isEnabled(particle) {\n        var _a, _b, _c, _d;\n        const color = (_a = particle.stroke) === null || _a === void 0 ? void 0 : _a.color;\n        return (!particle.destroyed &&\n            !particle.spawning &&\n            !!color &&\n            ((((_b = particle.strokeColor) === null || _b === void 0 ? void 0 : _b.h.value) !== undefined && color.animation.h.enable) ||\n                (((_c = particle.strokeColor) === null || _c === void 0 ? void 0 : _c.s.value) !== undefined && color.animation.s.enable) ||\n                (((_d = particle.strokeColor) === null || _d === void 0 ? void 0 : _d.l.value) !== undefined && color.animation.l.enable)));\n    }\n    update(particle, delta) {\n        if (!this.isEnabled(particle)) {\n            return;\n        }\n        updateStrokeColor(particle, delta);\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
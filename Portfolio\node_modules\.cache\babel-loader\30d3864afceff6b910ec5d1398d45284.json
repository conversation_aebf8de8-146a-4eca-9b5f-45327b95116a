{"ast": null, "code": "import { Linker } from \"./Linker\";\nexport async function loadInteraction(engine) {\n  await engine.addInteractor(\"particlesLinks\", container => new Linker(container));\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Interactions/Particles/Links/interaction.js"], "names": ["<PERSON><PERSON>", "loadInteraction", "engine", "addInteractor", "container"], "mappings": "AAAA,SAASA,MAAT,QAAuB,UAAvB;AACA,OAAO,eAAeC,eAAf,CAA+BC,MAA/B,EAAuC;AAC1C,QAAMA,MAAM,CAACC,aAAP,CAAqB,gBAArB,EAAwCC,SAAD,IAAe,IAAIJ,MAAJ,CAAWI,SAAX,CAAtD,CAAN;AACH", "sourcesContent": ["import { Linker } from \"./Linker\";\nexport async function loadInteraction(engine) {\n    await engine.addInteractor(\"particlesLinks\", (container) => new Linker(container));\n}\n"]}, "metadata": {}, "sourceType": "module"}
{"ast": null, "code": "import { getRangeValue, millisecondsToSeconds, randomInRange, setRangeValue } from \"@tsparticles/engine\";\nconst noTime = 0,\n  infiniteValue = -1,\n  noLife = 0,\n  minCanvasSize = 0;\nexport function updateLife(particle, delta, canvasSize) {\n  if (!particle.life) {\n    return;\n  }\n  const life = particle.life;\n  let justSpawned = false;\n  if (particle.spawning) {\n    life.delayTime += delta.value;\n    if (life.delayTime >= particle.life.delay) {\n      justSpawned = true;\n      particle.spawning = false;\n      life.delayTime = noTime;\n      life.time = noTime;\n    } else {\n      return;\n    }\n  }\n  if (life.duration === infiniteValue) {\n    return;\n  }\n  if (particle.spawning) {\n    return;\n  }\n  if (justSpawned) {\n    life.time = noTime;\n  } else {\n    life.time += delta.value;\n  }\n  if (life.time < life.duration) {\n    return;\n  }\n  life.time = noTime;\n  if (particle.life.count > noLife) {\n    particle.life.count--;\n  }\n  if (particle.life.count === noLife) {\n    particle.destroy();\n    return;\n  }\n  const widthRange = setRangeValue(minCanvasSize, canvasSize.width),\n    heightRange = setRangeValue(minCanvasSize, canvasSize.width);\n  particle.position.x = randomInRange(widthRange);\n  particle.position.y = randomInRange(heightRange);\n  particle.spawning = true;\n  life.delayTime = noTime;\n  life.time = noTime;\n  particle.reset();\n  const lifeOptions = particle.options.life;\n  if (lifeOptions) {\n    life.delay = getRangeValue(lifeOptions.delay.value) * millisecondsToSeconds;\n    life.duration = getRangeValue(lifeOptions.duration.value) * millisecondsToSeconds;\n  }\n}", "map": {"version": 3, "names": ["getRangeValue", "millisecondsToSeconds", "randomInRange", "setRangeValue", "noTime", "infiniteValue", "noLife", "minCanvasSize", "updateLife", "particle", "delta", "canvasSize", "life", "justSpawned", "spawning", "delayTime", "value", "delay", "time", "duration", "count", "destroy", "widthRange", "width", "heightRange", "position", "x", "y", "reset", "lifeOptions", "options"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/updater-life/browser/Utils.js"], "sourcesContent": ["import { getRangeValue, millisecondsToSeconds, randomInRange, setRangeValue, } from \"@tsparticles/engine\";\nconst noTime = 0, infiniteValue = -1, noLife = 0, minCanvasSize = 0;\nexport function updateLife(particle, delta, canvasSize) {\n    if (!particle.life) {\n        return;\n    }\n    const life = particle.life;\n    let justSpawned = false;\n    if (particle.spawning) {\n        life.delayTime += delta.value;\n        if (life.delayTime >= particle.life.delay) {\n            justSpawned = true;\n            particle.spawning = false;\n            life.delayTime = noTime;\n            life.time = noTime;\n        }\n        else {\n            return;\n        }\n    }\n    if (life.duration === infiniteValue) {\n        return;\n    }\n    if (particle.spawning) {\n        return;\n    }\n    if (justSpawned) {\n        life.time = noTime;\n    }\n    else {\n        life.time += delta.value;\n    }\n    if (life.time < life.duration) {\n        return;\n    }\n    life.time = noTime;\n    if (particle.life.count > noLife) {\n        particle.life.count--;\n    }\n    if (particle.life.count === noLife) {\n        particle.destroy();\n        return;\n    }\n    const widthRange = setRangeValue(minCanvasSize, canvasSize.width), heightRange = setRangeValue(minCanvasSize, canvasSize.width);\n    particle.position.x = randomInRange(widthRange);\n    particle.position.y = randomInRange(heightRange);\n    particle.spawning = true;\n    life.delayTime = noTime;\n    life.time = noTime;\n    particle.reset();\n    const lifeOptions = particle.options.life;\n    if (lifeOptions) {\n        life.delay = getRangeValue(lifeOptions.delay.value) * millisecondsToSeconds;\n        life.duration = getRangeValue(lifeOptions.duration.value) * millisecondsToSeconds;\n    }\n}\n"], "mappings": "AAAA,SAASA,aAAa,EAAEC,qBAAqB,EAAEC,aAAa,EAAEC,aAAa,QAAS,qBAAqB;AACzG,MAAMC,MAAM,GAAG,CAAC;EAAEC,aAAa,GAAG,CAAC,CAAC;EAAEC,MAAM,GAAG,CAAC;EAAEC,aAAa,GAAG,CAAC;AACnE,OAAO,SAASC,UAAUA,CAACC,QAAQ,EAAEC,KAAK,EAAEC,UAAU,EAAE;EACpD,IAAI,CAACF,QAAQ,CAACG,IAAI,EAAE;IAChB;EACJ;EACA,MAAMA,IAAI,GAAGH,QAAQ,CAACG,IAAI;EAC1B,IAAIC,WAAW,GAAG,KAAK;EACvB,IAAIJ,QAAQ,CAACK,QAAQ,EAAE;IACnBF,IAAI,CAACG,SAAS,IAAIL,KAAK,CAACM,KAAK;IAC7B,IAAIJ,IAAI,CAACG,SAAS,IAAIN,QAAQ,CAACG,IAAI,CAACK,KAAK,EAAE;MACvCJ,WAAW,GAAG,IAAI;MAClBJ,QAAQ,CAACK,QAAQ,GAAG,KAAK;MACzBF,IAAI,CAACG,SAAS,GAAGX,MAAM;MACvBQ,IAAI,CAACM,IAAI,GAAGd,MAAM;IACtB,CAAC,MACI;MACD;IACJ;EACJ;EACA,IAAIQ,IAAI,CAACO,QAAQ,KAAKd,aAAa,EAAE;IACjC;EACJ;EACA,IAAII,QAAQ,CAACK,QAAQ,EAAE;IACnB;EACJ;EACA,IAAID,WAAW,EAAE;IACbD,IAAI,CAACM,IAAI,GAAGd,MAAM;EACtB,CAAC,MACI;IACDQ,IAAI,CAACM,IAAI,IAAIR,KAAK,CAACM,KAAK;EAC5B;EACA,IAAIJ,IAAI,CAACM,IAAI,GAAGN,IAAI,CAACO,QAAQ,EAAE;IAC3B;EACJ;EACAP,IAAI,CAACM,IAAI,GAAGd,MAAM;EAClB,IAAIK,QAAQ,CAACG,IAAI,CAACQ,KAAK,GAAGd,MAAM,EAAE;IAC9BG,QAAQ,CAACG,IAAI,CAACQ,KAAK,EAAE;EACzB;EACA,IAAIX,QAAQ,CAACG,IAAI,CAACQ,KAAK,KAAKd,MAAM,EAAE;IAChCG,QAAQ,CAACY,OAAO,CAAC,CAAC;IAClB;EACJ;EACA,MAAMC,UAAU,GAAGnB,aAAa,CAACI,aAAa,EAAEI,UAAU,CAACY,KAAK,CAAC;IAAEC,WAAW,GAAGrB,aAAa,CAACI,aAAa,EAAEI,UAAU,CAACY,KAAK,CAAC;EAC/Hd,QAAQ,CAACgB,QAAQ,CAACC,CAAC,GAAGxB,aAAa,CAACoB,UAAU,CAAC;EAC/Cb,QAAQ,CAACgB,QAAQ,CAACE,CAAC,GAAGzB,aAAa,CAACsB,WAAW,CAAC;EAChDf,QAAQ,CAACK,QAAQ,GAAG,IAAI;EACxBF,IAAI,CAACG,SAAS,GAAGX,MAAM;EACvBQ,IAAI,CAACM,IAAI,GAAGd,MAAM;EAClBK,QAAQ,CAACmB,KAAK,CAAC,CAAC;EAChB,MAAMC,WAAW,GAAGpB,QAAQ,CAACqB,OAAO,CAAClB,IAAI;EACzC,IAAIiB,WAAW,EAAE;IACbjB,IAAI,CAACK,KAAK,GAAGjB,aAAa,CAAC6B,WAAW,CAACZ,KAAK,CAACD,KAAK,CAAC,GAAGf,qBAAqB;IAC3EW,IAAI,CAACO,QAAQ,GAAGnB,aAAa,CAAC6B,WAAW,CAACV,QAAQ,CAACH,KAAK,CAAC,GAAGf,qBAAqB;EACrF;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
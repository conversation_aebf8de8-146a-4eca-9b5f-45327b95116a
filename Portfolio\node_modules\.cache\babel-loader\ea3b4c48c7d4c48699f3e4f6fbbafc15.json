{"ast": null, "code": "import { OptionsColor } from \"../../OptionsColor\";\nexport class LinksShadow {\n  constructor() {\n    this.blur = 5;\n    this.color = new OptionsColor();\n    this.enable = false;\n    this.color.value = \"#00ff00\";\n  }\n\n  load(data) {\n    if (data === undefined) {\n      return;\n    }\n\n    if (data.blur !== undefined) {\n      this.blur = data.blur;\n    }\n\n    this.color = OptionsColor.create(this.color, data.color);\n\n    if (data.enable !== undefined) {\n      this.enable = data.enable;\n    }\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Options/Classes/Particles/Links/LinksShadow.js"], "names": ["OptionsColor", "LinksShadow", "constructor", "blur", "color", "enable", "value", "load", "data", "undefined", "create"], "mappings": "AAAA,SAASA,YAAT,QAA6B,oBAA7B;AACA,OAAO,MAAMC,WAAN,CAAkB;AACrBC,EAAAA,WAAW,GAAG;AACV,SAAKC,IAAL,GAAY,CAAZ;AACA,SAAKC,KAAL,GAAa,IAAIJ,YAAJ,EAAb;AACA,SAAKK,MAAL,GAAc,KAAd;AACA,SAAKD,KAAL,CAAWE,KAAX,GAAmB,SAAnB;AACH;;AACDC,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,QAAIA,IAAI,KAAKC,SAAb,EAAwB;AACpB;AACH;;AACD,QAAID,IAAI,CAACL,IAAL,KAAcM,SAAlB,EAA6B;AACzB,WAAKN,IAAL,GAAYK,IAAI,CAACL,IAAjB;AACH;;AACD,SAAKC,KAAL,GAAaJ,YAAY,CAACU,MAAb,CAAoB,KAAKN,KAAzB,EAAgCI,IAAI,CAACJ,KAArC,CAAb;;AACA,QAAII,IAAI,CAACH,MAAL,KAAgBI,SAApB,EAA+B;AAC3B,WAAKJ,MAAL,GAAcG,IAAI,CAACH,MAAnB;AACH;AACJ;;AAlBoB", "sourcesContent": ["import { OptionsColor } from \"../../OptionsColor\";\nexport class LinksShadow {\n    constructor() {\n        this.blur = 5;\n        this.color = new OptionsColor();\n        this.enable = false;\n        this.color.value = \"#00ff00\";\n    }\n    load(data) {\n        if (data === undefined) {\n            return;\n        }\n        if (data.blur !== undefined) {\n            this.blur = data.blur;\n        }\n        this.color = OptionsColor.create(this.color, data.color);\n        if (data.enable !== undefined) {\n            this.enable = data.enable;\n        }\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
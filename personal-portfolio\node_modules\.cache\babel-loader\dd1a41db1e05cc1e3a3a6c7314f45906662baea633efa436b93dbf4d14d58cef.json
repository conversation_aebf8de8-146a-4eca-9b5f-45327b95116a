{"ast": null, "code": "import { CircleDrawer } from \"./CircleDrawer.js\";\nexport async function loadCircleShape(engine, refresh = true) {\n  await engine.addShape(new CircleDrawer(), refresh);\n}", "map": {"version": 3, "names": ["CircleDrawer", "loadCircleShape", "engine", "refresh", "addShape"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/shape-circle/browser/index.js"], "sourcesContent": ["import { CircleDrawer } from \"./CircleDrawer.js\";\nexport async function loadCircleShape(engine, refresh = true) {\n    await engine.addShape(new CircleDrawer(), refresh);\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,mBAAmB;AAChD,OAAO,eAAeC,eAAeA,CAACC,MAAM,EAAEC,OAAO,GAAG,IAAI,EAAE;EAC1D,MAAMD,MAAM,CAACE,QAAQ,CAAC,IAAIJ,YAAY,CAAC,CAAC,EAAEG,OAAO,CAAC;AACtD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
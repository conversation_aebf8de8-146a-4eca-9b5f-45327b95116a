{"ast": null, "code": "import { getRandom, getRangeValue, initParticleNumericAnimationValue, percentDenominator, updateAnimation } from \"@tsparticles/engine\";\nexport class OpacityUpdater {\n  constructor(container) {\n    this.container = container;\n  }\n  init(particle) {\n    const opacityOptions = particle.options.opacity,\n      pxRatio = 1;\n    particle.opacity = initParticleNumericAnimationValue(opacityOptions, pxRatio);\n    const opacityAnimation = opacityOptions.animation;\n    if (opacityAnimation.enable) {\n      particle.opacity.velocity = getRangeValue(opacityAnimation.speed) / percentDenominator * this.container.retina.reduceFactor;\n      if (!opacityAnimation.sync) {\n        particle.opacity.velocity *= getRandom();\n      }\n    }\n  }\n  isEnabled(particle) {\n    const none = 0;\n    return !particle.destroyed && !particle.spawning && !!particle.opacity && particle.opacity.enable && ((particle.opacity.maxLoops ?? none) <= none || (particle.opacity.maxLoops ?? none) > none && (particle.opacity.loops ?? none) < (particle.opacity.maxLoops ?? none));\n  }\n  reset(particle) {\n    if (particle.opacity) {\n      particle.opacity.time = 0;\n      particle.opacity.loops = 0;\n    }\n  }\n  update(particle, delta) {\n    if (!this.isEnabled(particle) || !particle.opacity) {\n      return;\n    }\n    updateAnimation(particle, particle.opacity, true, particle.options.opacity.animation.destroy, delta);\n  }\n}", "map": {"version": 3, "names": ["getRandom", "getRangeValue", "initParticleNumericAnimationValue", "percentDenominator", "updateAnimation", "OpacityUpdater", "constructor", "container", "init", "particle", "opacityOptions", "options", "opacity", "pxRatio", "opacityAnimation", "animation", "enable", "velocity", "speed", "retina", "reduceFactor", "sync", "isEnabled", "none", "destroyed", "spawning", "max<PERSON><PERSON>s", "loops", "reset", "time", "update", "delta", "destroy"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/updater-opacity/browser/OpacityUpdater.js"], "sourcesContent": ["import { getRandom, getRangeValue, initParticleNumericAnimationValue, percentDenominator, updateAnimation, } from \"@tsparticles/engine\";\nexport class OpacityUpdater {\n    constructor(container) {\n        this.container = container;\n    }\n    init(particle) {\n        const opacityOptions = particle.options.opacity, pxRatio = 1;\n        particle.opacity = initParticleNumericAnimationValue(opacityOptions, pxRatio);\n        const opacityAnimation = opacityOptions.animation;\n        if (opacityAnimation.enable) {\n            particle.opacity.velocity =\n                (getRangeValue(opacityAnimation.speed) / percentDenominator) * this.container.retina.reduceFactor;\n            if (!opacityAnimation.sync) {\n                particle.opacity.velocity *= getRandom();\n            }\n        }\n    }\n    isEnabled(particle) {\n        const none = 0;\n        return (!particle.destroyed &&\n            !particle.spawning &&\n            !!particle.opacity &&\n            particle.opacity.enable &&\n            ((particle.opacity.maxLoops ?? none) <= none ||\n                ((particle.opacity.maxLoops ?? none) > none &&\n                    (particle.opacity.loops ?? none) < (particle.opacity.maxLoops ?? none))));\n    }\n    reset(particle) {\n        if (particle.opacity) {\n            particle.opacity.time = 0;\n            particle.opacity.loops = 0;\n        }\n    }\n    update(particle, delta) {\n        if (!this.isEnabled(particle) || !particle.opacity) {\n            return;\n        }\n        updateAnimation(particle, particle.opacity, true, particle.options.opacity.animation.destroy, delta);\n    }\n}\n"], "mappings": "AAAA,SAASA,SAAS,EAAEC,aAAa,EAAEC,iCAAiC,EAAEC,kBAAkB,EAAEC,eAAe,QAAS,qBAAqB;AACvI,OAAO,MAAMC,cAAc,CAAC;EACxBC,WAAWA,CAACC,SAAS,EAAE;IACnB,IAAI,CAACA,SAAS,GAAGA,SAAS;EAC9B;EACAC,IAAIA,CAACC,QAAQ,EAAE;IACX,MAAMC,cAAc,GAAGD,QAAQ,CAACE,OAAO,CAACC,OAAO;MAAEC,OAAO,GAAG,CAAC;IAC5DJ,QAAQ,CAACG,OAAO,GAAGV,iCAAiC,CAACQ,cAAc,EAAEG,OAAO,CAAC;IAC7E,MAAMC,gBAAgB,GAAGJ,cAAc,CAACK,SAAS;IACjD,IAAID,gBAAgB,CAACE,MAAM,EAAE;MACzBP,QAAQ,CAACG,OAAO,CAACK,QAAQ,GACpBhB,aAAa,CAACa,gBAAgB,CAACI,KAAK,CAAC,GAAGf,kBAAkB,GAAI,IAAI,CAACI,SAAS,CAACY,MAAM,CAACC,YAAY;MACrG,IAAI,CAACN,gBAAgB,CAACO,IAAI,EAAE;QACxBZ,QAAQ,CAACG,OAAO,CAACK,QAAQ,IAAIjB,SAAS,CAAC,CAAC;MAC5C;IACJ;EACJ;EACAsB,SAASA,CAACb,QAAQ,EAAE;IAChB,MAAMc,IAAI,GAAG,CAAC;IACd,OAAQ,CAACd,QAAQ,CAACe,SAAS,IACvB,CAACf,QAAQ,CAACgB,QAAQ,IAClB,CAAC,CAAChB,QAAQ,CAACG,OAAO,IAClBH,QAAQ,CAACG,OAAO,CAACI,MAAM,KACtB,CAACP,QAAQ,CAACG,OAAO,CAACc,QAAQ,IAAIH,IAAI,KAAKA,IAAI,IACvC,CAACd,QAAQ,CAACG,OAAO,CAACc,QAAQ,IAAIH,IAAI,IAAIA,IAAI,IACvC,CAACd,QAAQ,CAACG,OAAO,CAACe,KAAK,IAAIJ,IAAI,KAAKd,QAAQ,CAACG,OAAO,CAACc,QAAQ,IAAIH,IAAI,CAAE,CAAC;EACxF;EACAK,KAAKA,CAACnB,QAAQ,EAAE;IACZ,IAAIA,QAAQ,CAACG,OAAO,EAAE;MAClBH,QAAQ,CAACG,OAAO,CAACiB,IAAI,GAAG,CAAC;MACzBpB,QAAQ,CAACG,OAAO,CAACe,KAAK,GAAG,CAAC;IAC9B;EACJ;EACAG,MAAMA,CAACrB,QAAQ,EAAEsB,KAAK,EAAE;IACpB,IAAI,CAAC,IAAI,CAACT,SAAS,CAACb,QAAQ,CAAC,IAAI,CAACA,QAAQ,CAACG,OAAO,EAAE;MAChD;IACJ;IACAR,eAAe,CAACK,QAAQ,EAAEA,QAAQ,CAACG,OAAO,EAAE,IAAI,EAAEH,QAAQ,CAACE,OAAO,CAACC,OAAO,CAACG,SAAS,CAACiB,OAAO,EAAED,KAAK,CAAC;EACxG;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
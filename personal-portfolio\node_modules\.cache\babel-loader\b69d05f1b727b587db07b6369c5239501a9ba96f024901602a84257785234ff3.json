{"ast": null, "code": "import { setRangeValue } from \"@tsparticles/engine\";\nexport class EmitterRate {\n  constructor() {\n    this.quantity = 1;\n    this.delay = 0.1;\n  }\n  load(data) {\n    if (data === undefined) {\n      return;\n    }\n    if (data.quantity !== undefined) {\n      this.quantity = setRangeValue(data.quantity);\n    }\n    if (data.delay !== undefined) {\n      this.delay = setRangeValue(data.delay);\n    }\n  }\n}", "map": {"version": 3, "names": ["setRangeValue", "EmitterRate", "constructor", "quantity", "delay", "load", "data", "undefined"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/plugin-emitters/browser/Options/Classes/EmitterRate.js"], "sourcesContent": ["import { setRangeValue } from \"@tsparticles/engine\";\nexport class EmitterRate {\n    constructor() {\n        this.quantity = 1;\n        this.delay = 0.1;\n    }\n    load(data) {\n        if (data === undefined) {\n            return;\n        }\n        if (data.quantity !== undefined) {\n            this.quantity = setRangeValue(data.quantity);\n        }\n        if (data.delay !== undefined) {\n            this.delay = setRangeValue(data.delay);\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,qBAAqB;AACnD,OAAO,MAAMC,WAAW,CAAC;EACrBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,QAAQ,GAAG,CAAC;IACjB,IAAI,CAACC,KAAK,GAAG,GAAG;EACpB;EACAC,IAAIA,CAACC,IAAI,EAAE;IACP,IAAIA,IAAI,KAAKC,SAAS,EAAE;MACpB;IACJ;IACA,IAAID,IAAI,CAACH,QAAQ,KAAKI,SAAS,EAAE;MAC7B,IAAI,CAACJ,QAAQ,GAAGH,aAAa,CAACM,IAAI,CAACH,QAAQ,CAAC;IAChD;IACA,IAAIG,IAAI,CAACF,KAAK,KAAKG,SAAS,EAAE;MAC1B,IAAI,CAACH,KAAK,GAAGJ,aAAa,CAACM,IAAI,CAACF,KAAK,CAAC;IAC1C;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
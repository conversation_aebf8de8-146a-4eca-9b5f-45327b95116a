{"ast": null, "code": "import { init } from \"./init.js\";\nimport { isSsr } from \"./Utils/Utils.js\";\nconst tsParticles = init();\nif (!isSsr()) {\n  window.tsParticles = tsParticles;\n}\nexport * from \"./exports.js\";\nexport * from \"./export-types.js\";\nexport { tsParticles };", "map": {"version": 3, "names": ["init", "isSsr", "tsParticles", "window"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/engine/browser/index.js"], "sourcesContent": ["import { init } from \"./init.js\";\nimport { isSsr } from \"./Utils/Utils.js\";\nconst tsParticles = init();\nif (!isSsr()) {\n    window.tsParticles = tsParticles;\n}\nexport * from \"./exports.js\";\nexport * from \"./export-types.js\";\nexport { tsParticles };\n"], "mappings": "AAAA,SAASA,IAAI,QAAQ,WAAW;AAChC,SAASC,KAAK,QAAQ,kBAAkB;AACxC,MAAMC,WAAW,GAAGF,IAAI,CAAC,CAAC;AAC1B,IAAI,CAACC,KAAK,CAAC,CAAC,EAAE;EACVE,MAAM,CAACD,WAAW,GAAGA,WAAW;AACpC;AACA,cAAc,cAAc;AAC5B,cAAc,mBAAmB;AACjC,SAASA,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
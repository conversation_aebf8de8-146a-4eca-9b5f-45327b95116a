(function (factory) {
    if (typeof module === "object" && typeof module.exports === "object") {
        var v = factory(require, exports);
        if (v !== undefined) module.exports = v;
    }
    else if (typeof define === "function" && define.amd) {
        define(["require", "exports", "./Utils", "./ImageDrawer", "./ImagePreloader", "tsparticles-engine"], factory);
    }
})(function (require, exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.loadImageShape = void 0;
    const Utils_1 = require("./Utils");
    const ImageDrawer_1 = require("./ImageDrawer");
    const ImagePreloader_1 = require("./ImagePreloader");
    const tsparticles_engine_1 = require("tsparticles-engine");
    function addLoadImageToEngine(engine) {
        if (engine.loadImage) {
            return;
        }
        engine.loadImage = async (data) => {
            if (!data.name && !data.src) {
                throw new Error(`${tsparticles_engine_1.errorPrefix} no image source provided`);
            }
            if (!engine.images) {
                engine.images = [];
            }
            if (engine.images.find((t) => t.name === data.name || t.source === data.src)) {
                return;
            }
            try {
                const image = {
                    gif: data.gif ?? false,
                    name: data.name ?? data.src,
                    source: data.src,
                    type: data.src.substring(data.src.length - 3),
                    error: false,
                    loading: true,
                    replaceColor: data.replaceColor,
                    ratio: data.width && data.height ? data.width / data.height : undefined,
                };
                engine.images.push(image);
                const imageFunc = data.gif ? Utils_1.loadGifImage : data.replaceColor ? Utils_1.downloadSvgImage : Utils_1.loadImage;
                await imageFunc(image);
            }
            catch {
                throw new Error(`${tsparticles_engine_1.errorPrefix} ${data.name ?? data.src} not found`);
            }
        };
    }
    async function loadImageShape(engine, refresh = true) {
        addLoadImageToEngine(engine);
        const preloader = new ImagePreloader_1.ImagePreloaderPlugin(engine);
        await engine.addPlugin(preloader, refresh);
        await engine.addShape(["image", "images"], new ImageDrawer_1.ImageDrawer(engine), refresh);
    }
    exports.loadImageShape = loadImageShape;
});

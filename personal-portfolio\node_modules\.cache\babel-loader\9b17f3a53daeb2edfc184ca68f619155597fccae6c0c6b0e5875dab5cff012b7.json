{"ast": null, "code": "import { ExternalInteractorBase, getDistance, isInArray } from \"@tsparticles/engine\";\nimport { Slow } from \"./Options/Classes/Slow.js\";\nconst slowMode = \"slow\",\n  minRadius = 0;\nexport class Slower extends ExternalInteractorBase {\n  constructor(container) {\n    super(container);\n  }\n  clear(particle, delta, force) {\n    if (particle.slow.inRange && !force) {\n      return;\n    }\n    particle.slow.factor = 1;\n  }\n  init() {\n    const container = this.container,\n      slow = container.actualOptions.interactivity.modes.slow;\n    if (!slow) {\n      return;\n    }\n    container.retina.slowModeRadius = slow.radius * container.retina.pixelRatio;\n  }\n  interact() {}\n  isEnabled(particle) {\n    const container = this.container,\n      mouse = container.interactivity.mouse,\n      events = (particle?.interactivity ?? container.actualOptions.interactivity).events;\n    return events.onHover.enable && !!mouse.position && isInArray(slowMode, events.onHover.mode);\n  }\n  loadModeOptions(options, ...sources) {\n    if (!options.slow) {\n      options.slow = new Slow();\n    }\n    for (const source of sources) {\n      options.slow.load(source?.slow);\n    }\n  }\n  reset(particle) {\n    particle.slow.inRange = false;\n    const container = this.container,\n      options = container.actualOptions,\n      mousePos = container.interactivity.mouse.position,\n      radius = container.retina.slowModeRadius,\n      slowOptions = options.interactivity.modes.slow;\n    if (!slowOptions || !radius || radius < minRadius || !mousePos) {\n      return;\n    }\n    const particlePos = particle.getPosition(),\n      dist = getDistance(mousePos, particlePos),\n      proximityFactor = dist / radius,\n      slowFactor = slowOptions.factor,\n      {\n        slow\n      } = particle;\n    if (dist > radius) {\n      return;\n    }\n    slow.inRange = true;\n    slow.factor = proximityFactor / slowFactor;\n  }\n}", "map": {"version": 3, "names": ["ExternalInteractorBase", "getDistance", "isInArray", "Slow", "slowMode", "minRadius", "Slower", "constructor", "container", "clear", "particle", "delta", "force", "slow", "inRange", "factor", "init", "actualOptions", "interactivity", "modes", "retina", "slowModeRadius", "radius", "pixelRatio", "interact", "isEnabled", "mouse", "events", "onHover", "enable", "position", "mode", "loadModeOptions", "options", "sources", "source", "load", "reset", "mousePos", "slowOptions", "particlePos", "getPosition", "dist", "proximityFactor", "slowFactor"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/interaction-external-slow/browser/Slower.js"], "sourcesContent": ["import { ExternalInteractorBase, getDistance, isInArray, } from \"@tsparticles/engine\";\nimport { Slow } from \"./Options/Classes/Slow.js\";\nconst slowMode = \"slow\", minRadius = 0;\nexport class Slower extends ExternalInteractorBase {\n    constructor(container) {\n        super(container);\n    }\n    clear(particle, delta, force) {\n        if (particle.slow.inRange && !force) {\n            return;\n        }\n        particle.slow.factor = 1;\n    }\n    init() {\n        const container = this.container, slow = container.actualOptions.interactivity.modes.slow;\n        if (!slow) {\n            return;\n        }\n        container.retina.slowModeRadius = slow.radius * container.retina.pixelRatio;\n    }\n    interact() {\n    }\n    isEnabled(particle) {\n        const container = this.container, mouse = container.interactivity.mouse, events = (particle?.interactivity ?? container.actualOptions.interactivity).events;\n        return events.onHover.enable && !!mouse.position && isInArray(slowMode, events.onHover.mode);\n    }\n    loadModeOptions(options, ...sources) {\n        if (!options.slow) {\n            options.slow = new Slow();\n        }\n        for (const source of sources) {\n            options.slow.load(source?.slow);\n        }\n    }\n    reset(particle) {\n        particle.slow.inRange = false;\n        const container = this.container, options = container.actualOptions, mousePos = container.interactivity.mouse.position, radius = container.retina.slowModeRadius, slowOptions = options.interactivity.modes.slow;\n        if (!slowOptions || !radius || radius < minRadius || !mousePos) {\n            return;\n        }\n        const particlePos = particle.getPosition(), dist = getDistance(mousePos, particlePos), proximityFactor = dist / radius, slowFactor = slowOptions.factor, { slow } = particle;\n        if (dist > radius) {\n            return;\n        }\n        slow.inRange = true;\n        slow.factor = proximityFactor / slowFactor;\n    }\n}\n"], "mappings": "AAAA,SAASA,sBAAsB,EAAEC,WAAW,EAAEC,SAAS,QAAS,qBAAqB;AACrF,SAASC,IAAI,QAAQ,2BAA2B;AAChD,MAAMC,QAAQ,GAAG,MAAM;EAAEC,SAAS,GAAG,CAAC;AACtC,OAAO,MAAMC,MAAM,SAASN,sBAAsB,CAAC;EAC/CO,WAAWA,CAACC,SAAS,EAAE;IACnB,KAAK,CAACA,SAAS,CAAC;EACpB;EACAC,KAAKA,CAACC,QAAQ,EAAEC,KAAK,EAAEC,KAAK,EAAE;IAC1B,IAAIF,QAAQ,CAACG,IAAI,CAACC,OAAO,IAAI,CAACF,KAAK,EAAE;MACjC;IACJ;IACAF,QAAQ,CAACG,IAAI,CAACE,MAAM,GAAG,CAAC;EAC5B;EACAC,IAAIA,CAAA,EAAG;IACH,MAAMR,SAAS,GAAG,IAAI,CAACA,SAAS;MAAEK,IAAI,GAAGL,SAAS,CAACS,aAAa,CAACC,aAAa,CAACC,KAAK,CAACN,IAAI;IACzF,IAAI,CAACA,IAAI,EAAE;MACP;IACJ;IACAL,SAAS,CAACY,MAAM,CAACC,cAAc,GAAGR,IAAI,CAACS,MAAM,GAAGd,SAAS,CAACY,MAAM,CAACG,UAAU;EAC/E;EACAC,QAAQA,CAAA,EAAG,CACX;EACAC,SAASA,CAACf,QAAQ,EAAE;IAChB,MAAMF,SAAS,GAAG,IAAI,CAACA,SAAS;MAAEkB,KAAK,GAAGlB,SAAS,CAACU,aAAa,CAACQ,KAAK;MAAEC,MAAM,GAAG,CAACjB,QAAQ,EAAEQ,aAAa,IAAIV,SAAS,CAACS,aAAa,CAACC,aAAa,EAAES,MAAM;IAC3J,OAAOA,MAAM,CAACC,OAAO,CAACC,MAAM,IAAI,CAAC,CAACH,KAAK,CAACI,QAAQ,IAAI5B,SAAS,CAACE,QAAQ,EAAEuB,MAAM,CAACC,OAAO,CAACG,IAAI,CAAC;EAChG;EACAC,eAAeA,CAACC,OAAO,EAAE,GAAGC,OAAO,EAAE;IACjC,IAAI,CAACD,OAAO,CAACpB,IAAI,EAAE;MACfoB,OAAO,CAACpB,IAAI,GAAG,IAAIV,IAAI,CAAC,CAAC;IAC7B;IACA,KAAK,MAAMgC,MAAM,IAAID,OAAO,EAAE;MAC1BD,OAAO,CAACpB,IAAI,CAACuB,IAAI,CAACD,MAAM,EAAEtB,IAAI,CAAC;IACnC;EACJ;EACAwB,KAAKA,CAAC3B,QAAQ,EAAE;IACZA,QAAQ,CAACG,IAAI,CAACC,OAAO,GAAG,KAAK;IAC7B,MAAMN,SAAS,GAAG,IAAI,CAACA,SAAS;MAAEyB,OAAO,GAAGzB,SAAS,CAACS,aAAa;MAAEqB,QAAQ,GAAG9B,SAAS,CAACU,aAAa,CAACQ,KAAK,CAACI,QAAQ;MAAER,MAAM,GAAGd,SAAS,CAACY,MAAM,CAACC,cAAc;MAAEkB,WAAW,GAAGN,OAAO,CAACf,aAAa,CAACC,KAAK,CAACN,IAAI;IAChN,IAAI,CAAC0B,WAAW,IAAI,CAACjB,MAAM,IAAIA,MAAM,GAAGjB,SAAS,IAAI,CAACiC,QAAQ,EAAE;MAC5D;IACJ;IACA,MAAME,WAAW,GAAG9B,QAAQ,CAAC+B,WAAW,CAAC,CAAC;MAAEC,IAAI,GAAGzC,WAAW,CAACqC,QAAQ,EAAEE,WAAW,CAAC;MAAEG,eAAe,GAAGD,IAAI,GAAGpB,MAAM;MAAEsB,UAAU,GAAGL,WAAW,CAACxB,MAAM;MAAE;QAAEF;MAAK,CAAC,GAAGH,QAAQ;IAC5K,IAAIgC,IAAI,GAAGpB,MAAM,EAAE;MACf;IACJ;IACAT,IAAI,CAACC,OAAO,GAAG,IAAI;IACnBD,IAAI,CAACE,MAAM,GAAG4B,eAAe,GAAGC,UAAU;EAC9C;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
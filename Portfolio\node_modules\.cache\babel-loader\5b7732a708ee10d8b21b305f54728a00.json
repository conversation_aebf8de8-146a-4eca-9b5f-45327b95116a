{"ast": null, "code": "import getComputedStyle from \"./getComputedStyle.js\";\nexport default function isScrollParent(element) {\n  // Firefox wants us to check `-x` and `-y` variations as well\n  var _getComputedStyle = getComputedStyle(element),\n      overflow = _getComputedStyle.overflow,\n      overflowX = _getComputedStyle.overflowX,\n      overflowY = _getComputedStyle.overflowY;\n\n  return /auto|scroll|overlay|hidden/.test(overflow + overflowY + overflowX);\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/@popperjs/core/lib/dom-utils/isScrollParent.js"], "names": ["getComputedStyle", "isScrollParent", "element", "_getComputedStyle", "overflow", "overflowX", "overflowY", "test"], "mappings": "AAAA,OAAOA,gBAAP,MAA6B,uBAA7B;AACA,eAAe,SAASC,cAAT,CAAwBC,OAAxB,EAAiC;AAC9C;AACA,MAAIC,iBAAiB,GAAGH,gBAAgB,CAACE,OAAD,CAAxC;AAAA,MACIE,QAAQ,GAAGD,iBAAiB,CAACC,QADjC;AAAA,MAEIC,SAAS,GAAGF,iBAAiB,CAACE,SAFlC;AAAA,MAGIC,SAAS,GAAGH,iBAAiB,CAACG,SAHlC;;AAKA,SAAO,6BAA6BC,IAA7B,CAAkCH,QAAQ,GAAGE,SAAX,GAAuBD,SAAzD,CAAP;AACD", "sourcesContent": ["import getComputedStyle from \"./getComputedStyle.js\";\nexport default function isScrollParent(element) {\n  // Firefox wants us to check `-x` and `-y` variations as well\n  var _getComputedStyle = getComputedStyle(element),\n      overflow = _getComputedStyle.overflow,\n      overflowX = _getComputedStyle.overflowX,\n      overflowY = _getComputedStyle.overflowY;\n\n  return /auto|scroll|overlay|hidden/.test(overflow + overflowY + overflowX);\n}"]}, "metadata": {}, "sourceType": "module"}
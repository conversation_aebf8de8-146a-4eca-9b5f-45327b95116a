{"ast": null, "code": "import { ValueWithRandom } from \"@tsparticles/engine\";\nexport class SplitFactor extends ValueWithRandom {\n  constructor() {\n    super();\n    this.value = 3;\n  }\n}", "map": {"version": 3, "names": ["ValueWithRandom", "SplitFactor", "constructor", "value"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/updater-destroy/browser/Options/Classes/SplitFactor.js"], "sourcesContent": ["import { ValueWithRandom } from \"@tsparticles/engine\";\nexport class SplitFactor extends ValueWithRandom {\n    constructor() {\n        super();\n        this.value = 3;\n    }\n}\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,qBAAqB;AACrD,OAAO,MAAMC,WAAW,SAASD,eAAe,CAAC;EAC7CE,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,KAAK,GAAG,CAAC;EAClB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
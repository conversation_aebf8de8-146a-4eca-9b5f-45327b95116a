{"ast": null, "code": "export var ResponsiveMode;\n(function (ResponsiveMode) {\n  ResponsiveMode[\"screen\"] = \"screen\";\n  ResponsiveMode[\"canvas\"] = \"canvas\";\n})(ResponsiveMode || (ResponsiveMode = {}));", "map": {"version": 3, "names": ["ResponsiveMode"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/engine/browser/Enums/Modes/ResponsiveMode.js"], "sourcesContent": ["export var ResponsiveMode;\n(function (ResponsiveMode) {\n    ResponsiveMode[\"screen\"] = \"screen\";\n    ResponsiveMode[\"canvas\"] = \"canvas\";\n})(ResponsiveMode || (ResponsiveMode = {}));\n"], "mappings": "AAAA,OAAO,IAAIA,cAAc;AACzB,CAAC,UAAUA,cAAc,EAAE;EACvBA,cAAc,CAAC,QAAQ,CAAC,GAAG,QAAQ;EACnCA,cAAc,CAAC,QAAQ,CAAC,GAAG,QAAQ;AACvC,CAAC,EAAEA,cAAc,KAAKA,cAAc,GAAG,CAAC,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
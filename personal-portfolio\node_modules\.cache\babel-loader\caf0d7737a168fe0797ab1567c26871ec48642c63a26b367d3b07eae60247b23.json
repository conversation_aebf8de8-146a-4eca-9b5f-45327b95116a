{"ast": null, "code": "import { AnimationStatus, DestroyType, degToRad, getRandom, getRangeValue, halfRandom, updateAnimation } from \"@tsparticles/engine\";\nimport { Tilt } from \"./Options/Classes/Tilt.js\";\nimport { TiltDirection } from \"./TiltDirection.js\";\nconst identity = 1,\n  double = 2,\n  doublePI = Math.PI * double,\n  maxAngle = 360;\nexport class TiltUpdater {\n  constructor(container) {\n    this.container = container;\n  }\n  getTransformValues(particle) {\n    const tilt = particle.tilt?.enable && particle.tilt;\n    return {\n      b: tilt ? Math.cos(tilt.value) * tilt.cosDirection : undefined,\n      c: tilt ? Math.sin(tilt.value) * tilt.sinDirection : undefined\n    };\n  }\n  init(particle) {\n    const tiltOptions = particle.options.tilt;\n    if (!tiltOptions) {\n      return;\n    }\n    particle.tilt = {\n      enable: tiltOptions.enable,\n      value: degToRad(getRangeValue(tiltOptions.value)),\n      sinDirection: getRandom() >= halfRandom ? identity : -identity,\n      cosDirection: getRandom() >= halfRandom ? identity : -identity,\n      min: 0,\n      max: doublePI\n    };\n    let tiltDirection = tiltOptions.direction;\n    if (tiltDirection === TiltDirection.random) {\n      const index = Math.floor(getRandom() * double),\n        minIndex = 0;\n      tiltDirection = index > minIndex ? TiltDirection.counterClockwise : TiltDirection.clockwise;\n    }\n    switch (tiltDirection) {\n      case TiltDirection.counterClockwise:\n      case \"counterClockwise\":\n        particle.tilt.status = AnimationStatus.decreasing;\n        break;\n      case TiltDirection.clockwise:\n        particle.tilt.status = AnimationStatus.increasing;\n        break;\n    }\n    const tiltAnimation = particle.options.tilt?.animation;\n    if (tiltAnimation?.enable) {\n      particle.tilt.decay = identity - getRangeValue(tiltAnimation.decay);\n      particle.tilt.velocity = getRangeValue(tiltAnimation.speed) / maxAngle * this.container.retina.reduceFactor;\n      if (!tiltAnimation.sync) {\n        particle.tilt.velocity *= getRandom();\n      }\n    }\n  }\n  isEnabled(particle) {\n    const tiltAnimation = particle.options.tilt?.animation;\n    return !particle.destroyed && !particle.spawning && !!tiltAnimation?.enable;\n  }\n  loadOptions(options, ...sources) {\n    if (!options.tilt) {\n      options.tilt = new Tilt();\n    }\n    for (const source of sources) {\n      options.tilt.load(source?.tilt);\n    }\n  }\n  async update(particle, delta) {\n    if (!this.isEnabled(particle) || !particle.tilt) {\n      return;\n    }\n    updateAnimation(particle, particle.tilt, false, DestroyType.none, delta);\n    await Promise.resolve();\n  }\n}", "map": {"version": 3, "names": ["AnimationStatus", "DestroyType", "degToRad", "getRandom", "getRangeValue", "halfRandom", "updateAnimation", "Tilt", "TiltDirection", "identity", "double", "doublePI", "Math", "PI", "maxAngle", "TiltUpdater", "constructor", "container", "getTransformValues", "particle", "tilt", "enable", "b", "cos", "value", "cosDirection", "undefined", "c", "sin", "sinDirection", "init", "tiltOptions", "options", "min", "max", "tiltDirection", "direction", "random", "index", "floor", "minIndex", "counterClockwise", "clockwise", "status", "decreasing", "increasing", "tiltAnimation", "animation", "decay", "velocity", "speed", "retina", "reduceFactor", "sync", "isEnabled", "destroyed", "spawning", "loadOptions", "sources", "source", "load", "update", "delta", "none", "Promise", "resolve"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/updater-tilt/browser/TiltUpdater.js"], "sourcesContent": ["import { AnimationStatus, DestroyType, degToRad, getRandom, getRangeValue, halfRandom, updateAnimation, } from \"@tsparticles/engine\";\nimport { Tilt } from \"./Options/Classes/Tilt.js\";\nimport { TiltDirection } from \"./TiltDirection.js\";\nconst identity = 1, double = 2, doublePI = Math.PI * double, maxAngle = 360;\nexport class TiltUpdater {\n    constructor(container) {\n        this.container = container;\n    }\n    getTransformValues(particle) {\n        const tilt = particle.tilt?.enable && particle.tilt;\n        return {\n            b: tilt ? Math.cos(tilt.value) * tilt.cosDirection : undefined,\n            c: tilt ? Math.sin(tilt.value) * tilt.sinDirection : undefined,\n        };\n    }\n    init(particle) {\n        const tiltOptions = particle.options.tilt;\n        if (!tiltOptions) {\n            return;\n        }\n        particle.tilt = {\n            enable: tiltOptions.enable,\n            value: degToRad(getRangeValue(tiltOptions.value)),\n            sinDirection: getRandom() >= halfRandom ? identity : -identity,\n            cosDirection: getRandom() >= halfRandom ? identity : -identity,\n            min: 0,\n            max: doublePI,\n        };\n        let tiltDirection = tiltOptions.direction;\n        if (tiltDirection === TiltDirection.random) {\n            const index = Math.floor(getRandom() * double), minIndex = 0;\n            tiltDirection = index > minIndex ? TiltDirection.counterClockwise : TiltDirection.clockwise;\n        }\n        switch (tiltDirection) {\n            case TiltDirection.counterClockwise:\n            case \"counterClockwise\":\n                particle.tilt.status = AnimationStatus.decreasing;\n                break;\n            case TiltDirection.clockwise:\n                particle.tilt.status = AnimationStatus.increasing;\n                break;\n        }\n        const tiltAnimation = particle.options.tilt?.animation;\n        if (tiltAnimation?.enable) {\n            particle.tilt.decay = identity - getRangeValue(tiltAnimation.decay);\n            particle.tilt.velocity =\n                (getRangeValue(tiltAnimation.speed) / maxAngle) * this.container.retina.reduceFactor;\n            if (!tiltAnimation.sync) {\n                particle.tilt.velocity *= getRandom();\n            }\n        }\n    }\n    isEnabled(particle) {\n        const tiltAnimation = particle.options.tilt?.animation;\n        return !particle.destroyed && !particle.spawning && !!tiltAnimation?.enable;\n    }\n    loadOptions(options, ...sources) {\n        if (!options.tilt) {\n            options.tilt = new Tilt();\n        }\n        for (const source of sources) {\n            options.tilt.load(source?.tilt);\n        }\n    }\n    async update(particle, delta) {\n        if (!this.isEnabled(particle) || !particle.tilt) {\n            return;\n        }\n        updateAnimation(particle, particle.tilt, false, DestroyType.none, delta);\n        await Promise.resolve();\n    }\n}\n"], "mappings": "AAAA,SAASA,eAAe,EAAEC,WAAW,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,aAAa,EAAEC,UAAU,EAAEC,eAAe,QAAS,qBAAqB;AACpI,SAASC,IAAI,QAAQ,2BAA2B;AAChD,SAASC,aAAa,QAAQ,oBAAoB;AAClD,MAAMC,QAAQ,GAAG,CAAC;EAAEC,MAAM,GAAG,CAAC;EAAEC,QAAQ,GAAGC,IAAI,CAACC,EAAE,GAAGH,MAAM;EAAEI,QAAQ,GAAG,GAAG;AAC3E,OAAO,MAAMC,WAAW,CAAC;EACrBC,WAAWA,CAACC,SAAS,EAAE;IACnB,IAAI,CAACA,SAAS,GAAGA,SAAS;EAC9B;EACAC,kBAAkBA,CAACC,QAAQ,EAAE;IACzB,MAAMC,IAAI,GAAGD,QAAQ,CAACC,IAAI,EAAEC,MAAM,IAAIF,QAAQ,CAACC,IAAI;IACnD,OAAO;MACHE,CAAC,EAAEF,IAAI,GAAGR,IAAI,CAACW,GAAG,CAACH,IAAI,CAACI,KAAK,CAAC,GAAGJ,IAAI,CAACK,YAAY,GAAGC,SAAS;MAC9DC,CAAC,EAAEP,IAAI,GAAGR,IAAI,CAACgB,GAAG,CAACR,IAAI,CAACI,KAAK,CAAC,GAAGJ,IAAI,CAACS,YAAY,GAAGH;IACzD,CAAC;EACL;EACAI,IAAIA,CAACX,QAAQ,EAAE;IACX,MAAMY,WAAW,GAAGZ,QAAQ,CAACa,OAAO,CAACZ,IAAI;IACzC,IAAI,CAACW,WAAW,EAAE;MACd;IACJ;IACAZ,QAAQ,CAACC,IAAI,GAAG;MACZC,MAAM,EAAEU,WAAW,CAACV,MAAM;MAC1BG,KAAK,EAAEtB,QAAQ,CAACE,aAAa,CAAC2B,WAAW,CAACP,KAAK,CAAC,CAAC;MACjDK,YAAY,EAAE1B,SAAS,CAAC,CAAC,IAAIE,UAAU,GAAGI,QAAQ,GAAG,CAACA,QAAQ;MAC9DgB,YAAY,EAAEtB,SAAS,CAAC,CAAC,IAAIE,UAAU,GAAGI,QAAQ,GAAG,CAACA,QAAQ;MAC9DwB,GAAG,EAAE,CAAC;MACNC,GAAG,EAAEvB;IACT,CAAC;IACD,IAAIwB,aAAa,GAAGJ,WAAW,CAACK,SAAS;IACzC,IAAID,aAAa,KAAK3B,aAAa,CAAC6B,MAAM,EAAE;MACxC,MAAMC,KAAK,GAAG1B,IAAI,CAAC2B,KAAK,CAACpC,SAAS,CAAC,CAAC,GAAGO,MAAM,CAAC;QAAE8B,QAAQ,GAAG,CAAC;MAC5DL,aAAa,GAAGG,KAAK,GAAGE,QAAQ,GAAGhC,aAAa,CAACiC,gBAAgB,GAAGjC,aAAa,CAACkC,SAAS;IAC/F;IACA,QAAQP,aAAa;MACjB,KAAK3B,aAAa,CAACiC,gBAAgB;MACnC,KAAK,kBAAkB;QACnBtB,QAAQ,CAACC,IAAI,CAACuB,MAAM,GAAG3C,eAAe,CAAC4C,UAAU;QACjD;MACJ,KAAKpC,aAAa,CAACkC,SAAS;QACxBvB,QAAQ,CAACC,IAAI,CAACuB,MAAM,GAAG3C,eAAe,CAAC6C,UAAU;QACjD;IACR;IACA,MAAMC,aAAa,GAAG3B,QAAQ,CAACa,OAAO,CAACZ,IAAI,EAAE2B,SAAS;IACtD,IAAID,aAAa,EAAEzB,MAAM,EAAE;MACvBF,QAAQ,CAACC,IAAI,CAAC4B,KAAK,GAAGvC,QAAQ,GAAGL,aAAa,CAAC0C,aAAa,CAACE,KAAK,CAAC;MACnE7B,QAAQ,CAACC,IAAI,CAAC6B,QAAQ,GACjB7C,aAAa,CAAC0C,aAAa,CAACI,KAAK,CAAC,GAAGpC,QAAQ,GAAI,IAAI,CAACG,SAAS,CAACkC,MAAM,CAACC,YAAY;MACxF,IAAI,CAACN,aAAa,CAACO,IAAI,EAAE;QACrBlC,QAAQ,CAACC,IAAI,CAAC6B,QAAQ,IAAI9C,SAAS,CAAC,CAAC;MACzC;IACJ;EACJ;EACAmD,SAASA,CAACnC,QAAQ,EAAE;IAChB,MAAM2B,aAAa,GAAG3B,QAAQ,CAACa,OAAO,CAACZ,IAAI,EAAE2B,SAAS;IACtD,OAAO,CAAC5B,QAAQ,CAACoC,SAAS,IAAI,CAACpC,QAAQ,CAACqC,QAAQ,IAAI,CAAC,CAACV,aAAa,EAAEzB,MAAM;EAC/E;EACAoC,WAAWA,CAACzB,OAAO,EAAE,GAAG0B,OAAO,EAAE;IAC7B,IAAI,CAAC1B,OAAO,CAACZ,IAAI,EAAE;MACfY,OAAO,CAACZ,IAAI,GAAG,IAAIb,IAAI,CAAC,CAAC;IAC7B;IACA,KAAK,MAAMoD,MAAM,IAAID,OAAO,EAAE;MAC1B1B,OAAO,CAACZ,IAAI,CAACwC,IAAI,CAACD,MAAM,EAAEvC,IAAI,CAAC;IACnC;EACJ;EACA,MAAMyC,MAAMA,CAAC1C,QAAQ,EAAE2C,KAAK,EAAE;IAC1B,IAAI,CAAC,IAAI,CAACR,SAAS,CAACnC,QAAQ,CAAC,IAAI,CAACA,QAAQ,CAACC,IAAI,EAAE;MAC7C;IACJ;IACAd,eAAe,CAACa,QAAQ,EAAEA,QAAQ,CAACC,IAAI,EAAE,KAAK,EAAEnB,WAAW,CAAC8D,IAAI,EAAED,KAAK,CAAC;IACxE,MAAME,OAAO,CAACC,OAAO,CAAC,CAAC;EAC3B;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
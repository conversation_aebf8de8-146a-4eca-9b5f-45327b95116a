{"ast": null, "code": "export class ByteStream {\n  constructor(bytes) {\n    this.pos = 0;\n    this.data = new Uint8ClampedArray(bytes);\n  }\n  getString(count) {\n    const slice = this.data.slice(this.pos, this.pos + count);\n    this.pos += slice.length;\n    return slice.reduce((acc, curr) => acc + String.fromCharCode(curr), \"\");\n  }\n  nextByte() {\n    return this.data[this.pos++];\n  }\n  nextTwoBytes() {\n    this.pos += 2;\n    return this.data[this.pos - 2] + (this.data[this.pos - 1] << 8);\n  }\n  readSubBlocks() {\n    let blockString = \"\",\n      size = 0;\n    do {\n      size = this.data[this.pos++];\n      for (let count = size; --count >= 0; blockString += String.fromCharCode(this.data[this.pos++])) {}\n    } while (size !== 0);\n    return blockString;\n  }\n  readSubBlocksBin() {\n    let size = 0,\n      len = 0;\n    for (let offset = 0; (size = this.data[this.pos + offset]) !== 0; offset += size + 1) {\n      len += size;\n    }\n    const blockData = new Uint8Array(len);\n    for (let i = 0; (size = this.data[this.pos++]) !== 0;) {\n      for (let count = size; --count >= 0; blockData[i++] = this.data[this.pos++]) {}\n    }\n    return blockData;\n  }\n  skipSubBlocks() {\n    for (; this.data[this.pos] !== 0; this.pos += this.data[this.pos] + 1) {}\n    this.pos++;\n  }\n}", "map": {"version": 3, "names": ["ByteStream", "constructor", "bytes", "pos", "data", "Uint8ClampedArray", "getString", "count", "slice", "length", "reduce", "acc", "curr", "String", "fromCharCode", "nextByte", "nextTwoBytes", "readSubBlocks", "blockString", "size", "readSubBlocksBin", "len", "offset", "blockData", "Uint8Array", "i", "skip<PERSON><PERSON><PERSON><PERSON>s"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/tsparticles-shape-image/esm/GifUtils/ByteStream.js"], "sourcesContent": ["export class ByteStream {\n    constructor(bytes) {\n        this.pos = 0;\n        this.data = new Uint8ClampedArray(bytes);\n    }\n    getString(count) {\n        const slice = this.data.slice(this.pos, this.pos + count);\n        this.pos += slice.length;\n        return slice.reduce((acc, curr) => acc + String.fromCharCode(curr), \"\");\n    }\n    nextByte() {\n        return this.data[this.pos++];\n    }\n    nextTwoBytes() {\n        this.pos += 2;\n        return this.data[this.pos - 2] + (this.data[this.pos - 1] << 8);\n    }\n    readSubBlocks() {\n        let blockString = \"\", size = 0;\n        do {\n            size = this.data[this.pos++];\n            for (let count = size; --count >= 0; blockString += String.fromCharCode(this.data[this.pos++])) {\n            }\n        } while (size !== 0);\n        return blockString;\n    }\n    readSubBlocksBin() {\n        let size = 0, len = 0;\n        for (let offset = 0; (size = this.data[this.pos + offset]) !== 0; offset += size + 1) {\n            len += size;\n        }\n        const blockData = new Uint8Array(len);\n        for (let i = 0; (size = this.data[this.pos++]) !== 0;) {\n            for (let count = size; --count >= 0; blockData[i++] = this.data[this.pos++]) {\n            }\n        }\n        return blockData;\n    }\n    skipSubBlocks() {\n        for (; this.data[this.pos] !== 0; this.pos += this.data[this.pos] + 1) {\n        }\n        this.pos++;\n    }\n}\n"], "mappings": "AAAA,OAAO,MAAMA,UAAU,CAAC;EACpBC,WAAWA,CAACC,KAAK,EAAE;IACf,IAAI,CAACC,GAAG,GAAG,CAAC;IACZ,IAAI,CAACC,IAAI,GAAG,IAAIC,iBAAiB,CAACH,KAAK,CAAC;EAC5C;EACAI,SAASA,CAACC,KAAK,EAAE;IACb,MAAMC,KAAK,GAAG,IAAI,CAACJ,IAAI,CAACI,KAAK,CAAC,IAAI,CAACL,GAAG,EAAE,IAAI,CAACA,GAAG,GAAGI,KAAK,CAAC;IACzD,IAAI,CAACJ,GAAG,IAAIK,KAAK,CAACC,MAAM;IACxB,OAAOD,KAAK,CAACE,MAAM,CAAC,CAACC,GAAG,EAAEC,IAAI,KAAKD,GAAG,GAAGE,MAAM,CAACC,YAAY,CAACF,IAAI,CAAC,EAAE,EAAE,CAAC;EAC3E;EACAG,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACX,IAAI,CAAC,IAAI,CAACD,GAAG,EAAE,CAAC;EAChC;EACAa,YAAYA,CAAA,EAAG;IACX,IAAI,CAACb,GAAG,IAAI,CAAC;IACb,OAAO,IAAI,CAACC,IAAI,CAAC,IAAI,CAACD,GAAG,GAAG,CAAC,CAAC,IAAI,IAAI,CAACC,IAAI,CAAC,IAAI,CAACD,GAAG,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC;EACnE;EACAc,aAAaA,CAAA,EAAG;IACZ,IAAIC,WAAW,GAAG,EAAE;MAAEC,IAAI,GAAG,CAAC;IAC9B,GAAG;MACCA,IAAI,GAAG,IAAI,CAACf,IAAI,CAAC,IAAI,CAACD,GAAG,EAAE,CAAC;MAC5B,KAAK,IAAII,KAAK,GAAGY,IAAI,EAAE,EAAEZ,KAAK,IAAI,CAAC,EAAEW,WAAW,IAAIL,MAAM,CAACC,YAAY,CAAC,IAAI,CAACV,IAAI,CAAC,IAAI,CAACD,GAAG,EAAE,CAAC,CAAC,EAAE,CAChG;IACJ,CAAC,QAAQgB,IAAI,KAAK,CAAC;IACnB,OAAOD,WAAW;EACtB;EACAE,gBAAgBA,CAAA,EAAG;IACf,IAAID,IAAI,GAAG,CAAC;MAAEE,GAAG,GAAG,CAAC;IACrB,KAAK,IAAIC,MAAM,GAAG,CAAC,EAAE,CAACH,IAAI,GAAG,IAAI,CAACf,IAAI,CAAC,IAAI,CAACD,GAAG,GAAGmB,MAAM,CAAC,MAAM,CAAC,EAAEA,MAAM,IAAIH,IAAI,GAAG,CAAC,EAAE;MAClFE,GAAG,IAAIF,IAAI;IACf;IACA,MAAMI,SAAS,GAAG,IAAIC,UAAU,CAACH,GAAG,CAAC;IACrC,KAAK,IAAII,CAAC,GAAG,CAAC,EAAE,CAACN,IAAI,GAAG,IAAI,CAACf,IAAI,CAAC,IAAI,CAACD,GAAG,EAAE,CAAC,MAAM,CAAC,GAAG;MACnD,KAAK,IAAII,KAAK,GAAGY,IAAI,EAAE,EAAEZ,KAAK,IAAI,CAAC,EAAEgB,SAAS,CAACE,CAAC,EAAE,CAAC,GAAG,IAAI,CAACrB,IAAI,CAAC,IAAI,CAACD,GAAG,EAAE,CAAC,EAAE,CAC7E;IACJ;IACA,OAAOoB,SAAS;EACpB;EACAG,aAAaA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACtB,IAAI,CAAC,IAAI,CAACD,GAAG,CAAC,KAAK,CAAC,EAAE,IAAI,CAACA,GAAG,IAAI,IAAI,CAACC,IAAI,CAAC,IAAI,CAACD,GAAG,CAAC,GAAG,CAAC,EAAE,CACvE;IACA,IAAI,CAACA,GAAG,EAAE;EACd;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { BubbleBase } from \"./BubbleBase\";\nimport { BubbleDiv } from \"./BubbleDiv\";\nexport class B<PERSON>ble extends BubbleBase {\n  load(data) {\n    super.load(data);\n\n    if (!(data !== undefined && data.divs !== undefined)) {\n      return;\n    }\n\n    if (data.divs instanceof Array) {\n      this.divs = data.divs.map(s => {\n        const tmp = new BubbleDiv();\n        tmp.load(s);\n        return tmp;\n      });\n    } else {\n      if (this.divs instanceof Array || !this.divs) {\n        this.divs = new BubbleDiv();\n      }\n\n      this.divs.load(data.divs);\n    }\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Options/Classes/Interactivity/Modes/Bubble.js"], "names": ["BubbleBase", "BubbleDiv", "Bubble", "load", "data", "undefined", "divs", "Array", "map", "s", "tmp"], "mappings": "AAAA,SAASA,UAAT,QAA2B,cAA3B;AACA,SAASC,SAAT,QAA0B,aAA1B;AACA,OAAO,MAAMC,MAAN,SAAqBF,UAArB,CAAgC;AACnCG,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,UAAMD,IAAN,CAAWC,IAAX;;AACA,QAAI,EAAEA,IAAI,KAAKC,SAAT,IAAsBD,IAAI,CAACE,IAAL,KAAcD,SAAtC,CAAJ,EAAsD;AAClD;AACH;;AACD,QAAID,IAAI,CAACE,IAAL,YAAqBC,KAAzB,EAAgC;AAC5B,WAAKD,IAAL,GAAYF,IAAI,CAACE,IAAL,CAAUE,GAAV,CAAeC,CAAD,IAAO;AAC7B,cAAMC,GAAG,GAAG,IAAIT,SAAJ,EAAZ;AACAS,QAAAA,GAAG,CAACP,IAAJ,CAASM,CAAT;AACA,eAAOC,GAAP;AACH,OAJW,CAAZ;AAKH,KAND,MAOK;AACD,UAAI,KAAKJ,IAAL,YAAqBC,KAArB,IAA8B,CAAC,KAAKD,IAAxC,EAA8C;AAC1C,aAAKA,IAAL,GAAY,IAAIL,SAAJ,EAAZ;AACH;;AACD,WAAKK,IAAL,CAAUH,IAAV,CAAeC,IAAI,CAACE,IAApB;AACH;AACJ;;AAnBkC", "sourcesContent": ["import { BubbleBase } from \"./BubbleBase\";\nimport { BubbleDiv } from \"./BubbleDiv\";\nexport class B<PERSON>ble extends BubbleBase {\n    load(data) {\n        super.load(data);\n        if (!(data !== undefined && data.divs !== undefined)) {\n            return;\n        }\n        if (data.divs instanceof Array) {\n            this.divs = data.divs.map((s) => {\n                const tmp = new BubbleDiv();\n                tmp.load(s);\n                return tmp;\n            });\n        }\n        else {\n            if (this.divs instanceof Array || !this.divs) {\n                this.divs = new BubbleDiv();\n            }\n            this.divs.load(data.divs);\n        }\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
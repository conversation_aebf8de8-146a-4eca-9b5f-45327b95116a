{"ast": null, "code": "import { PixelMode } from \"@tsparticles/engine\";\nexport class EmitterSize {\n  constructor() {\n    this.mode = PixelMode.percent;\n    this.height = 0;\n    this.width = 0;\n  }\n  load(data) {\n    if (data === undefined) {\n      return;\n    }\n    if (data.mode !== undefined) {\n      this.mode = data.mode;\n    }\n    if (data.height !== undefined) {\n      this.height = data.height;\n    }\n    if (data.width !== undefined) {\n      this.width = data.width;\n    }\n  }\n}", "map": {"version": 3, "names": ["PixelMode", "EmitterSize", "constructor", "mode", "percent", "height", "width", "load", "data", "undefined"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/plugin-emitters/browser/Options/Classes/EmitterSize.js"], "sourcesContent": ["import { PixelMode } from \"@tsparticles/engine\";\nexport class EmitterSize {\n    constructor() {\n        this.mode = PixelMode.percent;\n        this.height = 0;\n        this.width = 0;\n    }\n    load(data) {\n        if (data === undefined) {\n            return;\n        }\n        if (data.mode !== undefined) {\n            this.mode = data.mode;\n        }\n        if (data.height !== undefined) {\n            this.height = data.height;\n        }\n        if (data.width !== undefined) {\n            this.width = data.width;\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,qBAAqB;AAC/C,OAAO,MAAMC,WAAW,CAAC;EACrBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,IAAI,GAAGH,SAAS,CAACI,OAAO;IAC7B,IAAI,CAACC,MAAM,GAAG,CAAC;IACf,IAAI,CAACC,KAAK,GAAG,CAAC;EAClB;EACAC,IAAIA,CAACC,IAAI,EAAE;IACP,IAAIA,IAAI,KAAKC,SAAS,EAAE;MACpB;IACJ;IACA,IAAID,IAAI,CAACL,IAAI,KAAKM,SAAS,EAAE;MACzB,IAAI,CAACN,IAAI,GAAGK,IAAI,CAACL,IAAI;IACzB;IACA,IAAIK,IAAI,CAACH,MAAM,KAAKI,SAAS,EAAE;MAC3B,IAAI,CAACJ,MAAM,GAAGG,IAAI,CAACH,MAAM;IAC7B;IACA,IAAIG,IAAI,CAACF,KAAK,KAAKG,SAAS,EAAE;MAC1B,IAAI,CAACH,KAAK,GAAGE,IAAI,CAACF,KAAK;IAC3B;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
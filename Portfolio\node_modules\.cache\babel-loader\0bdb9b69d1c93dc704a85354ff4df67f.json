{"ast": null, "code": "export class DivEvent {\n  constructor() {\n    this.selectors = [];\n    this.enable = false;\n    this.mode = [];\n    this.type = \"circle\";\n  }\n\n  get elementId() {\n    return this.ids;\n  }\n\n  set elementId(value) {\n    this.ids = value;\n  }\n\n  get el() {\n    return this.elementId;\n  }\n\n  set el(value) {\n    this.elementId = value;\n  }\n\n  get ids() {\n    return this.selectors instanceof Array ? this.selectors.map(t => t.replace(\"#\", \"\")) : this.selectors.replace(\"#\", \"\");\n  }\n\n  set ids(value) {\n    this.selectors = value instanceof Array ? value.map(t => `#${t}`) : `#${value}`;\n  }\n\n  load(data) {\n    var _a, _b;\n\n    if (data === undefined) {\n      return;\n    }\n\n    const ids = (_b = (_a = data.ids) !== null && _a !== void 0 ? _a : data.elementId) !== null && _b !== void 0 ? _b : data.el;\n\n    if (ids !== undefined) {\n      this.ids = ids;\n    }\n\n    if (data.selectors !== undefined) {\n      this.selectors = data.selectors;\n    }\n\n    if (data.enable !== undefined) {\n      this.enable = data.enable;\n    }\n\n    if (data.mode !== undefined) {\n      this.mode = data.mode;\n    }\n\n    if (data.type !== undefined) {\n      this.type = data.type;\n    }\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Options/Classes/Interactivity/Events/DivEvent.js"], "names": ["DivEvent", "constructor", "selectors", "enable", "mode", "type", "elementId", "ids", "value", "el", "Array", "map", "t", "replace", "load", "data", "_a", "_b", "undefined"], "mappings": "AAAA,OAAO,MAAMA,QAAN,CAAe;AAClBC,EAAAA,WAAW,GAAG;AACV,SAAKC,SAAL,GAAiB,EAAjB;AACA,SAAKC,MAAL,GAAc,KAAd;AACA,SAAKC,IAAL,GAAY,EAAZ;AACA,SAAKC,IAAL,GAAY,QAAZ;AACH;;AACY,MAATC,SAAS,GAAG;AACZ,WAAO,KAAKC,GAAZ;AACH;;AACY,MAATD,SAAS,CAACE,KAAD,EAAQ;AACjB,SAAKD,GAAL,GAAWC,KAAX;AACH;;AACK,MAAFC,EAAE,GAAG;AACL,WAAO,KAAKH,SAAZ;AACH;;AACK,MAAFG,EAAE,CAACD,KAAD,EAAQ;AACV,SAAKF,SAAL,GAAiBE,KAAjB;AACH;;AACM,MAAHD,GAAG,GAAG;AACN,WAAO,KAAKL,SAAL,YAA0BQ,KAA1B,GACD,KAAKR,SAAL,CAAeS,GAAf,CAAoBC,CAAD,IAAOA,CAAC,CAACC,OAAF,CAAU,GAAV,EAAe,EAAf,CAA1B,CADC,GAED,KAAKX,SAAL,CAAeW,OAAf,CAAuB,GAAvB,EAA4B,EAA5B,CAFN;AAGH;;AACM,MAAHN,GAAG,CAACC,KAAD,EAAQ;AACX,SAAKN,SAAL,GAAiBM,KAAK,YAAYE,KAAjB,GAAyBF,KAAK,CAACG,GAAN,CAAWC,CAAD,IAAQ,IAAGA,CAAE,EAAvB,CAAzB,GAAsD,IAAGJ,KAAM,EAAhF;AACH;;AACDM,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,QAAIC,EAAJ,EAAQC,EAAR;;AACA,QAAIF,IAAI,KAAKG,SAAb,EAAwB;AACpB;AACH;;AACD,UAAMX,GAAG,GAAG,CAACU,EAAE,GAAG,CAACD,EAAE,GAAGD,IAAI,CAACR,GAAX,MAAoB,IAApB,IAA4BS,EAAE,KAAK,KAAK,CAAxC,GAA4CA,EAA5C,GAAiDD,IAAI,CAACT,SAA5D,MAA2E,IAA3E,IAAmFW,EAAE,KAAK,KAAK,CAA/F,GAAmGA,EAAnG,GAAwGF,IAAI,CAACN,EAAzH;;AACA,QAAIF,GAAG,KAAKW,SAAZ,EAAuB;AACnB,WAAKX,GAAL,GAAWA,GAAX;AACH;;AACD,QAAIQ,IAAI,CAACb,SAAL,KAAmBgB,SAAvB,EAAkC;AAC9B,WAAKhB,SAAL,GAAiBa,IAAI,CAACb,SAAtB;AACH;;AACD,QAAIa,IAAI,CAACZ,MAAL,KAAgBe,SAApB,EAA+B;AAC3B,WAAKf,MAAL,GAAcY,IAAI,CAACZ,MAAnB;AACH;;AACD,QAAIY,IAAI,CAACX,IAAL,KAAcc,SAAlB,EAA6B;AACzB,WAAKd,IAAL,GAAYW,IAAI,CAACX,IAAjB;AACH;;AACD,QAAIW,IAAI,CAACV,IAAL,KAAca,SAAlB,EAA6B;AACzB,WAAKb,IAAL,GAAYU,IAAI,CAACV,IAAjB;AACH;AACJ;;AAhDiB", "sourcesContent": ["export class DivEvent {\n    constructor() {\n        this.selectors = [];\n        this.enable = false;\n        this.mode = [];\n        this.type = \"circle\";\n    }\n    get elementId() {\n        return this.ids;\n    }\n    set elementId(value) {\n        this.ids = value;\n    }\n    get el() {\n        return this.elementId;\n    }\n    set el(value) {\n        this.elementId = value;\n    }\n    get ids() {\n        return this.selectors instanceof Array\n            ? this.selectors.map((t) => t.replace(\"#\", \"\"))\n            : this.selectors.replace(\"#\", \"\");\n    }\n    set ids(value) {\n        this.selectors = value instanceof Array ? value.map((t) => `#${t}`) : `#${value}`;\n    }\n    load(data) {\n        var _a, _b;\n        if (data === undefined) {\n            return;\n        }\n        const ids = (_b = (_a = data.ids) !== null && _a !== void 0 ? _a : data.elementId) !== null && _b !== void 0 ? _b : data.el;\n        if (ids !== undefined) {\n            this.ids = ids;\n        }\n        if (data.selectors !== undefined) {\n            this.selectors = data.selectors;\n        }\n        if (data.enable !== undefined) {\n            this.enable = data.enable;\n        }\n        if (data.mode !== undefined) {\n            this.mode = data.mode;\n        }\n        if (data.type !== undefined) {\n            this.type = data.type;\n        }\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
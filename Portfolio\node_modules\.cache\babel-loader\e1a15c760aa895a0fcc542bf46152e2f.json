{"ast": null, "code": "import toDate from \"../../toDate/index.js\";\nimport startOfUTCI<PERSON>Week from \"../startOfUTCISOWeek/index.js\";\nimport startOfUTCISOWeekYear from \"../startOfUTCISOWeekYear/index.js\";\nimport requiredArgs from \"../requiredArgs/index.js\";\nvar MILLISECONDS_IN_WEEK = 604800000; // This function will be a part of public API when UTC function will be implemented.\n// See issue: https://github.com/date-fns/date-fns/issues/376\n\nexport default function getUTCISOWeek(dirtyDate) {\n  requiredArgs(1, arguments);\n  var date = toDate(dirtyDate);\n  var diff = startOfUTCISOWeek(date).getTime() - startOfUTCISOWeekYear(date).getTime(); // Round the number of days to the nearest integer\n  // because the number of milliseconds in a week is not constant\n  // (e.g. it's different in the week of the daylight saving time clock shift)\n\n  return Math.round(diff / MILLISECONDS_IN_WEEK) + 1;\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/date-fns/esm/_lib/getUTCISOWeek/index.js"], "names": ["toDate", "startOfUTCISOWeek", "startOfUTCISOWeekYear", "requiredArgs", "MILLISECONDS_IN_WEEK", "getUTCISOWeek", "dirtyDate", "arguments", "date", "diff", "getTime", "Math", "round"], "mappings": "AAAA,OAAOA,MAAP,MAAmB,uBAAnB;AACA,OAAOC,iBAAP,MAA8B,+BAA9B;AACA,OAAOC,qBAAP,MAAkC,mCAAlC;AACA,OAAOC,YAAP,MAAyB,0BAAzB;AACA,IAAIC,oBAAoB,GAAG,SAA3B,C,CAAsC;AACtC;;AAEA,eAAe,SAASC,aAAT,CAAuBC,SAAvB,EAAkC;AAC/CH,EAAAA,YAAY,CAAC,CAAD,EAAII,SAAJ,CAAZ;AACA,MAAIC,IAAI,GAAGR,MAAM,CAACM,SAAD,CAAjB;AACA,MAAIG,IAAI,GAAGR,iBAAiB,CAACO,IAAD,CAAjB,CAAwBE,OAAxB,KAAoCR,qBAAqB,CAACM,IAAD,CAArB,CAA4BE,OAA5B,EAA/C,CAH+C,CAGuC;AACtF;AACA;;AAEA,SAAOC,IAAI,CAACC,KAAL,CAAWH,IAAI,GAAGL,oBAAlB,IAA0C,CAAjD;AACD", "sourcesContent": ["import toDate from \"../../toDate/index.js\";\nimport startOfUTCI<PERSON>Week from \"../startOfUTCISOWeek/index.js\";\nimport startOfUTCISOWeekYear from \"../startOfUTCISOWeekYear/index.js\";\nimport requiredArgs from \"../requiredArgs/index.js\";\nvar MILLISECONDS_IN_WEEK = 604800000; // This function will be a part of public API when UTC function will be implemented.\n// See issue: https://github.com/date-fns/date-fns/issues/376\n\nexport default function getUTCISOWeek(dirtyDate) {\n  requiredArgs(1, arguments);\n  var date = toDate(dirtyDate);\n  var diff = startOfUTCISOWeek(date).getTime() - startOfUTCISOWeekYear(date).getTime(); // Round the number of days to the nearest integer\n  // because the number of milliseconds in a week is not constant\n  // (e.g. it's different in the week of the daylight saving time clock shift)\n\n  return Math.round(diff / MILLISECONDS_IN_WEEK) + 1;\n}"]}, "metadata": {}, "sourceType": "module"}
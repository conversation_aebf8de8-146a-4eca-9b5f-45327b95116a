{"ast": null, "code": "import { Circle, Rectangle } from \"@tsparticles/engine\";\nconst double = 2;\nexport class CircleWarp extends Circle {\n  constructor(x, y, radius, canvasSize) {\n    super(x, y, radius);\n    this.canvasSize = canvasSize;\n    this.canvasSize = {\n      ...canvasSize\n    };\n  }\n  contains(point) {\n    const {\n        width,\n        height\n      } = this.canvasSize,\n      {\n        x,\n        y\n      } = point;\n    return super.contains(point) || super.contains({\n      x: x - width,\n      y\n    }) || super.contains({\n      x: x - width,\n      y: y - height\n    }) || super.contains({\n      x,\n      y: y - height\n    });\n  }\n  intersects(range) {\n    if (super.intersects(range)) {\n      return true;\n    }\n    const rect = range,\n      circle = range,\n      newPos = {\n        x: range.position.x - this.canvasSize.width,\n        y: range.position.y - this.canvasSize.height\n      };\n    if (circle.radius !== undefined) {\n      const biggerCircle = new Circle(newPos.x, newPos.y, circle.radius * double);\n      return super.intersects(biggerCircle);\n    } else if (rect.size !== undefined) {\n      const rectSW = new Rectangle(newPos.x, newPos.y, rect.size.width * double, rect.size.height * double);\n      return super.intersects(rectSW);\n    }\n    return false;\n  }\n}", "map": {"version": 3, "names": ["Circle", "Rectangle", "double", "CircleWarp", "constructor", "x", "y", "radius", "canvasSize", "contains", "point", "width", "height", "intersects", "range", "rect", "circle", "newPos", "position", "undefined", "biggerCircle", "size", "rectSW"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/interaction-particles-links/browser/CircleWarp.js"], "sourcesContent": ["import { Circle, Rectangle } from \"@tsparticles/engine\";\nconst double = 2;\nexport class CircleWarp extends Circle {\n    constructor(x, y, radius, canvasSize) {\n        super(x, y, radius);\n        this.canvasSize = canvasSize;\n        this.canvasSize = { ...canvasSize };\n    }\n    contains(point) {\n        const { width, height } = this.canvasSize, { x, y } = point;\n        return (super.contains(point) ||\n            super.contains({ x: x - width, y }) ||\n            super.contains({ x: x - width, y: y - height }) ||\n            super.contains({ x, y: y - height }));\n    }\n    intersects(range) {\n        if (super.intersects(range)) {\n            return true;\n        }\n        const rect = range, circle = range, newPos = {\n            x: range.position.x - this.canvasSize.width,\n            y: range.position.y - this.canvasSize.height,\n        };\n        if (circle.radius !== undefined) {\n            const biggerCircle = new Circle(newPos.x, newPos.y, circle.radius * double);\n            return super.intersects(biggerCircle);\n        }\n        else if (rect.size !== undefined) {\n            const rectSW = new Rectangle(newPos.x, newPos.y, rect.size.width * double, rect.size.height * double);\n            return super.intersects(rectSW);\n        }\n        return false;\n    }\n}\n"], "mappings": "AAAA,SAASA,MAAM,EAAEC,SAAS,QAAQ,qBAAqB;AACvD,MAAMC,MAAM,GAAG,CAAC;AAChB,OAAO,MAAMC,UAAU,SAASH,MAAM,CAAC;EACnCI,WAAWA,CAACC,CAAC,EAAEC,CAAC,EAAEC,MAAM,EAAEC,UAAU,EAAE;IAClC,KAAK,CAACH,CAAC,EAAEC,CAAC,EAAEC,MAAM,CAAC;IACnB,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACA,UAAU,GAAG;MAAE,GAAGA;IAAW,CAAC;EACvC;EACAC,QAAQA,CAACC,KAAK,EAAE;IACZ,MAAM;QAAEC,KAAK;QAAEC;MAAO,CAAC,GAAG,IAAI,CAACJ,UAAU;MAAE;QAAEH,CAAC;QAAEC;MAAE,CAAC,GAAGI,KAAK;IAC3D,OAAQ,KAAK,CAACD,QAAQ,CAACC,KAAK,CAAC,IACzB,KAAK,CAACD,QAAQ,CAAC;MAAEJ,CAAC,EAAEA,CAAC,GAAGM,KAAK;MAAEL;IAAE,CAAC,CAAC,IACnC,KAAK,CAACG,QAAQ,CAAC;MAAEJ,CAAC,EAAEA,CAAC,GAAGM,KAAK;MAAEL,CAAC,EAAEA,CAAC,GAAGM;IAAO,CAAC,CAAC,IAC/C,KAAK,CAACH,QAAQ,CAAC;MAAEJ,CAAC;MAAEC,CAAC,EAAEA,CAAC,GAAGM;IAAO,CAAC,CAAC;EAC5C;EACAC,UAAUA,CAACC,KAAK,EAAE;IACd,IAAI,KAAK,CAACD,UAAU,CAACC,KAAK,CAAC,EAAE;MACzB,OAAO,IAAI;IACf;IACA,MAAMC,IAAI,GAAGD,KAAK;MAAEE,MAAM,GAAGF,KAAK;MAAEG,MAAM,GAAG;QACzCZ,CAAC,EAAES,KAAK,CAACI,QAAQ,CAACb,CAAC,GAAG,IAAI,CAACG,UAAU,CAACG,KAAK;QAC3CL,CAAC,EAAEQ,KAAK,CAACI,QAAQ,CAACZ,CAAC,GAAG,IAAI,CAACE,UAAU,CAACI;MAC1C,CAAC;IACD,IAAII,MAAM,CAACT,MAAM,KAAKY,SAAS,EAAE;MAC7B,MAAMC,YAAY,GAAG,IAAIpB,MAAM,CAACiB,MAAM,CAACZ,CAAC,EAAEY,MAAM,CAACX,CAAC,EAAEU,MAAM,CAACT,MAAM,GAAGL,MAAM,CAAC;MAC3E,OAAO,KAAK,CAACW,UAAU,CAACO,YAAY,CAAC;IACzC,CAAC,MACI,IAAIL,IAAI,CAACM,IAAI,KAAKF,SAAS,EAAE;MAC9B,MAAMG,MAAM,GAAG,IAAIrB,SAAS,CAACgB,MAAM,CAACZ,CAAC,EAAEY,MAAM,CAACX,CAAC,EAAES,IAAI,CAACM,IAAI,CAACV,KAAK,GAAGT,MAAM,EAAEa,IAAI,CAACM,IAAI,CAACT,MAAM,GAAGV,MAAM,CAAC;MACrG,OAAO,KAAK,CAACW,UAAU,CAACS,MAAM,CAAC;IACnC;IACA,OAAO,KAAK;EAChB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "export class Parallax {\n  constructor() {\n    this.enable = false;\n    this.force = 2;\n    this.smooth = 10;\n  }\n\n  load(data) {\n    if (data === undefined) {\n      return;\n    }\n\n    if (data.enable !== undefined) {\n      this.enable = data.enable;\n    }\n\n    if (data.force !== undefined) {\n      this.force = data.force;\n    }\n\n    if (data.smooth !== undefined) {\n      this.smooth = data.smooth;\n    }\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Options/Classes/Interactivity/Events/Parallax.js"], "names": ["Parallax", "constructor", "enable", "force", "smooth", "load", "data", "undefined"], "mappings": "AAAA,OAAO,MAAMA,QAAN,CAAe;AAClBC,EAAAA,WAAW,GAAG;AACV,SAAKC,MAAL,GAAc,KAAd;AACA,SAAKC,KAAL,GAAa,CAAb;AACA,SAAKC,MAAL,GAAc,EAAd;AACH;;AACDC,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,QAAIA,IAAI,KAAKC,SAAb,EAAwB;AACpB;AACH;;AACD,QAAID,IAAI,CAACJ,MAAL,KAAgBK,SAApB,EAA+B;AAC3B,WAAKL,MAAL,GAAcI,IAAI,CAACJ,MAAnB;AACH;;AACD,QAAII,IAAI,CAACH,KAAL,KAAeI,SAAnB,EAA8B;AAC1B,WAAKJ,KAAL,GAAaG,IAAI,CAACH,KAAlB;AACH;;AACD,QAAIG,IAAI,CAACF,MAAL,KAAgBG,SAApB,EAA+B;AAC3B,WAAKH,MAAL,GAAcE,IAAI,CAACF,MAAnB;AACH;AACJ;;AAnBiB", "sourcesContent": ["export class Parallax {\n    constructor() {\n        this.enable = false;\n        this.force = 2;\n        this.smooth = 10;\n    }\n    load(data) {\n        if (data === undefined) {\n            return;\n        }\n        if (data.enable !== undefined) {\n            this.enable = data.enable;\n        }\n        if (data.force !== undefined) {\n            this.force = data.force;\n        }\n        if (data.smooth !== undefined) {\n            this.smooth = data.smooth;\n        }\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
{"ast": null, "code": "import { executeOnSingleOrMultiple, getRangeMax, isInArray, itemFromSingleOrMultiple, loadFont } from \"@tsparticles/engine\";\nimport { drawEmoji } from \"./Utils.js\";\nconst defaultFont = '\"Twemoji Mozilla\", Apple Color Emoji, \"Segoe UI Emoji\", \"Noto Color Emoji\", \"EmojiOne Color\"';\nexport class EmojiDrawer {\n  constructor() {\n    this.validTypes = [\"emoji\"];\n    this._emojiShapeDict = new Map();\n  }\n  destroy() {\n    for (const [key, emojiData] of this._emojiShapeDict) {\n      if (emojiData instanceof ImageBitmap) {\n        emojiData?.close();\n        this._emojiShapeDict.delete(key);\n      }\n    }\n  }\n  draw(data) {\n    drawEmoji(data);\n  }\n  async init(container) {\n    const options = container.actualOptions,\n      {\n        validTypes\n      } = this;\n    if (!validTypes.find(t => isInArray(t, options.particles.shape.type))) {\n      return;\n    }\n    const promises = [loadFont(defaultFont)],\n      shapeOptions = validTypes.map(t => options.particles.shape.options[t]).find(t => !!t);\n    if (shapeOptions) {\n      executeOnSingleOrMultiple(shapeOptions, shape => {\n        if (shape.font) {\n          promises.push(loadFont(shape.font));\n        }\n      });\n    }\n    await Promise.all(promises);\n  }\n  particleDestroy(particle) {\n    delete particle.emojiData;\n  }\n  particleInit(container, particle) {\n    const double = 2,\n      shapeData = particle.shapeData;\n    if (!shapeData?.value) {\n      return;\n    }\n    const emoji = itemFromSingleOrMultiple(shapeData.value, particle.randomIndexData),\n      font = shapeData.font ?? defaultFont;\n    if (!emoji) {\n      return;\n    }\n    const key = `${emoji}_${font}`,\n      existingData = this._emojiShapeDict.get(key);\n    if (existingData) {\n      particle.emojiData = existingData;\n      return;\n    }\n    const canvasSize = getRangeMax(particle.size.value) * double;\n    let emojiData;\n    const maxSize = getRangeMax(particle.size.value);\n    if (typeof OffscreenCanvas !== \"undefined\") {\n      const canvas = new OffscreenCanvas(canvasSize, canvasSize),\n        context = canvas.getContext(\"2d\");\n      if (!context) {\n        return;\n      }\n      context.font = `400 ${maxSize * double}px ${font}`;\n      context.textBaseline = \"middle\";\n      context.textAlign = \"center\";\n      context.fillText(emoji, maxSize, maxSize);\n      emojiData = canvas.transferToImageBitmap();\n    } else {\n      const canvas = document.createElement(\"canvas\");\n      canvas.width = canvasSize;\n      canvas.height = canvasSize;\n      const context = canvas.getContext(\"2d\");\n      if (!context) {\n        return;\n      }\n      context.font = `400 ${maxSize * double}px ${font}`;\n      context.textBaseline = \"middle\";\n      context.textAlign = \"center\";\n      context.fillText(emoji, maxSize, maxSize);\n      emojiData = canvas;\n    }\n    this._emojiShapeDict.set(key, emojiData);\n    particle.emojiData = emojiData;\n  }\n}", "map": {"version": 3, "names": ["executeOnSingleOrMultiple", "getRangeMax", "isInArray", "itemFromSingleOrMultiple", "loadFont", "<PERSON><PERSON><PERSON><PERSON>", "defaultFont", "EmojiDrawer", "constructor", "validTypes", "_emojiShapeDict", "Map", "destroy", "key", "emojiData", "ImageBitmap", "close", "delete", "draw", "data", "init", "container", "options", "actualOptions", "find", "t", "particles", "shape", "type", "promises", "shapeOptions", "map", "font", "push", "Promise", "all", "particleDestroy", "particle", "particleInit", "double", "shapeData", "value", "emoji", "randomIndexData", "existingData", "get", "canvasSize", "size", "maxSize", "OffscreenCanvas", "canvas", "context", "getContext", "textBaseline", "textAlign", "fillText", "transferToImageBitmap", "document", "createElement", "width", "height", "set"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/shape-emoji/browser/EmojiDrawer.js"], "sourcesContent": ["import { executeOnSingleOrMultiple, getRangeMax, isInArray, itemFromSingleOrMultiple, loadFont, } from \"@tsparticles/engine\";\nimport { drawEmoji } from \"./Utils.js\";\nconst defaultFont = '\"Twemoji Mozilla\", Apple Color Emoji, \"Segoe UI Emoji\", \"Noto Color Emoji\", \"EmojiOne Color\"';\nexport class EmojiDrawer {\n    constructor() {\n        this.validTypes = [\"emoji\"];\n        this._emojiShapeDict = new Map();\n    }\n    destroy() {\n        for (const [key, emojiData] of this._emojiShapeDict) {\n            if (emojiData instanceof ImageBitmap) {\n                emojiData?.close();\n                this._emojiShapeDict.delete(key);\n            }\n        }\n    }\n    draw(data) {\n        drawEmoji(data);\n    }\n    async init(container) {\n        const options = container.actualOptions, { validTypes } = this;\n        if (!validTypes.find(t => isInArray(t, options.particles.shape.type))) {\n            return;\n        }\n        const promises = [loadFont(defaultFont)], shapeOptions = validTypes\n            .map(t => options.particles.shape.options[t])\n            .find(t => !!t);\n        if (shapeOptions) {\n            executeOnSingleOrMultiple(shapeOptions, shape => {\n                if (shape.font) {\n                    promises.push(loadFont(shape.font));\n                }\n            });\n        }\n        await Promise.all(promises);\n    }\n    particleDestroy(particle) {\n        delete particle.emojiData;\n    }\n    particleInit(container, particle) {\n        const double = 2, shapeData = particle.shapeData;\n        if (!shapeData?.value) {\n            return;\n        }\n        const emoji = itemFromSingleOrMultiple(shapeData.value, particle.randomIndexData), font = shapeData.font ?? defaultFont;\n        if (!emoji) {\n            return;\n        }\n        const key = `${emoji}_${font}`, existingData = this._emojiShapeDict.get(key);\n        if (existingData) {\n            particle.emojiData = existingData;\n            return;\n        }\n        const canvasSize = getRangeMax(particle.size.value) * double;\n        let emojiData;\n        const maxSize = getRangeMax(particle.size.value);\n        if (typeof OffscreenCanvas !== \"undefined\") {\n            const canvas = new OffscreenCanvas(canvasSize, canvasSize), context = canvas.getContext(\"2d\");\n            if (!context) {\n                return;\n            }\n            context.font = `400 ${maxSize * double}px ${font}`;\n            context.textBaseline = \"middle\";\n            context.textAlign = \"center\";\n            context.fillText(emoji, maxSize, maxSize);\n            emojiData = canvas.transferToImageBitmap();\n        }\n        else {\n            const canvas = document.createElement(\"canvas\");\n            canvas.width = canvasSize;\n            canvas.height = canvasSize;\n            const context = canvas.getContext(\"2d\");\n            if (!context) {\n                return;\n            }\n            context.font = `400 ${maxSize * double}px ${font}`;\n            context.textBaseline = \"middle\";\n            context.textAlign = \"center\";\n            context.fillText(emoji, maxSize, maxSize);\n            emojiData = canvas;\n        }\n        this._emojiShapeDict.set(key, emojiData);\n        particle.emojiData = emojiData;\n    }\n}\n"], "mappings": "AAAA,SAASA,yBAAyB,EAAEC,WAAW,EAAEC,SAAS,EAAEC,wBAAwB,EAAEC,QAAQ,QAAS,qBAAqB;AAC5H,SAASC,SAAS,QAAQ,YAAY;AACtC,MAAMC,WAAW,GAAG,8FAA8F;AAClH,OAAO,MAAMC,WAAW,CAAC;EACrBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,UAAU,GAAG,CAAC,OAAO,CAAC;IAC3B,IAAI,CAACC,eAAe,GAAG,IAAIC,GAAG,CAAC,CAAC;EACpC;EACAC,OAAOA,CAAA,EAAG;IACN,KAAK,MAAM,CAACC,GAAG,EAAEC,SAAS,CAAC,IAAI,IAAI,CAACJ,eAAe,EAAE;MACjD,IAAII,SAAS,YAAYC,WAAW,EAAE;QAClCD,SAAS,EAAEE,KAAK,CAAC,CAAC;QAClB,IAAI,CAACN,eAAe,CAACO,MAAM,CAACJ,GAAG,CAAC;MACpC;IACJ;EACJ;EACAK,IAAIA,CAACC,IAAI,EAAE;IACPd,SAAS,CAACc,IAAI,CAAC;EACnB;EACA,MAAMC,IAAIA,CAACC,SAAS,EAAE;IAClB,MAAMC,OAAO,GAAGD,SAAS,CAACE,aAAa;MAAE;QAAEd;MAAW,CAAC,GAAG,IAAI;IAC9D,IAAI,CAACA,UAAU,CAACe,IAAI,CAACC,CAAC,IAAIvB,SAAS,CAACuB,CAAC,EAAEH,OAAO,CAACI,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC,CAAC,EAAE;MACnE;IACJ;IACA,MAAMC,QAAQ,GAAG,CAACzB,QAAQ,CAACE,WAAW,CAAC,CAAC;MAAEwB,YAAY,GAAGrB,UAAU,CAC9DsB,GAAG,CAACN,CAAC,IAAIH,OAAO,CAACI,SAAS,CAACC,KAAK,CAACL,OAAO,CAACG,CAAC,CAAC,CAAC,CAC5CD,IAAI,CAACC,CAAC,IAAI,CAAC,CAACA,CAAC,CAAC;IACnB,IAAIK,YAAY,EAAE;MACd9B,yBAAyB,CAAC8B,YAAY,EAAEH,KAAK,IAAI;QAC7C,IAAIA,KAAK,CAACK,IAAI,EAAE;UACZH,QAAQ,CAACI,IAAI,CAAC7B,QAAQ,CAACuB,KAAK,CAACK,IAAI,CAAC,CAAC;QACvC;MACJ,CAAC,CAAC;IACN;IACA,MAAME,OAAO,CAACC,GAAG,CAACN,QAAQ,CAAC;EAC/B;EACAO,eAAeA,CAACC,QAAQ,EAAE;IACtB,OAAOA,QAAQ,CAACvB,SAAS;EAC7B;EACAwB,YAAYA,CAACjB,SAAS,EAAEgB,QAAQ,EAAE;IAC9B,MAAME,MAAM,GAAG,CAAC;MAAEC,SAAS,GAAGH,QAAQ,CAACG,SAAS;IAChD,IAAI,CAACA,SAAS,EAAEC,KAAK,EAAE;MACnB;IACJ;IACA,MAAMC,KAAK,GAAGvC,wBAAwB,CAACqC,SAAS,CAACC,KAAK,EAAEJ,QAAQ,CAACM,eAAe,CAAC;MAAEX,IAAI,GAAGQ,SAAS,CAACR,IAAI,IAAI1B,WAAW;IACvH,IAAI,CAACoC,KAAK,EAAE;MACR;IACJ;IACA,MAAM7B,GAAG,GAAG,GAAG6B,KAAK,IAAIV,IAAI,EAAE;MAAEY,YAAY,GAAG,IAAI,CAAClC,eAAe,CAACmC,GAAG,CAAChC,GAAG,CAAC;IAC5E,IAAI+B,YAAY,EAAE;MACdP,QAAQ,CAACvB,SAAS,GAAG8B,YAAY;MACjC;IACJ;IACA,MAAME,UAAU,GAAG7C,WAAW,CAACoC,QAAQ,CAACU,IAAI,CAACN,KAAK,CAAC,GAAGF,MAAM;IAC5D,IAAIzB,SAAS;IACb,MAAMkC,OAAO,GAAG/C,WAAW,CAACoC,QAAQ,CAACU,IAAI,CAACN,KAAK,CAAC;IAChD,IAAI,OAAOQ,eAAe,KAAK,WAAW,EAAE;MACxC,MAAMC,MAAM,GAAG,IAAID,eAAe,CAACH,UAAU,EAAEA,UAAU,CAAC;QAAEK,OAAO,GAAGD,MAAM,CAACE,UAAU,CAAC,IAAI,CAAC;MAC7F,IAAI,CAACD,OAAO,EAAE;QACV;MACJ;MACAA,OAAO,CAACnB,IAAI,GAAG,OAAOgB,OAAO,GAAGT,MAAM,MAAMP,IAAI,EAAE;MAClDmB,OAAO,CAACE,YAAY,GAAG,QAAQ;MAC/BF,OAAO,CAACG,SAAS,GAAG,QAAQ;MAC5BH,OAAO,CAACI,QAAQ,CAACb,KAAK,EAAEM,OAAO,EAAEA,OAAO,CAAC;MACzClC,SAAS,GAAGoC,MAAM,CAACM,qBAAqB,CAAC,CAAC;IAC9C,CAAC,MACI;MACD,MAAMN,MAAM,GAAGO,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;MAC/CR,MAAM,CAACS,KAAK,GAAGb,UAAU;MACzBI,MAAM,CAACU,MAAM,GAAGd,UAAU;MAC1B,MAAMK,OAAO,GAAGD,MAAM,CAACE,UAAU,CAAC,IAAI,CAAC;MACvC,IAAI,CAACD,OAAO,EAAE;QACV;MACJ;MACAA,OAAO,CAACnB,IAAI,GAAG,OAAOgB,OAAO,GAAGT,MAAM,MAAMP,IAAI,EAAE;MAClDmB,OAAO,CAACE,YAAY,GAAG,QAAQ;MAC/BF,OAAO,CAACG,SAAS,GAAG,QAAQ;MAC5BH,OAAO,CAACI,QAAQ,CAACb,KAAK,EAAEM,OAAO,EAAEA,OAAO,CAAC;MACzClC,SAAS,GAAGoC,MAAM;IACtB;IACA,IAAI,CAACxC,eAAe,CAACmD,GAAG,CAAChD,GAAG,EAAEC,SAAS,CAAC;IACxCuB,QAAQ,CAACvB,SAAS,GAAGA,SAAS;EAClC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
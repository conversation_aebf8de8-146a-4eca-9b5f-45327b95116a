{"ast": null, "code": "import { DestroyUpdater } from \"./DestroyUpdater.js\";\nexport async function loadDestroyUpdater(engine, refresh = true) {\n  await engine.addParticleUpdater(\"destroy\", container => {\n    return Promise.resolve(new DestroyUpdater(engine, container));\n  }, refresh);\n}", "map": {"version": 3, "names": ["DestroyUpdater", "loadDestroyUpdater", "engine", "refresh", "addParticleUpdater", "container", "Promise", "resolve"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/updater-destroy/browser/index.js"], "sourcesContent": ["import { DestroyUpdater } from \"./DestroyUpdater.js\";\nexport async function loadDestroyUpdater(engine, refresh = true) {\n    await engine.addParticleUpdater(\"destroy\", container => {\n        return Promise.resolve(new DestroyUpdater(engine, container));\n    }, refresh);\n}\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,qBAAqB;AACpD,OAAO,eAAeC,kBAAkBA,CAACC,MAAM,EAAEC,OAAO,GAAG,IAAI,EAAE;EAC7D,MAAMD,MAAM,CAACE,kBAAkB,CAAC,SAAS,EAAEC,SAAS,IAAI;IACpD,OAAOC,OAAO,CAACC,OAAO,CAAC,IAAIP,cAAc,CAACE,MAAM,EAAEG,SAAS,CAAC,CAAC;EACjE,CAAC,EAAEF,OAAO,CAAC;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
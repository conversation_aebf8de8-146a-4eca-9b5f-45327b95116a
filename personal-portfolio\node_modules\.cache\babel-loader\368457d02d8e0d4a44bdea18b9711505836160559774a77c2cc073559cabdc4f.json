{"ast": null, "code": "import { PixelMode, Vector, calcPositionOrRandomFromSizeRanged, deepExtend, getRangeValue, getSize, isPointInside, itemFromSingleOrMultiple, millisecondsToSeconds, randomInRange, rangeColorToHsl } from \"@tsparticles/engine\";\nimport { Emitter } from \"./Options/Classes/Emitter.js\";\nimport { EmitterSize } from \"./Options/Classes/EmitterSize.js\";\nconst half = 0.5,\n  defaultLifeDelay = 0,\n  minLifeCount = 0,\n  defaultSpawnDelay = 0,\n  defaultEmitDelay = 0,\n  defaultLifeCount = -1,\n  defaultColorAnimationFactor = 1;\nfunction setParticlesOptionsColor(particlesOptions, color) {\n  if (particlesOptions.color) {\n    particlesOptions.color.value = color;\n  } else {\n    particlesOptions.color = {\n      value: color\n    };\n  }\n}\nexport class EmitterInstance {\n  constructor(engine, emitters, container, options, position) {\n    this.emitters = emitters;\n    this.container = container;\n    this._destroy = () => {\n      this._mutationObserver?.disconnect();\n      this._mutationObserver = undefined;\n      this._resizeObserver?.disconnect();\n      this._resizeObserver = undefined;\n      this.emitters.removeEmitter(this);\n      this._engine.dispatchEvent(\"emitterDestroyed\", {\n        container: this.container,\n        data: {\n          emitter: this\n        }\n      });\n    };\n    this._prepareToDie = () => {\n      if (this._paused) {\n        return;\n      }\n      const duration = this.options.life?.duration !== undefined ? getRangeValue(this.options.life.duration) : undefined,\n        minDuration = 0,\n        minLifeCount = 0;\n      if (this.container.retina.reduceFactor && (this._lifeCount > minLifeCount || this._immortal) && duration !== undefined && duration > minDuration) {\n        this._duration = duration * millisecondsToSeconds;\n      }\n    };\n    this._setColorAnimation = (animation, initValue, maxValue, factor = defaultColorAnimationFactor) => {\n      const container = this.container;\n      if (!animation.enable) {\n        return initValue;\n      }\n      const colorOffset = randomInRange(animation.offset),\n        delay = getRangeValue(this.options.rate.delay),\n        emitFactor = delay * millisecondsToSeconds / container.retina.reduceFactor,\n        defaultColorSpeed = 0,\n        colorSpeed = getRangeValue(animation.speed ?? defaultColorSpeed);\n      return (initValue + colorSpeed * container.fpsLimit / emitFactor + colorOffset * factor) % maxValue;\n    };\n    this._engine = engine;\n    this._currentDuration = 0;\n    this._currentEmitDelay = 0;\n    this._currentSpawnDelay = 0;\n    this._initialPosition = position;\n    if (options instanceof Emitter) {\n      this.options = options;\n    } else {\n      this.options = new Emitter();\n      this.options.load(options);\n    }\n    this._spawnDelay = getRangeValue(this.options.life.delay ?? defaultLifeDelay) * millisecondsToSeconds / this.container.retina.reduceFactor;\n    this.position = this._initialPosition ?? this._calcPosition();\n    this.name = this.options.name;\n    this.fill = this.options.fill;\n    this._firstSpawn = !this.options.life.wait;\n    this._startParticlesAdded = false;\n    let particlesOptions = deepExtend({}, this.options.particles);\n    particlesOptions ??= {};\n    particlesOptions.move ??= {};\n    particlesOptions.move.direction ??= this.options.direction;\n    if (this.options.spawnColor) {\n      this.spawnColor = rangeColorToHsl(this.options.spawnColor);\n    }\n    this._paused = !this.options.autoPlay;\n    this._particlesOptions = particlesOptions;\n    this._size = this._calcSize();\n    this.size = getSize(this._size, this.container.canvas.size);\n    this._lifeCount = this.options.life.count ?? defaultLifeCount;\n    this._immortal = this._lifeCount <= minLifeCount;\n    if (this.options.domId) {\n      const element = document.getElementById(this.options.domId);\n      if (element) {\n        this._mutationObserver = new MutationObserver(() => {\n          this.resize();\n        });\n        this._resizeObserver = new ResizeObserver(() => {\n          this.resize();\n        });\n        this._mutationObserver.observe(element, {\n          attributes: true,\n          attributeFilter: [\"style\", \"width\", \"height\"]\n        });\n        this._resizeObserver.observe(element);\n      }\n    }\n    const shapeOptions = this.options.shape,\n      shapeGenerator = this._engine.emitterShapeManager?.getShapeGenerator(shapeOptions.type);\n    if (shapeGenerator) {\n      this._shape = shapeGenerator.generate(this.position, this.size, this.fill, shapeOptions.options);\n    }\n    this._engine.dispatchEvent(\"emitterCreated\", {\n      container,\n      data: {\n        emitter: this\n      }\n    });\n    this.play();\n  }\n  externalPause() {\n    this._paused = true;\n    this.pause();\n  }\n  externalPlay() {\n    this._paused = false;\n    this.play();\n  }\n  async init() {\n    await this._shape?.init();\n  }\n  pause() {\n    if (this._paused) {\n      return;\n    }\n    delete this._emitDelay;\n  }\n  play() {\n    if (this._paused) {\n      return;\n    }\n    if (!(this.container.retina.reduceFactor && (this._lifeCount > minLifeCount || this._immortal || !this.options.life.count) && (this._firstSpawn || this._currentSpawnDelay >= (this._spawnDelay ?? defaultSpawnDelay)))) {\n      return;\n    }\n    if (this._emitDelay === undefined) {\n      const delay = getRangeValue(this.options.rate.delay);\n      this._emitDelay = delay * millisecondsToSeconds / this.container.retina.reduceFactor;\n    }\n    if (this._lifeCount > minLifeCount || this._immortal) {\n      this._prepareToDie();\n    }\n  }\n  resize() {\n    const initialPosition = this._initialPosition;\n    this.position = initialPosition && isPointInside(initialPosition, this.container.canvas.size, Vector.origin) ? initialPosition : this._calcPosition();\n    this._size = this._calcSize();\n    this.size = getSize(this._size, this.container.canvas.size);\n    this._shape?.resize(this.position, this.size);\n  }\n  update(delta) {\n    if (this._paused) {\n      return;\n    }\n    if (this._firstSpawn) {\n      this._firstSpawn = false;\n      this._currentSpawnDelay = this._spawnDelay ?? defaultSpawnDelay;\n      this._currentEmitDelay = this._emitDelay ?? defaultEmitDelay;\n    }\n    if (!this._startParticlesAdded) {\n      this._startParticlesAdded = true;\n      this._emitParticles(this.options.startCount);\n    }\n    if (this._duration !== undefined) {\n      this._currentDuration += delta.value;\n      if (this._currentDuration >= this._duration) {\n        this.pause();\n        if (this._spawnDelay !== undefined) {\n          delete this._spawnDelay;\n        }\n        if (!this._immortal) {\n          this._lifeCount--;\n        }\n        if (this._lifeCount > minLifeCount || this._immortal) {\n          this.position = this._calcPosition();\n          this._shape?.resize(this.position, this.size);\n          this._spawnDelay = getRangeValue(this.options.life.delay ?? defaultLifeDelay) * millisecondsToSeconds / this.container.retina.reduceFactor;\n        } else {\n          this._destroy();\n        }\n        this._currentDuration -= this._duration;\n        delete this._duration;\n      }\n    }\n    if (this._spawnDelay !== undefined) {\n      this._currentSpawnDelay += delta.value;\n      if (this._currentSpawnDelay >= this._spawnDelay) {\n        this._engine.dispatchEvent(\"emitterPlay\", {\n          container: this.container\n        });\n        this.play();\n        this._currentSpawnDelay -= this._currentSpawnDelay;\n        delete this._spawnDelay;\n      }\n    }\n    if (this._emitDelay !== undefined) {\n      this._currentEmitDelay += delta.value;\n      if (this._currentEmitDelay >= this._emitDelay) {\n        this._emit();\n        this._currentEmitDelay -= this._emitDelay;\n      }\n    }\n  }\n  _calcPosition() {\n    if (this.options.domId) {\n      const element = document.getElementById(this.options.domId);\n      if (element) {\n        const elRect = element.getBoundingClientRect(),\n          pxRatio = this.container.retina.pixelRatio;\n        return {\n          x: (elRect.x + elRect.width * half) * pxRatio,\n          y: (elRect.y + elRect.height * half) * pxRatio\n        };\n      }\n    }\n    return calcPositionOrRandomFromSizeRanged({\n      size: this.container.canvas.size,\n      position: this.options.position\n    });\n  }\n  _calcSize() {\n    const container = this.container;\n    if (this.options.domId) {\n      const element = document.getElementById(this.options.domId);\n      if (element) {\n        const elRect = element.getBoundingClientRect();\n        return {\n          width: elRect.width * container.retina.pixelRatio,\n          height: elRect.height * container.retina.pixelRatio,\n          mode: PixelMode.precise\n        };\n      }\n    }\n    return this.options.size ?? (() => {\n      const size = new EmitterSize();\n      size.load({\n        height: 0,\n        mode: PixelMode.percent,\n        width: 0\n      });\n      return size;\n    })();\n  }\n  _emit() {\n    if (this._paused) {\n      return;\n    }\n    const quantity = getRangeValue(this.options.rate.quantity);\n    this._emitParticles(quantity);\n  }\n  _emitParticles(quantity) {\n    const singleParticlesOptions = itemFromSingleOrMultiple(this._particlesOptions);\n    for (let i = 0; i < quantity; i++) {\n      const particlesOptions = deepExtend({}, singleParticlesOptions);\n      if (this.spawnColor) {\n        const hslAnimation = this.options.spawnColor?.animation;\n        if (hslAnimation) {\n          const maxValues = {\n              h: 360,\n              s: 100,\n              l: 100\n            },\n            colorFactor = 3.6;\n          this.spawnColor.h = this._setColorAnimation(hslAnimation.h, this.spawnColor.h, maxValues.h, colorFactor);\n          this.spawnColor.s = this._setColorAnimation(hslAnimation.s, this.spawnColor.s, maxValues.s);\n          this.spawnColor.l = this._setColorAnimation(hslAnimation.l, this.spawnColor.l, maxValues.l);\n        }\n        setParticlesOptionsColor(particlesOptions, this.spawnColor);\n      }\n      const shapeOptions = this.options.shape;\n      let position = this.position;\n      if (this._shape) {\n        const shapePosData = this._shape.randomPosition();\n        if (shapePosData) {\n          position = shapePosData.position;\n          const replaceData = shapeOptions.replace;\n          if (replaceData.color && shapePosData.color) {\n            setParticlesOptionsColor(particlesOptions, shapePosData.color);\n          }\n          if (replaceData.opacity) {\n            if (particlesOptions.opacity) {\n              particlesOptions.opacity.value = shapePosData.opacity;\n            } else {\n              particlesOptions.opacity = {\n                value: shapePosData.opacity\n              };\n            }\n          }\n        } else {\n          position = null;\n        }\n      }\n      if (position) {\n        this.container.particles.addParticle(position, particlesOptions);\n      }\n    }\n  }\n}", "map": {"version": 3, "names": ["PixelMode", "Vector", "calcPositionOrRandomFromSizeRanged", "deepExtend", "getRangeValue", "getSize", "isPointInside", "itemFromSingleOrMultiple", "millisecondsToSeconds", "randomInRange", "rangeColorToHsl", "Emitter", "EmitterSize", "half", "defaultLifeDelay", "minLifeCount", "defaultSpawnDelay", "defaultEmitDelay", "defaultLifeCount", "defaultColorAnimationFactor", "setParticlesOptionsColor", "particlesOptions", "color", "value", "EmitterInstance", "constructor", "engine", "emitters", "container", "options", "position", "_destroy", "_mutationObserver", "disconnect", "undefined", "_resizeObserver", "removeEmitter", "_engine", "dispatchEvent", "data", "emitter", "_prepareToDie", "_paused", "duration", "life", "minDuration", "retina", "reduceFactor", "_lifeCount", "_immortal", "_duration", "_setColorAnimation", "animation", "initValue", "maxValue", "factor", "enable", "colorOffset", "offset", "delay", "rate", "emitFactor", "defaultColorSpeed", "colorSpeed", "speed", "fpsLimit", "_currentDuration", "_currentEmitDelay", "_currentSpawnDelay", "_initialPosition", "load", "_spawnDelay", "_calcPosition", "name", "fill", "_firstSpawn", "wait", "_startParticlesAdded", "particles", "move", "direction", "spawnColor", "autoPlay", "_particlesOptions", "_size", "_calcSize", "size", "canvas", "count", "domId", "element", "document", "getElementById", "MutationObserver", "resize", "ResizeObserver", "observe", "attributes", "attributeFilter", "shapeOptions", "shape", "shapeGenerator", "emitter<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getShapeGenerator", "type", "_shape", "generate", "play", "externalPause", "pause", "externalPlay", "init", "_emitDelay", "initialPosition", "origin", "update", "delta", "_emitParticles", "startCount", "_emit", "elRect", "getBoundingClientRect", "pxRatio", "pixelRatio", "x", "width", "y", "height", "mode", "precise", "percent", "quantity", "singleParticlesOptions", "i", "hslAnimation", "max<PERSON><PERSON><PERSON>", "h", "s", "l", "colorFactor", "shapePosData", "randomPosition", "replaceData", "replace", "opacity", "addParticle"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/plugin-emitters/browser/EmitterInstance.js"], "sourcesContent": ["import { PixelMode, Vector, calcPositionOrRandomFromSizeRanged, deepExtend, getRangeValue, getSize, isPointInside, itemFromSingleOrMultiple, millisecondsToSeconds, randomInRange, rangeColorToHsl, } from \"@tsparticles/engine\";\nimport { Emitter } from \"./Options/Classes/Emitter.js\";\nimport { EmitterSize } from \"./Options/Classes/EmitterSize.js\";\nconst half = 0.5, defaultLifeDelay = 0, minLifeCount = 0, defaultSpawnDelay = 0, defaultEmitDelay = 0, defaultLifeCount = -1, defaultColorAnimationFactor = 1;\nfunction setParticlesOptionsColor(particlesOptions, color) {\n    if (particlesOptions.color) {\n        particlesOptions.color.value = color;\n    }\n    else {\n        particlesOptions.color = {\n            value: color,\n        };\n    }\n}\nexport class EmitterInstance {\n    constructor(engine, emitters, container, options, position) {\n        this.emitters = emitters;\n        this.container = container;\n        this._destroy = () => {\n            this._mutationObserver?.disconnect();\n            this._mutationObserver = undefined;\n            this._resizeObserver?.disconnect();\n            this._resizeObserver = undefined;\n            this.emitters.removeEmitter(this);\n            this._engine.dispatchEvent(\"emitterDestroyed\", {\n                container: this.container,\n                data: {\n                    emitter: this,\n                },\n            });\n        };\n        this._prepareToDie = () => {\n            if (this._paused) {\n                return;\n            }\n            const duration = this.options.life?.duration !== undefined ? getRangeValue(this.options.life.duration) : undefined, minDuration = 0, minLifeCount = 0;\n            if (this.container.retina.reduceFactor &&\n                (this._lifeCount > minLifeCount || this._immortal) &&\n                duration !== undefined &&\n                duration > minDuration) {\n                this._duration = duration * millisecondsToSeconds;\n            }\n        };\n        this._setColorAnimation = (animation, initValue, maxValue, factor = defaultColorAnimationFactor) => {\n            const container = this.container;\n            if (!animation.enable) {\n                return initValue;\n            }\n            const colorOffset = randomInRange(animation.offset), delay = getRangeValue(this.options.rate.delay), emitFactor = (delay * millisecondsToSeconds) / container.retina.reduceFactor, defaultColorSpeed = 0, colorSpeed = getRangeValue(animation.speed ?? defaultColorSpeed);\n            return (initValue + (colorSpeed * container.fpsLimit) / emitFactor + colorOffset * factor) % maxValue;\n        };\n        this._engine = engine;\n        this._currentDuration = 0;\n        this._currentEmitDelay = 0;\n        this._currentSpawnDelay = 0;\n        this._initialPosition = position;\n        if (options instanceof Emitter) {\n            this.options = options;\n        }\n        else {\n            this.options = new Emitter();\n            this.options.load(options);\n        }\n        this._spawnDelay =\n            (getRangeValue(this.options.life.delay ?? defaultLifeDelay) * millisecondsToSeconds) /\n                this.container.retina.reduceFactor;\n        this.position = this._initialPosition ?? this._calcPosition();\n        this.name = this.options.name;\n        this.fill = this.options.fill;\n        this._firstSpawn = !this.options.life.wait;\n        this._startParticlesAdded = false;\n        let particlesOptions = deepExtend({}, this.options.particles);\n        particlesOptions ??= {};\n        particlesOptions.move ??= {};\n        particlesOptions.move.direction ??= this.options.direction;\n        if (this.options.spawnColor) {\n            this.spawnColor = rangeColorToHsl(this.options.spawnColor);\n        }\n        this._paused = !this.options.autoPlay;\n        this._particlesOptions = particlesOptions;\n        this._size = this._calcSize();\n        this.size = getSize(this._size, this.container.canvas.size);\n        this._lifeCount = this.options.life.count ?? defaultLifeCount;\n        this._immortal = this._lifeCount <= minLifeCount;\n        if (this.options.domId) {\n            const element = document.getElementById(this.options.domId);\n            if (element) {\n                this._mutationObserver = new MutationObserver(() => {\n                    this.resize();\n                });\n                this._resizeObserver = new ResizeObserver(() => {\n                    this.resize();\n                });\n                this._mutationObserver.observe(element, {\n                    attributes: true,\n                    attributeFilter: [\"style\", \"width\", \"height\"],\n                });\n                this._resizeObserver.observe(element);\n            }\n        }\n        const shapeOptions = this.options.shape, shapeGenerator = this._engine.emitterShapeManager?.getShapeGenerator(shapeOptions.type);\n        if (shapeGenerator) {\n            this._shape = shapeGenerator.generate(this.position, this.size, this.fill, shapeOptions.options);\n        }\n        this._engine.dispatchEvent(\"emitterCreated\", {\n            container,\n            data: {\n                emitter: this,\n            },\n        });\n        this.play();\n    }\n    externalPause() {\n        this._paused = true;\n        this.pause();\n    }\n    externalPlay() {\n        this._paused = false;\n        this.play();\n    }\n    async init() {\n        await this._shape?.init();\n    }\n    pause() {\n        if (this._paused) {\n            return;\n        }\n        delete this._emitDelay;\n    }\n    play() {\n        if (this._paused) {\n            return;\n        }\n        if (!(this.container.retina.reduceFactor &&\n            (this._lifeCount > minLifeCount || this._immortal || !this.options.life.count) &&\n            (this._firstSpawn || this._currentSpawnDelay >= (this._spawnDelay ?? defaultSpawnDelay)))) {\n            return;\n        }\n        if (this._emitDelay === undefined) {\n            const delay = getRangeValue(this.options.rate.delay);\n            this._emitDelay = (delay * millisecondsToSeconds) / this.container.retina.reduceFactor;\n        }\n        if (this._lifeCount > minLifeCount || this._immortal) {\n            this._prepareToDie();\n        }\n    }\n    resize() {\n        const initialPosition = this._initialPosition;\n        this.position =\n            initialPosition && isPointInside(initialPosition, this.container.canvas.size, Vector.origin)\n                ? initialPosition\n                : this._calcPosition();\n        this._size = this._calcSize();\n        this.size = getSize(this._size, this.container.canvas.size);\n        this._shape?.resize(this.position, this.size);\n    }\n    update(delta) {\n        if (this._paused) {\n            return;\n        }\n        if (this._firstSpawn) {\n            this._firstSpawn = false;\n            this._currentSpawnDelay = this._spawnDelay ?? defaultSpawnDelay;\n            this._currentEmitDelay = this._emitDelay ?? defaultEmitDelay;\n        }\n        if (!this._startParticlesAdded) {\n            this._startParticlesAdded = true;\n            this._emitParticles(this.options.startCount);\n        }\n        if (this._duration !== undefined) {\n            this._currentDuration += delta.value;\n            if (this._currentDuration >= this._duration) {\n                this.pause();\n                if (this._spawnDelay !== undefined) {\n                    delete this._spawnDelay;\n                }\n                if (!this._immortal) {\n                    this._lifeCount--;\n                }\n                if (this._lifeCount > minLifeCount || this._immortal) {\n                    this.position = this._calcPosition();\n                    this._shape?.resize(this.position, this.size);\n                    this._spawnDelay =\n                        (getRangeValue(this.options.life.delay ?? defaultLifeDelay) * millisecondsToSeconds) /\n                            this.container.retina.reduceFactor;\n                }\n                else {\n                    this._destroy();\n                }\n                this._currentDuration -= this._duration;\n                delete this._duration;\n            }\n        }\n        if (this._spawnDelay !== undefined) {\n            this._currentSpawnDelay += delta.value;\n            if (this._currentSpawnDelay >= this._spawnDelay) {\n                this._engine.dispatchEvent(\"emitterPlay\", {\n                    container: this.container,\n                });\n                this.play();\n                this._currentSpawnDelay -= this._currentSpawnDelay;\n                delete this._spawnDelay;\n            }\n        }\n        if (this._emitDelay !== undefined) {\n            this._currentEmitDelay += delta.value;\n            if (this._currentEmitDelay >= this._emitDelay) {\n                this._emit();\n                this._currentEmitDelay -= this._emitDelay;\n            }\n        }\n    }\n    _calcPosition() {\n        if (this.options.domId) {\n            const element = document.getElementById(this.options.domId);\n            if (element) {\n                const elRect = element.getBoundingClientRect(), pxRatio = this.container.retina.pixelRatio;\n                return {\n                    x: (elRect.x + elRect.width * half) * pxRatio,\n                    y: (elRect.y + elRect.height * half) * pxRatio,\n                };\n            }\n        }\n        return calcPositionOrRandomFromSizeRanged({\n            size: this.container.canvas.size,\n            position: this.options.position,\n        });\n    }\n    _calcSize() {\n        const container = this.container;\n        if (this.options.domId) {\n            const element = document.getElementById(this.options.domId);\n            if (element) {\n                const elRect = element.getBoundingClientRect();\n                return {\n                    width: elRect.width * container.retina.pixelRatio,\n                    height: elRect.height * container.retina.pixelRatio,\n                    mode: PixelMode.precise,\n                };\n            }\n        }\n        return (this.options.size ??\n            (() => {\n                const size = new EmitterSize();\n                size.load({\n                    height: 0,\n                    mode: PixelMode.percent,\n                    width: 0,\n                });\n                return size;\n            })());\n    }\n    _emit() {\n        if (this._paused) {\n            return;\n        }\n        const quantity = getRangeValue(this.options.rate.quantity);\n        this._emitParticles(quantity);\n    }\n    _emitParticles(quantity) {\n        const singleParticlesOptions = itemFromSingleOrMultiple(this._particlesOptions);\n        for (let i = 0; i < quantity; i++) {\n            const particlesOptions = deepExtend({}, singleParticlesOptions);\n            if (this.spawnColor) {\n                const hslAnimation = this.options.spawnColor?.animation;\n                if (hslAnimation) {\n                    const maxValues = {\n                        h: 360,\n                        s: 100,\n                        l: 100,\n                    }, colorFactor = 3.6;\n                    this.spawnColor.h = this._setColorAnimation(hslAnimation.h, this.spawnColor.h, maxValues.h, colorFactor);\n                    this.spawnColor.s = this._setColorAnimation(hslAnimation.s, this.spawnColor.s, maxValues.s);\n                    this.spawnColor.l = this._setColorAnimation(hslAnimation.l, this.spawnColor.l, maxValues.l);\n                }\n                setParticlesOptionsColor(particlesOptions, this.spawnColor);\n            }\n            const shapeOptions = this.options.shape;\n            let position = this.position;\n            if (this._shape) {\n                const shapePosData = this._shape.randomPosition();\n                if (shapePosData) {\n                    position = shapePosData.position;\n                    const replaceData = shapeOptions.replace;\n                    if (replaceData.color && shapePosData.color) {\n                        setParticlesOptionsColor(particlesOptions, shapePosData.color);\n                    }\n                    if (replaceData.opacity) {\n                        if (particlesOptions.opacity) {\n                            particlesOptions.opacity.value = shapePosData.opacity;\n                        }\n                        else {\n                            particlesOptions.opacity = {\n                                value: shapePosData.opacity,\n                            };\n                        }\n                    }\n                }\n                else {\n                    position = null;\n                }\n            }\n            if (position) {\n                this.container.particles.addParticle(position, particlesOptions);\n            }\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,SAAS,EAAEC,MAAM,EAAEC,kCAAkC,EAAEC,UAAU,EAAEC,aAAa,EAAEC,OAAO,EAAEC,aAAa,EAAEC,wBAAwB,EAAEC,qBAAqB,EAAEC,aAAa,EAAEC,eAAe,QAAS,qBAAqB;AAChO,SAASC,OAAO,QAAQ,8BAA8B;AACtD,SAASC,WAAW,QAAQ,kCAAkC;AAC9D,MAAMC,IAAI,GAAG,GAAG;EAAEC,gBAAgB,GAAG,CAAC;EAAEC,YAAY,GAAG,CAAC;EAAEC,iBAAiB,GAAG,CAAC;EAAEC,gBAAgB,GAAG,CAAC;EAAEC,gBAAgB,GAAG,CAAC,CAAC;EAAEC,2BAA2B,GAAG,CAAC;AAC7J,SAASC,wBAAwBA,CAACC,gBAAgB,EAAEC,KAAK,EAAE;EACvD,IAAID,gBAAgB,CAACC,KAAK,EAAE;IACxBD,gBAAgB,CAACC,KAAK,CAACC,KAAK,GAAGD,KAAK;EACxC,CAAC,MACI;IACDD,gBAAgB,CAACC,KAAK,GAAG;MACrBC,KAAK,EAAED;IACX,CAAC;EACL;AACJ;AACA,OAAO,MAAME,eAAe,CAAC;EACzBC,WAAWA,CAACC,MAAM,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,OAAO,EAAEC,QAAQ,EAAE;IACxD,IAAI,CAACH,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACG,QAAQ,GAAG,MAAM;MAClB,IAAI,CAACC,iBAAiB,EAAEC,UAAU,CAAC,CAAC;MACpC,IAAI,CAACD,iBAAiB,GAAGE,SAAS;MAClC,IAAI,CAACC,eAAe,EAAEF,UAAU,CAAC,CAAC;MAClC,IAAI,CAACE,eAAe,GAAGD,SAAS;MAChC,IAAI,CAACP,QAAQ,CAACS,aAAa,CAAC,IAAI,CAAC;MACjC,IAAI,CAACC,OAAO,CAACC,aAAa,CAAC,kBAAkB,EAAE;QAC3CV,SAAS,EAAE,IAAI,CAACA,SAAS;QACzBW,IAAI,EAAE;UACFC,OAAO,EAAE;QACb;MACJ,CAAC,CAAC;IACN,CAAC;IACD,IAAI,CAACC,aAAa,GAAG,MAAM;MACvB,IAAI,IAAI,CAACC,OAAO,EAAE;QACd;MACJ;MACA,MAAMC,QAAQ,GAAG,IAAI,CAACd,OAAO,CAACe,IAAI,EAAED,QAAQ,KAAKT,SAAS,GAAG9B,aAAa,CAAC,IAAI,CAACyB,OAAO,CAACe,IAAI,CAACD,QAAQ,CAAC,GAAGT,SAAS;QAAEW,WAAW,GAAG,CAAC;QAAE9B,YAAY,GAAG,CAAC;MACrJ,IAAI,IAAI,CAACa,SAAS,CAACkB,MAAM,CAACC,YAAY,KACjC,IAAI,CAACC,UAAU,GAAGjC,YAAY,IAAI,IAAI,CAACkC,SAAS,CAAC,IAClDN,QAAQ,KAAKT,SAAS,IACtBS,QAAQ,GAAGE,WAAW,EAAE;QACxB,IAAI,CAACK,SAAS,GAAGP,QAAQ,GAAGnC,qBAAqB;MACrD;IACJ,CAAC;IACD,IAAI,CAAC2C,kBAAkB,GAAG,CAACC,SAAS,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,GAAGpC,2BAA2B,KAAK;MAChG,MAAMS,SAAS,GAAG,IAAI,CAACA,SAAS;MAChC,IAAI,CAACwB,SAAS,CAACI,MAAM,EAAE;QACnB,OAAOH,SAAS;MACpB;MACA,MAAMI,WAAW,GAAGhD,aAAa,CAAC2C,SAAS,CAACM,MAAM,CAAC;QAAEC,KAAK,GAAGvD,aAAa,CAAC,IAAI,CAACyB,OAAO,CAAC+B,IAAI,CAACD,KAAK,CAAC;QAAEE,UAAU,GAAIF,KAAK,GAAGnD,qBAAqB,GAAIoB,SAAS,CAACkB,MAAM,CAACC,YAAY;QAAEe,iBAAiB,GAAG,CAAC;QAAEC,UAAU,GAAG3D,aAAa,CAACgD,SAAS,CAACY,KAAK,IAAIF,iBAAiB,CAAC;MAC1Q,OAAO,CAACT,SAAS,GAAIU,UAAU,GAAGnC,SAAS,CAACqC,QAAQ,GAAIJ,UAAU,GAAGJ,WAAW,GAAGF,MAAM,IAAID,QAAQ;IACzG,CAAC;IACD,IAAI,CAACjB,OAAO,GAAGX,MAAM;IACrB,IAAI,CAACwC,gBAAgB,GAAG,CAAC;IACzB,IAAI,CAACC,iBAAiB,GAAG,CAAC;IAC1B,IAAI,CAACC,kBAAkB,GAAG,CAAC;IAC3B,IAAI,CAACC,gBAAgB,GAAGvC,QAAQ;IAChC,IAAID,OAAO,YAAYlB,OAAO,EAAE;MAC5B,IAAI,CAACkB,OAAO,GAAGA,OAAO;IAC1B,CAAC,MACI;MACD,IAAI,CAACA,OAAO,GAAG,IAAIlB,OAAO,CAAC,CAAC;MAC5B,IAAI,CAACkB,OAAO,CAACyC,IAAI,CAACzC,OAAO,CAAC;IAC9B;IACA,IAAI,CAAC0C,WAAW,GACXnE,aAAa,CAAC,IAAI,CAACyB,OAAO,CAACe,IAAI,CAACe,KAAK,IAAI7C,gBAAgB,CAAC,GAAGN,qBAAqB,GAC/E,IAAI,CAACoB,SAAS,CAACkB,MAAM,CAACC,YAAY;IAC1C,IAAI,CAACjB,QAAQ,GAAG,IAAI,CAACuC,gBAAgB,IAAI,IAAI,CAACG,aAAa,CAAC,CAAC;IAC7D,IAAI,CAACC,IAAI,GAAG,IAAI,CAAC5C,OAAO,CAAC4C,IAAI;IAC7B,IAAI,CAACC,IAAI,GAAG,IAAI,CAAC7C,OAAO,CAAC6C,IAAI;IAC7B,IAAI,CAACC,WAAW,GAAG,CAAC,IAAI,CAAC9C,OAAO,CAACe,IAAI,CAACgC,IAAI;IAC1C,IAAI,CAACC,oBAAoB,GAAG,KAAK;IACjC,IAAIxD,gBAAgB,GAAGlB,UAAU,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC0B,OAAO,CAACiD,SAAS,CAAC;IAC7DzD,gBAAgB,KAAK,CAAC,CAAC;IACvBA,gBAAgB,CAAC0D,IAAI,KAAK,CAAC,CAAC;IAC5B1D,gBAAgB,CAAC0D,IAAI,CAACC,SAAS,KAAK,IAAI,CAACnD,OAAO,CAACmD,SAAS;IAC1D,IAAI,IAAI,CAACnD,OAAO,CAACoD,UAAU,EAAE;MACzB,IAAI,CAACA,UAAU,GAAGvE,eAAe,CAAC,IAAI,CAACmB,OAAO,CAACoD,UAAU,CAAC;IAC9D;IACA,IAAI,CAACvC,OAAO,GAAG,CAAC,IAAI,CAACb,OAAO,CAACqD,QAAQ;IACrC,IAAI,CAACC,iBAAiB,GAAG9D,gBAAgB;IACzC,IAAI,CAAC+D,KAAK,GAAG,IAAI,CAACC,SAAS,CAAC,CAAC;IAC7B,IAAI,CAACC,IAAI,GAAGjF,OAAO,CAAC,IAAI,CAAC+E,KAAK,EAAE,IAAI,CAACxD,SAAS,CAAC2D,MAAM,CAACD,IAAI,CAAC;IAC3D,IAAI,CAACtC,UAAU,GAAG,IAAI,CAACnB,OAAO,CAACe,IAAI,CAAC4C,KAAK,IAAItE,gBAAgB;IAC7D,IAAI,CAAC+B,SAAS,GAAG,IAAI,CAACD,UAAU,IAAIjC,YAAY;IAChD,IAAI,IAAI,CAACc,OAAO,CAAC4D,KAAK,EAAE;MACpB,MAAMC,OAAO,GAAGC,QAAQ,CAACC,cAAc,CAAC,IAAI,CAAC/D,OAAO,CAAC4D,KAAK,CAAC;MAC3D,IAAIC,OAAO,EAAE;QACT,IAAI,CAAC1D,iBAAiB,GAAG,IAAI6D,gBAAgB,CAAC,MAAM;UAChD,IAAI,CAACC,MAAM,CAAC,CAAC;QACjB,CAAC,CAAC;QACF,IAAI,CAAC3D,eAAe,GAAG,IAAI4D,cAAc,CAAC,MAAM;UAC5C,IAAI,CAACD,MAAM,CAAC,CAAC;QACjB,CAAC,CAAC;QACF,IAAI,CAAC9D,iBAAiB,CAACgE,OAAO,CAACN,OAAO,EAAE;UACpCO,UAAU,EAAE,IAAI;UAChBC,eAAe,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,QAAQ;QAChD,CAAC,CAAC;QACF,IAAI,CAAC/D,eAAe,CAAC6D,OAAO,CAACN,OAAO,CAAC;MACzC;IACJ;IACA,MAAMS,YAAY,GAAG,IAAI,CAACtE,OAAO,CAACuE,KAAK;MAAEC,cAAc,GAAG,IAAI,CAAChE,OAAO,CAACiE,mBAAmB,EAAEC,iBAAiB,CAACJ,YAAY,CAACK,IAAI,CAAC;IAChI,IAAIH,cAAc,EAAE;MAChB,IAAI,CAACI,MAAM,GAAGJ,cAAc,CAACK,QAAQ,CAAC,IAAI,CAAC5E,QAAQ,EAAE,IAAI,CAACwD,IAAI,EAAE,IAAI,CAACZ,IAAI,EAAEyB,YAAY,CAACtE,OAAO,CAAC;IACpG;IACA,IAAI,CAACQ,OAAO,CAACC,aAAa,CAAC,gBAAgB,EAAE;MACzCV,SAAS;MACTW,IAAI,EAAE;QACFC,OAAO,EAAE;MACb;IACJ,CAAC,CAAC;IACF,IAAI,CAACmE,IAAI,CAAC,CAAC;EACf;EACAC,aAAaA,CAAA,EAAG;IACZ,IAAI,CAAClE,OAAO,GAAG,IAAI;IACnB,IAAI,CAACmE,KAAK,CAAC,CAAC;EAChB;EACAC,YAAYA,CAAA,EAAG;IACX,IAAI,CAACpE,OAAO,GAAG,KAAK;IACpB,IAAI,CAACiE,IAAI,CAAC,CAAC;EACf;EACA,MAAMI,IAAIA,CAAA,EAAG;IACT,MAAM,IAAI,CAACN,MAAM,EAAEM,IAAI,CAAC,CAAC;EAC7B;EACAF,KAAKA,CAAA,EAAG;IACJ,IAAI,IAAI,CAACnE,OAAO,EAAE;MACd;IACJ;IACA,OAAO,IAAI,CAACsE,UAAU;EAC1B;EACAL,IAAIA,CAAA,EAAG;IACH,IAAI,IAAI,CAACjE,OAAO,EAAE;MACd;IACJ;IACA,IAAI,EAAE,IAAI,CAACd,SAAS,CAACkB,MAAM,CAACC,YAAY,KACnC,IAAI,CAACC,UAAU,GAAGjC,YAAY,IAAI,IAAI,CAACkC,SAAS,IAAI,CAAC,IAAI,CAACpB,OAAO,CAACe,IAAI,CAAC4C,KAAK,CAAC,KAC7E,IAAI,CAACb,WAAW,IAAI,IAAI,CAACP,kBAAkB,KAAK,IAAI,CAACG,WAAW,IAAIvD,iBAAiB,CAAC,CAAC,CAAC,EAAE;MAC3F;IACJ;IACA,IAAI,IAAI,CAACgG,UAAU,KAAK9E,SAAS,EAAE;MAC/B,MAAMyB,KAAK,GAAGvD,aAAa,CAAC,IAAI,CAACyB,OAAO,CAAC+B,IAAI,CAACD,KAAK,CAAC;MACpD,IAAI,CAACqD,UAAU,GAAIrD,KAAK,GAAGnD,qBAAqB,GAAI,IAAI,CAACoB,SAAS,CAACkB,MAAM,CAACC,YAAY;IAC1F;IACA,IAAI,IAAI,CAACC,UAAU,GAAGjC,YAAY,IAAI,IAAI,CAACkC,SAAS,EAAE;MAClD,IAAI,CAACR,aAAa,CAAC,CAAC;IACxB;EACJ;EACAqD,MAAMA,CAAA,EAAG;IACL,MAAMmB,eAAe,GAAG,IAAI,CAAC5C,gBAAgB;IAC7C,IAAI,CAACvC,QAAQ,GACTmF,eAAe,IAAI3G,aAAa,CAAC2G,eAAe,EAAE,IAAI,CAACrF,SAAS,CAAC2D,MAAM,CAACD,IAAI,EAAErF,MAAM,CAACiH,MAAM,CAAC,GACtFD,eAAe,GACf,IAAI,CAACzC,aAAa,CAAC,CAAC;IAC9B,IAAI,CAACY,KAAK,GAAG,IAAI,CAACC,SAAS,CAAC,CAAC;IAC7B,IAAI,CAACC,IAAI,GAAGjF,OAAO,CAAC,IAAI,CAAC+E,KAAK,EAAE,IAAI,CAACxD,SAAS,CAAC2D,MAAM,CAACD,IAAI,CAAC;IAC3D,IAAI,CAACmB,MAAM,EAAEX,MAAM,CAAC,IAAI,CAAChE,QAAQ,EAAE,IAAI,CAACwD,IAAI,CAAC;EACjD;EACA6B,MAAMA,CAACC,KAAK,EAAE;IACV,IAAI,IAAI,CAAC1E,OAAO,EAAE;MACd;IACJ;IACA,IAAI,IAAI,CAACiC,WAAW,EAAE;MAClB,IAAI,CAACA,WAAW,GAAG,KAAK;MACxB,IAAI,CAACP,kBAAkB,GAAG,IAAI,CAACG,WAAW,IAAIvD,iBAAiB;MAC/D,IAAI,CAACmD,iBAAiB,GAAG,IAAI,CAAC6C,UAAU,IAAI/F,gBAAgB;IAChE;IACA,IAAI,CAAC,IAAI,CAAC4D,oBAAoB,EAAE;MAC5B,IAAI,CAACA,oBAAoB,GAAG,IAAI;MAChC,IAAI,CAACwC,cAAc,CAAC,IAAI,CAACxF,OAAO,CAACyF,UAAU,CAAC;IAChD;IACA,IAAI,IAAI,CAACpE,SAAS,KAAKhB,SAAS,EAAE;MAC9B,IAAI,CAACgC,gBAAgB,IAAIkD,KAAK,CAAC7F,KAAK;MACpC,IAAI,IAAI,CAAC2C,gBAAgB,IAAI,IAAI,CAAChB,SAAS,EAAE;QACzC,IAAI,CAAC2D,KAAK,CAAC,CAAC;QACZ,IAAI,IAAI,CAACtC,WAAW,KAAKrC,SAAS,EAAE;UAChC,OAAO,IAAI,CAACqC,WAAW;QAC3B;QACA,IAAI,CAAC,IAAI,CAACtB,SAAS,EAAE;UACjB,IAAI,CAACD,UAAU,EAAE;QACrB;QACA,IAAI,IAAI,CAACA,UAAU,GAAGjC,YAAY,IAAI,IAAI,CAACkC,SAAS,EAAE;UAClD,IAAI,CAACnB,QAAQ,GAAG,IAAI,CAAC0C,aAAa,CAAC,CAAC;UACpC,IAAI,CAACiC,MAAM,EAAEX,MAAM,CAAC,IAAI,CAAChE,QAAQ,EAAE,IAAI,CAACwD,IAAI,CAAC;UAC7C,IAAI,CAACf,WAAW,GACXnE,aAAa,CAAC,IAAI,CAACyB,OAAO,CAACe,IAAI,CAACe,KAAK,IAAI7C,gBAAgB,CAAC,GAAGN,qBAAqB,GAC/E,IAAI,CAACoB,SAAS,CAACkB,MAAM,CAACC,YAAY;QAC9C,CAAC,MACI;UACD,IAAI,CAAChB,QAAQ,CAAC,CAAC;QACnB;QACA,IAAI,CAACmC,gBAAgB,IAAI,IAAI,CAAChB,SAAS;QACvC,OAAO,IAAI,CAACA,SAAS;MACzB;IACJ;IACA,IAAI,IAAI,CAACqB,WAAW,KAAKrC,SAAS,EAAE;MAChC,IAAI,CAACkC,kBAAkB,IAAIgD,KAAK,CAAC7F,KAAK;MACtC,IAAI,IAAI,CAAC6C,kBAAkB,IAAI,IAAI,CAACG,WAAW,EAAE;QAC7C,IAAI,CAAClC,OAAO,CAACC,aAAa,CAAC,aAAa,EAAE;UACtCV,SAAS,EAAE,IAAI,CAACA;QACpB,CAAC,CAAC;QACF,IAAI,CAAC+E,IAAI,CAAC,CAAC;QACX,IAAI,CAACvC,kBAAkB,IAAI,IAAI,CAACA,kBAAkB;QAClD,OAAO,IAAI,CAACG,WAAW;MAC3B;IACJ;IACA,IAAI,IAAI,CAACyC,UAAU,KAAK9E,SAAS,EAAE;MAC/B,IAAI,CAACiC,iBAAiB,IAAIiD,KAAK,CAAC7F,KAAK;MACrC,IAAI,IAAI,CAAC4C,iBAAiB,IAAI,IAAI,CAAC6C,UAAU,EAAE;QAC3C,IAAI,CAACO,KAAK,CAAC,CAAC;QACZ,IAAI,CAACpD,iBAAiB,IAAI,IAAI,CAAC6C,UAAU;MAC7C;IACJ;EACJ;EACAxC,aAAaA,CAAA,EAAG;IACZ,IAAI,IAAI,CAAC3C,OAAO,CAAC4D,KAAK,EAAE;MACpB,MAAMC,OAAO,GAAGC,QAAQ,CAACC,cAAc,CAAC,IAAI,CAAC/D,OAAO,CAAC4D,KAAK,CAAC;MAC3D,IAAIC,OAAO,EAAE;QACT,MAAM8B,MAAM,GAAG9B,OAAO,CAAC+B,qBAAqB,CAAC,CAAC;UAAEC,OAAO,GAAG,IAAI,CAAC9F,SAAS,CAACkB,MAAM,CAAC6E,UAAU;QAC1F,OAAO;UACHC,CAAC,EAAE,CAACJ,MAAM,CAACI,CAAC,GAAGJ,MAAM,CAACK,KAAK,GAAGhH,IAAI,IAAI6G,OAAO;UAC7CI,CAAC,EAAE,CAACN,MAAM,CAACM,CAAC,GAAGN,MAAM,CAACO,MAAM,GAAGlH,IAAI,IAAI6G;QAC3C,CAAC;MACL;IACJ;IACA,OAAOxH,kCAAkC,CAAC;MACtCoF,IAAI,EAAE,IAAI,CAAC1D,SAAS,CAAC2D,MAAM,CAACD,IAAI;MAChCxD,QAAQ,EAAE,IAAI,CAACD,OAAO,CAACC;IAC3B,CAAC,CAAC;EACN;EACAuD,SAASA,CAAA,EAAG;IACR,MAAMzD,SAAS,GAAG,IAAI,CAACA,SAAS;IAChC,IAAI,IAAI,CAACC,OAAO,CAAC4D,KAAK,EAAE;MACpB,MAAMC,OAAO,GAAGC,QAAQ,CAACC,cAAc,CAAC,IAAI,CAAC/D,OAAO,CAAC4D,KAAK,CAAC;MAC3D,IAAIC,OAAO,EAAE;QACT,MAAM8B,MAAM,GAAG9B,OAAO,CAAC+B,qBAAqB,CAAC,CAAC;QAC9C,OAAO;UACHI,KAAK,EAAEL,MAAM,CAACK,KAAK,GAAGjG,SAAS,CAACkB,MAAM,CAAC6E,UAAU;UACjDI,MAAM,EAAEP,MAAM,CAACO,MAAM,GAAGnG,SAAS,CAACkB,MAAM,CAAC6E,UAAU;UACnDK,IAAI,EAAEhI,SAAS,CAACiI;QACpB,CAAC;MACL;IACJ;IACA,OAAQ,IAAI,CAACpG,OAAO,CAACyD,IAAI,IACrB,CAAC,MAAM;MACH,MAAMA,IAAI,GAAG,IAAI1E,WAAW,CAAC,CAAC;MAC9B0E,IAAI,CAAChB,IAAI,CAAC;QACNyD,MAAM,EAAE,CAAC;QACTC,IAAI,EAAEhI,SAAS,CAACkI,OAAO;QACvBL,KAAK,EAAE;MACX,CAAC,CAAC;MACF,OAAOvC,IAAI;IACf,CAAC,EAAE,CAAC;EACZ;EACAiC,KAAKA,CAAA,EAAG;IACJ,IAAI,IAAI,CAAC7E,OAAO,EAAE;MACd;IACJ;IACA,MAAMyF,QAAQ,GAAG/H,aAAa,CAAC,IAAI,CAACyB,OAAO,CAAC+B,IAAI,CAACuE,QAAQ,CAAC;IAC1D,IAAI,CAACd,cAAc,CAACc,QAAQ,CAAC;EACjC;EACAd,cAAcA,CAACc,QAAQ,EAAE;IACrB,MAAMC,sBAAsB,GAAG7H,wBAAwB,CAAC,IAAI,CAAC4E,iBAAiB,CAAC;IAC/E,KAAK,IAAIkD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,QAAQ,EAAEE,CAAC,EAAE,EAAE;MAC/B,MAAMhH,gBAAgB,GAAGlB,UAAU,CAAC,CAAC,CAAC,EAAEiI,sBAAsB,CAAC;MAC/D,IAAI,IAAI,CAACnD,UAAU,EAAE;QACjB,MAAMqD,YAAY,GAAG,IAAI,CAACzG,OAAO,CAACoD,UAAU,EAAE7B,SAAS;QACvD,IAAIkF,YAAY,EAAE;UACd,MAAMC,SAAS,GAAG;cACdC,CAAC,EAAE,GAAG;cACNC,CAAC,EAAE,GAAG;cACNC,CAAC,EAAE;YACP,CAAC;YAAEC,WAAW,GAAG,GAAG;UACpB,IAAI,CAAC1D,UAAU,CAACuD,CAAC,GAAG,IAAI,CAACrF,kBAAkB,CAACmF,YAAY,CAACE,CAAC,EAAE,IAAI,CAACvD,UAAU,CAACuD,CAAC,EAAED,SAAS,CAACC,CAAC,EAAEG,WAAW,CAAC;UACxG,IAAI,CAAC1D,UAAU,CAACwD,CAAC,GAAG,IAAI,CAACtF,kBAAkB,CAACmF,YAAY,CAACG,CAAC,EAAE,IAAI,CAACxD,UAAU,CAACwD,CAAC,EAAEF,SAAS,CAACE,CAAC,CAAC;UAC3F,IAAI,CAACxD,UAAU,CAACyD,CAAC,GAAG,IAAI,CAACvF,kBAAkB,CAACmF,YAAY,CAACI,CAAC,EAAE,IAAI,CAACzD,UAAU,CAACyD,CAAC,EAAEH,SAAS,CAACG,CAAC,CAAC;QAC/F;QACAtH,wBAAwB,CAACC,gBAAgB,EAAE,IAAI,CAAC4D,UAAU,CAAC;MAC/D;MACA,MAAMkB,YAAY,GAAG,IAAI,CAACtE,OAAO,CAACuE,KAAK;MACvC,IAAItE,QAAQ,GAAG,IAAI,CAACA,QAAQ;MAC5B,IAAI,IAAI,CAAC2E,MAAM,EAAE;QACb,MAAMmC,YAAY,GAAG,IAAI,CAACnC,MAAM,CAACoC,cAAc,CAAC,CAAC;QACjD,IAAID,YAAY,EAAE;UACd9G,QAAQ,GAAG8G,YAAY,CAAC9G,QAAQ;UAChC,MAAMgH,WAAW,GAAG3C,YAAY,CAAC4C,OAAO;UACxC,IAAID,WAAW,CAACxH,KAAK,IAAIsH,YAAY,CAACtH,KAAK,EAAE;YACzCF,wBAAwB,CAACC,gBAAgB,EAAEuH,YAAY,CAACtH,KAAK,CAAC;UAClE;UACA,IAAIwH,WAAW,CAACE,OAAO,EAAE;YACrB,IAAI3H,gBAAgB,CAAC2H,OAAO,EAAE;cAC1B3H,gBAAgB,CAAC2H,OAAO,CAACzH,KAAK,GAAGqH,YAAY,CAACI,OAAO;YACzD,CAAC,MACI;cACD3H,gBAAgB,CAAC2H,OAAO,GAAG;gBACvBzH,KAAK,EAAEqH,YAAY,CAACI;cACxB,CAAC;YACL;UACJ;QACJ,CAAC,MACI;UACDlH,QAAQ,GAAG,IAAI;QACnB;MACJ;MACA,IAAIA,QAAQ,EAAE;QACV,IAAI,CAACF,SAAS,CAACkD,SAAS,CAACmE,WAAW,CAACnH,QAAQ,EAAET,gBAAgB,CAAC;MACpE;IACJ;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
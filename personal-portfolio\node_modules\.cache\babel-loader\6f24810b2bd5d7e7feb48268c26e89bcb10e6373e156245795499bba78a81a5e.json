{"ast": null, "code": "import { ValueWithRandom } from \"../../ValueWithRandom.js\";\nexport class ZIndex extends ValueWithRandom {\n  constructor() {\n    super();\n    this.opacityRate = 1;\n    this.sizeRate = 1;\n    this.velocityRate = 1;\n  }\n  load(data) {\n    super.load(data);\n    if (!data) {\n      return;\n    }\n    if (data.opacityRate !== undefined) {\n      this.opacityRate = data.opacityRate;\n    }\n    if (data.sizeRate !== undefined) {\n      this.sizeRate = data.sizeRate;\n    }\n    if (data.velocityRate !== undefined) {\n      this.velocityRate = data.velocityRate;\n    }\n  }\n}", "map": {"version": 3, "names": ["ValueWithRandom", "ZIndex", "constructor", "opacityRate", "sizeRate", "velocityRate", "load", "data", "undefined"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/engine/browser/Options/Classes/Particles/ZIndex/ZIndex.js"], "sourcesContent": ["import { ValueWithRandom } from \"../../ValueWithRandom.js\";\nexport class ZIndex extends ValueWithRandom {\n    constructor() {\n        super();\n        this.opacityRate = 1;\n        this.sizeRate = 1;\n        this.velocityRate = 1;\n    }\n    load(data) {\n        super.load(data);\n        if (!data) {\n            return;\n        }\n        if (data.opacityRate !== undefined) {\n            this.opacityRate = data.opacityRate;\n        }\n        if (data.sizeRate !== undefined) {\n            this.sizeRate = data.sizeRate;\n        }\n        if (data.velocityRate !== undefined) {\n            this.velocityRate = data.velocityRate;\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,0BAA0B;AAC1D,OAAO,MAAMC,MAAM,SAASD,eAAe,CAAC;EACxCE,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,WAAW,GAAG,CAAC;IACpB,IAAI,CAACC,QAAQ,GAAG,CAAC;IACjB,IAAI,CAACC,YAAY,GAAG,CAAC;EACzB;EACAC,IAAIA,CAACC,IAAI,EAAE;IACP,KAAK,CAACD,IAAI,CAACC,IAAI,CAAC;IAChB,IAAI,CAACA,IAAI,EAAE;MACP;IACJ;IACA,IAAIA,IAAI,CAACJ,WAAW,KAAKK,SAAS,EAAE;MAChC,IAAI,CAACL,WAAW,GAAGI,IAAI,CAACJ,WAAW;IACvC;IACA,IAAII,IAAI,CAACH,QAAQ,KAAKI,SAAS,EAAE;MAC7B,IAAI,CAACJ,QAAQ,GAAGG,IAAI,CAACH,QAAQ;IACjC;IACA,IAAIG,IAAI,CAACF,YAAY,KAAKG,SAAS,EAAE;MACjC,IAAI,CAACH,YAAY,GAAGE,IAAI,CAACF,YAAY;IACzC;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
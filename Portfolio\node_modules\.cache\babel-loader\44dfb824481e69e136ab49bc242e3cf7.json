{"ast": null, "code": "export class FrameManager {\n  constructor(container) {\n    this.container = container;\n  }\n\n  async nextFrame(timestamp) {\n    var _a;\n\n    try {\n      const container = this.container;\n\n      if (container.lastFrameTime !== undefined && timestamp < container.lastFrameTime + 1000 / container.fpsLimit) {\n        container.draw(false);\n        return;\n      }\n\n      (_a = container.lastFrameTime) !== null && _a !== void 0 ? _a : container.lastFrameTime = timestamp;\n      const deltaValue = timestamp - container.lastFrameTime;\n      const delta = {\n        value: deltaValue,\n        factor: 60 * deltaValue / 1000\n      };\n      container.lifeTime += delta.value;\n      container.lastFrameTime = timestamp;\n\n      if (deltaValue > 1000) {\n        container.draw(false);\n        return;\n      }\n\n      await container.particles.draw(delta);\n\n      if (container.duration > 0 && container.lifeTime > container.duration) {\n        container.destroy();\n        return;\n      }\n\n      if (container.getAnimationStatus()) {\n        container.draw(false);\n      }\n    } catch (e) {\n      console.error(\"tsParticles error in animation loop\", e);\n    }\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Core/Utils/FrameManager.js"], "names": ["FrameManager", "constructor", "container", "next<PERSON><PERSON><PERSON>", "timestamp", "_a", "lastFrameTime", "undefined", "fpsLimit", "draw", "deltaValue", "delta", "value", "factor", "lifeTime", "particles", "duration", "destroy", "getAnimationStatus", "e", "console", "error"], "mappings": "AAAA,OAAO,MAAMA,YAAN,CAAmB;AACtBC,EAAAA,WAAW,CAACC,SAAD,EAAY;AACnB,SAAKA,SAAL,GAAiBA,SAAjB;AACH;;AACc,QAATC,SAAS,CAACC,SAAD,EAAY;AACvB,QAAIC,EAAJ;;AACA,QAAI;AACA,YAAMH,SAAS,GAAG,KAAKA,SAAvB;;AACA,UAAIA,SAAS,CAACI,aAAV,KAA4BC,SAA5B,IACAH,SAAS,GAAGF,SAAS,CAACI,aAAV,GAA0B,OAAOJ,SAAS,CAACM,QAD3D,EACqE;AACjEN,QAAAA,SAAS,CAACO,IAAV,CAAe,KAAf;AACA;AACH;;AACD,OAACJ,EAAE,GAAGH,SAAS,CAACI,aAAhB,MAAmC,IAAnC,IAA2CD,EAAE,KAAK,KAAK,CAAvD,GAA2DA,EAA3D,GAAiEH,SAAS,CAACI,aAAV,GAA0BF,SAA3F;AACA,YAAMM,UAAU,GAAGN,SAAS,GAAGF,SAAS,CAACI,aAAzC;AACA,YAAMK,KAAK,GAAG;AACVC,QAAAA,KAAK,EAAEF,UADG;AAEVG,QAAAA,MAAM,EAAG,KAAKH,UAAN,GAAoB;AAFlB,OAAd;AAIAR,MAAAA,SAAS,CAACY,QAAV,IAAsBH,KAAK,CAACC,KAA5B;AACAV,MAAAA,SAAS,CAACI,aAAV,GAA0BF,SAA1B;;AACA,UAAIM,UAAU,GAAG,IAAjB,EAAuB;AACnBR,QAAAA,SAAS,CAACO,IAAV,CAAe,KAAf;AACA;AACH;;AACD,YAAMP,SAAS,CAACa,SAAV,CAAoBN,IAApB,CAAyBE,KAAzB,CAAN;;AACA,UAAIT,SAAS,CAACc,QAAV,GAAqB,CAArB,IAA0Bd,SAAS,CAACY,QAAV,GAAqBZ,SAAS,CAACc,QAA7D,EAAuE;AACnEd,QAAAA,SAAS,CAACe,OAAV;AACA;AACH;;AACD,UAAIf,SAAS,CAACgB,kBAAV,EAAJ,EAAoC;AAChChB,QAAAA,SAAS,CAACO,IAAV,CAAe,KAAf;AACH;AACJ,KA3BD,CA4BA,OAAOU,CAAP,EAAU;AACNC,MAAAA,OAAO,CAACC,KAAR,CAAc,qCAAd,EAAqDF,CAArD;AACH;AACJ;;AArCqB", "sourcesContent": ["export class FrameManager {\n    constructor(container) {\n        this.container = container;\n    }\n    async nextFrame(timestamp) {\n        var _a;\n        try {\n            const container = this.container;\n            if (container.lastFrameTime !== undefined &&\n                timestamp < container.lastFrameTime + 1000 / container.fpsLimit) {\n                container.draw(false);\n                return;\n            }\n            (_a = container.lastFrameTime) !== null && _a !== void 0 ? _a : (container.lastFrameTime = timestamp);\n            const deltaValue = timestamp - container.lastFrameTime;\n            const delta = {\n                value: deltaValue,\n                factor: (60 * deltaValue) / 1000,\n            };\n            container.lifeTime += delta.value;\n            container.lastFrameTime = timestamp;\n            if (deltaValue > 1000) {\n                container.draw(false);\n                return;\n            }\n            await container.particles.draw(delta);\n            if (container.duration > 0 && container.lifeTime > container.duration) {\n                container.destroy();\n                return;\n            }\n            if (container.getAnimationStatus()) {\n                container.draw(false);\n            }\n        }\n        catch (e) {\n            console.error(\"tsParticles error in animation loop\", e);\n        }\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
{"ast": null, "code": "import { Grabber } from \"./Grabber.js\";\nexport async function loadExternalGrabInteraction(engine, refresh = true) {\n  await engine.addInteractor(\"externalGrab\", container => {\n    return Promise.resolve(new Grabber(container));\n  }, refresh);\n}\nexport * from \"./Options/Classes/Grab.js\";\nexport * from \"./Options/Classes/GrabLinks.js\";\nexport * from \"./Options/Interfaces/IGrab.js\";\nexport * from \"./Options/Interfaces/IGrabLinks.js\";", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "loadExternalGrabInteraction", "engine", "refresh", "addInteractor", "container", "Promise", "resolve"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/interaction-external-grab/browser/index.js"], "sourcesContent": ["import { Grabber } from \"./Grabber.js\";\nexport async function loadExternalGrabInteraction(engine, refresh = true) {\n    await engine.addInteractor(\"externalGrab\", container => {\n        return Promise.resolve(new Grabber(container));\n    }, refresh);\n}\nexport * from \"./Options/Classes/Grab.js\";\nexport * from \"./Options/Classes/GrabLinks.js\";\nexport * from \"./Options/Interfaces/IGrab.js\";\nexport * from \"./Options/Interfaces/IGrabLinks.js\";\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc;AACtC,OAAO,eAAeC,2BAA2BA,CAACC,MAAM,EAAEC,OAAO,GAAG,IAAI,EAAE;EACtE,MAAMD,MAAM,CAACE,aAAa,CAAC,cAAc,EAAEC,SAAS,IAAI;IACpD,OAAOC,OAAO,CAACC,OAAO,CAAC,IAAIP,OAAO,CAACK,SAAS,CAAC,CAAC;EAClD,CAAC,EAAEF,OAAO,CAAC;AACf;AACA,cAAc,2BAA2B;AACzC,cAAc,gCAAgC;AAC9C,cAAc,+BAA+B;AAC7C,cAAc,oCAAoC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { deepExtend, executeOnSingleOrMultiple } from \"../../../Utils/Utils.js\";\nimport { AnimatableColor } from \"../AnimatableColor.js\";\nimport { Collisions } from \"./Collisions/Collisions.js\";\nimport { Effect } from \"./Effect/Effect.js\";\nimport { Move } from \"./Move/Move.js\";\nimport { Opacity } from \"./Opacity/Opacity.js\";\nimport { ParticlesBounce } from \"./Bounce/ParticlesBounce.js\";\nimport { ParticlesNumber } from \"./Number/ParticlesNumber.js\";\nimport { Shadow } from \"./Shadow.js\";\nimport { Shape } from \"./Shape/Shape.js\";\nimport { Size } from \"./Size/Size.js\";\nimport { Stroke } from \"./Stroke.js\";\nimport { ZIndex } from \"./ZIndex/ZIndex.js\";\nexport class ParticlesOptions {\n  constructor(engine, container) {\n    this._engine = engine;\n    this._container = container;\n    this.bounce = new ParticlesBounce();\n    this.collisions = new Collisions();\n    this.color = new AnimatableColor();\n    this.color.value = \"#fff\";\n    this.effect = new Effect();\n    this.groups = {};\n    this.move = new Move();\n    this.number = new ParticlesNumber();\n    this.opacity = new Opacity();\n    this.reduceDuplicates = false;\n    this.shadow = new Shadow();\n    this.shape = new Shape();\n    this.size = new Size();\n    this.stroke = new Stroke();\n    this.zIndex = new ZIndex();\n  }\n  load(data) {\n    if (!data) {\n      return;\n    }\n    if (data.groups !== undefined) {\n      for (const group of Object.keys(data.groups)) {\n        if (!Object.hasOwn(data.groups, group)) {\n          continue;\n        }\n        const item = data.groups[group];\n        if (item !== undefined) {\n          this.groups[group] = deepExtend(this.groups[group] ?? {}, item);\n        }\n      }\n    }\n    if (data.reduceDuplicates !== undefined) {\n      this.reduceDuplicates = data.reduceDuplicates;\n    }\n    this.bounce.load(data.bounce);\n    this.color.load(AnimatableColor.create(this.color, data.color));\n    this.effect.load(data.effect);\n    this.move.load(data.move);\n    this.number.load(data.number);\n    this.opacity.load(data.opacity);\n    this.shape.load(data.shape);\n    this.size.load(data.size);\n    this.shadow.load(data.shadow);\n    this.zIndex.load(data.zIndex);\n    this.collisions.load(data.collisions);\n    if (data.interactivity !== undefined) {\n      this.interactivity = deepExtend({}, data.interactivity);\n    }\n    const strokeToLoad = data.stroke;\n    if (strokeToLoad) {\n      this.stroke = executeOnSingleOrMultiple(strokeToLoad, t => {\n        const tmp = new Stroke();\n        tmp.load(t);\n        return tmp;\n      });\n    }\n    if (this._container) {\n      const updaters = this._engine.updaters.get(this._container);\n      if (updaters) {\n        for (const updater of updaters) {\n          if (updater.loadOptions) {\n            updater.loadOptions(this, data);\n          }\n        }\n      }\n      const interactors = this._engine.interactors.get(this._container);\n      if (interactors) {\n        for (const interactor of interactors) {\n          if (interactor.loadParticlesOptions) {\n            interactor.loadParticlesOptions(this, data);\n          }\n        }\n      }\n    }\n  }\n}", "map": {"version": 3, "names": ["deepExtend", "executeOnSingleOrMultiple", "AnimatableColor", "Collisions", "Effect", "Move", "Opacity", "ParticlesBounce", "ParticlesNumber", "Shadow", "<PERSON><PERSON><PERSON>", "Size", "Stroke", "ZIndex", "ParticlesOptions", "constructor", "engine", "container", "_engine", "_container", "bounce", "collisions", "color", "value", "effect", "groups", "move", "number", "opacity", "reduceDuplicates", "shadow", "shape", "size", "stroke", "zIndex", "load", "data", "undefined", "group", "Object", "keys", "hasOwn", "item", "create", "interactivity", "strokeToLoad", "t", "tmp", "updaters", "get", "updater", "loadOptions", "interactors", "interactor", "loadParticlesOptions"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/engine/browser/Options/Classes/Particles/ParticlesOptions.js"], "sourcesContent": ["import { deepExtend, executeOnSingleOrMultiple } from \"../../../Utils/Utils.js\";\nimport { AnimatableColor } from \"../AnimatableColor.js\";\nimport { Collisions } from \"./Collisions/Collisions.js\";\nimport { Effect } from \"./Effect/Effect.js\";\nimport { Move } from \"./Move/Move.js\";\nimport { Opacity } from \"./Opacity/Opacity.js\";\nimport { ParticlesBounce } from \"./Bounce/ParticlesBounce.js\";\nimport { ParticlesNumber } from \"./Number/ParticlesNumber.js\";\nimport { Shadow } from \"./Shadow.js\";\nimport { Shape } from \"./Shape/Shape.js\";\nimport { Size } from \"./Size/Size.js\";\nimport { Stroke } from \"./Stroke.js\";\nimport { ZIndex } from \"./ZIndex/ZIndex.js\";\nexport class ParticlesOptions {\n    constructor(engine, container) {\n        this._engine = engine;\n        this._container = container;\n        this.bounce = new ParticlesBounce();\n        this.collisions = new Collisions();\n        this.color = new AnimatableColor();\n        this.color.value = \"#fff\";\n        this.effect = new Effect();\n        this.groups = {};\n        this.move = new Move();\n        this.number = new ParticlesNumber();\n        this.opacity = new Opacity();\n        this.reduceDuplicates = false;\n        this.shadow = new Shadow();\n        this.shape = new Shape();\n        this.size = new Size();\n        this.stroke = new Stroke();\n        this.zIndex = new ZIndex();\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.groups !== undefined) {\n            for (const group of Object.keys(data.groups)) {\n                if (!Object.hasOwn(data.groups, group)) {\n                    continue;\n                }\n                const item = data.groups[group];\n                if (item !== undefined) {\n                    this.groups[group] = deepExtend(this.groups[group] ?? {}, item);\n                }\n            }\n        }\n        if (data.reduceDuplicates !== undefined) {\n            this.reduceDuplicates = data.reduceDuplicates;\n        }\n        this.bounce.load(data.bounce);\n        this.color.load(AnimatableColor.create(this.color, data.color));\n        this.effect.load(data.effect);\n        this.move.load(data.move);\n        this.number.load(data.number);\n        this.opacity.load(data.opacity);\n        this.shape.load(data.shape);\n        this.size.load(data.size);\n        this.shadow.load(data.shadow);\n        this.zIndex.load(data.zIndex);\n        this.collisions.load(data.collisions);\n        if (data.interactivity !== undefined) {\n            this.interactivity = deepExtend({}, data.interactivity);\n        }\n        const strokeToLoad = data.stroke;\n        if (strokeToLoad) {\n            this.stroke = executeOnSingleOrMultiple(strokeToLoad, t => {\n                const tmp = new Stroke();\n                tmp.load(t);\n                return tmp;\n            });\n        }\n        if (this._container) {\n            const updaters = this._engine.updaters.get(this._container);\n            if (updaters) {\n                for (const updater of updaters) {\n                    if (updater.loadOptions) {\n                        updater.loadOptions(this, data);\n                    }\n                }\n            }\n            const interactors = this._engine.interactors.get(this._container);\n            if (interactors) {\n                for (const interactor of interactors) {\n                    if (interactor.loadParticlesOptions) {\n                        interactor.loadParticlesOptions(this, data);\n                    }\n                }\n            }\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,UAAU,EAAEC,yBAAyB,QAAQ,yBAAyB;AAC/E,SAASC,eAAe,QAAQ,uBAAuB;AACvD,SAASC,UAAU,QAAQ,4BAA4B;AACvD,SAASC,MAAM,QAAQ,oBAAoB;AAC3C,SAASC,IAAI,QAAQ,gBAAgB;AACrC,SAASC,OAAO,QAAQ,sBAAsB;AAC9C,SAASC,eAAe,QAAQ,6BAA6B;AAC7D,SAASC,eAAe,QAAQ,6BAA6B;AAC7D,SAASC,MAAM,QAAQ,aAAa;AACpC,SAASC,KAAK,QAAQ,kBAAkB;AACxC,SAASC,IAAI,QAAQ,gBAAgB;AACrC,SAASC,MAAM,QAAQ,aAAa;AACpC,SAASC,MAAM,QAAQ,oBAAoB;AAC3C,OAAO,MAAMC,gBAAgB,CAAC;EAC1BC,WAAWA,CAACC,MAAM,EAAEC,SAAS,EAAE;IAC3B,IAAI,CAACC,OAAO,GAAGF,MAAM;IACrB,IAAI,CAACG,UAAU,GAAGF,SAAS;IAC3B,IAAI,CAACG,MAAM,GAAG,IAAIb,eAAe,CAAC,CAAC;IACnC,IAAI,CAACc,UAAU,GAAG,IAAIlB,UAAU,CAAC,CAAC;IAClC,IAAI,CAACmB,KAAK,GAAG,IAAIpB,eAAe,CAAC,CAAC;IAClC,IAAI,CAACoB,KAAK,CAACC,KAAK,GAAG,MAAM;IACzB,IAAI,CAACC,MAAM,GAAG,IAAIpB,MAAM,CAAC,CAAC;IAC1B,IAAI,CAACqB,MAAM,GAAG,CAAC,CAAC;IAChB,IAAI,CAACC,IAAI,GAAG,IAAIrB,IAAI,CAAC,CAAC;IACtB,IAAI,CAACsB,MAAM,GAAG,IAAInB,eAAe,CAAC,CAAC;IACnC,IAAI,CAACoB,OAAO,GAAG,IAAItB,OAAO,CAAC,CAAC;IAC5B,IAAI,CAACuB,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACC,MAAM,GAAG,IAAIrB,MAAM,CAAC,CAAC;IAC1B,IAAI,CAACsB,KAAK,GAAG,IAAIrB,KAAK,CAAC,CAAC;IACxB,IAAI,CAACsB,IAAI,GAAG,IAAIrB,IAAI,CAAC,CAAC;IACtB,IAAI,CAACsB,MAAM,GAAG,IAAIrB,MAAM,CAAC,CAAC;IAC1B,IAAI,CAACsB,MAAM,GAAG,IAAIrB,MAAM,CAAC,CAAC;EAC9B;EACAsB,IAAIA,CAACC,IAAI,EAAE;IACP,IAAI,CAACA,IAAI,EAAE;MACP;IACJ;IACA,IAAIA,IAAI,CAACX,MAAM,KAAKY,SAAS,EAAE;MAC3B,KAAK,MAAMC,KAAK,IAAIC,MAAM,CAACC,IAAI,CAACJ,IAAI,CAACX,MAAM,CAAC,EAAE;QAC1C,IAAI,CAACc,MAAM,CAACE,MAAM,CAACL,IAAI,CAACX,MAAM,EAAEa,KAAK,CAAC,EAAE;UACpC;QACJ;QACA,MAAMI,IAAI,GAAGN,IAAI,CAACX,MAAM,CAACa,KAAK,CAAC;QAC/B,IAAII,IAAI,KAAKL,SAAS,EAAE;UACpB,IAAI,CAACZ,MAAM,CAACa,KAAK,CAAC,GAAGtC,UAAU,CAAC,IAAI,CAACyB,MAAM,CAACa,KAAK,CAAC,IAAI,CAAC,CAAC,EAAEI,IAAI,CAAC;QACnE;MACJ;IACJ;IACA,IAAIN,IAAI,CAACP,gBAAgB,KAAKQ,SAAS,EAAE;MACrC,IAAI,CAACR,gBAAgB,GAAGO,IAAI,CAACP,gBAAgB;IACjD;IACA,IAAI,CAACT,MAAM,CAACe,IAAI,CAACC,IAAI,CAAChB,MAAM,CAAC;IAC7B,IAAI,CAACE,KAAK,CAACa,IAAI,CAACjC,eAAe,CAACyC,MAAM,CAAC,IAAI,CAACrB,KAAK,EAAEc,IAAI,CAACd,KAAK,CAAC,CAAC;IAC/D,IAAI,CAACE,MAAM,CAACW,IAAI,CAACC,IAAI,CAACZ,MAAM,CAAC;IAC7B,IAAI,CAACE,IAAI,CAACS,IAAI,CAACC,IAAI,CAACV,IAAI,CAAC;IACzB,IAAI,CAACC,MAAM,CAACQ,IAAI,CAACC,IAAI,CAACT,MAAM,CAAC;IAC7B,IAAI,CAACC,OAAO,CAACO,IAAI,CAACC,IAAI,CAACR,OAAO,CAAC;IAC/B,IAAI,CAACG,KAAK,CAACI,IAAI,CAACC,IAAI,CAACL,KAAK,CAAC;IAC3B,IAAI,CAACC,IAAI,CAACG,IAAI,CAACC,IAAI,CAACJ,IAAI,CAAC;IACzB,IAAI,CAACF,MAAM,CAACK,IAAI,CAACC,IAAI,CAACN,MAAM,CAAC;IAC7B,IAAI,CAACI,MAAM,CAACC,IAAI,CAACC,IAAI,CAACF,MAAM,CAAC;IAC7B,IAAI,CAACb,UAAU,CAACc,IAAI,CAACC,IAAI,CAACf,UAAU,CAAC;IACrC,IAAIe,IAAI,CAACQ,aAAa,KAAKP,SAAS,EAAE;MAClC,IAAI,CAACO,aAAa,GAAG5C,UAAU,CAAC,CAAC,CAAC,EAAEoC,IAAI,CAACQ,aAAa,CAAC;IAC3D;IACA,MAAMC,YAAY,GAAGT,IAAI,CAACH,MAAM;IAChC,IAAIY,YAAY,EAAE;MACd,IAAI,CAACZ,MAAM,GAAGhC,yBAAyB,CAAC4C,YAAY,EAAEC,CAAC,IAAI;QACvD,MAAMC,GAAG,GAAG,IAAInC,MAAM,CAAC,CAAC;QACxBmC,GAAG,CAACZ,IAAI,CAACW,CAAC,CAAC;QACX,OAAOC,GAAG;MACd,CAAC,CAAC;IACN;IACA,IAAI,IAAI,CAAC5B,UAAU,EAAE;MACjB,MAAM6B,QAAQ,GAAG,IAAI,CAAC9B,OAAO,CAAC8B,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAC9B,UAAU,CAAC;MAC3D,IAAI6B,QAAQ,EAAE;QACV,KAAK,MAAME,OAAO,IAAIF,QAAQ,EAAE;UAC5B,IAAIE,OAAO,CAACC,WAAW,EAAE;YACrBD,OAAO,CAACC,WAAW,CAAC,IAAI,EAAEf,IAAI,CAAC;UACnC;QACJ;MACJ;MACA,MAAMgB,WAAW,GAAG,IAAI,CAAClC,OAAO,CAACkC,WAAW,CAACH,GAAG,CAAC,IAAI,CAAC9B,UAAU,CAAC;MACjE,IAAIiC,WAAW,EAAE;QACb,KAAK,MAAMC,UAAU,IAAID,WAAW,EAAE;UAClC,IAAIC,UAAU,CAACC,oBAAoB,EAAE;YACjCD,UAAU,CAACC,oBAAoB,CAAC,IAAI,EAAElB,IAAI,CAAC;UAC/C;QACJ;MACJ;IACJ;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
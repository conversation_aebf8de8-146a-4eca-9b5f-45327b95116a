{"ast": null, "code": "import { OptionsColor } from \"../OptionsColor.js\";\nexport class BackgroundMaskCover {\n  constructor() {\n    this.opacity = 1;\n  }\n  load(data) {\n    if (!data) {\n      return;\n    }\n    if (data.color !== undefined) {\n      this.color = OptionsColor.create(this.color, data.color);\n    }\n    if (data.image !== undefined) {\n      this.image = data.image;\n    }\n    if (data.opacity !== undefined) {\n      this.opacity = data.opacity;\n    }\n  }\n}", "map": {"version": 3, "names": ["OptionsColor", "BackgroundMaskCover", "constructor", "opacity", "load", "data", "color", "undefined", "create", "image"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/engine/browser/Options/Classes/BackgroundMask/BackgroundMaskCover.js"], "sourcesContent": ["import { OptionsColor } from \"../OptionsColor.js\";\nexport class BackgroundMaskCover {\n    constructor() {\n        this.opacity = 1;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.color !== undefined) {\n            this.color = OptionsColor.create(this.color, data.color);\n        }\n        if (data.image !== undefined) {\n            this.image = data.image;\n        }\n        if (data.opacity !== undefined) {\n            this.opacity = data.opacity;\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,oBAAoB;AACjD,OAAO,MAAMC,mBAAmB,CAAC;EAC7BC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,OAAO,GAAG,CAAC;EACpB;EACAC,IAAIA,CAACC,IAAI,EAAE;IACP,IAAI,CAACA,IAAI,EAAE;MACP;IACJ;IACA,IAAIA,IAAI,CAACC,KAAK,KAAKC,SAAS,EAAE;MAC1B,IAAI,CAACD,KAAK,GAAGN,YAAY,CAACQ,MAAM,CAAC,IAAI,CAACF,KAAK,EAAED,IAAI,CAACC,KAAK,CAAC;IAC5D;IACA,IAAID,IAAI,CAACI,KAAK,KAAKF,SAAS,EAAE;MAC1B,IAAI,CAACE,KAAK,GAAGJ,IAAI,CAACI,KAAK;IAC3B;IACA,IAAIJ,IAAI,CAACF,OAAO,KAAKI,SAAS,EAAE;MAC5B,IAAI,CAACJ,OAAO,GAAGE,IAAI,CAACF,OAAO;IAC/B;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { setRangeValue } from \"tsparticles-engine\";\nexport class RotateAnimation {\n  constructor() {\n    this.enable = false;\n    this.speed = 0;\n    this.decay = 0;\n    this.sync = false;\n  }\n  load(data) {\n    if (!data) {\n      return;\n    }\n    if (data.enable !== undefined) {\n      this.enable = data.enable;\n    }\n    if (data.speed !== undefined) {\n      this.speed = setRangeValue(data.speed);\n    }\n    if (data.decay !== undefined) {\n      this.decay = setRangeValue(data.decay);\n    }\n    if (data.sync !== undefined) {\n      this.sync = data.sync;\n    }\n  }\n}", "map": {"version": 3, "names": ["setRangeValue", "RotateAnimation", "constructor", "enable", "speed", "decay", "sync", "load", "data", "undefined"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/tsparticles-updater-rotate/esm/Options/Classes/RotateAnimation.js"], "sourcesContent": ["import { setRangeValue } from \"tsparticles-engine\";\nexport class RotateAnimation {\n    constructor() {\n        this.enable = false;\n        this.speed = 0;\n        this.decay = 0;\n        this.sync = false;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.enable !== undefined) {\n            this.enable = data.enable;\n        }\n        if (data.speed !== undefined) {\n            this.speed = setRangeValue(data.speed);\n        }\n        if (data.decay !== undefined) {\n            this.decay = setRangeValue(data.decay);\n        }\n        if (data.sync !== undefined) {\n            this.sync = data.sync;\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,oBAAoB;AAClD,OAAO,MAAMC,eAAe,CAAC;EACzBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,MAAM,GAAG,KAAK;IACnB,IAAI,CAACC,KAAK,GAAG,CAAC;IACd,IAAI,CAACC,KAAK,GAAG,CAAC;IACd,IAAI,CAACC,IAAI,GAAG,KAAK;EACrB;EACAC,IAAIA,CAACC,IAAI,EAAE;IACP,IAAI,CAACA,IAAI,EAAE;MACP;IACJ;IACA,IAAIA,IAAI,CAACL,MAAM,KAAKM,SAAS,EAAE;MAC3B,IAAI,CAACN,MAAM,GAAGK,IAAI,CAACL,MAAM;IAC7B;IACA,IAAIK,IAAI,CAACJ,KAAK,KAAKK,SAAS,EAAE;MAC1B,IAAI,CAACL,KAAK,GAAGJ,aAAa,CAACQ,IAAI,CAACJ,KAAK,CAAC;IAC1C;IACA,IAAII,IAAI,CAACH,KAAK,KAAKI,SAAS,EAAE;MAC1B,IAAI,CAACJ,KAAK,GAAGL,aAAa,CAACQ,IAAI,CAACH,KAAK,CAAC;IAC1C;IACA,IAAIG,IAAI,CAACF,IAAI,KAAKG,SAAS,EAAE;MACzB,IAAI,CAACH,IAAI,GAAGE,IAAI,CAACF,IAAI;IACzB;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
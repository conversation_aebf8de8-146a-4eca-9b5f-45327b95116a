{"ast": null, "code": "import { PolygonDrawerBase } from \"./PolygonDrawerBase\";\nexport class PolygonDrawer extends PolygonDrawerBase {\n  getSidesData(particle, radius) {\n    var _a, _b;\n\n    const polygon = particle.shapeData;\n    const sides = (_b = (_a = polygon === null || polygon === void 0 ? void 0 : polygon.sides) !== null && _a !== void 0 ? _a : polygon === null || polygon === void 0 ? void 0 : polygon.nb_sides) !== null && _b !== void 0 ? _b : 5;\n    return {\n      count: {\n        denominator: 1,\n        numerator: sides\n      },\n      length: radius * 2.66 / (sides / 3)\n    };\n  }\n\n  getCenter(particle, radius) {\n    const sides = this.getSidesCount(particle);\n    return {\n      x: -radius / (sides / 3.5),\n      y: -radius / (2.66 / 3.5)\n    };\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Shapes/Polygon/PolygonDrawer.js"], "names": ["PolygonDrawerBase", "PolygonDrawer", "getSidesData", "particle", "radius", "_a", "_b", "polygon", "shapeData", "sides", "nb_sides", "count", "denominator", "numerator", "length", "getCenter", "getSidesCount", "x", "y"], "mappings": "AAAA,SAASA,iBAAT,QAAkC,qBAAlC;AACA,OAAO,MAAMC,aAAN,SAA4BD,iBAA5B,CAA8C;AACjDE,EAAAA,YAAY,CAACC,QAAD,EAAWC,MAAX,EAAmB;AAC3B,QAAIC,EAAJ,EAAQC,EAAR;;AACA,UAAMC,OAAO,GAAGJ,QAAQ,CAACK,SAAzB;AACA,UAAMC,KAAK,GAAG,CAACH,EAAE,GAAG,CAACD,EAAE,GAAGE,OAAO,KAAK,IAAZ,IAAoBA,OAAO,KAAK,KAAK,CAArC,GAAyC,KAAK,CAA9C,GAAkDA,OAAO,CAACE,KAAhE,MAA2E,IAA3E,IAAmFJ,EAAE,KAAK,KAAK,CAA/F,GAAmGA,EAAnG,GAAwGE,OAAO,KAAK,IAAZ,IAAoBA,OAAO,KAAK,KAAK,CAArC,GAAyC,KAAK,CAA9C,GAAkDA,OAAO,CAACG,QAAxK,MAAsL,IAAtL,IAA8LJ,EAAE,KAAK,KAAK,CAA1M,GAA8MA,EAA9M,GAAmN,CAAjO;AACA,WAAO;AACHK,MAAAA,KAAK,EAAE;AACHC,QAAAA,WAAW,EAAE,CADV;AAEHC,QAAAA,SAAS,EAAEJ;AAFR,OADJ;AAKHK,MAAAA,MAAM,EAAGV,MAAM,GAAG,IAAV,IAAmBK,KAAK,GAAG,CAA3B;AALL,KAAP;AAOH;;AACDM,EAAAA,SAAS,CAACZ,QAAD,EAAWC,MAAX,EAAmB;AACxB,UAAMK,KAAK,GAAG,KAAKO,aAAL,CAAmBb,QAAnB,CAAd;AACA,WAAO;AACHc,MAAAA,CAAC,EAAE,CAACb,MAAD,IAAWK,KAAK,GAAG,GAAnB,CADA;AAEHS,MAAAA,CAAC,EAAE,CAACd,MAAD,IAAW,OAAO,GAAlB;AAFA,KAAP;AAIH;;AAnBgD", "sourcesContent": ["import { PolygonDrawerBase } from \"./PolygonDrawerBase\";\nexport class PolygonDrawer extends PolygonDrawerBase {\n    getSidesData(particle, radius) {\n        var _a, _b;\n        const polygon = particle.shapeData;\n        const sides = (_b = (_a = polygon === null || polygon === void 0 ? void 0 : polygon.sides) !== null && _a !== void 0 ? _a : polygon === null || polygon === void 0 ? void 0 : polygon.nb_sides) !== null && _b !== void 0 ? _b : 5;\n        return {\n            count: {\n                denominator: 1,\n                numerator: sides,\n            },\n            length: (radius * 2.66) / (sides / 3),\n        };\n    }\n    getCenter(particle, radius) {\n        const sides = this.getSidesCount(particle);\n        return {\n            x: -radius / (sides / 3.5),\n            y: -radius / (2.66 / 3.5),\n        };\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
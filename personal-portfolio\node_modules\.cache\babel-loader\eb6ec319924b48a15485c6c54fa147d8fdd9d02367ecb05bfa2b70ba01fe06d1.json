{"ast": null, "code": "export class CollisionsAbsorb {\n  constructor() {\n    this.speed = 2;\n  }\n  load(data) {\n    if (!data) {\n      return;\n    }\n    if (data.speed !== undefined) {\n      this.speed = data.speed;\n    }\n  }\n}", "map": {"version": 3, "names": ["CollisionsAbsorb", "constructor", "speed", "load", "data", "undefined"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/engine/browser/Options/Classes/Particles/Collisions/CollisionsAbsorb.js"], "sourcesContent": ["export class CollisionsAbsorb {\n    constructor() {\n        this.speed = 2;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.speed !== undefined) {\n            this.speed = data.speed;\n        }\n    }\n}\n"], "mappings": "AAAA,OAAO,MAAMA,gBAAgB,CAAC;EAC1BC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,KAAK,GAAG,CAAC;EAClB;EACAC,IAAIA,CAACC,IAAI,EAAE;IACP,IAAI,CAACA,IAAI,EAAE;MACP;IACJ;IACA,IAAIA,IAAI,CAACF,KAAK,KAAKG,SAAS,EAAE;MAC1B,IAAI,CAACH,KAAK,GAAGE,IAAI,CAACF,KAAK;IAC3B;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
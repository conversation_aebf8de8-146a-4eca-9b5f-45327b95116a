{"ast": null, "code": "import { AbsorberSizeLimit } from \"./AbsorberSizeLimit\";\nimport { ValueWithRandom } from \"../../../../Options/Classes/ValueWithRandom\";\nexport class AbsorberSize extends ValueWithRandom {\n  constructor() {\n    super();\n    this.density = 5;\n    this.random.minimumValue = 1;\n    this.value = 50;\n    this.limit = new AbsorberSizeLimit();\n  }\n\n  load(data) {\n    if (!data) {\n      return;\n    }\n\n    super.load(data);\n\n    if (data.density !== undefined) {\n      this.density = data.density;\n    }\n\n    if (typeof data.limit === \"number\") {\n      this.limit.radius = data.limit;\n    } else {\n      this.limit.load(data.limit);\n    }\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Plugins/Absorbers/Options/Classes/AbsorberSize.js"], "names": ["AbsorberSizeLimit", "ValueWithRandom", "AbsorberSize", "constructor", "density", "random", "minimumValue", "value", "limit", "load", "data", "undefined", "radius"], "mappings": "AAAA,SAASA,iBAAT,QAAkC,qBAAlC;AACA,SAASC,eAAT,QAAgC,6CAAhC;AACA,OAAO,MAAMC,YAAN,SAA2BD,eAA3B,CAA2C;AAC9CE,EAAAA,WAAW,GAAG;AACV;AACA,SAAKC,OAAL,GAAe,CAAf;AACA,SAAKC,MAAL,CAAYC,YAAZ,GAA2B,CAA3B;AACA,SAAKC,KAAL,GAAa,EAAb;AACA,SAAKC,KAAL,GAAa,IAAIR,iBAAJ,EAAb;AACH;;AACDS,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,QAAI,CAACA,IAAL,EAAW;AACP;AACH;;AACD,UAAMD,IAAN,CAAWC,IAAX;;AACA,QAAIA,IAAI,CAACN,OAAL,KAAiBO,SAArB,EAAgC;AAC5B,WAAKP,OAAL,GAAeM,IAAI,CAACN,OAApB;AACH;;AACD,QAAI,OAAOM,IAAI,CAACF,KAAZ,KAAsB,QAA1B,EAAoC;AAChC,WAAKA,KAAL,CAAWI,MAAX,GAAoBF,IAAI,CAACF,KAAzB;AACH,KAFD,MAGK;AACD,WAAKA,KAAL,CAAWC,IAAX,CAAgBC,IAAI,CAACF,KAArB;AACH;AACJ;;AAtB6C", "sourcesContent": ["import { AbsorberSizeLimit } from \"./AbsorberSizeLimit\";\nimport { ValueWithRandom } from \"../../../../Options/Classes/ValueWithRandom\";\nexport class AbsorberSize extends ValueWithRandom {\n    constructor() {\n        super();\n        this.density = 5;\n        this.random.minimumValue = 1;\n        this.value = 50;\n        this.limit = new AbsorberSizeLimit();\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        super.load(data);\n        if (data.density !== undefined) {\n            this.density = data.density;\n        }\n        if (typeof data.limit === \"number\") {\n            this.limit.radius = data.limit;\n        }\n        else {\n            this.limit.load(data.limit);\n        }\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
{"ast": null, "code": "export var ThemeMode;\n(function (ThemeMode) {\n  ThemeMode[\"any\"] = \"any\";\n  ThemeMode[\"dark\"] = \"dark\";\n  ThemeMode[\"light\"] = \"light\";\n})(ThemeMode || (ThemeMode = {}));", "map": {"version": 3, "names": ["ThemeMode"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/engine/browser/Enums/Modes/ThemeMode.js"], "sourcesContent": ["export var ThemeMode;\n(function (ThemeMode) {\n    ThemeMode[\"any\"] = \"any\";\n    ThemeMode[\"dark\"] = \"dark\";\n    ThemeMode[\"light\"] = \"light\";\n})(ThemeMode || (ThemeMode = {}));\n"], "mappings": "AAAA,OAAO,IAAIA,SAAS;AACpB,CAAC,UAAUA,SAAS,EAAE;EAClBA,SAAS,CAAC,KAAK,CAAC,GAAG,KAAK;EACxBA,SAAS,CAAC,MAAM,CAAC,GAAG,MAAM;EAC1BA,SAAS,CAAC,OAAO,CAAC,GAAG,OAAO;AAChC,CAAC,EAAEA,SAAS,KAAKA,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
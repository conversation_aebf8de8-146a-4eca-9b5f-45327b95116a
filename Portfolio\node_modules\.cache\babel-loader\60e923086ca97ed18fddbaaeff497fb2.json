{"ast": null, "code": "export class Remove {\n  constructor() {\n    this.quantity = 2;\n  }\n\n  get particles_nb() {\n    return this.quantity;\n  }\n\n  set particles_nb(value) {\n    this.quantity = value;\n  }\n\n  load(data) {\n    var _a;\n\n    if (data === undefined) {\n      return;\n    }\n\n    const quantity = (_a = data.quantity) !== null && _a !== void 0 ? _a : data.particles_nb;\n\n    if (quantity !== undefined) {\n      this.quantity = quantity;\n    }\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Options/Classes/Interactivity/Modes/Remove.js"], "names": ["Remove", "constructor", "quantity", "particles_nb", "value", "load", "data", "_a", "undefined"], "mappings": "AAAA,OAAO,MAAMA,MAAN,CAAa;AAChBC,EAAAA,WAAW,GAAG;AACV,SAAKC,QAAL,GAAgB,CAAhB;AACH;;AACe,MAAZC,YAAY,GAAG;AACf,WAAO,KAAKD,QAAZ;AACH;;AACe,MAAZC,YAAY,CAACC,KAAD,EAAQ;AACpB,SAAKF,QAAL,GAAgBE,KAAhB;AACH;;AACDC,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,QAAIC,EAAJ;;AACA,QAAID,IAAI,KAAKE,SAAb,EAAwB;AACpB;AACH;;AACD,UAAMN,QAAQ,GAAG,CAACK,EAAE,GAAGD,IAAI,CAACJ,QAAX,MAAyB,IAAzB,IAAiCK,EAAE,KAAK,KAAK,CAA7C,GAAiDA,EAAjD,GAAsDD,IAAI,CAACH,YAA5E;;AACA,QAAID,QAAQ,KAAKM,SAAjB,EAA4B;AACxB,WAAKN,QAAL,GAAgBA,QAAhB;AACH;AACJ;;AAnBe", "sourcesContent": ["export class Remove {\n    constructor() {\n        this.quantity = 2;\n    }\n    get particles_nb() {\n        return this.quantity;\n    }\n    set particles_nb(value) {\n        this.quantity = value;\n    }\n    load(data) {\n        var _a;\n        if (data === undefined) {\n            return;\n        }\n        const quantity = (_a = data.quantity) !== null && _a !== void 0 ? _a : data.particles_nb;\n        if (quantity !== undefined) {\n            this.quantity = quantity;\n        }\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
{"ast": null, "code": "import { setRangeValue } from \"../../Utils\";\nexport class ColorAnimation {\n  constructor() {\n    this.count = 0;\n    this.enable = false;\n    this.offset = 0;\n    this.speed = 1;\n    this.sync = true;\n  }\n\n  load(data) {\n    if (data === undefined) {\n      return;\n    }\n\n    if (data.count !== undefined) {\n      this.count = setRangeValue(data.count);\n    }\n\n    if (data.enable !== undefined) {\n      this.enable = data.enable;\n    }\n\n    if (data.offset !== undefined) {\n      this.offset = setRangeValue(data.offset);\n    }\n\n    if (data.speed !== undefined) {\n      this.speed = setRangeValue(data.speed);\n    }\n\n    if (data.sync !== undefined) {\n      this.sync = data.sync;\n    }\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Options/Classes/ColorAnimation.js"], "names": ["setRangeValue", "ColorAnimation", "constructor", "count", "enable", "offset", "speed", "sync", "load", "data", "undefined"], "mappings": "AAAA,SAASA,aAAT,QAA8B,aAA9B;AACA,OAAO,MAAMC,cAAN,CAAqB;AACxBC,EAAAA,WAAW,GAAG;AACV,SAAKC,KAAL,GAAa,CAAb;AACA,SAAKC,MAAL,GAAc,KAAd;AACA,SAAKC,MAAL,GAAc,CAAd;AACA,SAAKC,KAAL,GAAa,CAAb;AACA,SAAKC,IAAL,GAAY,IAAZ;AACH;;AACDC,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,QAAIA,IAAI,KAAKC,SAAb,EAAwB;AACpB;AACH;;AACD,QAAID,IAAI,CAACN,KAAL,KAAeO,SAAnB,EAA8B;AAC1B,WAAKP,KAAL,GAAaH,aAAa,CAACS,IAAI,CAACN,KAAN,CAA1B;AACH;;AACD,QAAIM,IAAI,CAACL,MAAL,KAAgBM,SAApB,EAA+B;AAC3B,WAAKN,MAAL,GAAcK,IAAI,CAACL,MAAnB;AACH;;AACD,QAAIK,IAAI,CAACJ,MAAL,KAAgBK,SAApB,EAA+B;AAC3B,WAAKL,MAAL,GAAcL,aAAa,CAACS,IAAI,CAACJ,MAAN,CAA3B;AACH;;AACD,QAAII,IAAI,CAACH,KAAL,KAAeI,SAAnB,EAA8B;AAC1B,WAAKJ,KAAL,GAAaN,aAAa,CAACS,IAAI,CAACH,KAAN,CAA1B;AACH;;AACD,QAAIG,IAAI,CAACF,IAAL,KAAcG,SAAlB,EAA6B;AACzB,WAAKH,IAAL,GAAYE,IAAI,CAACF,IAAjB;AACH;AACJ;;AA3BuB", "sourcesContent": ["import { setRangeValue } from \"../../Utils\";\nexport class ColorAnimation {\n    constructor() {\n        this.count = 0;\n        this.enable = false;\n        this.offset = 0;\n        this.speed = 1;\n        this.sync = true;\n    }\n    load(data) {\n        if (data === undefined) {\n            return;\n        }\n        if (data.count !== undefined) {\n            this.count = setRangeValue(data.count);\n        }\n        if (data.enable !== undefined) {\n            this.enable = data.enable;\n        }\n        if (data.offset !== undefined) {\n            this.offset = setRangeValue(data.offset);\n        }\n        if (data.speed !== undefined) {\n            this.speed = setRangeValue(data.speed);\n        }\n        if (data.sync !== undefined) {\n            this.sync = data.sync;\n        }\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
{"ast": null, "code": "import { PolygonMaskDraw } from \"./PolygonMaskDraw\";\nimport { PolygonMaskInline } from \"./PolygonMaskInline\";\nimport { PolygonMaskLocalSvg } from \"./PolygonMaskLocalSvg\";\nimport { PolygonMaskMove } from \"./PolygonMaskMove\";\nimport { deepExtend } from \"../../../../Utils\";\nexport class PolygonMask {\n  constructor() {\n    this.draw = new PolygonMaskDraw();\n    this.enable = false;\n    this.inline = new PolygonMaskInline();\n    this.move = new PolygonMaskMove();\n    this.scale = 1;\n    this.type = \"none\";\n  }\n\n  get inlineArrangement() {\n    return this.inline.arrangement;\n  }\n\n  set inlineArrangement(value) {\n    this.inline.arrangement = value;\n  }\n\n  load(data) {\n    var _a;\n\n    if (!data) {\n      return;\n    }\n\n    this.draw.load(data.draw);\n    const inline = (_a = data.inline) !== null && _a !== void 0 ? _a : {\n      arrangement: data.inlineArrangement\n    };\n\n    if (inline !== undefined) {\n      this.inline.load(inline);\n    }\n\n    this.move.load(data.move);\n\n    if (data.scale !== undefined) {\n      this.scale = data.scale;\n    }\n\n    if (data.type !== undefined) {\n      this.type = data.type;\n    }\n\n    if (data.enable !== undefined) {\n      this.enable = data.enable;\n    } else {\n      this.enable = this.type !== \"none\";\n    }\n\n    if (data.url !== undefined) {\n      this.url = data.url;\n    }\n\n    if (data.data !== undefined) {\n      if (typeof data.data === \"string\") {\n        this.data = data.data;\n      } else {\n        this.data = new PolygonMaskLocalSvg();\n        this.data.load(data.data);\n      }\n    }\n\n    if (data.position !== undefined) {\n      this.position = deepExtend({}, data.position);\n    }\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Plugins/PolygonMask/Options/Classes/PolygonMask.js"], "names": ["PolygonMaskDraw", "PolygonMaskInline", "PolygonMaskLocalSvg", "PolygonMaskMove", "deepExtend", "PolygonMask", "constructor", "draw", "enable", "inline", "move", "scale", "type", "inlineArrangement", "arrangement", "value", "load", "data", "_a", "undefined", "url", "position"], "mappings": "AAAA,SAASA,eAAT,QAAgC,mBAAhC;AACA,SAASC,iBAAT,QAAkC,qBAAlC;AACA,SAASC,mBAAT,QAAoC,uBAApC;AACA,SAASC,eAAT,QAAgC,mBAAhC;AACA,SAASC,UAAT,QAA2B,mBAA3B;AACA,OAAO,MAAMC,WAAN,CAAkB;AACrBC,EAAAA,WAAW,GAAG;AACV,SAAKC,IAAL,GAAY,IAAIP,eAAJ,EAAZ;AACA,SAAKQ,MAAL,GAAc,KAAd;AACA,SAAKC,MAAL,GAAc,IAAIR,iBAAJ,EAAd;AACA,SAAKS,IAAL,GAAY,IAAIP,eAAJ,EAAZ;AACA,SAAKQ,KAAL,GAAa,CAAb;AACA,SAAKC,IAAL,GAAY,MAAZ;AACH;;AACoB,MAAjBC,iBAAiB,GAAG;AACpB,WAAO,KAAKJ,MAAL,CAAYK,WAAnB;AACH;;AACoB,MAAjBD,iBAAiB,CAACE,KAAD,EAAQ;AACzB,SAAKN,MAAL,CAAYK,WAAZ,GAA0BC,KAA1B;AACH;;AACDC,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,QAAIC,EAAJ;;AACA,QAAI,CAACD,IAAL,EAAW;AACP;AACH;;AACD,SAAKV,IAAL,CAAUS,IAAV,CAAeC,IAAI,CAACV,IAApB;AACA,UAAME,MAAM,GAAG,CAACS,EAAE,GAAGD,IAAI,CAACR,MAAX,MAAuB,IAAvB,IAA+BS,EAAE,KAAK,KAAK,CAA3C,GAA+CA,EAA/C,GAAoD;AAC/DJ,MAAAA,WAAW,EAAEG,IAAI,CAACJ;AAD6C,KAAnE;;AAGA,QAAIJ,MAAM,KAAKU,SAAf,EAA0B;AACtB,WAAKV,MAAL,CAAYO,IAAZ,CAAiBP,MAAjB;AACH;;AACD,SAAKC,IAAL,CAAUM,IAAV,CAAeC,IAAI,CAACP,IAApB;;AACA,QAAIO,IAAI,CAACN,KAAL,KAAeQ,SAAnB,EAA8B;AAC1B,WAAKR,KAAL,GAAaM,IAAI,CAACN,KAAlB;AACH;;AACD,QAAIM,IAAI,CAACL,IAAL,KAAcO,SAAlB,EAA6B;AACzB,WAAKP,IAAL,GAAYK,IAAI,CAACL,IAAjB;AACH;;AACD,QAAIK,IAAI,CAACT,MAAL,KAAgBW,SAApB,EAA+B;AAC3B,WAAKX,MAAL,GAAcS,IAAI,CAACT,MAAnB;AACH,KAFD,MAGK;AACD,WAAKA,MAAL,GAAc,KAAKI,IAAL,KAAc,MAA5B;AACH;;AACD,QAAIK,IAAI,CAACG,GAAL,KAAaD,SAAjB,EAA4B;AACxB,WAAKC,GAAL,GAAWH,IAAI,CAACG,GAAhB;AACH;;AACD,QAAIH,IAAI,CAACA,IAAL,KAAcE,SAAlB,EAA6B;AACzB,UAAI,OAAOF,IAAI,CAACA,IAAZ,KAAqB,QAAzB,EAAmC;AAC/B,aAAKA,IAAL,GAAYA,IAAI,CAACA,IAAjB;AACH,OAFD,MAGK;AACD,aAAKA,IAAL,GAAY,IAAIf,mBAAJ,EAAZ;AACA,aAAKe,IAAL,CAAUD,IAAV,CAAeC,IAAI,CAACA,IAApB;AACH;AACJ;;AACD,QAAIA,IAAI,CAACI,QAAL,KAAkBF,SAAtB,EAAiC;AAC7B,WAAKE,QAAL,GAAgBjB,UAAU,CAAC,EAAD,EAAKa,IAAI,CAACI,QAAV,CAA1B;AACH;AACJ;;AAvDoB", "sourcesContent": ["import { PolygonMaskDraw } from \"./PolygonMaskDraw\";\nimport { PolygonMaskInline } from \"./PolygonMaskInline\";\nimport { PolygonMaskLocalSvg } from \"./PolygonMaskLocalSvg\";\nimport { PolygonMaskMove } from \"./PolygonMaskMove\";\nimport { deepExtend } from \"../../../../Utils\";\nexport class PolygonMask {\n    constructor() {\n        this.draw = new PolygonMaskDraw();\n        this.enable = false;\n        this.inline = new PolygonMaskInline();\n        this.move = new PolygonMaskMove();\n        this.scale = 1;\n        this.type = \"none\";\n    }\n    get inlineArrangement() {\n        return this.inline.arrangement;\n    }\n    set inlineArrangement(value) {\n        this.inline.arrangement = value;\n    }\n    load(data) {\n        var _a;\n        if (!data) {\n            return;\n        }\n        this.draw.load(data.draw);\n        const inline = (_a = data.inline) !== null && _a !== void 0 ? _a : {\n            arrangement: data.inlineArrangement,\n        };\n        if (inline !== undefined) {\n            this.inline.load(inline);\n        }\n        this.move.load(data.move);\n        if (data.scale !== undefined) {\n            this.scale = data.scale;\n        }\n        if (data.type !== undefined) {\n            this.type = data.type;\n        }\n        if (data.enable !== undefined) {\n            this.enable = data.enable;\n        }\n        else {\n            this.enable = this.type !== \"none\";\n        }\n        if (data.url !== undefined) {\n            this.url = data.url;\n        }\n        if (data.data !== undefined) {\n            if (typeof data.data === \"string\") {\n                this.data = data.data;\n            }\n            else {\n                this.data = new PolygonMaskLocalSvg();\n                this.data.load(data.data);\n            }\n        }\n        if (data.position !== undefined) {\n            this.position = deepExtend({}, data.position);\n        }\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
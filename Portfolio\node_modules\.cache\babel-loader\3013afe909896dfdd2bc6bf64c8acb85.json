{"ast": null, "code": "import { HslAnimation } from \"./HslAnimation\";\nimport { OptionsColor } from \"./OptionsColor\";\nexport class AnimatableColor extends OptionsColor {\n  constructor() {\n    super();\n    this.animation = new HslAnimation();\n  }\n\n  static create(source, data) {\n    const color = new AnimatableColor();\n    color.load(source);\n\n    if (data !== undefined) {\n      if (typeof data === \"string\" || data instanceof Array) {\n        color.load({\n          value: data\n        });\n      } else {\n        color.load(data);\n      }\n    }\n\n    return color;\n  }\n\n  load(data) {\n    super.load(data);\n\n    if (!data) {\n      return;\n    }\n\n    const colorAnimation = data.animation;\n\n    if (colorAnimation !== undefined) {\n      if (colorAnimation.enable !== undefined) {\n        this.animation.h.load(colorAnimation);\n      } else {\n        this.animation.load(data.animation);\n      }\n    }\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Options/Classes/AnimatableColor.js"], "names": ["HslAnimation", "OptionsColor", "AnimatableColor", "constructor", "animation", "create", "source", "data", "color", "load", "undefined", "Array", "value", "colorAnimation", "enable", "h"], "mappings": "AAAA,SAASA,YAAT,QAA6B,gBAA7B;AACA,SAASC,YAAT,QAA6B,gBAA7B;AACA,OAAO,MAAMC,eAAN,SAA8BD,YAA9B,CAA2C;AAC9CE,EAAAA,WAAW,GAAG;AACV;AACA,SAAKC,SAAL,GAAiB,IAAIJ,YAAJ,EAAjB;AACH;;AACY,SAANK,MAAM,CAACC,MAAD,EAASC,IAAT,EAAe;AACxB,UAAMC,KAAK,GAAG,IAAIN,eAAJ,EAAd;AACAM,IAAAA,KAAK,CAACC,IAAN,CAAWH,MAAX;;AACA,QAAIC,IAAI,KAAKG,SAAb,EAAwB;AACpB,UAAI,OAAOH,IAAP,KAAgB,QAAhB,IAA4BA,IAAI,YAAYI,KAAhD,EAAuD;AACnDH,QAAAA,KAAK,CAACC,IAAN,CAAW;AAAEG,UAAAA,KAAK,EAAEL;AAAT,SAAX;AACH,OAFD,MAGK;AACDC,QAAAA,KAAK,CAACC,IAAN,CAAWF,IAAX;AACH;AACJ;;AACD,WAAOC,KAAP;AACH;;AACDC,EAAAA,IAAI,CAACF,IAAD,EAAO;AACP,UAAME,IAAN,CAAWF,IAAX;;AACA,QAAI,CAACA,IAAL,EAAW;AACP;AACH;;AACD,UAAMM,cAAc,GAAGN,IAAI,CAACH,SAA5B;;AACA,QAAIS,cAAc,KAAKH,SAAvB,EAAkC;AAC9B,UAAIG,cAAc,CAACC,MAAf,KAA0BJ,SAA9B,EAAyC;AACrC,aAAKN,SAAL,CAAeW,CAAf,CAAiBN,IAAjB,CAAsBI,cAAtB;AACH,OAFD,MAGK;AACD,aAAKT,SAAL,CAAeK,IAAf,CAAoBF,IAAI,CAACH,SAAzB;AACH;AACJ;AACJ;;AAhC6C", "sourcesContent": ["import { HslAnimation } from \"./HslAnimation\";\nimport { OptionsColor } from \"./OptionsColor\";\nexport class AnimatableColor extends OptionsColor {\n    constructor() {\n        super();\n        this.animation = new HslAnimation();\n    }\n    static create(source, data) {\n        const color = new AnimatableColor();\n        color.load(source);\n        if (data !== undefined) {\n            if (typeof data === \"string\" || data instanceof Array) {\n                color.load({ value: data });\n            }\n            else {\n                color.load(data);\n            }\n        }\n        return color;\n    }\n    load(data) {\n        super.load(data);\n        if (!data) {\n            return;\n        }\n        const colorAnimation = data.animation;\n        if (colorAnimation !== undefined) {\n            if (colorAnimation.enable !== undefined) {\n                this.animation.h.load(colorAnimation);\n            }\n            else {\n                this.animation.load(data.animation);\n            }\n        }\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
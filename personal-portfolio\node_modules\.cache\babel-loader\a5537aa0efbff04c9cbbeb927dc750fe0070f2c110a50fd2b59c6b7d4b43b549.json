{"ast": null, "code": "import { Pusher } from \"./Pusher.js\";\nexport async function loadExternalPushInteraction(engine, refresh = true) {\n  await engine.addInteractor(\"externalPush\", container => {\n    return Promise.resolve(new Pusher(container));\n  }, refresh);\n}\nexport * from \"./Options/Classes/Push.js\";\nexport * from \"./Options/Interfaces/IPush.js\";", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "loadExternalPushInteraction", "engine", "refresh", "addInteractor", "container", "Promise", "resolve"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/interaction-external-push/browser/index.js"], "sourcesContent": ["import { Pusher } from \"./Pusher.js\";\nexport async function loadExternalPushInteraction(engine, refresh = true) {\n    await engine.addInteractor(\"externalPush\", container => {\n        return Promise.resolve(new Pusher(container));\n    }, refresh);\n}\nexport * from \"./Options/Classes/Push.js\";\nexport * from \"./Options/Interfaces/IPush.js\";\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,aAAa;AACpC,OAAO,eAAeC,2BAA2BA,CAACC,MAAM,EAAEC,OAAO,GAAG,IAAI,EAAE;EACtE,MAAMD,MAAM,CAACE,aAAa,CAAC,cAAc,EAAEC,SAAS,IAAI;IACpD,OAAOC,OAAO,CAACC,OAAO,CAAC,IAAIP,MAAM,CAACK,SAAS,CAAC,CAAC;EACjD,CAAC,EAAEF,OAAO,CAAC;AACf;AACA,cAAc,2BAA2B;AACzC,cAAc,+BAA+B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
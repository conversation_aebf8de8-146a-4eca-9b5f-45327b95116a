{"ast": null, "code": "export class FullScreen {\n  constructor() {\n    this.enable = true;\n    this.zIndex = 0;\n  }\n  load(data) {\n    if (!data) {\n      return;\n    }\n    if (data.enable !== undefined) {\n      this.enable = data.enable;\n    }\n    if (data.zIndex !== undefined) {\n      this.zIndex = data.zIndex;\n    }\n  }\n}", "map": {"version": 3, "names": ["FullScreen", "constructor", "enable", "zIndex", "load", "data", "undefined"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/engine/browser/Options/Classes/FullScreen/FullScreen.js"], "sourcesContent": ["export class FullScreen {\n    constructor() {\n        this.enable = true;\n        this.zIndex = 0;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.enable !== undefined) {\n            this.enable = data.enable;\n        }\n        if (data.zIndex !== undefined) {\n            this.zIndex = data.zIndex;\n        }\n    }\n}\n"], "mappings": "AAAA,OAAO,MAAMA,UAAU,CAAC;EACpBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,MAAM,GAAG,IAAI;IAClB,IAAI,CAACC,MAAM,GAAG,CAAC;EACnB;EACAC,IAAIA,CAACC,IAAI,EAAE;IACP,IAAI,CAACA,IAAI,EAAE;MACP;IACJ;IACA,IAAIA,IAAI,CAACH,MAAM,KAAKI,SAAS,EAAE;MAC3B,IAAI,CAACJ,MAAM,GAAGG,IAAI,CAACH,MAAM;IAC7B;IACA,IAAIG,IAAI,CAACF,MAAM,KAAKG,SAAS,EAAE;MAC3B,IAAI,CAACH,MAAM,GAAGE,IAAI,CAACF,MAAM;IAC7B;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
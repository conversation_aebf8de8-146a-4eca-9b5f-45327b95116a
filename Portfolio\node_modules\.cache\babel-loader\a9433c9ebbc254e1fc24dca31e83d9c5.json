{"ast": null, "code": "import { Circle } from \"./Circle\";\nimport { <PERSON>Warp } from \"./CircleWarp\";\nimport { Rectangle } from \"./Rectangle\";\nimport { getDistance } from \"../../Utils\";\nexport class QuadTree {\n  constructor(rectangle, capacity) {\n    this.rectangle = rectangle;\n    this.capacity = capacity;\n    this.points = [];\n    this.divided = false;\n  }\n\n  subdivide() {\n    const x = this.rectangle.position.x;\n    const y = this.rectangle.position.y;\n    const w = this.rectangle.size.width;\n    const h = this.rectangle.size.height;\n    const capacity = this.capacity;\n    this.northEast = new QuadTree(new Rectangle(x, y, w / 2, h / 2), capacity);\n    this.northWest = new QuadTree(new Rectangle(x + w / 2, y, w / 2, h / 2), capacity);\n    this.southEast = new QuadTree(new Rectangle(x, y + h / 2, w / 2, h / 2), capacity);\n    this.southWest = new QuadTree(new Rectangle(x + w / 2, y + h / 2, w / 2, h / 2), capacity);\n    this.divided = true;\n  }\n\n  insert(point) {\n    var _a, _b, _c, _d, _e;\n\n    if (!this.rectangle.contains(point.position)) {\n      return false;\n    }\n\n    if (this.points.length < this.capacity) {\n      this.points.push(point);\n      return true;\n    }\n\n    if (!this.divided) {\n      this.subdivide();\n    }\n\n    return (_e = ((_a = this.northEast) === null || _a === void 0 ? void 0 : _a.insert(point)) || ((_b = this.northWest) === null || _b === void 0 ? void 0 : _b.insert(point)) || ((_c = this.southEast) === null || _c === void 0 ? void 0 : _c.insert(point)) || ((_d = this.southWest) === null || _d === void 0 ? void 0 : _d.insert(point))) !== null && _e !== void 0 ? _e : false;\n  }\n\n  queryCircle(position, radius) {\n    return this.query(new Circle(position.x, position.y, radius));\n  }\n\n  queryCircleWarp(position, radius, containerOrSize) {\n    const container = containerOrSize;\n    const size = containerOrSize;\n    return this.query(new CircleWarp(position.x, position.y, radius, container.canvas !== undefined ? container.canvas.size : size));\n  }\n\n  queryRectangle(position, size) {\n    return this.query(new Rectangle(position.x, position.y, size.width, size.height));\n  }\n\n  query(range, found) {\n    var _a, _b, _c, _d;\n\n    const res = found !== null && found !== void 0 ? found : [];\n\n    if (!range.intersects(this.rectangle)) {\n      return [];\n    } else {\n      for (const p of this.points) {\n        if (!range.contains(p.position) && getDistance(range.position, p.position) > p.particle.getRadius()) {\n          continue;\n        }\n\n        res.push(p.particle);\n      }\n\n      if (this.divided) {\n        (_a = this.northEast) === null || _a === void 0 ? void 0 : _a.query(range, res);\n        (_b = this.northWest) === null || _b === void 0 ? void 0 : _b.query(range, res);\n        (_c = this.southEast) === null || _c === void 0 ? void 0 : _c.query(range, res);\n        (_d = this.southWest) === null || _d === void 0 ? void 0 : _d.query(range, res);\n      }\n    }\n\n    return res;\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Core/Utils/QuadTree.js"], "names": ["Circle", "CircleWarp", "Rectangle", "getDistance", "QuadTree", "constructor", "rectangle", "capacity", "points", "divided", "subdivide", "x", "position", "y", "w", "size", "width", "h", "height", "northEast", "northWest", "southEast", "southWest", "insert", "point", "_a", "_b", "_c", "_d", "_e", "contains", "length", "push", "queryCircle", "radius", "query", "queryCircleWarp", "containerOrSize", "container", "canvas", "undefined", "queryRectangle", "range", "found", "res", "intersects", "p", "particle", "getRadius"], "mappings": "AAAA,SAASA,MAAT,QAAuB,UAAvB;AACA,SAASC,UAAT,QAA2B,cAA3B;AACA,SAASC,SAAT,QAA0B,aAA1B;AACA,SAASC,WAAT,QAA4B,aAA5B;AACA,OAAO,MAAMC,QAAN,CAAe;AAClBC,EAAAA,WAAW,CAACC,SAAD,EAAYC,QAAZ,EAAsB;AAC7B,SAAKD,SAAL,GAAiBA,SAAjB;AACA,SAAKC,QAAL,GAAgBA,QAAhB;AACA,SAAKC,MAAL,GAAc,EAAd;AACA,SAAKC,OAAL,GAAe,KAAf;AACH;;AACDC,EAAAA,SAAS,GAAG;AACR,UAAMC,CAAC,GAAG,KAAKL,SAAL,CAAeM,QAAf,CAAwBD,CAAlC;AACA,UAAME,CAAC,GAAG,KAAKP,SAAL,CAAeM,QAAf,CAAwBC,CAAlC;AACA,UAAMC,CAAC,GAAG,KAAKR,SAAL,CAAeS,IAAf,CAAoBC,KAA9B;AACA,UAAMC,CAAC,GAAG,KAAKX,SAAL,CAAeS,IAAf,CAAoBG,MAA9B;AACA,UAAMX,QAAQ,GAAG,KAAKA,QAAtB;AACA,SAAKY,SAAL,GAAiB,IAAIf,QAAJ,CAAa,IAAIF,SAAJ,CAAcS,CAAd,EAAiBE,CAAjB,EAAoBC,CAAC,GAAG,CAAxB,EAA2BG,CAAC,GAAG,CAA/B,CAAb,EAAgDV,QAAhD,CAAjB;AACA,SAAKa,SAAL,GAAiB,IAAIhB,QAAJ,CAAa,IAAIF,SAAJ,CAAcS,CAAC,GAAGG,CAAC,GAAG,CAAtB,EAAyBD,CAAzB,EAA4BC,CAAC,GAAG,CAAhC,EAAmCG,CAAC,GAAG,CAAvC,CAAb,EAAwDV,QAAxD,CAAjB;AACA,SAAKc,SAAL,GAAiB,IAAIjB,QAAJ,CAAa,IAAIF,SAAJ,CAAcS,CAAd,EAAiBE,CAAC,GAAGI,CAAC,GAAG,CAAzB,EAA4BH,CAAC,GAAG,CAAhC,EAAmCG,CAAC,GAAG,CAAvC,CAAb,EAAwDV,QAAxD,CAAjB;AACA,SAAKe,SAAL,GAAiB,IAAIlB,QAAJ,CAAa,IAAIF,SAAJ,CAAcS,CAAC,GAAGG,CAAC,GAAG,CAAtB,EAAyBD,CAAC,GAAGI,CAAC,GAAG,CAAjC,EAAoCH,CAAC,GAAG,CAAxC,EAA2CG,CAAC,GAAG,CAA/C,CAAb,EAAgEV,QAAhE,CAAjB;AACA,SAAKE,OAAL,GAAe,IAAf;AACH;;AACDc,EAAAA,MAAM,CAACC,KAAD,EAAQ;AACV,QAAIC,EAAJ,EAAQC,EAAR,EAAYC,EAAZ,EAAgBC,EAAhB,EAAoBC,EAApB;;AACA,QAAI,CAAC,KAAKvB,SAAL,CAAewB,QAAf,CAAwBN,KAAK,CAACZ,QAA9B,CAAL,EAA8C;AAC1C,aAAO,KAAP;AACH;;AACD,QAAI,KAAKJ,MAAL,CAAYuB,MAAZ,GAAqB,KAAKxB,QAA9B,EAAwC;AACpC,WAAKC,MAAL,CAAYwB,IAAZ,CAAiBR,KAAjB;AACA,aAAO,IAAP;AACH;;AACD,QAAI,CAAC,KAAKf,OAAV,EAAmB;AACf,WAAKC,SAAL;AACH;;AACD,WAAQ,CAACmB,EAAE,GAAI,CAAC,CAACJ,EAAE,GAAG,KAAKN,SAAX,MAA0B,IAA1B,IAAkCM,EAAE,KAAK,KAAK,CAA9C,GAAkD,KAAK,CAAvD,GAA2DA,EAAE,CAACF,MAAH,CAAUC,KAAV,CAA5D,MACV,CAACE,EAAE,GAAG,KAAKN,SAAX,MAA0B,IAA1B,IAAkCM,EAAE,KAAK,KAAK,CAA9C,GAAkD,KAAK,CAAvD,GAA2DA,EAAE,CAACH,MAAH,CAAUC,KAAV,CADjD,MAEV,CAACG,EAAE,GAAG,KAAKN,SAAX,MAA0B,IAA1B,IAAkCM,EAAE,KAAK,KAAK,CAA9C,GAAkD,KAAK,CAAvD,GAA2DA,EAAE,CAACJ,MAAH,CAAUC,KAAV,CAFjD,MAGV,CAACI,EAAE,GAAG,KAAKN,SAAX,MAA0B,IAA1B,IAAkCM,EAAE,KAAK,KAAK,CAA9C,GAAkD,KAAK,CAAvD,GAA2DA,EAAE,CAACL,MAAH,CAAUC,KAAV,CAHjD,CAAP,MAGgF,IAHhF,IAGwFK,EAAE,KAAK,KAAK,CAHpG,GAGwGA,EAHxG,GAG6G,KAHrH;AAIH;;AACDI,EAAAA,WAAW,CAACrB,QAAD,EAAWsB,MAAX,EAAmB;AAC1B,WAAO,KAAKC,KAAL,CAAW,IAAInC,MAAJ,CAAWY,QAAQ,CAACD,CAApB,EAAuBC,QAAQ,CAACC,CAAhC,EAAmCqB,MAAnC,CAAX,CAAP;AACH;;AACDE,EAAAA,eAAe,CAACxB,QAAD,EAAWsB,MAAX,EAAmBG,eAAnB,EAAoC;AAC/C,UAAMC,SAAS,GAAGD,eAAlB;AACA,UAAMtB,IAAI,GAAGsB,eAAb;AACA,WAAO,KAAKF,KAAL,CAAW,IAAIlC,UAAJ,CAAeW,QAAQ,CAACD,CAAxB,EAA2BC,QAAQ,CAACC,CAApC,EAAuCqB,MAAvC,EAA+CI,SAAS,CAACC,MAAV,KAAqBC,SAArB,GAAiCF,SAAS,CAACC,MAAV,CAAiBxB,IAAlD,GAAyDA,IAAxG,CAAX,CAAP;AACH;;AACD0B,EAAAA,cAAc,CAAC7B,QAAD,EAAWG,IAAX,EAAiB;AAC3B,WAAO,KAAKoB,KAAL,CAAW,IAAIjC,SAAJ,CAAcU,QAAQ,CAACD,CAAvB,EAA0BC,QAAQ,CAACC,CAAnC,EAAsCE,IAAI,CAACC,KAA3C,EAAkDD,IAAI,CAACG,MAAvD,CAAX,CAAP;AACH;;AACDiB,EAAAA,KAAK,CAACO,KAAD,EAAQC,KAAR,EAAe;AAChB,QAAIlB,EAAJ,EAAQC,EAAR,EAAYC,EAAZ,EAAgBC,EAAhB;;AACA,UAAMgB,GAAG,GAAGD,KAAK,KAAK,IAAV,IAAkBA,KAAK,KAAK,KAAK,CAAjC,GAAqCA,KAArC,GAA6C,EAAzD;;AACA,QAAI,CAACD,KAAK,CAACG,UAAN,CAAiB,KAAKvC,SAAtB,CAAL,EAAuC;AACnC,aAAO,EAAP;AACH,KAFD,MAGK;AACD,WAAK,MAAMwC,CAAX,IAAgB,KAAKtC,MAArB,EAA6B;AACzB,YAAI,CAACkC,KAAK,CAACZ,QAAN,CAAegB,CAAC,CAAClC,QAAjB,CAAD,IAA+BT,WAAW,CAACuC,KAAK,CAAC9B,QAAP,EAAiBkC,CAAC,CAAClC,QAAnB,CAAX,GAA0CkC,CAAC,CAACC,QAAF,CAAWC,SAAX,EAA7E,EAAqG;AACjG;AACH;;AACDJ,QAAAA,GAAG,CAACZ,IAAJ,CAASc,CAAC,CAACC,QAAX;AACH;;AACD,UAAI,KAAKtC,OAAT,EAAkB;AACd,SAACgB,EAAE,GAAG,KAAKN,SAAX,MAA0B,IAA1B,IAAkCM,EAAE,KAAK,KAAK,CAA9C,GAAkD,KAAK,CAAvD,GAA2DA,EAAE,CAACU,KAAH,CAASO,KAAT,EAAgBE,GAAhB,CAA3D;AACA,SAAClB,EAAE,GAAG,KAAKN,SAAX,MAA0B,IAA1B,IAAkCM,EAAE,KAAK,KAAK,CAA9C,GAAkD,KAAK,CAAvD,GAA2DA,EAAE,CAACS,KAAH,CAASO,KAAT,EAAgBE,GAAhB,CAA3D;AACA,SAACjB,EAAE,GAAG,KAAKN,SAAX,MAA0B,IAA1B,IAAkCM,EAAE,KAAK,KAAK,CAA9C,GAAkD,KAAK,CAAvD,GAA2DA,EAAE,CAACQ,KAAH,CAASO,KAAT,EAAgBE,GAAhB,CAA3D;AACA,SAAChB,EAAE,GAAG,KAAKN,SAAX,MAA0B,IAA1B,IAAkCM,EAAE,KAAK,KAAK,CAA9C,GAAkD,KAAK,CAAvD,GAA2DA,EAAE,CAACO,KAAH,CAASO,KAAT,EAAgBE,GAAhB,CAA3D;AACH;AACJ;;AACD,WAAOA,GAAP;AACH;;AApEiB", "sourcesContent": ["import { Circle } from \"./Circle\";\nimport { <PERSON>Warp } from \"./CircleWarp\";\nimport { Rectangle } from \"./Rectangle\";\nimport { getDistance } from \"../../Utils\";\nexport class QuadTree {\n    constructor(rectangle, capacity) {\n        this.rectangle = rectangle;\n        this.capacity = capacity;\n        this.points = [];\n        this.divided = false;\n    }\n    subdivide() {\n        const x = this.rectangle.position.x;\n        const y = this.rectangle.position.y;\n        const w = this.rectangle.size.width;\n        const h = this.rectangle.size.height;\n        const capacity = this.capacity;\n        this.northEast = new QuadTree(new Rectangle(x, y, w / 2, h / 2), capacity);\n        this.northWest = new QuadTree(new Rectangle(x + w / 2, y, w / 2, h / 2), capacity);\n        this.southEast = new QuadTree(new Rectangle(x, y + h / 2, w / 2, h / 2), capacity);\n        this.southWest = new QuadTree(new Rectangle(x + w / 2, y + h / 2, w / 2, h / 2), capacity);\n        this.divided = true;\n    }\n    insert(point) {\n        var _a, _b, _c, _d, _e;\n        if (!this.rectangle.contains(point.position)) {\n            return false;\n        }\n        if (this.points.length < this.capacity) {\n            this.points.push(point);\n            return true;\n        }\n        if (!this.divided) {\n            this.subdivide();\n        }\n        return ((_e = (((_a = this.northEast) === null || _a === void 0 ? void 0 : _a.insert(point)) ||\n            ((_b = this.northWest) === null || _b === void 0 ? void 0 : _b.insert(point)) ||\n            ((_c = this.southEast) === null || _c === void 0 ? void 0 : _c.insert(point)) ||\n            ((_d = this.southWest) === null || _d === void 0 ? void 0 : _d.insert(point)))) !== null && _e !== void 0 ? _e : false);\n    }\n    queryCircle(position, radius) {\n        return this.query(new Circle(position.x, position.y, radius));\n    }\n    queryCircleWarp(position, radius, containerOrSize) {\n        const container = containerOrSize;\n        const size = containerOrSize;\n        return this.query(new CircleWarp(position.x, position.y, radius, container.canvas !== undefined ? container.canvas.size : size));\n    }\n    queryRectangle(position, size) {\n        return this.query(new Rectangle(position.x, position.y, size.width, size.height));\n    }\n    query(range, found) {\n        var _a, _b, _c, _d;\n        const res = found !== null && found !== void 0 ? found : [];\n        if (!range.intersects(this.rectangle)) {\n            return [];\n        }\n        else {\n            for (const p of this.points) {\n                if (!range.contains(p.position) && getDistance(range.position, p.position) > p.particle.getRadius()) {\n                    continue;\n                }\n                res.push(p.particle);\n            }\n            if (this.divided) {\n                (_a = this.northEast) === null || _a === void 0 ? void 0 : _a.query(range, res);\n                (_b = this.northWest) === null || _b === void 0 ? void 0 : _b.query(range, res);\n                (_c = this.southEast) === null || _c === void 0 ? void 0 : _c.query(range, res);\n                (_d = this.southWest) === null || _d === void 0 ? void 0 : _d.query(range, res);\n            }\n        }\n        return res;\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
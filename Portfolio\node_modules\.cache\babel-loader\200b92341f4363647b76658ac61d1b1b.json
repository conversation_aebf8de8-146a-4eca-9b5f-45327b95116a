{"ast": null, "code": "import { Split } from \"./Split\";\nexport class Destroy {\n  constructor() {\n    this.mode = \"none\";\n    this.split = new Split();\n  }\n\n  load(data) {\n    if (!data) {\n      return;\n    }\n\n    if (data.mode !== undefined) {\n      this.mode = data.mode;\n    }\n\n    this.split.load(data.split);\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Options/Classes/Particles/Destroy/Destroy.js"], "names": ["Split", "Destroy", "constructor", "mode", "split", "load", "data", "undefined"], "mappings": "AAAA,SAASA,KAAT,QAAsB,SAAtB;AACA,OAAO,MAAMC,OAAN,CAAc;AACjBC,EAAAA,WAAW,GAAG;AACV,SAAKC,IAAL,GAAY,MAAZ;AACA,SAAKC,KAAL,GAAa,IAAIJ,KAAJ,EAAb;AACH;;AACDK,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,QAAI,CAACA,IAAL,EAAW;AACP;AACH;;AACD,QAAIA,IAAI,CAACH,IAAL,KAAcI,SAAlB,EAA6B;AACzB,WAAKJ,IAAL,GAAYG,IAAI,CAACH,IAAjB;AACH;;AACD,SAAKC,KAAL,CAAWC,IAAX,CAAgBC,IAAI,CAACF,KAArB;AACH;;AAbgB", "sourcesContent": ["import { Split } from \"./Split\";\nexport class Destroy {\n    constructor() {\n        this.mode = \"none\";\n        this.split = new Split();\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.mode !== undefined) {\n            this.mode = data.mode;\n        }\n        this.split.load(data.split);\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
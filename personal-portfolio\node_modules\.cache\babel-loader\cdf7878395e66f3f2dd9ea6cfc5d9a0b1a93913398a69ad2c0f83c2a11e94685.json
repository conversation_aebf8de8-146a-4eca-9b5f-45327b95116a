{"ast": null, "code": "import { isArray, isString } from \"../../Utils/TypeUtils.js\";\nimport { HslAnimation } from \"./HslAnimation.js\";\nimport { OptionsColor } from \"./OptionsColor.js\";\nexport class AnimatableColor extends OptionsColor {\n  constructor() {\n    super();\n    this.animation = new HslAnimation();\n  }\n  static create(source, data) {\n    const color = new AnimatableColor();\n    color.load(source);\n    if (data !== undefined) {\n      if (isString(data) || isArray(data)) {\n        color.load({\n          value: data\n        });\n      } else {\n        color.load(data);\n      }\n    }\n    return color;\n  }\n  load(data) {\n    super.load(data);\n    if (!data) {\n      return;\n    }\n    const colorAnimation = data.animation;\n    if (colorAnimation !== undefined) {\n      if (colorAnimation.enable !== undefined) {\n        this.animation.h.load(colorAnimation);\n      } else {\n        this.animation.load(data.animation);\n      }\n    }\n  }\n}", "map": {"version": 3, "names": ["isArray", "isString", "HslAnimation", "OptionsColor", "AnimatableColor", "constructor", "animation", "create", "source", "data", "color", "load", "undefined", "value", "colorAnimation", "enable", "h"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/engine/browser/Options/Classes/AnimatableColor.js"], "sourcesContent": ["import { isArray, isString } from \"../../Utils/TypeUtils.js\";\nimport { HslAnimation } from \"./HslAnimation.js\";\nimport { OptionsColor } from \"./OptionsColor.js\";\nexport class AnimatableColor extends OptionsColor {\n    constructor() {\n        super();\n        this.animation = new HslAnimation();\n    }\n    static create(source, data) {\n        const color = new AnimatableColor();\n        color.load(source);\n        if (data !== undefined) {\n            if (isString(data) || isArray(data)) {\n                color.load({ value: data });\n            }\n            else {\n                color.load(data);\n            }\n        }\n        return color;\n    }\n    load(data) {\n        super.load(data);\n        if (!data) {\n            return;\n        }\n        const colorAnimation = data.animation;\n        if (colorAnimation !== undefined) {\n            if (colorAnimation.enable !== undefined) {\n                this.animation.h.load(colorAnimation);\n            }\n            else {\n                this.animation.load(data.animation);\n            }\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,OAAO,EAAEC,QAAQ,QAAQ,0BAA0B;AAC5D,SAASC,YAAY,QAAQ,mBAAmB;AAChD,SAASC,YAAY,QAAQ,mBAAmB;AAChD,OAAO,MAAMC,eAAe,SAASD,YAAY,CAAC;EAC9CE,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,SAAS,GAAG,IAAIJ,YAAY,CAAC,CAAC;EACvC;EACA,OAAOK,MAAMA,CAACC,MAAM,EAAEC,IAAI,EAAE;IACxB,MAAMC,KAAK,GAAG,IAAIN,eAAe,CAAC,CAAC;IACnCM,KAAK,CAACC,IAAI,CAACH,MAAM,CAAC;IAClB,IAAIC,IAAI,KAAKG,SAAS,EAAE;MACpB,IAAIX,QAAQ,CAACQ,IAAI,CAAC,IAAIT,OAAO,CAACS,IAAI,CAAC,EAAE;QACjCC,KAAK,CAACC,IAAI,CAAC;UAAEE,KAAK,EAAEJ;QAAK,CAAC,CAAC;MAC/B,CAAC,MACI;QACDC,KAAK,CAACC,IAAI,CAACF,IAAI,CAAC;MACpB;IACJ;IACA,OAAOC,KAAK;EAChB;EACAC,IAAIA,CAACF,IAAI,EAAE;IACP,KAAK,CAACE,IAAI,CAACF,IAAI,CAAC;IAChB,IAAI,CAACA,IAAI,EAAE;MACP;IACJ;IACA,MAAMK,cAAc,GAAGL,IAAI,CAACH,SAAS;IACrC,IAAIQ,cAAc,KAAKF,SAAS,EAAE;MAC9B,IAAIE,cAAc,CAACC,MAAM,KAAKH,SAAS,EAAE;QACrC,IAAI,CAACN,SAAS,CAACU,CAAC,CAACL,IAAI,CAACG,cAAc,CAAC;MACzC,CAAC,MACI;QACD,IAAI,CAACR,SAAS,CAACK,IAAI,CAACF,IAAI,CAACH,SAAS,CAAC;MACvC;IACJ;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
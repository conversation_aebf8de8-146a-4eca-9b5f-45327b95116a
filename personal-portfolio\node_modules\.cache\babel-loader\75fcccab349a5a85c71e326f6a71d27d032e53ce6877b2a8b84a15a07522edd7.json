{"ast": null, "code": "import { InteractorType } from \"../../Enums/Types/InteractorType.js\";\nexport class InteractionManager {\n  constructor(engine, container) {\n    this.container = container;\n    this._engine = engine;\n    this._interactors = [];\n    this._externalInteractors = [];\n    this._particleInteractors = [];\n  }\n  externalInteract(delta) {\n    for (const interactor of this._externalInteractors) {\n      if (interactor.isEnabled()) {\n        interactor.interact(delta);\n      }\n    }\n  }\n  handleClickMode(mode) {\n    for (const interactor of this._externalInteractors) {\n      interactor.handleClickMode?.(mode);\n    }\n  }\n  async init() {\n    this._interactors = await this._engine.getInteractors(this.container, true);\n    this._externalInteractors = [];\n    this._particleInteractors = [];\n    for (const interactor of this._interactors) {\n      switch (interactor.type) {\n        case InteractorType.external:\n          this._externalInteractors.push(interactor);\n          break;\n        case InteractorType.particles:\n          this._particleInteractors.push(interactor);\n          break;\n      }\n      interactor.init();\n    }\n  }\n  particlesInteract(particle, delta) {\n    for (const interactor of this._externalInteractors) {\n      interactor.clear(particle, delta);\n    }\n    for (const interactor of this._particleInteractors) {\n      if (interactor.isEnabled(particle)) {\n        interactor.interact(particle, delta);\n      }\n    }\n  }\n  reset(particle) {\n    for (const interactor of this._externalInteractors) {\n      if (interactor.isEnabled()) {\n        interactor.reset(particle);\n      }\n    }\n    for (const interactor of this._particleInteractors) {\n      if (interactor.isEnabled(particle)) {\n        interactor.reset(particle);\n      }\n    }\n  }\n}", "map": {"version": 3, "names": ["InteractorType", "InteractionManager", "constructor", "engine", "container", "_engine", "_interactors", "_externalInteractors", "_particleInteractors", "externalInteract", "delta", "interactor", "isEnabled", "interact", "handleClickMode", "mode", "init", "getInteractors", "type", "external", "push", "particles", "particlesInteract", "particle", "clear", "reset"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/engine/browser/Core/Utils/InteractionManager.js"], "sourcesContent": ["import { InteractorType } from \"../../Enums/Types/InteractorType.js\";\nexport class InteractionManager {\n    constructor(engine, container) {\n        this.container = container;\n        this._engine = engine;\n        this._interactors = [];\n        this._externalInteractors = [];\n        this._particleInteractors = [];\n    }\n    externalInteract(delta) {\n        for (const interactor of this._externalInteractors) {\n            if (interactor.isEnabled()) {\n                interactor.interact(delta);\n            }\n        }\n    }\n    handleClickMode(mode) {\n        for (const interactor of this._externalInteractors) {\n            interactor.handleClickMode?.(mode);\n        }\n    }\n    async init() {\n        this._interactors = await this._engine.getInteractors(this.container, true);\n        this._externalInteractors = [];\n        this._particleInteractors = [];\n        for (const interactor of this._interactors) {\n            switch (interactor.type) {\n                case InteractorType.external:\n                    this._externalInteractors.push(interactor);\n                    break;\n                case InteractorType.particles:\n                    this._particleInteractors.push(interactor);\n                    break;\n            }\n            interactor.init();\n        }\n    }\n    particlesInteract(particle, delta) {\n        for (const interactor of this._externalInteractors) {\n            interactor.clear(particle, delta);\n        }\n        for (const interactor of this._particleInteractors) {\n            if (interactor.isEnabled(particle)) {\n                interactor.interact(particle, delta);\n            }\n        }\n    }\n    reset(particle) {\n        for (const interactor of this._externalInteractors) {\n            if (interactor.isEnabled()) {\n                interactor.reset(particle);\n            }\n        }\n        for (const interactor of this._particleInteractors) {\n            if (interactor.isEnabled(particle)) {\n                interactor.reset(particle);\n            }\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,qCAAqC;AACpE,OAAO,MAAMC,kBAAkB,CAAC;EAC5BC,WAAWA,CAACC,MAAM,EAAEC,SAAS,EAAE;IAC3B,IAAI,CAACA,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,OAAO,GAAGF,MAAM;IACrB,IAAI,CAACG,YAAY,GAAG,EAAE;IACtB,IAAI,CAACC,oBAAoB,GAAG,EAAE;IAC9B,IAAI,CAACC,oBAAoB,GAAG,EAAE;EAClC;EACAC,gBAAgBA,CAACC,KAAK,EAAE;IACpB,KAAK,MAAMC,UAAU,IAAI,IAAI,CAACJ,oBAAoB,EAAE;MAChD,IAAII,UAAU,CAACC,SAAS,CAAC,CAAC,EAAE;QACxBD,UAAU,CAACE,QAAQ,CAACH,KAAK,CAAC;MAC9B;IACJ;EACJ;EACAI,eAAeA,CAACC,IAAI,EAAE;IAClB,KAAK,MAAMJ,UAAU,IAAI,IAAI,CAACJ,oBAAoB,EAAE;MAChDI,UAAU,CAACG,eAAe,GAAGC,IAAI,CAAC;IACtC;EACJ;EACA,MAAMC,IAAIA,CAAA,EAAG;IACT,IAAI,CAACV,YAAY,GAAG,MAAM,IAAI,CAACD,OAAO,CAACY,cAAc,CAAC,IAAI,CAACb,SAAS,EAAE,IAAI,CAAC;IAC3E,IAAI,CAACG,oBAAoB,GAAG,EAAE;IAC9B,IAAI,CAACC,oBAAoB,GAAG,EAAE;IAC9B,KAAK,MAAMG,UAAU,IAAI,IAAI,CAACL,YAAY,EAAE;MACxC,QAAQK,UAAU,CAACO,IAAI;QACnB,KAAKlB,cAAc,CAACmB,QAAQ;UACxB,IAAI,CAACZ,oBAAoB,CAACa,IAAI,CAACT,UAAU,CAAC;UAC1C;QACJ,KAAKX,cAAc,CAACqB,SAAS;UACzB,IAAI,CAACb,oBAAoB,CAACY,IAAI,CAACT,UAAU,CAAC;UAC1C;MACR;MACAA,UAAU,CAACK,IAAI,CAAC,CAAC;IACrB;EACJ;EACAM,iBAAiBA,CAACC,QAAQ,EAAEb,KAAK,EAAE;IAC/B,KAAK,MAAMC,UAAU,IAAI,IAAI,CAACJ,oBAAoB,EAAE;MAChDI,UAAU,CAACa,KAAK,CAACD,QAAQ,EAAEb,KAAK,CAAC;IACrC;IACA,KAAK,MAAMC,UAAU,IAAI,IAAI,CAACH,oBAAoB,EAAE;MAChD,IAAIG,UAAU,CAACC,SAAS,CAACW,QAAQ,CAAC,EAAE;QAChCZ,UAAU,CAACE,QAAQ,CAACU,QAAQ,EAAEb,KAAK,CAAC;MACxC;IACJ;EACJ;EACAe,KAAKA,CAACF,QAAQ,EAAE;IACZ,KAAK,MAAMZ,UAAU,IAAI,IAAI,CAACJ,oBAAoB,EAAE;MAChD,IAAII,UAAU,CAACC,SAAS,CAAC,CAAC,EAAE;QACxBD,UAAU,CAACc,KAAK,CAACF,QAAQ,CAAC;MAC9B;IACJ;IACA,KAAK,MAAMZ,UAAU,IAAI,IAAI,CAACH,oBAAoB,EAAE;MAChD,IAAIG,UAAU,CAACC,SAAS,CAACW,QAAQ,CAAC,EAAE;QAChCZ,UAAU,CAACc,KAAK,CAACF,QAAQ,CAAC;MAC9B;IACJ;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "export var TiltDirection;\n(function (TiltDirection) {\n  TiltDirection[\"clockwise\"] = \"clockwise\";\n  TiltDirection[\"counterClockwise\"] = \"counter-clockwise\";\n  TiltDirection[\"random\"] = \"random\";\n})(TiltDirection || (TiltDirection = {}));", "map": {"version": 3, "names": ["TiltDirection"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/updater-tilt/browser/TiltDirection.js"], "sourcesContent": ["export var TiltDirection;\n(function (TiltDirection) {\n    TiltDirection[\"clockwise\"] = \"clockwise\";\n    TiltDirection[\"counterClockwise\"] = \"counter-clockwise\";\n    TiltDirection[\"random\"] = \"random\";\n})(TiltDirection || (TiltDirection = {}));\n"], "mappings": "AAAA,OAAO,IAAIA,aAAa;AACxB,CAAC,UAAUA,aAAa,EAAE;EACtBA,aAAa,CAAC,WAAW,CAAC,GAAG,WAAW;EACxCA,aAAa,CAAC,kBAAkB,CAAC,GAAG,mBAAmB;EACvDA,aAAa,CAAC,QAAQ,CAAC,GAAG,QAAQ;AACtC,CAAC,EAAEA,aAAa,KAAKA,aAAa,GAAG,CAAC,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { ParticlesOptions } from \"../Options/Classes/Particles/ParticlesOptions.js\";\nexport function loadOptions(options, ...sourceOptionsArr) {\n  for (const sourceOptions of sourceOptionsArr) {\n    options.load(sourceOptions);\n  }\n}\nexport function loadParticlesOptions(engine, container, ...sourceOptionsArr) {\n  const options = new ParticlesOptions(engine, container);\n  loadOptions(options, ...sourceOptionsArr);\n  return options;\n}", "map": {"version": 3, "names": ["ParticlesOptions", "loadOptions", "options", "sourceOptionsArr", "sourceOptions", "load", "loadParticlesOptions", "engine", "container"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/engine/browser/Utils/OptionsUtils.js"], "sourcesContent": ["import { ParticlesOptions } from \"../Options/Classes/Particles/ParticlesOptions.js\";\nexport function loadOptions(options, ...sourceOptionsArr) {\n    for (const sourceOptions of sourceOptionsArr) {\n        options.load(sourceOptions);\n    }\n}\nexport function loadParticlesOptions(engine, container, ...sourceOptionsArr) {\n    const options = new ParticlesOptions(engine, container);\n    loadOptions(options, ...sourceOptionsArr);\n    return options;\n}\n"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,kDAAkD;AACnF,OAAO,SAASC,WAAWA,CAACC,OAAO,EAAE,GAAGC,gBAAgB,EAAE;EACtD,KAAK,MAAMC,aAAa,IAAID,gBAAgB,EAAE;IAC1CD,OAAO,CAACG,IAAI,CAACD,aAAa,CAAC;EAC/B;AACJ;AACA,OAAO,SAASE,oBAAoBA,CAACC,MAAM,EAAEC,SAAS,EAAE,GAAGL,gBAAgB,EAAE;EACzE,MAAMD,OAAO,GAAG,IAAIF,gBAAgB,CAACO,MAAM,EAAEC,SAAS,CAAC;EACvDP,WAAW,CAACC,OAAO,EAAE,GAAGC,gBAAgB,CAAC;EACzC,OAAOD,OAAO;AAClB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
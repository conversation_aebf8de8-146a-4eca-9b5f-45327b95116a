{"ast": null, "code": "export default function getHTMLElementScroll(element) {\n  return {\n    scrollLeft: element.scrollLeft,\n    scrollTop: element.scrollTop\n  };\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/@popperjs/core/lib/dom-utils/getHTMLElementScroll.js"], "names": ["getHTMLElementScroll", "element", "scrollLeft", "scrollTop"], "mappings": "AAAA,eAAe,SAASA,oBAAT,CAA8BC,OAA9B,EAAuC;AACpD,SAAO;AACLC,IAAAA,UAAU,EAAED,OAAO,CAACC,UADf;AAELC,IAAAA,SAAS,EAAEF,OAAO,CAACE;AAFd,GAAP;AAID", "sourcesContent": ["export default function getHTMLElementScroll(element) {\n  return {\n    scrollLeft: element.scrollLeft,\n    scrollTop: element.scrollTop\n  };\n}"]}, "metadata": {}, "sourceType": "module"}
{"ast": null, "code": "import { LifeUpdater } from \"./LifeUpdater.js\";\nexport async function loadLifeUpdater(engine, refresh = true) {\n  await engine.addParticleUpdater(\"life\", async container => {\n    return Promise.resolve(new LifeUpdater(container));\n  }, refresh);\n}", "map": {"version": 3, "names": ["LifeUpdater", "loadLifeUpdater", "engine", "refresh", "addParticleUpdater", "container", "Promise", "resolve"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/updater-life/browser/index.js"], "sourcesContent": ["import { LifeUpdater } from \"./LifeUpdater.js\";\nexport async function loadLifeUpdater(engine, refresh = true) {\n    await engine.addParticleUpdater(\"life\", async (container) => {\n        return Promise.resolve(new LifeUpdater(container));\n    }, refresh);\n}\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,kBAAkB;AAC9C,OAAO,eAAeC,eAAeA,CAACC,MAAM,EAAEC,OAAO,GAAG,IAAI,EAAE;EAC1D,MAAMD,MAAM,CAACE,kBAAkB,CAAC,MAAM,EAAE,MAAOC,SAAS,IAAK;IACzD,OAAOC,OAAO,CAACC,OAAO,CAAC,IAAIP,WAAW,CAACK,SAAS,CAAC,CAAC;EACtD,CAAC,EAAEF,OAAO,CAAC;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
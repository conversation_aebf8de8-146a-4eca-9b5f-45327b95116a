{"ast": null, "code": "import { colorToHsl, getRangeValue } from \"../../Utils\";\n\nfunction updateRoll(particle, delta) {\n  const roll = particle.options.roll;\n\n  if (!particle.roll || !roll.enable) {\n    return;\n  }\n\n  const speed = particle.roll.speed * delta.factor;\n  const max = 2 * Math.PI;\n  particle.roll.angle += speed;\n\n  if (particle.roll.angle > max) {\n    particle.roll.angle -= max;\n  }\n}\n\nexport class RollUpdater {\n  init(particle) {\n    const rollOpt = particle.options.roll;\n\n    if (rollOpt.enable) {\n      particle.roll = {\n        angle: Math.random() * Math.PI * 2,\n        speed: getRangeValue(rollOpt.speed) / 360\n      };\n\n      if (rollOpt.backColor) {\n        particle.backColor = colorToHsl(rollOpt.backColor);\n      } else if (rollOpt.darken.enable && rollOpt.enlighten.enable) {\n        const alterType = Math.random() >= 0.5 ? \"darken\" : \"enlighten\";\n        particle.roll.alter = {\n          type: alterType,\n          value: getRangeValue(alterType === \"darken\" ? rollOpt.darken.value : rollOpt.enlighten.value)\n        };\n      } else if (rollOpt.darken.enable) {\n        particle.roll.alter = {\n          type: \"darken\",\n          value: getRangeValue(rollOpt.darken.value)\n        };\n      } else if (rollOpt.enlighten.enable) {\n        particle.roll.alter = {\n          type: \"enlighten\",\n          value: getRangeValue(rollOpt.enlighten.value)\n        };\n      }\n    } else {\n      particle.roll = {\n        angle: 0,\n        speed: 0\n      };\n    }\n  }\n\n  isEnabled(particle) {\n    const roll = particle.options.roll;\n    return !particle.destroyed && !particle.spawning && roll.enable;\n  }\n\n  update(particle, delta) {\n    if (!this.isEnabled(particle)) {\n      return;\n    }\n\n    updateRoll(particle, delta);\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Updaters/Roll/RollUpdater.js"], "names": ["colorToHsl", "getRangeValue", "updateRoll", "particle", "delta", "roll", "options", "enable", "speed", "factor", "max", "Math", "PI", "angle", "RollUpdater", "init", "rollOpt", "random", "backColor", "darken", "enlighten", "alterType", "alter", "type", "value", "isEnabled", "destroyed", "spawning", "update"], "mappings": "AAAA,SAASA,UAAT,EAAqBC,aAArB,QAA0C,aAA1C;;AACA,SAASC,UAAT,CAAoBC,QAApB,EAA8BC,KAA9B,EAAqC;AACjC,QAAMC,IAAI,GAAGF,QAAQ,CAACG,OAAT,CAAiBD,IAA9B;;AACA,MAAI,CAACF,QAAQ,CAACE,IAAV,IAAkB,CAACA,IAAI,CAACE,MAA5B,EAAoC;AAChC;AACH;;AACD,QAAMC,KAAK,GAAGL,QAAQ,CAACE,IAAT,CAAcG,KAAd,GAAsBJ,KAAK,CAACK,MAA1C;AACA,QAAMC,GAAG,GAAG,IAAIC,IAAI,CAACC,EAArB;AACAT,EAAAA,QAAQ,CAACE,IAAT,CAAcQ,KAAd,IAAuBL,KAAvB;;AACA,MAAIL,QAAQ,CAACE,IAAT,CAAcQ,KAAd,GAAsBH,GAA1B,EAA+B;AAC3BP,IAAAA,QAAQ,CAACE,IAAT,CAAcQ,KAAd,IAAuBH,GAAvB;AACH;AACJ;;AACD,OAAO,MAAMI,WAAN,CAAkB;AACrBC,EAAAA,IAAI,CAACZ,QAAD,EAAW;AACX,UAAMa,OAAO,GAAGb,QAAQ,CAACG,OAAT,CAAiBD,IAAjC;;AACA,QAAIW,OAAO,CAACT,MAAZ,EAAoB;AAChBJ,MAAAA,QAAQ,CAACE,IAAT,GAAgB;AACZQ,QAAAA,KAAK,EAAEF,IAAI,CAACM,MAAL,KAAgBN,IAAI,CAACC,EAArB,GAA0B,CADrB;AAEZJ,QAAAA,KAAK,EAAEP,aAAa,CAACe,OAAO,CAACR,KAAT,CAAb,GAA+B;AAF1B,OAAhB;;AAIA,UAAIQ,OAAO,CAACE,SAAZ,EAAuB;AACnBf,QAAAA,QAAQ,CAACe,SAAT,GAAqBlB,UAAU,CAACgB,OAAO,CAACE,SAAT,CAA/B;AACH,OAFD,MAGK,IAAIF,OAAO,CAACG,MAAR,CAAeZ,MAAf,IAAyBS,OAAO,CAACI,SAAR,CAAkBb,MAA/C,EAAuD;AACxD,cAAMc,SAAS,GAAGV,IAAI,CAACM,MAAL,MAAiB,GAAjB,GAAuB,QAAvB,GAAkC,WAApD;AACAd,QAAAA,QAAQ,CAACE,IAAT,CAAciB,KAAd,GAAsB;AAClBC,UAAAA,IAAI,EAAEF,SADY;AAElBG,UAAAA,KAAK,EAAEvB,aAAa,CAACoB,SAAS,KAAK,QAAd,GAAyBL,OAAO,CAACG,MAAR,CAAeK,KAAxC,GAAgDR,OAAO,CAACI,SAAR,CAAkBI,KAAnE;AAFF,SAAtB;AAIH,OANI,MAOA,IAAIR,OAAO,CAACG,MAAR,CAAeZ,MAAnB,EAA2B;AAC5BJ,QAAAA,QAAQ,CAACE,IAAT,CAAciB,KAAd,GAAsB;AAClBC,UAAAA,IAAI,EAAE,QADY;AAElBC,UAAAA,KAAK,EAAEvB,aAAa,CAACe,OAAO,CAACG,MAAR,CAAeK,KAAhB;AAFF,SAAtB;AAIH,OALI,MAMA,IAAIR,OAAO,CAACI,SAAR,CAAkBb,MAAtB,EAA8B;AAC/BJ,QAAAA,QAAQ,CAACE,IAAT,CAAciB,KAAd,GAAsB;AAClBC,UAAAA,IAAI,EAAE,WADY;AAElBC,UAAAA,KAAK,EAAEvB,aAAa,CAACe,OAAO,CAACI,SAAR,CAAkBI,KAAnB;AAFF,SAAtB;AAIH;AACJ,KA3BD,MA4BK;AACDrB,MAAAA,QAAQ,CAACE,IAAT,GAAgB;AAAEQ,QAAAA,KAAK,EAAE,CAAT;AAAYL,QAAAA,KAAK,EAAE;AAAnB,OAAhB;AACH;AACJ;;AACDiB,EAAAA,SAAS,CAACtB,QAAD,EAAW;AAChB,UAAME,IAAI,GAAGF,QAAQ,CAACG,OAAT,CAAiBD,IAA9B;AACA,WAAO,CAACF,QAAQ,CAACuB,SAAV,IAAuB,CAACvB,QAAQ,CAACwB,QAAjC,IAA6CtB,IAAI,CAACE,MAAzD;AACH;;AACDqB,EAAAA,MAAM,CAACzB,QAAD,EAAWC,KAAX,EAAkB;AACpB,QAAI,CAAC,KAAKqB,SAAL,CAAetB,QAAf,CAAL,EAA+B;AAC3B;AACH;;AACDD,IAAAA,UAAU,CAACC,QAAD,EAAWC,KAAX,CAAV;AACH;;AA5CoB", "sourcesContent": ["import { colorToHsl, getRangeValue } from \"../../Utils\";\nfunction updateRoll(particle, delta) {\n    const roll = particle.options.roll;\n    if (!particle.roll || !roll.enable) {\n        return;\n    }\n    const speed = particle.roll.speed * delta.factor;\n    const max = 2 * Math.PI;\n    particle.roll.angle += speed;\n    if (particle.roll.angle > max) {\n        particle.roll.angle -= max;\n    }\n}\nexport class RollUpdater {\n    init(particle) {\n        const rollOpt = particle.options.roll;\n        if (rollOpt.enable) {\n            particle.roll = {\n                angle: Math.random() * Math.PI * 2,\n                speed: getRangeValue(rollOpt.speed) / 360,\n            };\n            if (rollOpt.backColor) {\n                particle.backColor = colorToHsl(rollOpt.backColor);\n            }\n            else if (rollOpt.darken.enable && rollOpt.enlighten.enable) {\n                const alterType = Math.random() >= 0.5 ? \"darken\" : \"enlighten\";\n                particle.roll.alter = {\n                    type: alterType,\n                    value: getRangeValue(alterType === \"darken\" ? rollOpt.darken.value : rollOpt.enlighten.value),\n                };\n            }\n            else if (rollOpt.darken.enable) {\n                particle.roll.alter = {\n                    type: \"darken\",\n                    value: getRangeValue(rollOpt.darken.value),\n                };\n            }\n            else if (rollOpt.enlighten.enable) {\n                particle.roll.alter = {\n                    type: \"enlighten\",\n                    value: getRangeValue(rollOpt.enlighten.value),\n                };\n            }\n        }\n        else {\n            particle.roll = { angle: 0, speed: 0 };\n        }\n    }\n    isEnabled(particle) {\n        const roll = particle.options.roll;\n        return !particle.destroyed && !particle.spawning && roll.enable;\n    }\n    update(particle, delta) {\n        if (!this.isEnabled(particle)) {\n            return;\n        }\n        updateRoll(particle, delta);\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
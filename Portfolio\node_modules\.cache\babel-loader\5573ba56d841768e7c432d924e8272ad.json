{"ast": null, "code": "import { Random } from \"./Random\";\nimport { setRangeValue } from \"../../Utils\";\nexport class ValueWithRandom {\n  constructor() {\n    this.random = new Random();\n    this.value = 0;\n  }\n\n  load(data) {\n    if (!data) {\n      return;\n    }\n\n    if (typeof data.random === \"boolean\") {\n      this.random.enable = data.random;\n    } else {\n      this.random.load(data.random);\n    }\n\n    if (data.value !== undefined) {\n      this.value = setRangeValue(data.value, this.random.enable ? this.random.minimumValue : undefined);\n    }\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Options/Classes/ValueWithRandom.js"], "names": ["Random", "setRangeValue", "ValueWithRandom", "constructor", "random", "value", "load", "data", "enable", "undefined", "minimumValue"], "mappings": "AAAA,SAASA,MAAT,QAAuB,UAAvB;AACA,SAASC,aAAT,QAA8B,aAA9B;AACA,OAAO,MAAMC,eAAN,CAAsB;AACzBC,EAAAA,WAAW,GAAG;AACV,SAAKC,MAAL,GAAc,IAAIJ,MAAJ,EAAd;AACA,SAAKK,KAAL,GAAa,CAAb;AACH;;AACDC,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,QAAI,CAACA,IAAL,EAAW;AACP;AACH;;AACD,QAAI,OAAOA,IAAI,CAACH,MAAZ,KAAuB,SAA3B,EAAsC;AAClC,WAAKA,MAAL,CAAYI,MAAZ,GAAqBD,IAAI,CAACH,MAA1B;AACH,KAFD,MAGK;AACD,WAAKA,MAAL,CAAYE,IAAZ,CAAiBC,IAAI,CAACH,MAAtB;AACH;;AACD,QAAIG,IAAI,CAACF,KAAL,KAAeI,SAAnB,EAA8B;AAC1B,WAAKJ,KAAL,GAAaJ,aAAa,CAACM,IAAI,CAACF,KAAN,EAAa,KAAKD,MAAL,CAAYI,MAAZ,GAAqB,KAAKJ,MAAL,CAAYM,YAAjC,GAAgDD,SAA7D,CAA1B;AACH;AACJ;;AAlBwB", "sourcesContent": ["import { Random } from \"./Random\";\nimport { setRangeValue } from \"../../Utils\";\nexport class ValueWithRandom {\n    constructor() {\n        this.random = new Random();\n        this.value = 0;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (typeof data.random === \"boolean\") {\n            this.random.enable = data.random;\n        }\n        else {\n            this.random.load(data.random);\n        }\n        if (data.value !== undefined) {\n            this.value = setRangeValue(data.value, this.random.enable ? this.random.minimumValue : undefined);\n        }\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
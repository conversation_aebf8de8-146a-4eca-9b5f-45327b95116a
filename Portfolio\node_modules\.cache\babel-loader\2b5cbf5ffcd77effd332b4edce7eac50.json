{"ast": null, "code": "import { AnimatableColor } from \"../AnimatableColor\";\nexport class Stroke {\n  constructor() {\n    this.width = 0;\n  }\n\n  load(data) {\n    if (data === undefined) {\n      return;\n    }\n\n    if (data.color !== undefined) {\n      this.color = AnimatableColor.create(this.color, data.color);\n    }\n\n    if (data.width !== undefined) {\n      this.width = data.width;\n    }\n\n    if (data.opacity !== undefined) {\n      this.opacity = data.opacity;\n    }\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Options/Classes/Particles/Stroke.js"], "names": ["AnimatableColor", "Stroke", "constructor", "width", "load", "data", "undefined", "color", "create", "opacity"], "mappings": "AAAA,SAASA,eAAT,QAAgC,oBAAhC;AACA,OAAO,MAAMC,MAAN,CAAa;AAChBC,EAAAA,WAAW,GAAG;AACV,SAAKC,KAAL,GAAa,CAAb;AACH;;AACDC,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,QAAIA,IAAI,KAAKC,SAAb,EAAwB;AACpB;AACH;;AACD,QAAID,IAAI,CAACE,KAAL,KAAeD,SAAnB,EAA8B;AAC1B,WAAKC,KAAL,GAAaP,eAAe,CAACQ,MAAhB,CAAuB,KAAKD,KAA5B,EAAmCF,IAAI,CAACE,KAAxC,CAAb;AACH;;AACD,QAAIF,IAAI,CAACF,KAAL,KAAeG,SAAnB,EAA8B;AAC1B,WAAKH,KAAL,GAAaE,IAAI,CAACF,KAAlB;AACH;;AACD,QAAIE,IAAI,CAACI,OAAL,KAAiBH,SAArB,EAAgC;AAC5B,WAAKG,OAAL,GAAeJ,IAAI,CAACI,OAApB;AACH;AACJ;;AAjBe", "sourcesContent": ["import { AnimatableColor } from \"../AnimatableColor\";\nexport class Stroke {\n    constructor() {\n        this.width = 0;\n    }\n    load(data) {\n        if (data === undefined) {\n            return;\n        }\n        if (data.color !== undefined) {\n            this.color = AnimatableColor.create(this.color, data.color);\n        }\n        if (data.width !== undefined) {\n            this.width = data.width;\n        }\n        if (data.opacity !== undefined) {\n            this.opacity = data.opacity;\n        }\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
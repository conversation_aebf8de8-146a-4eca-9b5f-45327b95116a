{"ast": null, "code": "import { errorPrefix } from \"./Constants.js\";\nimport { isNumber } from \"../../Utils/TypeUtils.js\";\nconst origin = {\n    x: 0,\n    y: 0,\n    z: 0\n  },\n  squareExp = 2,\n  inverseFactorNumerator = 1.0;\nexport class Vector3d {\n  constructor(xOrCoords, y, z) {\n    this._updateFromAngle = (angle, length) => {\n      this.x = Math.cos(angle) * length;\n      this.y = Math.sin(angle) * length;\n    };\n    if (!isNumber(xOrCoords) && xOrCoords) {\n      this.x = xOrCoords.x;\n      this.y = xOrCoords.y;\n      const coords3d = xOrCoords;\n      this.z = coords3d.z ? coords3d.z : origin.z;\n    } else if (xOrCoords !== undefined && y !== undefined) {\n      this.x = xOrCoords;\n      this.y = y;\n      this.z = z ?? origin.z;\n    } else {\n      throw new Error(`${errorPrefix} Vector3d not initialized correctly`);\n    }\n  }\n  static get origin() {\n    return Vector3d.create(origin.x, origin.y, origin.z);\n  }\n  get angle() {\n    return Math.atan2(this.y, this.x);\n  }\n  set angle(angle) {\n    this._updateFromAngle(angle, this.length);\n  }\n  get length() {\n    return Math.sqrt(this.getLengthSq());\n  }\n  set length(length) {\n    this._updateFromAngle(this.angle, length);\n  }\n  static clone(source) {\n    return Vector3d.create(source.x, source.y, source.z);\n  }\n  static create(x, y, z) {\n    return new Vector3d(x, y, z);\n  }\n  add(v) {\n    return Vector3d.create(this.x + v.x, this.y + v.y, this.z + v.z);\n  }\n  addTo(v) {\n    this.x += v.x;\n    this.y += v.y;\n    this.z += v.z;\n  }\n  copy() {\n    return Vector3d.clone(this);\n  }\n  distanceTo(v) {\n    return this.sub(v).length;\n  }\n  distanceToSq(v) {\n    return this.sub(v).getLengthSq();\n  }\n  div(n) {\n    return Vector3d.create(this.x / n, this.y / n, this.z / n);\n  }\n  divTo(n) {\n    this.x /= n;\n    this.y /= n;\n    this.z /= n;\n  }\n  getLengthSq() {\n    return this.x ** squareExp + this.y ** squareExp;\n  }\n  mult(n) {\n    return Vector3d.create(this.x * n, this.y * n, this.z * n);\n  }\n  multTo(n) {\n    this.x *= n;\n    this.y *= n;\n    this.z *= n;\n  }\n  normalize() {\n    const length = this.length,\n      noLength = 0;\n    if (length != noLength) {\n      this.multTo(inverseFactorNumerator / length);\n    }\n  }\n  rotate(angle) {\n    return Vector3d.create(this.x * Math.cos(angle) - this.y * Math.sin(angle), this.x * Math.sin(angle) + this.y * Math.cos(angle), origin.z);\n  }\n  setTo(c) {\n    this.x = c.x;\n    this.y = c.y;\n    const v3d = c;\n    this.z = v3d.z ? v3d.z : origin.z;\n  }\n  sub(v) {\n    return Vector3d.create(this.x - v.x, this.y - v.y, this.z - v.z);\n  }\n  subFrom(v) {\n    this.x -= v.x;\n    this.y -= v.y;\n    this.z -= v.z;\n  }\n}\nexport class Vector extends Vector3d {\n  constructor(xOrCoords, y) {\n    super(xOrCoords, y, origin.z);\n  }\n  static get origin() {\n    return Vector.create(origin.x, origin.y);\n  }\n  static clone(source) {\n    return Vector.create(source.x, source.y);\n  }\n  static create(x, y) {\n    return new Vector(x, y);\n  }\n}", "map": {"version": 3, "names": ["errorPrefix", "isNumber", "origin", "x", "y", "z", "squareExp", "inverseFactorNumerator", "Vector3d", "constructor", "xOrCoords", "_updateFromAngle", "angle", "length", "Math", "cos", "sin", "coords3d", "undefined", "Error", "create", "atan2", "sqrt", "getLengthSq", "clone", "source", "add", "v", "addTo", "copy", "distanceTo", "sub", "distanceToSq", "div", "n", "divTo", "mult", "multTo", "normalize", "no<PERSON><PERSON><PERSON>", "rotate", "setTo", "c", "v3d", "subFrom", "Vector"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/engine/browser/Core/Utils/Vectors.js"], "sourcesContent": ["import { errorPrefix } from \"./Constants.js\";\nimport { isNumber } from \"../../Utils/TypeUtils.js\";\nconst origin = {\n    x: 0,\n    y: 0,\n    z: 0,\n}, squareExp = 2, inverseFactorNumerator = 1.0;\nexport class Vector3d {\n    constructor(xOrCoords, y, z) {\n        this._updateFromAngle = (angle, length) => {\n            this.x = Math.cos(angle) * length;\n            this.y = Math.sin(angle) * length;\n        };\n        if (!isNumber(xOrCoords) && xOrCoords) {\n            this.x = xOrCoords.x;\n            this.y = xOrCoords.y;\n            const coords3d = xOrCoords;\n            this.z = coords3d.z ? coords3d.z : origin.z;\n        }\n        else if (xOrCoords !== undefined && y !== undefined) {\n            this.x = xOrCoords;\n            this.y = y;\n            this.z = z ?? origin.z;\n        }\n        else {\n            throw new Error(`${errorPrefix} Vector3d not initialized correctly`);\n        }\n    }\n    static get origin() {\n        return Vector3d.create(origin.x, origin.y, origin.z);\n    }\n    get angle() {\n        return Math.atan2(this.y, this.x);\n    }\n    set angle(angle) {\n        this._updateFromAngle(angle, this.length);\n    }\n    get length() {\n        return Math.sqrt(this.getLengthSq());\n    }\n    set length(length) {\n        this._updateFromAngle(this.angle, length);\n    }\n    static clone(source) {\n        return Vector3d.create(source.x, source.y, source.z);\n    }\n    static create(x, y, z) {\n        return new Vector3d(x, y, z);\n    }\n    add(v) {\n        return Vector3d.create(this.x + v.x, this.y + v.y, this.z + v.z);\n    }\n    addTo(v) {\n        this.x += v.x;\n        this.y += v.y;\n        this.z += v.z;\n    }\n    copy() {\n        return Vector3d.clone(this);\n    }\n    distanceTo(v) {\n        return this.sub(v).length;\n    }\n    distanceToSq(v) {\n        return this.sub(v).getLengthSq();\n    }\n    div(n) {\n        return Vector3d.create(this.x / n, this.y / n, this.z / n);\n    }\n    divTo(n) {\n        this.x /= n;\n        this.y /= n;\n        this.z /= n;\n    }\n    getLengthSq() {\n        return this.x ** squareExp + this.y ** squareExp;\n    }\n    mult(n) {\n        return Vector3d.create(this.x * n, this.y * n, this.z * n);\n    }\n    multTo(n) {\n        this.x *= n;\n        this.y *= n;\n        this.z *= n;\n    }\n    normalize() {\n        const length = this.length, noLength = 0;\n        if (length != noLength) {\n            this.multTo(inverseFactorNumerator / length);\n        }\n    }\n    rotate(angle) {\n        return Vector3d.create(this.x * Math.cos(angle) - this.y * Math.sin(angle), this.x * Math.sin(angle) + this.y * Math.cos(angle), origin.z);\n    }\n    setTo(c) {\n        this.x = c.x;\n        this.y = c.y;\n        const v3d = c;\n        this.z = v3d.z ? v3d.z : origin.z;\n    }\n    sub(v) {\n        return Vector3d.create(this.x - v.x, this.y - v.y, this.z - v.z);\n    }\n    subFrom(v) {\n        this.x -= v.x;\n        this.y -= v.y;\n        this.z -= v.z;\n    }\n}\nexport class Vector extends Vector3d {\n    constructor(xOrCoords, y) {\n        super(xOrCoords, y, origin.z);\n    }\n    static get origin() {\n        return Vector.create(origin.x, origin.y);\n    }\n    static clone(source) {\n        return Vector.create(source.x, source.y);\n    }\n    static create(x, y) {\n        return new Vector(x, y);\n    }\n}\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,QAAQ,QAAQ,0BAA0B;AACnD,MAAMC,MAAM,GAAG;IACXC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE;EACP,CAAC;EAAEC,SAAS,GAAG,CAAC;EAAEC,sBAAsB,GAAG,GAAG;AAC9C,OAAO,MAAMC,QAAQ,CAAC;EAClBC,WAAWA,CAACC,SAAS,EAAEN,CAAC,EAAEC,CAAC,EAAE;IACzB,IAAI,CAACM,gBAAgB,GAAG,CAACC,KAAK,EAAEC,MAAM,KAAK;MACvC,IAAI,CAACV,CAAC,GAAGW,IAAI,CAACC,GAAG,CAACH,KAAK,CAAC,GAAGC,MAAM;MACjC,IAAI,CAACT,CAAC,GAAGU,IAAI,CAACE,GAAG,CAACJ,KAAK,CAAC,GAAGC,MAAM;IACrC,CAAC;IACD,IAAI,CAACZ,QAAQ,CAACS,SAAS,CAAC,IAAIA,SAAS,EAAE;MACnC,IAAI,CAACP,CAAC,GAAGO,SAAS,CAACP,CAAC;MACpB,IAAI,CAACC,CAAC,GAAGM,SAAS,CAACN,CAAC;MACpB,MAAMa,QAAQ,GAAGP,SAAS;MAC1B,IAAI,CAACL,CAAC,GAAGY,QAAQ,CAACZ,CAAC,GAAGY,QAAQ,CAACZ,CAAC,GAAGH,MAAM,CAACG,CAAC;IAC/C,CAAC,MACI,IAAIK,SAAS,KAAKQ,SAAS,IAAId,CAAC,KAAKc,SAAS,EAAE;MACjD,IAAI,CAACf,CAAC,GAAGO,SAAS;MAClB,IAAI,CAACN,CAAC,GAAGA,CAAC;MACV,IAAI,CAACC,CAAC,GAAGA,CAAC,IAAIH,MAAM,CAACG,CAAC;IAC1B,CAAC,MACI;MACD,MAAM,IAAIc,KAAK,CAAC,GAAGnB,WAAW,qCAAqC,CAAC;IACxE;EACJ;EACA,WAAWE,MAAMA,CAAA,EAAG;IAChB,OAAOM,QAAQ,CAACY,MAAM,CAAClB,MAAM,CAACC,CAAC,EAAED,MAAM,CAACE,CAAC,EAAEF,MAAM,CAACG,CAAC,CAAC;EACxD;EACA,IAAIO,KAAKA,CAAA,EAAG;IACR,OAAOE,IAAI,CAACO,KAAK,CAAC,IAAI,CAACjB,CAAC,EAAE,IAAI,CAACD,CAAC,CAAC;EACrC;EACA,IAAIS,KAAKA,CAACA,KAAK,EAAE;IACb,IAAI,CAACD,gBAAgB,CAACC,KAAK,EAAE,IAAI,CAACC,MAAM,CAAC;EAC7C;EACA,IAAIA,MAAMA,CAAA,EAAG;IACT,OAAOC,IAAI,CAACQ,IAAI,CAAC,IAAI,CAACC,WAAW,CAAC,CAAC,CAAC;EACxC;EACA,IAAIV,MAAMA,CAACA,MAAM,EAAE;IACf,IAAI,CAACF,gBAAgB,CAAC,IAAI,CAACC,KAAK,EAAEC,MAAM,CAAC;EAC7C;EACA,OAAOW,KAAKA,CAACC,MAAM,EAAE;IACjB,OAAOjB,QAAQ,CAACY,MAAM,CAACK,MAAM,CAACtB,CAAC,EAAEsB,MAAM,CAACrB,CAAC,EAAEqB,MAAM,CAACpB,CAAC,CAAC;EACxD;EACA,OAAOe,MAAMA,CAACjB,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;IACnB,OAAO,IAAIG,QAAQ,CAACL,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC;EAChC;EACAqB,GAAGA,CAACC,CAAC,EAAE;IACH,OAAOnB,QAAQ,CAACY,MAAM,CAAC,IAAI,CAACjB,CAAC,GAAGwB,CAAC,CAACxB,CAAC,EAAE,IAAI,CAACC,CAAC,GAAGuB,CAAC,CAACvB,CAAC,EAAE,IAAI,CAACC,CAAC,GAAGsB,CAAC,CAACtB,CAAC,CAAC;EACpE;EACAuB,KAAKA,CAACD,CAAC,EAAE;IACL,IAAI,CAACxB,CAAC,IAAIwB,CAAC,CAACxB,CAAC;IACb,IAAI,CAACC,CAAC,IAAIuB,CAAC,CAACvB,CAAC;IACb,IAAI,CAACC,CAAC,IAAIsB,CAAC,CAACtB,CAAC;EACjB;EACAwB,IAAIA,CAAA,EAAG;IACH,OAAOrB,QAAQ,CAACgB,KAAK,CAAC,IAAI,CAAC;EAC/B;EACAM,UAAUA,CAACH,CAAC,EAAE;IACV,OAAO,IAAI,CAACI,GAAG,CAACJ,CAAC,CAAC,CAACd,MAAM;EAC7B;EACAmB,YAAYA,CAACL,CAAC,EAAE;IACZ,OAAO,IAAI,CAACI,GAAG,CAACJ,CAAC,CAAC,CAACJ,WAAW,CAAC,CAAC;EACpC;EACAU,GAAGA,CAACC,CAAC,EAAE;IACH,OAAO1B,QAAQ,CAACY,MAAM,CAAC,IAAI,CAACjB,CAAC,GAAG+B,CAAC,EAAE,IAAI,CAAC9B,CAAC,GAAG8B,CAAC,EAAE,IAAI,CAAC7B,CAAC,GAAG6B,CAAC,CAAC;EAC9D;EACAC,KAAKA,CAACD,CAAC,EAAE;IACL,IAAI,CAAC/B,CAAC,IAAI+B,CAAC;IACX,IAAI,CAAC9B,CAAC,IAAI8B,CAAC;IACX,IAAI,CAAC7B,CAAC,IAAI6B,CAAC;EACf;EACAX,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAACpB,CAAC,IAAIG,SAAS,GAAG,IAAI,CAACF,CAAC,IAAIE,SAAS;EACpD;EACA8B,IAAIA,CAACF,CAAC,EAAE;IACJ,OAAO1B,QAAQ,CAACY,MAAM,CAAC,IAAI,CAACjB,CAAC,GAAG+B,CAAC,EAAE,IAAI,CAAC9B,CAAC,GAAG8B,CAAC,EAAE,IAAI,CAAC7B,CAAC,GAAG6B,CAAC,CAAC;EAC9D;EACAG,MAAMA,CAACH,CAAC,EAAE;IACN,IAAI,CAAC/B,CAAC,IAAI+B,CAAC;IACX,IAAI,CAAC9B,CAAC,IAAI8B,CAAC;IACX,IAAI,CAAC7B,CAAC,IAAI6B,CAAC;EACf;EACAI,SAASA,CAAA,EAAG;IACR,MAAMzB,MAAM,GAAG,IAAI,CAACA,MAAM;MAAE0B,QAAQ,GAAG,CAAC;IACxC,IAAI1B,MAAM,IAAI0B,QAAQ,EAAE;MACpB,IAAI,CAACF,MAAM,CAAC9B,sBAAsB,GAAGM,MAAM,CAAC;IAChD;EACJ;EACA2B,MAAMA,CAAC5B,KAAK,EAAE;IACV,OAAOJ,QAAQ,CAACY,MAAM,CAAC,IAAI,CAACjB,CAAC,GAAGW,IAAI,CAACC,GAAG,CAACH,KAAK,CAAC,GAAG,IAAI,CAACR,CAAC,GAAGU,IAAI,CAACE,GAAG,CAACJ,KAAK,CAAC,EAAE,IAAI,CAACT,CAAC,GAAGW,IAAI,CAACE,GAAG,CAACJ,KAAK,CAAC,GAAG,IAAI,CAACR,CAAC,GAAGU,IAAI,CAACC,GAAG,CAACH,KAAK,CAAC,EAAEV,MAAM,CAACG,CAAC,CAAC;EAC9I;EACAoC,KAAKA,CAACC,CAAC,EAAE;IACL,IAAI,CAACvC,CAAC,GAAGuC,CAAC,CAACvC,CAAC;IACZ,IAAI,CAACC,CAAC,GAAGsC,CAAC,CAACtC,CAAC;IACZ,MAAMuC,GAAG,GAAGD,CAAC;IACb,IAAI,CAACrC,CAAC,GAAGsC,GAAG,CAACtC,CAAC,GAAGsC,GAAG,CAACtC,CAAC,GAAGH,MAAM,CAACG,CAAC;EACrC;EACA0B,GAAGA,CAACJ,CAAC,EAAE;IACH,OAAOnB,QAAQ,CAACY,MAAM,CAAC,IAAI,CAACjB,CAAC,GAAGwB,CAAC,CAACxB,CAAC,EAAE,IAAI,CAACC,CAAC,GAAGuB,CAAC,CAACvB,CAAC,EAAE,IAAI,CAACC,CAAC,GAAGsB,CAAC,CAACtB,CAAC,CAAC;EACpE;EACAuC,OAAOA,CAACjB,CAAC,EAAE;IACP,IAAI,CAACxB,CAAC,IAAIwB,CAAC,CAACxB,CAAC;IACb,IAAI,CAACC,CAAC,IAAIuB,CAAC,CAACvB,CAAC;IACb,IAAI,CAACC,CAAC,IAAIsB,CAAC,CAACtB,CAAC;EACjB;AACJ;AACA,OAAO,MAAMwC,MAAM,SAASrC,QAAQ,CAAC;EACjCC,WAAWA,CAACC,SAAS,EAAEN,CAAC,EAAE;IACtB,KAAK,CAACM,SAAS,EAAEN,CAAC,EAAEF,MAAM,CAACG,CAAC,CAAC;EACjC;EACA,WAAWH,MAAMA,CAAA,EAAG;IAChB,OAAO2C,MAAM,CAACzB,MAAM,CAAClB,MAAM,CAACC,CAAC,EAAED,MAAM,CAACE,CAAC,CAAC;EAC5C;EACA,OAAOoB,KAAKA,CAACC,MAAM,EAAE;IACjB,OAAOoB,MAAM,CAACzB,MAAM,CAACK,MAAM,CAACtB,CAAC,EAAEsB,MAAM,CAACrB,CAAC,CAAC;EAC5C;EACA,OAAOgB,MAAMA,CAACjB,CAAC,EAAEC,CAAC,EAAE;IAChB,OAAO,IAAIyC,MAAM,CAAC1C,CAAC,EAAEC,CAAC,CAAC;EAC3B;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { PolygonDrawerBase } from \"./PolygonDrawerBase\";\nexport class TriangleDrawer extends PolygonDrawerBase {\n  getCenter(particle, radius) {\n    return {\n      x: -radius,\n      y: radius / 1.66\n    };\n  }\n  getSidesCount() {\n    return 3;\n  }\n  getSidesData(particle, radius) {\n    return {\n      count: {\n        denominator: 2,\n        numerator: 3\n      },\n      length: radius * 2\n    };\n  }\n}", "map": {"version": 3, "names": ["PolygonDrawerBase", "TriangleDrawer", "getCenter", "particle", "radius", "x", "y", "getSidesCount", "getSidesData", "count", "denominator", "numerator", "length"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/tsparticles-shape-polygon/esm/TriangleDrawer.js"], "sourcesContent": ["import { PolygonDrawerBase } from \"./PolygonDrawerBase\";\nexport class TriangleDrawer extends PolygonDrawerBase {\n    getCenter(particle, radius) {\n        return {\n            x: -radius,\n            y: radius / 1.66,\n        };\n    }\n    getSidesCount() {\n        return 3;\n    }\n    getSidesData(particle, radius) {\n        return {\n            count: {\n                denominator: 2,\n                numerator: 3,\n            },\n            length: radius * 2,\n        };\n    }\n}\n"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,qBAAqB;AACvD,OAAO,MAAMC,cAAc,SAASD,iBAAiB,CAAC;EAClDE,SAASA,CAACC,QAAQ,EAAEC,MAAM,EAAE;IACxB,OAAO;MACHC,CAAC,EAAE,CAACD,MAAM;MACVE,CAAC,EAAEF,MAAM,GAAG;IAChB,CAAC;EACL;EACAG,aAAaA,CAAA,EAAG;IACZ,OAAO,CAAC;EACZ;EACAC,YAAYA,CAACL,QAAQ,EAAEC,MAAM,EAAE;IAC3B,OAAO;MACHK,KAAK,EAAE;QACHC,WAAW,EAAE,CAAC;QACdC,SAAS,EAAE;MACf,CAAC;MACDC,MAAM,EAAER,MAAM,GAAG;IACrB,CAAC;EACL;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
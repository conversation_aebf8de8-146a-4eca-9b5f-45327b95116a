{"ast": null, "code": "import { circleBounce, circleBounceDataFromParticle, clamp, getDistance } from \"../../../Utils\";\nimport { ParticlesInteractorBase } from \"../../../Core\";\n\nfunction bounce(p1, p2) {\n  circleBounce(circleBounceDataFromParticle(p1), circleBounceDataFromParticle(p2));\n}\n\nfunction destroy(p1, p2) {\n  if (!p1.unbreakable && !p2.unbreakable) {\n    bounce(p1, p2);\n  }\n\n  if (p1.getRadius() === undefined && p2.getRadius() !== undefined) {\n    p1.destroy();\n  } else if (p1.getRadius() !== undefined && p2.getRadius() === undefined) {\n    p2.destroy();\n  } else if (p1.getRadius() !== undefined && p2.getRadius() !== undefined) {\n    if (p1.getRadius() >= p2.getRadius()) {\n      p2.destroy();\n    } else {\n      p1.destroy();\n    }\n  }\n}\n\nexport class Collider extends ParticlesInteractorBase {\n  constructor(container) {\n    super(container);\n  }\n\n  isEnabled(particle) {\n    return particle.options.collisions.enable;\n  }\n\n  reset() {}\n\n  async interact(p1) {\n    const container = this.container;\n    const pos1 = p1.getPosition();\n    const radius1 = p1.getRadius();\n    const query = container.particles.quadTree.queryCircle(pos1, radius1 * 2);\n\n    for (const p2 of query) {\n      if (p1 === p2 || !p2.options.collisions.enable || p1.options.collisions.mode !== p2.options.collisions.mode || p2.destroyed || p2.spawning) {\n        continue;\n      }\n\n      const pos2 = p2.getPosition();\n\n      if (Math.round(pos1.z) !== Math.round(pos2.z)) {\n        continue;\n      }\n\n      const dist = getDistance(pos1, pos2);\n      const radius2 = p2.getRadius();\n      const distP = radius1 + radius2;\n\n      if (dist <= distP) {\n        this.resolveCollision(p1, p2);\n      }\n    }\n  }\n\n  resolveCollision(p1, p2) {\n    switch (p1.options.collisions.mode) {\n      case \"absorb\":\n        {\n          this.absorb(p1, p2);\n          break;\n        }\n\n      case \"bounce\":\n        {\n          bounce(p1, p2);\n          break;\n        }\n\n      case \"destroy\":\n        {\n          destroy(p1, p2);\n          break;\n        }\n    }\n  }\n\n  absorb(p1, p2) {\n    const container = this.container;\n    const fps = container.fpsLimit / 1000;\n\n    if (p1.getRadius() === undefined && p2.getRadius() !== undefined) {\n      p1.destroy();\n    } else if (p1.getRadius() !== undefined && p2.getRadius() === undefined) {\n      p2.destroy();\n    } else if (p1.getRadius() !== undefined && p2.getRadius() !== undefined) {\n      if (p1.getRadius() >= p2.getRadius()) {\n        const factor = clamp(p1.getRadius() / p2.getRadius(), 0, p2.getRadius()) * fps;\n        p1.size.value += factor;\n        p2.size.value -= factor;\n\n        if (p2.getRadius() <= container.retina.pixelRatio) {\n          p2.size.value = 0;\n          p2.destroy();\n        }\n      } else {\n        const factor = clamp(p2.getRadius() / p1.getRadius(), 0, p1.getRadius()) * fps;\n        p1.size.value -= factor;\n        p2.size.value += factor;\n\n        if (p1.getRadius() <= container.retina.pixelRatio) {\n          p1.size.value = 0;\n          p1.destroy();\n        }\n      }\n    }\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Interactions/Particles/Collisions/Collider.js"], "names": ["circleBounce", "circleBounceDataFromParticle", "clamp", "getDistance", "ParticlesInteractorBase", "bounce", "p1", "p2", "destroy", "unbreakable", "getRadius", "undefined", "Collider", "constructor", "container", "isEnabled", "particle", "options", "collisions", "enable", "reset", "interact", "pos1", "getPosition", "radius1", "query", "particles", "quadTree", "queryCircle", "mode", "destroyed", "spawning", "pos2", "Math", "round", "z", "dist", "radius2", "distP", "resolveCollision", "absorb", "fps", "fpsLimit", "factor", "size", "value", "retina", "pixelRatio"], "mappings": "AAAA,SAASA,YAAT,EAAuBC,4BAAvB,EAAqDC,KAArD,EAA4DC,WAA5D,QAA+E,gBAA/E;AACA,SAASC,uBAAT,QAAwC,eAAxC;;AACA,SAASC,MAAT,CAAgBC,EAAhB,EAAoBC,EAApB,EAAwB;AACpBP,EAAAA,YAAY,CAACC,4BAA4B,CAACK,EAAD,CAA7B,EAAmCL,4BAA4B,CAACM,EAAD,CAA/D,CAAZ;AACH;;AACD,SAASC,OAAT,CAAiBF,EAAjB,EAAqBC,EAArB,EAAyB;AACrB,MAAI,CAACD,EAAE,CAACG,WAAJ,IAAmB,CAACF,EAAE,CAACE,WAA3B,EAAwC;AACpCJ,IAAAA,MAAM,CAACC,EAAD,EAAKC,EAAL,CAAN;AACH;;AACD,MAAID,EAAE,CAACI,SAAH,OAAmBC,SAAnB,IAAgCJ,EAAE,CAACG,SAAH,OAAmBC,SAAvD,EAAkE;AAC9DL,IAAAA,EAAE,CAACE,OAAH;AACH,GAFD,MAGK,IAAIF,EAAE,CAACI,SAAH,OAAmBC,SAAnB,IAAgCJ,EAAE,CAACG,SAAH,OAAmBC,SAAvD,EAAkE;AACnEJ,IAAAA,EAAE,CAACC,OAAH;AACH,GAFI,MAGA,IAAIF,EAAE,CAACI,SAAH,OAAmBC,SAAnB,IAAgCJ,EAAE,CAACG,SAAH,OAAmBC,SAAvD,EAAkE;AACnE,QAAIL,EAAE,CAACI,SAAH,MAAkBH,EAAE,CAACG,SAAH,EAAtB,EAAsC;AAClCH,MAAAA,EAAE,CAACC,OAAH;AACH,KAFD,MAGK;AACDF,MAAAA,EAAE,CAACE,OAAH;AACH;AACJ;AACJ;;AACD,OAAO,MAAMI,QAAN,SAAuBR,uBAAvB,CAA+C;AAClDS,EAAAA,WAAW,CAACC,SAAD,EAAY;AACnB,UAAMA,SAAN;AACH;;AACDC,EAAAA,SAAS,CAACC,QAAD,EAAW;AAChB,WAAOA,QAAQ,CAACC,OAAT,CAAiBC,UAAjB,CAA4BC,MAAnC;AACH;;AACDC,EAAAA,KAAK,GAAG,CACP;;AACa,QAARC,QAAQ,CAACf,EAAD,EAAK;AACf,UAAMQ,SAAS,GAAG,KAAKA,SAAvB;AACA,UAAMQ,IAAI,GAAGhB,EAAE,CAACiB,WAAH,EAAb;AACA,UAAMC,OAAO,GAAGlB,EAAE,CAACI,SAAH,EAAhB;AACA,UAAMe,KAAK,GAAGX,SAAS,CAACY,SAAV,CAAoBC,QAApB,CAA6BC,WAA7B,CAAyCN,IAAzC,EAA+CE,OAAO,GAAG,CAAzD,CAAd;;AACA,SAAK,MAAMjB,EAAX,IAAiBkB,KAAjB,EAAwB;AACpB,UAAInB,EAAE,KAAKC,EAAP,IACA,CAACA,EAAE,CAACU,OAAH,CAAWC,UAAX,CAAsBC,MADvB,IAEAb,EAAE,CAACW,OAAH,CAAWC,UAAX,CAAsBW,IAAtB,KAA+BtB,EAAE,CAACU,OAAH,CAAWC,UAAX,CAAsBW,IAFrD,IAGAtB,EAAE,CAACuB,SAHH,IAIAvB,EAAE,CAACwB,QAJP,EAIiB;AACb;AACH;;AACD,YAAMC,IAAI,GAAGzB,EAAE,CAACgB,WAAH,EAAb;;AACA,UAAIU,IAAI,CAACC,KAAL,CAAWZ,IAAI,CAACa,CAAhB,MAAuBF,IAAI,CAACC,KAAL,CAAWF,IAAI,CAACG,CAAhB,CAA3B,EAA+C;AAC3C;AACH;;AACD,YAAMC,IAAI,GAAGjC,WAAW,CAACmB,IAAD,EAAOU,IAAP,CAAxB;AACA,YAAMK,OAAO,GAAG9B,EAAE,CAACG,SAAH,EAAhB;AACA,YAAM4B,KAAK,GAAGd,OAAO,GAAGa,OAAxB;;AACA,UAAID,IAAI,IAAIE,KAAZ,EAAmB;AACf,aAAKC,gBAAL,CAAsBjC,EAAtB,EAA0BC,EAA1B;AACH;AACJ;AACJ;;AACDgC,EAAAA,gBAAgB,CAACjC,EAAD,EAAKC,EAAL,EAAS;AACrB,YAAQD,EAAE,CAACW,OAAH,CAAWC,UAAX,CAAsBW,IAA9B;AACI,WAAK,QAAL;AAAe;AACX,eAAKW,MAAL,CAAYlC,EAAZ,EAAgBC,EAAhB;AACA;AACH;;AACD,WAAK,QAAL;AAAe;AACXF,UAAAA,MAAM,CAACC,EAAD,EAAKC,EAAL,CAAN;AACA;AACH;;AACD,WAAK,SAAL;AAAgB;AACZC,UAAAA,OAAO,CAACF,EAAD,EAAKC,EAAL,CAAP;AACA;AACH;AAZL;AAcH;;AACDiC,EAAAA,MAAM,CAAClC,EAAD,EAAKC,EAAL,EAAS;AACX,UAAMO,SAAS,GAAG,KAAKA,SAAvB;AACA,UAAM2B,GAAG,GAAG3B,SAAS,CAAC4B,QAAV,GAAqB,IAAjC;;AACA,QAAIpC,EAAE,CAACI,SAAH,OAAmBC,SAAnB,IAAgCJ,EAAE,CAACG,SAAH,OAAmBC,SAAvD,EAAkE;AAC9DL,MAAAA,EAAE,CAACE,OAAH;AACH,KAFD,MAGK,IAAIF,EAAE,CAACI,SAAH,OAAmBC,SAAnB,IAAgCJ,EAAE,CAACG,SAAH,OAAmBC,SAAvD,EAAkE;AACnEJ,MAAAA,EAAE,CAACC,OAAH;AACH,KAFI,MAGA,IAAIF,EAAE,CAACI,SAAH,OAAmBC,SAAnB,IAAgCJ,EAAE,CAACG,SAAH,OAAmBC,SAAvD,EAAkE;AACnE,UAAIL,EAAE,CAACI,SAAH,MAAkBH,EAAE,CAACG,SAAH,EAAtB,EAAsC;AAClC,cAAMiC,MAAM,GAAGzC,KAAK,CAACI,EAAE,CAACI,SAAH,KAAiBH,EAAE,CAACG,SAAH,EAAlB,EAAkC,CAAlC,EAAqCH,EAAE,CAACG,SAAH,EAArC,CAAL,GAA4D+B,GAA3E;AACAnC,QAAAA,EAAE,CAACsC,IAAH,CAAQC,KAAR,IAAiBF,MAAjB;AACApC,QAAAA,EAAE,CAACqC,IAAH,CAAQC,KAAR,IAAiBF,MAAjB;;AACA,YAAIpC,EAAE,CAACG,SAAH,MAAkBI,SAAS,CAACgC,MAAV,CAAiBC,UAAvC,EAAmD;AAC/CxC,UAAAA,EAAE,CAACqC,IAAH,CAAQC,KAAR,GAAgB,CAAhB;AACAtC,UAAAA,EAAE,CAACC,OAAH;AACH;AACJ,OARD,MASK;AACD,cAAMmC,MAAM,GAAGzC,KAAK,CAACK,EAAE,CAACG,SAAH,KAAiBJ,EAAE,CAACI,SAAH,EAAlB,EAAkC,CAAlC,EAAqCJ,EAAE,CAACI,SAAH,EAArC,CAAL,GAA4D+B,GAA3E;AACAnC,QAAAA,EAAE,CAACsC,IAAH,CAAQC,KAAR,IAAiBF,MAAjB;AACApC,QAAAA,EAAE,CAACqC,IAAH,CAAQC,KAAR,IAAiBF,MAAjB;;AACA,YAAIrC,EAAE,CAACI,SAAH,MAAkBI,SAAS,CAACgC,MAAV,CAAiBC,UAAvC,EAAmD;AAC/CzC,UAAAA,EAAE,CAACsC,IAAH,CAAQC,KAAR,GAAgB,CAAhB;AACAvC,UAAAA,EAAE,CAACE,OAAH;AACH;AACJ;AACJ;AACJ;;AA/EiD", "sourcesContent": ["import { circleBounce, circleBounceDataFromParticle, clamp, getDistance } from \"../../../Utils\";\nimport { ParticlesInteractorBase } from \"../../../Core\";\nfunction bounce(p1, p2) {\n    circleBounce(circleBounceDataFromParticle(p1), circleBounceDataFromParticle(p2));\n}\nfunction destroy(p1, p2) {\n    if (!p1.unbreakable && !p2.unbreakable) {\n        bounce(p1, p2);\n    }\n    if (p1.getRadius() === undefined && p2.getRadius() !== undefined) {\n        p1.destroy();\n    }\n    else if (p1.getRadius() !== undefined && p2.getRadius() === undefined) {\n        p2.destroy();\n    }\n    else if (p1.getRadius() !== undefined && p2.getRadius() !== undefined) {\n        if (p1.getRadius() >= p2.getRadius()) {\n            p2.destroy();\n        }\n        else {\n            p1.destroy();\n        }\n    }\n}\nexport class Collider extends ParticlesInteractorBase {\n    constructor(container) {\n        super(container);\n    }\n    isEnabled(particle) {\n        return particle.options.collisions.enable;\n    }\n    reset() {\n    }\n    async interact(p1) {\n        const container = this.container;\n        const pos1 = p1.getPosition();\n        const radius1 = p1.getRadius();\n        const query = container.particles.quadTree.queryCircle(pos1, radius1 * 2);\n        for (const p2 of query) {\n            if (p1 === p2 ||\n                !p2.options.collisions.enable ||\n                p1.options.collisions.mode !== p2.options.collisions.mode ||\n                p2.destroyed ||\n                p2.spawning) {\n                continue;\n            }\n            const pos2 = p2.getPosition();\n            if (Math.round(pos1.z) !== Math.round(pos2.z)) {\n                continue;\n            }\n            const dist = getDistance(pos1, pos2);\n            const radius2 = p2.getRadius();\n            const distP = radius1 + radius2;\n            if (dist <= distP) {\n                this.resolveCollision(p1, p2);\n            }\n        }\n    }\n    resolveCollision(p1, p2) {\n        switch (p1.options.collisions.mode) {\n            case \"absorb\": {\n                this.absorb(p1, p2);\n                break;\n            }\n            case \"bounce\": {\n                bounce(p1, p2);\n                break;\n            }\n            case \"destroy\": {\n                destroy(p1, p2);\n                break;\n            }\n        }\n    }\n    absorb(p1, p2) {\n        const container = this.container;\n        const fps = container.fpsLimit / 1000;\n        if (p1.getRadius() === undefined && p2.getRadius() !== undefined) {\n            p1.destroy();\n        }\n        else if (p1.getRadius() !== undefined && p2.getRadius() === undefined) {\n            p2.destroy();\n        }\n        else if (p1.getRadius() !== undefined && p2.getRadius() !== undefined) {\n            if (p1.getRadius() >= p2.getRadius()) {\n                const factor = clamp(p1.getRadius() / p2.getRadius(), 0, p2.getRadius()) * fps;\n                p1.size.value += factor;\n                p2.size.value -= factor;\n                if (p2.getRadius() <= container.retina.pixelRatio) {\n                    p2.size.value = 0;\n                    p2.destroy();\n                }\n            }\n            else {\n                const factor = clamp(p2.getRadius() / p1.getRadius(), 0, p1.getRadius()) * fps;\n                p1.size.value -= factor;\n                p2.size.value += factor;\n                if (p1.getRadius() <= container.retina.pixelRatio) {\n                    p1.size.value = 0;\n                    p1.destroy();\n                }\n            }\n        }\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
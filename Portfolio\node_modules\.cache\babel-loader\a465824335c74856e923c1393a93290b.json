{"ast": null, "code": "export default function buildFormatLongFn(args) {\n  return function () {\n    var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {}; // TODO: Remove String()\n\n    var width = options.width ? String(options.width) : args.defaultWidth;\n    var format = args.formats[width] || args.formats[args.defaultWidth];\n    return format;\n  };\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/date-fns/esm/locale/_lib/buildFormatLongFn/index.js"], "names": ["buildFormatLongFn", "args", "options", "arguments", "length", "undefined", "width", "String", "defaultWidth", "format", "formats"], "mappings": "AAAA,eAAe,SAASA,iBAAT,CAA2BC,IAA3B,EAAiC;AAC9C,SAAO,YAAY;AACjB,QAAIC,OAAO,GAAGC,SAAS,CAACC,MAAV,GAAmB,CAAnB,IAAwBD,SAAS,CAAC,CAAD,CAAT,KAAiBE,SAAzC,GAAqDF,SAAS,CAAC,CAAD,CAA9D,GAAoE,EAAlF,CADiB,CAEjB;;AACA,QAAIG,KAAK,GAAGJ,OAAO,CAACI,KAAR,GAAgBC,MAAM,CAACL,OAAO,CAACI,KAAT,CAAtB,GAAwCL,IAAI,CAACO,YAAzD;AACA,QAAIC,MAAM,GAAGR,IAAI,CAACS,OAAL,CAAaJ,KAAb,KAAuBL,IAAI,CAACS,OAAL,CAAaT,IAAI,CAACO,YAAlB,CAApC;AACA,WAAOC,MAAP;AACD,GAND;AAOD", "sourcesContent": ["export default function buildFormatLongFn(args) {\n  return function () {\n    var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    // TODO: Remove String()\n    var width = options.width ? String(options.width) : args.defaultWidth;\n    var format = args.formats[width] || args.formats[args.defaultWidth];\n    return format;\n  };\n}"]}, "metadata": {}, "sourceType": "module"}
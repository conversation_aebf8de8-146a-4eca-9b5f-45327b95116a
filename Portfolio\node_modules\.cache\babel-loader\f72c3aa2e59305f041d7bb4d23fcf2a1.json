{"ast": null, "code": "import { Repulser } from \"./Repulser\";\nexport async function loadExternalRepulseInteraction(engine) {\n  await engine.addInteractor(\"externalRepulse\", container => new Repulser(container));\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Interactions/External/Repulse/index.js"], "names": ["<PERSON><PERSON><PERSON>", "loadExternalRepulseInteraction", "engine", "addInteractor", "container"], "mappings": "AAAA,SAASA,QAAT,QAAyB,YAAzB;AACA,OAAO,eAAeC,8BAAf,CAA8CC,MAA9C,EAAsD;AACzD,QAAMA,MAAM,CAACC,aAAP,CAAqB,iBAArB,EAAyCC,SAAD,IAAe,IAAIJ,QAAJ,CAAaI,SAAb,CAAvD,CAAN;AACH", "sourcesContent": ["import { Repulser } from \"./Repulser\";\nexport async function loadExternalRepulseInteraction(engine) {\n    await engine.addInteractor(\"externalRepulse\", (container) => new Repulser(container));\n}\n"]}, "metadata": {}, "sourceType": "module"}
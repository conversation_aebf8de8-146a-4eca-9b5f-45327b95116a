{"ast": null, "code": "import { clamp, getRangeMax, getRange<PERSON>in, getRangeValue, randomInRange } from \"../../Utils\";\n\nfunction checkDestroy(particle, value, minValue, maxValue) {\n  switch (particle.options.opacity.animation.destroy) {\n    case \"max\":\n      if (value >= maxValue) {\n        particle.destroy();\n      }\n\n      break;\n\n    case \"min\":\n      if (value <= minValue) {\n        particle.destroy();\n      }\n\n      break;\n  }\n}\n\nfunction updateOpacity(particle, delta) {\n  var _a, _b, _c, _d, _e;\n\n  if (!particle.opacity) {\n    return;\n  }\n\n  const minValue = particle.opacity.min;\n  const maxValue = particle.opacity.max;\n\n  if (particle.destroyed || !particle.opacity.enable || ((_a = particle.opacity.maxLoops) !== null && _a !== void 0 ? _a : 0) > 0 && ((_b = particle.opacity.loops) !== null && _b !== void 0 ? _b : 0) > ((_c = particle.opacity.maxLoops) !== null && _c !== void 0 ? _c : 0)) {\n    return;\n  }\n\n  switch (particle.opacity.status) {\n    case 0:\n      if (particle.opacity.value >= maxValue) {\n        particle.opacity.status = 1;\n\n        if (!particle.opacity.loops) {\n          particle.opacity.loops = 0;\n        }\n\n        particle.opacity.loops++;\n      } else {\n        particle.opacity.value += ((_d = particle.opacity.velocity) !== null && _d !== void 0 ? _d : 0) * delta.factor;\n      }\n\n      break;\n\n    case 1:\n      if (particle.opacity.value <= minValue) {\n        particle.opacity.status = 0;\n\n        if (!particle.opacity.loops) {\n          particle.opacity.loops = 0;\n        }\n\n        particle.opacity.loops++;\n      } else {\n        particle.opacity.value -= ((_e = particle.opacity.velocity) !== null && _e !== void 0 ? _e : 0) * delta.factor;\n      }\n\n      break;\n  }\n\n  checkDestroy(particle, particle.opacity.value, minValue, maxValue);\n\n  if (!particle.destroyed) {\n    particle.opacity.value = clamp(particle.opacity.value, minValue, maxValue);\n  }\n}\n\nexport class OpacityUpdater {\n  constructor(container) {\n    this.container = container;\n  }\n\n  init(particle) {\n    const opacityOptions = particle.options.opacity;\n    particle.opacity = {\n      enable: opacityOptions.animation.enable,\n      max: getRangeMax(opacityOptions.value),\n      min: getRangeMin(opacityOptions.value),\n      value: getRangeValue(opacityOptions.value),\n      loops: 0,\n      maxLoops: getRangeValue(opacityOptions.animation.count)\n    };\n    const opacityAnimation = opacityOptions.animation;\n\n    if (opacityAnimation.enable) {\n      particle.opacity.status = 0;\n      const opacityRange = opacityOptions.value;\n      particle.opacity.min = getRangeMin(opacityRange);\n      particle.opacity.max = getRangeMax(opacityRange);\n\n      switch (opacityAnimation.startValue) {\n        case \"min\":\n          particle.opacity.value = particle.opacity.min;\n          particle.opacity.status = 0;\n          break;\n\n        case \"random\":\n          particle.opacity.value = randomInRange(particle.opacity);\n          particle.opacity.status = Math.random() >= 0.5 ? 0 : 1;\n          break;\n\n        case \"max\":\n        default:\n          particle.opacity.value = particle.opacity.max;\n          particle.opacity.status = 1;\n          break;\n      }\n\n      particle.opacity.velocity = getRangeValue(opacityAnimation.speed) / 100 * this.container.retina.reduceFactor;\n\n      if (!opacityAnimation.sync) {\n        particle.opacity.velocity *= Math.random();\n      }\n    }\n  }\n\n  isEnabled(particle) {\n    var _a, _b, _c, _d;\n\n    return !particle.destroyed && !particle.spawning && !!particle.opacity && particle.opacity.enable && (((_a = particle.opacity.maxLoops) !== null && _a !== void 0 ? _a : 0) <= 0 || ((_b = particle.opacity.maxLoops) !== null && _b !== void 0 ? _b : 0) > 0 && ((_c = particle.opacity.loops) !== null && _c !== void 0 ? _c : 0) < ((_d = particle.opacity.maxLoops) !== null && _d !== void 0 ? _d : 0));\n  }\n\n  update(particle, delta) {\n    if (!this.isEnabled(particle)) {\n      return;\n    }\n\n    updateOpacity(particle, delta);\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Updaters/Opacity/OpacityUpdater.js"], "names": ["clamp", "getRangeMax", "getRangeMin", "getRangeValue", "randomInRange", "checkDestroy", "particle", "value", "minValue", "maxValue", "options", "opacity", "animation", "destroy", "updateOpacity", "delta", "_a", "_b", "_c", "_d", "_e", "min", "max", "destroyed", "enable", "max<PERSON><PERSON>s", "loops", "status", "velocity", "factor", "OpacityUpdater", "constructor", "container", "init", "opacityOptions", "count", "opacityAnimation", "opacityRange", "startValue", "Math", "random", "speed", "retina", "reduceFactor", "sync", "isEnabled", "spawning", "update"], "mappings": "AAAA,SAASA,KAAT,EAAgBC,WAAhB,EAA6BC,WAA7B,EAA0CC,aAA1C,EAAyDC,aAAzD,QAA8E,aAA9E;;AACA,SAASC,YAAT,CAAsBC,QAAtB,EAAgCC,KAAhC,EAAuCC,QAAvC,EAAiDC,QAAjD,EAA2D;AACvD,UAAQH,QAAQ,CAACI,OAAT,CAAiBC,OAAjB,CAAyBC,SAAzB,CAAmCC,OAA3C;AACI,SAAK,KAAL;AACI,UAAIN,KAAK,IAAIE,QAAb,EAAuB;AACnBH,QAAAA,QAAQ,CAACO,OAAT;AACH;;AACD;;AACJ,SAAK,KAAL;AACI,UAAIN,KAAK,IAAIC,QAAb,EAAuB;AACnBF,QAAAA,QAAQ,CAACO,OAAT;AACH;;AACD;AAVR;AAYH;;AACD,SAASC,aAAT,CAAuBR,QAAvB,EAAiCS,KAAjC,EAAwC;AACpC,MAAIC,EAAJ,EAAQC,EAAR,EAAYC,EAAZ,EAAgBC,EAAhB,EAAoBC,EAApB;;AACA,MAAI,CAACd,QAAQ,CAACK,OAAd,EAAuB;AACnB;AACH;;AACD,QAAMH,QAAQ,GAAGF,QAAQ,CAACK,OAAT,CAAiBU,GAAlC;AACA,QAAMZ,QAAQ,GAAGH,QAAQ,CAACK,OAAT,CAAiBW,GAAlC;;AACA,MAAIhB,QAAQ,CAACiB,SAAT,IACA,CAACjB,QAAQ,CAACK,OAAT,CAAiBa,MADlB,IAEC,CAAC,CAACR,EAAE,GAAGV,QAAQ,CAACK,OAAT,CAAiBc,QAAvB,MAAqC,IAArC,IAA6CT,EAAE,KAAK,KAAK,CAAzD,GAA6DA,EAA7D,GAAkE,CAAnE,IAAwE,CAAxE,IAA6E,CAAC,CAACC,EAAE,GAAGX,QAAQ,CAACK,OAAT,CAAiBe,KAAvB,MAAkC,IAAlC,IAA0CT,EAAE,KAAK,KAAK,CAAtD,GAA0DA,EAA1D,GAA+D,CAAhE,KAAsE,CAACC,EAAE,GAAGZ,QAAQ,CAACK,OAAT,CAAiBc,QAAvB,MAAqC,IAArC,IAA6CP,EAAE,KAAK,KAAK,CAAzD,GAA6DA,EAA7D,GAAkE,CAAxI,CAFlF,EAE+N;AAC3N;AACH;;AACD,UAAQZ,QAAQ,CAACK,OAAT,CAAiBgB,MAAzB;AACI,SAAK,CAAL;AACI,UAAIrB,QAAQ,CAACK,OAAT,CAAiBJ,KAAjB,IAA0BE,QAA9B,EAAwC;AACpCH,QAAAA,QAAQ,CAACK,OAAT,CAAiBgB,MAAjB,GAA0B,CAA1B;;AACA,YAAI,CAACrB,QAAQ,CAACK,OAAT,CAAiBe,KAAtB,EAA6B;AACzBpB,UAAAA,QAAQ,CAACK,OAAT,CAAiBe,KAAjB,GAAyB,CAAzB;AACH;;AACDpB,QAAAA,QAAQ,CAACK,OAAT,CAAiBe,KAAjB;AACH,OAND,MAOK;AACDpB,QAAAA,QAAQ,CAACK,OAAT,CAAiBJ,KAAjB,IAA0B,CAAC,CAACY,EAAE,GAAGb,QAAQ,CAACK,OAAT,CAAiBiB,QAAvB,MAAqC,IAArC,IAA6CT,EAAE,KAAK,KAAK,CAAzD,GAA6DA,EAA7D,GAAkE,CAAnE,IAAwEJ,KAAK,CAACc,MAAxG;AACH;;AACD;;AACJ,SAAK,CAAL;AACI,UAAIvB,QAAQ,CAACK,OAAT,CAAiBJ,KAAjB,IAA0BC,QAA9B,EAAwC;AACpCF,QAAAA,QAAQ,CAACK,OAAT,CAAiBgB,MAAjB,GAA0B,CAA1B;;AACA,YAAI,CAACrB,QAAQ,CAACK,OAAT,CAAiBe,KAAtB,EAA6B;AACzBpB,UAAAA,QAAQ,CAACK,OAAT,CAAiBe,KAAjB,GAAyB,CAAzB;AACH;;AACDpB,QAAAA,QAAQ,CAACK,OAAT,CAAiBe,KAAjB;AACH,OAND,MAOK;AACDpB,QAAAA,QAAQ,CAACK,OAAT,CAAiBJ,KAAjB,IAA0B,CAAC,CAACa,EAAE,GAAGd,QAAQ,CAACK,OAAT,CAAiBiB,QAAvB,MAAqC,IAArC,IAA6CR,EAAE,KAAK,KAAK,CAAzD,GAA6DA,EAA7D,GAAkE,CAAnE,IAAwEL,KAAK,CAACc,MAAxG;AACH;;AACD;AAxBR;;AA0BAxB,EAAAA,YAAY,CAACC,QAAD,EAAWA,QAAQ,CAACK,OAAT,CAAiBJ,KAA5B,EAAmCC,QAAnC,EAA6CC,QAA7C,CAAZ;;AACA,MAAI,CAACH,QAAQ,CAACiB,SAAd,EAAyB;AACrBjB,IAAAA,QAAQ,CAACK,OAAT,CAAiBJ,KAAjB,GAAyBP,KAAK,CAACM,QAAQ,CAACK,OAAT,CAAiBJ,KAAlB,EAAyBC,QAAzB,EAAmCC,QAAnC,CAA9B;AACH;AACJ;;AACD,OAAO,MAAMqB,cAAN,CAAqB;AACxBC,EAAAA,WAAW,CAACC,SAAD,EAAY;AACnB,SAAKA,SAAL,GAAiBA,SAAjB;AACH;;AACDC,EAAAA,IAAI,CAAC3B,QAAD,EAAW;AACX,UAAM4B,cAAc,GAAG5B,QAAQ,CAACI,OAAT,CAAiBC,OAAxC;AACAL,IAAAA,QAAQ,CAACK,OAAT,GAAmB;AACfa,MAAAA,MAAM,EAAEU,cAAc,CAACtB,SAAf,CAAyBY,MADlB;AAEfF,MAAAA,GAAG,EAAErB,WAAW,CAACiC,cAAc,CAAC3B,KAAhB,CAFD;AAGfc,MAAAA,GAAG,EAAEnB,WAAW,CAACgC,cAAc,CAAC3B,KAAhB,CAHD;AAIfA,MAAAA,KAAK,EAAEJ,aAAa,CAAC+B,cAAc,CAAC3B,KAAhB,CAJL;AAKfmB,MAAAA,KAAK,EAAE,CALQ;AAMfD,MAAAA,QAAQ,EAAEtB,aAAa,CAAC+B,cAAc,CAACtB,SAAf,CAAyBuB,KAA1B;AANR,KAAnB;AAQA,UAAMC,gBAAgB,GAAGF,cAAc,CAACtB,SAAxC;;AACA,QAAIwB,gBAAgB,CAACZ,MAArB,EAA6B;AACzBlB,MAAAA,QAAQ,CAACK,OAAT,CAAiBgB,MAAjB,GAA0B,CAA1B;AACA,YAAMU,YAAY,GAAGH,cAAc,CAAC3B,KAApC;AACAD,MAAAA,QAAQ,CAACK,OAAT,CAAiBU,GAAjB,GAAuBnB,WAAW,CAACmC,YAAD,CAAlC;AACA/B,MAAAA,QAAQ,CAACK,OAAT,CAAiBW,GAAjB,GAAuBrB,WAAW,CAACoC,YAAD,CAAlC;;AACA,cAAQD,gBAAgB,CAACE,UAAzB;AACI,aAAK,KAAL;AACIhC,UAAAA,QAAQ,CAACK,OAAT,CAAiBJ,KAAjB,GAAyBD,QAAQ,CAACK,OAAT,CAAiBU,GAA1C;AACAf,UAAAA,QAAQ,CAACK,OAAT,CAAiBgB,MAAjB,GAA0B,CAA1B;AACA;;AACJ,aAAK,QAAL;AACIrB,UAAAA,QAAQ,CAACK,OAAT,CAAiBJ,KAAjB,GAAyBH,aAAa,CAACE,QAAQ,CAACK,OAAV,CAAtC;AACAL,UAAAA,QAAQ,CAACK,OAAT,CAAiBgB,MAAjB,GACIY,IAAI,CAACC,MAAL,MAAiB,GAAjB,GAAuB,CAAvB,GAA2B,CAD/B;AAEA;;AACJ,aAAK,KAAL;AACA;AACIlC,UAAAA,QAAQ,CAACK,OAAT,CAAiBJ,KAAjB,GAAyBD,QAAQ,CAACK,OAAT,CAAiBW,GAA1C;AACAhB,UAAAA,QAAQ,CAACK,OAAT,CAAiBgB,MAAjB,GAA0B,CAA1B;AACA;AAdR;;AAgBArB,MAAAA,QAAQ,CAACK,OAAT,CAAiBiB,QAAjB,GACKzB,aAAa,CAACiC,gBAAgB,CAACK,KAAlB,CAAb,GAAwC,GAAzC,GAAgD,KAAKT,SAAL,CAAeU,MAAf,CAAsBC,YAD1E;;AAEA,UAAI,CAACP,gBAAgB,CAACQ,IAAtB,EAA4B;AACxBtC,QAAAA,QAAQ,CAACK,OAAT,CAAiBiB,QAAjB,IAA6BW,IAAI,CAACC,MAAL,EAA7B;AACH;AACJ;AACJ;;AACDK,EAAAA,SAAS,CAACvC,QAAD,EAAW;AAChB,QAAIU,EAAJ,EAAQC,EAAR,EAAYC,EAAZ,EAAgBC,EAAhB;;AACA,WAAQ,CAACb,QAAQ,CAACiB,SAAV,IACJ,CAACjB,QAAQ,CAACwC,QADN,IAEJ,CAAC,CAACxC,QAAQ,CAACK,OAFP,IAGJL,QAAQ,CAACK,OAAT,CAAiBa,MAHb,KAIH,CAAC,CAACR,EAAE,GAAGV,QAAQ,CAACK,OAAT,CAAiBc,QAAvB,MAAqC,IAArC,IAA6CT,EAAE,KAAK,KAAK,CAAzD,GAA6DA,EAA7D,GAAkE,CAAnE,KAAyE,CAAzE,IACI,CAAC,CAACC,EAAE,GAAGX,QAAQ,CAACK,OAAT,CAAiBc,QAAvB,MAAqC,IAArC,IAA6CR,EAAE,KAAK,KAAK,CAAzD,GAA6DA,EAA7D,GAAkE,CAAnE,IAAwE,CAAxE,IACG,CAAC,CAACC,EAAE,GAAGZ,QAAQ,CAACK,OAAT,CAAiBe,KAAvB,MAAkC,IAAlC,IAA0CR,EAAE,KAAK,KAAK,CAAtD,GAA0DA,EAA1D,GAA+D,CAAhE,KAAsE,CAACC,EAAE,GAAGb,QAAQ,CAACK,OAAT,CAAiBc,QAAvB,MAAqC,IAArC,IAA6CN,EAAE,KAAK,KAAK,CAAzD,GAA6DA,EAA7D,GAAkE,CAAxI,CANJ,CAAR;AAOH;;AACD4B,EAAAA,MAAM,CAACzC,QAAD,EAAWS,KAAX,EAAkB;AACpB,QAAI,CAAC,KAAK8B,SAAL,CAAevC,QAAf,CAAL,EAA+B;AAC3B;AACH;;AACDQ,IAAAA,aAAa,CAACR,QAAD,EAAWS,KAAX,CAAb;AACH;;AA1DuB", "sourcesContent": ["import { clamp, getRangeMax, getRange<PERSON>in, getRangeValue, randomInRange } from \"../../Utils\";\nfunction checkDestroy(particle, value, minValue, maxValue) {\n    switch (particle.options.opacity.animation.destroy) {\n        case \"max\":\n            if (value >= maxValue) {\n                particle.destroy();\n            }\n            break;\n        case \"min\":\n            if (value <= minValue) {\n                particle.destroy();\n            }\n            break;\n    }\n}\nfunction updateOpacity(particle, delta) {\n    var _a, _b, _c, _d, _e;\n    if (!particle.opacity) {\n        return;\n    }\n    const minValue = particle.opacity.min;\n    const maxValue = particle.opacity.max;\n    if (particle.destroyed ||\n        !particle.opacity.enable ||\n        (((_a = particle.opacity.maxLoops) !== null && _a !== void 0 ? _a : 0) > 0 && ((_b = particle.opacity.loops) !== null && _b !== void 0 ? _b : 0) > ((_c = particle.opacity.maxLoops) !== null && _c !== void 0 ? _c : 0))) {\n        return;\n    }\n    switch (particle.opacity.status) {\n        case 0:\n            if (particle.opacity.value >= maxValue) {\n                particle.opacity.status = 1;\n                if (!particle.opacity.loops) {\n                    particle.opacity.loops = 0;\n                }\n                particle.opacity.loops++;\n            }\n            else {\n                particle.opacity.value += ((_d = particle.opacity.velocity) !== null && _d !== void 0 ? _d : 0) * delta.factor;\n            }\n            break;\n        case 1:\n            if (particle.opacity.value <= minValue) {\n                particle.opacity.status = 0;\n                if (!particle.opacity.loops) {\n                    particle.opacity.loops = 0;\n                }\n                particle.opacity.loops++;\n            }\n            else {\n                particle.opacity.value -= ((_e = particle.opacity.velocity) !== null && _e !== void 0 ? _e : 0) * delta.factor;\n            }\n            break;\n    }\n    checkDestroy(particle, particle.opacity.value, minValue, maxValue);\n    if (!particle.destroyed) {\n        particle.opacity.value = clamp(particle.opacity.value, minValue, maxValue);\n    }\n}\nexport class OpacityUpdater {\n    constructor(container) {\n        this.container = container;\n    }\n    init(particle) {\n        const opacityOptions = particle.options.opacity;\n        particle.opacity = {\n            enable: opacityOptions.animation.enable,\n            max: getRangeMax(opacityOptions.value),\n            min: getRangeMin(opacityOptions.value),\n            value: getRangeValue(opacityOptions.value),\n            loops: 0,\n            maxLoops: getRangeValue(opacityOptions.animation.count),\n        };\n        const opacityAnimation = opacityOptions.animation;\n        if (opacityAnimation.enable) {\n            particle.opacity.status = 0;\n            const opacityRange = opacityOptions.value;\n            particle.opacity.min = getRangeMin(opacityRange);\n            particle.opacity.max = getRangeMax(opacityRange);\n            switch (opacityAnimation.startValue) {\n                case \"min\":\n                    particle.opacity.value = particle.opacity.min;\n                    particle.opacity.status = 0;\n                    break;\n                case \"random\":\n                    particle.opacity.value = randomInRange(particle.opacity);\n                    particle.opacity.status =\n                        Math.random() >= 0.5 ? 0 : 1;\n                    break;\n                case \"max\":\n                default:\n                    particle.opacity.value = particle.opacity.max;\n                    particle.opacity.status = 1;\n                    break;\n            }\n            particle.opacity.velocity =\n                (getRangeValue(opacityAnimation.speed) / 100) * this.container.retina.reduceFactor;\n            if (!opacityAnimation.sync) {\n                particle.opacity.velocity *= Math.random();\n            }\n        }\n    }\n    isEnabled(particle) {\n        var _a, _b, _c, _d;\n        return (!particle.destroyed &&\n            !particle.spawning &&\n            !!particle.opacity &&\n            particle.opacity.enable &&\n            (((_a = particle.opacity.maxLoops) !== null && _a !== void 0 ? _a : 0) <= 0 ||\n                (((_b = particle.opacity.maxLoops) !== null && _b !== void 0 ? _b : 0) > 0 &&\n                    ((_c = particle.opacity.loops) !== null && _c !== void 0 ? _c : 0) < ((_d = particle.opacity.maxLoops) !== null && _d !== void 0 ? _d : 0))));\n    }\n    update(particle, delta) {\n        if (!this.isEnabled(particle)) {\n            return;\n        }\n        updateOpacity(particle, delta);\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
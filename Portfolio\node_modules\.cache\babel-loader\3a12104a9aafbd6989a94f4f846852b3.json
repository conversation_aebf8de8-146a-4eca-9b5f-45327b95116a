{"ast": null, "code": "import { ImageDrawer } from \"./ImageDrawer\";\nexport async function loadImageShape(engine) {\n  const imageDrawer = new ImageDrawer();\n  await engine.addShape(\"image\", imageDrawer);\n  await engine.addShape(\"images\", imageDrawer);\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Shapes/Image/index.js"], "names": ["ImageDrawer", "loadImageShape", "engine", "imageDrawer", "addShape"], "mappings": "AAAA,SAASA,WAAT,QAA4B,eAA5B;AACA,OAAO,eAAeC,cAAf,CAA8BC,MAA9B,EAAsC;AACzC,QAAMC,WAAW,GAAG,IAAIH,WAAJ,EAApB;AACA,QAAME,MAAM,CAACE,QAAP,CAAgB,OAAhB,EAAyBD,WAAzB,CAAN;AACA,QAAMD,MAAM,CAACE,QAAP,CAAgB,QAAhB,EAA0BD,WAA1B,CAAN;AACH", "sourcesContent": ["import { ImageDrawer } from \"./ImageDrawer\";\nexport async function loadImageShape(engine) {\n    const imageDrawer = new ImageDrawer();\n    await engine.addShape(\"image\", imageDrawer);\n    await engine.addShape(\"images\", imageDrawer);\n}\n"]}, "metadata": {}, "sourceType": "module"}
{"ast": null, "code": "import { SplitFactor } from \"./SplitFactor\";\nimport { SplitRate } from \"./SplitRate\";\nimport { deepExtend } from \"../../../../Utils\";\nexport class Split {\n  constructor() {\n    this.count = 1;\n    this.factor = new SplitFactor();\n    this.rate = new SplitRate();\n    this.sizeOffset = true;\n  }\n\n  load(data) {\n    if (!data) {\n      return;\n    }\n\n    if (data.count !== undefined) {\n      this.count = data.count;\n    }\n\n    this.factor.load(data.factor);\n    this.rate.load(data.rate);\n\n    if (data.particles !== undefined) {\n      this.particles = deepExtend({}, data.particles);\n    }\n\n    if (data.sizeOffset !== undefined) {\n      this.sizeOffset = data.sizeOffset;\n    }\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Options/Classes/Particles/Destroy/Split.js"], "names": ["SplitFactor", "SplitRate", "deepExtend", "Split", "constructor", "count", "factor", "rate", "sizeOffset", "load", "data", "undefined", "particles"], "mappings": "AAAA,SAASA,WAAT,QAA4B,eAA5B;AACA,SAASC,SAAT,QAA0B,aAA1B;AACA,SAASC,UAAT,QAA2B,mBAA3B;AACA,OAAO,MAAMC,KAAN,CAAY;AACfC,EAAAA,WAAW,GAAG;AACV,SAAKC,KAAL,GAAa,CAAb;AACA,SAAKC,MAAL,GAAc,IAAIN,WAAJ,EAAd;AACA,SAAKO,IAAL,GAAY,IAAIN,SAAJ,EAAZ;AACA,SAAKO,UAAL,GAAkB,IAAlB;AACH;;AACDC,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,QAAI,CAACA,IAAL,EAAW;AACP;AACH;;AACD,QAAIA,IAAI,CAACL,KAAL,KAAeM,SAAnB,EAA8B;AAC1B,WAAKN,KAAL,GAAaK,IAAI,CAACL,KAAlB;AACH;;AACD,SAAKC,MAAL,CAAYG,IAAZ,CAAiBC,IAAI,CAACJ,MAAtB;AACA,SAAKC,IAAL,CAAUE,IAAV,CAAeC,IAAI,CAACH,IAApB;;AACA,QAAIG,IAAI,CAACE,SAAL,KAAmBD,SAAvB,EAAkC;AAC9B,WAAKC,SAAL,GAAiBV,UAAU,CAAC,EAAD,EAAKQ,IAAI,CAACE,SAAV,CAA3B;AACH;;AACD,QAAIF,IAAI,CAACF,UAAL,KAAoBG,SAAxB,EAAmC;AAC/B,WAAKH,UAAL,GAAkBE,IAAI,CAACF,UAAvB;AACH;AACJ;;AAtBc", "sourcesContent": ["import { SplitFactor } from \"./SplitFactor\";\nimport { SplitRate } from \"./SplitRate\";\nimport { deepExtend } from \"../../../../Utils\";\nexport class Split {\n    constructor() {\n        this.count = 1;\n        this.factor = new SplitFactor();\n        this.rate = new SplitRate();\n        this.sizeOffset = true;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.count !== undefined) {\n            this.count = data.count;\n        }\n        this.factor.load(data.factor);\n        this.rate.load(data.rate);\n        if (data.particles !== undefined) {\n            this.particles = deepExtend({}, data.particles);\n        }\n        if (data.sizeOffset !== undefined) {\n            this.sizeOffset = data.sizeOffset;\n        }\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
{"ast": null, "code": "import { StrokeColorUpdater } from \"./StrokeColorUpdater.js\";\nexport async function loadStrokeColorUpdater(engine, refresh = true) {\n  await engine.addParticleUpdater(\"strokeColor\", container => {\n    return Promise.resolve(new StrokeColorUpdater(container));\n  }, refresh);\n}", "map": {"version": 3, "names": ["StrokeColorUpdater", "loadStrokeColorUpdater", "engine", "refresh", "addParticleUpdater", "container", "Promise", "resolve"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/updater-stroke-color/browser/index.js"], "sourcesContent": ["import { StrokeColorUpdater } from \"./StrokeColorUpdater.js\";\nexport async function loadStrokeColorUpdater(engine, refresh = true) {\n    await engine.addParticleUpdater(\"strokeColor\", container => {\n        return Promise.resolve(new StrokeColorUpdater(container));\n    }, refresh);\n}\n"], "mappings": "AAAA,SAASA,kBAAkB,QAAQ,yBAAyB;AAC5D,OAAO,eAAeC,sBAAsBA,CAACC,MAAM,EAAEC,OAAO,GAAG,IAAI,EAAE;EACjE,MAAMD,MAAM,CAACE,kBAAkB,CAAC,aAAa,EAAEC,SAAS,IAAI;IACxD,OAAOC,OAAO,CAACC,OAAO,CAAC,IAAIP,kBAAkB,CAACK,SAAS,CAAC,CAAC;EAC7D,CAAC,EAAEF,OAAO,CAAC;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
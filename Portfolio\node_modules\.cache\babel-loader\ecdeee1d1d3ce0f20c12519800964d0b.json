{"ast": null, "code": "export class PolygonMaskLocalSvg {\n  constructor() {\n    this.path = [];\n    this.size = {\n      height: 0,\n      width: 0\n    };\n  }\n\n  load(data) {\n    if (!data) {\n      return;\n    }\n\n    if (data.path !== undefined) {\n      this.path = data.path;\n    }\n\n    if (data.size !== undefined) {\n      if (data.size.width !== undefined) {\n        this.size.width = data.size.width;\n      }\n\n      if (data.size.height !== undefined) {\n        this.size.height = data.size.height;\n      }\n    }\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Plugins/PolygonMask/Options/Classes/PolygonMaskLocalSvg.js"], "names": ["PolygonMaskLocalSvg", "constructor", "path", "size", "height", "width", "load", "data", "undefined"], "mappings": "AAAA,OAAO,MAAMA,mBAAN,CAA0B;AAC7BC,EAAAA,WAAW,GAAG;AACV,SAAKC,IAAL,GAAY,EAAZ;AACA,SAAKC,IAAL,GAAY;AACRC,MAAAA,MAAM,EAAE,CADA;AAERC,MAAAA,KAAK,EAAE;AAFC,KAAZ;AAIH;;AACDC,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,QAAI,CAACA,IAAL,EAAW;AACP;AACH;;AACD,QAAIA,IAAI,CAACL,IAAL,KAAcM,SAAlB,EAA6B;AACzB,WAAKN,IAAL,GAAYK,IAAI,CAACL,IAAjB;AACH;;AACD,QAAIK,IAAI,CAACJ,IAAL,KAAcK,SAAlB,EAA6B;AACzB,UAAID,IAAI,CAACJ,IAAL,CAAUE,KAAV,KAAoBG,SAAxB,EAAmC;AAC/B,aAAKL,IAAL,CAAUE,KAAV,GAAkBE,IAAI,CAACJ,IAAL,CAAUE,KAA5B;AACH;;AACD,UAAIE,IAAI,CAACJ,IAAL,CAAUC,MAAV,KAAqBI,SAAzB,EAAoC;AAChC,aAAKL,IAAL,CAAUC,MAAV,GAAmBG,IAAI,CAACJ,IAAL,CAAUC,MAA7B;AACH;AACJ;AACJ;;AAvB4B", "sourcesContent": ["export class PolygonMaskLocalSvg {\n    constructor() {\n        this.path = [];\n        this.size = {\n            height: 0,\n            width: 0,\n        };\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.path !== undefined) {\n            this.path = data.path;\n        }\n        if (data.size !== undefined) {\n            if (data.size.width !== undefined) {\n                this.size.width = data.size.width;\n            }\n            if (data.size.height !== undefined) {\n                this.size.height = data.size.height;\n            }\n        }\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
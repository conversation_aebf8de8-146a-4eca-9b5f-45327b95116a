{"ast": null, "code": "var __classPrivateFieldSet = this && this.__classPrivateFieldSet || function (receiver, state, value, kind, f) {\n  if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n  return kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value), value;\n};\n\nvar __classPrivateFieldGet = this && this.__classPrivateFieldGet || function (receiver, state, kind, f) {\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n  return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n};\n\nvar _ImageDrawer_images;\n\nimport { downloadSvgImage, loadImage, replaceImageColor } from \"./Utils\";\nexport class ImageDrawer {\n  constructor() {\n    _ImageDrawer_images.set(this, void 0);\n\n    __classPrivateFieldSet(this, _ImageDrawer_images, [], \"f\");\n  }\n\n  getSidesCount() {\n    return 12;\n  }\n\n  getImages(container) {\n    const containerImages = __classPrivateFieldGet(this, _ImageDrawer_images, \"f\").find(t => t.id === container.id);\n\n    if (!containerImages) {\n      __classPrivateFieldGet(this, _ImageDrawer_images, \"f\").push({\n        id: container.id,\n        images: []\n      });\n\n      return this.getImages(container);\n    } else {\n      return containerImages;\n    }\n  }\n\n  addImage(container, image) {\n    const containerImages = this.getImages(container);\n    containerImages === null || containerImages === void 0 ? void 0 : containerImages.images.push(image);\n  }\n\n  destroy() {\n    __classPrivateFieldSet(this, _ImageDrawer_images, [], \"f\");\n  }\n\n  async loadImageShape(container, imageShape) {\n    const source = imageShape.src;\n\n    if (!source) {\n      throw new Error(\"Error tsParticles - No image.src\");\n    }\n\n    try {\n      const image = {\n        source: source,\n        type: source.substr(source.length - 3),\n        error: false,\n        loading: true\n      };\n      this.addImage(container, image);\n      const imageFunc = imageShape.replaceColor ? downloadSvgImage : loadImage;\n      await imageFunc(image);\n    } catch (_a) {\n      throw new Error(`tsParticles error - ${imageShape.src} not found`);\n    }\n  }\n\n  draw(context, particle, radius, opacity) {\n    var _a, _b;\n\n    const image = particle.image;\n    const element = (_a = image === null || image === void 0 ? void 0 : image.data) === null || _a === void 0 ? void 0 : _a.element;\n\n    if (!element) {\n      return;\n    }\n\n    const ratio = (_b = image === null || image === void 0 ? void 0 : image.ratio) !== null && _b !== void 0 ? _b : 1;\n    const pos = {\n      x: -radius,\n      y: -radius\n    };\n\n    if (!(image === null || image === void 0 ? void 0 : image.data.svgData) || !(image === null || image === void 0 ? void 0 : image.replaceColor)) {\n      context.globalAlpha = opacity;\n    }\n\n    context.drawImage(element, pos.x, pos.y, radius * 2, radius * 2 / ratio);\n\n    if (!(image === null || image === void 0 ? void 0 : image.data.svgData) || !(image === null || image === void 0 ? void 0 : image.replaceColor)) {\n      context.globalAlpha = 1;\n    }\n  }\n\n  loadShape(particle) {\n    var _a, _b, _c;\n\n    if (particle.shape !== \"image\" && particle.shape !== \"images\") {\n      return;\n    }\n\n    const images = this.getImages(particle.container).images;\n    const imageData = particle.shapeData;\n    const image = images.find(t => t.source === imageData.src);\n    let imageRes;\n\n    if (!image) {\n      this.loadImageShape(particle.container, imageData).then(() => {\n        this.loadShape(particle);\n      });\n      return;\n    }\n\n    if (image.error) {\n      return;\n    }\n\n    const color = particle.getFillColor();\n\n    if (image.svgData && imageData.replaceColor && color) {\n      imageRes = replaceImageColor(image, imageData, color, particle);\n    } else {\n      imageRes = {\n        data: image,\n        loaded: true,\n        ratio: imageData.width / imageData.height,\n        replaceColor: (_a = imageData.replaceColor) !== null && _a !== void 0 ? _a : imageData.replace_color,\n        source: imageData.src\n      };\n    }\n\n    if (!imageRes.ratio) {\n      imageRes.ratio = 1;\n    }\n\n    const fill = (_b = imageData.fill) !== null && _b !== void 0 ? _b : particle.fill;\n    const close = (_c = imageData.close) !== null && _c !== void 0 ? _c : particle.close;\n    const imageShape = {\n      image: imageRes,\n      fill,\n      close\n    };\n    particle.image = imageShape.image;\n    particle.fill = imageShape.fill;\n    particle.close = imageShape.close;\n  }\n\n}\n_ImageDrawer_images = new WeakMap();", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Shapes/Image/ImageDrawer.js"], "names": ["__classPrivateFieldSet", "receiver", "state", "value", "kind", "f", "TypeError", "has", "call", "set", "__classPrivateFieldGet", "get", "_ImageDrawer_images", "downloadSvgImage", "loadImage", "replaceImageColor", "ImageDrawer", "constructor", "getSidesCount", "getImages", "container", "containerImages", "find", "t", "id", "push", "images", "addImage", "image", "destroy", "loadImageShape", "imageShape", "source", "src", "Error", "type", "substr", "length", "error", "loading", "imageFunc", "replaceColor", "_a", "draw", "context", "particle", "radius", "opacity", "_b", "element", "data", "ratio", "pos", "x", "y", "svgData", "globalAlpha", "drawImage", "loadShape", "_c", "shape", "imageData", "shapeData", "imageRes", "then", "color", "getFillColor", "loaded", "width", "height", "replace_color", "fill", "close", "WeakMap"], "mappings": "AAAA,IAAIA,sBAAsB,GAAI,QAAQ,KAAKA,sBAAd,IAAyC,UAAUC,QAAV,EAAoBC,KAApB,EAA2BC,KAA3B,EAAkCC,IAAlC,EAAwCC,CAAxC,EAA2C;AAC7G,MAAID,IAAI,KAAK,GAAb,EAAkB,MAAM,IAAIE,SAAJ,CAAc,gCAAd,CAAN;AAClB,MAAIF,IAAI,KAAK,GAAT,IAAgB,CAACC,CAArB,EAAwB,MAAM,IAAIC,SAAJ,CAAc,+CAAd,CAAN;AACxB,MAAI,OAAOJ,KAAP,KAAiB,UAAjB,GAA8BD,QAAQ,KAAKC,KAAb,IAAsB,CAACG,CAArD,GAAyD,CAACH,KAAK,CAACK,GAAN,CAAUN,QAAV,CAA9D,EAAmF,MAAM,IAAIK,SAAJ,CAAc,yEAAd,CAAN;AACnF,SAAQF,IAAI,KAAK,GAAT,GAAeC,CAAC,CAACG,IAAF,CAAOP,QAAP,EAAiBE,KAAjB,CAAf,GAAyCE,CAAC,GAAGA,CAAC,CAACF,KAAF,GAAUA,KAAb,GAAqBD,KAAK,CAACO,GAAN,CAAUR,QAAV,EAAoBE,KAApB,CAAhE,EAA6FA,KAApG;AACH,CALD;;AAMA,IAAIO,sBAAsB,GAAI,QAAQ,KAAKA,sBAAd,IAAyC,UAAUT,QAAV,EAAoBC,KAApB,EAA2BE,IAA3B,EAAiCC,CAAjC,EAAoC;AACtG,MAAID,IAAI,KAAK,GAAT,IAAgB,CAACC,CAArB,EAAwB,MAAM,IAAIC,SAAJ,CAAc,+CAAd,CAAN;AACxB,MAAI,OAAOJ,KAAP,KAAiB,UAAjB,GAA8BD,QAAQ,KAAKC,KAAb,IAAsB,CAACG,CAArD,GAAyD,CAACH,KAAK,CAACK,GAAN,CAAUN,QAAV,CAA9D,EAAmF,MAAM,IAAIK,SAAJ,CAAc,0EAAd,CAAN;AACnF,SAAOF,IAAI,KAAK,GAAT,GAAeC,CAAf,GAAmBD,IAAI,KAAK,GAAT,GAAeC,CAAC,CAACG,IAAF,CAAOP,QAAP,CAAf,GAAkCI,CAAC,GAAGA,CAAC,CAACF,KAAL,GAAaD,KAAK,CAACS,GAAN,CAAUV,QAAV,CAA1E;AACH,CAJD;;AAKA,IAAIW,mBAAJ;;AACA,SAASC,gBAAT,EAA2BC,SAA3B,EAAsCC,iBAAtC,QAA+D,SAA/D;AACA,OAAO,MAAMC,WAAN,CAAkB;AACrBC,EAAAA,WAAW,GAAG;AACVL,IAAAA,mBAAmB,CAACH,GAApB,CAAwB,IAAxB,EAA8B,KAAK,CAAnC;;AACAT,IAAAA,sBAAsB,CAAC,IAAD,EAAOY,mBAAP,EAA4B,EAA5B,EAAgC,GAAhC,CAAtB;AACH;;AACDM,EAAAA,aAAa,GAAG;AACZ,WAAO,EAAP;AACH;;AACDC,EAAAA,SAAS,CAACC,SAAD,EAAY;AACjB,UAAMC,eAAe,GAAGX,sBAAsB,CAAC,IAAD,EAAOE,mBAAP,EAA4B,GAA5B,CAAtB,CAAuDU,IAAvD,CAA6DC,CAAD,IAAOA,CAAC,CAACC,EAAF,KAASJ,SAAS,CAACI,EAAtF,CAAxB;;AACA,QAAI,CAACH,eAAL,EAAsB;AAClBX,MAAAA,sBAAsB,CAAC,IAAD,EAAOE,mBAAP,EAA4B,GAA5B,CAAtB,CAAuDa,IAAvD,CAA4D;AACxDD,QAAAA,EAAE,EAAEJ,SAAS,CAACI,EAD0C;AAExDE,QAAAA,MAAM,EAAE;AAFgD,OAA5D;;AAIA,aAAO,KAAKP,SAAL,CAAeC,SAAf,CAAP;AACH,KAND,MAOK;AACD,aAAOC,eAAP;AACH;AACJ;;AACDM,EAAAA,QAAQ,CAACP,SAAD,EAAYQ,KAAZ,EAAmB;AACvB,UAAMP,eAAe,GAAG,KAAKF,SAAL,CAAeC,SAAf,CAAxB;AACAC,IAAAA,eAAe,KAAK,IAApB,IAA4BA,eAAe,KAAK,KAAK,CAArD,GAAyD,KAAK,CAA9D,GAAkEA,eAAe,CAACK,MAAhB,CAAuBD,IAAvB,CAA4BG,KAA5B,CAAlE;AACH;;AACDC,EAAAA,OAAO,GAAG;AACN7B,IAAAA,sBAAsB,CAAC,IAAD,EAAOY,mBAAP,EAA4B,EAA5B,EAAgC,GAAhC,CAAtB;AACH;;AACmB,QAAdkB,cAAc,CAACV,SAAD,EAAYW,UAAZ,EAAwB;AACxC,UAAMC,MAAM,GAAGD,UAAU,CAACE,GAA1B;;AACA,QAAI,CAACD,MAAL,EAAa;AACT,YAAM,IAAIE,KAAJ,CAAU,kCAAV,CAAN;AACH;;AACD,QAAI;AACA,YAAMN,KAAK,GAAG;AACVI,QAAAA,MAAM,EAAEA,MADE;AAEVG,QAAAA,IAAI,EAAEH,MAAM,CAACI,MAAP,CAAcJ,MAAM,CAACK,MAAP,GAAgB,CAA9B,CAFI;AAGVC,QAAAA,KAAK,EAAE,KAHG;AAIVC,QAAAA,OAAO,EAAE;AAJC,OAAd;AAMA,WAAKZ,QAAL,CAAcP,SAAd,EAAyBQ,KAAzB;AACA,YAAMY,SAAS,GAAGT,UAAU,CAACU,YAAX,GAA0B5B,gBAA1B,GAA6CC,SAA/D;AACA,YAAM0B,SAAS,CAACZ,KAAD,CAAf;AACH,KAVD,CAWA,OAAOc,EAAP,EAAW;AACP,YAAM,IAAIR,KAAJ,CAAW,uBAAsBH,UAAU,CAACE,GAAI,YAAhD,CAAN;AACH;AACJ;;AACDU,EAAAA,IAAI,CAACC,OAAD,EAAUC,QAAV,EAAoBC,MAApB,EAA4BC,OAA5B,EAAqC;AACrC,QAAIL,EAAJ,EAAQM,EAAR;;AACA,UAAMpB,KAAK,GAAGiB,QAAQ,CAACjB,KAAvB;AACA,UAAMqB,OAAO,GAAG,CAACP,EAAE,GAAGd,KAAK,KAAK,IAAV,IAAkBA,KAAK,KAAK,KAAK,CAAjC,GAAqC,KAAK,CAA1C,GAA8CA,KAAK,CAACsB,IAA1D,MAAoE,IAApE,IAA4ER,EAAE,KAAK,KAAK,CAAxF,GAA4F,KAAK,CAAjG,GAAqGA,EAAE,CAACO,OAAxH;;AACA,QAAI,CAACA,OAAL,EAAc;AACV;AACH;;AACD,UAAME,KAAK,GAAG,CAACH,EAAE,GAAGpB,KAAK,KAAK,IAAV,IAAkBA,KAAK,KAAK,KAAK,CAAjC,GAAqC,KAAK,CAA1C,GAA8CA,KAAK,CAACuB,KAA1D,MAAqE,IAArE,IAA6EH,EAAE,KAAK,KAAK,CAAzF,GAA6FA,EAA7F,GAAkG,CAAhH;AACA,UAAMI,GAAG,GAAG;AACRC,MAAAA,CAAC,EAAE,CAACP,MADI;AAERQ,MAAAA,CAAC,EAAE,CAACR;AAFI,KAAZ;;AAIA,QAAI,EAAElB,KAAK,KAAK,IAAV,IAAkBA,KAAK,KAAK,KAAK,CAAjC,GAAqC,KAAK,CAA1C,GAA8CA,KAAK,CAACsB,IAAN,CAAWK,OAA3D,KAAuE,EAAE3B,KAAK,KAAK,IAAV,IAAkBA,KAAK,KAAK,KAAK,CAAjC,GAAqC,KAAK,CAA1C,GAA8CA,KAAK,CAACa,YAAtD,CAA3E,EAAgJ;AAC5IG,MAAAA,OAAO,CAACY,WAAR,GAAsBT,OAAtB;AACH;;AACDH,IAAAA,OAAO,CAACa,SAAR,CAAkBR,OAAlB,EAA2BG,GAAG,CAACC,CAA/B,EAAkCD,GAAG,CAACE,CAAtC,EAAyCR,MAAM,GAAG,CAAlD,EAAsDA,MAAM,GAAG,CAAV,GAAeK,KAApE;;AACA,QAAI,EAAEvB,KAAK,KAAK,IAAV,IAAkBA,KAAK,KAAK,KAAK,CAAjC,GAAqC,KAAK,CAA1C,GAA8CA,KAAK,CAACsB,IAAN,CAAWK,OAA3D,KAAuE,EAAE3B,KAAK,KAAK,IAAV,IAAkBA,KAAK,KAAK,KAAK,CAAjC,GAAqC,KAAK,CAA1C,GAA8CA,KAAK,CAACa,YAAtD,CAA3E,EAAgJ;AAC5IG,MAAAA,OAAO,CAACY,WAAR,GAAsB,CAAtB;AACH;AACJ;;AACDE,EAAAA,SAAS,CAACb,QAAD,EAAW;AAChB,QAAIH,EAAJ,EAAQM,EAAR,EAAYW,EAAZ;;AACA,QAAId,QAAQ,CAACe,KAAT,KAAmB,OAAnB,IAA8Bf,QAAQ,CAACe,KAAT,KAAmB,QAArD,EAA+D;AAC3D;AACH;;AACD,UAAMlC,MAAM,GAAG,KAAKP,SAAL,CAAe0B,QAAQ,CAACzB,SAAxB,EAAmCM,MAAlD;AACA,UAAMmC,SAAS,GAAGhB,QAAQ,CAACiB,SAA3B;AACA,UAAMlC,KAAK,GAAGF,MAAM,CAACJ,IAAP,CAAaC,CAAD,IAAOA,CAAC,CAACS,MAAF,KAAa6B,SAAS,CAAC5B,GAA1C,CAAd;AACA,QAAI8B,QAAJ;;AACA,QAAI,CAACnC,KAAL,EAAY;AACR,WAAKE,cAAL,CAAoBe,QAAQ,CAACzB,SAA7B,EAAwCyC,SAAxC,EAAmDG,IAAnD,CAAwD,MAAM;AAC1D,aAAKN,SAAL,CAAeb,QAAf;AACH,OAFD;AAGA;AACH;;AACD,QAAIjB,KAAK,CAACU,KAAV,EAAiB;AACb;AACH;;AACD,UAAM2B,KAAK,GAAGpB,QAAQ,CAACqB,YAAT,EAAd;;AACA,QAAItC,KAAK,CAAC2B,OAAN,IAAiBM,SAAS,CAACpB,YAA3B,IAA2CwB,KAA/C,EAAsD;AAClDF,MAAAA,QAAQ,GAAGhD,iBAAiB,CAACa,KAAD,EAAQiC,SAAR,EAAmBI,KAAnB,EAA0BpB,QAA1B,CAA5B;AACH,KAFD,MAGK;AACDkB,MAAAA,QAAQ,GAAG;AACPb,QAAAA,IAAI,EAAEtB,KADC;AAEPuC,QAAAA,MAAM,EAAE,IAFD;AAGPhB,QAAAA,KAAK,EAAEU,SAAS,CAACO,KAAV,GAAkBP,SAAS,CAACQ,MAH5B;AAIP5B,QAAAA,YAAY,EAAE,CAACC,EAAE,GAAGmB,SAAS,CAACpB,YAAhB,MAAkC,IAAlC,IAA0CC,EAAE,KAAK,KAAK,CAAtD,GAA0DA,EAA1D,GAA+DmB,SAAS,CAACS,aAJhF;AAKPtC,QAAAA,MAAM,EAAE6B,SAAS,CAAC5B;AALX,OAAX;AAOH;;AACD,QAAI,CAAC8B,QAAQ,CAACZ,KAAd,EAAqB;AACjBY,MAAAA,QAAQ,CAACZ,KAAT,GAAiB,CAAjB;AACH;;AACD,UAAMoB,IAAI,GAAG,CAACvB,EAAE,GAAGa,SAAS,CAACU,IAAhB,MAA0B,IAA1B,IAAkCvB,EAAE,KAAK,KAAK,CAA9C,GAAkDA,EAAlD,GAAuDH,QAAQ,CAAC0B,IAA7E;AACA,UAAMC,KAAK,GAAG,CAACb,EAAE,GAAGE,SAAS,CAACW,KAAhB,MAA2B,IAA3B,IAAmCb,EAAE,KAAK,KAAK,CAA/C,GAAmDA,EAAnD,GAAwDd,QAAQ,CAAC2B,KAA/E;AACA,UAAMzC,UAAU,GAAG;AACfH,MAAAA,KAAK,EAAEmC,QADQ;AAEfQ,MAAAA,IAFe;AAGfC,MAAAA;AAHe,KAAnB;AAKA3B,IAAAA,QAAQ,CAACjB,KAAT,GAAiBG,UAAU,CAACH,KAA5B;AACAiB,IAAAA,QAAQ,CAAC0B,IAAT,GAAgBxC,UAAU,CAACwC,IAA3B;AACA1B,IAAAA,QAAQ,CAAC2B,KAAT,GAAiBzC,UAAU,CAACyC,KAA5B;AACH;;AAhHoB;AAkHzB5D,mBAAmB,GAAG,IAAI6D,OAAJ,EAAtB", "sourcesContent": ["var __classPrivateFieldSet = (this && this.__classPrivateFieldSet) || function (receiver, state, value, kind, f) {\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\n};\nvar __classPrivateFieldGet = (this && this.__classPrivateFieldGet) || function (receiver, state, kind, f) {\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n};\nvar _ImageDrawer_images;\nimport { downloadSvgImage, loadImage, replaceImageColor } from \"./Utils\";\nexport class ImageDrawer {\n    constructor() {\n        _ImageDrawer_images.set(this, void 0);\n        __classPrivateFieldSet(this, _ImageDrawer_images, [], \"f\");\n    }\n    getSidesCount() {\n        return 12;\n    }\n    getImages(container) {\n        const containerImages = __classPrivateFieldGet(this, _ImageDrawer_images, \"f\").find((t) => t.id === container.id);\n        if (!containerImages) {\n            __classPrivateFieldGet(this, _ImageDrawer_images, \"f\").push({\n                id: container.id,\n                images: [],\n            });\n            return this.getImages(container);\n        }\n        else {\n            return containerImages;\n        }\n    }\n    addImage(container, image) {\n        const containerImages = this.getImages(container);\n        containerImages === null || containerImages === void 0 ? void 0 : containerImages.images.push(image);\n    }\n    destroy() {\n        __classPrivateFieldSet(this, _ImageDrawer_images, [], \"f\");\n    }\n    async loadImageShape(container, imageShape) {\n        const source = imageShape.src;\n        if (!source) {\n            throw new Error(\"Error tsParticles - No image.src\");\n        }\n        try {\n            const image = {\n                source: source,\n                type: source.substr(source.length - 3),\n                error: false,\n                loading: true,\n            };\n            this.addImage(container, image);\n            const imageFunc = imageShape.replaceColor ? downloadSvgImage : loadImage;\n            await imageFunc(image);\n        }\n        catch (_a) {\n            throw new Error(`tsParticles error - ${imageShape.src} not found`);\n        }\n    }\n    draw(context, particle, radius, opacity) {\n        var _a, _b;\n        const image = particle.image;\n        const element = (_a = image === null || image === void 0 ? void 0 : image.data) === null || _a === void 0 ? void 0 : _a.element;\n        if (!element) {\n            return;\n        }\n        const ratio = (_b = image === null || image === void 0 ? void 0 : image.ratio) !== null && _b !== void 0 ? _b : 1;\n        const pos = {\n            x: -radius,\n            y: -radius,\n        };\n        if (!(image === null || image === void 0 ? void 0 : image.data.svgData) || !(image === null || image === void 0 ? void 0 : image.replaceColor)) {\n            context.globalAlpha = opacity;\n        }\n        context.drawImage(element, pos.x, pos.y, radius * 2, (radius * 2) / ratio);\n        if (!(image === null || image === void 0 ? void 0 : image.data.svgData) || !(image === null || image === void 0 ? void 0 : image.replaceColor)) {\n            context.globalAlpha = 1;\n        }\n    }\n    loadShape(particle) {\n        var _a, _b, _c;\n        if (particle.shape !== \"image\" && particle.shape !== \"images\") {\n            return;\n        }\n        const images = this.getImages(particle.container).images;\n        const imageData = particle.shapeData;\n        const image = images.find((t) => t.source === imageData.src);\n        let imageRes;\n        if (!image) {\n            this.loadImageShape(particle.container, imageData).then(() => {\n                this.loadShape(particle);\n            });\n            return;\n        }\n        if (image.error) {\n            return;\n        }\n        const color = particle.getFillColor();\n        if (image.svgData && imageData.replaceColor && color) {\n            imageRes = replaceImageColor(image, imageData, color, particle);\n        }\n        else {\n            imageRes = {\n                data: image,\n                loaded: true,\n                ratio: imageData.width / imageData.height,\n                replaceColor: (_a = imageData.replaceColor) !== null && _a !== void 0 ? _a : imageData.replace_color,\n                source: imageData.src,\n            };\n        }\n        if (!imageRes.ratio) {\n            imageRes.ratio = 1;\n        }\n        const fill = (_b = imageData.fill) !== null && _b !== void 0 ? _b : particle.fill;\n        const close = (_c = imageData.close) !== null && _c !== void 0 ? _c : particle.close;\n        const imageShape = {\n            image: imageRes,\n            fill,\n            close,\n        };\n        particle.image = imageShape.image;\n        particle.fill = imageShape.fill;\n        particle.close = imageShape.close;\n    }\n}\n_ImageDrawer_images = new WeakMap();\n"]}, "metadata": {}, "sourceType": "module"}
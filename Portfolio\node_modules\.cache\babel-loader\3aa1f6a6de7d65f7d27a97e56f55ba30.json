{"ast": null, "code": "import { TiltAnimation } from \"./TiltAnimation\";\nimport { ValueWithRandom } from \"../../ValueWithRandom\";\nexport class Tilt extends ValueWithRandom {\n  constructor() {\n    super();\n    this.animation = new TiltAnimation();\n    this.direction = \"clockwise\";\n    this.enable = false;\n    this.value = 0;\n  }\n\n  load(data) {\n    if (!data) {\n      return;\n    }\n\n    super.load(data);\n    this.animation.load(data.animation);\n\n    if (data.direction !== undefined) {\n      this.direction = data.direction;\n    }\n\n    if (data.enable !== undefined) {\n      this.enable = data.enable;\n    }\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Options/Classes/Particles/Tilt/Tilt.js"], "names": ["TiltAnimation", "ValueWithRandom", "Tilt", "constructor", "animation", "direction", "enable", "value", "load", "data", "undefined"], "mappings": "AAAA,SAASA,aAAT,QAA8B,iBAA9B;AACA,SAASC,eAAT,QAAgC,uBAAhC;AACA,OAAO,MAAMC,IAAN,SAAmBD,eAAnB,CAAmC;AACtCE,EAAAA,WAAW,GAAG;AACV;AACA,SAAKC,SAAL,GAAiB,IAAIJ,aAAJ,EAAjB;AACA,SAAKK,SAAL,GAAiB,WAAjB;AACA,SAAKC,MAAL,GAAc,KAAd;AACA,SAAKC,KAAL,GAAa,CAAb;AACH;;AACDC,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,QAAI,CAACA,IAAL,EAAW;AACP;AACH;;AACD,UAAMD,IAAN,CAAWC,IAAX;AACA,SAAKL,SAAL,CAAeI,IAAf,CAAoBC,IAAI,CAACL,SAAzB;;AACA,QAAIK,IAAI,CAACJ,SAAL,KAAmBK,SAAvB,EAAkC;AAC9B,WAAKL,SAAL,GAAiBI,IAAI,CAACJ,SAAtB;AACH;;AACD,QAAII,IAAI,CAACH,MAAL,KAAgBI,SAApB,EAA+B;AAC3B,WAAKJ,MAAL,GAAcG,IAAI,CAACH,MAAnB;AACH;AACJ;;AApBqC", "sourcesContent": ["import { TiltAnimation } from \"./TiltAnimation\";\nimport { ValueWithRandom } from \"../../ValueWithRandom\";\nexport class Tilt extends ValueWithRandom {\n    constructor() {\n        super();\n        this.animation = new TiltAnimation();\n        this.direction = \"clockwise\";\n        this.enable = false;\n        this.value = 0;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        super.load(data);\n        this.animation.load(data.animation);\n        if (data.direction !== undefined) {\n            this.direction = data.direction;\n        }\n        if (data.enable !== undefined) {\n            this.enable = data.enable;\n        }\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
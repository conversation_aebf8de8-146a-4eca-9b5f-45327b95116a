{"ast": null, "code": "import { getRangeValue } from \"../../Utils\";\n\nfunction updateWobble(particle, delta) {\n  var _a;\n\n  const wobble = particle.options.wobble;\n\n  if (!wobble.enable || !particle.wobble) {\n    return;\n  }\n\n  const speed = particle.wobble.speed * delta.factor;\n  const distance = ((_a = particle.retina.wobbleDistance) !== null && _a !== void 0 ? _a : 0) * delta.factor / (1000 / 60);\n  const max = 2 * Math.PI;\n  particle.wobble.angle += speed;\n\n  if (particle.wobble.angle > max) {\n    particle.wobble.angle -= max;\n  }\n\n  particle.position.x += distance * Math.cos(particle.wobble.angle);\n  particle.position.y += distance * Math.abs(Math.sin(particle.wobble.angle));\n}\n\nexport class WobbleUpdater {\n  constructor(container) {\n    this.container = container;\n  }\n\n  init(particle) {\n    const wobbleOpt = particle.options.wobble;\n\n    if (wobbleOpt.enable) {\n      particle.wobble = {\n        angle: Math.random() * Math.PI * 2,\n        speed: getRangeValue(wobbleOpt.speed) / 360\n      };\n    } else {\n      particle.wobble = {\n        angle: 0,\n        speed: 0\n      };\n    }\n\n    particle.retina.wobbleDistance = getRangeValue(wobbleOpt.distance) * this.container.retina.pixelRatio;\n  }\n\n  isEnabled(particle) {\n    return !particle.destroyed && !particle.spawning && particle.options.wobble.enable;\n  }\n\n  update(particle, delta) {\n    if (!this.isEnabled(particle)) {\n      return;\n    }\n\n    updateWobble(particle, delta);\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Updaters/Wobble/WobbleUpdater.js"], "names": ["getRangeValue", "updateWobble", "particle", "delta", "_a", "wobble", "options", "enable", "speed", "factor", "distance", "retina", "wobbleDistance", "max", "Math", "PI", "angle", "position", "x", "cos", "y", "abs", "sin", "WobbleUpdater", "constructor", "container", "init", "wobbleOpt", "random", "pixelRatio", "isEnabled", "destroyed", "spawning", "update"], "mappings": "AAAA,SAASA,aAAT,QAA8B,aAA9B;;AACA,SAASC,YAAT,CAAsBC,QAAtB,EAAgCC,KAAhC,EAAuC;AACnC,MAAIC,EAAJ;;AACA,QAAMC,MAAM,GAAGH,QAAQ,CAACI,OAAT,CAAiBD,MAAhC;;AACA,MAAI,CAACA,MAAM,CAACE,MAAR,IAAkB,CAACL,QAAQ,CAACG,MAAhC,EAAwC;AACpC;AACH;;AACD,QAAMG,KAAK,GAAGN,QAAQ,CAACG,MAAT,CAAgBG,KAAhB,GAAwBL,KAAK,CAACM,MAA5C;AACA,QAAMC,QAAQ,GAAI,CAAC,CAACN,EAAE,GAAGF,QAAQ,CAACS,MAAT,CAAgBC,cAAtB,MAA0C,IAA1C,IAAkDR,EAAE,KAAK,KAAK,CAA9D,GAAkEA,EAAlE,GAAuE,CAAxE,IAA6ED,KAAK,CAACM,MAApF,IAA+F,OAAO,EAAtG,CAAjB;AACA,QAAMI,GAAG,GAAG,IAAIC,IAAI,CAACC,EAArB;AACAb,EAAAA,QAAQ,CAACG,MAAT,CAAgBW,KAAhB,IAAyBR,KAAzB;;AACA,MAAIN,QAAQ,CAACG,MAAT,CAAgBW,KAAhB,GAAwBH,GAA5B,EAAiC;AAC7BX,IAAAA,QAAQ,CAACG,MAAT,CAAgBW,KAAhB,IAAyBH,GAAzB;AACH;;AACDX,EAAAA,QAAQ,CAACe,QAAT,CAAkBC,CAAlB,IAAuBR,QAAQ,GAAGI,IAAI,CAACK,GAAL,CAASjB,QAAQ,CAACG,MAAT,CAAgBW,KAAzB,CAAlC;AACAd,EAAAA,QAAQ,CAACe,QAAT,CAAkBG,CAAlB,IAAuBV,QAAQ,GAAGI,IAAI,CAACO,GAAL,CAASP,IAAI,CAACQ,GAAL,CAASpB,QAAQ,CAACG,MAAT,CAAgBW,KAAzB,CAAT,CAAlC;AACH;;AACD,OAAO,MAAMO,aAAN,CAAoB;AACvBC,EAAAA,WAAW,CAACC,SAAD,EAAY;AACnB,SAAKA,SAAL,GAAiBA,SAAjB;AACH;;AACDC,EAAAA,IAAI,CAACxB,QAAD,EAAW;AACX,UAAMyB,SAAS,GAAGzB,QAAQ,CAACI,OAAT,CAAiBD,MAAnC;;AACA,QAAIsB,SAAS,CAACpB,MAAd,EAAsB;AAClBL,MAAAA,QAAQ,CAACG,MAAT,GAAkB;AACdW,QAAAA,KAAK,EAAEF,IAAI,CAACc,MAAL,KAAgBd,IAAI,CAACC,EAArB,GAA0B,CADnB;AAEdP,QAAAA,KAAK,EAAER,aAAa,CAAC2B,SAAS,CAACnB,KAAX,CAAb,GAAiC;AAF1B,OAAlB;AAIH,KALD,MAMK;AACDN,MAAAA,QAAQ,CAACG,MAAT,GAAkB;AACdW,QAAAA,KAAK,EAAE,CADO;AAEdR,QAAAA,KAAK,EAAE;AAFO,OAAlB;AAIH;;AACDN,IAAAA,QAAQ,CAACS,MAAT,CAAgBC,cAAhB,GAAiCZ,aAAa,CAAC2B,SAAS,CAACjB,QAAX,CAAb,GAAoC,KAAKe,SAAL,CAAed,MAAf,CAAsBkB,UAA3F;AACH;;AACDC,EAAAA,SAAS,CAAC5B,QAAD,EAAW;AAChB,WAAO,CAACA,QAAQ,CAAC6B,SAAV,IAAuB,CAAC7B,QAAQ,CAAC8B,QAAjC,IAA6C9B,QAAQ,CAACI,OAAT,CAAiBD,MAAjB,CAAwBE,MAA5E;AACH;;AACD0B,EAAAA,MAAM,CAAC/B,QAAD,EAAWC,KAAX,EAAkB;AACpB,QAAI,CAAC,KAAK2B,SAAL,CAAe5B,QAAf,CAAL,EAA+B;AAC3B;AACH;;AACDD,IAAAA,YAAY,CAACC,QAAD,EAAWC,KAAX,CAAZ;AACH;;AA5BsB", "sourcesContent": ["import { getRangeValue } from \"../../Utils\";\nfunction updateWobble(particle, delta) {\n    var _a;\n    const wobble = particle.options.wobble;\n    if (!wobble.enable || !particle.wobble) {\n        return;\n    }\n    const speed = particle.wobble.speed * delta.factor;\n    const distance = (((_a = particle.retina.wobbleDistance) !== null && _a !== void 0 ? _a : 0) * delta.factor) / (1000 / 60);\n    const max = 2 * Math.PI;\n    particle.wobble.angle += speed;\n    if (particle.wobble.angle > max) {\n        particle.wobble.angle -= max;\n    }\n    particle.position.x += distance * Math.cos(particle.wobble.angle);\n    particle.position.y += distance * Math.abs(Math.sin(particle.wobble.angle));\n}\nexport class WobbleUpdater {\n    constructor(container) {\n        this.container = container;\n    }\n    init(particle) {\n        const wobbleOpt = particle.options.wobble;\n        if (wobbleOpt.enable) {\n            particle.wobble = {\n                angle: Math.random() * Math.PI * 2,\n                speed: getRangeValue(wobbleOpt.speed) / 360,\n            };\n        }\n        else {\n            particle.wobble = {\n                angle: 0,\n                speed: 0,\n            };\n        }\n        particle.retina.wobbleDistance = getRangeValue(wobbleOpt.distance) * this.container.retina.pixelRatio;\n    }\n    isEnabled(particle) {\n        return !particle.destroyed && !particle.spawning && particle.options.wobble.enable;\n    }\n    update(particle, delta) {\n        if (!this.isEnabled(particle)) {\n            return;\n        }\n        updateWobble(particle, delta);\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
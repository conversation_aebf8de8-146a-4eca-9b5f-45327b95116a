{"ast": null, "code": "import { animate, cancelAnimation, getRangeValue } from \"../Utils/NumberUtils.js\";\nimport { errorPrefix, millisecondsToSeconds } from \"./Utils/Constants.js\";\nimport { getLogger, safeIntersectionObserver } from \"../Utils/Utils.js\";\nimport { Canvas } from \"./Canvas.js\";\nimport { EventListeners } from \"./Utils/EventListeners.js\";\nimport { EventType } from \"../Enums/Types/EventType.js\";\nimport { Options } from \"../Options/Classes/Options.js\";\nimport { Particles } from \"./Particles.js\";\nimport { Retina } from \"./Retina.js\";\nimport { loadOptions } from \"../Utils/OptionsUtils.js\";\nfunction guardCheck(container) {\n  return container && !container.destroyed;\n}\nconst defaultFps = 60;\nfunction initDelta(value, fpsLimit = defaultFps, smooth = false) {\n  return {\n    value,\n    factor: smooth ? defaultFps / fpsLimit : defaultFps * value / millisecondsToSeconds\n  };\n}\nfunction loadContainerOptions(engine, container, ...sourceOptionsArr) {\n  const options = new Options(engine, container);\n  loadOptions(options, ...sourceOptionsArr);\n  return options;\n}\nexport class Container {\n  constructor(engine, id, sourceOptions) {\n    this._intersectionManager = entries => {\n      if (!guardCheck(this) || !this.actualOptions.pauseOnOutsideViewport) {\n        return;\n      }\n      for (const entry of entries) {\n        if (entry.target !== this.interactivity.element) {\n          continue;\n        }\n        if (entry.isIntersecting) {\n          void this.play();\n        } else {\n          this.pause();\n        }\n      }\n    };\n    this._nextFrame = timestamp => {\n      try {\n        if (!this._smooth && this._lastFrameTime !== undefined && timestamp < this._lastFrameTime + millisecondsToSeconds / this.fpsLimit) {\n          this.draw(false);\n          return;\n        }\n        this._lastFrameTime ??= timestamp;\n        const delta = initDelta(timestamp - this._lastFrameTime, this.fpsLimit, this._smooth);\n        this.addLifeTime(delta.value);\n        this._lastFrameTime = timestamp;\n        if (delta.value > millisecondsToSeconds) {\n          this.draw(false);\n          return;\n        }\n        this.particles.draw(delta);\n        if (!this.alive()) {\n          this.destroy();\n          return;\n        }\n        if (this.animationStatus) {\n          this.draw(false);\n        }\n      } catch (e) {\n        getLogger().error(`${errorPrefix} in animation loop`, e);\n      }\n    };\n    this._engine = engine;\n    this.id = Symbol(id);\n    this.fpsLimit = 120;\n    this._smooth = false;\n    this._delay = 0;\n    this._duration = 0;\n    this._lifeTime = 0;\n    this._firstStart = true;\n    this.started = false;\n    this.destroyed = false;\n    this._paused = true;\n    this._lastFrameTime = 0;\n    this.zLayers = 100;\n    this.pageHidden = false;\n    this._clickHandlers = new Map();\n    this._sourceOptions = sourceOptions;\n    this._initialSourceOptions = sourceOptions;\n    this.retina = new Retina(this);\n    this.canvas = new Canvas(this);\n    this.particles = new Particles(this._engine, this);\n    this.pathGenerators = new Map();\n    this.interactivity = {\n      mouse: {\n        clicking: false,\n        inside: false\n      }\n    };\n    this.plugins = new Map();\n    this.effectDrawers = new Map();\n    this.shapeDrawers = new Map();\n    this._options = loadContainerOptions(this._engine, this);\n    this.actualOptions = loadContainerOptions(this._engine, this);\n    this._eventListeners = new EventListeners(this);\n    this._intersectionObserver = safeIntersectionObserver(entries => this._intersectionManager(entries));\n    this._engine.dispatchEvent(EventType.containerBuilt, {\n      container: this\n    });\n  }\n  get animationStatus() {\n    return !this._paused && !this.pageHidden && guardCheck(this);\n  }\n  get options() {\n    return this._options;\n  }\n  get sourceOptions() {\n    return this._sourceOptions;\n  }\n  addClickHandler(callback) {\n    if (!guardCheck(this)) {\n      return;\n    }\n    const el = this.interactivity.element;\n    if (!el) {\n      return;\n    }\n    const clickOrTouchHandler = (e, pos, radius) => {\n        if (!guardCheck(this)) {\n          return;\n        }\n        const pxRatio = this.retina.pixelRatio,\n          posRetina = {\n            x: pos.x * pxRatio,\n            y: pos.y * pxRatio\n          },\n          particles = this.particles.quadTree.queryCircle(posRetina, radius * pxRatio);\n        callback(e, particles);\n      },\n      clickHandler = e => {\n        if (!guardCheck(this)) {\n          return;\n        }\n        const mouseEvent = e,\n          pos = {\n            x: mouseEvent.offsetX || mouseEvent.clientX,\n            y: mouseEvent.offsetY || mouseEvent.clientY\n          },\n          radius = 1;\n        clickOrTouchHandler(e, pos, radius);\n      },\n      touchStartHandler = () => {\n        if (!guardCheck(this)) {\n          return;\n        }\n        touched = true;\n        touchMoved = false;\n      },\n      touchMoveHandler = () => {\n        if (!guardCheck(this)) {\n          return;\n        }\n        touchMoved = true;\n      },\n      touchEndHandler = e => {\n        if (!guardCheck(this)) {\n          return;\n        }\n        if (touched && !touchMoved) {\n          const touchEvent = e,\n            lengthOffset = 1;\n          let lastTouch = touchEvent.touches[touchEvent.touches.length - lengthOffset];\n          if (!lastTouch) {\n            lastTouch = touchEvent.changedTouches[touchEvent.changedTouches.length - lengthOffset];\n            if (!lastTouch) {\n              return;\n            }\n          }\n          const element = this.canvas.element,\n            canvasRect = element ? element.getBoundingClientRect() : undefined,\n            minCoordinate = 0,\n            pos = {\n              x: lastTouch.clientX - (canvasRect ? canvasRect.left : minCoordinate),\n              y: lastTouch.clientY - (canvasRect ? canvasRect.top : minCoordinate)\n            };\n          clickOrTouchHandler(e, pos, Math.max(lastTouch.radiusX, lastTouch.radiusY));\n        }\n        touched = false;\n        touchMoved = false;\n      },\n      touchCancelHandler = () => {\n        if (!guardCheck(this)) {\n          return;\n        }\n        touched = false;\n        touchMoved = false;\n      };\n    let touched = false,\n      touchMoved = false;\n    this._clickHandlers.set(\"click\", clickHandler);\n    this._clickHandlers.set(\"touchstart\", touchStartHandler);\n    this._clickHandlers.set(\"touchmove\", touchMoveHandler);\n    this._clickHandlers.set(\"touchend\", touchEndHandler);\n    this._clickHandlers.set(\"touchcancel\", touchCancelHandler);\n    for (const [key, handler] of this._clickHandlers) {\n      el.addEventListener(key, handler);\n    }\n  }\n  addLifeTime(value) {\n    this._lifeTime += value;\n  }\n  addPath(key, generator, override = false) {\n    if (!guardCheck(this) || !override && this.pathGenerators.has(key)) {\n      return false;\n    }\n    this.pathGenerators.set(key, generator);\n    return true;\n  }\n  alive() {\n    return !this._duration || this._lifeTime <= this._duration;\n  }\n  clearClickHandlers() {\n    if (!guardCheck(this)) {\n      return;\n    }\n    for (const [key, handler] of this._clickHandlers) {\n      this.interactivity.element?.removeEventListener(key, handler);\n    }\n    this._clickHandlers.clear();\n  }\n  destroy(remove = true) {\n    if (!guardCheck(this)) {\n      return;\n    }\n    this.stop();\n    this.clearClickHandlers();\n    this.particles.destroy();\n    this.canvas.destroy();\n    for (const [, effectDrawer] of this.effectDrawers) {\n      effectDrawer.destroy?.(this);\n    }\n    for (const [, shapeDrawer] of this.shapeDrawers) {\n      shapeDrawer.destroy?.(this);\n    }\n    for (const key of this.effectDrawers.keys()) {\n      this.effectDrawers.delete(key);\n    }\n    for (const key of this.shapeDrawers.keys()) {\n      this.shapeDrawers.delete(key);\n    }\n    this._engine.clearPlugins(this);\n    this.destroyed = true;\n    if (remove) {\n      const mainArr = this._engine.items,\n        idx = mainArr.findIndex(t => t === this),\n        minIndex = 0;\n      if (idx >= minIndex) {\n        const deleteCount = 1;\n        mainArr.splice(idx, deleteCount);\n      }\n    }\n    this._engine.dispatchEvent(EventType.containerDestroyed, {\n      container: this\n    });\n  }\n  draw(force) {\n    if (!guardCheck(this)) {\n      return;\n    }\n    let refreshTime = force;\n    const frame = timestamp => {\n      if (refreshTime) {\n        this._lastFrameTime = undefined;\n        refreshTime = false;\n      }\n      this._nextFrame(timestamp);\n    };\n    this._drawAnimationFrame = animate(timestamp => frame(timestamp));\n  }\n  async export(type, options = {}) {\n    for (const [, plugin] of this.plugins) {\n      if (!plugin.export) {\n        continue;\n      }\n      const res = await plugin.export(type, options);\n      if (!res.supported) {\n        continue;\n      }\n      return res.blob;\n    }\n    getLogger().error(`${errorPrefix} - Export plugin with type ${type} not found`);\n  }\n  handleClickMode(mode) {\n    if (!guardCheck(this)) {\n      return;\n    }\n    this.particles.handleClickMode(mode);\n    for (const [, plugin] of this.plugins) {\n      plugin.handleClickMode?.(mode);\n    }\n  }\n  async init() {\n    if (!guardCheck(this)) {\n      return;\n    }\n    const effects = this._engine.getSupportedEffects();\n    for (const type of effects) {\n      const drawer = this._engine.getEffectDrawer(type);\n      if (drawer) {\n        this.effectDrawers.set(type, drawer);\n      }\n    }\n    const shapes = this._engine.getSupportedShapes();\n    for (const type of shapes) {\n      const drawer = this._engine.getShapeDrawer(type);\n      if (drawer) {\n        this.shapeDrawers.set(type, drawer);\n      }\n    }\n    await this.particles.initPlugins();\n    this._options = loadContainerOptions(this._engine, this, this._initialSourceOptions, this.sourceOptions);\n    this.actualOptions = loadContainerOptions(this._engine, this, this._options);\n    const availablePlugins = await this._engine.getAvailablePlugins(this);\n    for (const [id, plugin] of availablePlugins) {\n      this.plugins.set(id, plugin);\n    }\n    this.retina.init();\n    await this.canvas.init();\n    this.updateActualOptions();\n    this.canvas.initBackground();\n    this.canvas.resize();\n    const {\n      zLayers,\n      duration,\n      delay,\n      fpsLimit,\n      smooth\n    } = this.actualOptions;\n    this.zLayers = zLayers;\n    this._duration = getRangeValue(duration) * millisecondsToSeconds;\n    this._delay = getRangeValue(delay) * millisecondsToSeconds;\n    this._lifeTime = 0;\n    const defaultFpsLimit = 120,\n      minFpsLimit = 0;\n    this.fpsLimit = fpsLimit > minFpsLimit ? fpsLimit : defaultFpsLimit;\n    this._smooth = smooth;\n    for (const [, drawer] of this.effectDrawers) {\n      await drawer.init?.(this);\n    }\n    for (const [, drawer] of this.shapeDrawers) {\n      await drawer.init?.(this);\n    }\n    for (const [, plugin] of this.plugins) {\n      await plugin.init?.();\n    }\n    this._engine.dispatchEvent(EventType.containerInit, {\n      container: this\n    });\n    await this.particles.init();\n    this.particles.setDensity();\n    for (const [, plugin] of this.plugins) {\n      plugin.particlesSetup?.();\n    }\n    this._engine.dispatchEvent(EventType.particlesSetup, {\n      container: this\n    });\n  }\n  async loadTheme(name) {\n    if (!guardCheck(this)) {\n      return;\n    }\n    this._currentTheme = name;\n    await this.refresh();\n  }\n  pause() {\n    if (!guardCheck(this)) {\n      return;\n    }\n    if (this._drawAnimationFrame !== undefined) {\n      cancelAnimation(this._drawAnimationFrame);\n      delete this._drawAnimationFrame;\n    }\n    if (this._paused) {\n      return;\n    }\n    for (const [, plugin] of this.plugins) {\n      plugin.pause?.();\n    }\n    if (!this.pageHidden) {\n      this._paused = true;\n    }\n    this._engine.dispatchEvent(EventType.containerPaused, {\n      container: this\n    });\n  }\n  play(force) {\n    if (!guardCheck(this)) {\n      return;\n    }\n    const needsUpdate = this._paused || force;\n    if (this._firstStart && !this.actualOptions.autoPlay) {\n      this._firstStart = false;\n      return;\n    }\n    if (this._paused) {\n      this._paused = false;\n    }\n    if (needsUpdate) {\n      for (const [, plugin] of this.plugins) {\n        if (plugin.play) {\n          plugin.play();\n        }\n      }\n    }\n    this._engine.dispatchEvent(EventType.containerPlay, {\n      container: this\n    });\n    this.draw(needsUpdate ?? false);\n  }\n  async refresh() {\n    if (!guardCheck(this)) {\n      return;\n    }\n    this.stop();\n    return this.start();\n  }\n  async reset(sourceOptions) {\n    if (!guardCheck(this)) {\n      return;\n    }\n    this._initialSourceOptions = sourceOptions;\n    this._sourceOptions = sourceOptions;\n    this._options = loadContainerOptions(this._engine, this, this._initialSourceOptions, this.sourceOptions);\n    this.actualOptions = loadContainerOptions(this._engine, this, this._options);\n    return this.refresh();\n  }\n  async start() {\n    if (!guardCheck(this) || this.started) {\n      return;\n    }\n    await this.init();\n    this.started = true;\n    await new Promise(resolve => {\n      const start = async () => {\n        this._eventListeners.addListeners();\n        if (this.interactivity.element instanceof HTMLElement && this._intersectionObserver) {\n          this._intersectionObserver.observe(this.interactivity.element);\n        }\n        for (const [, plugin] of this.plugins) {\n          await plugin.start?.();\n        }\n        this._engine.dispatchEvent(EventType.containerStarted, {\n          container: this\n        });\n        this.play();\n        resolve();\n      };\n      this._delayTimeout = setTimeout(() => void start(), this._delay);\n    });\n  }\n  stop() {\n    if (!guardCheck(this) || !this.started) {\n      return;\n    }\n    if (this._delayTimeout) {\n      clearTimeout(this._delayTimeout);\n      delete this._delayTimeout;\n    }\n    this._firstStart = true;\n    this.started = false;\n    this._eventListeners.removeListeners();\n    this.pause();\n    this.particles.clear();\n    this.canvas.stop();\n    if (this.interactivity.element instanceof HTMLElement && this._intersectionObserver) {\n      this._intersectionObserver.unobserve(this.interactivity.element);\n    }\n    for (const [, plugin] of this.plugins) {\n      plugin.stop?.();\n    }\n    for (const key of this.plugins.keys()) {\n      this.plugins.delete(key);\n    }\n    this._sourceOptions = this._options;\n    this._engine.dispatchEvent(EventType.containerStopped, {\n      container: this\n    });\n  }\n  updateActualOptions() {\n    this.actualOptions.responsive = [];\n    const newMaxWidth = this.actualOptions.setResponsive(this.canvas.size.width, this.retina.pixelRatio, this._options);\n    this.actualOptions.setTheme(this._currentTheme);\n    if (this._responsiveMaxWidth === newMaxWidth) {\n      return false;\n    }\n    this._responsiveMaxWidth = newMaxWidth;\n    return true;\n  }\n}", "map": {"version": 3, "names": ["animate", "cancelAnimation", "getRangeValue", "errorPrefix", "millisecondsToSeconds", "<PERSON><PERSON><PERSON><PERSON>", "safeIntersectionObserver", "<PERSON><PERSON>", "EventListeners", "EventType", "Options", "Particles", "Retina", "loadOptions", "<PERSON><PERSON><PERSON><PERSON>", "container", "destroyed", "defaultFps", "initDelta", "value", "fpsLimit", "smooth", "factor", "loadContainerOptions", "engine", "sourceOptionsArr", "options", "Container", "constructor", "id", "sourceOptions", "_intersectionManager", "entries", "actualOptions", "pauseOnOutsideViewport", "entry", "target", "interactivity", "element", "isIntersecting", "play", "pause", "_nextFrame", "timestamp", "_smooth", "_lastFrameTime", "undefined", "draw", "delta", "addLifeTime", "particles", "alive", "destroy", "animationStatus", "e", "error", "_engine", "Symbol", "_delay", "_duration", "_lifeTime", "_firstStart", "started", "_paused", "zLayers", "pageHidden", "_clickHandlers", "Map", "_sourceOptions", "_initialSourceOptions", "retina", "canvas", "pathGenerators", "mouse", "clicking", "inside", "plugins", "effectDrawers", "shapeDrawers", "_options", "_eventListeners", "_intersectionObserver", "dispatchEvent", "containerBuilt", "addClickHandler", "callback", "el", "clickOrTouchHandler", "pos", "radius", "pxRatio", "pixelRatio", "posRetina", "x", "y", "quadTree", "queryCircle", "clickHandler", "mouseEvent", "offsetX", "clientX", "offsetY", "clientY", "touchStartHandler", "touched", "touchMoved", "touchMoveHandler", "touchEndHandler", "touchEvent", "lengthOffset", "lastTouch", "touches", "length", "changedTouches", "canvasRect", "getBoundingClientRect", "minCoordinate", "left", "top", "Math", "max", "radiusX", "radiusY", "touchCancelHandler", "set", "key", "handler", "addEventListener", "addPath", "generator", "override", "has", "clearClickHandlers", "removeEventListener", "clear", "remove", "stop", "effectDrawer", "shapeDrawer", "keys", "delete", "clearPlugins", "mainArr", "items", "idx", "findIndex", "t", "minIndex", "deleteCount", "splice", "containerDestroyed", "force", "refreshTime", "frame", "_drawAnimationFrame", "export", "type", "plugin", "res", "supported", "blob", "handleClickMode", "mode", "init", "effects", "getSupportedEffects", "drawer", "getEffectDrawer", "shapes", "getSupportedShapes", "getShapeDrawer", "initPlugins", "availablePlugins", "getAvailablePlugins", "updateActualOptions", "initBackground", "resize", "duration", "delay", "defaultFpsLimit", "minFpsLimit", "containerInit", "setDensity", "particlesSetup", "loadTheme", "name", "_currentTheme", "refresh", "containerPaused", "needsUpdate", "autoPlay", "containerPlay", "start", "reset", "Promise", "resolve", "addListeners", "HTMLElement", "observe", "containerStarted", "_delayTimeout", "setTimeout", "clearTimeout", "removeListeners", "unobserve", "containerStopped", "responsive", "newMaxWidth", "setResponsive", "size", "width", "setTheme", "_responsiveMaxWidth"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/engine/browser/Core/Container.js"], "sourcesContent": ["import { animate, cancelAnimation, getRangeValue } from \"../Utils/NumberUtils.js\";\nimport { errorPrefix, millisecondsToSeconds } from \"./Utils/Constants.js\";\nimport { getLogger, safeIntersectionObserver } from \"../Utils/Utils.js\";\nimport { Canvas } from \"./Canvas.js\";\nimport { EventListeners } from \"./Utils/EventListeners.js\";\nimport { EventType } from \"../Enums/Types/EventType.js\";\nimport { Options } from \"../Options/Classes/Options.js\";\nimport { Particles } from \"./Particles.js\";\nimport { Retina } from \"./Retina.js\";\nimport { loadOptions } from \"../Utils/OptionsUtils.js\";\nfunction guardCheck(container) {\n    return container && !container.destroyed;\n}\nconst defaultFps = 60;\nfunction initDelta(value, fpsLimit = defaultFps, smooth = false) {\n    return {\n        value,\n        factor: smooth ? defaultFps / fpsLimit : (defaultFps * value) / millisecondsToSeconds,\n    };\n}\nfunction loadContainerOptions(engine, container, ...sourceOptionsArr) {\n    const options = new Options(engine, container);\n    loadOptions(options, ...sourceOptionsArr);\n    return options;\n}\nexport class Container {\n    constructor(engine, id, sourceOptions) {\n        this._intersectionManager = entries => {\n            if (!guardCheck(this) || !this.actualOptions.pauseOnOutsideViewport) {\n                return;\n            }\n            for (const entry of entries) {\n                if (entry.target !== this.interactivity.element) {\n                    continue;\n                }\n                if (entry.isIntersecting) {\n                    void this.play();\n                }\n                else {\n                    this.pause();\n                }\n            }\n        };\n        this._nextFrame = (timestamp) => {\n            try {\n                if (!this._smooth &&\n                    this._lastFrameTime !== undefined &&\n                    timestamp < this._lastFrameTime + millisecondsToSeconds / this.fpsLimit) {\n                    this.draw(false);\n                    return;\n                }\n                this._lastFrameTime ??= timestamp;\n                const delta = initDelta(timestamp - this._lastFrameTime, this.fpsLimit, this._smooth);\n                this.addLifeTime(delta.value);\n                this._lastFrameTime = timestamp;\n                if (delta.value > millisecondsToSeconds) {\n                    this.draw(false);\n                    return;\n                }\n                this.particles.draw(delta);\n                if (!this.alive()) {\n                    this.destroy();\n                    return;\n                }\n                if (this.animationStatus) {\n                    this.draw(false);\n                }\n            }\n            catch (e) {\n                getLogger().error(`${errorPrefix} in animation loop`, e);\n            }\n        };\n        this._engine = engine;\n        this.id = Symbol(id);\n        this.fpsLimit = 120;\n        this._smooth = false;\n        this._delay = 0;\n        this._duration = 0;\n        this._lifeTime = 0;\n        this._firstStart = true;\n        this.started = false;\n        this.destroyed = false;\n        this._paused = true;\n        this._lastFrameTime = 0;\n        this.zLayers = 100;\n        this.pageHidden = false;\n        this._clickHandlers = new Map();\n        this._sourceOptions = sourceOptions;\n        this._initialSourceOptions = sourceOptions;\n        this.retina = new Retina(this);\n        this.canvas = new Canvas(this);\n        this.particles = new Particles(this._engine, this);\n        this.pathGenerators = new Map();\n        this.interactivity = {\n            mouse: {\n                clicking: false,\n                inside: false,\n            },\n        };\n        this.plugins = new Map();\n        this.effectDrawers = new Map();\n        this.shapeDrawers = new Map();\n        this._options = loadContainerOptions(this._engine, this);\n        this.actualOptions = loadContainerOptions(this._engine, this);\n        this._eventListeners = new EventListeners(this);\n        this._intersectionObserver = safeIntersectionObserver(entries => this._intersectionManager(entries));\n        this._engine.dispatchEvent(EventType.containerBuilt, { container: this });\n    }\n    get animationStatus() {\n        return !this._paused && !this.pageHidden && guardCheck(this);\n    }\n    get options() {\n        return this._options;\n    }\n    get sourceOptions() {\n        return this._sourceOptions;\n    }\n    addClickHandler(callback) {\n        if (!guardCheck(this)) {\n            return;\n        }\n        const el = this.interactivity.element;\n        if (!el) {\n            return;\n        }\n        const clickOrTouchHandler = (e, pos, radius) => {\n            if (!guardCheck(this)) {\n                return;\n            }\n            const pxRatio = this.retina.pixelRatio, posRetina = {\n                x: pos.x * pxRatio,\n                y: pos.y * pxRatio,\n            }, particles = this.particles.quadTree.queryCircle(posRetina, radius * pxRatio);\n            callback(e, particles);\n        }, clickHandler = (e) => {\n            if (!guardCheck(this)) {\n                return;\n            }\n            const mouseEvent = e, pos = {\n                x: mouseEvent.offsetX || mouseEvent.clientX,\n                y: mouseEvent.offsetY || mouseEvent.clientY,\n            }, radius = 1;\n            clickOrTouchHandler(e, pos, radius);\n        }, touchStartHandler = () => {\n            if (!guardCheck(this)) {\n                return;\n            }\n            touched = true;\n            touchMoved = false;\n        }, touchMoveHandler = () => {\n            if (!guardCheck(this)) {\n                return;\n            }\n            touchMoved = true;\n        }, touchEndHandler = (e) => {\n            if (!guardCheck(this)) {\n                return;\n            }\n            if (touched && !touchMoved) {\n                const touchEvent = e, lengthOffset = 1;\n                let lastTouch = touchEvent.touches[touchEvent.touches.length - lengthOffset];\n                if (!lastTouch) {\n                    lastTouch = touchEvent.changedTouches[touchEvent.changedTouches.length - lengthOffset];\n                    if (!lastTouch) {\n                        return;\n                    }\n                }\n                const element = this.canvas.element, canvasRect = element ? element.getBoundingClientRect() : undefined, minCoordinate = 0, pos = {\n                    x: lastTouch.clientX - (canvasRect ? canvasRect.left : minCoordinate),\n                    y: lastTouch.clientY - (canvasRect ? canvasRect.top : minCoordinate),\n                };\n                clickOrTouchHandler(e, pos, Math.max(lastTouch.radiusX, lastTouch.radiusY));\n            }\n            touched = false;\n            touchMoved = false;\n        }, touchCancelHandler = () => {\n            if (!guardCheck(this)) {\n                return;\n            }\n            touched = false;\n            touchMoved = false;\n        };\n        let touched = false, touchMoved = false;\n        this._clickHandlers.set(\"click\", clickHandler);\n        this._clickHandlers.set(\"touchstart\", touchStartHandler);\n        this._clickHandlers.set(\"touchmove\", touchMoveHandler);\n        this._clickHandlers.set(\"touchend\", touchEndHandler);\n        this._clickHandlers.set(\"touchcancel\", touchCancelHandler);\n        for (const [key, handler] of this._clickHandlers) {\n            el.addEventListener(key, handler);\n        }\n    }\n    addLifeTime(value) {\n        this._lifeTime += value;\n    }\n    addPath(key, generator, override = false) {\n        if (!guardCheck(this) || (!override && this.pathGenerators.has(key))) {\n            return false;\n        }\n        this.pathGenerators.set(key, generator);\n        return true;\n    }\n    alive() {\n        return !this._duration || this._lifeTime <= this._duration;\n    }\n    clearClickHandlers() {\n        if (!guardCheck(this)) {\n            return;\n        }\n        for (const [key, handler] of this._clickHandlers) {\n            this.interactivity.element?.removeEventListener(key, handler);\n        }\n        this._clickHandlers.clear();\n    }\n    destroy(remove = true) {\n        if (!guardCheck(this)) {\n            return;\n        }\n        this.stop();\n        this.clearClickHandlers();\n        this.particles.destroy();\n        this.canvas.destroy();\n        for (const [, effectDrawer] of this.effectDrawers) {\n            effectDrawer.destroy?.(this);\n        }\n        for (const [, shapeDrawer] of this.shapeDrawers) {\n            shapeDrawer.destroy?.(this);\n        }\n        for (const key of this.effectDrawers.keys()) {\n            this.effectDrawers.delete(key);\n        }\n        for (const key of this.shapeDrawers.keys()) {\n            this.shapeDrawers.delete(key);\n        }\n        this._engine.clearPlugins(this);\n        this.destroyed = true;\n        if (remove) {\n            const mainArr = this._engine.items, idx = mainArr.findIndex(t => t === this), minIndex = 0;\n            if (idx >= minIndex) {\n                const deleteCount = 1;\n                mainArr.splice(idx, deleteCount);\n            }\n        }\n        this._engine.dispatchEvent(EventType.containerDestroyed, { container: this });\n    }\n    draw(force) {\n        if (!guardCheck(this)) {\n            return;\n        }\n        let refreshTime = force;\n        const frame = (timestamp) => {\n            if (refreshTime) {\n                this._lastFrameTime = undefined;\n                refreshTime = false;\n            }\n            this._nextFrame(timestamp);\n        };\n        this._drawAnimationFrame = animate(timestamp => frame(timestamp));\n    }\n    async export(type, options = {}) {\n        for (const [, plugin] of this.plugins) {\n            if (!plugin.export) {\n                continue;\n            }\n            const res = await plugin.export(type, options);\n            if (!res.supported) {\n                continue;\n            }\n            return res.blob;\n        }\n        getLogger().error(`${errorPrefix} - Export plugin with type ${type} not found`);\n    }\n    handleClickMode(mode) {\n        if (!guardCheck(this)) {\n            return;\n        }\n        this.particles.handleClickMode(mode);\n        for (const [, plugin] of this.plugins) {\n            plugin.handleClickMode?.(mode);\n        }\n    }\n    async init() {\n        if (!guardCheck(this)) {\n            return;\n        }\n        const effects = this._engine.getSupportedEffects();\n        for (const type of effects) {\n            const drawer = this._engine.getEffectDrawer(type);\n            if (drawer) {\n                this.effectDrawers.set(type, drawer);\n            }\n        }\n        const shapes = this._engine.getSupportedShapes();\n        for (const type of shapes) {\n            const drawer = this._engine.getShapeDrawer(type);\n            if (drawer) {\n                this.shapeDrawers.set(type, drawer);\n            }\n        }\n        await this.particles.initPlugins();\n        this._options = loadContainerOptions(this._engine, this, this._initialSourceOptions, this.sourceOptions);\n        this.actualOptions = loadContainerOptions(this._engine, this, this._options);\n        const availablePlugins = await this._engine.getAvailablePlugins(this);\n        for (const [id, plugin] of availablePlugins) {\n            this.plugins.set(id, plugin);\n        }\n        this.retina.init();\n        await this.canvas.init();\n        this.updateActualOptions();\n        this.canvas.initBackground();\n        this.canvas.resize();\n        const { zLayers, duration, delay, fpsLimit, smooth } = this.actualOptions;\n        this.zLayers = zLayers;\n        this._duration = getRangeValue(duration) * millisecondsToSeconds;\n        this._delay = getRangeValue(delay) * millisecondsToSeconds;\n        this._lifeTime = 0;\n        const defaultFpsLimit = 120, minFpsLimit = 0;\n        this.fpsLimit = fpsLimit > minFpsLimit ? fpsLimit : defaultFpsLimit;\n        this._smooth = smooth;\n        for (const [, drawer] of this.effectDrawers) {\n            await drawer.init?.(this);\n        }\n        for (const [, drawer] of this.shapeDrawers) {\n            await drawer.init?.(this);\n        }\n        for (const [, plugin] of this.plugins) {\n            await plugin.init?.();\n        }\n        this._engine.dispatchEvent(EventType.containerInit, { container: this });\n        await this.particles.init();\n        this.particles.setDensity();\n        for (const [, plugin] of this.plugins) {\n            plugin.particlesSetup?.();\n        }\n        this._engine.dispatchEvent(EventType.particlesSetup, { container: this });\n    }\n    async loadTheme(name) {\n        if (!guardCheck(this)) {\n            return;\n        }\n        this._currentTheme = name;\n        await this.refresh();\n    }\n    pause() {\n        if (!guardCheck(this)) {\n            return;\n        }\n        if (this._drawAnimationFrame !== undefined) {\n            cancelAnimation(this._drawAnimationFrame);\n            delete this._drawAnimationFrame;\n        }\n        if (this._paused) {\n            return;\n        }\n        for (const [, plugin] of this.plugins) {\n            plugin.pause?.();\n        }\n        if (!this.pageHidden) {\n            this._paused = true;\n        }\n        this._engine.dispatchEvent(EventType.containerPaused, { container: this });\n    }\n    play(force) {\n        if (!guardCheck(this)) {\n            return;\n        }\n        const needsUpdate = this._paused || force;\n        if (this._firstStart && !this.actualOptions.autoPlay) {\n            this._firstStart = false;\n            return;\n        }\n        if (this._paused) {\n            this._paused = false;\n        }\n        if (needsUpdate) {\n            for (const [, plugin] of this.plugins) {\n                if (plugin.play) {\n                    plugin.play();\n                }\n            }\n        }\n        this._engine.dispatchEvent(EventType.containerPlay, { container: this });\n        this.draw(needsUpdate ?? false);\n    }\n    async refresh() {\n        if (!guardCheck(this)) {\n            return;\n        }\n        this.stop();\n        return this.start();\n    }\n    async reset(sourceOptions) {\n        if (!guardCheck(this)) {\n            return;\n        }\n        this._initialSourceOptions = sourceOptions;\n        this._sourceOptions = sourceOptions;\n        this._options = loadContainerOptions(this._engine, this, this._initialSourceOptions, this.sourceOptions);\n        this.actualOptions = loadContainerOptions(this._engine, this, this._options);\n        return this.refresh();\n    }\n    async start() {\n        if (!guardCheck(this) || this.started) {\n            return;\n        }\n        await this.init();\n        this.started = true;\n        await new Promise(resolve => {\n            const start = async () => {\n                this._eventListeners.addListeners();\n                if (this.interactivity.element instanceof HTMLElement && this._intersectionObserver) {\n                    this._intersectionObserver.observe(this.interactivity.element);\n                }\n                for (const [, plugin] of this.plugins) {\n                    await plugin.start?.();\n                }\n                this._engine.dispatchEvent(EventType.containerStarted, { container: this });\n                this.play();\n                resolve();\n            };\n            this._delayTimeout = setTimeout(() => void start(), this._delay);\n        });\n    }\n    stop() {\n        if (!guardCheck(this) || !this.started) {\n            return;\n        }\n        if (this._delayTimeout) {\n            clearTimeout(this._delayTimeout);\n            delete this._delayTimeout;\n        }\n        this._firstStart = true;\n        this.started = false;\n        this._eventListeners.removeListeners();\n        this.pause();\n        this.particles.clear();\n        this.canvas.stop();\n        if (this.interactivity.element instanceof HTMLElement && this._intersectionObserver) {\n            this._intersectionObserver.unobserve(this.interactivity.element);\n        }\n        for (const [, plugin] of this.plugins) {\n            plugin.stop?.();\n        }\n        for (const key of this.plugins.keys()) {\n            this.plugins.delete(key);\n        }\n        this._sourceOptions = this._options;\n        this._engine.dispatchEvent(EventType.containerStopped, { container: this });\n    }\n    updateActualOptions() {\n        this.actualOptions.responsive = [];\n        const newMaxWidth = this.actualOptions.setResponsive(this.canvas.size.width, this.retina.pixelRatio, this._options);\n        this.actualOptions.setTheme(this._currentTheme);\n        if (this._responsiveMaxWidth === newMaxWidth) {\n            return false;\n        }\n        this._responsiveMaxWidth = newMaxWidth;\n        return true;\n    }\n}\n"], "mappings": "AAAA,SAASA,OAAO,EAAEC,eAAe,EAAEC,aAAa,QAAQ,yBAAyB;AACjF,SAASC,WAAW,EAAEC,qBAAqB,QAAQ,sBAAsB;AACzE,SAASC,SAAS,EAAEC,wBAAwB,QAAQ,mBAAmB;AACvE,SAASC,MAAM,QAAQ,aAAa;AACpC,SAASC,cAAc,QAAQ,2BAA2B;AAC1D,SAASC,SAAS,QAAQ,6BAA6B;AACvD,SAASC,OAAO,QAAQ,+BAA+B;AACvD,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,MAAM,QAAQ,aAAa;AACpC,SAASC,WAAW,QAAQ,0BAA0B;AACtD,SAASC,UAAUA,CAACC,SAAS,EAAE;EAC3B,OAAOA,SAAS,IAAI,CAACA,SAAS,CAACC,SAAS;AAC5C;AACA,MAAMC,UAAU,GAAG,EAAE;AACrB,SAASC,SAASA,CAACC,KAAK,EAAEC,QAAQ,GAAGH,UAAU,EAAEI,MAAM,GAAG,KAAK,EAAE;EAC7D,OAAO;IACHF,KAAK;IACLG,MAAM,EAAED,MAAM,GAAGJ,UAAU,GAAGG,QAAQ,GAAIH,UAAU,GAAGE,KAAK,GAAIf;EACpE,CAAC;AACL;AACA,SAASmB,oBAAoBA,CAACC,MAAM,EAAET,SAAS,EAAE,GAAGU,gBAAgB,EAAE;EAClE,MAAMC,OAAO,GAAG,IAAIhB,OAAO,CAACc,MAAM,EAAET,SAAS,CAAC;EAC9CF,WAAW,CAACa,OAAO,EAAE,GAAGD,gBAAgB,CAAC;EACzC,OAAOC,OAAO;AAClB;AACA,OAAO,MAAMC,SAAS,CAAC;EACnBC,WAAWA,CAACJ,MAAM,EAAEK,EAAE,EAAEC,aAAa,EAAE;IACnC,IAAI,CAACC,oBAAoB,GAAGC,OAAO,IAAI;MACnC,IAAI,CAAClB,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAACmB,aAAa,CAACC,sBAAsB,EAAE;QACjE;MACJ;MACA,KAAK,MAAMC,KAAK,IAAIH,OAAO,EAAE;QACzB,IAAIG,KAAK,CAACC,MAAM,KAAK,IAAI,CAACC,aAAa,CAACC,OAAO,EAAE;UAC7C;QACJ;QACA,IAAIH,KAAK,CAACI,cAAc,EAAE;UACtB,KAAK,IAAI,CAACC,IAAI,CAAC,CAAC;QACpB,CAAC,MACI;UACD,IAAI,CAACC,KAAK,CAAC,CAAC;QAChB;MACJ;IACJ,CAAC;IACD,IAAI,CAACC,UAAU,GAAIC,SAAS,IAAK;MAC7B,IAAI;QACA,IAAI,CAAC,IAAI,CAACC,OAAO,IACb,IAAI,CAACC,cAAc,KAAKC,SAAS,IACjCH,SAAS,GAAG,IAAI,CAACE,cAAc,GAAGzC,qBAAqB,GAAG,IAAI,CAACgB,QAAQ,EAAE;UACzE,IAAI,CAAC2B,IAAI,CAAC,KAAK,CAAC;UAChB;QACJ;QACA,IAAI,CAACF,cAAc,KAAKF,SAAS;QACjC,MAAMK,KAAK,GAAG9B,SAAS,CAACyB,SAAS,GAAG,IAAI,CAACE,cAAc,EAAE,IAAI,CAACzB,QAAQ,EAAE,IAAI,CAACwB,OAAO,CAAC;QACrF,IAAI,CAACK,WAAW,CAACD,KAAK,CAAC7B,KAAK,CAAC;QAC7B,IAAI,CAAC0B,cAAc,GAAGF,SAAS;QAC/B,IAAIK,KAAK,CAAC7B,KAAK,GAAGf,qBAAqB,EAAE;UACrC,IAAI,CAAC2C,IAAI,CAAC,KAAK,CAAC;UAChB;QACJ;QACA,IAAI,CAACG,SAAS,CAACH,IAAI,CAACC,KAAK,CAAC;QAC1B,IAAI,CAAC,IAAI,CAACG,KAAK,CAAC,CAAC,EAAE;UACf,IAAI,CAACC,OAAO,CAAC,CAAC;UACd;QACJ;QACA,IAAI,IAAI,CAACC,eAAe,EAAE;UACtB,IAAI,CAACN,IAAI,CAAC,KAAK,CAAC;QACpB;MACJ,CAAC,CACD,OAAOO,CAAC,EAAE;QACNjD,SAAS,CAAC,CAAC,CAACkD,KAAK,CAAC,GAAGpD,WAAW,oBAAoB,EAAEmD,CAAC,CAAC;MAC5D;IACJ,CAAC;IACD,IAAI,CAACE,OAAO,GAAGhC,MAAM;IACrB,IAAI,CAACK,EAAE,GAAG4B,MAAM,CAAC5B,EAAE,CAAC;IACpB,IAAI,CAACT,QAAQ,GAAG,GAAG;IACnB,IAAI,CAACwB,OAAO,GAAG,KAAK;IACpB,IAAI,CAACc,MAAM,GAAG,CAAC;IACf,IAAI,CAACC,SAAS,GAAG,CAAC;IAClB,IAAI,CAACC,SAAS,GAAG,CAAC;IAClB,IAAI,CAACC,WAAW,GAAG,IAAI;IACvB,IAAI,CAACC,OAAO,GAAG,KAAK;IACpB,IAAI,CAAC9C,SAAS,GAAG,KAAK;IACtB,IAAI,CAAC+C,OAAO,GAAG,IAAI;IACnB,IAAI,CAAClB,cAAc,GAAG,CAAC;IACvB,IAAI,CAACmB,OAAO,GAAG,GAAG;IAClB,IAAI,CAACC,UAAU,GAAG,KAAK;IACvB,IAAI,CAACC,cAAc,GAAG,IAAIC,GAAG,CAAC,CAAC;IAC/B,IAAI,CAACC,cAAc,GAAGtC,aAAa;IACnC,IAAI,CAACuC,qBAAqB,GAAGvC,aAAa;IAC1C,IAAI,CAACwC,MAAM,GAAG,IAAI1D,MAAM,CAAC,IAAI,CAAC;IAC9B,IAAI,CAAC2D,MAAM,GAAG,IAAIhE,MAAM,CAAC,IAAI,CAAC;IAC9B,IAAI,CAAC2C,SAAS,GAAG,IAAIvC,SAAS,CAAC,IAAI,CAAC6C,OAAO,EAAE,IAAI,CAAC;IAClD,IAAI,CAACgB,cAAc,GAAG,IAAIL,GAAG,CAAC,CAAC;IAC/B,IAAI,CAAC9B,aAAa,GAAG;MACjBoC,KAAK,EAAE;QACHC,QAAQ,EAAE,KAAK;QACfC,MAAM,EAAE;MACZ;IACJ,CAAC;IACD,IAAI,CAACC,OAAO,GAAG,IAAIT,GAAG,CAAC,CAAC;IACxB,IAAI,CAACU,aAAa,GAAG,IAAIV,GAAG,CAAC,CAAC;IAC9B,IAAI,CAACW,YAAY,GAAG,IAAIX,GAAG,CAAC,CAAC;IAC7B,IAAI,CAACY,QAAQ,GAAGxD,oBAAoB,CAAC,IAAI,CAACiC,OAAO,EAAE,IAAI,CAAC;IACxD,IAAI,CAACvB,aAAa,GAAGV,oBAAoB,CAAC,IAAI,CAACiC,OAAO,EAAE,IAAI,CAAC;IAC7D,IAAI,CAACwB,eAAe,GAAG,IAAIxE,cAAc,CAAC,IAAI,CAAC;IAC/C,IAAI,CAACyE,qBAAqB,GAAG3E,wBAAwB,CAAC0B,OAAO,IAAI,IAAI,CAACD,oBAAoB,CAACC,OAAO,CAAC,CAAC;IACpG,IAAI,CAACwB,OAAO,CAAC0B,aAAa,CAACzE,SAAS,CAAC0E,cAAc,EAAE;MAAEpE,SAAS,EAAE;IAAK,CAAC,CAAC;EAC7E;EACA,IAAIsC,eAAeA,CAAA,EAAG;IAClB,OAAO,CAAC,IAAI,CAACU,OAAO,IAAI,CAAC,IAAI,CAACE,UAAU,IAAInD,UAAU,CAAC,IAAI,CAAC;EAChE;EACA,IAAIY,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACqD,QAAQ;EACxB;EACA,IAAIjD,aAAaA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACsC,cAAc;EAC9B;EACAgB,eAAeA,CAACC,QAAQ,EAAE;IACtB,IAAI,CAACvE,UAAU,CAAC,IAAI,CAAC,EAAE;MACnB;IACJ;IACA,MAAMwE,EAAE,GAAG,IAAI,CAACjD,aAAa,CAACC,OAAO;IACrC,IAAI,CAACgD,EAAE,EAAE;MACL;IACJ;IACA,MAAMC,mBAAmB,GAAGA,CAACjC,CAAC,EAAEkC,GAAG,EAAEC,MAAM,KAAK;QAC5C,IAAI,CAAC3E,UAAU,CAAC,IAAI,CAAC,EAAE;UACnB;QACJ;QACA,MAAM4E,OAAO,GAAG,IAAI,CAACpB,MAAM,CAACqB,UAAU;UAAEC,SAAS,GAAG;YAChDC,CAAC,EAAEL,GAAG,CAACK,CAAC,GAAGH,OAAO;YAClBI,CAAC,EAAEN,GAAG,CAACM,CAAC,GAAGJ;UACf,CAAC;UAAExC,SAAS,GAAG,IAAI,CAACA,SAAS,CAAC6C,QAAQ,CAACC,WAAW,CAACJ,SAAS,EAAEH,MAAM,GAAGC,OAAO,CAAC;QAC/EL,QAAQ,CAAC/B,CAAC,EAAEJ,SAAS,CAAC;MAC1B,CAAC;MAAE+C,YAAY,GAAI3C,CAAC,IAAK;QACrB,IAAI,CAACxC,UAAU,CAAC,IAAI,CAAC,EAAE;UACnB;QACJ;QACA,MAAMoF,UAAU,GAAG5C,CAAC;UAAEkC,GAAG,GAAG;YACxBK,CAAC,EAAEK,UAAU,CAACC,OAAO,IAAID,UAAU,CAACE,OAAO;YAC3CN,CAAC,EAAEI,UAAU,CAACG,OAAO,IAAIH,UAAU,CAACI;UACxC,CAAC;UAAEb,MAAM,GAAG,CAAC;QACbF,mBAAmB,CAACjC,CAAC,EAAEkC,GAAG,EAAEC,MAAM,CAAC;MACvC,CAAC;MAAEc,iBAAiB,GAAGA,CAAA,KAAM;QACzB,IAAI,CAACzF,UAAU,CAAC,IAAI,CAAC,EAAE;UACnB;QACJ;QACA0F,OAAO,GAAG,IAAI;QACdC,UAAU,GAAG,KAAK;MACtB,CAAC;MAAEC,gBAAgB,GAAGA,CAAA,KAAM;QACxB,IAAI,CAAC5F,UAAU,CAAC,IAAI,CAAC,EAAE;UACnB;QACJ;QACA2F,UAAU,GAAG,IAAI;MACrB,CAAC;MAAEE,eAAe,GAAIrD,CAAC,IAAK;QACxB,IAAI,CAACxC,UAAU,CAAC,IAAI,CAAC,EAAE;UACnB;QACJ;QACA,IAAI0F,OAAO,IAAI,CAACC,UAAU,EAAE;UACxB,MAAMG,UAAU,GAAGtD,CAAC;YAAEuD,YAAY,GAAG,CAAC;UACtC,IAAIC,SAAS,GAAGF,UAAU,CAACG,OAAO,CAACH,UAAU,CAACG,OAAO,CAACC,MAAM,GAAGH,YAAY,CAAC;UAC5E,IAAI,CAACC,SAAS,EAAE;YACZA,SAAS,GAAGF,UAAU,CAACK,cAAc,CAACL,UAAU,CAACK,cAAc,CAACD,MAAM,GAAGH,YAAY,CAAC;YACtF,IAAI,CAACC,SAAS,EAAE;cACZ;YACJ;UACJ;UACA,MAAMxE,OAAO,GAAG,IAAI,CAACiC,MAAM,CAACjC,OAAO;YAAE4E,UAAU,GAAG5E,OAAO,GAAGA,OAAO,CAAC6E,qBAAqB,CAAC,CAAC,GAAGrE,SAAS;YAAEsE,aAAa,GAAG,CAAC;YAAE5B,GAAG,GAAG;cAC9HK,CAAC,EAAEiB,SAAS,CAACV,OAAO,IAAIc,UAAU,GAAGA,UAAU,CAACG,IAAI,GAAGD,aAAa,CAAC;cACrEtB,CAAC,EAAEgB,SAAS,CAACR,OAAO,IAAIY,UAAU,GAAGA,UAAU,CAACI,GAAG,GAAGF,aAAa;YACvE,CAAC;UACD7B,mBAAmB,CAACjC,CAAC,EAAEkC,GAAG,EAAE+B,IAAI,CAACC,GAAG,CAACV,SAAS,CAACW,OAAO,EAAEX,SAAS,CAACY,OAAO,CAAC,CAAC;QAC/E;QACAlB,OAAO,GAAG,KAAK;QACfC,UAAU,GAAG,KAAK;MACtB,CAAC;MAAEkB,kBAAkB,GAAGA,CAAA,KAAM;QAC1B,IAAI,CAAC7G,UAAU,CAAC,IAAI,CAAC,EAAE;UACnB;QACJ;QACA0F,OAAO,GAAG,KAAK;QACfC,UAAU,GAAG,KAAK;MACtB,CAAC;IACD,IAAID,OAAO,GAAG,KAAK;MAAEC,UAAU,GAAG,KAAK;IACvC,IAAI,CAACvC,cAAc,CAAC0D,GAAG,CAAC,OAAO,EAAE3B,YAAY,CAAC;IAC9C,IAAI,CAAC/B,cAAc,CAAC0D,GAAG,CAAC,YAAY,EAAErB,iBAAiB,CAAC;IACxD,IAAI,CAACrC,cAAc,CAAC0D,GAAG,CAAC,WAAW,EAAElB,gBAAgB,CAAC;IACtD,IAAI,CAACxC,cAAc,CAAC0D,GAAG,CAAC,UAAU,EAAEjB,eAAe,CAAC;IACpD,IAAI,CAACzC,cAAc,CAAC0D,GAAG,CAAC,aAAa,EAAED,kBAAkB,CAAC;IAC1D,KAAK,MAAM,CAACE,GAAG,EAAEC,OAAO,CAAC,IAAI,IAAI,CAAC5D,cAAc,EAAE;MAC9CoB,EAAE,CAACyC,gBAAgB,CAACF,GAAG,EAAEC,OAAO,CAAC;IACrC;EACJ;EACA7E,WAAWA,CAAC9B,KAAK,EAAE;IACf,IAAI,CAACyC,SAAS,IAAIzC,KAAK;EAC3B;EACA6G,OAAOA,CAACH,GAAG,EAAEI,SAAS,EAAEC,QAAQ,GAAG,KAAK,EAAE;IACtC,IAAI,CAACpH,UAAU,CAAC,IAAI,CAAC,IAAK,CAACoH,QAAQ,IAAI,IAAI,CAAC1D,cAAc,CAAC2D,GAAG,CAACN,GAAG,CAAE,EAAE;MAClE,OAAO,KAAK;IAChB;IACA,IAAI,CAACrD,cAAc,CAACoD,GAAG,CAACC,GAAG,EAAEI,SAAS,CAAC;IACvC,OAAO,IAAI;EACf;EACA9E,KAAKA,CAAA,EAAG;IACJ,OAAO,CAAC,IAAI,CAACQ,SAAS,IAAI,IAAI,CAACC,SAAS,IAAI,IAAI,CAACD,SAAS;EAC9D;EACAyE,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACtH,UAAU,CAAC,IAAI,CAAC,EAAE;MACnB;IACJ;IACA,KAAK,MAAM,CAAC+G,GAAG,EAAEC,OAAO,CAAC,IAAI,IAAI,CAAC5D,cAAc,EAAE;MAC9C,IAAI,CAAC7B,aAAa,CAACC,OAAO,EAAE+F,mBAAmB,CAACR,GAAG,EAAEC,OAAO,CAAC;IACjE;IACA,IAAI,CAAC5D,cAAc,CAACoE,KAAK,CAAC,CAAC;EAC/B;EACAlF,OAAOA,CAACmF,MAAM,GAAG,IAAI,EAAE;IACnB,IAAI,CAACzH,UAAU,CAAC,IAAI,CAAC,EAAE;MACnB;IACJ;IACA,IAAI,CAAC0H,IAAI,CAAC,CAAC;IACX,IAAI,CAACJ,kBAAkB,CAAC,CAAC;IACzB,IAAI,CAAClF,SAAS,CAACE,OAAO,CAAC,CAAC;IACxB,IAAI,CAACmB,MAAM,CAACnB,OAAO,CAAC,CAAC;IACrB,KAAK,MAAM,GAAGqF,YAAY,CAAC,IAAI,IAAI,CAAC5D,aAAa,EAAE;MAC/C4D,YAAY,CAACrF,OAAO,GAAG,IAAI,CAAC;IAChC;IACA,KAAK,MAAM,GAAGsF,WAAW,CAAC,IAAI,IAAI,CAAC5D,YAAY,EAAE;MAC7C4D,WAAW,CAACtF,OAAO,GAAG,IAAI,CAAC;IAC/B;IACA,KAAK,MAAMyE,GAAG,IAAI,IAAI,CAAChD,aAAa,CAAC8D,IAAI,CAAC,CAAC,EAAE;MACzC,IAAI,CAAC9D,aAAa,CAAC+D,MAAM,CAACf,GAAG,CAAC;IAClC;IACA,KAAK,MAAMA,GAAG,IAAI,IAAI,CAAC/C,YAAY,CAAC6D,IAAI,CAAC,CAAC,EAAE;MACxC,IAAI,CAAC7D,YAAY,CAAC8D,MAAM,CAACf,GAAG,CAAC;IACjC;IACA,IAAI,CAACrE,OAAO,CAACqF,YAAY,CAAC,IAAI,CAAC;IAC/B,IAAI,CAAC7H,SAAS,GAAG,IAAI;IACrB,IAAIuH,MAAM,EAAE;MACR,MAAMO,OAAO,GAAG,IAAI,CAACtF,OAAO,CAACuF,KAAK;QAAEC,GAAG,GAAGF,OAAO,CAACG,SAAS,CAACC,CAAC,IAAIA,CAAC,KAAK,IAAI,CAAC;QAAEC,QAAQ,GAAG,CAAC;MAC1F,IAAIH,GAAG,IAAIG,QAAQ,EAAE;QACjB,MAAMC,WAAW,GAAG,CAAC;QACrBN,OAAO,CAACO,MAAM,CAACL,GAAG,EAAEI,WAAW,CAAC;MACpC;IACJ;IACA,IAAI,CAAC5F,OAAO,CAAC0B,aAAa,CAACzE,SAAS,CAAC6I,kBAAkB,EAAE;MAAEvI,SAAS,EAAE;IAAK,CAAC,CAAC;EACjF;EACAgC,IAAIA,CAACwG,KAAK,EAAE;IACR,IAAI,CAACzI,UAAU,CAAC,IAAI,CAAC,EAAE;MACnB;IACJ;IACA,IAAI0I,WAAW,GAAGD,KAAK;IACvB,MAAME,KAAK,GAAI9G,SAAS,IAAK;MACzB,IAAI6G,WAAW,EAAE;QACb,IAAI,CAAC3G,cAAc,GAAGC,SAAS;QAC/B0G,WAAW,GAAG,KAAK;MACvB;MACA,IAAI,CAAC9G,UAAU,CAACC,SAAS,CAAC;IAC9B,CAAC;IACD,IAAI,CAAC+G,mBAAmB,GAAG1J,OAAO,CAAC2C,SAAS,IAAI8G,KAAK,CAAC9G,SAAS,CAAC,CAAC;EACrE;EACA,MAAMgH,MAAMA,CAACC,IAAI,EAAElI,OAAO,GAAG,CAAC,CAAC,EAAE;IAC7B,KAAK,MAAM,GAAGmI,MAAM,CAAC,IAAI,IAAI,CAACjF,OAAO,EAAE;MACnC,IAAI,CAACiF,MAAM,CAACF,MAAM,EAAE;QAChB;MACJ;MACA,MAAMG,GAAG,GAAG,MAAMD,MAAM,CAACF,MAAM,CAACC,IAAI,EAAElI,OAAO,CAAC;MAC9C,IAAI,CAACoI,GAAG,CAACC,SAAS,EAAE;QAChB;MACJ;MACA,OAAOD,GAAG,CAACE,IAAI;IACnB;IACA3J,SAAS,CAAC,CAAC,CAACkD,KAAK,CAAC,GAAGpD,WAAW,8BAA8ByJ,IAAI,YAAY,CAAC;EACnF;EACAK,eAAeA,CAACC,IAAI,EAAE;IAClB,IAAI,CAACpJ,UAAU,CAAC,IAAI,CAAC,EAAE;MACnB;IACJ;IACA,IAAI,CAACoC,SAAS,CAAC+G,eAAe,CAACC,IAAI,CAAC;IACpC,KAAK,MAAM,GAAGL,MAAM,CAAC,IAAI,IAAI,CAACjF,OAAO,EAAE;MACnCiF,MAAM,CAACI,eAAe,GAAGC,IAAI,CAAC;IAClC;EACJ;EACA,MAAMC,IAAIA,CAAA,EAAG;IACT,IAAI,CAACrJ,UAAU,CAAC,IAAI,CAAC,EAAE;MACnB;IACJ;IACA,MAAMsJ,OAAO,GAAG,IAAI,CAAC5G,OAAO,CAAC6G,mBAAmB,CAAC,CAAC;IAClD,KAAK,MAAMT,IAAI,IAAIQ,OAAO,EAAE;MACxB,MAAME,MAAM,GAAG,IAAI,CAAC9G,OAAO,CAAC+G,eAAe,CAACX,IAAI,CAAC;MACjD,IAAIU,MAAM,EAAE;QACR,IAAI,CAACzF,aAAa,CAAC+C,GAAG,CAACgC,IAAI,EAAEU,MAAM,CAAC;MACxC;IACJ;IACA,MAAME,MAAM,GAAG,IAAI,CAAChH,OAAO,CAACiH,kBAAkB,CAAC,CAAC;IAChD,KAAK,MAAMb,IAAI,IAAIY,MAAM,EAAE;MACvB,MAAMF,MAAM,GAAG,IAAI,CAAC9G,OAAO,CAACkH,cAAc,CAACd,IAAI,CAAC;MAChD,IAAIU,MAAM,EAAE;QACR,IAAI,CAACxF,YAAY,CAAC8C,GAAG,CAACgC,IAAI,EAAEU,MAAM,CAAC;MACvC;IACJ;IACA,MAAM,IAAI,CAACpH,SAAS,CAACyH,WAAW,CAAC,CAAC;IAClC,IAAI,CAAC5F,QAAQ,GAAGxD,oBAAoB,CAAC,IAAI,CAACiC,OAAO,EAAE,IAAI,EAAE,IAAI,CAACa,qBAAqB,EAAE,IAAI,CAACvC,aAAa,CAAC;IACxG,IAAI,CAACG,aAAa,GAAGV,oBAAoB,CAAC,IAAI,CAACiC,OAAO,EAAE,IAAI,EAAE,IAAI,CAACuB,QAAQ,CAAC;IAC5E,MAAM6F,gBAAgB,GAAG,MAAM,IAAI,CAACpH,OAAO,CAACqH,mBAAmB,CAAC,IAAI,CAAC;IACrE,KAAK,MAAM,CAAChJ,EAAE,EAAEgI,MAAM,CAAC,IAAIe,gBAAgB,EAAE;MACzC,IAAI,CAAChG,OAAO,CAACgD,GAAG,CAAC/F,EAAE,EAAEgI,MAAM,CAAC;IAChC;IACA,IAAI,CAACvF,MAAM,CAAC6F,IAAI,CAAC,CAAC;IAClB,MAAM,IAAI,CAAC5F,MAAM,CAAC4F,IAAI,CAAC,CAAC;IACxB,IAAI,CAACW,mBAAmB,CAAC,CAAC;IAC1B,IAAI,CAACvG,MAAM,CAACwG,cAAc,CAAC,CAAC;IAC5B,IAAI,CAACxG,MAAM,CAACyG,MAAM,CAAC,CAAC;IACpB,MAAM;MAAEhH,OAAO;MAAEiH,QAAQ;MAAEC,KAAK;MAAE9J,QAAQ;MAAEC;IAAO,CAAC,GAAG,IAAI,CAACY,aAAa;IACzE,IAAI,CAAC+B,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACL,SAAS,GAAGzD,aAAa,CAAC+K,QAAQ,CAAC,GAAG7K,qBAAqB;IAChE,IAAI,CAACsD,MAAM,GAAGxD,aAAa,CAACgL,KAAK,CAAC,GAAG9K,qBAAqB;IAC1D,IAAI,CAACwD,SAAS,GAAG,CAAC;IAClB,MAAMuH,eAAe,GAAG,GAAG;MAAEC,WAAW,GAAG,CAAC;IAC5C,IAAI,CAAChK,QAAQ,GAAGA,QAAQ,GAAGgK,WAAW,GAAGhK,QAAQ,GAAG+J,eAAe;IACnE,IAAI,CAACvI,OAAO,GAAGvB,MAAM;IACrB,KAAK,MAAM,GAAGiJ,MAAM,CAAC,IAAI,IAAI,CAACzF,aAAa,EAAE;MACzC,MAAMyF,MAAM,CAACH,IAAI,GAAG,IAAI,CAAC;IAC7B;IACA,KAAK,MAAM,GAAGG,MAAM,CAAC,IAAI,IAAI,CAACxF,YAAY,EAAE;MACxC,MAAMwF,MAAM,CAACH,IAAI,GAAG,IAAI,CAAC;IAC7B;IACA,KAAK,MAAM,GAAGN,MAAM,CAAC,IAAI,IAAI,CAACjF,OAAO,EAAE;MACnC,MAAMiF,MAAM,CAACM,IAAI,GAAG,CAAC;IACzB;IACA,IAAI,CAAC3G,OAAO,CAAC0B,aAAa,CAACzE,SAAS,CAAC4K,aAAa,EAAE;MAAEtK,SAAS,EAAE;IAAK,CAAC,CAAC;IACxE,MAAM,IAAI,CAACmC,SAAS,CAACiH,IAAI,CAAC,CAAC;IAC3B,IAAI,CAACjH,SAAS,CAACoI,UAAU,CAAC,CAAC;IAC3B,KAAK,MAAM,GAAGzB,MAAM,CAAC,IAAI,IAAI,CAACjF,OAAO,EAAE;MACnCiF,MAAM,CAAC0B,cAAc,GAAG,CAAC;IAC7B;IACA,IAAI,CAAC/H,OAAO,CAAC0B,aAAa,CAACzE,SAAS,CAAC8K,cAAc,EAAE;MAAExK,SAAS,EAAE;IAAK,CAAC,CAAC;EAC7E;EACA,MAAMyK,SAASA,CAACC,IAAI,EAAE;IAClB,IAAI,CAAC3K,UAAU,CAAC,IAAI,CAAC,EAAE;MACnB;IACJ;IACA,IAAI,CAAC4K,aAAa,GAAGD,IAAI;IACzB,MAAM,IAAI,CAACE,OAAO,CAAC,CAAC;EACxB;EACAlJ,KAAKA,CAAA,EAAG;IACJ,IAAI,CAAC3B,UAAU,CAAC,IAAI,CAAC,EAAE;MACnB;IACJ;IACA,IAAI,IAAI,CAAC4I,mBAAmB,KAAK5G,SAAS,EAAE;MACxC7C,eAAe,CAAC,IAAI,CAACyJ,mBAAmB,CAAC;MACzC,OAAO,IAAI,CAACA,mBAAmB;IACnC;IACA,IAAI,IAAI,CAAC3F,OAAO,EAAE;MACd;IACJ;IACA,KAAK,MAAM,GAAG8F,MAAM,CAAC,IAAI,IAAI,CAACjF,OAAO,EAAE;MACnCiF,MAAM,CAACpH,KAAK,GAAG,CAAC;IACpB;IACA,IAAI,CAAC,IAAI,CAACwB,UAAU,EAAE;MAClB,IAAI,CAACF,OAAO,GAAG,IAAI;IACvB;IACA,IAAI,CAACP,OAAO,CAAC0B,aAAa,CAACzE,SAAS,CAACmL,eAAe,EAAE;MAAE7K,SAAS,EAAE;IAAK,CAAC,CAAC;EAC9E;EACAyB,IAAIA,CAAC+G,KAAK,EAAE;IACR,IAAI,CAACzI,UAAU,CAAC,IAAI,CAAC,EAAE;MACnB;IACJ;IACA,MAAM+K,WAAW,GAAG,IAAI,CAAC9H,OAAO,IAAIwF,KAAK;IACzC,IAAI,IAAI,CAAC1F,WAAW,IAAI,CAAC,IAAI,CAAC5B,aAAa,CAAC6J,QAAQ,EAAE;MAClD,IAAI,CAACjI,WAAW,GAAG,KAAK;MACxB;IACJ;IACA,IAAI,IAAI,CAACE,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,GAAG,KAAK;IACxB;IACA,IAAI8H,WAAW,EAAE;MACb,KAAK,MAAM,GAAGhC,MAAM,CAAC,IAAI,IAAI,CAACjF,OAAO,EAAE;QACnC,IAAIiF,MAAM,CAACrH,IAAI,EAAE;UACbqH,MAAM,CAACrH,IAAI,CAAC,CAAC;QACjB;MACJ;IACJ;IACA,IAAI,CAACgB,OAAO,CAAC0B,aAAa,CAACzE,SAAS,CAACsL,aAAa,EAAE;MAAEhL,SAAS,EAAE;IAAK,CAAC,CAAC;IACxE,IAAI,CAACgC,IAAI,CAAC8I,WAAW,IAAI,KAAK,CAAC;EACnC;EACA,MAAMF,OAAOA,CAAA,EAAG;IACZ,IAAI,CAAC7K,UAAU,CAAC,IAAI,CAAC,EAAE;MACnB;IACJ;IACA,IAAI,CAAC0H,IAAI,CAAC,CAAC;IACX,OAAO,IAAI,CAACwD,KAAK,CAAC,CAAC;EACvB;EACA,MAAMC,KAAKA,CAACnK,aAAa,EAAE;IACvB,IAAI,CAAChB,UAAU,CAAC,IAAI,CAAC,EAAE;MACnB;IACJ;IACA,IAAI,CAACuD,qBAAqB,GAAGvC,aAAa;IAC1C,IAAI,CAACsC,cAAc,GAAGtC,aAAa;IACnC,IAAI,CAACiD,QAAQ,GAAGxD,oBAAoB,CAAC,IAAI,CAACiC,OAAO,EAAE,IAAI,EAAE,IAAI,CAACa,qBAAqB,EAAE,IAAI,CAACvC,aAAa,CAAC;IACxG,IAAI,CAACG,aAAa,GAAGV,oBAAoB,CAAC,IAAI,CAACiC,OAAO,EAAE,IAAI,EAAE,IAAI,CAACuB,QAAQ,CAAC;IAC5E,OAAO,IAAI,CAAC4G,OAAO,CAAC,CAAC;EACzB;EACA,MAAMK,KAAKA,CAAA,EAAG;IACV,IAAI,CAAClL,UAAU,CAAC,IAAI,CAAC,IAAI,IAAI,CAACgD,OAAO,EAAE;MACnC;IACJ;IACA,MAAM,IAAI,CAACqG,IAAI,CAAC,CAAC;IACjB,IAAI,CAACrG,OAAO,GAAG,IAAI;IACnB,MAAM,IAAIoI,OAAO,CAACC,OAAO,IAAI;MACzB,MAAMH,KAAK,GAAG,MAAAA,CAAA,KAAY;QACtB,IAAI,CAAChH,eAAe,CAACoH,YAAY,CAAC,CAAC;QACnC,IAAI,IAAI,CAAC/J,aAAa,CAACC,OAAO,YAAY+J,WAAW,IAAI,IAAI,CAACpH,qBAAqB,EAAE;UACjF,IAAI,CAACA,qBAAqB,CAACqH,OAAO,CAAC,IAAI,CAACjK,aAAa,CAACC,OAAO,CAAC;QAClE;QACA,KAAK,MAAM,GAAGuH,MAAM,CAAC,IAAI,IAAI,CAACjF,OAAO,EAAE;UACnC,MAAMiF,MAAM,CAACmC,KAAK,GAAG,CAAC;QAC1B;QACA,IAAI,CAACxI,OAAO,CAAC0B,aAAa,CAACzE,SAAS,CAAC8L,gBAAgB,EAAE;UAAExL,SAAS,EAAE;QAAK,CAAC,CAAC;QAC3E,IAAI,CAACyB,IAAI,CAAC,CAAC;QACX2J,OAAO,CAAC,CAAC;MACb,CAAC;MACD,IAAI,CAACK,aAAa,GAAGC,UAAU,CAAC,MAAM,KAAKT,KAAK,CAAC,CAAC,EAAE,IAAI,CAACtI,MAAM,CAAC;IACpE,CAAC,CAAC;EACN;EACA8E,IAAIA,CAAA,EAAG;IACH,IAAI,CAAC1H,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAACgD,OAAO,EAAE;MACpC;IACJ;IACA,IAAI,IAAI,CAAC0I,aAAa,EAAE;MACpBE,YAAY,CAAC,IAAI,CAACF,aAAa,CAAC;MAChC,OAAO,IAAI,CAACA,aAAa;IAC7B;IACA,IAAI,CAAC3I,WAAW,GAAG,IAAI;IACvB,IAAI,CAACC,OAAO,GAAG,KAAK;IACpB,IAAI,CAACkB,eAAe,CAAC2H,eAAe,CAAC,CAAC;IACtC,IAAI,CAAClK,KAAK,CAAC,CAAC;IACZ,IAAI,CAACS,SAAS,CAACoF,KAAK,CAAC,CAAC;IACtB,IAAI,CAAC/D,MAAM,CAACiE,IAAI,CAAC,CAAC;IAClB,IAAI,IAAI,CAACnG,aAAa,CAACC,OAAO,YAAY+J,WAAW,IAAI,IAAI,CAACpH,qBAAqB,EAAE;MACjF,IAAI,CAACA,qBAAqB,CAAC2H,SAAS,CAAC,IAAI,CAACvK,aAAa,CAACC,OAAO,CAAC;IACpE;IACA,KAAK,MAAM,GAAGuH,MAAM,CAAC,IAAI,IAAI,CAACjF,OAAO,EAAE;MACnCiF,MAAM,CAACrB,IAAI,GAAG,CAAC;IACnB;IACA,KAAK,MAAMX,GAAG,IAAI,IAAI,CAACjD,OAAO,CAAC+D,IAAI,CAAC,CAAC,EAAE;MACnC,IAAI,CAAC/D,OAAO,CAACgE,MAAM,CAACf,GAAG,CAAC;IAC5B;IACA,IAAI,CAACzD,cAAc,GAAG,IAAI,CAACW,QAAQ;IACnC,IAAI,CAACvB,OAAO,CAAC0B,aAAa,CAACzE,SAAS,CAACoM,gBAAgB,EAAE;MAAE9L,SAAS,EAAE;IAAK,CAAC,CAAC;EAC/E;EACA+J,mBAAmBA,CAAA,EAAG;IAClB,IAAI,CAAC7I,aAAa,CAAC6K,UAAU,GAAG,EAAE;IAClC,MAAMC,WAAW,GAAG,IAAI,CAAC9K,aAAa,CAAC+K,aAAa,CAAC,IAAI,CAACzI,MAAM,CAAC0I,IAAI,CAACC,KAAK,EAAE,IAAI,CAAC5I,MAAM,CAACqB,UAAU,EAAE,IAAI,CAACZ,QAAQ,CAAC;IACnH,IAAI,CAAC9C,aAAa,CAACkL,QAAQ,CAAC,IAAI,CAACzB,aAAa,CAAC;IAC/C,IAAI,IAAI,CAAC0B,mBAAmB,KAAKL,WAAW,EAAE;MAC1C,OAAO,KAAK;IAChB;IACA,IAAI,CAACK,mBAAmB,GAAGL,WAAW;IACtC,OAAO,IAAI;EACf;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
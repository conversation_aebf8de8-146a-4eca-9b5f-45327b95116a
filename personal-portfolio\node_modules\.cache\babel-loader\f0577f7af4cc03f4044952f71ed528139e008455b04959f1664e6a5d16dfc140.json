{"ast": null, "code": "import { GrabLinks } from \"./GrabLinks.js\";\nexport class Grab {\n  constructor() {\n    this.distance = 100;\n    this.links = new GrabLinks();\n  }\n  load(data) {\n    if (!data) {\n      return;\n    }\n    if (data.distance !== undefined) {\n      this.distance = data.distance;\n    }\n    this.links.load(data.links);\n  }\n}", "map": {"version": 3, "names": ["GrabLinks", "<PERSON>rab", "constructor", "distance", "links", "load", "data", "undefined"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/interaction-external-grab/browser/Options/Classes/Grab.js"], "sourcesContent": ["import { GrabLinks } from \"./GrabLinks.js\";\nexport class Grab {\n    constructor() {\n        this.distance = 100;\n        this.links = new GrabLinks();\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.distance !== undefined) {\n            this.distance = data.distance;\n        }\n        this.links.load(data.links);\n    }\n}\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,gBAAgB;AAC1C,OAAO,MAAMC,IAAI,CAAC;EACdC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,QAAQ,GAAG,GAAG;IACnB,IAAI,CAACC,KAAK,GAAG,IAAIJ,SAAS,CAAC,CAAC;EAChC;EACAK,IAAIA,CAACC,IAAI,EAAE;IACP,IAAI,CAACA,IAAI,EAAE;MACP;IACJ;IACA,IAAIA,IAAI,CAACH,QAAQ,KAAKI,SAAS,EAAE;MAC7B,IAAI,CAACJ,QAAQ,GAAGG,IAAI,CAACH,QAAQ;IACjC;IACA,IAAI,CAACC,KAAK,CAACC,IAAI,CAACC,IAAI,CAACF,KAAK,CAAC;EAC/B;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { ExternalInteractorBase, isInArray } from \"@tsparticles/engine\";\nimport { Connect } from \"./Options/Classes/Connect.js\";\nimport { drawConnection } from \"./Utils.js\";\nconst connectMode = \"connect\",\n  minDistance = 0;\nexport class Connector extends ExternalInteractorBase {\n  constructor(container) {\n    super(container);\n  }\n  clear() {}\n  init() {\n    const container = this.container,\n      connect = container.actualOptions.interactivity.modes.connect;\n    if (!connect) {\n      return;\n    }\n    container.retina.connectModeDistance = connect.distance * container.retina.pixelRatio;\n    container.retina.connectModeRadius = connect.radius * container.retina.pixelRatio;\n  }\n  interact() {\n    const container = this.container,\n      options = container.actualOptions;\n    if (options.interactivity.events.onHover.enable && container.interactivity.status === \"pointermove\") {\n      const mousePos = container.interactivity.mouse.position,\n        {\n          connectModeDistance,\n          connectModeRadius\n        } = container.retina;\n      if (!connectModeDistance || connectModeDistance < minDistance || !connectModeRadius || connectModeRadius < minDistance || !mousePos) {\n        return;\n      }\n      const distance = Math.abs(connectModeRadius),\n        query = container.particles.quadTree.queryCircle(mousePos, distance, p => this.isEnabled(p));\n      query.forEach((p1, i) => {\n        const pos1 = p1.getPosition(),\n          indexOffset = 1;\n        for (const p2 of query.slice(i + indexOffset)) {\n          const pos2 = p2.getPosition(),\n            distMax = Math.abs(connectModeDistance),\n            xDiff = Math.abs(pos1.x - pos2.x),\n            yDiff = Math.abs(pos1.y - pos2.y);\n          if (xDiff < distMax && yDiff < distMax) {\n            drawConnection(container, p1, p2);\n          }\n        }\n      });\n    }\n  }\n  isEnabled(particle) {\n    const container = this.container,\n      mouse = container.interactivity.mouse,\n      events = (particle?.interactivity ?? container.actualOptions.interactivity).events;\n    if (!(events.onHover.enable && mouse.position)) {\n      return false;\n    }\n    return isInArray(connectMode, events.onHover.mode);\n  }\n  loadModeOptions(options, ...sources) {\n    if (!options.connect) {\n      options.connect = new Connect();\n    }\n    for (const source of sources) {\n      options.connect.load(source?.connect);\n    }\n  }\n  reset() {}\n}", "map": {"version": 3, "names": ["ExternalInteractorBase", "isInArray", "Connect", "drawConnection", "connectMode", "minDistance", "Connector", "constructor", "container", "clear", "init", "connect", "actualOptions", "interactivity", "modes", "retina", "connectModeDistance", "distance", "pixelRatio", "connectModeRadius", "radius", "interact", "options", "events", "onHover", "enable", "status", "mousePos", "mouse", "position", "Math", "abs", "query", "particles", "quadTree", "queryCircle", "p", "isEnabled", "for<PERSON>ach", "p1", "i", "pos1", "getPosition", "indexOffset", "p2", "slice", "pos2", "distMax", "xDiff", "x", "yDiff", "y", "particle", "mode", "loadModeOptions", "sources", "source", "load", "reset"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/interaction-external-connect/browser/Connector.js"], "sourcesContent": ["import { ExternalInteractorBase, isInArray, } from \"@tsparticles/engine\";\nimport { Connect } from \"./Options/Classes/Connect.js\";\nimport { drawConnection } from \"./Utils.js\";\nconst connectMode = \"connect\", minDistance = 0;\nexport class Connector extends ExternalInteractorBase {\n    constructor(container) {\n        super(container);\n    }\n    clear() {\n    }\n    init() {\n        const container = this.container, connect = container.actualOptions.interactivity.modes.connect;\n        if (!connect) {\n            return;\n        }\n        container.retina.connectModeDistance = connect.distance * container.retina.pixelRatio;\n        container.retina.connectModeRadius = connect.radius * container.retina.pixelRatio;\n    }\n    interact() {\n        const container = this.container, options = container.actualOptions;\n        if (options.interactivity.events.onHover.enable && container.interactivity.status === \"pointermove\") {\n            const mousePos = container.interactivity.mouse.position, { connectModeDistance, connectModeRadius } = container.retina;\n            if (!connectModeDistance ||\n                connectModeDistance < minDistance ||\n                !connectModeRadius ||\n                connectModeRadius < minDistance ||\n                !mousePos) {\n                return;\n            }\n            const distance = Math.abs(connectModeRadius), query = container.particles.quadTree.queryCircle(mousePos, distance, p => this.isEnabled(p));\n            query.forEach((p1, i) => {\n                const pos1 = p1.getPosition(), indexOffset = 1;\n                for (const p2 of query.slice(i + indexOffset)) {\n                    const pos2 = p2.getPosition(), distMax = Math.abs(connectModeDistance), xDiff = Math.abs(pos1.x - pos2.x), yDiff = Math.abs(pos1.y - pos2.y);\n                    if (xDiff < distMax && yDiff < distMax) {\n                        drawConnection(container, p1, p2);\n                    }\n                }\n            });\n        }\n    }\n    isEnabled(particle) {\n        const container = this.container, mouse = container.interactivity.mouse, events = (particle?.interactivity ?? container.actualOptions.interactivity).events;\n        if (!(events.onHover.enable && mouse.position)) {\n            return false;\n        }\n        return isInArray(connectMode, events.onHover.mode);\n    }\n    loadModeOptions(options, ...sources) {\n        if (!options.connect) {\n            options.connect = new Connect();\n        }\n        for (const source of sources) {\n            options.connect.load(source?.connect);\n        }\n    }\n    reset() {\n    }\n}\n"], "mappings": "AAAA,SAASA,sBAAsB,EAAEC,SAAS,QAAS,qBAAqB;AACxE,SAASC,OAAO,QAAQ,8BAA8B;AACtD,SAASC,cAAc,QAAQ,YAAY;AAC3C,MAAMC,WAAW,GAAG,SAAS;EAAEC,WAAW,GAAG,CAAC;AAC9C,OAAO,MAAMC,SAAS,SAASN,sBAAsB,CAAC;EAClDO,WAAWA,CAACC,SAAS,EAAE;IACnB,KAAK,CAACA,SAAS,CAAC;EACpB;EACAC,KAAKA,CAAA,EAAG,CACR;EACAC,IAAIA,CAAA,EAAG;IACH,MAAMF,SAAS,GAAG,IAAI,CAACA,SAAS;MAAEG,OAAO,GAAGH,SAAS,CAACI,aAAa,CAACC,aAAa,CAACC,KAAK,CAACH,OAAO;IAC/F,IAAI,CAACA,OAAO,EAAE;MACV;IACJ;IACAH,SAAS,CAACO,MAAM,CAACC,mBAAmB,GAAGL,OAAO,CAACM,QAAQ,GAAGT,SAAS,CAACO,MAAM,CAACG,UAAU;IACrFV,SAAS,CAACO,MAAM,CAACI,iBAAiB,GAAGR,OAAO,CAACS,MAAM,GAAGZ,SAAS,CAACO,MAAM,CAACG,UAAU;EACrF;EACAG,QAAQA,CAAA,EAAG;IACP,MAAMb,SAAS,GAAG,IAAI,CAACA,SAAS;MAAEc,OAAO,GAAGd,SAAS,CAACI,aAAa;IACnE,IAAIU,OAAO,CAACT,aAAa,CAACU,MAAM,CAACC,OAAO,CAACC,MAAM,IAAIjB,SAAS,CAACK,aAAa,CAACa,MAAM,KAAK,aAAa,EAAE;MACjG,MAAMC,QAAQ,GAAGnB,SAAS,CAACK,aAAa,CAACe,KAAK,CAACC,QAAQ;QAAE;UAAEb,mBAAmB;UAAEG;QAAkB,CAAC,GAAGX,SAAS,CAACO,MAAM;MACtH,IAAI,CAACC,mBAAmB,IACpBA,mBAAmB,GAAGX,WAAW,IACjC,CAACc,iBAAiB,IAClBA,iBAAiB,GAAGd,WAAW,IAC/B,CAACsB,QAAQ,EAAE;QACX;MACJ;MACA,MAAMV,QAAQ,GAAGa,IAAI,CAACC,GAAG,CAACZ,iBAAiB,CAAC;QAAEa,KAAK,GAAGxB,SAAS,CAACyB,SAAS,CAACC,QAAQ,CAACC,WAAW,CAACR,QAAQ,EAAEV,QAAQ,EAAEmB,CAAC,IAAI,IAAI,CAACC,SAAS,CAACD,CAAC,CAAC,CAAC;MAC1IJ,KAAK,CAACM,OAAO,CAAC,CAACC,EAAE,EAAEC,CAAC,KAAK;QACrB,MAAMC,IAAI,GAAGF,EAAE,CAACG,WAAW,CAAC,CAAC;UAAEC,WAAW,GAAG,CAAC;QAC9C,KAAK,MAAMC,EAAE,IAAIZ,KAAK,CAACa,KAAK,CAACL,CAAC,GAAGG,WAAW,CAAC,EAAE;UAC3C,MAAMG,IAAI,GAAGF,EAAE,CAACF,WAAW,CAAC,CAAC;YAAEK,OAAO,GAAGjB,IAAI,CAACC,GAAG,CAACf,mBAAmB,CAAC;YAAEgC,KAAK,GAAGlB,IAAI,CAACC,GAAG,CAACU,IAAI,CAACQ,CAAC,GAAGH,IAAI,CAACG,CAAC,CAAC;YAAEC,KAAK,GAAGpB,IAAI,CAACC,GAAG,CAACU,IAAI,CAACU,CAAC,GAAGL,IAAI,CAACK,CAAC,CAAC;UAC5I,IAAIH,KAAK,GAAGD,OAAO,IAAIG,KAAK,GAAGH,OAAO,EAAE;YACpC5C,cAAc,CAACK,SAAS,EAAE+B,EAAE,EAAEK,EAAE,CAAC;UACrC;QACJ;MACJ,CAAC,CAAC;IACN;EACJ;EACAP,SAASA,CAACe,QAAQ,EAAE;IAChB,MAAM5C,SAAS,GAAG,IAAI,CAACA,SAAS;MAAEoB,KAAK,GAAGpB,SAAS,CAACK,aAAa,CAACe,KAAK;MAAEL,MAAM,GAAG,CAAC6B,QAAQ,EAAEvC,aAAa,IAAIL,SAAS,CAACI,aAAa,CAACC,aAAa,EAAEU,MAAM;IAC3J,IAAI,EAAEA,MAAM,CAACC,OAAO,CAACC,MAAM,IAAIG,KAAK,CAACC,QAAQ,CAAC,EAAE;MAC5C,OAAO,KAAK;IAChB;IACA,OAAO5B,SAAS,CAACG,WAAW,EAAEmB,MAAM,CAACC,OAAO,CAAC6B,IAAI,CAAC;EACtD;EACAC,eAAeA,CAAChC,OAAO,EAAE,GAAGiC,OAAO,EAAE;IACjC,IAAI,CAACjC,OAAO,CAACX,OAAO,EAAE;MAClBW,OAAO,CAACX,OAAO,GAAG,IAAIT,OAAO,CAAC,CAAC;IACnC;IACA,KAAK,MAAMsD,MAAM,IAAID,OAAO,EAAE;MAC1BjC,OAAO,CAACX,OAAO,CAAC8C,IAAI,CAACD,MAAM,EAAE7C,OAAO,CAAC;IACzC;EACJ;EACA+C,KAAKA,CAAA,EAAG,CACR;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
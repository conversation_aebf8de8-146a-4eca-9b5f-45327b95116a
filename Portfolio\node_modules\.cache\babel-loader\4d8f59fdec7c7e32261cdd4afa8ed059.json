{"ast": null, "code": "export class PolygonMaskInline {\n  constructor() {\n    this.arrangement = \"one-per-point\";\n  }\n\n  load(data) {\n    if (!data) {\n      return;\n    }\n\n    if (data.arrangement !== undefined) {\n      this.arrangement = data.arrangement;\n    }\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Plugins/PolygonMask/Options/Classes/PolygonMaskInline.js"], "names": ["PolygonMaskInline", "constructor", "arrangement", "load", "data", "undefined"], "mappings": "AAAA,OAAO,MAAMA,iBAAN,CAAwB;AAC3BC,EAAAA,WAAW,GAAG;AACV,SAAKC,WAAL,GAAmB,eAAnB;AACH;;AACDC,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,QAAI,CAACA,IAAL,EAAW;AACP;AACH;;AACD,QAAIA,IAAI,CAACF,WAAL,KAAqBG,SAAzB,EAAoC;AAChC,WAAKH,WAAL,GAAmBE,IAAI,CAACF,WAAxB;AACH;AACJ;;AAX0B", "sourcesContent": ["export class PolygonMaskInline {\n    constructor() {\n        this.arrangement = \"one-per-point\";\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.arrangement !== undefined) {\n            this.arrangement = data.arrangement;\n        }\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
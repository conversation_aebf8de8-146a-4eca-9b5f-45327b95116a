{"ast": null, "code": "import { RangeType } from \"../../Types/RangeType.js\";\nimport { getDistance } from \"../../Utils/NumberUtils.js\";\nconst squareExp = 2;\nexport class BaseRange {\n  constructor(x, y, type) {\n    this.position = {\n      x: x,\n      y: y\n    };\n    this.type = type;\n  }\n}\nexport class Circle extends BaseRange {\n  constructor(x, y, radius) {\n    super(x, y, RangeType.circle);\n    this.radius = radius;\n  }\n  contains(point) {\n    return getDistance(point, this.position) <= this.radius;\n  }\n  intersects(range) {\n    const pos1 = this.position,\n      pos2 = range.position,\n      distPos = {\n        x: Math.abs(pos2.x - pos1.x),\n        y: Math.abs(pos2.y - pos1.y)\n      },\n      r = this.radius;\n    if (range instanceof Circle || range.type === RangeType.circle) {\n      const circleRange = range,\n        rSum = r + circleRange.radius,\n        dist = Math.sqrt(distPos.x ** squareExp + distPos.y ** squareExp);\n      return rSum > dist;\n    } else if (range instanceof Rectangle || range.type === RangeType.rectangle) {\n      const rectRange = range,\n        {\n          width,\n          height\n        } = rectRange.size,\n        edges = Math.pow(distPos.x - width, squareExp) + Math.pow(distPos.y - height, squareExp);\n      return edges <= r ** squareExp || distPos.x <= r + width && distPos.y <= r + height || distPos.x <= width || distPos.y <= height;\n    }\n    return false;\n  }\n}\nexport class Rectangle extends BaseRange {\n  constructor(x, y, width, height) {\n    super(x, y, RangeType.rectangle);\n    this.size = {\n      height: height,\n      width: width\n    };\n  }\n  contains(point) {\n    const w = this.size.width,\n      h = this.size.height,\n      pos = this.position;\n    return point.x >= pos.x && point.x <= pos.x + w && point.y >= pos.y && point.y <= pos.y + h;\n  }\n  intersects(range) {\n    if (range instanceof Circle) {\n      return range.intersects(this);\n    }\n    const w = this.size.width,\n      h = this.size.height,\n      pos1 = this.position,\n      pos2 = range.position,\n      size2 = range instanceof Rectangle ? range.size : {\n        width: 0,\n        height: 0\n      },\n      w2 = size2.width,\n      h2 = size2.height;\n    return pos2.x < pos1.x + w && pos2.x + w2 > pos1.x && pos2.y < pos1.y + h && pos2.y + h2 > pos1.y;\n  }\n}", "map": {"version": 3, "names": ["RangeType", "getDistance", "squareExp", "BaseRange", "constructor", "x", "y", "type", "position", "Circle", "radius", "circle", "contains", "point", "intersects", "range", "pos1", "pos2", "distPos", "Math", "abs", "r", "circleRange", "rSum", "dist", "sqrt", "Rectangle", "rectangle", "rectRange", "width", "height", "size", "edges", "pow", "w", "h", "pos", "size2", "w2", "h2"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/engine/browser/Core/Utils/Ranges.js"], "sourcesContent": ["import { RangeType } from \"../../Types/RangeType.js\";\nimport { getDistance } from \"../../Utils/NumberUtils.js\";\nconst squareExp = 2;\nexport class BaseRange {\n    constructor(x, y, type) {\n        this.position = {\n            x: x,\n            y: y,\n        };\n        this.type = type;\n    }\n}\nexport class Circle extends BaseRange {\n    constructor(x, y, radius) {\n        super(x, y, RangeType.circle);\n        this.radius = radius;\n    }\n    contains(point) {\n        return getDistance(point, this.position) <= this.radius;\n    }\n    intersects(range) {\n        const pos1 = this.position, pos2 = range.position, distPos = { x: Math.abs(pos2.x - pos1.x), y: Math.abs(pos2.y - pos1.y) }, r = this.radius;\n        if (range instanceof Circle || range.type === RangeType.circle) {\n            const circleRange = range, rSum = r + circleRange.radius, dist = Math.sqrt(distPos.x ** squareExp + distPos.y ** squareExp);\n            return rSum > dist;\n        }\n        else if (range instanceof Rectangle || range.type === RangeType.rectangle) {\n            const rectRange = range, { width, height } = rectRange.size, edges = Math.pow(distPos.x - width, squareExp) + Math.pow(distPos.y - height, squareExp);\n            return (edges <= r ** squareExp ||\n                (distPos.x <= r + width && distPos.y <= r + height) ||\n                distPos.x <= width ||\n                distPos.y <= height);\n        }\n        return false;\n    }\n}\nexport class Rectangle extends BaseRange {\n    constructor(x, y, width, height) {\n        super(x, y, RangeType.rectangle);\n        this.size = {\n            height: height,\n            width: width,\n        };\n    }\n    contains(point) {\n        const w = this.size.width, h = this.size.height, pos = this.position;\n        return point.x >= pos.x && point.x <= pos.x + w && point.y >= pos.y && point.y <= pos.y + h;\n    }\n    intersects(range) {\n        if (range instanceof Circle) {\n            return range.intersects(this);\n        }\n        const w = this.size.width, h = this.size.height, pos1 = this.position, pos2 = range.position, size2 = range instanceof Rectangle ? range.size : { width: 0, height: 0 }, w2 = size2.width, h2 = size2.height;\n        return pos2.x < pos1.x + w && pos2.x + w2 > pos1.x && pos2.y < pos1.y + h && pos2.y + h2 > pos1.y;\n    }\n}\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,0BAA0B;AACpD,SAASC,WAAW,QAAQ,4BAA4B;AACxD,MAAMC,SAAS,GAAG,CAAC;AACnB,OAAO,MAAMC,SAAS,CAAC;EACnBC,WAAWA,CAACC,CAAC,EAAEC,CAAC,EAAEC,IAAI,EAAE;IACpB,IAAI,CAACC,QAAQ,GAAG;MACZH,CAAC,EAAEA,CAAC;MACJC,CAAC,EAAEA;IACP,CAAC;IACD,IAAI,CAACC,IAAI,GAAGA,IAAI;EACpB;AACJ;AACA,OAAO,MAAME,MAAM,SAASN,SAAS,CAAC;EAClCC,WAAWA,CAACC,CAAC,EAAEC,CAAC,EAAEI,MAAM,EAAE;IACtB,KAAK,CAACL,CAAC,EAAEC,CAAC,EAAEN,SAAS,CAACW,MAAM,CAAC;IAC7B,IAAI,CAACD,MAAM,GAAGA,MAAM;EACxB;EACAE,QAAQA,CAACC,KAAK,EAAE;IACZ,OAAOZ,WAAW,CAACY,KAAK,EAAE,IAAI,CAACL,QAAQ,CAAC,IAAI,IAAI,CAACE,MAAM;EAC3D;EACAI,UAAUA,CAACC,KAAK,EAAE;IACd,MAAMC,IAAI,GAAG,IAAI,CAACR,QAAQ;MAAES,IAAI,GAAGF,KAAK,CAACP,QAAQ;MAAEU,OAAO,GAAG;QAAEb,CAAC,EAAEc,IAAI,CAACC,GAAG,CAACH,IAAI,CAACZ,CAAC,GAAGW,IAAI,CAACX,CAAC,CAAC;QAAEC,CAAC,EAAEa,IAAI,CAACC,GAAG,CAACH,IAAI,CAACX,CAAC,GAAGU,IAAI,CAACV,CAAC;MAAE,CAAC;MAAEe,CAAC,GAAG,IAAI,CAACX,MAAM;IAC5I,IAAIK,KAAK,YAAYN,MAAM,IAAIM,KAAK,CAACR,IAAI,KAAKP,SAAS,CAACW,MAAM,EAAE;MAC5D,MAAMW,WAAW,GAAGP,KAAK;QAAEQ,IAAI,GAAGF,CAAC,GAAGC,WAAW,CAACZ,MAAM;QAAEc,IAAI,GAAGL,IAAI,CAACM,IAAI,CAACP,OAAO,CAACb,CAAC,IAAIH,SAAS,GAAGgB,OAAO,CAACZ,CAAC,IAAIJ,SAAS,CAAC;MAC3H,OAAOqB,IAAI,GAAGC,IAAI;IACtB,CAAC,MACI,IAAIT,KAAK,YAAYW,SAAS,IAAIX,KAAK,CAACR,IAAI,KAAKP,SAAS,CAAC2B,SAAS,EAAE;MACvE,MAAMC,SAAS,GAAGb,KAAK;QAAE;UAAEc,KAAK;UAAEC;QAAO,CAAC,GAAGF,SAAS,CAACG,IAAI;QAAEC,KAAK,GAAGb,IAAI,CAACc,GAAG,CAACf,OAAO,CAACb,CAAC,GAAGwB,KAAK,EAAE3B,SAAS,CAAC,GAAGiB,IAAI,CAACc,GAAG,CAACf,OAAO,CAACZ,CAAC,GAAGwB,MAAM,EAAE5B,SAAS,CAAC;MACrJ,OAAQ8B,KAAK,IAAIX,CAAC,IAAInB,SAAS,IAC1BgB,OAAO,CAACb,CAAC,IAAIgB,CAAC,GAAGQ,KAAK,IAAIX,OAAO,CAACZ,CAAC,IAAIe,CAAC,GAAGS,MAAO,IACnDZ,OAAO,CAACb,CAAC,IAAIwB,KAAK,IAClBX,OAAO,CAACZ,CAAC,IAAIwB,MAAM;IAC3B;IACA,OAAO,KAAK;EAChB;AACJ;AACA,OAAO,MAAMJ,SAAS,SAASvB,SAAS,CAAC;EACrCC,WAAWA,CAACC,CAAC,EAAEC,CAAC,EAAEuB,KAAK,EAAEC,MAAM,EAAE;IAC7B,KAAK,CAACzB,CAAC,EAAEC,CAAC,EAAEN,SAAS,CAAC2B,SAAS,CAAC;IAChC,IAAI,CAACI,IAAI,GAAG;MACRD,MAAM,EAAEA,MAAM;MACdD,KAAK,EAAEA;IACX,CAAC;EACL;EACAjB,QAAQA,CAACC,KAAK,EAAE;IACZ,MAAMqB,CAAC,GAAG,IAAI,CAACH,IAAI,CAACF,KAAK;MAAEM,CAAC,GAAG,IAAI,CAACJ,IAAI,CAACD,MAAM;MAAEM,GAAG,GAAG,IAAI,CAAC5B,QAAQ;IACpE,OAAOK,KAAK,CAACR,CAAC,IAAI+B,GAAG,CAAC/B,CAAC,IAAIQ,KAAK,CAACR,CAAC,IAAI+B,GAAG,CAAC/B,CAAC,GAAG6B,CAAC,IAAIrB,KAAK,CAACP,CAAC,IAAI8B,GAAG,CAAC9B,CAAC,IAAIO,KAAK,CAACP,CAAC,IAAI8B,GAAG,CAAC9B,CAAC,GAAG6B,CAAC;EAC/F;EACArB,UAAUA,CAACC,KAAK,EAAE;IACd,IAAIA,KAAK,YAAYN,MAAM,EAAE;MACzB,OAAOM,KAAK,CAACD,UAAU,CAAC,IAAI,CAAC;IACjC;IACA,MAAMoB,CAAC,GAAG,IAAI,CAACH,IAAI,CAACF,KAAK;MAAEM,CAAC,GAAG,IAAI,CAACJ,IAAI,CAACD,MAAM;MAAEd,IAAI,GAAG,IAAI,CAACR,QAAQ;MAAES,IAAI,GAAGF,KAAK,CAACP,QAAQ;MAAE6B,KAAK,GAAGtB,KAAK,YAAYW,SAAS,GAAGX,KAAK,CAACgB,IAAI,GAAG;QAAEF,KAAK,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAE,CAAC;MAAEQ,EAAE,GAAGD,KAAK,CAACR,KAAK;MAAEU,EAAE,GAAGF,KAAK,CAACP,MAAM;IAC5M,OAAOb,IAAI,CAACZ,CAAC,GAAGW,IAAI,CAACX,CAAC,GAAG6B,CAAC,IAAIjB,IAAI,CAACZ,CAAC,GAAGiC,EAAE,GAAGtB,IAAI,CAACX,CAAC,IAAIY,IAAI,CAACX,CAAC,GAAGU,IAAI,CAACV,CAAC,GAAG6B,CAAC,IAAIlB,IAAI,CAACX,CAAC,GAAGiC,EAAE,GAAGvB,IAAI,CAACV,CAAC;EACrG;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
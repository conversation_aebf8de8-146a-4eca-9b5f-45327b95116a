{"ast": null, "code": "export var DestroyMode;\n(function (DestroyMode) {\n  DestroyMode[\"none\"] = \"none\";\n  DestroyMode[\"split\"] = \"split\";\n})(DestroyMode || (DestroyMode = {}));", "map": {"version": 3, "names": ["DestroyMode"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/updater-destroy/browser/Enums/DestroyMode.js"], "sourcesContent": ["export var DestroyMode;\n(function (DestroyMode) {\n    DestroyMode[\"none\"] = \"none\";\n    DestroyMode[\"split\"] = \"split\";\n})(DestroyMode || (DestroyMode = {}));\n"], "mappings": "AAAA,OAAO,IAAIA,WAAW;AACtB,CAAC,UAAUA,WAAW,EAAE;EACpBA,WAAW,CAAC,MAAM,CAAC,GAAG,MAAM;EAC5BA,WAAW,CAAC,OAAO,CAAC,GAAG,OAAO;AAClC,CAAC,EAAEA,WAAW,KAAKA,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
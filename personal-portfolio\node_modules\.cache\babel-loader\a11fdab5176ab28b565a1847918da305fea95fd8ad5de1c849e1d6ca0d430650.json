{"ast": null, "code": "export var StartValueType;\n(function (StartValueType) {\n  StartValueType[\"max\"] = \"max\";\n  StartValueType[\"min\"] = \"min\";\n  StartValueType[\"random\"] = \"random\";\n})(StartValueType || (StartValueType = {}));", "map": {"version": 3, "names": ["StartValueType"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/engine/browser/Enums/Types/StartValueType.js"], "sourcesContent": ["export var StartValueType;\n(function (StartValueType) {\n    StartValueType[\"max\"] = \"max\";\n    StartValueType[\"min\"] = \"min\";\n    StartValueType[\"random\"] = \"random\";\n})(StartValueType || (StartValueType = {}));\n"], "mappings": "AAAA,OAAO,IAAIA,cAAc;AACzB,CAAC,UAAUA,cAAc,EAAE;EACvBA,cAAc,CAAC,KAAK,CAAC,GAAG,KAAK;EAC7BA,cAAc,CAAC,KAAK,CAAC,GAAG,KAAK;EAC7BA,cAAc,CAAC,QAAQ,CAAC,GAAG,QAAQ;AACvC,CAAC,EAAEA,cAAc,KAAKA,cAAc,GAAG,CAAC,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
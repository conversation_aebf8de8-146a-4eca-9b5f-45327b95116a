{"ast": null, "code": "import { SquareDrawer } from \"./SquareDrawer.js\";\nexport async function loadSquareShape(engine, refresh = true) {\n  await engine.addShape(new SquareDrawer(), refresh);\n}", "map": {"version": 3, "names": ["SquareDrawer", "loadSquareShape", "engine", "refresh", "addShape"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/shape-square/browser/index.js"], "sourcesContent": ["import { SquareDrawer } from \"./SquareDrawer.js\";\nexport async function loadSquareShape(engine, refresh = true) {\n    await engine.addShape(new SquareDrawer(), refresh);\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,mBAAmB;AAChD,OAAO,eAAeC,eAAeA,CAACC,MAAM,EAAEC,OAAO,GAAG,IAAI,EAAE;EAC1D,MAAMD,MAAM,CAACE,QAAQ,CAAC,IAAIJ,YAAY,CAAC,CAAC,EAAEG,OAAO,CAAC;AACtD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { PixelMode } from \"../../Enums/Modes/PixelMode.js\";\nimport { deepExtend } from \"../../Utils/Utils.js\";\nconst defaultPosition = 50;\nexport class ManualParticle {\n  load(data) {\n    if (!data) {\n      return;\n    }\n    if (data.position) {\n      this.position = {\n        x: data.position.x ?? defaultPosition,\n        y: data.position.y ?? defaultPosition,\n        mode: data.position.mode ?? PixelMode.percent\n      };\n    }\n    if (data.options) {\n      this.options = deepExtend({}, data.options);\n    }\n  }\n}", "map": {"version": 3, "names": ["PixelMode", "deepExtend", "defaultPosition", "ManualParticle", "load", "data", "position", "x", "y", "mode", "percent", "options"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/engine/browser/Options/Classes/ManualParticle.js"], "sourcesContent": ["import { PixelMode } from \"../../Enums/Modes/PixelMode.js\";\nimport { deepExtend } from \"../../Utils/Utils.js\";\nconst defaultPosition = 50;\nexport class ManualParticle {\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.position) {\n            this.position = {\n                x: data.position.x ?? defaultPosition,\n                y: data.position.y ?? defaultPosition,\n                mode: data.position.mode ?? PixelMode.percent,\n            };\n        }\n        if (data.options) {\n            this.options = deepExtend({}, data.options);\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,gCAAgC;AAC1D,SAASC,UAAU,QAAQ,sBAAsB;AACjD,MAAMC,eAAe,GAAG,EAAE;AAC1B,OAAO,MAAMC,cAAc,CAAC;EACxBC,IAAIA,CAACC,IAAI,EAAE;IACP,IAAI,CAACA,IAAI,EAAE;MACP;IACJ;IACA,IAAIA,IAAI,CAACC,QAAQ,EAAE;MACf,IAAI,CAACA,QAAQ,GAAG;QACZC,CAAC,EAAEF,IAAI,CAACC,QAAQ,CAACC,CAAC,IAAIL,eAAe;QACrCM,CAAC,EAAEH,IAAI,CAACC,QAAQ,CAACE,CAAC,IAAIN,eAAe;QACrCO,IAAI,EAAEJ,IAAI,CAACC,QAAQ,CAACG,IAAI,IAAIT,SAAS,CAACU;MAC1C,CAAC;IACL;IACA,IAAIL,IAAI,CAACM,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,GAAGV,UAAU,CAAC,CAAC,CAAC,EAAEI,IAAI,CAACM,OAAO,CAAC;IAC/C;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "export class CollisionsOverlap {\n  constructor() {\n    this.enable = true;\n    this.retries = 0;\n  }\n\n  load(data) {\n    if (!data) {\n      return;\n    }\n\n    if (data.enable !== undefined) {\n      this.enable = data.enable;\n    }\n\n    if (data.retries !== undefined) {\n      this.retries = data.retries;\n    }\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Options/Classes/Particles/Collisions/CollisionsOverlap.js"], "names": ["CollisionsOverlap", "constructor", "enable", "retries", "load", "data", "undefined"], "mappings": "AAAA,OAAO,MAAMA,iBAAN,CAAwB;AAC3BC,EAAAA,WAAW,GAAG;AACV,SAAKC,MAAL,GAAc,IAAd;AACA,SAAKC,OAAL,GAAe,CAAf;AACH;;AACDC,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,QAAI,CAACA,IAAL,EAAW;AACP;AACH;;AACD,QAAIA,IAAI,CAACH,MAAL,KAAgBI,SAApB,EAA+B;AAC3B,WAAKJ,MAAL,GAAcG,IAAI,CAACH,MAAnB;AACH;;AACD,QAAIG,IAAI,CAACF,OAAL,KAAiBG,SAArB,EAAgC;AAC5B,WAAKH,OAAL,GAAeE,IAAI,CAACF,OAApB;AACH;AACJ;;AAf0B", "sourcesContent": ["export class CollisionsOverlap {\n    constructor() {\n        this.enable = true;\n        this.retries = 0;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.enable !== undefined) {\n            this.enable = data.enable;\n        }\n        if (data.retries !== undefined) {\n            this.retries = data.retries;\n        }\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
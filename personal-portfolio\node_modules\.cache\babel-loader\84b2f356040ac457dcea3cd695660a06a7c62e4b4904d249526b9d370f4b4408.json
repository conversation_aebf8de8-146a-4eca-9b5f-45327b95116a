{"ast": null, "code": "import { isArray, isString } from \"../../Utils/TypeUtils.js\";\nexport class OptionsColor {\n  constructor() {\n    this.value = \"\";\n  }\n  static create(source, data) {\n    const color = new OptionsColor();\n    color.load(source);\n    if (data !== undefined) {\n      if (isString(data) || isArray(data)) {\n        color.load({\n          value: data\n        });\n      } else {\n        color.load(data);\n      }\n    }\n    return color;\n  }\n  load(data) {\n    if (data?.value === undefined) {\n      return;\n    }\n    this.value = data.value;\n  }\n}", "map": {"version": 3, "names": ["isArray", "isString", "OptionsColor", "constructor", "value", "create", "source", "data", "color", "load", "undefined"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/engine/browser/Options/Classes/OptionsColor.js"], "sourcesContent": ["import { isArray, isString } from \"../../Utils/TypeUtils.js\";\nexport class OptionsColor {\n    constructor() {\n        this.value = \"\";\n    }\n    static create(source, data) {\n        const color = new OptionsColor();\n        color.load(source);\n        if (data !== undefined) {\n            if (isString(data) || isArray(data)) {\n                color.load({ value: data });\n            }\n            else {\n                color.load(data);\n            }\n        }\n        return color;\n    }\n    load(data) {\n        if (data?.value === undefined) {\n            return;\n        }\n        this.value = data.value;\n    }\n}\n"], "mappings": "AAAA,SAASA,OAAO,EAAEC,QAAQ,QAAQ,0BAA0B;AAC5D,OAAO,MAAMC,YAAY,CAAC;EACtBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,KAAK,GAAG,EAAE;EACnB;EACA,OAAOC,MAAMA,CAACC,MAAM,EAAEC,IAAI,EAAE;IACxB,MAAMC,KAAK,GAAG,IAAIN,YAAY,CAAC,CAAC;IAChCM,KAAK,CAACC,IAAI,CAACH,MAAM,CAAC;IAClB,IAAIC,IAAI,KAAKG,SAAS,EAAE;MACpB,IAAIT,QAAQ,CAACM,IAAI,CAAC,IAAIP,OAAO,CAACO,IAAI,CAAC,EAAE;QACjCC,KAAK,CAACC,IAAI,CAAC;UAAEL,KAAK,EAAEG;QAAK,CAAC,CAAC;MAC/B,CAAC,MACI;QACDC,KAAK,CAACC,IAAI,CAACF,IAAI,CAAC;MACpB;IACJ;IACA,OAAOC,KAAK;EAChB;EACAC,IAAIA,CAACF,IAAI,EAAE;IACP,IAAIA,IAAI,EAAEH,KAAK,KAAKM,SAAS,EAAE;MAC3B;IACJ;IACA,IAAI,CAACN,KAAK,GAAGG,IAAI,CAACH,KAAK;EAC3B;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { LightArea } from \"./LightArea\";\nimport { LightShadow } from \"./LightShadow\";\nexport class Light {\n  constructor() {\n    this.area = new LightArea();\n    this.shadow = new LightShadow();\n  }\n\n  load(data) {\n    if (data === undefined) {\n      return;\n    }\n\n    this.area.load(data.area);\n    this.shadow.load(data.shadow);\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Options/Classes/Interactivity/Modes/Light.js"], "names": ["LightArea", "LightShadow", "Light", "constructor", "area", "shadow", "load", "data", "undefined"], "mappings": "AAAA,SAASA,SAAT,QAA0B,aAA1B;AACA,SAASC,WAAT,QAA4B,eAA5B;AACA,OAAO,MAAMC,KAAN,CAAY;AACfC,EAAAA,WAAW,GAAG;AACV,SAAKC,IAAL,GAAY,IAAIJ,SAAJ,EAAZ;AACA,SAAKK,MAAL,GAAc,IAAIJ,WAAJ,EAAd;AACH;;AACDK,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,QAAIA,IAAI,KAAKC,SAAb,EAAwB;AACpB;AACH;;AACD,SAAKJ,IAAL,CAAUE,IAAV,CAAeC,IAAI,CAACH,IAApB;AACA,SAAKC,MAAL,CAAYC,IAAZ,CAAiBC,IAAI,CAACF,MAAtB;AACH;;AAXc", "sourcesContent": ["import { LightArea } from \"./LightArea\";\nimport { LightShadow } from \"./LightShadow\";\nexport class Light {\n    constructor() {\n        this.area = new LightArea();\n        this.shadow = new LightShadow();\n    }\n    load(data) {\n        if (data === undefined) {\n            return;\n        }\n        this.area.load(data.area);\n        this.shadow.load(data.shadow);\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
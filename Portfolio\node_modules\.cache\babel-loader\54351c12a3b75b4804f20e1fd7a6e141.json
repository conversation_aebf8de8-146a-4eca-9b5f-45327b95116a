{"ast": null, "code": "export class OutModes {\n  constructor() {\n    this.default = \"out\";\n  }\n\n  load(data) {\n    var _a, _b, _c, _d;\n\n    if (!data) {\n      return;\n    }\n\n    if (data.default !== undefined) {\n      this.default = data.default;\n    }\n\n    this.bottom = (_a = data.bottom) !== null && _a !== void 0 ? _a : data.default;\n    this.left = (_b = data.left) !== null && _b !== void 0 ? _b : data.default;\n    this.right = (_c = data.right) !== null && _c !== void 0 ? _c : data.default;\n    this.top = (_d = data.top) !== null && _d !== void 0 ? _d : data.default;\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Options/Classes/Particles/Move/OutModes.js"], "names": ["OutModes", "constructor", "default", "load", "data", "_a", "_b", "_c", "_d", "undefined", "bottom", "left", "right", "top"], "mappings": "AAAA,OAAO,MAAMA,QAAN,CAAe;AAClBC,EAAAA,WAAW,GAAG;AACV,SAAKC,OAAL,GAAe,KAAf;AACH;;AACDC,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,QAAIC,EAAJ,EAAQC,EAAR,EAAYC,EAAZ,EAAgBC,EAAhB;;AACA,QAAI,CAACJ,IAAL,EAAW;AACP;AACH;;AACD,QAAIA,IAAI,CAACF,OAAL,KAAiBO,SAArB,EAAgC;AAC5B,WAAKP,OAAL,GAAeE,IAAI,CAACF,OAApB;AACH;;AACD,SAAKQ,MAAL,GAAc,CAACL,EAAE,GAAGD,IAAI,CAACM,MAAX,MAAuB,IAAvB,IAA+BL,EAAE,KAAK,KAAK,CAA3C,GAA+CA,EAA/C,GAAoDD,IAAI,CAACF,OAAvE;AACA,SAAKS,IAAL,GAAY,CAACL,EAAE,GAAGF,IAAI,CAACO,IAAX,MAAqB,IAArB,IAA6BL,EAAE,KAAK,KAAK,CAAzC,GAA6CA,EAA7C,GAAkDF,IAAI,CAACF,OAAnE;AACA,SAAKU,KAAL,GAAa,CAACL,EAAE,GAAGH,IAAI,CAACQ,KAAX,MAAsB,IAAtB,IAA8BL,EAAE,KAAK,KAAK,CAA1C,GAA8CA,EAA9C,GAAmDH,IAAI,CAACF,OAArE;AACA,SAAKW,GAAL,GAAW,CAACL,EAAE,GAAGJ,IAAI,CAACS,GAAX,MAAoB,IAApB,IAA4BL,EAAE,KAAK,KAAK,CAAxC,GAA4CA,EAA5C,GAAiDJ,IAAI,CAACF,OAAjE;AACH;;AAhBiB", "sourcesContent": ["export class OutModes {\n    constructor() {\n        this.default = \"out\";\n    }\n    load(data) {\n        var _a, _b, _c, _d;\n        if (!data) {\n            return;\n        }\n        if (data.default !== undefined) {\n            this.default = data.default;\n        }\n        this.bottom = (_a = data.bottom) !== null && _a !== void 0 ? _a : data.default;\n        this.left = (_b = data.left) !== null && _b !== void 0 ? _b : data.default;\n        this.right = (_c = data.right) !== null && _c !== void 0 ? _c : data.default;\n        this.top = (_d = data.top) !== null && _d !== void 0 ? _d : data.default;\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
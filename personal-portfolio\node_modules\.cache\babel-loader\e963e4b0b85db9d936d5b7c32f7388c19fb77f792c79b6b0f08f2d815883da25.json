{"ast": null, "code": "import { RotateUpdater } from \"./RotateUpdater\";\nexport async function loadRotateUpdater(engine, refresh = true) {\n  await engine.addParticleUpdater(\"rotate\", container => new RotateUpdater(container), refresh);\n}", "map": {"version": 3, "names": ["RotateUpdater", "loadRotateUpdater", "engine", "refresh", "addParticleUpdater", "container"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/tsparticles-updater-rotate/esm/index.js"], "sourcesContent": ["import { RotateUpdater } from \"./RotateUpdater\";\nexport async function loadRotateUpdater(engine, refresh = true) {\n    await engine.addParticleUpdater(\"rotate\", (container) => new RotateUpdater(container), refresh);\n}\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,iBAAiB;AAC/C,OAAO,eAAeC,iBAAiBA,CAACC,MAAM,EAAEC,OAAO,GAAG,IAAI,EAAE;EAC5D,MAAMD,MAAM,CAACE,kBAAkB,CAAC,QAAQ,EAAGC,SAAS,IAAK,IAAIL,aAAa,CAACK,SAAS,CAAC,EAAEF,OAAO,CAAC;AACnG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "export class LineDrawer {\n  getSidesCount() {\n    return 1;\n  }\n\n  draw(context, particle, radius) {\n    context.moveTo(-radius / 2, 0);\n    context.lineTo(radius / 2, 0);\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Shapes/Line/LineDrawer.js"], "names": ["LineDrawer", "getSidesCount", "draw", "context", "particle", "radius", "moveTo", "lineTo"], "mappings": "AAAA,OAAO,MAAMA,UAAN,CAAiB;AACpBC,EAAAA,aAAa,GAAG;AACZ,WAAO,CAAP;AACH;;AACDC,EAAAA,IAAI,CAACC,OAAD,EAAUC,QAAV,EAAoBC,MAApB,EAA4B;AAC5BF,IAAAA,OAAO,CAACG,MAAR,CAAe,CAACD,MAAD,GAAU,CAAzB,EAA4B,CAA5B;AACAF,IAAAA,OAAO,CAACI,MAAR,CAAeF,MAAM,GAAG,CAAxB,EAA2B,CAA3B;AACH;;AAPmB", "sourcesContent": ["export class LineDrawer {\n    getSidesCount() {\n        return 1;\n    }\n    draw(context, particle, radius) {\n        context.moveTo(-radius / 2, 0);\n        context.lineTo(radius / 2, 0);\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
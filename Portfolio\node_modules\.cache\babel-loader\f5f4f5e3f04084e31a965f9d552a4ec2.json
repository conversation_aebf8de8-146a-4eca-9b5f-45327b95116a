{"ast": null, "code": "import { WobbleUpdater } from \"./WobbleUpdater\";\nexport async function loadWobbleUpdater(engine) {\n  await engine.addParticleUpdater(\"wobble\", container => new WobbleUpdater(container));\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Updaters/Wobble/index.js"], "names": ["WobbleUpdater", "loadWobbleUpdater", "engine", "addParticleUpdater", "container"], "mappings": "AAAA,SAASA,aAAT,QAA8B,iBAA9B;AACA,OAAO,eAAeC,iBAAf,CAAiCC,MAAjC,EAAyC;AAC5C,QAAMA,MAAM,CAACC,kBAAP,CAA0B,QAA1B,EAAqCC,SAAD,IAAe,IAAIJ,aAAJ,CAAkBI,SAAlB,CAAnD,CAAN;AACH", "sourcesContent": ["import { WobbleUpdater } from \"./WobbleUpdater\";\nexport async function loadWobbleUpdater(engine) {\n    await engine.addParticleUpdater(\"wobble\", (container) => new WobbleUpdater(container));\n}\n"]}, "metadata": {}, "sourceType": "module"}
{"ast": null, "code": "import { ValueWithRandom } from \"../../ValueWithRandom\";\nexport class LifeDelay extends ValueWithRandom {\n  constructor() {\n    super();\n    this.sync = false;\n  }\n\n  load(data) {\n    if (!data) {\n      return;\n    }\n\n    super.load(data);\n\n    if (data.sync !== undefined) {\n      this.sync = data.sync;\n    }\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Options/Classes/Particles/Life/LifeDelay.js"], "names": ["ValueWithRandom", "LifeDelay", "constructor", "sync", "load", "data", "undefined"], "mappings": "AAAA,SAASA,eAAT,QAAgC,uBAAhC;AACA,OAAO,MAAMC,SAAN,SAAwBD,eAAxB,CAAwC;AAC3CE,EAAAA,WAAW,GAAG;AACV;AACA,SAAKC,IAAL,GAAY,KAAZ;AACH;;AACDC,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,QAAI,CAACA,IAAL,EAAW;AACP;AACH;;AACD,UAAMD,IAAN,CAAWC,IAAX;;AACA,QAAIA,IAAI,CAACF,IAAL,KAAcG,SAAlB,EAA6B;AACzB,WAAKH,IAAL,GAAYE,IAAI,CAACF,IAAjB;AACH;AACJ;;AAb0C", "sourcesContent": ["import { ValueWithRandom } from \"../../ValueWithRandom\";\nexport class LifeDelay extends ValueWithRandom {\n    constructor() {\n        super();\n        this.sync = false;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        super.load(data);\n        if (data.sync !== undefined) {\n            this.sync = data.sync;\n        }\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
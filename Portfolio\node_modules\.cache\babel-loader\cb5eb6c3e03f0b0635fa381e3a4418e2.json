{"ast": null, "code": "export class ThemeDefault {\n  constructor() {\n    this.auto = false;\n    this.mode = \"any\";\n    this.value = false;\n  }\n\n  load(data) {\n    if (!data) {\n      return;\n    }\n\n    if (data.auto !== undefined) {\n      this.auto = data.auto;\n    }\n\n    if (data.mode !== undefined) {\n      this.mode = data.mode;\n    }\n\n    if (data.value !== undefined) {\n      this.value = data.value;\n    }\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Options/Classes/Theme/ThemeDefault.js"], "names": ["ThemeDefault", "constructor", "auto", "mode", "value", "load", "data", "undefined"], "mappings": "AAAA,OAAO,MAAMA,YAAN,CAAmB;AACtBC,EAAAA,WAAW,GAAG;AACV,SAAKC,IAAL,GAAY,KAAZ;AACA,SAAKC,IAAL,GAAY,KAAZ;AACA,SAAKC,KAAL,GAAa,KAAb;AACH;;AACDC,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,QAAI,CAACA,IAAL,EAAW;AACP;AACH;;AACD,QAAIA,IAAI,CAACJ,IAAL,KAAcK,SAAlB,EAA6B;AACzB,WAAKL,IAAL,GAAYI,IAAI,CAACJ,IAAjB;AACH;;AACD,QAAII,IAAI,CAACH,IAAL,KAAcI,SAAlB,EAA6B;AACzB,WAAKJ,IAAL,GAAYG,IAAI,CAACH,IAAjB;AACH;;AACD,QAAIG,IAAI,CAACF,KAAL,KAAeG,SAAnB,EAA8B;AAC1B,WAAKH,KAAL,GAAaE,IAAI,CAACF,KAAlB;AACH;AACJ;;AAnBqB", "sourcesContent": ["export class ThemeDefault {\n    constructor() {\n        this.auto = false;\n        this.mode = \"any\";\n        this.value = false;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.auto !== undefined) {\n            this.auto = data.auto;\n        }\n        if (data.mode !== undefined) {\n            this.mode = data.mode;\n        }\n        if (data.value !== undefined) {\n            this.value = data.value;\n        }\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
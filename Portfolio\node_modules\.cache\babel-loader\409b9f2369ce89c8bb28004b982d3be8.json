{"ast": null, "code": "import { ValueWithRandom } from \"../../ValueWithRandom\";\nexport class BounceFactor extends ValueWithRandom {\n  constructor() {\n    super();\n    this.random.minimumValue = 0.1;\n    this.value = 1;\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Options/Classes/Particles/Bounce/BounceFactor.js"], "names": ["ValueWithRandom", "BounceFactor", "constructor", "random", "minimumValue", "value"], "mappings": "AAAA,SAASA,eAAT,QAAgC,uBAAhC;AACA,OAAO,MAAMC,YAAN,SAA2BD,eAA3B,CAA2C;AAC9CE,EAAAA,WAAW,GAAG;AACV;AACA,SAAKC,MAAL,CAAYC,YAAZ,GAA2B,GAA3B;AACA,SAAKC,KAAL,GAAa,CAAb;AACH;;AAL6C", "sourcesContent": ["import { ValueWithRandom } from \"../../ValueWithRandom\";\nexport class BounceFactor extends ValueWithRandom {\n    constructor() {\n        super();\n        this.random.minimumValue = 0.1;\n        this.value = 1;\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
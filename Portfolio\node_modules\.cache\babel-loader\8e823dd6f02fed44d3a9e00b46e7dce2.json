{"ast": null, "code": "import { isSsr, itemFromArray } from \"../../Utils\";\nimport { Constants } from \"./Constants\";\n\nfunction manageListener(element, event, handler, add, options) {\n  if (add) {\n    let addOptions = {\n      passive: true\n    };\n\n    if (typeof options === \"boolean\") {\n      addOptions.capture = options;\n    } else if (options !== undefined) {\n      addOptions = options;\n    }\n\n    element.addEventListener(event, handler, addOptions);\n  } else {\n    const removeOptions = options;\n    element.removeEventListener(event, handler, removeOptions);\n  }\n}\n\nexport class EventListeners {\n  constructor(container) {\n    this.container = container;\n    this.canPush = true;\n\n    this.mouseMoveHandler = e => this.mouseTouchMove(e);\n\n    this.touchStartHandler = e => this.mouseTouchMove(e);\n\n    this.touchMoveHandler = e => this.mouseTouchMove(e);\n\n    this.touchEndHandler = () => this.mouseTouchFinish();\n\n    this.mouseLeaveHandler = () => this.mouseTouchFinish();\n\n    this.touchCancelHandler = () => this.mouseTouchFinish();\n\n    this.touchEndClickHandler = e => this.mouseTouchClick(e);\n\n    this.mouseUpHandler = e => this.mouseTouchClick(e);\n\n    this.mouseDownHandler = () => this.mouseDown();\n\n    this.visibilityChangeHandler = () => this.handleVisibilityChange();\n\n    this.themeChangeHandler = e => this.handleThemeChange(e);\n\n    this.oldThemeChangeHandler = e => this.handleThemeChange(e);\n\n    this.resizeHandler = () => this.handleWindowResize();\n  }\n\n  addListeners() {\n    this.manageListeners(true);\n  }\n\n  removeListeners() {\n    this.manageListeners(false);\n  }\n\n  manageListeners(add) {\n    var _a;\n\n    const container = this.container;\n    const options = container.actualOptions;\n    const detectType = options.interactivity.detectsOn;\n    let mouseLeaveEvent = Constants.mouseLeaveEvent;\n\n    if (detectType === \"window\") {\n      container.interactivity.element = window;\n      mouseLeaveEvent = Constants.mouseOutEvent;\n    } else if (detectType === \"parent\" && container.canvas.element) {\n      const canvasEl = container.canvas.element;\n      container.interactivity.element = (_a = canvasEl.parentElement) !== null && _a !== void 0 ? _a : canvasEl.parentNode;\n    } else {\n      container.interactivity.element = container.canvas.element;\n    }\n\n    const mediaMatch = !isSsr() && typeof matchMedia !== \"undefined\" && matchMedia(\"(prefers-color-scheme: dark)\");\n\n    if (mediaMatch) {\n      if (mediaMatch.addEventListener !== undefined) {\n        manageListener(mediaMatch, \"change\", this.themeChangeHandler, add);\n      } else if (mediaMatch.addListener !== undefined) {\n        if (add) {\n          mediaMatch.addListener(this.oldThemeChangeHandler);\n        } else {\n          mediaMatch.removeListener(this.oldThemeChangeHandler);\n        }\n      }\n    }\n\n    const interactivityEl = container.interactivity.element;\n\n    if (!interactivityEl) {\n      return;\n    }\n\n    const html = interactivityEl;\n\n    if (options.interactivity.events.onHover.enable || options.interactivity.events.onClick.enable) {\n      manageListener(interactivityEl, Constants.mouseMoveEvent, this.mouseMoveHandler, add);\n      manageListener(interactivityEl, Constants.touchStartEvent, this.touchStartHandler, add);\n      manageListener(interactivityEl, Constants.touchMoveEvent, this.touchMoveHandler, add);\n\n      if (!options.interactivity.events.onClick.enable) {\n        manageListener(interactivityEl, Constants.touchEndEvent, this.touchEndHandler, add);\n      } else {\n        manageListener(interactivityEl, Constants.touchEndEvent, this.touchEndClickHandler, add);\n        manageListener(interactivityEl, Constants.mouseUpEvent, this.mouseUpHandler, add);\n        manageListener(interactivityEl, Constants.mouseDownEvent, this.mouseDownHandler, add);\n      }\n\n      manageListener(interactivityEl, mouseLeaveEvent, this.mouseLeaveHandler, add);\n      manageListener(interactivityEl, Constants.touchCancelEvent, this.touchCancelHandler, add);\n    }\n\n    if (container.canvas.element) {\n      container.canvas.element.style.pointerEvents = html === container.canvas.element ? \"initial\" : \"none\";\n    }\n\n    if (options.interactivity.events.resize) {\n      if (typeof ResizeObserver !== \"undefined\") {\n        if (this.resizeObserver && !add) {\n          if (container.canvas.element) {\n            this.resizeObserver.unobserve(container.canvas.element);\n          }\n\n          this.resizeObserver.disconnect();\n          delete this.resizeObserver;\n        } else if (!this.resizeObserver && add && container.canvas.element) {\n          this.resizeObserver = new ResizeObserver(entries => {\n            const entry = entries.find(e => e.target === container.canvas.element);\n\n            if (!entry) {\n              return;\n            }\n\n            this.handleWindowResize();\n          });\n          this.resizeObserver.observe(container.canvas.element);\n        }\n      } else {\n        manageListener(window, Constants.resizeEvent, this.resizeHandler, add);\n      }\n    }\n\n    if (document) {\n      manageListener(document, Constants.visibilityChangeEvent, this.visibilityChangeHandler, add, false);\n    }\n  }\n\n  handleWindowResize() {\n    if (this.resizeTimeout) {\n      clearTimeout(this.resizeTimeout);\n      delete this.resizeTimeout;\n    }\n\n    this.resizeTimeout = setTimeout(async () => {\n      var _a;\n\n      return await ((_a = this.container.canvas) === null || _a === void 0 ? void 0 : _a.windowResize());\n    }, 500);\n  }\n\n  handleVisibilityChange() {\n    const container = this.container;\n    const options = container.actualOptions;\n    this.mouseTouchFinish();\n\n    if (!options.pauseOnBlur) {\n      return;\n    }\n\n    if (document === null || document === void 0 ? void 0 : document.hidden) {\n      container.pageHidden = true;\n      container.pause();\n    } else {\n      container.pageHidden = false;\n\n      if (container.getAnimationStatus()) {\n        container.play(true);\n      } else {\n        container.draw(true);\n      }\n    }\n  }\n\n  mouseDown() {\n    const interactivity = this.container.interactivity;\n\n    if (interactivity) {\n      const mouse = interactivity.mouse;\n      mouse.clicking = true;\n      mouse.downPosition = mouse.position;\n    }\n  }\n\n  mouseTouchMove(e) {\n    var _a, _b, _c, _d, _e, _f, _g;\n\n    const container = this.container;\n    const options = container.actualOptions;\n\n    if (((_a = container.interactivity) === null || _a === void 0 ? void 0 : _a.element) === undefined) {\n      return;\n    }\n\n    container.interactivity.mouse.inside = true;\n    let pos;\n    const canvas = container.canvas.element;\n\n    if (e.type.startsWith(\"mouse\")) {\n      this.canPush = true;\n      const mouseEvent = e;\n\n      if (container.interactivity.element === window) {\n        if (canvas) {\n          const clientRect = canvas.getBoundingClientRect();\n          pos = {\n            x: mouseEvent.clientX - clientRect.left,\n            y: mouseEvent.clientY - clientRect.top\n          };\n        }\n      } else if (options.interactivity.detectsOn === \"parent\") {\n        const source = mouseEvent.target;\n        const target = mouseEvent.currentTarget;\n        const canvasEl = container.canvas.element;\n\n        if (source && target && canvasEl) {\n          const sourceRect = source.getBoundingClientRect();\n          const targetRect = target.getBoundingClientRect();\n          const canvasRect = canvasEl.getBoundingClientRect();\n          pos = {\n            x: mouseEvent.offsetX + 2 * sourceRect.left - (targetRect.left + canvasRect.left),\n            y: mouseEvent.offsetY + 2 * sourceRect.top - (targetRect.top + canvasRect.top)\n          };\n        } else {\n          pos = {\n            x: (_b = mouseEvent.offsetX) !== null && _b !== void 0 ? _b : mouseEvent.clientX,\n            y: (_c = mouseEvent.offsetY) !== null && _c !== void 0 ? _c : mouseEvent.clientY\n          };\n        }\n      } else {\n        if (mouseEvent.target === container.canvas.element) {\n          pos = {\n            x: (_d = mouseEvent.offsetX) !== null && _d !== void 0 ? _d : mouseEvent.clientX,\n            y: (_e = mouseEvent.offsetY) !== null && _e !== void 0 ? _e : mouseEvent.clientY\n          };\n        }\n      }\n    } else {\n      this.canPush = e.type !== \"touchmove\";\n      const touchEvent = e;\n      const lastTouch = touchEvent.touches[touchEvent.touches.length - 1];\n      const canvasRect = canvas === null || canvas === void 0 ? void 0 : canvas.getBoundingClientRect();\n      pos = {\n        x: lastTouch.clientX - ((_f = canvasRect === null || canvasRect === void 0 ? void 0 : canvasRect.left) !== null && _f !== void 0 ? _f : 0),\n        y: lastTouch.clientY - ((_g = canvasRect === null || canvasRect === void 0 ? void 0 : canvasRect.top) !== null && _g !== void 0 ? _g : 0)\n      };\n    }\n\n    const pxRatio = container.retina.pixelRatio;\n\n    if (pos) {\n      pos.x *= pxRatio;\n      pos.y *= pxRatio;\n    }\n\n    container.interactivity.mouse.position = pos;\n    container.interactivity.status = Constants.mouseMoveEvent;\n  }\n\n  mouseTouchFinish() {\n    const interactivity = this.container.interactivity;\n\n    if (interactivity === undefined) {\n      return;\n    }\n\n    const mouse = interactivity.mouse;\n    delete mouse.position;\n    delete mouse.clickPosition;\n    delete mouse.downPosition;\n    interactivity.status = Constants.mouseLeaveEvent;\n    mouse.inside = false;\n    mouse.clicking = false;\n  }\n\n  mouseTouchClick(e) {\n    const container = this.container;\n    const options = container.actualOptions;\n    const mouse = container.interactivity.mouse;\n    mouse.inside = true;\n    let handled = false;\n    const mousePosition = mouse.position;\n\n    if (mousePosition === undefined || !options.interactivity.events.onClick.enable) {\n      return;\n    }\n\n    for (const [, plugin] of container.plugins) {\n      if (plugin.clickPositionValid !== undefined) {\n        handled = plugin.clickPositionValid(mousePosition);\n\n        if (handled) {\n          break;\n        }\n      }\n    }\n\n    if (!handled) {\n      this.doMouseTouchClick(e);\n    }\n\n    mouse.clicking = false;\n  }\n\n  doMouseTouchClick(e) {\n    const container = this.container;\n    const options = container.actualOptions;\n\n    if (this.canPush) {\n      const mousePos = container.interactivity.mouse.position;\n\n      if (mousePos) {\n        container.interactivity.mouse.clickPosition = {\n          x: mousePos.x,\n          y: mousePos.y\n        };\n      } else {\n        return;\n      }\n\n      container.interactivity.mouse.clickTime = new Date().getTime();\n      const onClick = options.interactivity.events.onClick;\n\n      if (onClick.mode instanceof Array) {\n        for (const mode of onClick.mode) {\n          this.handleClickMode(mode);\n        }\n      } else {\n        this.handleClickMode(onClick.mode);\n      }\n    }\n\n    if (e.type === \"touchend\") {\n      setTimeout(() => this.mouseTouchFinish(), 500);\n    }\n  }\n\n  handleThemeChange(e) {\n    const mediaEvent = e;\n    const themeName = mediaEvent.matches ? this.container.options.defaultDarkTheme : this.container.options.defaultLightTheme;\n    const theme = this.container.options.themes.find(theme => theme.name === themeName);\n\n    if (theme && theme.default.auto) {\n      this.container.loadTheme(themeName);\n    }\n  }\n\n  handleClickMode(mode) {\n    const container = this.container;\n    const options = container.actualOptions;\n    const pushNb = options.interactivity.modes.push.quantity;\n    const removeNb = options.interactivity.modes.remove.quantity;\n\n    switch (mode) {\n      case \"push\":\n        {\n          if (pushNb > 0) {\n            const pushOptions = options.interactivity.modes.push;\n            const group = itemFromArray([undefined, ...pushOptions.groups]);\n            const groupOptions = group !== undefined ? container.actualOptions.particles.groups[group] : undefined;\n            container.particles.push(pushNb, container.interactivity.mouse, groupOptions, group);\n          }\n\n          break;\n        }\n\n      case \"remove\":\n        container.particles.removeQuantity(removeNb);\n        break;\n\n      case \"bubble\":\n        container.bubble.clicking = true;\n        break;\n\n      case \"repulse\":\n        container.repulse.clicking = true;\n        container.repulse.count = 0;\n\n        for (const particle of container.repulse.particles) {\n          particle.velocity.setTo(particle.initialVelocity);\n        }\n\n        container.repulse.particles = [];\n        container.repulse.finish = false;\n        setTimeout(() => {\n          if (!container.destroyed) {\n            container.repulse.clicking = false;\n          }\n        }, options.interactivity.modes.repulse.duration * 1000);\n        break;\n\n      case \"attract\":\n        container.attract.clicking = true;\n        container.attract.count = 0;\n\n        for (const particle of container.attract.particles) {\n          particle.velocity.setTo(particle.initialVelocity);\n        }\n\n        container.attract.particles = [];\n        container.attract.finish = false;\n        setTimeout(() => {\n          if (!container.destroyed) {\n            container.attract.clicking = false;\n          }\n        }, options.interactivity.modes.attract.duration * 1000);\n        break;\n\n      case \"pause\":\n        if (container.getAnimationStatus()) {\n          container.pause();\n        } else {\n          container.play();\n        }\n\n        break;\n    }\n\n    for (const [, plugin] of container.plugins) {\n      if (plugin.handleClickMode) {\n        plugin.handleClickMode(mode);\n      }\n    }\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Core/Utils/EventListeners.js"], "names": ["isSsr", "itemFromArray", "Constants", "manageListener", "element", "event", "handler", "add", "options", "addOptions", "passive", "capture", "undefined", "addEventListener", "removeOptions", "removeEventListener", "EventListeners", "constructor", "container", "canPush", "mouseMoveHandler", "e", "mouseTouchMove", "touchStartHandler", "touchMoveHandler", "touchEndHandler", "mouseTouchFinish", "mouseLeaveHandler", "touchCancelHandler", "touchEndClickHandler", "mouseTouchClick", "mouseUpHandler", "mouseDownHandler", "mouseDown", "visibilityChangeHandler", "handleVisibilityChange", "themeChangeHandler", "handleThemeChange", "oldThemeChangeHandler", "resize<PERSON><PERSON>ler", "handleWindowResize", "addListeners", "manageListeners", "removeListeners", "_a", "actualOptions", "detectType", "interactivity", "detectsOn", "mouseLeaveEvent", "window", "mouseOutEvent", "canvas", "canvasEl", "parentElement", "parentNode", "mediaMatch", "matchMedia", "addListener", "removeListener", "interactivityEl", "html", "events", "onHover", "enable", "onClick", "mouseMoveEvent", "touchStartEvent", "touchMoveEvent", "touchEndEvent", "mouseUpEvent", "mouseDownEvent", "touchCancelEvent", "style", "pointerEvents", "resize", "ResizeObserver", "resizeObserver", "unobserve", "disconnect", "entries", "entry", "find", "target", "observe", "resizeEvent", "document", "visibilityChangeEvent", "resizeTimeout", "clearTimeout", "setTimeout", "windowResize", "pauseOnBlur", "hidden", "pageHidden", "pause", "getAnimationStatus", "play", "draw", "mouse", "clicking", "downPosition", "position", "_b", "_c", "_d", "_e", "_f", "_g", "inside", "pos", "type", "startsWith", "mouseEvent", "clientRect", "getBoundingClientRect", "x", "clientX", "left", "y", "clientY", "top", "source", "currentTarget", "sourceRect", "targetRect", "canvasRect", "offsetX", "offsetY", "touchEvent", "lastTouch", "touches", "length", "pxRatio", "retina", "pixelRatio", "status", "clickPosition", "handled", "mousePosition", "plugin", "plugins", "clickPositionValid", "doMouseTouchClick", "mousePos", "clickTime", "Date", "getTime", "mode", "Array", "handleClickMode", "mediaEvent", "themeName", "matches", "defaultDarkTheme", "defaultLightTheme", "theme", "themes", "name", "default", "auto", "loadTheme", "pushNb", "modes", "push", "quantity", "removeNb", "remove", "pushOptions", "group", "groups", "groupOptions", "particles", "removeQuantity", "bubble", "repulse", "count", "particle", "velocity", "setTo", "initialVelocity", "finish", "destroyed", "duration", "attract"], "mappings": "AAAA,SAASA,KAAT,EAAgBC,aAAhB,QAAqC,aAArC;AACA,SAASC,SAAT,QAA0B,aAA1B;;AACA,SAASC,cAAT,CAAwBC,OAAxB,EAAiCC,KAAjC,EAAwCC,OAAxC,EAAiDC,GAAjD,EAAsDC,OAAtD,EAA+D;AAC3D,MAAID,GAAJ,EAAS;AACL,QAAIE,UAAU,GAAG;AAAEC,MAAAA,OAAO,EAAE;AAAX,KAAjB;;AACA,QAAI,OAAOF,OAAP,KAAmB,SAAvB,EAAkC;AAC9BC,MAAAA,UAAU,CAACE,OAAX,GAAqBH,OAArB;AACH,KAFD,MAGK,IAAIA,OAAO,KAAKI,SAAhB,EAA2B;AAC5BH,MAAAA,UAAU,GAAGD,OAAb;AACH;;AACDJ,IAAAA,OAAO,CAACS,gBAAR,CAAyBR,KAAzB,EAAgCC,OAAhC,EAAyCG,UAAzC;AACH,GATD,MAUK;AACD,UAAMK,aAAa,GAAGN,OAAtB;AACAJ,IAAAA,OAAO,CAACW,mBAAR,CAA4BV,KAA5B,EAAmCC,OAAnC,EAA4CQ,aAA5C;AACH;AACJ;;AACD,OAAO,MAAME,cAAN,CAAqB;AACxBC,EAAAA,WAAW,CAACC,SAAD,EAAY;AACnB,SAAKA,SAAL,GAAiBA,SAAjB;AACA,SAAKC,OAAL,GAAe,IAAf;;AACA,SAAKC,gBAAL,GAAyBC,CAAD,IAAO,KAAKC,cAAL,CAAoBD,CAApB,CAA/B;;AACA,SAAKE,iBAAL,GAA0BF,CAAD,IAAO,KAAKC,cAAL,CAAoBD,CAApB,CAAhC;;AACA,SAAKG,gBAAL,GAAyBH,CAAD,IAAO,KAAKC,cAAL,CAAoBD,CAApB,CAA/B;;AACA,SAAKI,eAAL,GAAuB,MAAM,KAAKC,gBAAL,EAA7B;;AACA,SAAKC,iBAAL,GAAyB,MAAM,KAAKD,gBAAL,EAA/B;;AACA,SAAKE,kBAAL,GAA0B,MAAM,KAAKF,gBAAL,EAAhC;;AACA,SAAKG,oBAAL,GAA6BR,CAAD,IAAO,KAAKS,eAAL,CAAqBT,CAArB,CAAnC;;AACA,SAAKU,cAAL,GAAuBV,CAAD,IAAO,KAAKS,eAAL,CAAqBT,CAArB,CAA7B;;AACA,SAAKW,gBAAL,GAAwB,MAAM,KAAKC,SAAL,EAA9B;;AACA,SAAKC,uBAAL,GAA+B,MAAM,KAAKC,sBAAL,EAArC;;AACA,SAAKC,kBAAL,GAA2Bf,CAAD,IAAO,KAAKgB,iBAAL,CAAuBhB,CAAvB,CAAjC;;AACA,SAAKiB,qBAAL,GAA8BjB,CAAD,IAAO,KAAKgB,iBAAL,CAAuBhB,CAAvB,CAApC;;AACA,SAAKkB,aAAL,GAAqB,MAAM,KAAKC,kBAAL,EAA3B;AACH;;AACDC,EAAAA,YAAY,GAAG;AACX,SAAKC,eAAL,CAAqB,IAArB;AACH;;AACDC,EAAAA,eAAe,GAAG;AACd,SAAKD,eAAL,CAAqB,KAArB;AACH;;AACDA,EAAAA,eAAe,CAACnC,GAAD,EAAM;AACjB,QAAIqC,EAAJ;;AACA,UAAM1B,SAAS,GAAG,KAAKA,SAAvB;AACA,UAAMV,OAAO,GAAGU,SAAS,CAAC2B,aAA1B;AACA,UAAMC,UAAU,GAAGtC,OAAO,CAACuC,aAAR,CAAsBC,SAAzC;AACA,QAAIC,eAAe,GAAG/C,SAAS,CAAC+C,eAAhC;;AACA,QAAIH,UAAU,KAAK,QAAnB,EAA6B;AACzB5B,MAAAA,SAAS,CAAC6B,aAAV,CAAwB3C,OAAxB,GAAkC8C,MAAlC;AACAD,MAAAA,eAAe,GAAG/C,SAAS,CAACiD,aAA5B;AACH,KAHD,MAIK,IAAIL,UAAU,KAAK,QAAf,IAA2B5B,SAAS,CAACkC,MAAV,CAAiBhD,OAAhD,EAAyD;AAC1D,YAAMiD,QAAQ,GAAGnC,SAAS,CAACkC,MAAV,CAAiBhD,OAAlC;AACAc,MAAAA,SAAS,CAAC6B,aAAV,CAAwB3C,OAAxB,GAAkC,CAACwC,EAAE,GAAGS,QAAQ,CAACC,aAAf,MAAkC,IAAlC,IAA0CV,EAAE,KAAK,KAAK,CAAtD,GAA0DA,EAA1D,GAA+DS,QAAQ,CAACE,UAA1G;AACH,KAHI,MAIA;AACDrC,MAAAA,SAAS,CAAC6B,aAAV,CAAwB3C,OAAxB,GAAkCc,SAAS,CAACkC,MAAV,CAAiBhD,OAAnD;AACH;;AACD,UAAMoD,UAAU,GAAG,CAACxD,KAAK,EAAN,IAAY,OAAOyD,UAAP,KAAsB,WAAlC,IAAiDA,UAAU,CAAC,8BAAD,CAA9E;;AACA,QAAID,UAAJ,EAAgB;AACZ,UAAIA,UAAU,CAAC3C,gBAAX,KAAgCD,SAApC,EAA+C;AAC3CT,QAAAA,cAAc,CAACqD,UAAD,EAAa,QAAb,EAAuB,KAAKpB,kBAA5B,EAAgD7B,GAAhD,CAAd;AACH,OAFD,MAGK,IAAIiD,UAAU,CAACE,WAAX,KAA2B9C,SAA/B,EAA0C;AAC3C,YAAIL,GAAJ,EAAS;AACLiD,UAAAA,UAAU,CAACE,WAAX,CAAuB,KAAKpB,qBAA5B;AACH,SAFD,MAGK;AACDkB,UAAAA,UAAU,CAACG,cAAX,CAA0B,KAAKrB,qBAA/B;AACH;AACJ;AACJ;;AACD,UAAMsB,eAAe,GAAG1C,SAAS,CAAC6B,aAAV,CAAwB3C,OAAhD;;AACA,QAAI,CAACwD,eAAL,EAAsB;AAClB;AACH;;AACD,UAAMC,IAAI,GAAGD,eAAb;;AACA,QAAIpD,OAAO,CAACuC,aAAR,CAAsBe,MAAtB,CAA6BC,OAA7B,CAAqCC,MAArC,IAA+CxD,OAAO,CAACuC,aAAR,CAAsBe,MAAtB,CAA6BG,OAA7B,CAAqCD,MAAxF,EAAgG;AAC5F7D,MAAAA,cAAc,CAACyD,eAAD,EAAkB1D,SAAS,CAACgE,cAA5B,EAA4C,KAAK9C,gBAAjD,EAAmEb,GAAnE,CAAd;AACAJ,MAAAA,cAAc,CAACyD,eAAD,EAAkB1D,SAAS,CAACiE,eAA5B,EAA6C,KAAK5C,iBAAlD,EAAqEhB,GAArE,CAAd;AACAJ,MAAAA,cAAc,CAACyD,eAAD,EAAkB1D,SAAS,CAACkE,cAA5B,EAA4C,KAAK5C,gBAAjD,EAAmEjB,GAAnE,CAAd;;AACA,UAAI,CAACC,OAAO,CAACuC,aAAR,CAAsBe,MAAtB,CAA6BG,OAA7B,CAAqCD,MAA1C,EAAkD;AAC9C7D,QAAAA,cAAc,CAACyD,eAAD,EAAkB1D,SAAS,CAACmE,aAA5B,EAA2C,KAAK5C,eAAhD,EAAiElB,GAAjE,CAAd;AACH,OAFD,MAGK;AACDJ,QAAAA,cAAc,CAACyD,eAAD,EAAkB1D,SAAS,CAACmE,aAA5B,EAA2C,KAAKxC,oBAAhD,EAAsEtB,GAAtE,CAAd;AACAJ,QAAAA,cAAc,CAACyD,eAAD,EAAkB1D,SAAS,CAACoE,YAA5B,EAA0C,KAAKvC,cAA/C,EAA+DxB,GAA/D,CAAd;AACAJ,QAAAA,cAAc,CAACyD,eAAD,EAAkB1D,SAAS,CAACqE,cAA5B,EAA4C,KAAKvC,gBAAjD,EAAmEzB,GAAnE,CAAd;AACH;;AACDJ,MAAAA,cAAc,CAACyD,eAAD,EAAkBX,eAAlB,EAAmC,KAAKtB,iBAAxC,EAA2DpB,GAA3D,CAAd;AACAJ,MAAAA,cAAc,CAACyD,eAAD,EAAkB1D,SAAS,CAACsE,gBAA5B,EAA8C,KAAK5C,kBAAnD,EAAuErB,GAAvE,CAAd;AACH;;AACD,QAAIW,SAAS,CAACkC,MAAV,CAAiBhD,OAArB,EAA8B;AAC1Bc,MAAAA,SAAS,CAACkC,MAAV,CAAiBhD,OAAjB,CAAyBqE,KAAzB,CAA+BC,aAA/B,GAA+Cb,IAAI,KAAK3C,SAAS,CAACkC,MAAV,CAAiBhD,OAA1B,GAAoC,SAApC,GAAgD,MAA/F;AACH;;AACD,QAAII,OAAO,CAACuC,aAAR,CAAsBe,MAAtB,CAA6Ba,MAAjC,EAAyC;AACrC,UAAI,OAAOC,cAAP,KAA0B,WAA9B,EAA2C;AACvC,YAAI,KAAKC,cAAL,IAAuB,CAACtE,GAA5B,EAAiC;AAC7B,cAAIW,SAAS,CAACkC,MAAV,CAAiBhD,OAArB,EAA8B;AAC1B,iBAAKyE,cAAL,CAAoBC,SAApB,CAA8B5D,SAAS,CAACkC,MAAV,CAAiBhD,OAA/C;AACH;;AACD,eAAKyE,cAAL,CAAoBE,UAApB;AACA,iBAAO,KAAKF,cAAZ;AACH,SAND,MAOK,IAAI,CAAC,KAAKA,cAAN,IAAwBtE,GAAxB,IAA+BW,SAAS,CAACkC,MAAV,CAAiBhD,OAApD,EAA6D;AAC9D,eAAKyE,cAAL,GAAsB,IAAID,cAAJ,CAAoBI,OAAD,IAAa;AAClD,kBAAMC,KAAK,GAAGD,OAAO,CAACE,IAAR,CAAc7D,CAAD,IAAOA,CAAC,CAAC8D,MAAF,KAAajE,SAAS,CAACkC,MAAV,CAAiBhD,OAAlD,CAAd;;AACA,gBAAI,CAAC6E,KAAL,EAAY;AACR;AACH;;AACD,iBAAKzC,kBAAL;AACH,WANqB,CAAtB;AAOA,eAAKqC,cAAL,CAAoBO,OAApB,CAA4BlE,SAAS,CAACkC,MAAV,CAAiBhD,OAA7C;AACH;AACJ,OAlBD,MAmBK;AACDD,QAAAA,cAAc,CAAC+C,MAAD,EAAShD,SAAS,CAACmF,WAAnB,EAAgC,KAAK9C,aAArC,EAAoDhC,GAApD,CAAd;AACH;AACJ;;AACD,QAAI+E,QAAJ,EAAc;AACVnF,MAAAA,cAAc,CAACmF,QAAD,EAAWpF,SAAS,CAACqF,qBAArB,EAA4C,KAAKrD,uBAAjD,EAA0E3B,GAA1E,EAA+E,KAA/E,CAAd;AACH;AACJ;;AACDiC,EAAAA,kBAAkB,GAAG;AACjB,QAAI,KAAKgD,aAAT,EAAwB;AACpBC,MAAAA,YAAY,CAAC,KAAKD,aAAN,CAAZ;AACA,aAAO,KAAKA,aAAZ;AACH;;AACD,SAAKA,aAAL,GAAqBE,UAAU,CAAC,YAAY;AAAE,UAAI9C,EAAJ;;AAAQ,aAAO,OAAO,CAACA,EAAE,GAAG,KAAK1B,SAAL,CAAekC,MAArB,MAAiC,IAAjC,IAAyCR,EAAE,KAAK,KAAK,CAArD,GAAyD,KAAK,CAA9D,GAAkEA,EAAE,CAAC+C,YAAH,EAAzE,CAAP;AAAqG,KAA5H,EAA8H,GAA9H,CAA/B;AACH;;AACDxD,EAAAA,sBAAsB,GAAG;AACrB,UAAMjB,SAAS,GAAG,KAAKA,SAAvB;AACA,UAAMV,OAAO,GAAGU,SAAS,CAAC2B,aAA1B;AACA,SAAKnB,gBAAL;;AACA,QAAI,CAAClB,OAAO,CAACoF,WAAb,EAA0B;AACtB;AACH;;AACD,QAAIN,QAAQ,KAAK,IAAb,IAAqBA,QAAQ,KAAK,KAAK,CAAvC,GAA2C,KAAK,CAAhD,GAAoDA,QAAQ,CAACO,MAAjE,EAAyE;AACrE3E,MAAAA,SAAS,CAAC4E,UAAV,GAAuB,IAAvB;AACA5E,MAAAA,SAAS,CAAC6E,KAAV;AACH,KAHD,MAIK;AACD7E,MAAAA,SAAS,CAAC4E,UAAV,GAAuB,KAAvB;;AACA,UAAI5E,SAAS,CAAC8E,kBAAV,EAAJ,EAAoC;AAChC9E,QAAAA,SAAS,CAAC+E,IAAV,CAAe,IAAf;AACH,OAFD,MAGK;AACD/E,QAAAA,SAAS,CAACgF,IAAV,CAAe,IAAf;AACH;AACJ;AACJ;;AACDjE,EAAAA,SAAS,GAAG;AACR,UAAMc,aAAa,GAAG,KAAK7B,SAAL,CAAe6B,aAArC;;AACA,QAAIA,aAAJ,EAAmB;AACf,YAAMoD,KAAK,GAAGpD,aAAa,CAACoD,KAA5B;AACAA,MAAAA,KAAK,CAACC,QAAN,GAAiB,IAAjB;AACAD,MAAAA,KAAK,CAACE,YAAN,GAAqBF,KAAK,CAACG,QAA3B;AACH;AACJ;;AACDhF,EAAAA,cAAc,CAACD,CAAD,EAAI;AACd,QAAIuB,EAAJ,EAAQ2D,EAAR,EAAYC,EAAZ,EAAgBC,EAAhB,EAAoBC,EAApB,EAAwBC,EAAxB,EAA4BC,EAA5B;;AACA,UAAM1F,SAAS,GAAG,KAAKA,SAAvB;AACA,UAAMV,OAAO,GAAGU,SAAS,CAAC2B,aAA1B;;AACA,QAAI,CAAC,CAACD,EAAE,GAAG1B,SAAS,CAAC6B,aAAhB,MAAmC,IAAnC,IAA2CH,EAAE,KAAK,KAAK,CAAvD,GAA2D,KAAK,CAAhE,GAAoEA,EAAE,CAACxC,OAAxE,MAAqFQ,SAAzF,EAAoG;AAChG;AACH;;AACDM,IAAAA,SAAS,CAAC6B,aAAV,CAAwBoD,KAAxB,CAA8BU,MAA9B,GAAuC,IAAvC;AACA,QAAIC,GAAJ;AACA,UAAM1D,MAAM,GAAGlC,SAAS,CAACkC,MAAV,CAAiBhD,OAAhC;;AACA,QAAIiB,CAAC,CAAC0F,IAAF,CAAOC,UAAP,CAAkB,OAAlB,CAAJ,EAAgC;AAC5B,WAAK7F,OAAL,GAAe,IAAf;AACA,YAAM8F,UAAU,GAAG5F,CAAnB;;AACA,UAAIH,SAAS,CAAC6B,aAAV,CAAwB3C,OAAxB,KAAoC8C,MAAxC,EAAgD;AAC5C,YAAIE,MAAJ,EAAY;AACR,gBAAM8D,UAAU,GAAG9D,MAAM,CAAC+D,qBAAP,EAAnB;AACAL,UAAAA,GAAG,GAAG;AACFM,YAAAA,CAAC,EAAEH,UAAU,CAACI,OAAX,GAAqBH,UAAU,CAACI,IADjC;AAEFC,YAAAA,CAAC,EAAEN,UAAU,CAACO,OAAX,GAAqBN,UAAU,CAACO;AAFjC,WAAN;AAIH;AACJ,OARD,MASK,IAAIjH,OAAO,CAACuC,aAAR,CAAsBC,SAAtB,KAAoC,QAAxC,EAAkD;AACnD,cAAM0E,MAAM,GAAGT,UAAU,CAAC9B,MAA1B;AACA,cAAMA,MAAM,GAAG8B,UAAU,CAACU,aAA1B;AACA,cAAMtE,QAAQ,GAAGnC,SAAS,CAACkC,MAAV,CAAiBhD,OAAlC;;AACA,YAAIsH,MAAM,IAAIvC,MAAV,IAAoB9B,QAAxB,EAAkC;AAC9B,gBAAMuE,UAAU,GAAGF,MAAM,CAACP,qBAAP,EAAnB;AACA,gBAAMU,UAAU,GAAG1C,MAAM,CAACgC,qBAAP,EAAnB;AACA,gBAAMW,UAAU,GAAGzE,QAAQ,CAAC8D,qBAAT,EAAnB;AACAL,UAAAA,GAAG,GAAG;AACFM,YAAAA,CAAC,EAAEH,UAAU,CAACc,OAAX,GAAqB,IAAIH,UAAU,CAACN,IAApC,IAA4CO,UAAU,CAACP,IAAX,GAAkBQ,UAAU,CAACR,IAAzE,CADD;AAEFC,YAAAA,CAAC,EAAEN,UAAU,CAACe,OAAX,GAAqB,IAAIJ,UAAU,CAACH,GAApC,IAA2CI,UAAU,CAACJ,GAAX,GAAiBK,UAAU,CAACL,GAAvE;AAFD,WAAN;AAIH,SARD,MASK;AACDX,UAAAA,GAAG,GAAG;AACFM,YAAAA,CAAC,EAAE,CAACb,EAAE,GAAGU,UAAU,CAACc,OAAjB,MAA8B,IAA9B,IAAsCxB,EAAE,KAAK,KAAK,CAAlD,GAAsDA,EAAtD,GAA2DU,UAAU,CAACI,OADvE;AAEFE,YAAAA,CAAC,EAAE,CAACf,EAAE,GAAGS,UAAU,CAACe,OAAjB,MAA8B,IAA9B,IAAsCxB,EAAE,KAAK,KAAK,CAAlD,GAAsDA,EAAtD,GAA2DS,UAAU,CAACO;AAFvE,WAAN;AAIH;AACJ,OAnBI,MAoBA;AACD,YAAIP,UAAU,CAAC9B,MAAX,KAAsBjE,SAAS,CAACkC,MAAV,CAAiBhD,OAA3C,EAAoD;AAChD0G,UAAAA,GAAG,GAAG;AACFM,YAAAA,CAAC,EAAE,CAACX,EAAE,GAAGQ,UAAU,CAACc,OAAjB,MAA8B,IAA9B,IAAsCtB,EAAE,KAAK,KAAK,CAAlD,GAAsDA,EAAtD,GAA2DQ,UAAU,CAACI,OADvE;AAEFE,YAAAA,CAAC,EAAE,CAACb,EAAE,GAAGO,UAAU,CAACe,OAAjB,MAA8B,IAA9B,IAAsCtB,EAAE,KAAK,KAAK,CAAlD,GAAsDA,EAAtD,GAA2DO,UAAU,CAACO;AAFvE,WAAN;AAIH;AACJ;AACJ,KAxCD,MAyCK;AACD,WAAKrG,OAAL,GAAeE,CAAC,CAAC0F,IAAF,KAAW,WAA1B;AACA,YAAMkB,UAAU,GAAG5G,CAAnB;AACA,YAAM6G,SAAS,GAAGD,UAAU,CAACE,OAAX,CAAmBF,UAAU,CAACE,OAAX,CAAmBC,MAAnB,GAA4B,CAA/C,CAAlB;AACA,YAAMN,UAAU,GAAG1E,MAAM,KAAK,IAAX,IAAmBA,MAAM,KAAK,KAAK,CAAnC,GAAuC,KAAK,CAA5C,GAAgDA,MAAM,CAAC+D,qBAAP,EAAnE;AACAL,MAAAA,GAAG,GAAG;AACFM,QAAAA,CAAC,EAAEc,SAAS,CAACb,OAAV,IAAqB,CAACV,EAAE,GAAGmB,UAAU,KAAK,IAAf,IAAuBA,UAAU,KAAK,KAAK,CAA3C,GAA+C,KAAK,CAApD,GAAwDA,UAAU,CAACR,IAAzE,MAAmF,IAAnF,IAA2FX,EAAE,KAAK,KAAK,CAAvG,GAA2GA,EAA3G,GAAgH,CAArI,CADD;AAEFY,QAAAA,CAAC,EAAEW,SAAS,CAACV,OAAV,IAAqB,CAACZ,EAAE,GAAGkB,UAAU,KAAK,IAAf,IAAuBA,UAAU,KAAK,KAAK,CAA3C,GAA+C,KAAK,CAApD,GAAwDA,UAAU,CAACL,GAAzE,MAAkF,IAAlF,IAA0Fb,EAAE,KAAK,KAAK,CAAtG,GAA0GA,EAA1G,GAA+G,CAApI;AAFD,OAAN;AAIH;;AACD,UAAMyB,OAAO,GAAGnH,SAAS,CAACoH,MAAV,CAAiBC,UAAjC;;AACA,QAAIzB,GAAJ,EAAS;AACLA,MAAAA,GAAG,CAACM,CAAJ,IAASiB,OAAT;AACAvB,MAAAA,GAAG,CAACS,CAAJ,IAASc,OAAT;AACH;;AACDnH,IAAAA,SAAS,CAAC6B,aAAV,CAAwBoD,KAAxB,CAA8BG,QAA9B,GAAyCQ,GAAzC;AACA5F,IAAAA,SAAS,CAAC6B,aAAV,CAAwByF,MAAxB,GAAiCtI,SAAS,CAACgE,cAA3C;AACH;;AACDxC,EAAAA,gBAAgB,GAAG;AACf,UAAMqB,aAAa,GAAG,KAAK7B,SAAL,CAAe6B,aAArC;;AACA,QAAIA,aAAa,KAAKnC,SAAtB,EAAiC;AAC7B;AACH;;AACD,UAAMuF,KAAK,GAAGpD,aAAa,CAACoD,KAA5B;AACA,WAAOA,KAAK,CAACG,QAAb;AACA,WAAOH,KAAK,CAACsC,aAAb;AACA,WAAOtC,KAAK,CAACE,YAAb;AACAtD,IAAAA,aAAa,CAACyF,MAAd,GAAuBtI,SAAS,CAAC+C,eAAjC;AACAkD,IAAAA,KAAK,CAACU,MAAN,GAAe,KAAf;AACAV,IAAAA,KAAK,CAACC,QAAN,GAAiB,KAAjB;AACH;;AACDtE,EAAAA,eAAe,CAACT,CAAD,EAAI;AACf,UAAMH,SAAS,GAAG,KAAKA,SAAvB;AACA,UAAMV,OAAO,GAAGU,SAAS,CAAC2B,aAA1B;AACA,UAAMsD,KAAK,GAAGjF,SAAS,CAAC6B,aAAV,CAAwBoD,KAAtC;AACAA,IAAAA,KAAK,CAACU,MAAN,GAAe,IAAf;AACA,QAAI6B,OAAO,GAAG,KAAd;AACA,UAAMC,aAAa,GAAGxC,KAAK,CAACG,QAA5B;;AACA,QAAIqC,aAAa,KAAK/H,SAAlB,IAA+B,CAACJ,OAAO,CAACuC,aAAR,CAAsBe,MAAtB,CAA6BG,OAA7B,CAAqCD,MAAzE,EAAiF;AAC7E;AACH;;AACD,SAAK,MAAM,GAAG4E,MAAH,CAAX,IAAyB1H,SAAS,CAAC2H,OAAnC,EAA4C;AACxC,UAAID,MAAM,CAACE,kBAAP,KAA8BlI,SAAlC,EAA6C;AACzC8H,QAAAA,OAAO,GAAGE,MAAM,CAACE,kBAAP,CAA0BH,aAA1B,CAAV;;AACA,YAAID,OAAJ,EAAa;AACT;AACH;AACJ;AACJ;;AACD,QAAI,CAACA,OAAL,EAAc;AACV,WAAKK,iBAAL,CAAuB1H,CAAvB;AACH;;AACD8E,IAAAA,KAAK,CAACC,QAAN,GAAiB,KAAjB;AACH;;AACD2C,EAAAA,iBAAiB,CAAC1H,CAAD,EAAI;AACjB,UAAMH,SAAS,GAAG,KAAKA,SAAvB;AACA,UAAMV,OAAO,GAAGU,SAAS,CAAC2B,aAA1B;;AACA,QAAI,KAAK1B,OAAT,EAAkB;AACd,YAAM6H,QAAQ,GAAG9H,SAAS,CAAC6B,aAAV,CAAwBoD,KAAxB,CAA8BG,QAA/C;;AACA,UAAI0C,QAAJ,EAAc;AACV9H,QAAAA,SAAS,CAAC6B,aAAV,CAAwBoD,KAAxB,CAA8BsC,aAA9B,GAA8C;AAC1CrB,UAAAA,CAAC,EAAE4B,QAAQ,CAAC5B,CAD8B;AAE1CG,UAAAA,CAAC,EAAEyB,QAAQ,CAACzB;AAF8B,SAA9C;AAIH,OALD,MAMK;AACD;AACH;;AACDrG,MAAAA,SAAS,CAAC6B,aAAV,CAAwBoD,KAAxB,CAA8B8C,SAA9B,GAA0C,IAAIC,IAAJ,GAAWC,OAAX,EAA1C;AACA,YAAMlF,OAAO,GAAGzD,OAAO,CAACuC,aAAR,CAAsBe,MAAtB,CAA6BG,OAA7C;;AACA,UAAIA,OAAO,CAACmF,IAAR,YAAwBC,KAA5B,EAAmC;AAC/B,aAAK,MAAMD,IAAX,IAAmBnF,OAAO,CAACmF,IAA3B,EAAiC;AAC7B,eAAKE,eAAL,CAAqBF,IAArB;AACH;AACJ,OAJD,MAKK;AACD,aAAKE,eAAL,CAAqBrF,OAAO,CAACmF,IAA7B;AACH;AACJ;;AACD,QAAI/H,CAAC,CAAC0F,IAAF,KAAW,UAAf,EAA2B;AACvBrB,MAAAA,UAAU,CAAC,MAAM,KAAKhE,gBAAL,EAAP,EAAgC,GAAhC,CAAV;AACH;AACJ;;AACDW,EAAAA,iBAAiB,CAAChB,CAAD,EAAI;AACjB,UAAMkI,UAAU,GAAGlI,CAAnB;AACA,UAAMmI,SAAS,GAAGD,UAAU,CAACE,OAAX,GACZ,KAAKvI,SAAL,CAAeV,OAAf,CAAuBkJ,gBADX,GAEZ,KAAKxI,SAAL,CAAeV,OAAf,CAAuBmJ,iBAF7B;AAGA,UAAMC,KAAK,GAAG,KAAK1I,SAAL,CAAeV,OAAf,CAAuBqJ,MAAvB,CAA8B3E,IAA9B,CAAoC0E,KAAD,IAAWA,KAAK,CAACE,IAAN,KAAeN,SAA7D,CAAd;;AACA,QAAII,KAAK,IAAIA,KAAK,CAACG,OAAN,CAAcC,IAA3B,EAAiC;AAC7B,WAAK9I,SAAL,CAAe+I,SAAf,CAAyBT,SAAzB;AACH;AACJ;;AACDF,EAAAA,eAAe,CAACF,IAAD,EAAO;AAClB,UAAMlI,SAAS,GAAG,KAAKA,SAAvB;AACA,UAAMV,OAAO,GAAGU,SAAS,CAAC2B,aAA1B;AACA,UAAMqH,MAAM,GAAG1J,OAAO,CAACuC,aAAR,CAAsBoH,KAAtB,CAA4BC,IAA5B,CAAiCC,QAAhD;AACA,UAAMC,QAAQ,GAAG9J,OAAO,CAACuC,aAAR,CAAsBoH,KAAtB,CAA4BI,MAA5B,CAAmCF,QAApD;;AACA,YAAQjB,IAAR;AACI,WAAK,MAAL;AAAa;AACT,cAAIc,MAAM,GAAG,CAAb,EAAgB;AACZ,kBAAMM,WAAW,GAAGhK,OAAO,CAACuC,aAAR,CAAsBoH,KAAtB,CAA4BC,IAAhD;AACA,kBAAMK,KAAK,GAAGxK,aAAa,CAAC,CAACW,SAAD,EAAY,GAAG4J,WAAW,CAACE,MAA3B,CAAD,CAA3B;AACA,kBAAMC,YAAY,GAAGF,KAAK,KAAK7J,SAAV,GAAsBM,SAAS,CAAC2B,aAAV,CAAwB+H,SAAxB,CAAkCF,MAAlC,CAAyCD,KAAzC,CAAtB,GAAwE7J,SAA7F;AACAM,YAAAA,SAAS,CAAC0J,SAAV,CAAoBR,IAApB,CAAyBF,MAAzB,EAAiChJ,SAAS,CAAC6B,aAAV,CAAwBoD,KAAzD,EAAgEwE,YAAhE,EAA8EF,KAA9E;AACH;;AACD;AACH;;AACD,WAAK,QAAL;AACIvJ,QAAAA,SAAS,CAAC0J,SAAV,CAAoBC,cAApB,CAAmCP,QAAnC;AACA;;AACJ,WAAK,QAAL;AACIpJ,QAAAA,SAAS,CAAC4J,MAAV,CAAiB1E,QAAjB,GAA4B,IAA5B;AACA;;AACJ,WAAK,SAAL;AACIlF,QAAAA,SAAS,CAAC6J,OAAV,CAAkB3E,QAAlB,GAA6B,IAA7B;AACAlF,QAAAA,SAAS,CAAC6J,OAAV,CAAkBC,KAAlB,GAA0B,CAA1B;;AACA,aAAK,MAAMC,QAAX,IAAuB/J,SAAS,CAAC6J,OAAV,CAAkBH,SAAzC,EAAoD;AAChDK,UAAAA,QAAQ,CAACC,QAAT,CAAkBC,KAAlB,CAAwBF,QAAQ,CAACG,eAAjC;AACH;;AACDlK,QAAAA,SAAS,CAAC6J,OAAV,CAAkBH,SAAlB,GAA8B,EAA9B;AACA1J,QAAAA,SAAS,CAAC6J,OAAV,CAAkBM,MAAlB,GAA2B,KAA3B;AACA3F,QAAAA,UAAU,CAAC,MAAM;AACb,cAAI,CAACxE,SAAS,CAACoK,SAAf,EAA0B;AACtBpK,YAAAA,SAAS,CAAC6J,OAAV,CAAkB3E,QAAlB,GAA6B,KAA7B;AACH;AACJ,SAJS,EAIP5F,OAAO,CAACuC,aAAR,CAAsBoH,KAAtB,CAA4BY,OAA5B,CAAoCQ,QAApC,GAA+C,IAJxC,CAAV;AAKA;;AACJ,WAAK,SAAL;AACIrK,QAAAA,SAAS,CAACsK,OAAV,CAAkBpF,QAAlB,GAA6B,IAA7B;AACAlF,QAAAA,SAAS,CAACsK,OAAV,CAAkBR,KAAlB,GAA0B,CAA1B;;AACA,aAAK,MAAMC,QAAX,IAAuB/J,SAAS,CAACsK,OAAV,CAAkBZ,SAAzC,EAAoD;AAChDK,UAAAA,QAAQ,CAACC,QAAT,CAAkBC,KAAlB,CAAwBF,QAAQ,CAACG,eAAjC;AACH;;AACDlK,QAAAA,SAAS,CAACsK,OAAV,CAAkBZ,SAAlB,GAA8B,EAA9B;AACA1J,QAAAA,SAAS,CAACsK,OAAV,CAAkBH,MAAlB,GAA2B,KAA3B;AACA3F,QAAAA,UAAU,CAAC,MAAM;AACb,cAAI,CAACxE,SAAS,CAACoK,SAAf,EAA0B;AACtBpK,YAAAA,SAAS,CAACsK,OAAV,CAAkBpF,QAAlB,GAA6B,KAA7B;AACH;AACJ,SAJS,EAIP5F,OAAO,CAACuC,aAAR,CAAsBoH,KAAtB,CAA4BqB,OAA5B,CAAoCD,QAApC,GAA+C,IAJxC,CAAV;AAKA;;AACJ,WAAK,OAAL;AACI,YAAIrK,SAAS,CAAC8E,kBAAV,EAAJ,EAAoC;AAChC9E,UAAAA,SAAS,CAAC6E,KAAV;AACH,SAFD,MAGK;AACD7E,UAAAA,SAAS,CAAC+E,IAAV;AACH;;AACD;AAnDR;;AAqDA,SAAK,MAAM,GAAG2C,MAAH,CAAX,IAAyB1H,SAAS,CAAC2H,OAAnC,EAA4C;AACxC,UAAID,MAAM,CAACU,eAAX,EAA4B;AACxBV,QAAAA,MAAM,CAACU,eAAP,CAAuBF,IAAvB;AACH;AACJ;AACJ;;AA7VuB", "sourcesContent": ["import { isSsr, itemFromArray } from \"../../Utils\";\nimport { Constants } from \"./Constants\";\nfunction manageListener(element, event, handler, add, options) {\n    if (add) {\n        let addOptions = { passive: true };\n        if (typeof options === \"boolean\") {\n            addOptions.capture = options;\n        }\n        else if (options !== undefined) {\n            addOptions = options;\n        }\n        element.addEventListener(event, handler, addOptions);\n    }\n    else {\n        const removeOptions = options;\n        element.removeEventListener(event, handler, removeOptions);\n    }\n}\nexport class EventListeners {\n    constructor(container) {\n        this.container = container;\n        this.canPush = true;\n        this.mouseMoveHandler = (e) => this.mouseTouchMove(e);\n        this.touchStartHandler = (e) => this.mouseTouchMove(e);\n        this.touchMoveHandler = (e) => this.mouseTouchMove(e);\n        this.touchEndHandler = () => this.mouseTouchFinish();\n        this.mouseLeaveHandler = () => this.mouseTouchFinish();\n        this.touchCancelHandler = () => this.mouseTouchFinish();\n        this.touchEndClickHandler = (e) => this.mouseTouchClick(e);\n        this.mouseUpHandler = (e) => this.mouseTouchClick(e);\n        this.mouseDownHandler = () => this.mouseDown();\n        this.visibilityChangeHandler = () => this.handleVisibilityChange();\n        this.themeChangeHandler = (e) => this.handleThemeChange(e);\n        this.oldThemeChangeHandler = (e) => this.handleThemeChange(e);\n        this.resizeHandler = () => this.handleWindowResize();\n    }\n    addListeners() {\n        this.manageListeners(true);\n    }\n    removeListeners() {\n        this.manageListeners(false);\n    }\n    manageListeners(add) {\n        var _a;\n        const container = this.container;\n        const options = container.actualOptions;\n        const detectType = options.interactivity.detectsOn;\n        let mouseLeaveEvent = Constants.mouseLeaveEvent;\n        if (detectType === \"window\") {\n            container.interactivity.element = window;\n            mouseLeaveEvent = Constants.mouseOutEvent;\n        }\n        else if (detectType === \"parent\" && container.canvas.element) {\n            const canvasEl = container.canvas.element;\n            container.interactivity.element = (_a = canvasEl.parentElement) !== null && _a !== void 0 ? _a : canvasEl.parentNode;\n        }\n        else {\n            container.interactivity.element = container.canvas.element;\n        }\n        const mediaMatch = !isSsr() && typeof matchMedia !== \"undefined\" && matchMedia(\"(prefers-color-scheme: dark)\");\n        if (mediaMatch) {\n            if (mediaMatch.addEventListener !== undefined) {\n                manageListener(mediaMatch, \"change\", this.themeChangeHandler, add);\n            }\n            else if (mediaMatch.addListener !== undefined) {\n                if (add) {\n                    mediaMatch.addListener(this.oldThemeChangeHandler);\n                }\n                else {\n                    mediaMatch.removeListener(this.oldThemeChangeHandler);\n                }\n            }\n        }\n        const interactivityEl = container.interactivity.element;\n        if (!interactivityEl) {\n            return;\n        }\n        const html = interactivityEl;\n        if (options.interactivity.events.onHover.enable || options.interactivity.events.onClick.enable) {\n            manageListener(interactivityEl, Constants.mouseMoveEvent, this.mouseMoveHandler, add);\n            manageListener(interactivityEl, Constants.touchStartEvent, this.touchStartHandler, add);\n            manageListener(interactivityEl, Constants.touchMoveEvent, this.touchMoveHandler, add);\n            if (!options.interactivity.events.onClick.enable) {\n                manageListener(interactivityEl, Constants.touchEndEvent, this.touchEndHandler, add);\n            }\n            else {\n                manageListener(interactivityEl, Constants.touchEndEvent, this.touchEndClickHandler, add);\n                manageListener(interactivityEl, Constants.mouseUpEvent, this.mouseUpHandler, add);\n                manageListener(interactivityEl, Constants.mouseDownEvent, this.mouseDownHandler, add);\n            }\n            manageListener(interactivityEl, mouseLeaveEvent, this.mouseLeaveHandler, add);\n            manageListener(interactivityEl, Constants.touchCancelEvent, this.touchCancelHandler, add);\n        }\n        if (container.canvas.element) {\n            container.canvas.element.style.pointerEvents = html === container.canvas.element ? \"initial\" : \"none\";\n        }\n        if (options.interactivity.events.resize) {\n            if (typeof ResizeObserver !== \"undefined\") {\n                if (this.resizeObserver && !add) {\n                    if (container.canvas.element) {\n                        this.resizeObserver.unobserve(container.canvas.element);\n                    }\n                    this.resizeObserver.disconnect();\n                    delete this.resizeObserver;\n                }\n                else if (!this.resizeObserver && add && container.canvas.element) {\n                    this.resizeObserver = new ResizeObserver((entries) => {\n                        const entry = entries.find((e) => e.target === container.canvas.element);\n                        if (!entry) {\n                            return;\n                        }\n                        this.handleWindowResize();\n                    });\n                    this.resizeObserver.observe(container.canvas.element);\n                }\n            }\n            else {\n                manageListener(window, Constants.resizeEvent, this.resizeHandler, add);\n            }\n        }\n        if (document) {\n            manageListener(document, Constants.visibilityChangeEvent, this.visibilityChangeHandler, add, false);\n        }\n    }\n    handleWindowResize() {\n        if (this.resizeTimeout) {\n            clearTimeout(this.resizeTimeout);\n            delete this.resizeTimeout;\n        }\n        this.resizeTimeout = setTimeout(async () => { var _a; return await ((_a = this.container.canvas) === null || _a === void 0 ? void 0 : _a.windowResize()); }, 500);\n    }\n    handleVisibilityChange() {\n        const container = this.container;\n        const options = container.actualOptions;\n        this.mouseTouchFinish();\n        if (!options.pauseOnBlur) {\n            return;\n        }\n        if (document === null || document === void 0 ? void 0 : document.hidden) {\n            container.pageHidden = true;\n            container.pause();\n        }\n        else {\n            container.pageHidden = false;\n            if (container.getAnimationStatus()) {\n                container.play(true);\n            }\n            else {\n                container.draw(true);\n            }\n        }\n    }\n    mouseDown() {\n        const interactivity = this.container.interactivity;\n        if (interactivity) {\n            const mouse = interactivity.mouse;\n            mouse.clicking = true;\n            mouse.downPosition = mouse.position;\n        }\n    }\n    mouseTouchMove(e) {\n        var _a, _b, _c, _d, _e, _f, _g;\n        const container = this.container;\n        const options = container.actualOptions;\n        if (((_a = container.interactivity) === null || _a === void 0 ? void 0 : _a.element) === undefined) {\n            return;\n        }\n        container.interactivity.mouse.inside = true;\n        let pos;\n        const canvas = container.canvas.element;\n        if (e.type.startsWith(\"mouse\")) {\n            this.canPush = true;\n            const mouseEvent = e;\n            if (container.interactivity.element === window) {\n                if (canvas) {\n                    const clientRect = canvas.getBoundingClientRect();\n                    pos = {\n                        x: mouseEvent.clientX - clientRect.left,\n                        y: mouseEvent.clientY - clientRect.top,\n                    };\n                }\n            }\n            else if (options.interactivity.detectsOn === \"parent\") {\n                const source = mouseEvent.target;\n                const target = mouseEvent.currentTarget;\n                const canvasEl = container.canvas.element;\n                if (source && target && canvasEl) {\n                    const sourceRect = source.getBoundingClientRect();\n                    const targetRect = target.getBoundingClientRect();\n                    const canvasRect = canvasEl.getBoundingClientRect();\n                    pos = {\n                        x: mouseEvent.offsetX + 2 * sourceRect.left - (targetRect.left + canvasRect.left),\n                        y: mouseEvent.offsetY + 2 * sourceRect.top - (targetRect.top + canvasRect.top),\n                    };\n                }\n                else {\n                    pos = {\n                        x: (_b = mouseEvent.offsetX) !== null && _b !== void 0 ? _b : mouseEvent.clientX,\n                        y: (_c = mouseEvent.offsetY) !== null && _c !== void 0 ? _c : mouseEvent.clientY,\n                    };\n                }\n            }\n            else {\n                if (mouseEvent.target === container.canvas.element) {\n                    pos = {\n                        x: (_d = mouseEvent.offsetX) !== null && _d !== void 0 ? _d : mouseEvent.clientX,\n                        y: (_e = mouseEvent.offsetY) !== null && _e !== void 0 ? _e : mouseEvent.clientY,\n                    };\n                }\n            }\n        }\n        else {\n            this.canPush = e.type !== \"touchmove\";\n            const touchEvent = e;\n            const lastTouch = touchEvent.touches[touchEvent.touches.length - 1];\n            const canvasRect = canvas === null || canvas === void 0 ? void 0 : canvas.getBoundingClientRect();\n            pos = {\n                x: lastTouch.clientX - ((_f = canvasRect === null || canvasRect === void 0 ? void 0 : canvasRect.left) !== null && _f !== void 0 ? _f : 0),\n                y: lastTouch.clientY - ((_g = canvasRect === null || canvasRect === void 0 ? void 0 : canvasRect.top) !== null && _g !== void 0 ? _g : 0),\n            };\n        }\n        const pxRatio = container.retina.pixelRatio;\n        if (pos) {\n            pos.x *= pxRatio;\n            pos.y *= pxRatio;\n        }\n        container.interactivity.mouse.position = pos;\n        container.interactivity.status = Constants.mouseMoveEvent;\n    }\n    mouseTouchFinish() {\n        const interactivity = this.container.interactivity;\n        if (interactivity === undefined) {\n            return;\n        }\n        const mouse = interactivity.mouse;\n        delete mouse.position;\n        delete mouse.clickPosition;\n        delete mouse.downPosition;\n        interactivity.status = Constants.mouseLeaveEvent;\n        mouse.inside = false;\n        mouse.clicking = false;\n    }\n    mouseTouchClick(e) {\n        const container = this.container;\n        const options = container.actualOptions;\n        const mouse = container.interactivity.mouse;\n        mouse.inside = true;\n        let handled = false;\n        const mousePosition = mouse.position;\n        if (mousePosition === undefined || !options.interactivity.events.onClick.enable) {\n            return;\n        }\n        for (const [, plugin] of container.plugins) {\n            if (plugin.clickPositionValid !== undefined) {\n                handled = plugin.clickPositionValid(mousePosition);\n                if (handled) {\n                    break;\n                }\n            }\n        }\n        if (!handled) {\n            this.doMouseTouchClick(e);\n        }\n        mouse.clicking = false;\n    }\n    doMouseTouchClick(e) {\n        const container = this.container;\n        const options = container.actualOptions;\n        if (this.canPush) {\n            const mousePos = container.interactivity.mouse.position;\n            if (mousePos) {\n                container.interactivity.mouse.clickPosition = {\n                    x: mousePos.x,\n                    y: mousePos.y,\n                };\n            }\n            else {\n                return;\n            }\n            container.interactivity.mouse.clickTime = new Date().getTime();\n            const onClick = options.interactivity.events.onClick;\n            if (onClick.mode instanceof Array) {\n                for (const mode of onClick.mode) {\n                    this.handleClickMode(mode);\n                }\n            }\n            else {\n                this.handleClickMode(onClick.mode);\n            }\n        }\n        if (e.type === \"touchend\") {\n            setTimeout(() => this.mouseTouchFinish(), 500);\n        }\n    }\n    handleThemeChange(e) {\n        const mediaEvent = e;\n        const themeName = mediaEvent.matches\n            ? this.container.options.defaultDarkTheme\n            : this.container.options.defaultLightTheme;\n        const theme = this.container.options.themes.find((theme) => theme.name === themeName);\n        if (theme && theme.default.auto) {\n            this.container.loadTheme(themeName);\n        }\n    }\n    handleClickMode(mode) {\n        const container = this.container;\n        const options = container.actualOptions;\n        const pushNb = options.interactivity.modes.push.quantity;\n        const removeNb = options.interactivity.modes.remove.quantity;\n        switch (mode) {\n            case \"push\": {\n                if (pushNb > 0) {\n                    const pushOptions = options.interactivity.modes.push;\n                    const group = itemFromArray([undefined, ...pushOptions.groups]);\n                    const groupOptions = group !== undefined ? container.actualOptions.particles.groups[group] : undefined;\n                    container.particles.push(pushNb, container.interactivity.mouse, groupOptions, group);\n                }\n                break;\n            }\n            case \"remove\":\n                container.particles.removeQuantity(removeNb);\n                break;\n            case \"bubble\":\n                container.bubble.clicking = true;\n                break;\n            case \"repulse\":\n                container.repulse.clicking = true;\n                container.repulse.count = 0;\n                for (const particle of container.repulse.particles) {\n                    particle.velocity.setTo(particle.initialVelocity);\n                }\n                container.repulse.particles = [];\n                container.repulse.finish = false;\n                setTimeout(() => {\n                    if (!container.destroyed) {\n                        container.repulse.clicking = false;\n                    }\n                }, options.interactivity.modes.repulse.duration * 1000);\n                break;\n            case \"attract\":\n                container.attract.clicking = true;\n                container.attract.count = 0;\n                for (const particle of container.attract.particles) {\n                    particle.velocity.setTo(particle.initialVelocity);\n                }\n                container.attract.particles = [];\n                container.attract.finish = false;\n                setTimeout(() => {\n                    if (!container.destroyed) {\n                        container.attract.clicking = false;\n                    }\n                }, options.interactivity.modes.attract.duration * 1000);\n                break;\n            case \"pause\":\n                if (container.getAnimationStatus()) {\n                    container.pause();\n                }\n                else {\n                    container.play();\n                }\n                break;\n        }\n        for (const [, plugin] of container.plugins) {\n            if (plugin.handleClickMode) {\n                plugin.handleClickMode(mode);\n            }\n        }\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
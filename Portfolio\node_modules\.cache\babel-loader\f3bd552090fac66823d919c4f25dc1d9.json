{"ast": null, "code": "export class RepulseBase {\n  constructor() {\n    this.distance = 200;\n    this.duration = 0.4;\n    this.factor = 100;\n    this.speed = 1;\n    this.maxSpeed = 50;\n    this.easing = \"ease-out-quad\";\n  }\n\n  load(data) {\n    if (!data) {\n      return;\n    }\n\n    if (data.distance !== undefined) {\n      this.distance = data.distance;\n    }\n\n    if (data.duration !== undefined) {\n      this.duration = data.duration;\n    }\n\n    if (data.easing !== undefined) {\n      this.easing = data.easing;\n    }\n\n    if (data.factor !== undefined) {\n      this.factor = data.factor;\n    }\n\n    if (data.speed !== undefined) {\n      this.speed = data.speed;\n    }\n\n    if (data.maxSpeed !== undefined) {\n      this.maxSpeed = data.maxSpeed;\n    }\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Options/Classes/Interactivity/Modes/RepulseBase.js"], "names": ["RepulseBase", "constructor", "distance", "duration", "factor", "speed", "maxSpeed", "easing", "load", "data", "undefined"], "mappings": "AAAA,OAAO,MAAMA,WAAN,CAAkB;AACrBC,EAAAA,WAAW,GAAG;AACV,SAAKC,QAAL,GAAgB,GAAhB;AACA,SAAKC,QAAL,GAAgB,GAAhB;AACA,SAAKC,MAAL,GAAc,GAAd;AACA,SAAKC,KAAL,GAAa,CAAb;AACA,SAAKC,QAAL,GAAgB,EAAhB;AACA,SAAKC,MAAL,GAAc,eAAd;AACH;;AACDC,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,QAAI,CAACA,IAAL,EAAW;AACP;AACH;;AACD,QAAIA,IAAI,CAACP,QAAL,KAAkBQ,SAAtB,EAAiC;AAC7B,WAAKR,QAAL,GAAgBO,IAAI,CAACP,QAArB;AACH;;AACD,QAAIO,IAAI,CAACN,QAAL,KAAkBO,SAAtB,EAAiC;AAC7B,WAAKP,QAAL,GAAgBM,IAAI,CAACN,QAArB;AACH;;AACD,QAAIM,IAAI,CAACF,MAAL,KAAgBG,SAApB,EAA+B;AAC3B,WAAKH,MAAL,GAAcE,IAAI,CAACF,MAAnB;AACH;;AACD,QAAIE,IAAI,CAACL,MAAL,KAAgBM,SAApB,EAA+B;AAC3B,WAAKN,MAAL,GAAcK,IAAI,CAACL,MAAnB;AACH;;AACD,QAAIK,IAAI,CAACJ,KAAL,KAAeK,SAAnB,EAA8B;AAC1B,WAAKL,KAAL,GAAaI,IAAI,CAACJ,KAAlB;AACH;;AACD,QAAII,IAAI,CAACH,QAAL,KAAkBI,SAAtB,EAAiC;AAC7B,WAAKJ,QAAL,GAAgBG,IAAI,CAACH,QAArB;AACH;AACJ;;AA/BoB", "sourcesContent": ["export class RepulseBase {\n    constructor() {\n        this.distance = 200;\n        this.duration = 0.4;\n        this.factor = 100;\n        this.speed = 1;\n        this.maxSpeed = 50;\n        this.easing = \"ease-out-quad\";\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.distance !== undefined) {\n            this.distance = data.distance;\n        }\n        if (data.duration !== undefined) {\n            this.duration = data.duration;\n        }\n        if (data.easing !== undefined) {\n            this.easing = data.easing;\n        }\n        if (data.factor !== undefined) {\n            this.factor = data.factor;\n        }\n        if (data.speed !== undefined) {\n            this.speed = data.speed;\n        }\n        if (data.maxSpeed !== undefined) {\n            this.maxSpeed = data.maxSpeed;\n        }\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
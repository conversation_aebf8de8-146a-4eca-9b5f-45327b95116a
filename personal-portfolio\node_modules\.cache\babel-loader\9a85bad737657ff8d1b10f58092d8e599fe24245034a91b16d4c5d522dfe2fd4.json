{"ast": null, "code": "export var ParticleOutType;\n(function (ParticleOutType) {\n  ParticleOutType[\"normal\"] = \"normal\";\n  ParticleOutType[\"inside\"] = \"inside\";\n  ParticleOutType[\"outside\"] = \"outside\";\n})(ParticleOutType || (ParticleOutType = {}));", "map": {"version": 3, "names": ["ParticleOutType"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/engine/browser/Enums/Types/ParticleOutType.js"], "sourcesContent": ["export var ParticleOutType;\n(function (ParticleOutType) {\n    ParticleOutType[\"normal\"] = \"normal\";\n    ParticleOutType[\"inside\"] = \"inside\";\n    ParticleOutType[\"outside\"] = \"outside\";\n})(ParticleOutType || (ParticleOutType = {}));\n"], "mappings": "AAAA,OAAO,IAAIA,eAAe;AAC1B,CAAC,UAAUA,eAAe,EAAE;EACxBA,eAAe,CAAC,QAAQ,CAAC,GAAG,QAAQ;EACpCA,eAAe,CAAC,QAAQ,CAAC,GAAG,QAAQ;EACpCA,eAAe,CAAC,SAAS,CAAC,GAAG,SAAS;AAC1C,CAAC,EAAEA,eAAe,KAAKA,eAAe,GAAG,CAAC,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
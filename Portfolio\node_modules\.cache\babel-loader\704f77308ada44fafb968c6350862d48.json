{"ast": null, "code": "import { MotionReduce } from \"./MotionReduce\";\nexport class Motion {\n  constructor() {\n    this.disable = false;\n    this.reduce = new MotionReduce();\n  }\n\n  load(data) {\n    if (!data) {\n      return;\n    }\n\n    if (data.disable !== undefined) {\n      this.disable = data.disable;\n    }\n\n    this.reduce.load(data.reduce);\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Options/Classes/Motion/Motion.js"], "names": ["MotionReduce", "Motion", "constructor", "disable", "reduce", "load", "data", "undefined"], "mappings": "AAAA,SAASA,YAAT,QAA6B,gBAA7B;AACA,OAAO,MAAMC,MAAN,CAAa;AAChBC,EAAAA,WAAW,GAAG;AACV,SAAKC,OAAL,GAAe,KAAf;AACA,SAAKC,MAAL,GAAc,IAAIJ,YAAJ,EAAd;AACH;;AACDK,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,QAAI,CAACA,IAAL,EAAW;AACP;AACH;;AACD,QAAIA,IAAI,CAACH,OAAL,KAAiBI,SAArB,EAAgC;AAC5B,WAAKJ,OAAL,GAAeG,IAAI,CAACH,OAApB;AACH;;AACD,SAAKC,MAAL,CAAYC,IAAZ,CAAiBC,IAAI,CAACF,MAAtB;AACH;;AAbe", "sourcesContent": ["import { MotionReduce } from \"./MotionReduce\";\nexport class Motion {\n    constructor() {\n        this.disable = false;\n        this.reduce = new MotionReduce();\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.disable !== undefined) {\n            this.disable = data.disable;\n        }\n        this.reduce.load(data.reduce);\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
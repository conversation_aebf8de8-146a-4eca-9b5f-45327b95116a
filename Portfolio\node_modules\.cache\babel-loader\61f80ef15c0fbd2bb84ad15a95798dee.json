{"ast": null, "code": "import { OptionsColor } from \"../../OptionsColor\";\nexport class BubbleBase {\n  constructor() {\n    this.distance = 200;\n    this.duration = 0.4;\n    this.mix = false;\n  }\n\n  load(data) {\n    if (data === undefined) {\n      return;\n    }\n\n    if (data.distance !== undefined) {\n      this.distance = data.distance;\n    }\n\n    if (data.duration !== undefined) {\n      this.duration = data.duration;\n    }\n\n    if (data.mix !== undefined) {\n      this.mix = data.mix;\n    }\n\n    if (data.opacity !== undefined) {\n      this.opacity = data.opacity;\n    }\n\n    if (data.color !== undefined) {\n      if (data.color instanceof Array) {\n        this.color = data.color.map(s => OptionsColor.create(undefined, s));\n      } else {\n        if (this.color instanceof Array) {\n          this.color = new OptionsColor();\n        }\n\n        this.color = OptionsColor.create(this.color, data.color);\n      }\n    }\n\n    if (data.size !== undefined) {\n      this.size = data.size;\n    }\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Options/Classes/Interactivity/Modes/BubbleBase.js"], "names": ["OptionsColor", "BubbleBase", "constructor", "distance", "duration", "mix", "load", "data", "undefined", "opacity", "color", "Array", "map", "s", "create", "size"], "mappings": "AAAA,SAASA,YAAT,QAA6B,oBAA7B;AACA,OAAO,MAAMC,UAAN,CAAiB;AACpBC,EAAAA,WAAW,GAAG;AACV,SAAKC,QAAL,GAAgB,GAAhB;AACA,SAAKC,QAAL,GAAgB,GAAhB;AACA,SAAKC,GAAL,GAAW,KAAX;AACH;;AACDC,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,QAAIA,IAAI,KAAKC,SAAb,EAAwB;AACpB;AACH;;AACD,QAAID,IAAI,CAACJ,QAAL,KAAkBK,SAAtB,EAAiC;AAC7B,WAAKL,QAAL,GAAgBI,IAAI,CAACJ,QAArB;AACH;;AACD,QAAII,IAAI,CAACH,QAAL,KAAkBI,SAAtB,EAAiC;AAC7B,WAAKJ,QAAL,GAAgBG,IAAI,CAACH,QAArB;AACH;;AACD,QAAIG,IAAI,CAACF,GAAL,KAAaG,SAAjB,EAA4B;AACxB,WAAKH,GAAL,GAAWE,IAAI,CAACF,GAAhB;AACH;;AACD,QAAIE,IAAI,CAACE,OAAL,KAAiBD,SAArB,EAAgC;AAC5B,WAAKC,OAAL,GAAeF,IAAI,CAACE,OAApB;AACH;;AACD,QAAIF,IAAI,CAACG,KAAL,KAAeF,SAAnB,EAA8B;AAC1B,UAAID,IAAI,CAACG,KAAL,YAAsBC,KAA1B,EAAiC;AAC7B,aAAKD,KAAL,GAAaH,IAAI,CAACG,KAAL,CAAWE,GAAX,CAAgBC,CAAD,IAAOb,YAAY,CAACc,MAAb,CAAoBN,SAApB,EAA+BK,CAA/B,CAAtB,CAAb;AACH,OAFD,MAGK;AACD,YAAI,KAAKH,KAAL,YAAsBC,KAA1B,EAAiC;AAC7B,eAAKD,KAAL,GAAa,IAAIV,YAAJ,EAAb;AACH;;AACD,aAAKU,KAAL,GAAaV,YAAY,CAACc,MAAb,CAAoB,KAAKJ,KAAzB,EAAgCH,IAAI,CAACG,KAArC,CAAb;AACH;AACJ;;AACD,QAAIH,IAAI,CAACQ,IAAL,KAAcP,SAAlB,EAA6B;AACzB,WAAKO,IAAL,GAAYR,IAAI,CAACQ,IAAjB;AACH;AACJ;;AApCmB", "sourcesContent": ["import { OptionsColor } from \"../../OptionsColor\";\nexport class BubbleBase {\n    constructor() {\n        this.distance = 200;\n        this.duration = 0.4;\n        this.mix = false;\n    }\n    load(data) {\n        if (data === undefined) {\n            return;\n        }\n        if (data.distance !== undefined) {\n            this.distance = data.distance;\n        }\n        if (data.duration !== undefined) {\n            this.duration = data.duration;\n        }\n        if (data.mix !== undefined) {\n            this.mix = data.mix;\n        }\n        if (data.opacity !== undefined) {\n            this.opacity = data.opacity;\n        }\n        if (data.color !== undefined) {\n            if (data.color instanceof Array) {\n                this.color = data.color.map((s) => OptionsColor.create(undefined, s));\n            }\n            else {\n                if (this.color instanceof Array) {\n                    this.color = new OptionsColor();\n                }\n                this.color = OptionsColor.create(this.color, data.color);\n            }\n        }\n        if (data.size !== undefined) {\n            this.size = data.size;\n        }\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
{"ast": null, "code": "import getWindowScroll from \"./getWindowScroll.js\";\nimport getWindow from \"./getWindow.js\";\nimport { isHTMLElement } from \"./instanceOf.js\";\nimport getHTMLElementScroll from \"./getHTMLElementScroll.js\";\nexport default function getNodeScroll(node) {\n  if (node === getWindow(node) || !isHTMLElement(node)) {\n    return getWindowScroll(node);\n  } else {\n    return getHTMLElementScroll(node);\n  }\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/@popperjs/core/lib/dom-utils/getNodeScroll.js"], "names": ["getWindowScroll", "getWindow", "isHTMLElement", "getHTMLElementScroll", "getNodeScroll", "node"], "mappings": "AAAA,OAAOA,eAAP,MAA4B,sBAA5B;AACA,OAAOC,SAAP,MAAsB,gBAAtB;AACA,SAASC,aAAT,QAA8B,iBAA9B;AACA,OAAOC,oBAAP,MAAiC,2BAAjC;AACA,eAAe,SAASC,aAAT,CAAuBC,IAAvB,EAA6B;AAC1C,MAAIA,IAAI,KAAKJ,SAAS,CAACI,IAAD,CAAlB,IAA4B,CAACH,aAAa,CAACG,IAAD,CAA9C,EAAsD;AACpD,WAAOL,eAAe,CAACK,IAAD,CAAtB;AACD,GAFD,MAEO;AACL,WAAOF,oBAAoB,CAACE,IAAD,CAA3B;AACD;AACF", "sourcesContent": ["import getWindowScroll from \"./getWindowScroll.js\";\nimport getWindow from \"./getWindow.js\";\nimport { isHTMLElement } from \"./instanceOf.js\";\nimport getHTMLElementScroll from \"./getHTMLElementScroll.js\";\nexport default function getNodeScroll(node) {\n  if (node === getWindow(node) || !isHTMLElement(node)) {\n    return getWindowScroll(node);\n  } else {\n    return getHTMLElementScroll(node);\n  }\n}"]}, "metadata": {}, "sourceType": "module"}
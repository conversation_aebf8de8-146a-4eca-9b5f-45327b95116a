{"ast": null, "code": "import { Bouncer } from \"./Bouncer.js\";\nexport async function loadExternalBounceInteraction(engine, refresh = true) {\n  await engine.addInteractor(\"externalBounce\", container => {\n    return Promise.resolve(new Bouncer(container));\n  }, refresh);\n}\nexport * from \"./Options/Classes/Bounce.js\";\nexport * from \"./Options/Interfaces/IBounce.js\";", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "loadExternalBounceInteraction", "engine", "refresh", "addInteractor", "container", "Promise", "resolve"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/interaction-external-bounce/browser/index.js"], "sourcesContent": ["import { Bouncer } from \"./Bouncer.js\";\nexport async function loadExternalBounceInteraction(engine, refresh = true) {\n    await engine.addInteractor(\"externalBounce\", container => {\n        return Promise.resolve(new Bouncer(container));\n    }, refresh);\n}\nexport * from \"./Options/Classes/Bounce.js\";\nexport * from \"./Options/Interfaces/IBounce.js\";\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc;AACtC,OAAO,eAAeC,6BAA6BA,CAACC,MAAM,EAAEC,OAAO,GAAG,IAAI,EAAE;EACxE,MAAMD,MAAM,CAACE,aAAa,CAAC,gBAAgB,EAAEC,SAAS,IAAI;IACtD,OAAOC,OAAO,CAACC,OAAO,CAAC,IAAIP,OAAO,CAACK,SAAS,CAAC,CAAC;EAClD,CAAC,EAAEF,OAAO,CAAC;AACf;AACA,cAAc,6BAA6B;AAC3C,cAAc,iCAAiC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
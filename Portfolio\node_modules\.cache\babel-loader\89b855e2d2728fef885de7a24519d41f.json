{"ast": null, "code": "import { loadInteraction } from \"./interaction\";\nimport { loadPlugin } from \"./plugin\";\nexport async function loadParticlesLinksInteraction(engine) {\n  await loadInteraction(engine);\n  await loadPlugin(engine);\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Interactions/Particles/Links/index.js"], "names": ["loadInteraction", "loadPlugin", "loadParticlesLinksInteraction", "engine"], "mappings": "AAAA,SAASA,eAAT,QAAgC,eAAhC;AACA,SAASC,UAAT,QAA2B,UAA3B;AACA,OAAO,eAAeC,6BAAf,CAA6CC,MAA7C,EAAqD;AACxD,QAAMH,eAAe,CAACG,MAAD,CAArB;AACA,QAAMF,UAAU,CAACE,MAAD,CAAhB;AACH", "sourcesContent": ["import { loadInteraction } from \"./interaction\";\nimport { loadPlugin } from \"./plugin\";\nexport async function loadParticlesLinksInteraction(engine) {\n    await loadInteraction(engine);\n    await loadPlugin(engine);\n}\n"]}, "metadata": {}, "sourceType": "module"}
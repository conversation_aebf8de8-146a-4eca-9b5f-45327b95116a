{"ast": null, "code": "import { Engine } from \"./Core/Engine.js\";\nimport { HslColorManager } from \"./Utils/HslColorManager.js\";\nimport { RgbColorManager } from \"./Utils/RgbColorManager.js\";\nimport { addColorManager } from \"./Utils/ColorUtils.js\";\nexport function init() {\n  const rgbColorManager = new RgbColorManager(),\n    hslColorManager = new HslColorManager();\n  addColorManager(rgbColorManager);\n  addColorManager(hslColorManager);\n  const engine = new Engine();\n  engine.init();\n  return engine;\n}", "map": {"version": 3, "names": ["Engine", "HslColorManager", "RgbColorManager", "addColorManager", "init", "rgbColorManager", "hslColorManager", "engine"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/engine/browser/init.js"], "sourcesContent": ["import { Engine } from \"./Core/Engine.js\";\nimport { HslColorManager } from \"./Utils/HslColorManager.js\";\nimport { RgbColorManager } from \"./Utils/RgbColorManager.js\";\nimport { addColorManager } from \"./Utils/ColorUtils.js\";\nexport function init() {\n    const rgbColorManager = new RgbColorManager(), hslColorManager = new HslColorManager();\n    addColorManager(rgbColorManager);\n    addColorManager(hslColorManager);\n    const engine = new Engine();\n    engine.init();\n    return engine;\n}\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,kBAAkB;AACzC,SAASC,eAAe,QAAQ,4BAA4B;AAC5D,SAASC,eAAe,QAAQ,4BAA4B;AAC5D,SAASC,eAAe,QAAQ,uBAAuB;AACvD,OAAO,SAASC,IAAIA,CAAA,EAAG;EACnB,MAAMC,eAAe,GAAG,IAAIH,eAAe,CAAC,CAAC;IAAEI,eAAe,GAAG,IAAIL,eAAe,CAAC,CAAC;EACtFE,eAAe,CAACE,eAAe,CAAC;EAChCF,eAAe,CAACG,eAAe,CAAC;EAChC,MAAMC,MAAM,GAAG,IAAIP,MAAM,CAAC,CAAC;EAC3BO,MAAM,CAACH,IAAI,CAAC,CAAC;EACb,OAAOG,MAAM;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
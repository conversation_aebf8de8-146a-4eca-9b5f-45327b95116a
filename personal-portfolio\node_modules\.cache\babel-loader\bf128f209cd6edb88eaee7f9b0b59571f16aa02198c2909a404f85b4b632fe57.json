{"ast": null, "code": "export var RangeType;\n(function (RangeType) {\n  RangeType[\"circle\"] = \"circle\";\n  RangeType[\"rectangle\"] = \"rectangle\";\n})(RangeType || (RangeType = {}));", "map": {"version": 3, "names": ["RangeType"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/engine/browser/Types/RangeType.js"], "sourcesContent": ["export var RangeType;\n(function (RangeType) {\n    RangeType[\"circle\"] = \"circle\";\n    RangeType[\"rectangle\"] = \"rectangle\";\n})(RangeType || (RangeType = {}));\n"], "mappings": "AAAA,OAAO,IAAIA,SAAS;AACpB,CAAC,UAAUA,SAAS,EAAE;EAClBA,SAAS,CAAC,QAAQ,CAAC,GAAG,QAAQ;EAC9BA,SAAS,CAAC,WAAW,CAAC,GAAG,WAAW;AACxC,CAAC,EAAEA,SAAS,KAAKA,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
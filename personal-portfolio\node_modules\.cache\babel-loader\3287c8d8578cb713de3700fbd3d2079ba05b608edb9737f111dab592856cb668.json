{"ast": null, "code": "import { isNumber, setRangeValue } from \"@tsparticles/engine\";\nimport { WobbleSpeed } from \"./WobbleSpeed.js\";\nexport class Wobble {\n  constructor() {\n    this.distance = 5;\n    this.enable = false;\n    this.speed = new WobbleSpeed();\n  }\n  load(data) {\n    if (!data) {\n      return;\n    }\n    if (data.distance !== undefined) {\n      this.distance = setRangeValue(data.distance);\n    }\n    if (data.enable !== undefined) {\n      this.enable = data.enable;\n    }\n    if (data.speed !== undefined) {\n      if (isNumber(data.speed)) {\n        this.speed.load({\n          angle: data.speed\n        });\n      } else {\n        const rangeSpeed = data.speed;\n        if (rangeSpeed.min !== undefined) {\n          this.speed.load({\n            angle: rangeSpeed\n          });\n        } else {\n          this.speed.load(data.speed);\n        }\n      }\n    }\n  }\n}", "map": {"version": 3, "names": ["isNumber", "setRangeValue", "WobbleSpeed", "Wobble", "constructor", "distance", "enable", "speed", "load", "data", "undefined", "angle", "rangeSpeed", "min"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/updater-wobble/browser/Options/Classes/Wobble.js"], "sourcesContent": ["import { isNumber, setRangeValue, } from \"@tsparticles/engine\";\nimport { WobbleSpeed } from \"./WobbleSpeed.js\";\nexport class Wobble {\n    constructor() {\n        this.distance = 5;\n        this.enable = false;\n        this.speed = new WobbleSpeed();\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.distance !== undefined) {\n            this.distance = setRangeValue(data.distance);\n        }\n        if (data.enable !== undefined) {\n            this.enable = data.enable;\n        }\n        if (data.speed !== undefined) {\n            if (isNumber(data.speed)) {\n                this.speed.load({ angle: data.speed });\n            }\n            else {\n                const rangeSpeed = data.speed;\n                if (rangeSpeed.min !== undefined) {\n                    this.speed.load({ angle: rangeSpeed });\n                }\n                else {\n                    this.speed.load(data.speed);\n                }\n            }\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,aAAa,QAAS,qBAAqB;AAC9D,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAO,MAAMC,MAAM,CAAC;EAChBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,QAAQ,GAAG,CAAC;IACjB,IAAI,CAACC,MAAM,GAAG,KAAK;IACnB,IAAI,CAACC,KAAK,GAAG,IAAIL,WAAW,CAAC,CAAC;EAClC;EACAM,IAAIA,CAACC,IAAI,EAAE;IACP,IAAI,CAACA,IAAI,EAAE;MACP;IACJ;IACA,IAAIA,IAAI,CAACJ,QAAQ,KAAKK,SAAS,EAAE;MAC7B,IAAI,CAACL,QAAQ,GAAGJ,aAAa,CAACQ,IAAI,CAACJ,QAAQ,CAAC;IAChD;IACA,IAAII,IAAI,CAACH,MAAM,KAAKI,SAAS,EAAE;MAC3B,IAAI,CAACJ,MAAM,GAAGG,IAAI,CAACH,MAAM;IAC7B;IACA,IAAIG,IAAI,CAACF,KAAK,KAAKG,SAAS,EAAE;MAC1B,IAAIV,QAAQ,CAACS,IAAI,CAACF,KAAK,CAAC,EAAE;QACtB,IAAI,CAACA,KAAK,CAACC,IAAI,CAAC;UAAEG,KAAK,EAAEF,IAAI,CAACF;QAAM,CAAC,CAAC;MAC1C,CAAC,MACI;QACD,MAAMK,UAAU,GAAGH,IAAI,CAACF,KAAK;QAC7B,IAAIK,UAAU,CAACC,GAAG,KAAKH,SAAS,EAAE;UAC9B,IAAI,CAACH,KAAK,CAACC,IAAI,CAAC;YAAEG,KAAK,EAAEC;UAAW,CAAC,CAAC;QAC1C,CAAC,MACI;UACD,IAAI,CAACL,KAAK,CAACC,IAAI,CAACC,IAAI,CAACF,KAAK,CAAC;QAC/B;MACJ;IACJ;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
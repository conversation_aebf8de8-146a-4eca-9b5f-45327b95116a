{"ast": null, "code": "import { BackgroundMaskCover } from \"./BackgroundMaskCover\";\nexport class BackgroundMask {\n  constructor() {\n    this.composite = \"destination-out\";\n    this.cover = new BackgroundMaskCover();\n    this.enable = false;\n  }\n\n  load(data) {\n    if (data === undefined) {\n      return;\n    }\n\n    if (data.composite !== undefined) {\n      this.composite = data.composite;\n    }\n\n    if (data.cover !== undefined) {\n      const cover = data.cover;\n      const color = typeof data.cover === \"string\" ? {\n        color: data.cover\n      } : data.cover;\n      this.cover.load(cover.color !== undefined ? cover : {\n        color: color\n      });\n    }\n\n    if (data.enable !== undefined) {\n      this.enable = data.enable;\n    }\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Options/Classes/BackgroundMask/BackgroundMask.js"], "names": ["BackgroundMaskCover", "BackgroundMask", "constructor", "composite", "cover", "enable", "load", "data", "undefined", "color"], "mappings": "AAAA,SAASA,mBAAT,QAAoC,uBAApC;AACA,OAAO,MAAMC,cAAN,CAAqB;AACxBC,EAAAA,WAAW,GAAG;AACV,SAAKC,SAAL,GAAiB,iBAAjB;AACA,SAAKC,KAAL,GAAa,IAAIJ,mBAAJ,EAAb;AACA,SAAKK,MAAL,GAAc,KAAd;AACH;;AACDC,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,QAAIA,IAAI,KAAKC,SAAb,EAAwB;AACpB;AACH;;AACD,QAAID,IAAI,CAACJ,SAAL,KAAmBK,SAAvB,EAAkC;AAC9B,WAAKL,SAAL,GAAiBI,IAAI,CAACJ,SAAtB;AACH;;AACD,QAAII,IAAI,CAACH,KAAL,KAAeI,SAAnB,EAA8B;AAC1B,YAAMJ,KAAK,GAAGG,IAAI,CAACH,KAAnB;AACA,YAAMK,KAAK,GAAI,OAAOF,IAAI,CAACH,KAAZ,KAAsB,QAAtB,GAAiC;AAAEK,QAAAA,KAAK,EAAEF,IAAI,CAACH;AAAd,OAAjC,GAAyDG,IAAI,CAACH,KAA7E;AACA,WAAKA,KAAL,CAAWE,IAAX,CAAgBF,KAAK,CAACK,KAAN,KAAgBD,SAAhB,GAA4BJ,KAA5B,GAAoC;AAAEK,QAAAA,KAAK,EAAEA;AAAT,OAApD;AACH;;AACD,QAAIF,IAAI,CAACF,MAAL,KAAgBG,SAApB,EAA+B;AAC3B,WAAKH,MAAL,GAAcE,IAAI,CAACF,MAAnB;AACH;AACJ;;AArBuB", "sourcesContent": ["import { BackgroundMaskCover } from \"./BackgroundMaskCover\";\nexport class BackgroundMask {\n    constructor() {\n        this.composite = \"destination-out\";\n        this.cover = new BackgroundMaskCover();\n        this.enable = false;\n    }\n    load(data) {\n        if (data === undefined) {\n            return;\n        }\n        if (data.composite !== undefined) {\n            this.composite = data.composite;\n        }\n        if (data.cover !== undefined) {\n            const cover = data.cover;\n            const color = (typeof data.cover === \"string\" ? { color: data.cover } : data.cover);\n            this.cover.load(cover.color !== undefined ? cover : { color: color });\n        }\n        if (data.enable !== undefined) {\n            this.enable = data.enable;\n        }\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
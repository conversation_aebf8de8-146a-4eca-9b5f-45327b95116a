{"ast": null, "code": "import { AnimationOptions } from \"../../AnimationOptions\";\nexport class OpacityAnimation extends AnimationOptions {\n  constructor() {\n    super();\n    this.destroy = \"none\";\n    this.enable = false;\n    this.speed = 2;\n    this.startValue = \"random\";\n    this.sync = false;\n  }\n\n  get opacity_min() {\n    return this.minimumValue;\n  }\n\n  set opacity_min(value) {\n    this.minimumValue = value;\n  }\n\n  load(data) {\n    var _a;\n\n    if (data === undefined) {\n      return;\n    }\n\n    super.load(data);\n\n    if (data.destroy !== undefined) {\n      this.destroy = data.destroy;\n    }\n\n    if (data.enable !== undefined) {\n      this.enable = data.enable;\n    }\n\n    this.minimumValue = (_a = data.minimumValue) !== null && _a !== void 0 ? _a : data.opacity_min;\n\n    if (data.speed !== undefined) {\n      this.speed = data.speed;\n    }\n\n    if (data.startValue !== undefined) {\n      this.startValue = data.startValue;\n    }\n\n    if (data.sync !== undefined) {\n      this.sync = data.sync;\n    }\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Options/Classes/Particles/Opacity/OpacityAnimation.js"], "names": ["AnimationOptions", "OpacityAnimation", "constructor", "destroy", "enable", "speed", "startValue", "sync", "opacity_min", "minimumValue", "value", "load", "data", "_a", "undefined"], "mappings": "AAAA,SAASA,gBAAT,QAAiC,wBAAjC;AACA,OAAO,MAAMC,gBAAN,SAA+BD,gBAA/B,CAAgD;AACnDE,EAAAA,WAAW,GAAG;AACV;AACA,SAAKC,OAAL,GAAe,MAAf;AACA,SAAKC,MAAL,GAAc,KAAd;AACA,SAAKC,KAAL,GAAa,CAAb;AACA,SAAKC,UAAL,GAAkB,QAAlB;AACA,SAAKC,IAAL,GAAY,KAAZ;AACH;;AACc,MAAXC,WAAW,GAAG;AACd,WAAO,KAAKC,YAAZ;AACH;;AACc,MAAXD,WAAW,CAACE,KAAD,EAAQ;AACnB,SAAKD,YAAL,GAAoBC,KAApB;AACH;;AACDC,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,QAAIC,EAAJ;;AACA,QAAID,IAAI,KAAKE,SAAb,EAAwB;AACpB;AACH;;AACD,UAAMH,IAAN,CAAWC,IAAX;;AACA,QAAIA,IAAI,CAACT,OAAL,KAAiBW,SAArB,EAAgC;AAC5B,WAAKX,OAAL,GAAeS,IAAI,CAACT,OAApB;AACH;;AACD,QAAIS,IAAI,CAACR,MAAL,KAAgBU,SAApB,EAA+B;AAC3B,WAAKV,MAAL,GAAcQ,IAAI,CAACR,MAAnB;AACH;;AACD,SAAKK,YAAL,GAAoB,CAACI,EAAE,GAAGD,IAAI,CAACH,YAAX,MAA6B,IAA7B,IAAqCI,EAAE,KAAK,KAAK,CAAjD,GAAqDA,EAArD,GAA0DD,IAAI,CAACJ,WAAnF;;AACA,QAAII,IAAI,CAACP,KAAL,KAAeS,SAAnB,EAA8B;AAC1B,WAAKT,KAAL,GAAaO,IAAI,CAACP,KAAlB;AACH;;AACD,QAAIO,IAAI,CAACN,UAAL,KAAoBQ,SAAxB,EAAmC;AAC/B,WAAKR,UAAL,GAAkBM,IAAI,CAACN,UAAvB;AACH;;AACD,QAAIM,IAAI,CAACL,IAAL,KAAcO,SAAlB,EAA6B;AACzB,WAAKP,IAAL,GAAYK,IAAI,CAACL,IAAjB;AACH;AACJ;;AArCkD", "sourcesContent": ["import { AnimationOptions } from \"../../AnimationOptions\";\nexport class OpacityAnimation extends AnimationOptions {\n    constructor() {\n        super();\n        this.destroy = \"none\";\n        this.enable = false;\n        this.speed = 2;\n        this.startValue = \"random\";\n        this.sync = false;\n    }\n    get opacity_min() {\n        return this.minimumValue;\n    }\n    set opacity_min(value) {\n        this.minimumValue = value;\n    }\n    load(data) {\n        var _a;\n        if (data === undefined) {\n            return;\n        }\n        super.load(data);\n        if (data.destroy !== undefined) {\n            this.destroy = data.destroy;\n        }\n        if (data.enable !== undefined) {\n            this.enable = data.enable;\n        }\n        this.minimumValue = (_a = data.minimumValue) !== null && _a !== void 0 ? _a : data.opacity_min;\n        if (data.speed !== undefined) {\n            this.speed = data.speed;\n        }\n        if (data.startValue !== undefined) {\n            this.startValue = data.startValue;\n        }\n        if (data.sync !== undefined) {\n            this.sync = data.sync;\n        }\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
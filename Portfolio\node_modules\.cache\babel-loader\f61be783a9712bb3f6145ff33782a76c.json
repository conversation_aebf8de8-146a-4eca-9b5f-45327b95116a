{"ast": null, "code": "import { ExternalInteractorBase } from \"../../../Core\";\nimport { isInArray } from \"../../../Utils\";\nexport class TrailMaker extends ExternalInteractorBase {\n  constructor(container) {\n    super(container);\n    this.delay = 0;\n  }\n\n  async interact(delta) {\n    var _a, _b, _c, _d;\n\n    if (!this.container.retina.reduceFactor) {\n      return;\n    }\n\n    const container = this.container,\n          options = container.actualOptions,\n          trailOptions = options.interactivity.modes.trail,\n          optDelay = trailOptions.delay * 1000 / this.container.retina.reduceFactor;\n\n    if (this.delay < optDelay) {\n      this.delay += delta.value;\n    }\n\n    if (this.delay < optDelay) {\n      return;\n    }\n\n    let canEmit = true;\n\n    if (trailOptions.pauseOnStop) {\n      if (container.interactivity.mouse.position === this.lastPosition || ((_a = container.interactivity.mouse.position) === null || _a === void 0 ? void 0 : _a.x) === ((_b = this.lastPosition) === null || _b === void 0 ? void 0 : _b.x) && ((_c = container.interactivity.mouse.position) === null || _c === void 0 ? void 0 : _c.y) === ((_d = this.lastPosition) === null || _d === void 0 ? void 0 : _d.y)) {\n        canEmit = false;\n      }\n    }\n\n    if (container.interactivity.mouse.position) {\n      this.lastPosition = {\n        x: container.interactivity.mouse.position.x,\n        y: container.interactivity.mouse.position.y\n      };\n    } else {\n      delete this.lastPosition;\n    }\n\n    if (canEmit) {\n      container.particles.push(trailOptions.quantity, container.interactivity.mouse, trailOptions.particles);\n    }\n\n    this.delay -= optDelay;\n  }\n\n  isEnabled() {\n    const container = this.container,\n          options = container.actualOptions,\n          mouse = container.interactivity.mouse,\n          events = options.interactivity.events;\n    return mouse.clicking && mouse.inside && !!mouse.position && isInArray(\"trail\", events.onClick.mode) || mouse.inside && !!mouse.position && isInArray(\"trail\", events.onHover.mode);\n  }\n\n  reset() {}\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Interactions/External/Trail/TrailMaker.js"], "names": ["ExternalInteractorBase", "isInArray", "TrailMaker", "constructor", "container", "delay", "interact", "delta", "_a", "_b", "_c", "_d", "retina", "reduceFactor", "options", "actualOptions", "trailOptions", "interactivity", "modes", "trail", "optDelay", "value", "canEmit", "pauseOnStop", "mouse", "position", "lastPosition", "x", "y", "particles", "push", "quantity", "isEnabled", "events", "clicking", "inside", "onClick", "mode", "onHover", "reset"], "mappings": "AAAA,SAASA,sBAAT,QAAuC,eAAvC;AACA,SAASC,SAAT,QAA0B,gBAA1B;AACA,OAAO,MAAMC,UAAN,SAAyBF,sBAAzB,CAAgD;AACnDG,EAAAA,WAAW,CAACC,SAAD,EAAY;AACnB,UAAMA,SAAN;AACA,SAAKC,KAAL,GAAa,CAAb;AACH;;AACa,QAARC,QAAQ,CAACC,KAAD,EAAQ;AAClB,QAAIC,EAAJ,EAAQC,EAAR,EAAYC,EAAZ,EAAgBC,EAAhB;;AACA,QAAI,CAAC,KAAKP,SAAL,CAAeQ,MAAf,CAAsBC,YAA3B,EAAyC;AACrC;AACH;;AACD,UAAMT,SAAS,GAAG,KAAKA,SAAvB;AAAA,UAAkCU,OAAO,GAAGV,SAAS,CAACW,aAAtD;AAAA,UAAqEC,YAAY,GAAGF,OAAO,CAACG,aAAR,CAAsBC,KAAtB,CAA4BC,KAAhH;AAAA,UAAuHC,QAAQ,GAAIJ,YAAY,CAACX,KAAb,GAAqB,IAAtB,GAA8B,KAAKD,SAAL,CAAeQ,MAAf,CAAsBC,YAAtL;;AACA,QAAI,KAAKR,KAAL,GAAae,QAAjB,EAA2B;AACvB,WAAKf,KAAL,IAAcE,KAAK,CAACc,KAApB;AACH;;AACD,QAAI,KAAKhB,KAAL,GAAae,QAAjB,EAA2B;AACvB;AACH;;AACD,QAAIE,OAAO,GAAG,IAAd;;AACA,QAAIN,YAAY,CAACO,WAAjB,EAA8B;AAC1B,UAAInB,SAAS,CAACa,aAAV,CAAwBO,KAAxB,CAA8BC,QAA9B,KAA2C,KAAKC,YAAhD,IACC,CAAC,CAAClB,EAAE,GAAGJ,SAAS,CAACa,aAAV,CAAwBO,KAAxB,CAA8BC,QAApC,MAAkD,IAAlD,IAA0DjB,EAAE,KAAK,KAAK,CAAtE,GAA0E,KAAK,CAA/E,GAAmFA,EAAE,CAACmB,CAAvF,OAA+F,CAAClB,EAAE,GAAG,KAAKiB,YAAX,MAA6B,IAA7B,IAAqCjB,EAAE,KAAK,KAAK,CAAjD,GAAqD,KAAK,CAA1D,GAA8DA,EAAE,CAACkB,CAAhK,KACG,CAAC,CAACjB,EAAE,GAAGN,SAAS,CAACa,aAAV,CAAwBO,KAAxB,CAA8BC,QAApC,MAAkD,IAAlD,IAA0Df,EAAE,KAAK,KAAK,CAAtE,GAA0E,KAAK,CAA/E,GAAmFA,EAAE,CAACkB,CAAvF,OAA+F,CAACjB,EAAE,GAAG,KAAKe,YAAX,MAA6B,IAA7B,IAAqCf,EAAE,KAAK,KAAK,CAAjD,GAAqD,KAAK,CAA1D,GAA8DA,EAAE,CAACiB,CAAhK,CAFR,EAE6K;AACzKN,QAAAA,OAAO,GAAG,KAAV;AACH;AACJ;;AACD,QAAIlB,SAAS,CAACa,aAAV,CAAwBO,KAAxB,CAA8BC,QAAlC,EAA4C;AACxC,WAAKC,YAAL,GAAoB;AAChBC,QAAAA,CAAC,EAAEvB,SAAS,CAACa,aAAV,CAAwBO,KAAxB,CAA8BC,QAA9B,CAAuCE,CAD1B;AAEhBC,QAAAA,CAAC,EAAExB,SAAS,CAACa,aAAV,CAAwBO,KAAxB,CAA8BC,QAA9B,CAAuCG;AAF1B,OAApB;AAIH,KALD,MAMK;AACD,aAAO,KAAKF,YAAZ;AACH;;AACD,QAAIJ,OAAJ,EAAa;AACTlB,MAAAA,SAAS,CAACyB,SAAV,CAAoBC,IAApB,CAAyBd,YAAY,CAACe,QAAtC,EAAgD3B,SAAS,CAACa,aAAV,CAAwBO,KAAxE,EAA+ER,YAAY,CAACa,SAA5F;AACH;;AACD,SAAKxB,KAAL,IAAce,QAAd;AACH;;AACDY,EAAAA,SAAS,GAAG;AACR,UAAM5B,SAAS,GAAG,KAAKA,SAAvB;AAAA,UAAkCU,OAAO,GAAGV,SAAS,CAACW,aAAtD;AAAA,UAAqES,KAAK,GAAGpB,SAAS,CAACa,aAAV,CAAwBO,KAArG;AAAA,UAA4GS,MAAM,GAAGnB,OAAO,CAACG,aAAR,CAAsBgB,MAA3I;AACA,WAAST,KAAK,CAACU,QAAN,IAAkBV,KAAK,CAACW,MAAxB,IAAkC,CAAC,CAACX,KAAK,CAACC,QAA1C,IAAsDxB,SAAS,CAAC,OAAD,EAAUgC,MAAM,CAACG,OAAP,CAAeC,IAAzB,CAAhE,IACHb,KAAK,CAACW,MAAN,IAAgB,CAAC,CAACX,KAAK,CAACC,QAAxB,IAAoCxB,SAAS,CAAC,OAAD,EAAUgC,MAAM,CAACK,OAAP,CAAeD,IAAzB,CADlD;AAEH;;AACDE,EAAAA,KAAK,GAAG,CACP;;AA7CkD", "sourcesContent": ["import { ExternalInteractorBase } from \"../../../Core\";\nimport { isInArray } from \"../../../Utils\";\nexport class TrailMaker extends ExternalInteractorBase {\n    constructor(container) {\n        super(container);\n        this.delay = 0;\n    }\n    async interact(delta) {\n        var _a, _b, _c, _d;\n        if (!this.container.retina.reduceFactor) {\n            return;\n        }\n        const container = this.container, options = container.actualOptions, trailOptions = options.interactivity.modes.trail, optDelay = (trailOptions.delay * 1000) / this.container.retina.reduceFactor;\n        if (this.delay < optDelay) {\n            this.delay += delta.value;\n        }\n        if (this.delay < optDelay) {\n            return;\n        }\n        let canEmit = true;\n        if (trailOptions.pauseOnStop) {\n            if (container.interactivity.mouse.position === this.lastPosition ||\n                (((_a = container.interactivity.mouse.position) === null || _a === void 0 ? void 0 : _a.x) === ((_b = this.lastPosition) === null || _b === void 0 ? void 0 : _b.x) &&\n                    ((_c = container.interactivity.mouse.position) === null || _c === void 0 ? void 0 : _c.y) === ((_d = this.lastPosition) === null || _d === void 0 ? void 0 : _d.y))) {\n                canEmit = false;\n            }\n        }\n        if (container.interactivity.mouse.position) {\n            this.lastPosition = {\n                x: container.interactivity.mouse.position.x,\n                y: container.interactivity.mouse.position.y,\n            };\n        }\n        else {\n            delete this.lastPosition;\n        }\n        if (canEmit) {\n            container.particles.push(trailOptions.quantity, container.interactivity.mouse, trailOptions.particles);\n        }\n        this.delay -= optDelay;\n    }\n    isEnabled() {\n        const container = this.container, options = container.actualOptions, mouse = container.interactivity.mouse, events = options.interactivity.events;\n        return ((mouse.clicking && mouse.inside && !!mouse.position && isInArray(\"trail\", events.onClick.mode)) ||\n            (mouse.inside && !!mouse.position && isInArray(\"trail\", events.onHover.mode)));\n    }\n    reset() {\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
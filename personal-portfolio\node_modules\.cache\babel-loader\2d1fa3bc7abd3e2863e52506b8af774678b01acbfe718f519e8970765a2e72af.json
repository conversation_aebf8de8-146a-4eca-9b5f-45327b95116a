{"ast": null, "code": "export var RollMode;\n(function (RollMode) {\n  RollMode[\"both\"] = \"both\";\n  RollMode[\"horizontal\"] = \"horizontal\";\n  RollMode[\"vertical\"] = \"vertical\";\n})(RollMode || (RollMode = {}));", "map": {"version": 3, "names": ["RollMode"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/updater-roll/browser/RollMode.js"], "sourcesContent": ["export var RollMode;\n(function (RollMode) {\n    RollMode[\"both\"] = \"both\";\n    RollMode[\"horizontal\"] = \"horizontal\";\n    RollMode[\"vertical\"] = \"vertical\";\n})(RollMode || (RollMode = {}));\n"], "mappings": "AAAA,OAAO,IAAIA,QAAQ;AACnB,CAAC,UAAUA,QAAQ,EAAE;EACjBA,QAAQ,CAAC,MAAM,CAAC,GAAG,MAAM;EACzBA,QAAQ,CAAC,YAAY,CAAC,GAAG,YAAY;EACrCA,QAAQ,CAAC,UAAU,CAAC,GAAG,UAAU;AACrC,CAAC,EAAEA,QAAQ,KAAKA,QAAQ,GAAG,CAAC,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
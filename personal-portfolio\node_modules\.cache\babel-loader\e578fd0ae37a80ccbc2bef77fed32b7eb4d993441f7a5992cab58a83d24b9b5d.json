{"ast": null, "code": "import { initParticle, updateRoll } from \"./Utils.js\";\nimport { Roll } from \"./Options/Classes/Roll.js\";\nexport class RollUpdater {\n  getTransformValues(particle) {\n    const roll = particle.roll?.enable && particle.roll,\n      rollHorizontal = roll && roll.horizontal,\n      rollVertical = roll && roll.vertical;\n    return {\n      a: rollHorizontal ? Math.cos(roll.angle) : undefined,\n      d: rollVertical ? Math.sin(roll.angle) : undefined\n    };\n  }\n  init(particle) {\n    initParticle(particle);\n  }\n  isEnabled(particle) {\n    const roll = particle.options.roll;\n    return !particle.destroyed && !particle.spawning && !!roll?.enable;\n  }\n  loadOptions(options, ...sources) {\n    if (!options.roll) {\n      options.roll = new Roll();\n    }\n    for (const source of sources) {\n      options.roll.load(source?.roll);\n    }\n  }\n  update(particle, delta) {\n    if (!this.isEnabled(particle)) {\n      return;\n    }\n    updateRoll(particle, delta);\n  }\n}", "map": {"version": 3, "names": ["initParticle", "updateRoll", "Roll", "RollUpdater", "getTransformValues", "particle", "roll", "enable", "rollHorizontal", "horizontal", "rollVertical", "vertical", "a", "Math", "cos", "angle", "undefined", "d", "sin", "init", "isEnabled", "options", "destroyed", "spawning", "loadOptions", "sources", "source", "load", "update", "delta"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/updater-roll/browser/RollUpdater.js"], "sourcesContent": ["import { initParticle, updateRoll } from \"./Utils.js\";\nimport { Roll } from \"./Options/Classes/Roll.js\";\nexport class RollUpdater {\n    getTransformValues(particle) {\n        const roll = particle.roll?.enable && particle.roll, rollHorizontal = roll && roll.horizontal, rollVertical = roll && roll.vertical;\n        return {\n            a: rollHorizontal ? Math.cos(roll.angle) : undefined,\n            d: rollVertical ? Math.sin(roll.angle) : undefined,\n        };\n    }\n    init(particle) {\n        initParticle(particle);\n    }\n    isEnabled(particle) {\n        const roll = particle.options.roll;\n        return !particle.destroyed && !particle.spawning && !!roll?.enable;\n    }\n    loadOptions(options, ...sources) {\n        if (!options.roll) {\n            options.roll = new Roll();\n        }\n        for (const source of sources) {\n            options.roll.load(source?.roll);\n        }\n    }\n    update(particle, delta) {\n        if (!this.isEnabled(particle)) {\n            return;\n        }\n        updateRoll(particle, delta);\n    }\n}\n"], "mappings": "AAAA,SAASA,YAAY,EAAEC,UAAU,QAAQ,YAAY;AACrD,SAASC,IAAI,QAAQ,2BAA2B;AAChD,OAAO,MAAMC,WAAW,CAAC;EACrBC,kBAAkBA,CAACC,QAAQ,EAAE;IACzB,MAAMC,IAAI,GAAGD,QAAQ,CAACC,IAAI,EAAEC,MAAM,IAAIF,QAAQ,CAACC,IAAI;MAAEE,cAAc,GAAGF,IAAI,IAAIA,IAAI,CAACG,UAAU;MAAEC,YAAY,GAAGJ,IAAI,IAAIA,IAAI,CAACK,QAAQ;IACnI,OAAO;MACHC,CAAC,EAAEJ,cAAc,GAAGK,IAAI,CAACC,GAAG,CAACR,IAAI,CAACS,KAAK,CAAC,GAAGC,SAAS;MACpDC,CAAC,EAAEP,YAAY,GAAGG,IAAI,CAACK,GAAG,CAACZ,IAAI,CAACS,KAAK,CAAC,GAAGC;IAC7C,CAAC;EACL;EACAG,IAAIA,CAACd,QAAQ,EAAE;IACXL,YAAY,CAACK,QAAQ,CAAC;EAC1B;EACAe,SAASA,CAACf,QAAQ,EAAE;IAChB,MAAMC,IAAI,GAAGD,QAAQ,CAACgB,OAAO,CAACf,IAAI;IAClC,OAAO,CAACD,QAAQ,CAACiB,SAAS,IAAI,CAACjB,QAAQ,CAACkB,QAAQ,IAAI,CAAC,CAACjB,IAAI,EAAEC,MAAM;EACtE;EACAiB,WAAWA,CAACH,OAAO,EAAE,GAAGI,OAAO,EAAE;IAC7B,IAAI,CAACJ,OAAO,CAACf,IAAI,EAAE;MACfe,OAAO,CAACf,IAAI,GAAG,IAAIJ,IAAI,CAAC,CAAC;IAC7B;IACA,KAAK,MAAMwB,MAAM,IAAID,OAAO,EAAE;MAC1BJ,OAAO,CAACf,IAAI,CAACqB,IAAI,CAACD,MAAM,EAAEpB,IAAI,CAAC;IACnC;EACJ;EACAsB,MAAMA,CAACvB,QAAQ,EAAEwB,KAAK,EAAE;IACpB,IAAI,CAAC,IAAI,CAACT,SAAS,CAACf,QAAQ,CAAC,EAAE;MAC3B;IACJ;IACAJ,UAAU,CAACI,QAAQ,EAAEwB,KAAK,CAAC;EAC/B;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
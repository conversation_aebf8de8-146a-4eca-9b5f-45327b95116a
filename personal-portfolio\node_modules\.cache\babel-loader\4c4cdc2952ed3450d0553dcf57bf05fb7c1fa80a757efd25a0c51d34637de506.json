{"ast": null, "code": "import { OptionsColor, deepExtend, executeOnSingleOrMultiple } from \"@tsparticles/engine\";\nimport { SplitFactor } from \"./SplitFactor.js\";\nimport { SplitRate } from \"./SplitRate.js\";\nexport class Split {\n  constructor() {\n    this.count = 1;\n    this.factor = new SplitFactor();\n    this.rate = new SplitRate();\n    this.sizeOffset = true;\n  }\n  load(data) {\n    if (!data) {\n      return;\n    }\n    if (data.color !== undefined) {\n      this.color = OptionsColor.create(this.color, data.color);\n    }\n    if (data.count !== undefined) {\n      this.count = data.count;\n    }\n    this.factor.load(data.factor);\n    this.rate.load(data.rate);\n    this.particles = executeOnSingleOrMultiple(data.particles, particles => {\n      return deepExtend({}, particles);\n    });\n    if (data.sizeOffset !== undefined) {\n      this.sizeOffset = data.sizeOffset;\n    }\n    if (data.colorOffset) {\n      this.colorOffset = this.colorOffset ?? {};\n      if (data.colorOffset.h !== undefined) {\n        this.colorOffset.h = data.colorOffset.h;\n      }\n      if (data.colorOffset.s !== undefined) {\n        this.colorOffset.s = data.colorOffset.s;\n      }\n      if (data.colorOffset.l !== undefined) {\n        this.colorOffset.l = data.colorOffset.l;\n      }\n    }\n  }\n}", "map": {"version": 3, "names": ["OptionsColor", "deepExtend", "executeOnSingleOrMultiple", "SplitFactor", "SplitRate", "Split", "constructor", "count", "factor", "rate", "sizeOffset", "load", "data", "color", "undefined", "create", "particles", "colorOffset", "h", "s", "l"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/updater-destroy/browser/Options/Classes/Split.js"], "sourcesContent": ["import { OptionsColor, deepExtend, executeOnSingleOrMultiple, } from \"@tsparticles/engine\";\nimport { SplitFactor } from \"./SplitFactor.js\";\nimport { SplitRate } from \"./SplitRate.js\";\nexport class Split {\n    constructor() {\n        this.count = 1;\n        this.factor = new SplitFactor();\n        this.rate = new SplitRate();\n        this.sizeOffset = true;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.color !== undefined) {\n            this.color = OptionsColor.create(this.color, data.color);\n        }\n        if (data.count !== undefined) {\n            this.count = data.count;\n        }\n        this.factor.load(data.factor);\n        this.rate.load(data.rate);\n        this.particles = executeOnSingleOrMultiple(data.particles, particles => {\n            return deepExtend({}, particles);\n        });\n        if (data.sizeOffset !== undefined) {\n            this.sizeOffset = data.sizeOffset;\n        }\n        if (data.colorOffset) {\n            this.colorOffset = this.colorOffset ?? {};\n            if (data.colorOffset.h !== undefined) {\n                this.colorOffset.h = data.colorOffset.h;\n            }\n            if (data.colorOffset.s !== undefined) {\n                this.colorOffset.s = data.colorOffset.s;\n            }\n            if (data.colorOffset.l !== undefined) {\n                this.colorOffset.l = data.colorOffset.l;\n            }\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,YAAY,EAAEC,UAAU,EAAEC,yBAAyB,QAAS,qBAAqB;AAC1F,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,OAAO,MAAMC,KAAK,CAAC;EACfC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,KAAK,GAAG,CAAC;IACd,IAAI,CAACC,MAAM,GAAG,IAAIL,WAAW,CAAC,CAAC;IAC/B,IAAI,CAACM,IAAI,GAAG,IAAIL,SAAS,CAAC,CAAC;IAC3B,IAAI,CAACM,UAAU,GAAG,IAAI;EAC1B;EACAC,IAAIA,CAACC,IAAI,EAAE;IACP,IAAI,CAACA,IAAI,EAAE;MACP;IACJ;IACA,IAAIA,IAAI,CAACC,KAAK,KAAKC,SAAS,EAAE;MAC1B,IAAI,CAACD,KAAK,GAAGb,YAAY,CAACe,MAAM,CAAC,IAAI,CAACF,KAAK,EAAED,IAAI,CAACC,KAAK,CAAC;IAC5D;IACA,IAAID,IAAI,CAACL,KAAK,KAAKO,SAAS,EAAE;MAC1B,IAAI,CAACP,KAAK,GAAGK,IAAI,CAACL,KAAK;IAC3B;IACA,IAAI,CAACC,MAAM,CAACG,IAAI,CAACC,IAAI,CAACJ,MAAM,CAAC;IAC7B,IAAI,CAACC,IAAI,CAACE,IAAI,CAACC,IAAI,CAACH,IAAI,CAAC;IACzB,IAAI,CAACO,SAAS,GAAGd,yBAAyB,CAACU,IAAI,CAACI,SAAS,EAAEA,SAAS,IAAI;MACpE,OAAOf,UAAU,CAAC,CAAC,CAAC,EAAEe,SAAS,CAAC;IACpC,CAAC,CAAC;IACF,IAAIJ,IAAI,CAACF,UAAU,KAAKI,SAAS,EAAE;MAC/B,IAAI,CAACJ,UAAU,GAAGE,IAAI,CAACF,UAAU;IACrC;IACA,IAAIE,IAAI,CAACK,WAAW,EAAE;MAClB,IAAI,CAACA,WAAW,GAAG,IAAI,CAACA,WAAW,IAAI,CAAC,CAAC;MACzC,IAAIL,IAAI,CAACK,WAAW,CAACC,CAAC,KAAKJ,SAAS,EAAE;QAClC,IAAI,CAACG,WAAW,CAACC,CAAC,GAAGN,IAAI,CAACK,WAAW,CAACC,CAAC;MAC3C;MACA,IAAIN,IAAI,CAACK,WAAW,CAACE,CAAC,KAAKL,SAAS,EAAE;QAClC,IAAI,CAACG,WAAW,CAACE,CAAC,GAAGP,IAAI,CAACK,WAAW,CAACE,CAAC;MAC3C;MACA,IAAIP,IAAI,CAACK,WAAW,CAACG,CAAC,KAAKN,SAAS,EAAE;QAClC,IAAI,CAACG,WAAW,CAACG,CAAC,GAAGR,IAAI,CAACK,WAAW,CAACG,CAAC;MAC3C;IACJ;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
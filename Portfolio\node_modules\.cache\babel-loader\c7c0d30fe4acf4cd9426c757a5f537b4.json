{"ast": null, "code": "import { ValueWithRandom } from \"../../../ValueWithRandom\";\nexport class PathDelay extends ValueWithRandom {\n  constructor() {\n    super();\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Options/Classes/Particles/Move/Path/PathDelay.js"], "names": ["ValueWithRandom", "PathDelay", "constructor"], "mappings": "AAAA,SAASA,eAAT,QAAgC,0BAAhC;AACA,OAAO,MAAMC,SAAN,SAAwBD,eAAxB,CAAwC;AAC3CE,EAAAA,WAAW,GAAG;AACV;AACH;;AAH0C", "sourcesContent": ["import { ValueWithRandom } from \"../../../ValueWithRandom\";\nexport class PathDelay extends ValueWithRandom {\n    constructor() {\n        super();\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
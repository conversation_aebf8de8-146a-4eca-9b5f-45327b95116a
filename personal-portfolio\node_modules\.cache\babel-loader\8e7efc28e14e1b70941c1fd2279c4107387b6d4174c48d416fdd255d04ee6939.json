{"ast": null, "code": "import { ValueWithRandom } from \"@tsparticles/engine\";\nimport { TiltDirection } from \"../../TiltDirection.js\";\nimport { TiltAnimation } from \"./TiltAnimation.js\";\nexport class Tilt extends ValueWithRandom {\n  constructor() {\n    super();\n    this.animation = new TiltAnimation();\n    this.direction = TiltDirection.clockwise;\n    this.enable = false;\n    this.value = 0;\n  }\n  load(data) {\n    super.load(data);\n    if (!data) {\n      return;\n    }\n    this.animation.load(data.animation);\n    if (data.direction !== undefined) {\n      this.direction = data.direction;\n    }\n    if (data.enable !== undefined) {\n      this.enable = data.enable;\n    }\n  }\n}", "map": {"version": 3, "names": ["ValueWithRandom", "TiltDirection", "TiltAnimation", "Tilt", "constructor", "animation", "direction", "clockwise", "enable", "value", "load", "data", "undefined"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/updater-tilt/browser/Options/Classes/Tilt.js"], "sourcesContent": ["import { ValueWithRandom } from \"@tsparticles/engine\";\nimport { TiltDirection } from \"../../TiltDirection.js\";\nimport { TiltAnimation } from \"./TiltAnimation.js\";\nexport class Tilt extends ValueWithRandom {\n    constructor() {\n        super();\n        this.animation = new TiltAnimation();\n        this.direction = TiltDirection.clockwise;\n        this.enable = false;\n        this.value = 0;\n    }\n    load(data) {\n        super.load(data);\n        if (!data) {\n            return;\n        }\n        this.animation.load(data.animation);\n        if (data.direction !== undefined) {\n            this.direction = data.direction;\n        }\n        if (data.enable !== undefined) {\n            this.enable = data.enable;\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,qBAAqB;AACrD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,oBAAoB;AAClD,OAAO,MAAMC,IAAI,SAASH,eAAe,CAAC;EACtCI,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,SAAS,GAAG,IAAIH,aAAa,CAAC,CAAC;IACpC,IAAI,CAACI,SAAS,GAAGL,aAAa,CAACM,SAAS;IACxC,IAAI,CAACC,MAAM,GAAG,KAAK;IACnB,IAAI,CAACC,KAAK,GAAG,CAAC;EAClB;EACAC,IAAIA,CAACC,IAAI,EAAE;IACP,KAAK,CAACD,IAAI,CAACC,IAAI,CAAC;IAChB,IAAI,CAACA,IAAI,EAAE;MACP;IACJ;IACA,IAAI,CAACN,SAAS,CAACK,IAAI,CAACC,IAAI,CAACN,SAAS,CAAC;IACnC,IAAIM,IAAI,CAACL,SAAS,KAAKM,SAAS,EAAE;MAC9B,IAAI,CAACN,SAAS,GAAGK,IAAI,CAACL,SAAS;IACnC;IACA,IAAIK,IAAI,CAACH,MAAM,KAAKI,SAAS,EAAE;MAC3B,IAAI,CAACJ,MAAM,GAAGG,IAAI,CAACH,MAAM;IAC7B;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
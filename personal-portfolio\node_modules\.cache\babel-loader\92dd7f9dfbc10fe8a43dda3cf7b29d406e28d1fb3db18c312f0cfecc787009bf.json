{"ast": null, "code": "import { BackgroundMaskCover } from \"./BackgroundMaskCover.js\";\nimport { isString } from \"../../../Utils/TypeUtils.js\";\nexport class BackgroundMask {\n  constructor() {\n    this.composite = \"destination-out\";\n    this.cover = new BackgroundMaskCover();\n    this.enable = false;\n  }\n  load(data) {\n    if (!data) {\n      return;\n    }\n    if (data.composite !== undefined) {\n      this.composite = data.composite;\n    }\n    if (data.cover !== undefined) {\n      const cover = data.cover,\n        color = isString(data.cover) ? {\n          color: data.cover\n        } : data.cover;\n      this.cover.load(cover.color !== undefined || cover.image !== undefined ? cover : {\n        color: color\n      });\n    }\n    if (data.enable !== undefined) {\n      this.enable = data.enable;\n    }\n  }\n}", "map": {"version": 3, "names": ["BackgroundMaskCover", "isString", "BackgroundMask", "constructor", "composite", "cover", "enable", "load", "data", "undefined", "color", "image"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/engine/browser/Options/Classes/BackgroundMask/BackgroundMask.js"], "sourcesContent": ["import { BackgroundMaskCover } from \"./BackgroundMaskCover.js\";\nimport { isString } from \"../../../Utils/TypeUtils.js\";\nexport class BackgroundMask {\n    constructor() {\n        this.composite = \"destination-out\";\n        this.cover = new BackgroundMaskCover();\n        this.enable = false;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.composite !== undefined) {\n            this.composite = data.composite;\n        }\n        if (data.cover !== undefined) {\n            const cover = data.cover, color = (isString(data.cover) ? { color: data.cover } : data.cover);\n            this.cover.load(cover.color !== undefined || cover.image !== undefined ? cover : { color: color });\n        }\n        if (data.enable !== undefined) {\n            this.enable = data.enable;\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,mBAAmB,QAAQ,0BAA0B;AAC9D,SAASC,QAAQ,QAAQ,6BAA6B;AACtD,OAAO,MAAMC,cAAc,CAAC;EACxBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,SAAS,GAAG,iBAAiB;IAClC,IAAI,CAACC,KAAK,GAAG,IAAIL,mBAAmB,CAAC,CAAC;IACtC,IAAI,CAACM,MAAM,GAAG,KAAK;EACvB;EACAC,IAAIA,CAACC,IAAI,EAAE;IACP,IAAI,CAACA,IAAI,EAAE;MACP;IACJ;IACA,IAAIA,IAAI,CAACJ,SAAS,KAAKK,SAAS,EAAE;MAC9B,IAAI,CAACL,SAAS,GAAGI,IAAI,CAACJ,SAAS;IACnC;IACA,IAAII,IAAI,CAACH,KAAK,KAAKI,SAAS,EAAE;MAC1B,MAAMJ,KAAK,GAAGG,IAAI,CAACH,KAAK;QAAEK,KAAK,GAAIT,QAAQ,CAACO,IAAI,CAACH,KAAK,CAAC,GAAG;UAAEK,KAAK,EAAEF,IAAI,CAACH;QAAM,CAAC,GAAGG,IAAI,CAACH,KAAM;MAC7F,IAAI,CAACA,KAAK,CAACE,IAAI,CAACF,KAAK,CAACK,KAAK,KAAKD,SAAS,IAAIJ,KAAK,CAACM,KAAK,KAAKF,SAAS,GAAGJ,KAAK,GAAG;QAAEK,KAAK,EAAEA;MAAM,CAAC,CAAC;IACtG;IACA,IAAIF,IAAI,CAACF,MAAM,KAAKG,SAAS,EAAE;MAC3B,IAAI,CAACH,MAAM,GAAGE,IAAI,CAACF,MAAM;IAC7B;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { OptionsColor } from \"../../OptionsColor\";\nexport class GrabLinks {\n  constructor() {\n    this.blink = false;\n    this.consent = false;\n    this.opacity = 1;\n  }\n\n  load(data) {\n    if (data === undefined) {\n      return;\n    }\n\n    if (data.blink !== undefined) {\n      this.blink = data.blink;\n    }\n\n    if (data.color !== undefined) {\n      this.color = OptionsColor.create(this.color, data.color);\n    }\n\n    if (data.consent !== undefined) {\n      this.consent = data.consent;\n    }\n\n    if (data.opacity !== undefined) {\n      this.opacity = data.opacity;\n    }\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Options/Classes/Interactivity/Modes/GrabLinks.js"], "names": ["OptionsColor", "GrabLinks", "constructor", "blink", "consent", "opacity", "load", "data", "undefined", "color", "create"], "mappings": "AAAA,SAASA,YAAT,QAA6B,oBAA7B;AACA,OAAO,MAAMC,SAAN,CAAgB;AACnBC,EAAAA,WAAW,GAAG;AACV,SAAKC,KAAL,GAAa,KAAb;AACA,SAAKC,OAAL,GAAe,KAAf;AACA,SAAKC,OAAL,GAAe,CAAf;AACH;;AACDC,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,QAAIA,IAAI,KAAKC,SAAb,EAAwB;AACpB;AACH;;AACD,QAAID,IAAI,CAACJ,KAAL,KAAeK,SAAnB,EAA8B;AAC1B,WAAKL,KAAL,GAAaI,IAAI,CAACJ,KAAlB;AACH;;AACD,QAAII,IAAI,CAACE,KAAL,KAAeD,SAAnB,EAA8B;AAC1B,WAAKC,KAAL,GAAaT,YAAY,CAACU,MAAb,CAAoB,KAAKD,KAAzB,EAAgCF,IAAI,CAACE,KAArC,CAAb;AACH;;AACD,QAAIF,IAAI,CAACH,OAAL,KAAiBI,SAArB,EAAgC;AAC5B,WAAKJ,OAAL,GAAeG,IAAI,CAACH,OAApB;AACH;;AACD,QAAIG,IAAI,CAACF,OAAL,KAAiBG,SAArB,EAAgC;AAC5B,WAAKH,OAAL,GAAeE,IAAI,CAACF,OAApB;AACH;AACJ;;AAtBkB", "sourcesContent": ["import { OptionsColor } from \"../../OptionsColor\";\nexport class GrabLinks {\n    constructor() {\n        this.blink = false;\n        this.consent = false;\n        this.opacity = 1;\n    }\n    load(data) {\n        if (data === undefined) {\n            return;\n        }\n        if (data.blink !== undefined) {\n            this.blink = data.blink;\n        }\n        if (data.color !== undefined) {\n            this.color = OptionsColor.create(this.color, data.color);\n        }\n        if (data.consent !== undefined) {\n            this.consent = data.consent;\n        }\n        if (data.opacity !== undefined) {\n            this.opacity = data.opacity;\n        }\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
{"ast": null, "code": "import { Preload } from \"./Options/Classes/Preload\";\nexport class ImagePreloaderPlugin {\n  constructor(engine) {\n    this.id = \"imagePreloader\";\n    this._engine = engine;\n  }\n  getPlugin() {\n    return {};\n  }\n  loadOptions(options, source) {\n    if (!source || !source.preload) {\n      return;\n    }\n    if (!options.preload) {\n      options.preload = [];\n    }\n    const preloadOptions = options.preload;\n    for (const item of source.preload) {\n      const existing = preloadOptions.find(t => t.name === item.name || t.src === item.src);\n      if (existing) {\n        existing.load(item);\n      } else {\n        const preload = new Preload();\n        preload.load(item);\n        preloadOptions.push(preload);\n      }\n    }\n  }\n  needsPlugin() {\n    return true;\n  }\n}", "map": {"version": 3, "names": ["Preload", "ImagePreloaderPlugin", "constructor", "engine", "id", "_engine", "getPlugin", "loadOptions", "options", "source", "preload", "preloadOptions", "item", "existing", "find", "t", "name", "src", "load", "push", "needsPlugin"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/tsparticles-shape-image/esm/ImagePreloader.js"], "sourcesContent": ["import { Preload } from \"./Options/Classes/Preload\";\nexport class ImagePreloaderPlugin {\n    constructor(engine) {\n        this.id = \"imagePreloader\";\n        this._engine = engine;\n    }\n    getPlugin() {\n        return {};\n    }\n    loadOptions(options, source) {\n        if (!source || !source.preload) {\n            return;\n        }\n        if (!options.preload) {\n            options.preload = [];\n        }\n        const preloadOptions = options.preload;\n        for (const item of source.preload) {\n            const existing = preloadOptions.find((t) => t.name === item.name || t.src === item.src);\n            if (existing) {\n                existing.load(item);\n            }\n            else {\n                const preload = new Preload();\n                preload.load(item);\n                preloadOptions.push(preload);\n            }\n        }\n    }\n    needsPlugin() {\n        return true;\n    }\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,2BAA2B;AACnD,OAAO,MAAMC,oBAAoB,CAAC;EAC9BC,WAAWA,CAACC,MAAM,EAAE;IAChB,IAAI,CAACC,EAAE,GAAG,gBAAgB;IAC1B,IAAI,CAACC,OAAO,GAAGF,MAAM;EACzB;EACAG,SAASA,CAAA,EAAG;IACR,OAAO,CAAC,CAAC;EACb;EACAC,WAAWA,CAACC,OAAO,EAAEC,MAAM,EAAE;IACzB,IAAI,CAACA,MAAM,IAAI,CAACA,MAAM,CAACC,OAAO,EAAE;MAC5B;IACJ;IACA,IAAI,CAACF,OAAO,CAACE,OAAO,EAAE;MAClBF,OAAO,CAACE,OAAO,GAAG,EAAE;IACxB;IACA,MAAMC,cAAc,GAAGH,OAAO,CAACE,OAAO;IACtC,KAAK,MAAME,IAAI,IAAIH,MAAM,CAACC,OAAO,EAAE;MAC/B,MAAMG,QAAQ,GAAGF,cAAc,CAACG,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACC,IAAI,KAAKJ,IAAI,CAACI,IAAI,IAAID,CAAC,CAACE,GAAG,KAAKL,IAAI,CAACK,GAAG,CAAC;MACvF,IAAIJ,QAAQ,EAAE;QACVA,QAAQ,CAACK,IAAI,CAACN,IAAI,CAAC;MACvB,CAAC,MACI;QACD,MAAMF,OAAO,GAAG,IAAIV,OAAO,CAAC,CAAC;QAC7BU,OAAO,CAACQ,IAAI,CAACN,IAAI,CAAC;QAClBD,cAAc,CAACQ,IAAI,CAACT,OAAO,CAAC;MAChC;IACJ;EACJ;EACAU,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI;EACf;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
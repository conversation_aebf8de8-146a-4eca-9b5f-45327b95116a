{"ast": null, "code": "export default function format(str) {\n  for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n    args[_key - 1] = arguments[_key];\n  }\n\n  return [].concat(args).reduce(function (p, c) {\n    return p.replace(/%s/, c);\n  }, str);\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/@popperjs/core/lib/utils/format.js"], "names": ["format", "str", "_len", "arguments", "length", "args", "Array", "_key", "concat", "reduce", "p", "c", "replace"], "mappings": "AAAA,eAAe,SAASA,MAAT,CAAgBC,GAAhB,EAAqB;AAClC,OAAK,IAAIC,IAAI,GAAGC,SAAS,CAACC,MAArB,EAA6BC,IAAI,GAAG,IAAIC,KAAJ,CAAUJ,IAAI,GAAG,CAAP,GAAWA,IAAI,GAAG,CAAlB,GAAsB,CAAhC,CAApC,EAAwEK,IAAI,GAAG,CAApF,EAAuFA,IAAI,GAAGL,IAA9F,EAAoGK,IAAI,EAAxG,EAA4G;AAC1GF,IAAAA,IAAI,CAACE,IAAI,GAAG,CAAR,CAAJ,GAAiBJ,SAAS,CAACI,IAAD,CAA1B;AACD;;AAED,SAAO,GAAGC,MAAH,CAAUH,IAAV,EAAgBI,MAAhB,CAAuB,UAAUC,CAAV,EAAaC,CAAb,EAAgB;AAC5C,WAAOD,CAAC,CAACE,OAAF,CAAU,IAAV,EAAgBD,CAAhB,CAAP;AACD,GAFM,EAEJV,GAFI,CAAP;AAGD", "sourcesContent": ["export default function format(str) {\n  for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n    args[_key - 1] = arguments[_key];\n  }\n\n  return [].concat(args).reduce(function (p, c) {\n    return p.replace(/%s/, c);\n  }, str);\n}"]}, "metadata": {}, "sourceType": "module"}
{"ast": null, "code": "export class PolygonDrawerBase {\n  getSidesCount(particle) {\n    var _a, _b;\n\n    const polygon = particle.shapeData;\n    return (_b = (_a = polygon === null || polygon === void 0 ? void 0 : polygon.sides) !== null && _a !== void 0 ? _a : polygon === null || polygon === void 0 ? void 0 : polygon.nb_sides) !== null && _b !== void 0 ? _b : 5;\n  }\n\n  draw(context, particle, radius) {\n    const start = this.getCenter(particle, radius);\n    const side = this.getSidesData(particle, radius);\n    const sideCount = side.count.numerator * side.count.denominator;\n    const decimalSides = side.count.numerator / side.count.denominator;\n    const interiorAngleDegrees = 180 * (decimalSides - 2) / decimalSides;\n    const interiorAngle = Math.PI - Math.PI * interiorAngleDegrees / 180;\n\n    if (!context) {\n      return;\n    }\n\n    context.beginPath();\n    context.translate(start.x, start.y);\n    context.moveTo(0, 0);\n\n    for (let i = 0; i < sideCount; i++) {\n      context.lineTo(side.length, 0);\n      context.translate(side.length, 0);\n      context.rotate(interiorAngle);\n    }\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Shapes/Polygon/PolygonDrawerBase.js"], "names": ["PolygonDrawerBase", "getSidesCount", "particle", "_a", "_b", "polygon", "shapeData", "sides", "nb_sides", "draw", "context", "radius", "start", "getCenter", "side", "getSidesData", "sideCount", "count", "numerator", "denominator", "decimalSides", "interiorAngleDegrees", "interiorAngle", "Math", "PI", "beginPath", "translate", "x", "y", "moveTo", "i", "lineTo", "length", "rotate"], "mappings": "AAAA,OAAO,MAAMA,iBAAN,CAAwB;AAC3BC,EAAAA,aAAa,CAACC,QAAD,EAAW;AACpB,QAAIC,EAAJ,EAAQC,EAAR;;AACA,UAAMC,OAAO,GAAGH,QAAQ,CAACI,SAAzB;AACA,WAAO,CAACF,EAAE,GAAG,CAACD,EAAE,GAAGE,OAAO,KAAK,IAAZ,IAAoBA,OAAO,KAAK,KAAK,CAArC,GAAyC,KAAK,CAA9C,GAAkDA,OAAO,CAACE,KAAhE,MAA2E,IAA3E,IAAmFJ,EAAE,KAAK,KAAK,CAA/F,GAAmGA,EAAnG,GAAwGE,OAAO,KAAK,IAAZ,IAAoBA,OAAO,KAAK,KAAK,CAArC,GAAyC,KAAK,CAA9C,GAAkDA,OAAO,CAACG,QAAxK,MAAsL,IAAtL,IAA8LJ,EAAE,KAAK,KAAK,CAA1M,GAA8MA,EAA9M,GAAmN,CAA1N;AACH;;AACDK,EAAAA,IAAI,CAACC,OAAD,EAAUR,QAAV,EAAoBS,MAApB,EAA4B;AAC5B,UAAMC,KAAK,GAAG,KAAKC,SAAL,CAAeX,QAAf,EAAyBS,MAAzB,CAAd;AACA,UAAMG,IAAI,GAAG,KAAKC,YAAL,CAAkBb,QAAlB,EAA4BS,MAA5B,CAAb;AACA,UAAMK,SAAS,GAAGF,IAAI,CAACG,KAAL,CAAWC,SAAX,GAAuBJ,IAAI,CAACG,KAAL,CAAWE,WAApD;AACA,UAAMC,YAAY,GAAGN,IAAI,CAACG,KAAL,CAAWC,SAAX,GAAuBJ,IAAI,CAACG,KAAL,CAAWE,WAAvD;AACA,UAAME,oBAAoB,GAAI,OAAOD,YAAY,GAAG,CAAtB,CAAD,GAA6BA,YAA1D;AACA,UAAME,aAAa,GAAGC,IAAI,CAACC,EAAL,GAAWD,IAAI,CAACC,EAAL,GAAUH,oBAAX,GAAmC,GAAnE;;AACA,QAAI,CAACX,OAAL,EAAc;AACV;AACH;;AACDA,IAAAA,OAAO,CAACe,SAAR;AACAf,IAAAA,OAAO,CAACgB,SAAR,CAAkBd,KAAK,CAACe,CAAxB,EAA2Bf,KAAK,CAACgB,CAAjC;AACAlB,IAAAA,OAAO,CAACmB,MAAR,CAAe,CAAf,EAAkB,CAAlB;;AACA,SAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGd,SAApB,EAA+Bc,CAAC,EAAhC,EAAoC;AAChCpB,MAAAA,OAAO,CAACqB,MAAR,CAAejB,IAAI,CAACkB,MAApB,EAA4B,CAA5B;AACAtB,MAAAA,OAAO,CAACgB,SAAR,CAAkBZ,IAAI,CAACkB,MAAvB,EAA+B,CAA/B;AACAtB,MAAAA,OAAO,CAACuB,MAAR,CAAeX,aAAf;AACH;AACJ;;AAxB0B", "sourcesContent": ["export class PolygonDrawerBase {\n    getSidesCount(particle) {\n        var _a, _b;\n        const polygon = particle.shapeData;\n        return (_b = (_a = polygon === null || polygon === void 0 ? void 0 : polygon.sides) !== null && _a !== void 0 ? _a : polygon === null || polygon === void 0 ? void 0 : polygon.nb_sides) !== null && _b !== void 0 ? _b : 5;\n    }\n    draw(context, particle, radius) {\n        const start = this.getCenter(particle, radius);\n        const side = this.getSidesData(particle, radius);\n        const sideCount = side.count.numerator * side.count.denominator;\n        const decimalSides = side.count.numerator / side.count.denominator;\n        const interiorAngleDegrees = (180 * (decimalSides - 2)) / decimalSides;\n        const interiorAngle = Math.PI - (Math.PI * interiorAngleDegrees) / 180;\n        if (!context) {\n            return;\n        }\n        context.beginPath();\n        context.translate(start.x, start.y);\n        context.moveTo(0, 0);\n        for (let i = 0; i < sideCount; i++) {\n            context.lineTo(side.length, 0);\n            context.translate(side.length, 0);\n            context.rotate(interiorAngle);\n        }\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
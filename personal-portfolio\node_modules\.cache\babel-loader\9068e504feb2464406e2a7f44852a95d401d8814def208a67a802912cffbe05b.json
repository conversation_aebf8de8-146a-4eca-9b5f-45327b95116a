{"ast": null, "code": "import { ExternalInteractorBase, getRangeValue } from \"@tsparticles/engine\";\nimport { Remove } from \"./Options/Classes/Remove.js\";\nconst removeMode = \"remove\";\nexport class Remover extends ExternalInteractorBase {\n  constructor(container) {\n    super(container);\n    this.handleClickMode = mode => {\n      const container = this.container,\n        options = container.actualOptions;\n      if (!options.interactivity.modes.remove || mode !== removeMode) {\n        return;\n      }\n      const removeNb = getRangeValue(options.interactivity.modes.remove.quantity);\n      container.particles.removeQuantity(removeNb);\n    };\n  }\n  clear() {}\n  init() {}\n  interact() {}\n  isEnabled() {\n    return true;\n  }\n  loadModeOptions(options, ...sources) {\n    if (!options.remove) {\n      options.remove = new Remove();\n    }\n    for (const source of sources) {\n      options.remove.load(source?.remove);\n    }\n  }\n  reset() {}\n}", "map": {"version": 3, "names": ["ExternalInteractorBase", "getRangeValue", "Remove", "removeMode", "Remover", "constructor", "container", "handleClickMode", "mode", "options", "actualOptions", "interactivity", "modes", "remove", "removeNb", "quantity", "particles", "removeQuantity", "clear", "init", "interact", "isEnabled", "loadModeOptions", "sources", "source", "load", "reset"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/interaction-external-remove/browser/Remover.js"], "sourcesContent": ["import { ExternalInteractorBase, getRangeValue, } from \"@tsparticles/engine\";\nimport { Remove } from \"./Options/Classes/Remove.js\";\nconst removeMode = \"remove\";\nexport class Remover extends ExternalInteractorBase {\n    constructor(container) {\n        super(container);\n        this.handleClickMode = (mode) => {\n            const container = this.container, options = container.actualOptions;\n            if (!options.interactivity.modes.remove || mode !== removeMode) {\n                return;\n            }\n            const removeNb = getRangeValue(options.interactivity.modes.remove.quantity);\n            container.particles.removeQuantity(removeNb);\n        };\n    }\n    clear() {\n    }\n    init() {\n    }\n    interact() {\n    }\n    isEnabled() {\n        return true;\n    }\n    loadModeOptions(options, ...sources) {\n        if (!options.remove) {\n            options.remove = new Remove();\n        }\n        for (const source of sources) {\n            options.remove.load(source?.remove);\n        }\n    }\n    reset() {\n    }\n}\n"], "mappings": "AAAA,SAASA,sBAAsB,EAAEC,aAAa,QAAS,qBAAqB;AAC5E,SAASC,MAAM,QAAQ,6BAA6B;AACpD,MAAMC,UAAU,GAAG,QAAQ;AAC3B,OAAO,MAAMC,OAAO,SAASJ,sBAAsB,CAAC;EAChDK,WAAWA,CAACC,SAAS,EAAE;IACnB,KAAK,CAACA,SAAS,CAAC;IAChB,IAAI,CAACC,eAAe,GAAIC,IAAI,IAAK;MAC7B,MAAMF,SAAS,GAAG,IAAI,CAACA,SAAS;QAAEG,OAAO,GAAGH,SAAS,CAACI,aAAa;MACnE,IAAI,CAACD,OAAO,CAACE,aAAa,CAACC,KAAK,CAACC,MAAM,IAAIL,IAAI,KAAKL,UAAU,EAAE;QAC5D;MACJ;MACA,MAAMW,QAAQ,GAAGb,aAAa,CAACQ,OAAO,CAACE,aAAa,CAACC,KAAK,CAACC,MAAM,CAACE,QAAQ,CAAC;MAC3ET,SAAS,CAACU,SAAS,CAACC,cAAc,CAACH,QAAQ,CAAC;IAChD,CAAC;EACL;EACAI,KAAKA,CAAA,EAAG,CACR;EACAC,IAAIA,CAAA,EAAG,CACP;EACAC,QAAQA,CAAA,EAAG,CACX;EACAC,SAASA,CAAA,EAAG;IACR,OAAO,IAAI;EACf;EACAC,eAAeA,CAACb,OAAO,EAAE,GAAGc,OAAO,EAAE;IACjC,IAAI,CAACd,OAAO,CAACI,MAAM,EAAE;MACjBJ,OAAO,CAACI,MAAM,GAAG,IAAIX,MAAM,CAAC,CAAC;IACjC;IACA,KAAK,MAAMsB,MAAM,IAAID,OAAO,EAAE;MAC1Bd,OAAO,CAACI,MAAM,CAACY,IAAI,CAACD,MAAM,EAAEX,MAAM,CAAC;IACvC;EACJ;EACAa,KAAKA,CAAA,EAAG,CACR;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { ExternalInteractorBase } from \"../../../Core\";\nimport { isInArray } from \"../../../Utils\";\nexport class Connector extends ExternalInteractorBase {\n  constructor(container) {\n    super(container);\n  }\n\n  isEnabled() {\n    const container = this.container,\n          mouse = container.interactivity.mouse,\n          events = container.actualOptions.interactivity.events;\n\n    if (!(events.onHover.enable && mouse.position)) {\n      return false;\n    }\n\n    return isInArray(\"connect\", events.onHover.mode);\n  }\n\n  reset() {}\n\n  async interact() {\n    const container = this.container,\n          options = container.actualOptions;\n\n    if (options.interactivity.events.onHover.enable && container.interactivity.status === \"mousemove\") {\n      const mousePos = container.interactivity.mouse.position;\n\n      if (!mousePos) {\n        return;\n      }\n\n      const distance = Math.abs(container.retina.connectModeRadius),\n            query = container.particles.quadTree.queryCircle(mousePos, distance);\n      let i = 0;\n\n      for (const p1 of query) {\n        const pos1 = p1.getPosition();\n\n        for (const p2 of query.slice(i + 1)) {\n          const pos2 = p2.getPosition(),\n                distMax = Math.abs(container.retina.connectModeDistance),\n                xDiff = Math.abs(pos1.x - pos2.x),\n                yDiff = Math.abs(pos1.y - pos2.y);\n\n          if (xDiff < distMax && yDiff < distMax) {\n            container.canvas.drawConnectLine(p1, p2);\n          }\n        }\n\n        ++i;\n      }\n    }\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Interactions/External/Connect/Connector.js"], "names": ["ExternalInteractorBase", "isInArray", "Connector", "constructor", "container", "isEnabled", "mouse", "interactivity", "events", "actualOptions", "onHover", "enable", "position", "mode", "reset", "interact", "options", "status", "mousePos", "distance", "Math", "abs", "retina", "connectModeRadius", "query", "particles", "quadTree", "queryCircle", "i", "p1", "pos1", "getPosition", "p2", "slice", "pos2", "distMax", "connectModeDistance", "xDiff", "x", "yDiff", "y", "canvas", "drawConnectLine"], "mappings": "AAAA,SAASA,sBAAT,QAAuC,eAAvC;AACA,SAASC,SAAT,QAA0B,gBAA1B;AACA,OAAO,MAAMC,SAAN,SAAwBF,sBAAxB,CAA+C;AAClDG,EAAAA,WAAW,CAACC,SAAD,EAAY;AACnB,UAAMA,SAAN;AACH;;AACDC,EAAAA,SAAS,GAAG;AACR,UAAMD,SAAS,GAAG,KAAKA,SAAvB;AAAA,UAAkCE,KAAK,GAAGF,SAAS,CAACG,aAAV,CAAwBD,KAAlE;AAAA,UAAyEE,MAAM,GAAGJ,SAAS,CAACK,aAAV,CAAwBF,aAAxB,CAAsCC,MAAxH;;AACA,QAAI,EAAEA,MAAM,CAACE,OAAP,CAAeC,MAAf,IAAyBL,KAAK,CAACM,QAAjC,CAAJ,EAAgD;AAC5C,aAAO,KAAP;AACH;;AACD,WAAOX,SAAS,CAAC,SAAD,EAAYO,MAAM,CAACE,OAAP,CAAeG,IAA3B,CAAhB;AACH;;AACDC,EAAAA,KAAK,GAAG,CACP;;AACa,QAARC,QAAQ,GAAG;AACb,UAAMX,SAAS,GAAG,KAAKA,SAAvB;AAAA,UAAkCY,OAAO,GAAGZ,SAAS,CAACK,aAAtD;;AACA,QAAIO,OAAO,CAACT,aAAR,CAAsBC,MAAtB,CAA6BE,OAA7B,CAAqCC,MAArC,IAA+CP,SAAS,CAACG,aAAV,CAAwBU,MAAxB,KAAmC,WAAtF,EAAmG;AAC/F,YAAMC,QAAQ,GAAGd,SAAS,CAACG,aAAV,CAAwBD,KAAxB,CAA8BM,QAA/C;;AACA,UAAI,CAACM,QAAL,EAAe;AACX;AACH;;AACD,YAAMC,QAAQ,GAAGC,IAAI,CAACC,GAAL,CAASjB,SAAS,CAACkB,MAAV,CAAiBC,iBAA1B,CAAjB;AAAA,YAA+DC,KAAK,GAAGpB,SAAS,CAACqB,SAAV,CAAoBC,QAApB,CAA6BC,WAA7B,CAAyCT,QAAzC,EAAmDC,QAAnD,CAAvE;AACA,UAAIS,CAAC,GAAG,CAAR;;AACA,WAAK,MAAMC,EAAX,IAAiBL,KAAjB,EAAwB;AACpB,cAAMM,IAAI,GAAGD,EAAE,CAACE,WAAH,EAAb;;AACA,aAAK,MAAMC,EAAX,IAAiBR,KAAK,CAACS,KAAN,CAAYL,CAAC,GAAG,CAAhB,CAAjB,EAAqC;AACjC,gBAAMM,IAAI,GAAGF,EAAE,CAACD,WAAH,EAAb;AAAA,gBAA+BI,OAAO,GAAGf,IAAI,CAACC,GAAL,CAASjB,SAAS,CAACkB,MAAV,CAAiBc,mBAA1B,CAAzC;AAAA,gBAAyFC,KAAK,GAAGjB,IAAI,CAACC,GAAL,CAASS,IAAI,CAACQ,CAAL,GAASJ,IAAI,CAACI,CAAvB,CAAjG;AAAA,gBAA4HC,KAAK,GAAGnB,IAAI,CAACC,GAAL,CAASS,IAAI,CAACU,CAAL,GAASN,IAAI,CAACM,CAAvB,CAApI;;AACA,cAAIH,KAAK,GAAGF,OAAR,IAAmBI,KAAK,GAAGJ,OAA/B,EAAwC;AACpC/B,YAAAA,SAAS,CAACqC,MAAV,CAAiBC,eAAjB,CAAiCb,EAAjC,EAAqCG,EAArC;AACH;AACJ;;AACD,UAAEJ,CAAF;AACH;AACJ;AACJ;;AAjCiD", "sourcesContent": ["import { ExternalInteractorBase } from \"../../../Core\";\nimport { isInArray } from \"../../../Utils\";\nexport class Connector extends ExternalInteractorBase {\n    constructor(container) {\n        super(container);\n    }\n    isEnabled() {\n        const container = this.container, mouse = container.interactivity.mouse, events = container.actualOptions.interactivity.events;\n        if (!(events.onHover.enable && mouse.position)) {\n            return false;\n        }\n        return isInArray(\"connect\", events.onHover.mode);\n    }\n    reset() {\n    }\n    async interact() {\n        const container = this.container, options = container.actualOptions;\n        if (options.interactivity.events.onHover.enable && container.interactivity.status === \"mousemove\") {\n            const mousePos = container.interactivity.mouse.position;\n            if (!mousePos) {\n                return;\n            }\n            const distance = Math.abs(container.retina.connectModeRadius), query = container.particles.quadTree.queryCircle(mousePos, distance);\n            let i = 0;\n            for (const p1 of query) {\n                const pos1 = p1.getPosition();\n                for (const p2 of query.slice(i + 1)) {\n                    const pos2 = p2.getPosition(), distMax = Math.abs(container.retina.connectModeDistance), xDiff = Math.abs(pos1.x - pos2.x), yDiff = Math.abs(pos1.y - pos2.y);\n                    if (xDiff < distMax && yDiff < distMax) {\n                        container.canvas.drawConnectLine(p1, p2);\n                    }\n                }\n                ++i;\n            }\n        }\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
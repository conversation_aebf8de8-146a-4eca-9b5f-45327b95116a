{"ast": null, "code": "import { CircleDrawer } from \"./CircleDrawer\";\nexport async function loadCircleShape(engine, refresh = true) {\n  await engine.addShape(\"circle\", new CircleDrawer(), refresh);\n}", "map": {"version": 3, "names": ["CircleDrawer", "loadCircleShape", "engine", "refresh", "addShape"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/tsparticles-shape-circle/esm/index.js"], "sourcesContent": ["import { CircleDrawer } from \"./CircleDrawer\";\nexport async function loadCircleShape(engine, refresh = true) {\n    await engine.addShape(\"circle\", new CircleDrawer(), refresh);\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,gBAAgB;AAC7C,OAAO,eAAeC,eAAeA,CAACC,MAAM,EAAEC,OAAO,GAAG,IAAI,EAAE;EAC1D,MAAMD,MAAM,CAACE,QAAQ,CAAC,QAAQ,EAAE,IAAIJ,YAAY,CAAC,CAAC,EAAEG,OAAO,CAAC;AAChE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
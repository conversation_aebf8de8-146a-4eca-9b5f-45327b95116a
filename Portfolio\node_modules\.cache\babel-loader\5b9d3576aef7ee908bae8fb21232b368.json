{"ast": null, "code": "import { OptionsColor } from \"../../OptionsColor\";\nimport { RollLight } from \"./RollLight\";\nimport { setRangeValue } from \"../../../../Utils\";\nexport class Roll {\n  constructor() {\n    this.darken = new RollLight();\n    this.enable = false;\n    this.enlighten = new RollLight();\n    this.mode = \"vertical\";\n    this.speed = 25;\n  }\n\n  load(data) {\n    if (!data) {\n      return;\n    }\n\n    if (data.backColor !== undefined) {\n      this.backColor = OptionsColor.create(this.backColor, data.backColor);\n    }\n\n    this.darken.load(data.darken);\n\n    if (data.enable !== undefined) {\n      this.enable = data.enable;\n    }\n\n    this.enlighten.load(data.enlighten);\n\n    if (data.mode !== undefined) {\n      this.mode = data.mode;\n    }\n\n    if (data.speed !== undefined) {\n      this.speed = setRangeValue(data.speed);\n    }\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Options/Classes/Particles/Roll/Roll.js"], "names": ["OptionsColor", "RollLight", "setRangeValue", "Roll", "constructor", "darken", "enable", "enlighten", "mode", "speed", "load", "data", "backColor", "undefined", "create"], "mappings": "AAAA,SAASA,YAAT,QAA6B,oBAA7B;AACA,SAASC,SAAT,QAA0B,aAA1B;AACA,SAASC,aAAT,QAA8B,mBAA9B;AACA,OAAO,MAAMC,IAAN,CAAW;AACdC,EAAAA,WAAW,GAAG;AACV,SAAKC,MAAL,GAAc,IAAIJ,SAAJ,EAAd;AACA,SAAKK,MAAL,GAAc,KAAd;AACA,SAAKC,SAAL,GAAiB,IAAIN,SAAJ,EAAjB;AACA,SAAKO,IAAL,GAAY,UAAZ;AACA,SAAKC,KAAL,GAAa,EAAb;AACH;;AACDC,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,QAAI,CAACA,IAAL,EAAW;AACP;AACH;;AACD,QAAIA,IAAI,CAACC,SAAL,KAAmBC,SAAvB,EAAkC;AAC9B,WAAKD,SAAL,GAAiBZ,YAAY,CAACc,MAAb,CAAoB,KAAKF,SAAzB,EAAoCD,IAAI,CAACC,SAAzC,CAAjB;AACH;;AACD,SAAKP,MAAL,CAAYK,IAAZ,CAAiBC,IAAI,CAACN,MAAtB;;AACA,QAAIM,IAAI,CAACL,MAAL,KAAgBO,SAApB,EAA+B;AAC3B,WAAKP,MAAL,GAAcK,IAAI,CAACL,MAAnB;AACH;;AACD,SAAKC,SAAL,CAAeG,IAAf,CAAoBC,IAAI,CAACJ,SAAzB;;AACA,QAAII,IAAI,CAACH,IAAL,KAAcK,SAAlB,EAA6B;AACzB,WAAKL,IAAL,GAAYG,IAAI,CAACH,IAAjB;AACH;;AACD,QAAIG,IAAI,CAACF,KAAL,KAAeI,SAAnB,EAA8B;AAC1B,WAAKJ,KAAL,GAAaP,aAAa,CAACS,IAAI,CAACF,KAAN,CAA1B;AACH;AACJ;;AA1Ba", "sourcesContent": ["import { OptionsColor } from \"../../OptionsColor\";\nimport { RollLight } from \"./RollLight\";\nimport { setRangeValue } from \"../../../../Utils\";\nexport class Roll {\n    constructor() {\n        this.darken = new RollLight();\n        this.enable = false;\n        this.enlighten = new RollLight();\n        this.mode = \"vertical\";\n        this.speed = 25;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.backColor !== undefined) {\n            this.backColor = OptionsColor.create(this.backColor, data.backColor);\n        }\n        this.darken.load(data.darken);\n        if (data.enable !== undefined) {\n            this.enable = data.enable;\n        }\n        this.enlighten.load(data.enlighten);\n        if (data.mode !== undefined) {\n            this.mode = data.mode;\n        }\n        if (data.speed !== undefined) {\n            this.speed = setRangeValue(data.speed);\n        }\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
{"ast": null, "code": "import { Attractor } from \"./Attractor.js\";\nexport async function loadParticlesAttractInteraction(engine, refresh = true) {\n  await engine.addInteractor(\"particlesAttract\", container => {\n    return Promise.resolve(new Attractor(container));\n  }, refresh);\n}", "map": {"version": 3, "names": ["Attractor", "loadParticlesAttractInteraction", "engine", "refresh", "addInteractor", "container", "Promise", "resolve"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/interaction-particles-attract/browser/index.js"], "sourcesContent": ["import { Attractor } from \"./Attractor.js\";\nexport async function loadParticlesAttractInteraction(engine, refresh = true) {\n    await engine.addInteractor(\"particlesAttract\", container => {\n        return Promise.resolve(new Attractor(container));\n    }, refresh);\n}\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,gBAAgB;AAC1C,OAAO,eAAeC,+BAA+BA,CAACC,MAAM,EAAEC,OAAO,GAAG,IAAI,EAAE;EAC1E,MAAMD,MAAM,CAACE,aAAa,CAAC,kBAAkB,EAAEC,SAAS,IAAI;IACxD,OAAOC,OAAO,CAACC,OAAO,CAAC,IAAIP,SAAS,CAACK,SAAS,CAAC,CAAC;EACpD,CAAC,EAAEF,OAAO,CAAC;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
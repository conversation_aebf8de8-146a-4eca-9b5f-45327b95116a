{"ast": null, "code": "import { ValueWithRandom } from \"../../ValueWithRandom\";\nexport class SplitFactor extends ValueWithRandom {\n  constructor() {\n    super();\n    this.value = 3;\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Options/Classes/Particles/Destroy/SplitFactor.js"], "names": ["ValueWithRandom", "SplitFactor", "constructor", "value"], "mappings": "AAAA,SAASA,eAAT,QAAgC,uBAAhC;AACA,OAAO,MAAMC,WAAN,SAA0BD,eAA1B,CAA0C;AAC7CE,EAAAA,WAAW,GAAG;AACV;AACA,SAAKC,KAAL,GAAa,CAAb;AACH;;AAJ4C", "sourcesContent": ["import { ValueWithRandom } from \"../../ValueWithRandom\";\nexport class SplitFactor extends ValueWithRandom {\n    constructor() {\n        super();\n        this.value = 3;\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
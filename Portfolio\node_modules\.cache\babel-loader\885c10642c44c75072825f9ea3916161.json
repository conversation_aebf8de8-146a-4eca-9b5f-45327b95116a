{"ast": null, "code": "import { Bubbler } from \"./Bubbler\";\nexport async function loadExternalBubbleInteraction(engine) {\n  await engine.addInteractor(\"externalBubble\", container => new Bubbler(container));\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Interactions/External/Bubble/index.js"], "names": ["Bubbler", "loadExternalBubbleInteraction", "engine", "addInteractor", "container"], "mappings": "AAAA,SAASA,OAAT,QAAwB,WAAxB;AACA,OAAO,eAAeC,6BAAf,CAA6CC,MAA7C,EAAqD;AACxD,QAAMA,MAAM,CAACC,aAAP,CAAqB,gBAArB,EAAwCC,SAAD,IAAe,IAAIJ,OAAJ,CAAYI,SAAZ,CAAtD,CAAN;AACH", "sourcesContent": ["import { Bubbler } from \"./Bubbler\";\nexport async function loadExternalBubbleInteraction(engine) {\n    await engine.addInteractor(\"externalBubble\", (container) => new Bubbler(container));\n}\n"]}, "metadata": {}, "sourceType": "module"}
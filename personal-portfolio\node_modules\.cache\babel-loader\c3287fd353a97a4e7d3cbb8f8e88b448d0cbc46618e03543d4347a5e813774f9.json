{"ast": null, "code": "import { loadImage } from \"../Utils.js\";\nimport { InterlaceOffsets, InterlaceSteps } from \"./Constants.js\";\nimport { ByteStream } from \"./ByteStream.js\";\nimport { DisposalMethod } from \"./Enums/DisposalMethod.js\";\nimport { GIFDataHeaders } from \"./Types/GIFDataHeaders.js\";\nconst origin = {\n    x: 0,\n    y: 0\n  },\n  defaultFrame = 0,\n  half = 0.5,\n  initialTime = 0,\n  firstIndex = 0,\n  defaultLoopCount = 0;\nfunction parseColorTable(byteStream, count) {\n  const colors = [];\n  for (let i = 0; i < count; i++) {\n    colors.push({\n      r: byteStream.data[byteStream.pos],\n      g: byteStream.data[byteStream.pos + 1],\n      b: byteStream.data[byteStream.pos + 2]\n    });\n    byteStream.pos += 3;\n  }\n  return colors;\n}\nfunction parseExtensionBlock(byteStream, gif, getFrameIndex, getTransparencyIndex) {\n  switch (byteStream.nextByte()) {\n    case GIFDataHeaders.GraphicsControlExtension:\n      {\n        const frame = gif.frames[getFrameIndex(false)];\n        byteStream.pos++;\n        const packedByte = byteStream.nextByte();\n        frame.GCreserved = (packedByte & 0xe0) >>> 5;\n        frame.disposalMethod = (packedByte & 0x1c) >>> 2;\n        frame.userInputDelayFlag = (packedByte & 2) === 2;\n        const transparencyFlag = (packedByte & 1) === 1;\n        frame.delayTime = byteStream.nextTwoBytes() * 0xa;\n        const transparencyIndex = byteStream.nextByte();\n        if (transparencyFlag) {\n          getTransparencyIndex(transparencyIndex);\n        }\n        byteStream.pos++;\n        break;\n      }\n    case GIFDataHeaders.ApplicationExtension:\n      {\n        byteStream.pos++;\n        const applicationExtension = {\n          identifier: byteStream.getString(8),\n          authenticationCode: byteStream.getString(3),\n          data: byteStream.readSubBlocksBin()\n        };\n        gif.applicationExtensions.push(applicationExtension);\n        break;\n      }\n    case GIFDataHeaders.CommentExtension:\n      {\n        gif.comments.push([getFrameIndex(false), byteStream.readSubBlocks()]);\n        break;\n      }\n    case GIFDataHeaders.PlainTextExtension:\n      {\n        if (gif.globalColorTable.length === 0) {\n          throw new EvalError(\"plain text extension without global color table\");\n        }\n        byteStream.pos++;\n        gif.frames[getFrameIndex(false)].plainTextData = {\n          left: byteStream.nextTwoBytes(),\n          top: byteStream.nextTwoBytes(),\n          width: byteStream.nextTwoBytes(),\n          height: byteStream.nextTwoBytes(),\n          charSize: {\n            width: byteStream.nextTwoBytes(),\n            height: byteStream.nextTwoBytes()\n          },\n          foregroundColor: byteStream.nextByte(),\n          backgroundColor: byteStream.nextByte(),\n          text: byteStream.readSubBlocks()\n        };\n        break;\n      }\n    default:\n      byteStream.skipSubBlocks();\n      break;\n  }\n}\nasync function parseImageBlock(byteStream, gif, avgAlpha, getFrameIndex, getTransparencyIndex, progressCallback) {\n  const frame = gif.frames[getFrameIndex(true)];\n  frame.left = byteStream.nextTwoBytes();\n  frame.top = byteStream.nextTwoBytes();\n  frame.width = byteStream.nextTwoBytes();\n  frame.height = byteStream.nextTwoBytes();\n  const packedByte = byteStream.nextByte(),\n    localColorTableFlag = (packedByte & 0x80) === 0x80,\n    interlacedFlag = (packedByte & 0x40) === 0x40;\n  frame.sortFlag = (packedByte & 0x20) === 0x20;\n  frame.reserved = (packedByte & 0x18) >>> 3;\n  const localColorCount = 1 << (packedByte & 7) + 1;\n  if (localColorTableFlag) {\n    frame.localColorTable = parseColorTable(byteStream, localColorCount);\n  }\n  const getColor = index => {\n    const {\n      r,\n      g,\n      b\n    } = (localColorTableFlag ? frame.localColorTable : gif.globalColorTable)[index];\n    if (index !== getTransparencyIndex(null)) {\n      return {\n        r,\n        g,\n        b,\n        a: 255\n      };\n    }\n    return {\n      r,\n      g,\n      b,\n      a: avgAlpha ? ~~((r + g + b) / 3) : 0\n    };\n  };\n  const image = (() => {\n    try {\n      return new ImageData(frame.width, frame.height, {\n        colorSpace: \"srgb\"\n      });\n    } catch (error) {\n      if (error instanceof DOMException && error.name === \"IndexSizeError\") {\n        return null;\n      }\n      throw error;\n    }\n  })();\n  if (image == null) {\n    throw new EvalError(\"GIF frame size is to large\");\n  }\n  const minCodeSize = byteStream.nextByte(),\n    imageData = byteStream.readSubBlocksBin(),\n    clearCode = 1 << minCodeSize;\n  const readBits = (pos, len) => {\n    const bytePos = pos >>> 3,\n      bitPos = pos & 7;\n    return (imageData[bytePos] + (imageData[bytePos + 1] << 8) + (imageData[bytePos + 2] << 16) & (1 << len) - 1 << bitPos) >>> bitPos;\n  };\n  if (interlacedFlag) {\n    for (let code = 0, size = minCodeSize + 1, pos = 0, dic = [[0]], pass = 0; pass < 4; pass++) {\n      if (InterlaceOffsets[pass] < frame.height) {\n        let pixelPos = 0,\n          lineIndex = 0,\n          exit = false;\n        while (!exit) {\n          const last = code;\n          code = readBits(pos, size);\n          pos += size + 1;\n          if (code === clearCode) {\n            size = minCodeSize + 1;\n            dic.length = clearCode + 2;\n            for (let i = 0; i < dic.length; i++) {\n              dic[i] = i < clearCode ? [i] : [];\n            }\n          } else {\n            if (code >= dic.length) {\n              dic.push(dic[last].concat(dic[last][0]));\n            } else if (last !== clearCode) {\n              dic.push(dic[last].concat(dic[code][0]));\n            }\n            for (const item of dic[code]) {\n              const {\n                r,\n                g,\n                b,\n                a\n              } = getColor(item);\n              image.data.set([r, g, b, a], InterlaceOffsets[pass] * frame.width + InterlaceSteps[pass] * lineIndex + pixelPos % (frame.width * 4));\n              pixelPos += 4;\n            }\n            if (dic.length === 1 << size && size < 0xc) {\n              size++;\n            }\n          }\n          if (pixelPos === frame.width * 4 * (lineIndex + 1)) {\n            lineIndex++;\n            if (InterlaceOffsets[pass] + InterlaceSteps[pass] * lineIndex >= frame.height) {\n              exit = true;\n            }\n          }\n        }\n      }\n      progressCallback?.(byteStream.pos / (byteStream.data.length - 1), getFrameIndex(false) + 1, image, {\n        x: frame.left,\n        y: frame.top\n      }, {\n        width: gif.width,\n        height: gif.height\n      });\n    }\n    frame.image = image;\n    frame.bitmap = await createImageBitmap(image);\n  } else {\n    let code = 0,\n      size = minCodeSize + 1,\n      pos = 0,\n      pixelPos = -4,\n      exit = false;\n    const dic = [[0]];\n    while (!exit) {\n      const last = code;\n      code = readBits(pos, size);\n      pos += size;\n      if (code === clearCode) {\n        size = minCodeSize + 1;\n        dic.length = clearCode + 2;\n        for (let i = 0; i < dic.length; i++) {\n          dic[i] = i < clearCode ? [i] : [];\n        }\n      } else {\n        if (code === clearCode + 1) {\n          exit = true;\n          break;\n        }\n        if (code >= dic.length) {\n          dic.push(dic[last].concat(dic[last][0]));\n        } else if (last !== clearCode) {\n          dic.push(dic[last].concat(dic[code][0]));\n        }\n        for (const item of dic[code]) {\n          const {\n            r,\n            g,\n            b,\n            a\n          } = getColor(item);\n          image.data.set([r, g, b, a], pixelPos += 4);\n        }\n        if (dic.length >= 1 << size && size < 0xc) {\n          size++;\n        }\n      }\n    }\n    frame.image = image;\n    frame.bitmap = await createImageBitmap(image);\n    progressCallback?.((byteStream.pos + 1) / byteStream.data.length, getFrameIndex(false) + 1, frame.image, {\n      x: frame.left,\n      y: frame.top\n    }, {\n      width: gif.width,\n      height: gif.height\n    });\n  }\n}\nasync function parseBlock(byteStream, gif, avgAlpha, getFrameIndex, getTransparencyIndex, progressCallback) {\n  switch (byteStream.nextByte()) {\n    case GIFDataHeaders.EndOfFile:\n      return true;\n    case GIFDataHeaders.Image:\n      await parseImageBlock(byteStream, gif, avgAlpha, getFrameIndex, getTransparencyIndex, progressCallback);\n      break;\n    case GIFDataHeaders.Extension:\n      parseExtensionBlock(byteStream, gif, getFrameIndex, getTransparencyIndex);\n      break;\n    default:\n      throw new EvalError(\"undefined block found\");\n  }\n  return false;\n}\nexport function getGIFLoopAmount(gif) {\n  for (const extension of gif.applicationExtensions) {\n    if (extension.identifier + extension.authenticationCode !== \"NETSCAPE2.0\") {\n      continue;\n    }\n    return extension.data[1] + (extension.data[2] << 8);\n  }\n  return NaN;\n}\nexport async function decodeGIF(gifURL, progressCallback, avgAlpha) {\n  if (!avgAlpha) avgAlpha = false;\n  const res = await fetch(gifURL);\n  if (!res.ok && res.status === 404) {\n    throw new EvalError(\"file not found\");\n  }\n  const buffer = await res.arrayBuffer();\n  const gif = {\n      width: 0,\n      height: 0,\n      totalTime: 0,\n      colorRes: 0,\n      pixelAspectRatio: 0,\n      frames: [],\n      sortFlag: false,\n      globalColorTable: [],\n      backgroundImage: new ImageData(1, 1, {\n        colorSpace: \"srgb\"\n      }),\n      comments: [],\n      applicationExtensions: []\n    },\n    byteStream = new ByteStream(new Uint8ClampedArray(buffer));\n  if (byteStream.getString(6) !== \"GIF89a\") {\n    throw new Error(\"not a supported GIF file\");\n  }\n  gif.width = byteStream.nextTwoBytes();\n  gif.height = byteStream.nextTwoBytes();\n  const packedByte = byteStream.nextByte(),\n    globalColorTableFlag = (packedByte & 0x80) === 0x80;\n  gif.colorRes = (packedByte & 0x70) >>> 4;\n  gif.sortFlag = (packedByte & 8) === 8;\n  const globalColorCount = 1 << (packedByte & 7) + 1,\n    backgroundColorIndex = byteStream.nextByte();\n  gif.pixelAspectRatio = byteStream.nextByte();\n  if (gif.pixelAspectRatio !== 0) {\n    gif.pixelAspectRatio = (gif.pixelAspectRatio + 0xf) / 0x40;\n  }\n  if (globalColorTableFlag) {\n    gif.globalColorTable = parseColorTable(byteStream, globalColorCount);\n  }\n  const backgroundImage = (() => {\n    try {\n      return new ImageData(gif.width, gif.height, {\n        colorSpace: \"srgb\"\n      });\n    } catch (error) {\n      if (error instanceof DOMException && error.name === \"IndexSizeError\") {\n        return null;\n      }\n      throw error;\n    }\n  })();\n  if (backgroundImage == null) {\n    throw new Error(\"GIF frame size is to large\");\n  }\n  const {\n    r,\n    g,\n    b\n  } = gif.globalColorTable[backgroundColorIndex];\n  backgroundImage.data.set(globalColorTableFlag ? [r, g, b, 255] : [0, 0, 0, 0]);\n  for (let i = 4; i < backgroundImage.data.length; i *= 2) {\n    backgroundImage.data.copyWithin(i, 0, i);\n  }\n  gif.backgroundImage = backgroundImage;\n  let frameIndex = -1,\n    incrementFrameIndex = true,\n    transparencyIndex = -1;\n  const getframeIndex = increment => {\n    if (increment) {\n      incrementFrameIndex = true;\n    }\n    return frameIndex;\n  };\n  const getTransparencyIndex = newValue => {\n    if (newValue != null) {\n      transparencyIndex = newValue;\n    }\n    return transparencyIndex;\n  };\n  try {\n    do {\n      if (incrementFrameIndex) {\n        gif.frames.push({\n          left: 0,\n          top: 0,\n          width: 0,\n          height: 0,\n          disposalMethod: DisposalMethod.Replace,\n          image: new ImageData(1, 1, {\n            colorSpace: \"srgb\"\n          }),\n          plainTextData: null,\n          userInputDelayFlag: false,\n          delayTime: 0,\n          sortFlag: false,\n          localColorTable: [],\n          reserved: 0,\n          GCreserved: 0\n        });\n        frameIndex++;\n        transparencyIndex = -1;\n        incrementFrameIndex = false;\n      }\n    } while (!(await parseBlock(byteStream, gif, avgAlpha, getframeIndex, getTransparencyIndex, progressCallback)));\n    gif.frames.length--;\n    for (const frame of gif.frames) {\n      if (frame.userInputDelayFlag && frame.delayTime === 0) {\n        gif.totalTime = Infinity;\n        break;\n      }\n      gif.totalTime += frame.delayTime;\n    }\n    return gif;\n  } catch (error) {\n    if (error instanceof EvalError) {\n      throw new Error(`error while parsing frame ${frameIndex} \"${error.message}\"`);\n    }\n    throw error;\n  }\n}\nexport function drawGif(data) {\n  const {\n      context,\n      radius,\n      particle,\n      delta\n    } = data,\n    image = particle.image;\n  if (!image?.gifData || !image.gif) {\n    return;\n  }\n  const offscreenCanvas = new OffscreenCanvas(image.gifData.width, image.gifData.height),\n    offscreenContext = offscreenCanvas.getContext(\"2d\");\n  if (!offscreenContext) {\n    throw new Error(\"could not create offscreen canvas context\");\n  }\n  offscreenContext.imageSmoothingQuality = \"low\";\n  offscreenContext.imageSmoothingEnabled = false;\n  offscreenContext.clearRect(origin.x, origin.y, offscreenCanvas.width, offscreenCanvas.height);\n  if (particle.gifLoopCount === undefined) {\n    particle.gifLoopCount = image.gifLoopCount ?? defaultLoopCount;\n  }\n  let frameIndex = particle.gifFrame ?? defaultFrame;\n  const pos = {\n      x: -image.gifData.width * half,\n      y: -image.gifData.height * half\n    },\n    frame = image.gifData.frames[frameIndex];\n  if (particle.gifTime === undefined) {\n    particle.gifTime = initialTime;\n  }\n  if (!frame.bitmap) {\n    return;\n  }\n  context.scale(radius / image.gifData.width, radius / image.gifData.height);\n  switch (frame.disposalMethod) {\n    case DisposalMethod.UndefinedA:\n    case DisposalMethod.UndefinedB:\n    case DisposalMethod.UndefinedC:\n    case DisposalMethod.UndefinedD:\n    case DisposalMethod.Replace:\n      offscreenContext.drawImage(frame.bitmap, frame.left, frame.top);\n      context.drawImage(offscreenCanvas, pos.x, pos.y);\n      offscreenContext.clearRect(origin.x, origin.y, offscreenCanvas.width, offscreenCanvas.height);\n      break;\n    case DisposalMethod.Combine:\n      offscreenContext.drawImage(frame.bitmap, frame.left, frame.top);\n      context.drawImage(offscreenCanvas, pos.x, pos.y);\n      break;\n    case DisposalMethod.RestoreBackground:\n      offscreenContext.drawImage(frame.bitmap, frame.left, frame.top);\n      context.drawImage(offscreenCanvas, pos.x, pos.y);\n      offscreenContext.clearRect(origin.x, origin.y, offscreenCanvas.width, offscreenCanvas.height);\n      if (!image.gifData.globalColorTable.length) {\n        offscreenContext.putImageData(image.gifData.frames[firstIndex].image, pos.x + frame.left, pos.y + frame.top);\n      } else {\n        offscreenContext.putImageData(image.gifData.backgroundImage, pos.x, pos.y);\n      }\n      break;\n    case DisposalMethod.RestorePrevious:\n      {\n        const previousImageData = offscreenContext.getImageData(origin.x, origin.y, offscreenCanvas.width, offscreenCanvas.height);\n        offscreenContext.drawImage(frame.bitmap, frame.left, frame.top);\n        context.drawImage(offscreenCanvas, pos.x, pos.y);\n        offscreenContext.clearRect(origin.x, origin.y, offscreenCanvas.width, offscreenCanvas.height);\n        offscreenContext.putImageData(previousImageData, origin.x, origin.y);\n      }\n      break;\n  }\n  particle.gifTime += delta.value;\n  if (particle.gifTime > frame.delayTime) {\n    particle.gifTime -= frame.delayTime;\n    if (++frameIndex >= image.gifData.frames.length) {\n      if (--particle.gifLoopCount <= defaultLoopCount) {\n        return;\n      }\n      frameIndex = firstIndex;\n      offscreenContext.clearRect(origin.x, origin.y, offscreenCanvas.width, offscreenCanvas.height);\n    }\n    particle.gifFrame = frameIndex;\n  }\n  context.scale(image.gifData.width / radius, image.gifData.height / radius);\n}\nexport async function loadGifImage(image) {\n  if (image.type !== \"gif\") {\n    await loadImage(image);\n    return;\n  }\n  image.loading = true;\n  try {\n    image.gifData = await decodeGIF(image.source);\n    image.gifLoopCount = getGIFLoopAmount(image.gifData) ?? defaultLoopCount;\n    if (!image.gifLoopCount) {\n      image.gifLoopCount = Infinity;\n    }\n  } catch {\n    image.error = true;\n  }\n  image.loading = false;\n}", "map": {"version": 3, "names": ["loadImage", "InterlaceOffsets", "InterlaceSteps", "ByteStream", "DisposalMethod", "GIFDataHeaders", "origin", "x", "y", "defaultFrame", "half", "initialTime", "firstIndex", "defaultLoopCount", "parseColorTable", "byteStream", "count", "colors", "i", "push", "r", "data", "pos", "g", "b", "parseExtensionBlock", "gif", "getFrameIndex", "getTransparencyIndex", "nextByte", "GraphicsControlExtension", "frame", "frames", "packedByte", "GCreserved", "disposalMethod", "userInputDelayFlag", "transparencyFlag", "delayTime", "nextTwoBytes", "transparencyIndex", "ApplicationExtension", "applicationExtension", "identifier", "getString", "authenticationCode", "readSubBlocksBin", "applicationExtensions", "CommentExtension", "comments", "readSubBlocks", "PlainTextExtension", "globalColorTable", "length", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "plainTextData", "left", "top", "width", "height", "charSize", "foregroundColor", "backgroundColor", "text", "skip<PERSON><PERSON><PERSON><PERSON>s", "parseImageBlock", "avgAlpha", "progressCallback", "localColorTableFlag", "interlacedFlag", "sortFlag", "reserved", "localColorCount", "localColorTable", "getColor", "index", "a", "image", "ImageData", "colorSpace", "error", "DOMException", "name", "minCodeSize", "imageData", "clearCode", "readBits", "len", "bytePos", "bitPos", "code", "size", "dic", "pass", "pixelPos", "lineIndex", "exit", "last", "concat", "item", "set", "bitmap", "createImageBitmap", "parseBlock", "EndOfFile", "Image", "Extension", "getGIFLoopAmount", "extension", "NaN", "decodeGIF", "gifURL", "res", "fetch", "ok", "status", "buffer", "arrayBuffer", "totalTime", "colorRes", "pixelAspectRatio", "backgroundImage", "Uint8ClampedArray", "Error", "globalColorTableFlag", "globalColorCount", "backgroundColorIndex", "copyWithin", "frameIndex", "incrementFrameIndex", "getframeIndex", "increment", "newValue", "Replace", "Infinity", "message", "drawGif", "context", "radius", "particle", "delta", "gifData", "offscreenCanvas", "OffscreenCanvas", "offscreenContext", "getContext", "imageSmoothingQuality", "imageSmoothingEnabled", "clearRect", "gifLoopCount", "undefined", "gif<PERSON><PERSON><PERSON>", "gifTime", "scale", "UndefinedA", "UndefinedB", "UndefinedC", "UndefinedD", "drawImage", "Combine", "RestoreBackground", "putImageData", "RestorePrevious", "previousImageData", "getImageData", "value", "loadGifImage", "type", "loading", "source"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/shape-image/browser/GifUtils/Utils.js"], "sourcesContent": ["import { loadImage } from \"../Utils.js\";\nimport { InterlaceOffsets, InterlaceSteps } from \"./Constants.js\";\nimport { ByteStream } from \"./ByteStream.js\";\nimport { DisposalMethod } from \"./Enums/DisposalMethod.js\";\nimport { GIFDataHeaders } from \"./Types/GIFDataHeaders.js\";\nconst origin = {\n    x: 0,\n    y: 0,\n}, defaultFrame = 0, half = 0.5, initialTime = 0, firstIndex = 0, defaultLoopCount = 0;\nfunction parseColorTable(byteStream, count) {\n    const colors = [];\n    for (let i = 0; i < count; i++) {\n        colors.push({\n            r: byteStream.data[byteStream.pos],\n            g: byteStream.data[byteStream.pos + 1],\n            b: byteStream.data[byteStream.pos + 2],\n        });\n        byteStream.pos += 3;\n    }\n    return colors;\n}\nfunction parseExtensionBlock(byteStream, gif, getFrameIndex, getTransparencyIndex) {\n    switch (byteStream.nextByte()) {\n        case GIFDataHeaders.GraphicsControlExtension: {\n            const frame = gif.frames[getFrameIndex(false)];\n            byteStream.pos++;\n            const packedByte = byteStream.nextByte();\n            frame.GCreserved = (packedByte & 0xe0) >>> 5;\n            frame.disposalMethod = (packedByte & 0x1c) >>> 2;\n            frame.userInputDelayFlag = (packedByte & 2) === 2;\n            const transparencyFlag = (packedByte & 1) === 1;\n            frame.delayTime = byteStream.nextTwoBytes() * 0xa;\n            const transparencyIndex = byteStream.nextByte();\n            if (transparencyFlag) {\n                getTransparencyIndex(transparencyIndex);\n            }\n            byteStream.pos++;\n            break;\n        }\n        case GIFDataHeaders.ApplicationExtension: {\n            byteStream.pos++;\n            const applicationExtension = {\n                identifier: byteStream.getString(8),\n                authenticationCode: byteStream.getString(3),\n                data: byteStream.readSubBlocksBin(),\n            };\n            gif.applicationExtensions.push(applicationExtension);\n            break;\n        }\n        case GIFDataHeaders.CommentExtension: {\n            gif.comments.push([getFrameIndex(false), byteStream.readSubBlocks()]);\n            break;\n        }\n        case GIFDataHeaders.PlainTextExtension: {\n            if (gif.globalColorTable.length === 0) {\n                throw new EvalError(\"plain text extension without global color table\");\n            }\n            byteStream.pos++;\n            gif.frames[getFrameIndex(false)].plainTextData = {\n                left: byteStream.nextTwoBytes(),\n                top: byteStream.nextTwoBytes(),\n                width: byteStream.nextTwoBytes(),\n                height: byteStream.nextTwoBytes(),\n                charSize: {\n                    width: byteStream.nextTwoBytes(),\n                    height: byteStream.nextTwoBytes(),\n                },\n                foregroundColor: byteStream.nextByte(),\n                backgroundColor: byteStream.nextByte(),\n                text: byteStream.readSubBlocks(),\n            };\n            break;\n        }\n        default:\n            byteStream.skipSubBlocks();\n            break;\n    }\n}\nasync function parseImageBlock(byteStream, gif, avgAlpha, getFrameIndex, getTransparencyIndex, progressCallback) {\n    const frame = gif.frames[getFrameIndex(true)];\n    frame.left = byteStream.nextTwoBytes();\n    frame.top = byteStream.nextTwoBytes();\n    frame.width = byteStream.nextTwoBytes();\n    frame.height = byteStream.nextTwoBytes();\n    const packedByte = byteStream.nextByte(), localColorTableFlag = (packedByte & 0x80) === 0x80, interlacedFlag = (packedByte & 0x40) === 0x40;\n    frame.sortFlag = (packedByte & 0x20) === 0x20;\n    frame.reserved = (packedByte & 0x18) >>> 3;\n    const localColorCount = 1 << ((packedByte & 7) + 1);\n    if (localColorTableFlag) {\n        frame.localColorTable = parseColorTable(byteStream, localColorCount);\n    }\n    const getColor = (index) => {\n        const { r, g, b } = (localColorTableFlag ? frame.localColorTable : gif.globalColorTable)[index];\n        if (index !== getTransparencyIndex(null)) {\n            return { r, g, b, a: 255 };\n        }\n        return { r, g, b, a: avgAlpha ? ~~((r + g + b) / 3) : 0 };\n    };\n    const image = (() => {\n        try {\n            return new ImageData(frame.width, frame.height, { colorSpace: \"srgb\" });\n        }\n        catch (error) {\n            if (error instanceof DOMException && error.name === \"IndexSizeError\") {\n                return null;\n            }\n            throw error;\n        }\n    })();\n    if (image == null) {\n        throw new EvalError(\"GIF frame size is to large\");\n    }\n    const minCodeSize = byteStream.nextByte(), imageData = byteStream.readSubBlocksBin(), clearCode = 1 << minCodeSize;\n    const readBits = (pos, len) => {\n        const bytePos = pos >>> 3, bitPos = pos & 7;\n        return (((imageData[bytePos] + (imageData[bytePos + 1] << 8) + (imageData[bytePos + 2] << 16)) &\n            (((1 << len) - 1) << bitPos)) >>>\n            bitPos);\n    };\n    if (interlacedFlag) {\n        for (let code = 0, size = minCodeSize + 1, pos = 0, dic = [[0]], pass = 0; pass < 4; pass++) {\n            if (InterlaceOffsets[pass] < frame.height) {\n                let pixelPos = 0, lineIndex = 0, exit = false;\n                while (!exit) {\n                    const last = code;\n                    code = readBits(pos, size);\n                    pos += size + 1;\n                    if (code === clearCode) {\n                        size = minCodeSize + 1;\n                        dic.length = clearCode + 2;\n                        for (let i = 0; i < dic.length; i++) {\n                            dic[i] = i < clearCode ? [i] : [];\n                        }\n                    }\n                    else {\n                        if (code >= dic.length) {\n                            dic.push(dic[last].concat(dic[last][0]));\n                        }\n                        else if (last !== clearCode) {\n                            dic.push(dic[last].concat(dic[code][0]));\n                        }\n                        for (const item of dic[code]) {\n                            const { r, g, b, a } = getColor(item);\n                            image.data.set([r, g, b, a], InterlaceOffsets[pass] * frame.width +\n                                InterlaceSteps[pass] * lineIndex +\n                                (pixelPos % (frame.width * 4)));\n                            pixelPos += 4;\n                        }\n                        if (dic.length === 1 << size && size < 0xc) {\n                            size++;\n                        }\n                    }\n                    if (pixelPos === frame.width * 4 * (lineIndex + 1)) {\n                        lineIndex++;\n                        if (InterlaceOffsets[pass] + InterlaceSteps[pass] * lineIndex >= frame.height) {\n                            exit = true;\n                        }\n                    }\n                }\n            }\n            progressCallback?.(byteStream.pos / (byteStream.data.length - 1), getFrameIndex(false) + 1, image, { x: frame.left, y: frame.top }, { width: gif.width, height: gif.height });\n        }\n        frame.image = image;\n        frame.bitmap = await createImageBitmap(image);\n    }\n    else {\n        let code = 0, size = minCodeSize + 1, pos = 0, pixelPos = -4, exit = false;\n        const dic = [[0]];\n        while (!exit) {\n            const last = code;\n            code = readBits(pos, size);\n            pos += size;\n            if (code === clearCode) {\n                size = minCodeSize + 1;\n                dic.length = clearCode + 2;\n                for (let i = 0; i < dic.length; i++) {\n                    dic[i] = i < clearCode ? [i] : [];\n                }\n            }\n            else {\n                if (code === clearCode + 1) {\n                    exit = true;\n                    break;\n                }\n                if (code >= dic.length) {\n                    dic.push(dic[last].concat(dic[last][0]));\n                }\n                else if (last !== clearCode) {\n                    dic.push(dic[last].concat(dic[code][0]));\n                }\n                for (const item of dic[code]) {\n                    const { r, g, b, a } = getColor(item);\n                    image.data.set([r, g, b, a], (pixelPos += 4));\n                }\n                if (dic.length >= 1 << size && size < 0xc) {\n                    size++;\n                }\n            }\n        }\n        frame.image = image;\n        frame.bitmap = await createImageBitmap(image);\n        progressCallback?.((byteStream.pos + 1) / byteStream.data.length, getFrameIndex(false) + 1, frame.image, { x: frame.left, y: frame.top }, { width: gif.width, height: gif.height });\n    }\n}\nasync function parseBlock(byteStream, gif, avgAlpha, getFrameIndex, getTransparencyIndex, progressCallback) {\n    switch (byteStream.nextByte()) {\n        case GIFDataHeaders.EndOfFile:\n            return true;\n        case GIFDataHeaders.Image:\n            await parseImageBlock(byteStream, gif, avgAlpha, getFrameIndex, getTransparencyIndex, progressCallback);\n            break;\n        case GIFDataHeaders.Extension:\n            parseExtensionBlock(byteStream, gif, getFrameIndex, getTransparencyIndex);\n            break;\n        default:\n            throw new EvalError(\"undefined block found\");\n    }\n    return false;\n}\nexport function getGIFLoopAmount(gif) {\n    for (const extension of gif.applicationExtensions) {\n        if (extension.identifier + extension.authenticationCode !== \"NETSCAPE2.0\") {\n            continue;\n        }\n        return extension.data[1] + (extension.data[2] << 8);\n    }\n    return NaN;\n}\nexport async function decodeGIF(gifURL, progressCallback, avgAlpha) {\n    if (!avgAlpha)\n        avgAlpha = false;\n    const res = await fetch(gifURL);\n    if (!res.ok && res.status === 404) {\n        throw new EvalError(\"file not found\");\n    }\n    const buffer = await res.arrayBuffer();\n    const gif = {\n        width: 0,\n        height: 0,\n        totalTime: 0,\n        colorRes: 0,\n        pixelAspectRatio: 0,\n        frames: [],\n        sortFlag: false,\n        globalColorTable: [],\n        backgroundImage: new ImageData(1, 1, { colorSpace: \"srgb\" }),\n        comments: [],\n        applicationExtensions: [],\n    }, byteStream = new ByteStream(new Uint8ClampedArray(buffer));\n    if (byteStream.getString(6) !== \"GIF89a\") {\n        throw new Error(\"not a supported GIF file\");\n    }\n    gif.width = byteStream.nextTwoBytes();\n    gif.height = byteStream.nextTwoBytes();\n    const packedByte = byteStream.nextByte(), globalColorTableFlag = (packedByte & 0x80) === 0x80;\n    gif.colorRes = (packedByte & 0x70) >>> 4;\n    gif.sortFlag = (packedByte & 8) === 8;\n    const globalColorCount = 1 << ((packedByte & 7) + 1), backgroundColorIndex = byteStream.nextByte();\n    gif.pixelAspectRatio = byteStream.nextByte();\n    if (gif.pixelAspectRatio !== 0) {\n        gif.pixelAspectRatio = (gif.pixelAspectRatio + 0xf) / 0x40;\n    }\n    if (globalColorTableFlag) {\n        gif.globalColorTable = parseColorTable(byteStream, globalColorCount);\n    }\n    const backgroundImage = (() => {\n        try {\n            return new ImageData(gif.width, gif.height, { colorSpace: \"srgb\" });\n        }\n        catch (error) {\n            if (error instanceof DOMException && error.name === \"IndexSizeError\") {\n                return null;\n            }\n            throw error;\n        }\n    })();\n    if (backgroundImage == null) {\n        throw new Error(\"GIF frame size is to large\");\n    }\n    const { r, g, b } = gif.globalColorTable[backgroundColorIndex];\n    backgroundImage.data.set(globalColorTableFlag ? [r, g, b, 255] : [0, 0, 0, 0]);\n    for (let i = 4; i < backgroundImage.data.length; i *= 2) {\n        backgroundImage.data.copyWithin(i, 0, i);\n    }\n    gif.backgroundImage = backgroundImage;\n    let frameIndex = -1, incrementFrameIndex = true, transparencyIndex = -1;\n    const getframeIndex = (increment) => {\n        if (increment) {\n            incrementFrameIndex = true;\n        }\n        return frameIndex;\n    };\n    const getTransparencyIndex = (newValue) => {\n        if (newValue != null) {\n            transparencyIndex = newValue;\n        }\n        return transparencyIndex;\n    };\n    try {\n        do {\n            if (incrementFrameIndex) {\n                gif.frames.push({\n                    left: 0,\n                    top: 0,\n                    width: 0,\n                    height: 0,\n                    disposalMethod: DisposalMethod.Replace,\n                    image: new ImageData(1, 1, { colorSpace: \"srgb\" }),\n                    plainTextData: null,\n                    userInputDelayFlag: false,\n                    delayTime: 0,\n                    sortFlag: false,\n                    localColorTable: [],\n                    reserved: 0,\n                    GCreserved: 0,\n                });\n                frameIndex++;\n                transparencyIndex = -1;\n                incrementFrameIndex = false;\n            }\n        } while (!(await parseBlock(byteStream, gif, avgAlpha, getframeIndex, getTransparencyIndex, progressCallback)));\n        gif.frames.length--;\n        for (const frame of gif.frames) {\n            if (frame.userInputDelayFlag && frame.delayTime === 0) {\n                gif.totalTime = Infinity;\n                break;\n            }\n            gif.totalTime += frame.delayTime;\n        }\n        return gif;\n    }\n    catch (error) {\n        if (error instanceof EvalError) {\n            throw new Error(`error while parsing frame ${frameIndex} \"${error.message}\"`);\n        }\n        throw error;\n    }\n}\nexport function drawGif(data) {\n    const { context, radius, particle, delta } = data, image = particle.image;\n    if (!image?.gifData || !image.gif) {\n        return;\n    }\n    const offscreenCanvas = new OffscreenCanvas(image.gifData.width, image.gifData.height), offscreenContext = offscreenCanvas.getContext(\"2d\");\n    if (!offscreenContext) {\n        throw new Error(\"could not create offscreen canvas context\");\n    }\n    offscreenContext.imageSmoothingQuality = \"low\";\n    offscreenContext.imageSmoothingEnabled = false;\n    offscreenContext.clearRect(origin.x, origin.y, offscreenCanvas.width, offscreenCanvas.height);\n    if (particle.gifLoopCount === undefined) {\n        particle.gifLoopCount = image.gifLoopCount ?? defaultLoopCount;\n    }\n    let frameIndex = particle.gifFrame ?? defaultFrame;\n    const pos = { x: -image.gifData.width * half, y: -image.gifData.height * half }, frame = image.gifData.frames[frameIndex];\n    if (particle.gifTime === undefined) {\n        particle.gifTime = initialTime;\n    }\n    if (!frame.bitmap) {\n        return;\n    }\n    context.scale(radius / image.gifData.width, radius / image.gifData.height);\n    switch (frame.disposalMethod) {\n        case DisposalMethod.UndefinedA:\n        case DisposalMethod.UndefinedB:\n        case DisposalMethod.UndefinedC:\n        case DisposalMethod.UndefinedD:\n        case DisposalMethod.Replace:\n            offscreenContext.drawImage(frame.bitmap, frame.left, frame.top);\n            context.drawImage(offscreenCanvas, pos.x, pos.y);\n            offscreenContext.clearRect(origin.x, origin.y, offscreenCanvas.width, offscreenCanvas.height);\n            break;\n        case DisposalMethod.Combine:\n            offscreenContext.drawImage(frame.bitmap, frame.left, frame.top);\n            context.drawImage(offscreenCanvas, pos.x, pos.y);\n            break;\n        case DisposalMethod.RestoreBackground:\n            offscreenContext.drawImage(frame.bitmap, frame.left, frame.top);\n            context.drawImage(offscreenCanvas, pos.x, pos.y);\n            offscreenContext.clearRect(origin.x, origin.y, offscreenCanvas.width, offscreenCanvas.height);\n            if (!image.gifData.globalColorTable.length) {\n                offscreenContext.putImageData(image.gifData.frames[firstIndex].image, pos.x + frame.left, pos.y + frame.top);\n            }\n            else {\n                offscreenContext.putImageData(image.gifData.backgroundImage, pos.x, pos.y);\n            }\n            break;\n        case DisposalMethod.RestorePrevious:\n            {\n                const previousImageData = offscreenContext.getImageData(origin.x, origin.y, offscreenCanvas.width, offscreenCanvas.height);\n                offscreenContext.drawImage(frame.bitmap, frame.left, frame.top);\n                context.drawImage(offscreenCanvas, pos.x, pos.y);\n                offscreenContext.clearRect(origin.x, origin.y, offscreenCanvas.width, offscreenCanvas.height);\n                offscreenContext.putImageData(previousImageData, origin.x, origin.y);\n            }\n            break;\n    }\n    particle.gifTime += delta.value;\n    if (particle.gifTime > frame.delayTime) {\n        particle.gifTime -= frame.delayTime;\n        if (++frameIndex >= image.gifData.frames.length) {\n            if (--particle.gifLoopCount <= defaultLoopCount) {\n                return;\n            }\n            frameIndex = firstIndex;\n            offscreenContext.clearRect(origin.x, origin.y, offscreenCanvas.width, offscreenCanvas.height);\n        }\n        particle.gifFrame = frameIndex;\n    }\n    context.scale(image.gifData.width / radius, image.gifData.height / radius);\n}\nexport async function loadGifImage(image) {\n    if (image.type !== \"gif\") {\n        await loadImage(image);\n        return;\n    }\n    image.loading = true;\n    try {\n        image.gifData = await decodeGIF(image.source);\n        image.gifLoopCount = getGIFLoopAmount(image.gifData) ?? defaultLoopCount;\n        if (!image.gifLoopCount) {\n            image.gifLoopCount = Infinity;\n        }\n    }\n    catch {\n        image.error = true;\n    }\n    image.loading = false;\n}\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,aAAa;AACvC,SAASC,gBAAgB,EAAEC,cAAc,QAAQ,gBAAgB;AACjE,SAASC,UAAU,QAAQ,iBAAiB;AAC5C,SAASC,cAAc,QAAQ,2BAA2B;AAC1D,SAASC,cAAc,QAAQ,2BAA2B;AAC1D,MAAMC,MAAM,GAAG;IACXC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE;EACP,CAAC;EAAEC,YAAY,GAAG,CAAC;EAAEC,IAAI,GAAG,GAAG;EAAEC,WAAW,GAAG,CAAC;EAAEC,UAAU,GAAG,CAAC;EAAEC,gBAAgB,GAAG,CAAC;AACtF,SAASC,eAAeA,CAACC,UAAU,EAAEC,KAAK,EAAE;EACxC,MAAMC,MAAM,GAAG,EAAE;EACjB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,KAAK,EAAEE,CAAC,EAAE,EAAE;IAC5BD,MAAM,CAACE,IAAI,CAAC;MACRC,CAAC,EAAEL,UAAU,CAACM,IAAI,CAACN,UAAU,CAACO,GAAG,CAAC;MAClCC,CAAC,EAAER,UAAU,CAACM,IAAI,CAACN,UAAU,CAACO,GAAG,GAAG,CAAC,CAAC;MACtCE,CAAC,EAAET,UAAU,CAACM,IAAI,CAACN,UAAU,CAACO,GAAG,GAAG,CAAC;IACzC,CAAC,CAAC;IACFP,UAAU,CAACO,GAAG,IAAI,CAAC;EACvB;EACA,OAAOL,MAAM;AACjB;AACA,SAASQ,mBAAmBA,CAACV,UAAU,EAAEW,GAAG,EAAEC,aAAa,EAAEC,oBAAoB,EAAE;EAC/E,QAAQb,UAAU,CAACc,QAAQ,CAAC,CAAC;IACzB,KAAKxB,cAAc,CAACyB,wBAAwB;MAAE;QAC1C,MAAMC,KAAK,GAAGL,GAAG,CAACM,MAAM,CAACL,aAAa,CAAC,KAAK,CAAC,CAAC;QAC9CZ,UAAU,CAACO,GAAG,EAAE;QAChB,MAAMW,UAAU,GAAGlB,UAAU,CAACc,QAAQ,CAAC,CAAC;QACxCE,KAAK,CAACG,UAAU,GAAG,CAACD,UAAU,GAAG,IAAI,MAAM,CAAC;QAC5CF,KAAK,CAACI,cAAc,GAAG,CAACF,UAAU,GAAG,IAAI,MAAM,CAAC;QAChDF,KAAK,CAACK,kBAAkB,GAAG,CAACH,UAAU,GAAG,CAAC,MAAM,CAAC;QACjD,MAAMI,gBAAgB,GAAG,CAACJ,UAAU,GAAG,CAAC,MAAM,CAAC;QAC/CF,KAAK,CAACO,SAAS,GAAGvB,UAAU,CAACwB,YAAY,CAAC,CAAC,GAAG,GAAG;QACjD,MAAMC,iBAAiB,GAAGzB,UAAU,CAACc,QAAQ,CAAC,CAAC;QAC/C,IAAIQ,gBAAgB,EAAE;UAClBT,oBAAoB,CAACY,iBAAiB,CAAC;QAC3C;QACAzB,UAAU,CAACO,GAAG,EAAE;QAChB;MACJ;IACA,KAAKjB,cAAc,CAACoC,oBAAoB;MAAE;QACtC1B,UAAU,CAACO,GAAG,EAAE;QAChB,MAAMoB,oBAAoB,GAAG;UACzBC,UAAU,EAAE5B,UAAU,CAAC6B,SAAS,CAAC,CAAC,CAAC;UACnCC,kBAAkB,EAAE9B,UAAU,CAAC6B,SAAS,CAAC,CAAC,CAAC;UAC3CvB,IAAI,EAAEN,UAAU,CAAC+B,gBAAgB,CAAC;QACtC,CAAC;QACDpB,GAAG,CAACqB,qBAAqB,CAAC5B,IAAI,CAACuB,oBAAoB,CAAC;QACpD;MACJ;IACA,KAAKrC,cAAc,CAAC2C,gBAAgB;MAAE;QAClCtB,GAAG,CAACuB,QAAQ,CAAC9B,IAAI,CAAC,CAACQ,aAAa,CAAC,KAAK,CAAC,EAAEZ,UAAU,CAACmC,aAAa,CAAC,CAAC,CAAC,CAAC;QACrE;MACJ;IACA,KAAK7C,cAAc,CAAC8C,kBAAkB;MAAE;QACpC,IAAIzB,GAAG,CAAC0B,gBAAgB,CAACC,MAAM,KAAK,CAAC,EAAE;UACnC,MAAM,IAAIC,SAAS,CAAC,iDAAiD,CAAC;QAC1E;QACAvC,UAAU,CAACO,GAAG,EAAE;QAChBI,GAAG,CAACM,MAAM,CAACL,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC4B,aAAa,GAAG;UAC7CC,IAAI,EAAEzC,UAAU,CAACwB,YAAY,CAAC,CAAC;UAC/BkB,GAAG,EAAE1C,UAAU,CAACwB,YAAY,CAAC,CAAC;UAC9BmB,KAAK,EAAE3C,UAAU,CAACwB,YAAY,CAAC,CAAC;UAChCoB,MAAM,EAAE5C,UAAU,CAACwB,YAAY,CAAC,CAAC;UACjCqB,QAAQ,EAAE;YACNF,KAAK,EAAE3C,UAAU,CAACwB,YAAY,CAAC,CAAC;YAChCoB,MAAM,EAAE5C,UAAU,CAACwB,YAAY,CAAC;UACpC,CAAC;UACDsB,eAAe,EAAE9C,UAAU,CAACc,QAAQ,CAAC,CAAC;UACtCiC,eAAe,EAAE/C,UAAU,CAACc,QAAQ,CAAC,CAAC;UACtCkC,IAAI,EAAEhD,UAAU,CAACmC,aAAa,CAAC;QACnC,CAAC;QACD;MACJ;IACA;MACInC,UAAU,CAACiD,aAAa,CAAC,CAAC;MAC1B;EACR;AACJ;AACA,eAAeC,eAAeA,CAAClD,UAAU,EAAEW,GAAG,EAAEwC,QAAQ,EAAEvC,aAAa,EAAEC,oBAAoB,EAAEuC,gBAAgB,EAAE;EAC7G,MAAMpC,KAAK,GAAGL,GAAG,CAACM,MAAM,CAACL,aAAa,CAAC,IAAI,CAAC,CAAC;EAC7CI,KAAK,CAACyB,IAAI,GAAGzC,UAAU,CAACwB,YAAY,CAAC,CAAC;EACtCR,KAAK,CAAC0B,GAAG,GAAG1C,UAAU,CAACwB,YAAY,CAAC,CAAC;EACrCR,KAAK,CAAC2B,KAAK,GAAG3C,UAAU,CAACwB,YAAY,CAAC,CAAC;EACvCR,KAAK,CAAC4B,MAAM,GAAG5C,UAAU,CAACwB,YAAY,CAAC,CAAC;EACxC,MAAMN,UAAU,GAAGlB,UAAU,CAACc,QAAQ,CAAC,CAAC;IAAEuC,mBAAmB,GAAG,CAACnC,UAAU,GAAG,IAAI,MAAM,IAAI;IAAEoC,cAAc,GAAG,CAACpC,UAAU,GAAG,IAAI,MAAM,IAAI;EAC3IF,KAAK,CAACuC,QAAQ,GAAG,CAACrC,UAAU,GAAG,IAAI,MAAM,IAAI;EAC7CF,KAAK,CAACwC,QAAQ,GAAG,CAACtC,UAAU,GAAG,IAAI,MAAM,CAAC;EAC1C,MAAMuC,eAAe,GAAG,CAAC,IAAK,CAACvC,UAAU,GAAG,CAAC,IAAI,CAAE;EACnD,IAAImC,mBAAmB,EAAE;IACrBrC,KAAK,CAAC0C,eAAe,GAAG3D,eAAe,CAACC,UAAU,EAAEyD,eAAe,CAAC;EACxE;EACA,MAAME,QAAQ,GAAIC,KAAK,IAAK;IACxB,MAAM;MAAEvD,CAAC;MAAEG,CAAC;MAAEC;IAAE,CAAC,GAAG,CAAC4C,mBAAmB,GAAGrC,KAAK,CAAC0C,eAAe,GAAG/C,GAAG,CAAC0B,gBAAgB,EAAEuB,KAAK,CAAC;IAC/F,IAAIA,KAAK,KAAK/C,oBAAoB,CAAC,IAAI,CAAC,EAAE;MACtC,OAAO;QAAER,CAAC;QAAEG,CAAC;QAAEC,CAAC;QAAEoD,CAAC,EAAE;MAAI,CAAC;IAC9B;IACA,OAAO;MAAExD,CAAC;MAAEG,CAAC;MAAEC,CAAC;MAAEoD,CAAC,EAAEV,QAAQ,GAAG,CAAC,EAAE,CAAC9C,CAAC,GAAGG,CAAC,GAAGC,CAAC,IAAI,CAAC,CAAC,GAAG;IAAE,CAAC;EAC7D,CAAC;EACD,MAAMqD,KAAK,GAAG,CAAC,MAAM;IACjB,IAAI;MACA,OAAO,IAAIC,SAAS,CAAC/C,KAAK,CAAC2B,KAAK,EAAE3B,KAAK,CAAC4B,MAAM,EAAE;QAAEoB,UAAU,EAAE;MAAO,CAAC,CAAC;IAC3E,CAAC,CACD,OAAOC,KAAK,EAAE;MACV,IAAIA,KAAK,YAAYC,YAAY,IAAID,KAAK,CAACE,IAAI,KAAK,gBAAgB,EAAE;QAClE,OAAO,IAAI;MACf;MACA,MAAMF,KAAK;IACf;EACJ,CAAC,EAAE,CAAC;EACJ,IAAIH,KAAK,IAAI,IAAI,EAAE;IACf,MAAM,IAAIvB,SAAS,CAAC,4BAA4B,CAAC;EACrD;EACA,MAAM6B,WAAW,GAAGpE,UAAU,CAACc,QAAQ,CAAC,CAAC;IAAEuD,SAAS,GAAGrE,UAAU,CAAC+B,gBAAgB,CAAC,CAAC;IAAEuC,SAAS,GAAG,CAAC,IAAIF,WAAW;EAClH,MAAMG,QAAQ,GAAGA,CAAChE,GAAG,EAAEiE,GAAG,KAAK;IAC3B,MAAMC,OAAO,GAAGlE,GAAG,KAAK,CAAC;MAAEmE,MAAM,GAAGnE,GAAG,GAAG,CAAC;IAC3C,OAAQ,CAAE8D,SAAS,CAACI,OAAO,CAAC,IAAIJ,SAAS,CAACI,OAAO,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,IAAIJ,SAAS,CAACI,OAAO,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,GACvF,CAAC,CAAC,IAAID,GAAG,IAAI,CAAC,IAAKE,MAAO,MAC5BA,MAAM;EACd,CAAC;EACD,IAAIpB,cAAc,EAAE;IAChB,KAAK,IAAIqB,IAAI,GAAG,CAAC,EAAEC,IAAI,GAAGR,WAAW,GAAG,CAAC,EAAE7D,GAAG,GAAG,CAAC,EAAEsE,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEC,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAG,CAAC,EAAEA,IAAI,EAAE,EAAE;MACzF,IAAI5F,gBAAgB,CAAC4F,IAAI,CAAC,GAAG9D,KAAK,CAAC4B,MAAM,EAAE;QACvC,IAAImC,QAAQ,GAAG,CAAC;UAAEC,SAAS,GAAG,CAAC;UAAEC,IAAI,GAAG,KAAK;QAC7C,OAAO,CAACA,IAAI,EAAE;UACV,MAAMC,IAAI,GAAGP,IAAI;UACjBA,IAAI,GAAGJ,QAAQ,CAAChE,GAAG,EAAEqE,IAAI,CAAC;UAC1BrE,GAAG,IAAIqE,IAAI,GAAG,CAAC;UACf,IAAID,IAAI,KAAKL,SAAS,EAAE;YACpBM,IAAI,GAAGR,WAAW,GAAG,CAAC;YACtBS,GAAG,CAACvC,MAAM,GAAGgC,SAAS,GAAG,CAAC;YAC1B,KAAK,IAAInE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0E,GAAG,CAACvC,MAAM,EAAEnC,CAAC,EAAE,EAAE;cACjC0E,GAAG,CAAC1E,CAAC,CAAC,GAAGA,CAAC,GAAGmE,SAAS,GAAG,CAACnE,CAAC,CAAC,GAAG,EAAE;YACrC;UACJ,CAAC,MACI;YACD,IAAIwE,IAAI,IAAIE,GAAG,CAACvC,MAAM,EAAE;cACpBuC,GAAG,CAACzE,IAAI,CAACyE,GAAG,CAACK,IAAI,CAAC,CAACC,MAAM,CAACN,GAAG,CAACK,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5C,CAAC,MACI,IAAIA,IAAI,KAAKZ,SAAS,EAAE;cACzBO,GAAG,CAACzE,IAAI,CAACyE,GAAG,CAACK,IAAI,CAAC,CAACC,MAAM,CAACN,GAAG,CAACF,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5C;YACA,KAAK,MAAMS,IAAI,IAAIP,GAAG,CAACF,IAAI,CAAC,EAAE;cAC1B,MAAM;gBAAEtE,CAAC;gBAAEG,CAAC;gBAAEC,CAAC;gBAAEoD;cAAE,CAAC,GAAGF,QAAQ,CAACyB,IAAI,CAAC;cACrCtB,KAAK,CAACxD,IAAI,CAAC+E,GAAG,CAAC,CAAChF,CAAC,EAAEG,CAAC,EAAEC,CAAC,EAAEoD,CAAC,CAAC,EAAE3E,gBAAgB,CAAC4F,IAAI,CAAC,GAAG9D,KAAK,CAAC2B,KAAK,GAC7DxD,cAAc,CAAC2F,IAAI,CAAC,GAAGE,SAAS,GAC/BD,QAAQ,IAAI/D,KAAK,CAAC2B,KAAK,GAAG,CAAC,CAAE,CAAC;cACnCoC,QAAQ,IAAI,CAAC;YACjB;YACA,IAAIF,GAAG,CAACvC,MAAM,KAAK,CAAC,IAAIsC,IAAI,IAAIA,IAAI,GAAG,GAAG,EAAE;cACxCA,IAAI,EAAE;YACV;UACJ;UACA,IAAIG,QAAQ,KAAK/D,KAAK,CAAC2B,KAAK,GAAG,CAAC,IAAIqC,SAAS,GAAG,CAAC,CAAC,EAAE;YAChDA,SAAS,EAAE;YACX,IAAI9F,gBAAgB,CAAC4F,IAAI,CAAC,GAAG3F,cAAc,CAAC2F,IAAI,CAAC,GAAGE,SAAS,IAAIhE,KAAK,CAAC4B,MAAM,EAAE;cAC3EqC,IAAI,GAAG,IAAI;YACf;UACJ;QACJ;MACJ;MACA7B,gBAAgB,GAAGpD,UAAU,CAACO,GAAG,IAAIP,UAAU,CAACM,IAAI,CAACgC,MAAM,GAAG,CAAC,CAAC,EAAE1B,aAAa,CAAC,KAAK,CAAC,GAAG,CAAC,EAAEkD,KAAK,EAAE;QAAEtE,CAAC,EAAEwB,KAAK,CAACyB,IAAI;QAAEhD,CAAC,EAAEuB,KAAK,CAAC0B;MAAI,CAAC,EAAE;QAAEC,KAAK,EAAEhC,GAAG,CAACgC,KAAK;QAAEC,MAAM,EAAEjC,GAAG,CAACiC;MAAO,CAAC,CAAC;IACjL;IACA5B,KAAK,CAAC8C,KAAK,GAAGA,KAAK;IACnB9C,KAAK,CAACsE,MAAM,GAAG,MAAMC,iBAAiB,CAACzB,KAAK,CAAC;EACjD,CAAC,MACI;IACD,IAAIa,IAAI,GAAG,CAAC;MAAEC,IAAI,GAAGR,WAAW,GAAG,CAAC;MAAE7D,GAAG,GAAG,CAAC;MAAEwE,QAAQ,GAAG,CAAC,CAAC;MAAEE,IAAI,GAAG,KAAK;IAC1E,MAAMJ,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IACjB,OAAO,CAACI,IAAI,EAAE;MACV,MAAMC,IAAI,GAAGP,IAAI;MACjBA,IAAI,GAAGJ,QAAQ,CAAChE,GAAG,EAAEqE,IAAI,CAAC;MAC1BrE,GAAG,IAAIqE,IAAI;MACX,IAAID,IAAI,KAAKL,SAAS,EAAE;QACpBM,IAAI,GAAGR,WAAW,GAAG,CAAC;QACtBS,GAAG,CAACvC,MAAM,GAAGgC,SAAS,GAAG,CAAC;QAC1B,KAAK,IAAInE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0E,GAAG,CAACvC,MAAM,EAAEnC,CAAC,EAAE,EAAE;UACjC0E,GAAG,CAAC1E,CAAC,CAAC,GAAGA,CAAC,GAAGmE,SAAS,GAAG,CAACnE,CAAC,CAAC,GAAG,EAAE;QACrC;MACJ,CAAC,MACI;QACD,IAAIwE,IAAI,KAAKL,SAAS,GAAG,CAAC,EAAE;UACxBW,IAAI,GAAG,IAAI;UACX;QACJ;QACA,IAAIN,IAAI,IAAIE,GAAG,CAACvC,MAAM,EAAE;UACpBuC,GAAG,CAACzE,IAAI,CAACyE,GAAG,CAACK,IAAI,CAAC,CAACC,MAAM,CAACN,GAAG,CAACK,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5C,CAAC,MACI,IAAIA,IAAI,KAAKZ,SAAS,EAAE;UACzBO,GAAG,CAACzE,IAAI,CAACyE,GAAG,CAACK,IAAI,CAAC,CAACC,MAAM,CAACN,GAAG,CAACF,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5C;QACA,KAAK,MAAMS,IAAI,IAAIP,GAAG,CAACF,IAAI,CAAC,EAAE;UAC1B,MAAM;YAAEtE,CAAC;YAAEG,CAAC;YAAEC,CAAC;YAAEoD;UAAE,CAAC,GAAGF,QAAQ,CAACyB,IAAI,CAAC;UACrCtB,KAAK,CAACxD,IAAI,CAAC+E,GAAG,CAAC,CAAChF,CAAC,EAAEG,CAAC,EAAEC,CAAC,EAAEoD,CAAC,CAAC,EAAGkB,QAAQ,IAAI,CAAE,CAAC;QACjD;QACA,IAAIF,GAAG,CAACvC,MAAM,IAAI,CAAC,IAAIsC,IAAI,IAAIA,IAAI,GAAG,GAAG,EAAE;UACvCA,IAAI,EAAE;QACV;MACJ;IACJ;IACA5D,KAAK,CAAC8C,KAAK,GAAGA,KAAK;IACnB9C,KAAK,CAACsE,MAAM,GAAG,MAAMC,iBAAiB,CAACzB,KAAK,CAAC;IAC7CV,gBAAgB,GAAG,CAACpD,UAAU,CAACO,GAAG,GAAG,CAAC,IAAIP,UAAU,CAACM,IAAI,CAACgC,MAAM,EAAE1B,aAAa,CAAC,KAAK,CAAC,GAAG,CAAC,EAAEI,KAAK,CAAC8C,KAAK,EAAE;MAAEtE,CAAC,EAAEwB,KAAK,CAACyB,IAAI;MAAEhD,CAAC,EAAEuB,KAAK,CAAC0B;IAAI,CAAC,EAAE;MAAEC,KAAK,EAAEhC,GAAG,CAACgC,KAAK;MAAEC,MAAM,EAAEjC,GAAG,CAACiC;IAAO,CAAC,CAAC;EACvL;AACJ;AACA,eAAe4C,UAAUA,CAACxF,UAAU,EAAEW,GAAG,EAAEwC,QAAQ,EAAEvC,aAAa,EAAEC,oBAAoB,EAAEuC,gBAAgB,EAAE;EACxG,QAAQpD,UAAU,CAACc,QAAQ,CAAC,CAAC;IACzB,KAAKxB,cAAc,CAACmG,SAAS;MACzB,OAAO,IAAI;IACf,KAAKnG,cAAc,CAACoG,KAAK;MACrB,MAAMxC,eAAe,CAAClD,UAAU,EAAEW,GAAG,EAAEwC,QAAQ,EAAEvC,aAAa,EAAEC,oBAAoB,EAAEuC,gBAAgB,CAAC;MACvG;IACJ,KAAK9D,cAAc,CAACqG,SAAS;MACzBjF,mBAAmB,CAACV,UAAU,EAAEW,GAAG,EAAEC,aAAa,EAAEC,oBAAoB,CAAC;MACzE;IACJ;MACI,MAAM,IAAI0B,SAAS,CAAC,uBAAuB,CAAC;EACpD;EACA,OAAO,KAAK;AAChB;AACA,OAAO,SAASqD,gBAAgBA,CAACjF,GAAG,EAAE;EAClC,KAAK,MAAMkF,SAAS,IAAIlF,GAAG,CAACqB,qBAAqB,EAAE;IAC/C,IAAI6D,SAAS,CAACjE,UAAU,GAAGiE,SAAS,CAAC/D,kBAAkB,KAAK,aAAa,EAAE;MACvE;IACJ;IACA,OAAO+D,SAAS,CAACvF,IAAI,CAAC,CAAC,CAAC,IAAIuF,SAAS,CAACvF,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;EACvD;EACA,OAAOwF,GAAG;AACd;AACA,OAAO,eAAeC,SAASA,CAACC,MAAM,EAAE5C,gBAAgB,EAAED,QAAQ,EAAE;EAChE,IAAI,CAACA,QAAQ,EACTA,QAAQ,GAAG,KAAK;EACpB,MAAM8C,GAAG,GAAG,MAAMC,KAAK,CAACF,MAAM,CAAC;EAC/B,IAAI,CAACC,GAAG,CAACE,EAAE,IAAIF,GAAG,CAACG,MAAM,KAAK,GAAG,EAAE;IAC/B,MAAM,IAAI7D,SAAS,CAAC,gBAAgB,CAAC;EACzC;EACA,MAAM8D,MAAM,GAAG,MAAMJ,GAAG,CAACK,WAAW,CAAC,CAAC;EACtC,MAAM3F,GAAG,GAAG;MACRgC,KAAK,EAAE,CAAC;MACRC,MAAM,EAAE,CAAC;MACT2D,SAAS,EAAE,CAAC;MACZC,QAAQ,EAAE,CAAC;MACXC,gBAAgB,EAAE,CAAC;MACnBxF,MAAM,EAAE,EAAE;MACVsC,QAAQ,EAAE,KAAK;MACflB,gBAAgB,EAAE,EAAE;MACpBqE,eAAe,EAAE,IAAI3C,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE;QAAEC,UAAU,EAAE;MAAO,CAAC,CAAC;MAC5D9B,QAAQ,EAAE,EAAE;MACZF,qBAAqB,EAAE;IAC3B,CAAC;IAAEhC,UAAU,GAAG,IAAIZ,UAAU,CAAC,IAAIuH,iBAAiB,CAACN,MAAM,CAAC,CAAC;EAC7D,IAAIrG,UAAU,CAAC6B,SAAS,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;IACtC,MAAM,IAAI+E,KAAK,CAAC,0BAA0B,CAAC;EAC/C;EACAjG,GAAG,CAACgC,KAAK,GAAG3C,UAAU,CAACwB,YAAY,CAAC,CAAC;EACrCb,GAAG,CAACiC,MAAM,GAAG5C,UAAU,CAACwB,YAAY,CAAC,CAAC;EACtC,MAAMN,UAAU,GAAGlB,UAAU,CAACc,QAAQ,CAAC,CAAC;IAAE+F,oBAAoB,GAAG,CAAC3F,UAAU,GAAG,IAAI,MAAM,IAAI;EAC7FP,GAAG,CAAC6F,QAAQ,GAAG,CAACtF,UAAU,GAAG,IAAI,MAAM,CAAC;EACxCP,GAAG,CAAC4C,QAAQ,GAAG,CAACrC,UAAU,GAAG,CAAC,MAAM,CAAC;EACrC,MAAM4F,gBAAgB,GAAG,CAAC,IAAK,CAAC5F,UAAU,GAAG,CAAC,IAAI,CAAE;IAAE6F,oBAAoB,GAAG/G,UAAU,CAACc,QAAQ,CAAC,CAAC;EAClGH,GAAG,CAAC8F,gBAAgB,GAAGzG,UAAU,CAACc,QAAQ,CAAC,CAAC;EAC5C,IAAIH,GAAG,CAAC8F,gBAAgB,KAAK,CAAC,EAAE;IAC5B9F,GAAG,CAAC8F,gBAAgB,GAAG,CAAC9F,GAAG,CAAC8F,gBAAgB,GAAG,GAAG,IAAI,IAAI;EAC9D;EACA,IAAII,oBAAoB,EAAE;IACtBlG,GAAG,CAAC0B,gBAAgB,GAAGtC,eAAe,CAACC,UAAU,EAAE8G,gBAAgB,CAAC;EACxE;EACA,MAAMJ,eAAe,GAAG,CAAC,MAAM;IAC3B,IAAI;MACA,OAAO,IAAI3C,SAAS,CAACpD,GAAG,CAACgC,KAAK,EAAEhC,GAAG,CAACiC,MAAM,EAAE;QAAEoB,UAAU,EAAE;MAAO,CAAC,CAAC;IACvE,CAAC,CACD,OAAOC,KAAK,EAAE;MACV,IAAIA,KAAK,YAAYC,YAAY,IAAID,KAAK,CAACE,IAAI,KAAK,gBAAgB,EAAE;QAClE,OAAO,IAAI;MACf;MACA,MAAMF,KAAK;IACf;EACJ,CAAC,EAAE,CAAC;EACJ,IAAIyC,eAAe,IAAI,IAAI,EAAE;IACzB,MAAM,IAAIE,KAAK,CAAC,4BAA4B,CAAC;EACjD;EACA,MAAM;IAAEvG,CAAC;IAAEG,CAAC;IAAEC;EAAE,CAAC,GAAGE,GAAG,CAAC0B,gBAAgB,CAAC0E,oBAAoB,CAAC;EAC9DL,eAAe,CAACpG,IAAI,CAAC+E,GAAG,CAACwB,oBAAoB,GAAG,CAACxG,CAAC,EAAEG,CAAC,EAAEC,CAAC,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;EAC9E,KAAK,IAAIN,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuG,eAAe,CAACpG,IAAI,CAACgC,MAAM,EAAEnC,CAAC,IAAI,CAAC,EAAE;IACrDuG,eAAe,CAACpG,IAAI,CAAC0G,UAAU,CAAC7G,CAAC,EAAE,CAAC,EAAEA,CAAC,CAAC;EAC5C;EACAQ,GAAG,CAAC+F,eAAe,GAAGA,eAAe;EACrC,IAAIO,UAAU,GAAG,CAAC,CAAC;IAAEC,mBAAmB,GAAG,IAAI;IAAEzF,iBAAiB,GAAG,CAAC,CAAC;EACvE,MAAM0F,aAAa,GAAIC,SAAS,IAAK;IACjC,IAAIA,SAAS,EAAE;MACXF,mBAAmB,GAAG,IAAI;IAC9B;IACA,OAAOD,UAAU;EACrB,CAAC;EACD,MAAMpG,oBAAoB,GAAIwG,QAAQ,IAAK;IACvC,IAAIA,QAAQ,IAAI,IAAI,EAAE;MAClB5F,iBAAiB,GAAG4F,QAAQ;IAChC;IACA,OAAO5F,iBAAiB;EAC5B,CAAC;EACD,IAAI;IACA,GAAG;MACC,IAAIyF,mBAAmB,EAAE;QACrBvG,GAAG,CAACM,MAAM,CAACb,IAAI,CAAC;UACZqC,IAAI,EAAE,CAAC;UACPC,GAAG,EAAE,CAAC;UACNC,KAAK,EAAE,CAAC;UACRC,MAAM,EAAE,CAAC;UACTxB,cAAc,EAAE/B,cAAc,CAACiI,OAAO;UACtCxD,KAAK,EAAE,IAAIC,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE;YAAEC,UAAU,EAAE;UAAO,CAAC,CAAC;UAClDxB,aAAa,EAAE,IAAI;UACnBnB,kBAAkB,EAAE,KAAK;UACzBE,SAAS,EAAE,CAAC;UACZgC,QAAQ,EAAE,KAAK;UACfG,eAAe,EAAE,EAAE;UACnBF,QAAQ,EAAE,CAAC;UACXrC,UAAU,EAAE;QAChB,CAAC,CAAC;QACF8F,UAAU,EAAE;QACZxF,iBAAiB,GAAG,CAAC,CAAC;QACtByF,mBAAmB,GAAG,KAAK;MAC/B;IACJ,CAAC,QAAQ,EAAE,MAAM1B,UAAU,CAACxF,UAAU,EAAEW,GAAG,EAAEwC,QAAQ,EAAEgE,aAAa,EAAEtG,oBAAoB,EAAEuC,gBAAgB,CAAC,CAAC;IAC9GzC,GAAG,CAACM,MAAM,CAACqB,MAAM,EAAE;IACnB,KAAK,MAAMtB,KAAK,IAAIL,GAAG,CAACM,MAAM,EAAE;MAC5B,IAAID,KAAK,CAACK,kBAAkB,IAAIL,KAAK,CAACO,SAAS,KAAK,CAAC,EAAE;QACnDZ,GAAG,CAAC4F,SAAS,GAAGgB,QAAQ;QACxB;MACJ;MACA5G,GAAG,CAAC4F,SAAS,IAAIvF,KAAK,CAACO,SAAS;IACpC;IACA,OAAOZ,GAAG;EACd,CAAC,CACD,OAAOsD,KAAK,EAAE;IACV,IAAIA,KAAK,YAAY1B,SAAS,EAAE;MAC5B,MAAM,IAAIqE,KAAK,CAAC,6BAA6BK,UAAU,KAAKhD,KAAK,CAACuD,OAAO,GAAG,CAAC;IACjF;IACA,MAAMvD,KAAK;EACf;AACJ;AACA,OAAO,SAASwD,OAAOA,CAACnH,IAAI,EAAE;EAC1B,MAAM;MAAEoH,OAAO;MAAEC,MAAM;MAAEC,QAAQ;MAAEC;IAAM,CAAC,GAAGvH,IAAI;IAAEwD,KAAK,GAAG8D,QAAQ,CAAC9D,KAAK;EACzE,IAAI,CAACA,KAAK,EAAEgE,OAAO,IAAI,CAAChE,KAAK,CAACnD,GAAG,EAAE;IAC/B;EACJ;EACA,MAAMoH,eAAe,GAAG,IAAIC,eAAe,CAAClE,KAAK,CAACgE,OAAO,CAACnF,KAAK,EAAEmB,KAAK,CAACgE,OAAO,CAAClF,MAAM,CAAC;IAAEqF,gBAAgB,GAAGF,eAAe,CAACG,UAAU,CAAC,IAAI,CAAC;EAC3I,IAAI,CAACD,gBAAgB,EAAE;IACnB,MAAM,IAAIrB,KAAK,CAAC,2CAA2C,CAAC;EAChE;EACAqB,gBAAgB,CAACE,qBAAqB,GAAG,KAAK;EAC9CF,gBAAgB,CAACG,qBAAqB,GAAG,KAAK;EAC9CH,gBAAgB,CAACI,SAAS,CAAC9I,MAAM,CAACC,CAAC,EAAED,MAAM,CAACE,CAAC,EAAEsI,eAAe,CAACpF,KAAK,EAAEoF,eAAe,CAACnF,MAAM,CAAC;EAC7F,IAAIgF,QAAQ,CAACU,YAAY,KAAKC,SAAS,EAAE;IACrCX,QAAQ,CAACU,YAAY,GAAGxE,KAAK,CAACwE,YAAY,IAAIxI,gBAAgB;EAClE;EACA,IAAImH,UAAU,GAAGW,QAAQ,CAACY,QAAQ,IAAI9I,YAAY;EAClD,MAAMa,GAAG,GAAG;MAAEf,CAAC,EAAE,CAACsE,KAAK,CAACgE,OAAO,CAACnF,KAAK,GAAGhD,IAAI;MAAEF,CAAC,EAAE,CAACqE,KAAK,CAACgE,OAAO,CAAClF,MAAM,GAAGjD;IAAK,CAAC;IAAEqB,KAAK,GAAG8C,KAAK,CAACgE,OAAO,CAAC7G,MAAM,CAACgG,UAAU,CAAC;EACzH,IAAIW,QAAQ,CAACa,OAAO,KAAKF,SAAS,EAAE;IAChCX,QAAQ,CAACa,OAAO,GAAG7I,WAAW;EAClC;EACA,IAAI,CAACoB,KAAK,CAACsE,MAAM,EAAE;IACf;EACJ;EACAoC,OAAO,CAACgB,KAAK,CAACf,MAAM,GAAG7D,KAAK,CAACgE,OAAO,CAACnF,KAAK,EAAEgF,MAAM,GAAG7D,KAAK,CAACgE,OAAO,CAAClF,MAAM,CAAC;EAC1E,QAAQ5B,KAAK,CAACI,cAAc;IACxB,KAAK/B,cAAc,CAACsJ,UAAU;IAC9B,KAAKtJ,cAAc,CAACuJ,UAAU;IAC9B,KAAKvJ,cAAc,CAACwJ,UAAU;IAC9B,KAAKxJ,cAAc,CAACyJ,UAAU;IAC9B,KAAKzJ,cAAc,CAACiI,OAAO;MACvBW,gBAAgB,CAACc,SAAS,CAAC/H,KAAK,CAACsE,MAAM,EAAEtE,KAAK,CAACyB,IAAI,EAAEzB,KAAK,CAAC0B,GAAG,CAAC;MAC/DgF,OAAO,CAACqB,SAAS,CAAChB,eAAe,EAAExH,GAAG,CAACf,CAAC,EAAEe,GAAG,CAACd,CAAC,CAAC;MAChDwI,gBAAgB,CAACI,SAAS,CAAC9I,MAAM,CAACC,CAAC,EAAED,MAAM,CAACE,CAAC,EAAEsI,eAAe,CAACpF,KAAK,EAAEoF,eAAe,CAACnF,MAAM,CAAC;MAC7F;IACJ,KAAKvD,cAAc,CAAC2J,OAAO;MACvBf,gBAAgB,CAACc,SAAS,CAAC/H,KAAK,CAACsE,MAAM,EAAEtE,KAAK,CAACyB,IAAI,EAAEzB,KAAK,CAAC0B,GAAG,CAAC;MAC/DgF,OAAO,CAACqB,SAAS,CAAChB,eAAe,EAAExH,GAAG,CAACf,CAAC,EAAEe,GAAG,CAACd,CAAC,CAAC;MAChD;IACJ,KAAKJ,cAAc,CAAC4J,iBAAiB;MACjChB,gBAAgB,CAACc,SAAS,CAAC/H,KAAK,CAACsE,MAAM,EAAEtE,KAAK,CAACyB,IAAI,EAAEzB,KAAK,CAAC0B,GAAG,CAAC;MAC/DgF,OAAO,CAACqB,SAAS,CAAChB,eAAe,EAAExH,GAAG,CAACf,CAAC,EAAEe,GAAG,CAACd,CAAC,CAAC;MAChDwI,gBAAgB,CAACI,SAAS,CAAC9I,MAAM,CAACC,CAAC,EAAED,MAAM,CAACE,CAAC,EAAEsI,eAAe,CAACpF,KAAK,EAAEoF,eAAe,CAACnF,MAAM,CAAC;MAC7F,IAAI,CAACkB,KAAK,CAACgE,OAAO,CAACzF,gBAAgB,CAACC,MAAM,EAAE;QACxC2F,gBAAgB,CAACiB,YAAY,CAACpF,KAAK,CAACgE,OAAO,CAAC7G,MAAM,CAACpB,UAAU,CAAC,CAACiE,KAAK,EAAEvD,GAAG,CAACf,CAAC,GAAGwB,KAAK,CAACyB,IAAI,EAAElC,GAAG,CAACd,CAAC,GAAGuB,KAAK,CAAC0B,GAAG,CAAC;MAChH,CAAC,MACI;QACDuF,gBAAgB,CAACiB,YAAY,CAACpF,KAAK,CAACgE,OAAO,CAACpB,eAAe,EAAEnG,GAAG,CAACf,CAAC,EAAEe,GAAG,CAACd,CAAC,CAAC;MAC9E;MACA;IACJ,KAAKJ,cAAc,CAAC8J,eAAe;MAC/B;QACI,MAAMC,iBAAiB,GAAGnB,gBAAgB,CAACoB,YAAY,CAAC9J,MAAM,CAACC,CAAC,EAAED,MAAM,CAACE,CAAC,EAAEsI,eAAe,CAACpF,KAAK,EAAEoF,eAAe,CAACnF,MAAM,CAAC;QAC1HqF,gBAAgB,CAACc,SAAS,CAAC/H,KAAK,CAACsE,MAAM,EAAEtE,KAAK,CAACyB,IAAI,EAAEzB,KAAK,CAAC0B,GAAG,CAAC;QAC/DgF,OAAO,CAACqB,SAAS,CAAChB,eAAe,EAAExH,GAAG,CAACf,CAAC,EAAEe,GAAG,CAACd,CAAC,CAAC;QAChDwI,gBAAgB,CAACI,SAAS,CAAC9I,MAAM,CAACC,CAAC,EAAED,MAAM,CAACE,CAAC,EAAEsI,eAAe,CAACpF,KAAK,EAAEoF,eAAe,CAACnF,MAAM,CAAC;QAC7FqF,gBAAgB,CAACiB,YAAY,CAACE,iBAAiB,EAAE7J,MAAM,CAACC,CAAC,EAAED,MAAM,CAACE,CAAC,CAAC;MACxE;MACA;EACR;EACAmI,QAAQ,CAACa,OAAO,IAAIZ,KAAK,CAACyB,KAAK;EAC/B,IAAI1B,QAAQ,CAACa,OAAO,GAAGzH,KAAK,CAACO,SAAS,EAAE;IACpCqG,QAAQ,CAACa,OAAO,IAAIzH,KAAK,CAACO,SAAS;IACnC,IAAI,EAAE0F,UAAU,IAAInD,KAAK,CAACgE,OAAO,CAAC7G,MAAM,CAACqB,MAAM,EAAE;MAC7C,IAAI,EAAEsF,QAAQ,CAACU,YAAY,IAAIxI,gBAAgB,EAAE;QAC7C;MACJ;MACAmH,UAAU,GAAGpH,UAAU;MACvBoI,gBAAgB,CAACI,SAAS,CAAC9I,MAAM,CAACC,CAAC,EAAED,MAAM,CAACE,CAAC,EAAEsI,eAAe,CAACpF,KAAK,EAAEoF,eAAe,CAACnF,MAAM,CAAC;IACjG;IACAgF,QAAQ,CAACY,QAAQ,GAAGvB,UAAU;EAClC;EACAS,OAAO,CAACgB,KAAK,CAAC5E,KAAK,CAACgE,OAAO,CAACnF,KAAK,GAAGgF,MAAM,EAAE7D,KAAK,CAACgE,OAAO,CAAClF,MAAM,GAAG+E,MAAM,CAAC;AAC9E;AACA,OAAO,eAAe4B,YAAYA,CAACzF,KAAK,EAAE;EACtC,IAAIA,KAAK,CAAC0F,IAAI,KAAK,KAAK,EAAE;IACtB,MAAMvK,SAAS,CAAC6E,KAAK,CAAC;IACtB;EACJ;EACAA,KAAK,CAAC2F,OAAO,GAAG,IAAI;EACpB,IAAI;IACA3F,KAAK,CAACgE,OAAO,GAAG,MAAM/B,SAAS,CAACjC,KAAK,CAAC4F,MAAM,CAAC;IAC7C5F,KAAK,CAACwE,YAAY,GAAG1C,gBAAgB,CAAC9B,KAAK,CAACgE,OAAO,CAAC,IAAIhI,gBAAgB;IACxE,IAAI,CAACgE,KAAK,CAACwE,YAAY,EAAE;MACrBxE,KAAK,CAACwE,YAAY,GAAGf,QAAQ;IACjC;EACJ,CAAC,CACD,MAAM;IACFzD,KAAK,CAACG,KAAK,GAAG,IAAI;EACtB;EACAH,KAAK,CAAC2F,OAAO,GAAG,KAAK;AACzB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
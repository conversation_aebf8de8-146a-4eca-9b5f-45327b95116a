{"ast": null, "code": "import { AnimatableColor } from \"../AnimatableColor.js\";\nimport { setRangeValue } from \"../../../Utils/NumberUtils.js\";\nexport class Stroke {\n  constructor() {\n    this.width = 0;\n  }\n  load(data) {\n    if (!data) {\n      return;\n    }\n    if (data.color !== undefined) {\n      this.color = AnimatableColor.create(this.color, data.color);\n    }\n    if (data.width !== undefined) {\n      this.width = setRangeValue(data.width);\n    }\n    if (data.opacity !== undefined) {\n      this.opacity = setRangeValue(data.opacity);\n    }\n  }\n}", "map": {"version": 3, "names": ["AnimatableColor", "setRangeValue", "Stroke", "constructor", "width", "load", "data", "color", "undefined", "create", "opacity"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/engine/browser/Options/Classes/Particles/Stroke.js"], "sourcesContent": ["import { AnimatableColor } from \"../AnimatableColor.js\";\nimport { setRangeValue } from \"../../../Utils/NumberUtils.js\";\nexport class Stroke {\n    constructor() {\n        this.width = 0;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.color !== undefined) {\n            this.color = AnimatableColor.create(this.color, data.color);\n        }\n        if (data.width !== undefined) {\n            this.width = setRangeValue(data.width);\n        }\n        if (data.opacity !== undefined) {\n            this.opacity = setRangeValue(data.opacity);\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,uBAAuB;AACvD,SAASC,aAAa,QAAQ,+BAA+B;AAC7D,OAAO,MAAMC,MAAM,CAAC;EAChBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,KAAK,GAAG,CAAC;EAClB;EACAC,IAAIA,CAACC,IAAI,EAAE;IACP,IAAI,CAACA,IAAI,EAAE;MACP;IACJ;IACA,IAAIA,IAAI,CAACC,KAAK,KAAKC,SAAS,EAAE;MAC1B,IAAI,CAACD,KAAK,GAAGP,eAAe,CAACS,MAAM,CAAC,IAAI,CAACF,KAAK,EAAED,IAAI,CAACC,KAAK,CAAC;IAC/D;IACA,IAAID,IAAI,CAACF,KAAK,KAAKI,SAAS,EAAE;MAC1B,IAAI,CAACJ,KAAK,GAAGH,aAAa,CAACK,IAAI,CAACF,KAAK,CAAC;IAC1C;IACA,IAAIE,IAAI,CAACI,OAAO,KAAKF,SAAS,EAAE;MAC5B,IAAI,CAACE,OAAO,GAAGT,aAAa,CAACK,IAAI,CAACI,OAAO,CAAC;IAC9C;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
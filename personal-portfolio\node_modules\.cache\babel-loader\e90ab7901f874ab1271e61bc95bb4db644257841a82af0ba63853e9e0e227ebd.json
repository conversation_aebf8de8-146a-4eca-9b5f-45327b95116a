{"ast": null, "code": "import { Parallax } from \"./Parallax.js\";\nexport class HoverEvent {\n  constructor() {\n    this.enable = false;\n    this.mode = [];\n    this.parallax = new Parallax();\n  }\n  load(data) {\n    if (!data) {\n      return;\n    }\n    if (data.enable !== undefined) {\n      this.enable = data.enable;\n    }\n    if (data.mode !== undefined) {\n      this.mode = data.mode;\n    }\n    this.parallax.load(data.parallax);\n  }\n}", "map": {"version": 3, "names": ["Parallax", "HoverEvent", "constructor", "enable", "mode", "parallax", "load", "data", "undefined"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/engine/browser/Options/Classes/Interactivity/Events/HoverEvent.js"], "sourcesContent": ["import { Parallax } from \"./Parallax.js\";\nexport class HoverEvent {\n    constructor() {\n        this.enable = false;\n        this.mode = [];\n        this.parallax = new Parallax();\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.enable !== undefined) {\n            this.enable = data.enable;\n        }\n        if (data.mode !== undefined) {\n            this.mode = data.mode;\n        }\n        this.parallax.load(data.parallax);\n    }\n}\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,eAAe;AACxC,OAAO,MAAMC,UAAU,CAAC;EACpBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,MAAM,GAAG,KAAK;IACnB,IAAI,CAACC,IAAI,GAAG,EAAE;IACd,IAAI,CAACC,QAAQ,GAAG,IAAIL,QAAQ,CAAC,CAAC;EAClC;EACAM,IAAIA,CAACC,IAAI,EAAE;IACP,IAAI,CAACA,IAAI,EAAE;MACP;IACJ;IACA,IAAIA,IAAI,CAACJ,MAAM,KAAKK,SAAS,EAAE;MAC3B,IAAI,CAACL,MAAM,GAAGI,IAAI,CAACJ,MAAM;IAC7B;IACA,IAAII,IAAI,CAACH,IAAI,KAAKI,SAAS,EAAE;MACzB,IAAI,CAACJ,IAAI,GAAGG,IAAI,CAACH,IAAI;IACzB;IACA,IAAI,CAACC,QAAQ,CAACC,IAAI,CAACC,IAAI,CAACF,QAAQ,CAAC;EACrC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
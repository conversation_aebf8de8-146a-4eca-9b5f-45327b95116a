{"ast": null, "code": "import { TwinkleValues } from \"./TwinkleValues.js\";\nexport class Twinkle {\n  constructor() {\n    this.lines = new TwinkleValues();\n    this.particles = new TwinkleValues();\n  }\n  load(data) {\n    if (!data) {\n      return;\n    }\n    this.lines.load(data.lines);\n    this.particles.load(data.particles);\n  }\n}", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "Twinkle", "constructor", "lines", "particles", "load", "data"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/updater-twinkle/browser/Options/Classes/Twinkle.js"], "sourcesContent": ["import { TwinkleValues } from \"./TwinkleValues.js\";\nexport class Twinkle {\n    constructor() {\n        this.lines = new TwinkleValues();\n        this.particles = new TwinkleValues();\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        this.lines.load(data.lines);\n        this.particles.load(data.particles);\n    }\n}\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,oBAAoB;AAClD,OAAO,MAAMC,OAAO,CAAC;EACjBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,KAAK,GAAG,IAAIH,aAAa,CAAC,CAAC;IAChC,IAAI,CAACI,SAAS,GAAG,IAAIJ,aAAa,CAAC,CAAC;EACxC;EACAK,IAAIA,CAACC,IAAI,EAAE;IACP,IAAI,CAACA,IAAI,EAAE;MACP;IACJ;IACA,IAAI,CAACH,KAAK,CAACE,IAAI,CAACC,IAAI,CAACH,KAAK,CAAC;IAC3B,IAAI,CAACC,SAAS,CAACC,IAAI,CAACC,IAAI,CAACF,SAAS,CAAC;EACvC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
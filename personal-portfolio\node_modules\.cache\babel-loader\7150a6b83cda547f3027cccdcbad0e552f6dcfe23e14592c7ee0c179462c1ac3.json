{"ast": null, "code": "import { Connector } from \"./Connector.js\";\nexport async function loadExternalConnectInteraction(engine, refresh = true) {\n  await engine.addInteractor(\"externalConnect\", container => {\n    return Promise.resolve(new Connector(container));\n  }, refresh);\n}\nexport * from \"./Options/Classes/Connect.js\";\nexport * from \"./Options/Classes/ConnectLinks.js\";\nexport * from \"./Options/Interfaces/IConnect.js\";\nexport * from \"./Options/Interfaces/IConnectLinks.js\";", "map": {"version": 3, "names": ["Connector", "loadExternalConnectInteraction", "engine", "refresh", "addInteractor", "container", "Promise", "resolve"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/interaction-external-connect/browser/index.js"], "sourcesContent": ["import { Connector } from \"./Connector.js\";\nexport async function loadExternalConnectInteraction(engine, refresh = true) {\n    await engine.addInteractor(\"externalConnect\", container => {\n        return Promise.resolve(new Connector(container));\n    }, refresh);\n}\nexport * from \"./Options/Classes/Connect.js\";\nexport * from \"./Options/Classes/ConnectLinks.js\";\nexport * from \"./Options/Interfaces/IConnect.js\";\nexport * from \"./Options/Interfaces/IConnectLinks.js\";\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,gBAAgB;AAC1C,OAAO,eAAeC,8BAA8BA,CAACC,MAAM,EAAEC,OAAO,GAAG,IAAI,EAAE;EACzE,MAAMD,MAAM,CAACE,aAAa,CAAC,iBAAiB,EAAEC,SAAS,IAAI;IACvD,OAAOC,OAAO,CAACC,OAAO,CAAC,IAAIP,SAAS,CAACK,SAAS,CAAC,CAAC;EACpD,CAAC,EAAEF,OAAO,CAAC;AACf;AACA,cAAc,8BAA8B;AAC5C,cAAc,mCAAmC;AACjD,cAAc,kCAAkC;AAChD,cAAc,uCAAuC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
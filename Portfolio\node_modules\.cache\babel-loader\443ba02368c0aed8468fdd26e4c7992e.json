{"ast": null, "code": "import { ValueWithRandom } from \"../../ValueWithRandom\";\nexport class SplitRate extends ValueWithRandom {\n  constructor() {\n    super();\n    this.value = {\n      min: 4,\n      max: 9\n    };\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Options/Classes/Particles/Destroy/SplitRate.js"], "names": ["ValueWithRandom", "SplitRate", "constructor", "value", "min", "max"], "mappings": "AAAA,SAASA,eAAT,QAAgC,uBAAhC;AACA,OAAO,MAAMC,SAAN,SAAwBD,eAAxB,CAAwC;AAC3CE,EAAAA,WAAW,GAAG;AACV;AACA,SAAKC,KAAL,GAAa;AAAEC,MAAAA,GAAG,EAAE,CAAP;AAAUC,MAAAA,GAAG,EAAE;AAAf,KAAb;AACH;;AAJ0C", "sourcesContent": ["import { ValueWithRandom } from \"../../ValueWithRandom\";\nexport class SplitRate extends ValueWithRandom {\n    constructor() {\n        super();\n        this.value = { min: 4, max: 9 };\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
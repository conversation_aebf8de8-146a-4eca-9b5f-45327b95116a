{"ast": null, "code": "import { ParticlesInteractorBase, getDistances, getRangeValue } from \"@tsparticles/engine\";\nconst attractFactor = 1000,\n  identity = 1;\nexport class Attractor extends ParticlesInteractorBase {\n  constructor(container) {\n    super(container);\n  }\n  clear() {}\n  init() {}\n  interact(p1) {\n    const container = this.container;\n    if (p1.attractDistance === undefined) {\n      p1.attractDistance = getRangeValue(p1.options.move.attract.distance) * container.retina.pixelRatio;\n    }\n    const distance = p1.attractDistance,\n      pos1 = p1.getPosition(),\n      query = container.particles.quadTree.queryCircle(pos1, distance);\n    for (const p2 of query) {\n      if (p1 === p2 || !p2.options.move.attract.enable || p2.destroyed || p2.spawning) {\n        continue;\n      }\n      const pos2 = p2.getPosition(),\n        {\n          dx,\n          dy\n        } = getDistances(pos1, pos2),\n        rotate = p1.options.move.attract.rotate,\n        ax = dx / (rotate.x * attractFactor),\n        ay = dy / (rotate.y * attractFactor),\n        p1Factor = p2.size.value / p1.size.value,\n        p2Factor = identity / p1Factor;\n      p1.velocity.x -= ax * p1Factor;\n      p1.velocity.y -= ay * p1Factor;\n      p2.velocity.x += ax * p2Factor;\n      p2.velocity.y += ay * p2Factor;\n    }\n  }\n  isEnabled(particle) {\n    return particle.options.move.attract.enable;\n  }\n  reset() {}\n}", "map": {"version": 3, "names": ["ParticlesInteractorBase", "getDistances", "getRangeValue", "attractFactor", "identity", "Attractor", "constructor", "container", "clear", "init", "interact", "p1", "attractDistance", "undefined", "options", "move", "attract", "distance", "retina", "pixelRatio", "pos1", "getPosition", "query", "particles", "quadTree", "queryCircle", "p2", "enable", "destroyed", "spawning", "pos2", "dx", "dy", "rotate", "ax", "x", "ay", "y", "p1Factor", "size", "value", "p2Factor", "velocity", "isEnabled", "particle", "reset"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/interaction-particles-attract/browser/Attractor.js"], "sourcesContent": ["import { ParticlesInteractorBase, getDistances, getRangeValue, } from \"@tsparticles/engine\";\nconst attractFactor = 1000, identity = 1;\nexport class Attractor extends ParticlesInteractorBase {\n    constructor(container) {\n        super(container);\n    }\n    clear() {\n    }\n    init() {\n    }\n    interact(p1) {\n        const container = this.container;\n        if (p1.attractDistance === undefined) {\n            p1.attractDistance = getRangeValue(p1.options.move.attract.distance) * container.retina.pixelRatio;\n        }\n        const distance = p1.attractDistance, pos1 = p1.getPosition(), query = container.particles.quadTree.queryCircle(pos1, distance);\n        for (const p2 of query) {\n            if (p1 === p2 || !p2.options.move.attract.enable || p2.destroyed || p2.spawning) {\n                continue;\n            }\n            const pos2 = p2.getPosition(), { dx, dy } = getDistances(pos1, pos2), rotate = p1.options.move.attract.rotate, ax = dx / (rotate.x * attractFactor), ay = dy / (rotate.y * attractFactor), p1Factor = p2.size.value / p1.size.value, p2Factor = identity / p1Factor;\n            p1.velocity.x -= ax * p1Factor;\n            p1.velocity.y -= ay * p1Factor;\n            p2.velocity.x += ax * p2Factor;\n            p2.velocity.y += ay * p2Factor;\n        }\n    }\n    isEnabled(particle) {\n        return particle.options.move.attract.enable;\n    }\n    reset() {\n    }\n}\n"], "mappings": "AAAA,SAASA,uBAAuB,EAAEC,YAAY,EAAEC,aAAa,QAAS,qBAAqB;AAC3F,MAAMC,aAAa,GAAG,IAAI;EAAEC,QAAQ,GAAG,CAAC;AACxC,OAAO,MAAMC,SAAS,SAASL,uBAAuB,CAAC;EACnDM,WAAWA,CAACC,SAAS,EAAE;IACnB,KAAK,CAACA,SAAS,CAAC;EACpB;EACAC,KAAKA,CAAA,EAAG,CACR;EACAC,IAAIA,CAAA,EAAG,CACP;EACAC,QAAQA,CAACC,EAAE,EAAE;IACT,MAAMJ,SAAS,GAAG,IAAI,CAACA,SAAS;IAChC,IAAII,EAAE,CAACC,eAAe,KAAKC,SAAS,EAAE;MAClCF,EAAE,CAACC,eAAe,GAAGV,aAAa,CAACS,EAAE,CAACG,OAAO,CAACC,IAAI,CAACC,OAAO,CAACC,QAAQ,CAAC,GAAGV,SAAS,CAACW,MAAM,CAACC,UAAU;IACtG;IACA,MAAMF,QAAQ,GAAGN,EAAE,CAACC,eAAe;MAAEQ,IAAI,GAAGT,EAAE,CAACU,WAAW,CAAC,CAAC;MAAEC,KAAK,GAAGf,SAAS,CAACgB,SAAS,CAACC,QAAQ,CAACC,WAAW,CAACL,IAAI,EAAEH,QAAQ,CAAC;IAC9H,KAAK,MAAMS,EAAE,IAAIJ,KAAK,EAAE;MACpB,IAAIX,EAAE,KAAKe,EAAE,IAAI,CAACA,EAAE,CAACZ,OAAO,CAACC,IAAI,CAACC,OAAO,CAACW,MAAM,IAAID,EAAE,CAACE,SAAS,IAAIF,EAAE,CAACG,QAAQ,EAAE;QAC7E;MACJ;MACA,MAAMC,IAAI,GAAGJ,EAAE,CAACL,WAAW,CAAC,CAAC;QAAE;UAAEU,EAAE;UAAEC;QAAG,CAAC,GAAG/B,YAAY,CAACmB,IAAI,EAAEU,IAAI,CAAC;QAAEG,MAAM,GAAGtB,EAAE,CAACG,OAAO,CAACC,IAAI,CAACC,OAAO,CAACiB,MAAM;QAAEC,EAAE,GAAGH,EAAE,IAAIE,MAAM,CAACE,CAAC,GAAGhC,aAAa,CAAC;QAAEiC,EAAE,GAAGJ,EAAE,IAAIC,MAAM,CAACI,CAAC,GAAGlC,aAAa,CAAC;QAAEmC,QAAQ,GAAGZ,EAAE,CAACa,IAAI,CAACC,KAAK,GAAG7B,EAAE,CAAC4B,IAAI,CAACC,KAAK;QAAEC,QAAQ,GAAGrC,QAAQ,GAAGkC,QAAQ;MACnQ3B,EAAE,CAAC+B,QAAQ,CAACP,CAAC,IAAID,EAAE,GAAGI,QAAQ;MAC9B3B,EAAE,CAAC+B,QAAQ,CAACL,CAAC,IAAID,EAAE,GAAGE,QAAQ;MAC9BZ,EAAE,CAACgB,QAAQ,CAACP,CAAC,IAAID,EAAE,GAAGO,QAAQ;MAC9Bf,EAAE,CAACgB,QAAQ,CAACL,CAAC,IAAID,EAAE,GAAGK,QAAQ;IAClC;EACJ;EACAE,SAASA,CAACC,QAAQ,EAAE;IAChB,OAAOA,QAAQ,CAAC9B,OAAO,CAACC,IAAI,CAACC,OAAO,CAACW,MAAM;EAC/C;EACAkB,KAAKA,CAAA,EAAG,CACR;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
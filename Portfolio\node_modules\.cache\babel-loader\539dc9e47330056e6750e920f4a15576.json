{"ast": null, "code": "export class Vector {\n  constructor(x, y) {\n    let defX, defY;\n\n    if (y === undefined) {\n      if (typeof x === \"number\") {\n        throw new Error(\"tsParticles - Vector not initialized correctly\");\n      }\n\n      const coords = x;\n      [defX, defY] = [coords.x, coords.y];\n    } else {\n      [defX, defY] = [x, y];\n    }\n\n    this.x = defX;\n    this.y = defY;\n  }\n\n  static clone(source) {\n    return Vector.create(source.x, source.y);\n  }\n\n  static create(x, y) {\n    return new Vector(x, y);\n  }\n\n  static get origin() {\n    return Vector.create(0, 0);\n  }\n\n  get angle() {\n    return Math.atan2(this.y, this.x);\n  }\n\n  set angle(angle) {\n    this.updateFromAngle(angle, this.length);\n  }\n\n  get length() {\n    return Math.sqrt(this.x ** 2 + this.y ** 2);\n  }\n\n  set length(length) {\n    this.updateFromAngle(this.angle, length);\n  }\n\n  add(v) {\n    return Vector.create(this.x + v.x, this.y + v.y);\n  }\n\n  addTo(v) {\n    this.x += v.x;\n    this.y += v.y;\n  }\n\n  sub(v) {\n    return Vector.create(this.x - v.x, this.y - v.y);\n  }\n\n  subFrom(v) {\n    this.x -= v.x;\n    this.y -= v.y;\n  }\n\n  mult(n) {\n    return Vector.create(this.x * n, this.y * n);\n  }\n\n  multTo(n) {\n    this.x *= n;\n    this.y *= n;\n  }\n\n  div(n) {\n    return Vector.create(this.x / n, this.y / n);\n  }\n\n  divTo(n) {\n    this.x /= n;\n    this.y /= n;\n  }\n\n  distanceTo(v) {\n    return this.sub(v).length;\n  }\n\n  getLengthSq() {\n    return this.x ** 2 + this.y ** 2;\n  }\n\n  distanceToSq(v) {\n    return this.sub(v).getLengthSq();\n  }\n\n  manhattanDistanceTo(v) {\n    return Math.abs(v.x - this.x) + Math.abs(v.y - this.y);\n  }\n\n  copy() {\n    return Vector.clone(this);\n  }\n\n  setTo(velocity) {\n    this.x = velocity.x;\n    this.y = velocity.y;\n  }\n\n  rotate(angle) {\n    return Vector.create(this.x * Math.cos(angle) - this.y * Math.sin(angle), this.x * Math.sin(angle) + this.y * Math.cos(angle));\n  }\n\n  updateFromAngle(angle, length) {\n    this.x = Math.cos(angle) * length;\n    this.y = Math.sin(angle) * length;\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Core/Utils/Vector.js"], "names": ["Vector", "constructor", "x", "y", "defX", "defY", "undefined", "Error", "coords", "clone", "source", "create", "origin", "angle", "Math", "atan2", "updateFromAngle", "length", "sqrt", "add", "v", "addTo", "sub", "subFrom", "mult", "n", "multTo", "div", "divTo", "distanceTo", "getLengthSq", "distanceToSq", "manhattanDistanceTo", "abs", "copy", "setTo", "velocity", "rotate", "cos", "sin"], "mappings": "AAAA,OAAO,MAAMA,MAAN,CAAa;AAChBC,EAAAA,WAAW,CAACC,CAAD,EAAIC,CAAJ,EAAO;AACd,QAAIC,IAAJ,EAAUC,IAAV;;AACA,QAAIF,CAAC,KAAKG,SAAV,EAAqB;AACjB,UAAI,OAAOJ,CAAP,KAAa,QAAjB,EAA2B;AACvB,cAAM,IAAIK,KAAJ,CAAU,gDAAV,CAAN;AACH;;AACD,YAAMC,MAAM,GAAGN,CAAf;AACA,OAACE,IAAD,EAAOC,IAAP,IAAe,CAACG,MAAM,CAACN,CAAR,EAAWM,MAAM,CAACL,CAAlB,CAAf;AACH,KAND,MAOK;AACD,OAACC,IAAD,EAAOC,IAAP,IAAe,CAACH,CAAD,EAAIC,CAAJ,CAAf;AACH;;AACD,SAAKD,CAAL,GAASE,IAAT;AACA,SAAKD,CAAL,GAASE,IAAT;AACH;;AACW,SAALI,KAAK,CAACC,MAAD,EAAS;AACjB,WAAOV,MAAM,CAACW,MAAP,CAAcD,MAAM,CAACR,CAArB,EAAwBQ,MAAM,CAACP,CAA/B,CAAP;AACH;;AACY,SAANQ,MAAM,CAACT,CAAD,EAAIC,CAAJ,EAAO;AAChB,WAAO,IAAIH,MAAJ,CAAWE,CAAX,EAAcC,CAAd,CAAP;AACH;;AACgB,aAANS,MAAM,GAAG;AAChB,WAAOZ,MAAM,CAACW,MAAP,CAAc,CAAd,EAAiB,CAAjB,CAAP;AACH;;AACQ,MAALE,KAAK,GAAG;AACR,WAAOC,IAAI,CAACC,KAAL,CAAW,KAAKZ,CAAhB,EAAmB,KAAKD,CAAxB,CAAP;AACH;;AACQ,MAALW,KAAK,CAACA,KAAD,EAAQ;AACb,SAAKG,eAAL,CAAqBH,KAArB,EAA4B,KAAKI,MAAjC;AACH;;AACS,MAANA,MAAM,GAAG;AACT,WAAOH,IAAI,CAACI,IAAL,CAAU,KAAKhB,CAAL,IAAU,CAAV,GAAc,KAAKC,CAAL,IAAU,CAAlC,CAAP;AACH;;AACS,MAANc,MAAM,CAACA,MAAD,EAAS;AACf,SAAKD,eAAL,CAAqB,KAAKH,KAA1B,EAAiCI,MAAjC;AACH;;AACDE,EAAAA,GAAG,CAACC,CAAD,EAAI;AACH,WAAOpB,MAAM,CAACW,MAAP,CAAc,KAAKT,CAAL,GAASkB,CAAC,CAAClB,CAAzB,EAA4B,KAAKC,CAAL,GAASiB,CAAC,CAACjB,CAAvC,CAAP;AACH;;AACDkB,EAAAA,KAAK,CAACD,CAAD,EAAI;AACL,SAAKlB,CAAL,IAAUkB,CAAC,CAAClB,CAAZ;AACA,SAAKC,CAAL,IAAUiB,CAAC,CAACjB,CAAZ;AACH;;AACDmB,EAAAA,GAAG,CAACF,CAAD,EAAI;AACH,WAAOpB,MAAM,CAACW,MAAP,CAAc,KAAKT,CAAL,GAASkB,CAAC,CAAClB,CAAzB,EAA4B,KAAKC,CAAL,GAASiB,CAAC,CAACjB,CAAvC,CAAP;AACH;;AACDoB,EAAAA,OAAO,CAACH,CAAD,EAAI;AACP,SAAKlB,CAAL,IAAUkB,CAAC,CAAClB,CAAZ;AACA,SAAKC,CAAL,IAAUiB,CAAC,CAACjB,CAAZ;AACH;;AACDqB,EAAAA,IAAI,CAACC,CAAD,EAAI;AACJ,WAAOzB,MAAM,CAACW,MAAP,CAAc,KAAKT,CAAL,GAASuB,CAAvB,EAA0B,KAAKtB,CAAL,GAASsB,CAAnC,CAAP;AACH;;AACDC,EAAAA,MAAM,CAACD,CAAD,EAAI;AACN,SAAKvB,CAAL,IAAUuB,CAAV;AACA,SAAKtB,CAAL,IAAUsB,CAAV;AACH;;AACDE,EAAAA,GAAG,CAACF,CAAD,EAAI;AACH,WAAOzB,MAAM,CAACW,MAAP,CAAc,KAAKT,CAAL,GAASuB,CAAvB,EAA0B,KAAKtB,CAAL,GAASsB,CAAnC,CAAP;AACH;;AACDG,EAAAA,KAAK,CAACH,CAAD,EAAI;AACL,SAAKvB,CAAL,IAAUuB,CAAV;AACA,SAAKtB,CAAL,IAAUsB,CAAV;AACH;;AACDI,EAAAA,UAAU,CAACT,CAAD,EAAI;AACV,WAAO,KAAKE,GAAL,CAASF,CAAT,EAAYH,MAAnB;AACH;;AACDa,EAAAA,WAAW,GAAG;AACV,WAAO,KAAK5B,CAAL,IAAU,CAAV,GAAc,KAAKC,CAAL,IAAU,CAA/B;AACH;;AACD4B,EAAAA,YAAY,CAACX,CAAD,EAAI;AACZ,WAAO,KAAKE,GAAL,CAASF,CAAT,EAAYU,WAAZ,EAAP;AACH;;AACDE,EAAAA,mBAAmB,CAACZ,CAAD,EAAI;AACnB,WAAON,IAAI,CAACmB,GAAL,CAASb,CAAC,CAAClB,CAAF,GAAM,KAAKA,CAApB,IAAyBY,IAAI,CAACmB,GAAL,CAASb,CAAC,CAACjB,CAAF,GAAM,KAAKA,CAApB,CAAhC;AACH;;AACD+B,EAAAA,IAAI,GAAG;AACH,WAAOlC,MAAM,CAACS,KAAP,CAAa,IAAb,CAAP;AACH;;AACD0B,EAAAA,KAAK,CAACC,QAAD,EAAW;AACZ,SAAKlC,CAAL,GAASkC,QAAQ,CAAClC,CAAlB;AACA,SAAKC,CAAL,GAASiC,QAAQ,CAACjC,CAAlB;AACH;;AACDkC,EAAAA,MAAM,CAACxB,KAAD,EAAQ;AACV,WAAOb,MAAM,CAACW,MAAP,CAAc,KAAKT,CAAL,GAASY,IAAI,CAACwB,GAAL,CAASzB,KAAT,CAAT,GAA2B,KAAKV,CAAL,GAASW,IAAI,CAACyB,GAAL,CAAS1B,KAAT,CAAlD,EAAmE,KAAKX,CAAL,GAASY,IAAI,CAACyB,GAAL,CAAS1B,KAAT,CAAT,GAA2B,KAAKV,CAAL,GAASW,IAAI,CAACwB,GAAL,CAASzB,KAAT,CAAvG,CAAP;AACH;;AACDG,EAAAA,eAAe,CAACH,KAAD,EAAQI,MAAR,EAAgB;AAC3B,SAAKf,CAAL,GAASY,IAAI,CAACwB,GAAL,CAASzB,KAAT,IAAkBI,MAA3B;AACA,SAAKd,CAAL,GAASW,IAAI,CAACyB,GAAL,CAAS1B,KAAT,IAAkBI,MAA3B;AACH;;AA1Fe", "sourcesContent": ["export class Vector {\n    constructor(x, y) {\n        let defX, defY;\n        if (y === undefined) {\n            if (typeof x === \"number\") {\n                throw new Error(\"tsParticles - Vector not initialized correctly\");\n            }\n            const coords = x;\n            [defX, defY] = [coords.x, coords.y];\n        }\n        else {\n            [defX, defY] = [x, y];\n        }\n        this.x = defX;\n        this.y = defY;\n    }\n    static clone(source) {\n        return Vector.create(source.x, source.y);\n    }\n    static create(x, y) {\n        return new Vector(x, y);\n    }\n    static get origin() {\n        return Vector.create(0, 0);\n    }\n    get angle() {\n        return Math.atan2(this.y, this.x);\n    }\n    set angle(angle) {\n        this.updateFromAngle(angle, this.length);\n    }\n    get length() {\n        return Math.sqrt(this.x ** 2 + this.y ** 2);\n    }\n    set length(length) {\n        this.updateFromAngle(this.angle, length);\n    }\n    add(v) {\n        return Vector.create(this.x + v.x, this.y + v.y);\n    }\n    addTo(v) {\n        this.x += v.x;\n        this.y += v.y;\n    }\n    sub(v) {\n        return Vector.create(this.x - v.x, this.y - v.y);\n    }\n    subFrom(v) {\n        this.x -= v.x;\n        this.y -= v.y;\n    }\n    mult(n) {\n        return Vector.create(this.x * n, this.y * n);\n    }\n    multTo(n) {\n        this.x *= n;\n        this.y *= n;\n    }\n    div(n) {\n        return Vector.create(this.x / n, this.y / n);\n    }\n    divTo(n) {\n        this.x /= n;\n        this.y /= n;\n    }\n    distanceTo(v) {\n        return this.sub(v).length;\n    }\n    getLengthSq() {\n        return this.x ** 2 + this.y ** 2;\n    }\n    distanceToSq(v) {\n        return this.sub(v).getLengthSq();\n    }\n    manhattanDistanceTo(v) {\n        return Math.abs(v.x - this.x) + Math.abs(v.y - this.y);\n    }\n    copy() {\n        return Vector.clone(this);\n    }\n    setTo(velocity) {\n        this.x = velocity.x;\n        this.y = velocity.y;\n    }\n    rotate(angle) {\n        return Vector.create(this.x * Math.cos(angle) - this.y * Math.sin(angle), this.x * Math.sin(angle) + this.y * Math.cos(angle));\n    }\n    updateFromAngle(angle, length) {\n        this.x = Math.cos(angle) * length;\n        this.y = Math.sin(angle) * length;\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
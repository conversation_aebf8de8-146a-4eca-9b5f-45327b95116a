{"ast": null, "code": "export class Parallax {\n  constructor() {\n    this.enable = false;\n    this.force = 2;\n    this.smooth = 10;\n  }\n  load(data) {\n    if (!data) {\n      return;\n    }\n    if (data.enable !== undefined) {\n      this.enable = data.enable;\n    }\n    if (data.force !== undefined) {\n      this.force = data.force;\n    }\n    if (data.smooth !== undefined) {\n      this.smooth = data.smooth;\n    }\n  }\n}", "map": {"version": 3, "names": ["Parallax", "constructor", "enable", "force", "smooth", "load", "data", "undefined"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/engine/browser/Options/Classes/Interactivity/Events/Parallax.js"], "sourcesContent": ["export class Parallax {\n    constructor() {\n        this.enable = false;\n        this.force = 2;\n        this.smooth = 10;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.enable !== undefined) {\n            this.enable = data.enable;\n        }\n        if (data.force !== undefined) {\n            this.force = data.force;\n        }\n        if (data.smooth !== undefined) {\n            this.smooth = data.smooth;\n        }\n    }\n}\n"], "mappings": "AAAA,OAAO,MAAMA,QAAQ,CAAC;EAClBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,MAAM,GAAG,KAAK;IACnB,IAAI,CAACC,KAAK,GAAG,CAAC;IACd,IAAI,CAACC,MAAM,GAAG,EAAE;EACpB;EACAC,IAAIA,CAACC,IAAI,EAAE;IACP,IAAI,CAACA,IAAI,EAAE;MACP;IACJ;IACA,IAAIA,IAAI,CAACJ,MAAM,KAAKK,SAAS,EAAE;MAC3B,IAAI,CAACL,MAAM,GAAGI,IAAI,CAACJ,MAAM;IAC7B;IACA,IAAII,IAAI,CAACH,KAAK,KAAKI,SAAS,EAAE;MAC1B,IAAI,CAACJ,KAAK,GAAGG,IAAI,CAACH,KAAK;IAC3B;IACA,IAAIG,IAAI,CAACF,MAAM,KAAKG,SAAS,EAAE;MAC3B,IAAI,CAACH,MAAM,GAAGE,IAAI,CAACF,MAAM;IAC7B;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "export var DestroyType;\n(function (DestroyType) {\n  DestroyType[\"none\"] = \"none\";\n  DestroyType[\"max\"] = \"max\";\n  DestroyType[\"min\"] = \"min\";\n})(DestroyType || (DestroyType = {}));", "map": {"version": 3, "names": ["DestroyType"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/engine/browser/Enums/Types/DestroyType.js"], "sourcesContent": ["export var DestroyType;\n(function (DestroyType) {\n    DestroyType[\"none\"] = \"none\";\n    DestroyType[\"max\"] = \"max\";\n    DestroyType[\"min\"] = \"min\";\n})(DestroyType || (DestroyType = {}));\n"], "mappings": "AAAA,OAAO,IAAIA,WAAW;AACtB,CAAC,UAAUA,WAAW,EAAE;EACpBA,WAAW,CAAC,MAAM,CAAC,GAAG,MAAM;EAC5BA,WAAW,CAAC,KAAK,CAAC,GAAG,KAAK;EAC1BA,WAAW,CAAC,KAAK,CAAC,GAAG,KAAK;AAC9B,CAAC,EAAEA,WAAW,KAAKA,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "const double = 2,\n  doublePI = Math.PI * double,\n  minAngle = 0,\n  origin = {\n    x: 0,\n    y: 0\n  };\nexport function drawCircle(data) {\n  const {\n    context,\n    particle,\n    radius\n  } = data;\n  if (!particle.circleRange) {\n    particle.circleRange = {\n      min: minAngle,\n      max: doublePI\n    };\n  }\n  const circleRange = particle.circleRange;\n  context.arc(origin.x, origin.y, radius, circleRange.min, circleRange.max, false);\n}", "map": {"version": 3, "names": ["double", "doublePI", "Math", "PI", "minAngle", "origin", "x", "y", "drawCircle", "data", "context", "particle", "radius", "circleRange", "min", "max", "arc"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/shape-circle/browser/Utils.js"], "sourcesContent": ["const double = 2, doublePI = Math.PI * double, minAngle = 0, origin = { x: 0, y: 0 };\nexport function drawCircle(data) {\n    const { context, particle, radius } = data;\n    if (!particle.circleRange) {\n        particle.circleRange = { min: minAngle, max: doublePI };\n    }\n    const circleRange = particle.circleRange;\n    context.arc(origin.x, origin.y, radius, circleRange.min, circleRange.max, false);\n}\n"], "mappings": "AAAA,MAAMA,MAAM,GAAG,CAAC;EAAEC,QAAQ,GAAGC,IAAI,CAACC,EAAE,GAAGH,MAAM;EAAEI,QAAQ,GAAG,CAAC;EAAEC,MAAM,GAAG;IAAEC,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE;EAAE,CAAC;AACpF,OAAO,SAASC,UAAUA,CAACC,IAAI,EAAE;EAC7B,MAAM;IAAEC,OAAO;IAAEC,QAAQ;IAAEC;EAAO,CAAC,GAAGH,IAAI;EAC1C,IAAI,CAACE,QAAQ,CAACE,WAAW,EAAE;IACvBF,QAAQ,CAACE,WAAW,GAAG;MAAEC,GAAG,EAAEV,QAAQ;MAAEW,GAAG,EAAEd;IAAS,CAAC;EAC3D;EACA,MAAMY,WAAW,GAAGF,QAAQ,CAACE,WAAW;EACxCH,OAAO,CAACM,GAAG,CAACX,MAAM,CAACC,CAAC,EAAED,MAAM,CAACE,CAAC,EAAEK,MAAM,EAAEC,WAAW,CAACC,GAAG,EAAED,WAAW,CAACE,GAAG,EAAE,KAAK,CAAC;AACpF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
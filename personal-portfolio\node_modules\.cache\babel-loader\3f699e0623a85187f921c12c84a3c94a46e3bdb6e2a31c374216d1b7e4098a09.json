{"ast": null, "code": "import { TiltUpdater } from \"./TiltUpdater.js\";\nexport async function loadTiltUpdater(engine, refresh = true) {\n  await engine.addParticleUpdater(\"tilt\", container => {\n    return Promise.resolve(new TiltUpdater(container));\n  }, refresh);\n}", "map": {"version": 3, "names": ["TiltUpdater", "loadTiltUpdater", "engine", "refresh", "addParticleUpdater", "container", "Promise", "resolve"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/updater-tilt/browser/index.js"], "sourcesContent": ["import { TiltUpdater } from \"./TiltUpdater.js\";\nexport async function loadTiltUpdater(engine, refresh = true) {\n    await engine.addParticleUpdater(\"tilt\", container => {\n        return Promise.resolve(new TiltUpdater(container));\n    }, refresh);\n}\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,kBAAkB;AAC9C,OAAO,eAAeC,eAAeA,CAACC,MAAM,EAAEC,OAAO,GAAG,IAAI,EAAE;EAC1D,MAAMD,MAAM,CAACE,kBAAkB,CAAC,MAAM,EAAEC,SAAS,IAAI;IACjD,OAAOC,OAAO,CAACC,OAAO,CAAC,IAAIP,WAAW,CAACK,SAAS,CAAC,CAAC;EACtD,CAAC,EAAEF,OAAO,CAAC;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { ExternalInteractorBase, isInArray, millisecondsToSeconds, mouseMoveEvent } from \"@tsparticles/engine\";\nimport { clickAttract, hoverAttract } from \"./Utils.js\";\nimport { Attract } from \"./Options/Classes/Attract.js\";\nconst attractMode = \"attract\";\nexport class Attractor extends ExternalInteractorBase {\n  constructor(engine, container) {\n    super(container);\n    this._engine = engine;\n    if (!container.attract) {\n      container.attract = {\n        particles: []\n      };\n    }\n    this.handleClickMode = mode => {\n      const options = this.container.actualOptions,\n        attract = options.interactivity.modes.attract;\n      if (!attract || mode !== attractMode) {\n        return;\n      }\n      if (!container.attract) {\n        container.attract = {\n          particles: []\n        };\n      }\n      container.attract.clicking = true;\n      container.attract.count = 0;\n      for (const particle of container.attract.particles) {\n        if (!this.isEnabled(particle)) {\n          continue;\n        }\n        particle.velocity.setTo(particle.initialVelocity);\n      }\n      container.attract.particles = [];\n      container.attract.finish = false;\n      setTimeout(() => {\n        if (container.destroyed) {\n          return;\n        }\n        if (!container.attract) {\n          container.attract = {\n            particles: []\n          };\n        }\n        container.attract.clicking = false;\n      }, attract.duration * millisecondsToSeconds);\n    };\n  }\n  clear() {}\n  init() {\n    const container = this.container,\n      attract = container.actualOptions.interactivity.modes.attract;\n    if (!attract) {\n      return;\n    }\n    container.retina.attractModeDistance = attract.distance * container.retina.pixelRatio;\n  }\n  interact() {\n    const container = this.container,\n      options = container.actualOptions,\n      mouseMoveStatus = container.interactivity.status === mouseMoveEvent,\n      events = options.interactivity.events,\n      {\n        enable: hoverEnabled,\n        mode: hoverMode\n      } = events.onHover,\n      {\n        enable: clickEnabled,\n        mode: clickMode\n      } = events.onClick;\n    if (mouseMoveStatus && hoverEnabled && isInArray(attractMode, hoverMode)) {\n      hoverAttract(this.container, p => this.isEnabled(p));\n    } else if (clickEnabled && isInArray(attractMode, clickMode)) {\n      clickAttract(this.container, p => this.isEnabled(p));\n    }\n  }\n  isEnabled(particle) {\n    const container = this.container,\n      options = container.actualOptions,\n      mouse = container.interactivity.mouse,\n      events = (particle?.interactivity ?? options.interactivity).events;\n    if ((!mouse.position || !events.onHover.enable) && (!mouse.clickPosition || !events.onClick.enable)) {\n      return false;\n    }\n    const hoverMode = events.onHover.mode,\n      clickMode = events.onClick.mode;\n    return isInArray(attractMode, hoverMode) || isInArray(attractMode, clickMode);\n  }\n  loadModeOptions(options, ...sources) {\n    if (!options.attract) {\n      options.attract = new Attract();\n    }\n    for (const source of sources) {\n      options.attract.load(source?.attract);\n    }\n  }\n  reset() {}\n}", "map": {"version": 3, "names": ["ExternalInteractorBase", "isInArray", "millisecondsToSeconds", "mouseMoveEvent", "clickAttract", "hoverAttract", "Attract", "attractMode", "Attractor", "constructor", "engine", "container", "_engine", "attract", "particles", "handleClickMode", "mode", "options", "actualOptions", "interactivity", "modes", "clicking", "count", "particle", "isEnabled", "velocity", "setTo", "initialVelocity", "finish", "setTimeout", "destroyed", "duration", "clear", "init", "retina", "attractModeDistance", "distance", "pixelRatio", "interact", "mouseMoveStatus", "status", "events", "enable", "hoverEnabled", "hoverMode", "onHover", "clickEnabled", "clickMode", "onClick", "p", "mouse", "position", "clickPosition", "loadModeOptions", "sources", "source", "load", "reset"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/interaction-external-attract/browser/Attractor.js"], "sourcesContent": ["import { ExternalInteractorBase, isInArray, millisecondsToSeconds, mouseMoveEvent, } from \"@tsparticles/engine\";\nimport { clickAttract, hoverAttract } from \"./Utils.js\";\nimport { Attract } from \"./Options/Classes/Attract.js\";\nconst attractMode = \"attract\";\nexport class Attractor extends ExternalInteractorBase {\n    constructor(engine, container) {\n        super(container);\n        this._engine = engine;\n        if (!container.attract) {\n            container.attract = { particles: [] };\n        }\n        this.handleClickMode = (mode) => {\n            const options = this.container.actualOptions, attract = options.interactivity.modes.attract;\n            if (!attract || mode !== attractMode) {\n                return;\n            }\n            if (!container.attract) {\n                container.attract = { particles: [] };\n            }\n            container.attract.clicking = true;\n            container.attract.count = 0;\n            for (const particle of container.attract.particles) {\n                if (!this.isEnabled(particle)) {\n                    continue;\n                }\n                particle.velocity.setTo(particle.initialVelocity);\n            }\n            container.attract.particles = [];\n            container.attract.finish = false;\n            setTimeout(() => {\n                if (container.destroyed) {\n                    return;\n                }\n                if (!container.attract) {\n                    container.attract = { particles: [] };\n                }\n                container.attract.clicking = false;\n            }, attract.duration * millisecondsToSeconds);\n        };\n    }\n    clear() {\n    }\n    init() {\n        const container = this.container, attract = container.actualOptions.interactivity.modes.attract;\n        if (!attract) {\n            return;\n        }\n        container.retina.attractModeDistance = attract.distance * container.retina.pixelRatio;\n    }\n    interact() {\n        const container = this.container, options = container.actualOptions, mouseMoveStatus = container.interactivity.status === mouseMoveEvent, events = options.interactivity.events, { enable: hoverEnabled, mode: hoverMode } = events.onHover, { enable: clickEnabled, mode: clickMode } = events.onClick;\n        if (mouseMoveStatus && hoverEnabled && isInArray(attractMode, hoverMode)) {\n            hoverAttract(this.container, p => this.isEnabled(p));\n        }\n        else if (clickEnabled && isInArray(attractMode, clickMode)) {\n            clickAttract(this.container, p => this.isEnabled(p));\n        }\n    }\n    isEnabled(particle) {\n        const container = this.container, options = container.actualOptions, mouse = container.interactivity.mouse, events = (particle?.interactivity ?? options.interactivity).events;\n        if ((!mouse.position || !events.onHover.enable) && (!mouse.clickPosition || !events.onClick.enable)) {\n            return false;\n        }\n        const hoverMode = events.onHover.mode, clickMode = events.onClick.mode;\n        return isInArray(attractMode, hoverMode) || isInArray(attractMode, clickMode);\n    }\n    loadModeOptions(options, ...sources) {\n        if (!options.attract) {\n            options.attract = new Attract();\n        }\n        for (const source of sources) {\n            options.attract.load(source?.attract);\n        }\n    }\n    reset() {\n    }\n}\n"], "mappings": "AAAA,SAASA,sBAAsB,EAAEC,SAAS,EAAEC,qBAAqB,EAAEC,cAAc,QAAS,qBAAqB;AAC/G,SAASC,YAAY,EAAEC,YAAY,QAAQ,YAAY;AACvD,SAASC,OAAO,QAAQ,8BAA8B;AACtD,MAAMC,WAAW,GAAG,SAAS;AAC7B,OAAO,MAAMC,SAAS,SAASR,sBAAsB,CAAC;EAClDS,WAAWA,CAACC,MAAM,EAAEC,SAAS,EAAE;IAC3B,KAAK,CAACA,SAAS,CAAC;IAChB,IAAI,CAACC,OAAO,GAAGF,MAAM;IACrB,IAAI,CAACC,SAAS,CAACE,OAAO,EAAE;MACpBF,SAAS,CAACE,OAAO,GAAG;QAAEC,SAAS,EAAE;MAAG,CAAC;IACzC;IACA,IAAI,CAACC,eAAe,GAAIC,IAAI,IAAK;MAC7B,MAAMC,OAAO,GAAG,IAAI,CAACN,SAAS,CAACO,aAAa;QAAEL,OAAO,GAAGI,OAAO,CAACE,aAAa,CAACC,KAAK,CAACP,OAAO;MAC3F,IAAI,CAACA,OAAO,IAAIG,IAAI,KAAKT,WAAW,EAAE;QAClC;MACJ;MACA,IAAI,CAACI,SAAS,CAACE,OAAO,EAAE;QACpBF,SAAS,CAACE,OAAO,GAAG;UAAEC,SAAS,EAAE;QAAG,CAAC;MACzC;MACAH,SAAS,CAACE,OAAO,CAACQ,QAAQ,GAAG,IAAI;MACjCV,SAAS,CAACE,OAAO,CAACS,KAAK,GAAG,CAAC;MAC3B,KAAK,MAAMC,QAAQ,IAAIZ,SAAS,CAACE,OAAO,CAACC,SAAS,EAAE;QAChD,IAAI,CAAC,IAAI,CAACU,SAAS,CAACD,QAAQ,CAAC,EAAE;UAC3B;QACJ;QACAA,QAAQ,CAACE,QAAQ,CAACC,KAAK,CAACH,QAAQ,CAACI,eAAe,CAAC;MACrD;MACAhB,SAAS,CAACE,OAAO,CAACC,SAAS,GAAG,EAAE;MAChCH,SAAS,CAACE,OAAO,CAACe,MAAM,GAAG,KAAK;MAChCC,UAAU,CAAC,MAAM;QACb,IAAIlB,SAAS,CAACmB,SAAS,EAAE;UACrB;QACJ;QACA,IAAI,CAACnB,SAAS,CAACE,OAAO,EAAE;UACpBF,SAAS,CAACE,OAAO,GAAG;YAAEC,SAAS,EAAE;UAAG,CAAC;QACzC;QACAH,SAAS,CAACE,OAAO,CAACQ,QAAQ,GAAG,KAAK;MACtC,CAAC,EAAER,OAAO,CAACkB,QAAQ,GAAG7B,qBAAqB,CAAC;IAChD,CAAC;EACL;EACA8B,KAAKA,CAAA,EAAG,CACR;EACAC,IAAIA,CAAA,EAAG;IACH,MAAMtB,SAAS,GAAG,IAAI,CAACA,SAAS;MAAEE,OAAO,GAAGF,SAAS,CAACO,aAAa,CAACC,aAAa,CAACC,KAAK,CAACP,OAAO;IAC/F,IAAI,CAACA,OAAO,EAAE;MACV;IACJ;IACAF,SAAS,CAACuB,MAAM,CAACC,mBAAmB,GAAGtB,OAAO,CAACuB,QAAQ,GAAGzB,SAAS,CAACuB,MAAM,CAACG,UAAU;EACzF;EACAC,QAAQA,CAAA,EAAG;IACP,MAAM3B,SAAS,GAAG,IAAI,CAACA,SAAS;MAAEM,OAAO,GAAGN,SAAS,CAACO,aAAa;MAAEqB,eAAe,GAAG5B,SAAS,CAACQ,aAAa,CAACqB,MAAM,KAAKrC,cAAc;MAAEsC,MAAM,GAAGxB,OAAO,CAACE,aAAa,CAACsB,MAAM;MAAE;QAAEC,MAAM,EAAEC,YAAY;QAAE3B,IAAI,EAAE4B;MAAU,CAAC,GAAGH,MAAM,CAACI,OAAO;MAAE;QAAEH,MAAM,EAAEI,YAAY;QAAE9B,IAAI,EAAE+B;MAAU,CAAC,GAAGN,MAAM,CAACO,OAAO;IACvS,IAAIT,eAAe,IAAII,YAAY,IAAI1C,SAAS,CAACM,WAAW,EAAEqC,SAAS,CAAC,EAAE;MACtEvC,YAAY,CAAC,IAAI,CAACM,SAAS,EAAEsC,CAAC,IAAI,IAAI,CAACzB,SAAS,CAACyB,CAAC,CAAC,CAAC;IACxD,CAAC,MACI,IAAIH,YAAY,IAAI7C,SAAS,CAACM,WAAW,EAAEwC,SAAS,CAAC,EAAE;MACxD3C,YAAY,CAAC,IAAI,CAACO,SAAS,EAAEsC,CAAC,IAAI,IAAI,CAACzB,SAAS,CAACyB,CAAC,CAAC,CAAC;IACxD;EACJ;EACAzB,SAASA,CAACD,QAAQ,EAAE;IAChB,MAAMZ,SAAS,GAAG,IAAI,CAACA,SAAS;MAAEM,OAAO,GAAGN,SAAS,CAACO,aAAa;MAAEgC,KAAK,GAAGvC,SAAS,CAACQ,aAAa,CAAC+B,KAAK;MAAET,MAAM,GAAG,CAAClB,QAAQ,EAAEJ,aAAa,IAAIF,OAAO,CAACE,aAAa,EAAEsB,MAAM;IAC9K,IAAI,CAAC,CAACS,KAAK,CAACC,QAAQ,IAAI,CAACV,MAAM,CAACI,OAAO,CAACH,MAAM,MAAM,CAACQ,KAAK,CAACE,aAAa,IAAI,CAACX,MAAM,CAACO,OAAO,CAACN,MAAM,CAAC,EAAE;MACjG,OAAO,KAAK;IAChB;IACA,MAAME,SAAS,GAAGH,MAAM,CAACI,OAAO,CAAC7B,IAAI;MAAE+B,SAAS,GAAGN,MAAM,CAACO,OAAO,CAAChC,IAAI;IACtE,OAAOf,SAAS,CAACM,WAAW,EAAEqC,SAAS,CAAC,IAAI3C,SAAS,CAACM,WAAW,EAAEwC,SAAS,CAAC;EACjF;EACAM,eAAeA,CAACpC,OAAO,EAAE,GAAGqC,OAAO,EAAE;IACjC,IAAI,CAACrC,OAAO,CAACJ,OAAO,EAAE;MAClBI,OAAO,CAACJ,OAAO,GAAG,IAAIP,OAAO,CAAC,CAAC;IACnC;IACA,KAAK,MAAMiD,MAAM,IAAID,OAAO,EAAE;MAC1BrC,OAAO,CAACJ,OAAO,CAAC2C,IAAI,CAACD,MAAM,EAAE1C,OAAO,CAAC;IACzC;EACJ;EACA4C,KAAKA,CAAA,EAAG,CACR;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { OutMode, calculateBounds } from \"@tsparticles/engine\";\nimport { bounceHorizontal, bounceVertical } from \"./Utils.js\";\nexport class BounceOutMode {\n  constructor(container) {\n    this.container = container;\n    this.modes = [OutMode.bounce, OutMode.split];\n  }\n  update(particle, direction, delta, outMode) {\n    if (!this.modes.includes(outMode)) {\n      return;\n    }\n    const container = this.container;\n    let handled = false;\n    for (const [, plugin] of container.plugins) {\n      if (plugin.particleBounce !== undefined) {\n        handled = plugin.particleBounce(particle, delta, direction);\n      }\n      if (handled) {\n        break;\n      }\n    }\n    if (handled) {\n      return;\n    }\n    const pos = particle.getPosition(),\n      offset = particle.offset,\n      size = particle.getRadius(),\n      bounds = calculateBounds(pos, size),\n      canvasSize = container.canvas.size;\n    bounceHorizontal({\n      particle,\n      outMode,\n      direction,\n      bounds,\n      canvasSize,\n      offset,\n      size\n    });\n    bounceVertical({\n      particle,\n      outMode,\n      direction,\n      bounds,\n      canvasSize,\n      offset,\n      size\n    });\n  }\n}", "map": {"version": 3, "names": ["OutMode", "calculateBounds", "bounceHorizontal", "bounceVertical", "BounceOutMode", "constructor", "container", "modes", "bounce", "split", "update", "particle", "direction", "delta", "outMode", "includes", "handled", "plugin", "plugins", "particleBounce", "undefined", "pos", "getPosition", "offset", "size", "getRadius", "bounds", "canvasSize", "canvas"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/updater-out-modes/browser/BounceOutMode.js"], "sourcesContent": ["import { OutMode, calculateBounds, } from \"@tsparticles/engine\";\nimport { bounceHorizontal, bounceVertical } from \"./Utils.js\";\nexport class BounceOutMode {\n    constructor(container) {\n        this.container = container;\n        this.modes = [\n            OutMode.bounce,\n            OutMode.split,\n        ];\n    }\n    update(particle, direction, delta, outMode) {\n        if (!this.modes.includes(outMode)) {\n            return;\n        }\n        const container = this.container;\n        let handled = false;\n        for (const [, plugin] of container.plugins) {\n            if (plugin.particleBounce !== undefined) {\n                handled = plugin.particleBounce(particle, delta, direction);\n            }\n            if (handled) {\n                break;\n            }\n        }\n        if (handled) {\n            return;\n        }\n        const pos = particle.getPosition(), offset = particle.offset, size = particle.getRadius(), bounds = calculateBounds(pos, size), canvasSize = container.canvas.size;\n        bounceHorizontal({ particle, outMode, direction, bounds, canvasSize, offset, size });\n        bounceVertical({ particle, outMode, direction, bounds, canvasSize, offset, size });\n    }\n}\n"], "mappings": "AAAA,SAASA,OAAO,EAAEC,eAAe,QAAS,qBAAqB;AAC/D,SAASC,gBAAgB,EAAEC,cAAc,QAAQ,YAAY;AAC7D,OAAO,MAAMC,aAAa,CAAC;EACvBC,WAAWA,CAACC,SAAS,EAAE;IACnB,IAAI,CAACA,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,KAAK,GAAG,CACTP,OAAO,CAACQ,MAAM,EACdR,OAAO,CAACS,KAAK,CAChB;EACL;EACAC,MAAMA,CAACC,QAAQ,EAAEC,SAAS,EAAEC,KAAK,EAAEC,OAAO,EAAE;IACxC,IAAI,CAAC,IAAI,CAACP,KAAK,CAACQ,QAAQ,CAACD,OAAO,CAAC,EAAE;MAC/B;IACJ;IACA,MAAMR,SAAS,GAAG,IAAI,CAACA,SAAS;IAChC,IAAIU,OAAO,GAAG,KAAK;IACnB,KAAK,MAAM,GAAGC,MAAM,CAAC,IAAIX,SAAS,CAACY,OAAO,EAAE;MACxC,IAAID,MAAM,CAACE,cAAc,KAAKC,SAAS,EAAE;QACrCJ,OAAO,GAAGC,MAAM,CAACE,cAAc,CAACR,QAAQ,EAAEE,KAAK,EAAED,SAAS,CAAC;MAC/D;MACA,IAAII,OAAO,EAAE;QACT;MACJ;IACJ;IACA,IAAIA,OAAO,EAAE;MACT;IACJ;IACA,MAAMK,GAAG,GAAGV,QAAQ,CAACW,WAAW,CAAC,CAAC;MAAEC,MAAM,GAAGZ,QAAQ,CAACY,MAAM;MAAEC,IAAI,GAAGb,QAAQ,CAACc,SAAS,CAAC,CAAC;MAAEC,MAAM,GAAGzB,eAAe,CAACoB,GAAG,EAAEG,IAAI,CAAC;MAAEG,UAAU,GAAGrB,SAAS,CAACsB,MAAM,CAACJ,IAAI;IAClKtB,gBAAgB,CAAC;MAAES,QAAQ;MAAEG,OAAO;MAAEF,SAAS;MAAEc,MAAM;MAAEC,UAAU;MAAEJ,MAAM;MAAEC;IAAK,CAAC,CAAC;IACpFrB,cAAc,CAAC;MAAEQ,QAAQ;MAAEG,OAAO;MAAEF,SAAS;MAAEc,MAAM;MAAEC,UAAU;MAAEJ,MAAM;MAAEC;IAAK,CAAC,CAAC;EACtF;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
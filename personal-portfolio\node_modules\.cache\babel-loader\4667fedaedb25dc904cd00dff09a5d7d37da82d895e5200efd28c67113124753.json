{"ast": null, "code": "export var AbsorberClickMode;\n(function (AbsorberClickMode) {\n  AbsorberClickMode[\"absorber\"] = \"absorber\";\n})(AbsorberClickMode || (AbsorberClickMode = {}));", "map": {"version": 3, "names": ["AbsorberClickMode"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/plugin-absorbers/browser/Enums/AbsorberClickMode.js"], "sourcesContent": ["export var AbsorberClickMode;\n(function (AbsorberClickMode) {\n    AbsorberClickMode[\"absorber\"] = \"absorber\";\n})(AbsorberClickMode || (AbsorberClickMode = {}));\n"], "mappings": "AAAA,OAAO,IAAIA,iBAAiB;AAC5B,CAAC,UAAUA,iBAAiB,EAAE;EAC1BA,iBAAiB,CAAC,UAAU,CAAC,GAAG,UAAU;AAC9C,CAAC,EAAEA,iBAAiB,KAAKA,iBAAiB,GAAG,CAAC,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
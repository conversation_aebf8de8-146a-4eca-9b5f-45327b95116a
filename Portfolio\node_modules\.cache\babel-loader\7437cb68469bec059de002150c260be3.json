{"ast": null, "code": "export class Push {\n  constructor() {\n    this.default = true;\n    this.groups = [];\n    this.quantity = 4;\n  }\n\n  get particles_nb() {\n    return this.quantity;\n  }\n\n  set particles_nb(value) {\n    this.quantity = value;\n  }\n\n  load(data) {\n    var _a;\n\n    if (data === undefined) {\n      return;\n    }\n\n    if (data.default !== undefined) {\n      this.default = data.default;\n    }\n\n    if (data.groups !== undefined) {\n      this.groups = data.groups.map(t => t);\n    }\n\n    if (!this.groups.length) {\n      this.default = true;\n    }\n\n    const quantity = (_a = data.quantity) !== null && _a !== void 0 ? _a : data.particles_nb;\n\n    if (quantity !== undefined) {\n      this.quantity = quantity;\n    }\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Options/Classes/Interactivity/Modes/Push.js"], "names": ["<PERSON><PERSON>", "constructor", "default", "groups", "quantity", "particles_nb", "value", "load", "data", "_a", "undefined", "map", "t", "length"], "mappings": "AAAA,OAAO,MAAMA,IAAN,CAAW;AACdC,EAAAA,WAAW,GAAG;AACV,SAAKC,OAAL,GAAe,IAAf;AACA,SAAKC,MAAL,GAAc,EAAd;AACA,SAAKC,QAAL,GAAgB,CAAhB;AACH;;AACe,MAAZC,YAAY,GAAG;AACf,WAAO,KAAKD,QAAZ;AACH;;AACe,MAAZC,YAAY,CAACC,KAAD,EAAQ;AACpB,SAAKF,QAAL,GAAgBE,KAAhB;AACH;;AACDC,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,QAAIC,EAAJ;;AACA,QAAID,IAAI,KAAKE,SAAb,EAAwB;AACpB;AACH;;AACD,QAAIF,IAAI,CAACN,OAAL,KAAiBQ,SAArB,EAAgC;AAC5B,WAAKR,OAAL,GAAeM,IAAI,CAACN,OAApB;AACH;;AACD,QAAIM,IAAI,CAACL,MAAL,KAAgBO,SAApB,EAA+B;AAC3B,WAAKP,MAAL,GAAcK,IAAI,CAACL,MAAL,CAAYQ,GAAZ,CAAiBC,CAAD,IAAOA,CAAvB,CAAd;AACH;;AACD,QAAI,CAAC,KAAKT,MAAL,CAAYU,MAAjB,EAAyB;AACrB,WAAKX,OAAL,GAAe,IAAf;AACH;;AACD,UAAME,QAAQ,GAAG,CAACK,EAAE,GAAGD,IAAI,CAACJ,QAAX,MAAyB,IAAzB,IAAiCK,EAAE,KAAK,KAAK,CAA7C,GAAiDA,EAAjD,GAAsDD,IAAI,CAACH,YAA5E;;AACA,QAAID,QAAQ,KAAKM,SAAjB,EAA4B;AACxB,WAAKN,QAAL,GAAgBA,QAAhB;AACH;AACJ;;AA9Ba", "sourcesContent": ["export class Push {\n    constructor() {\n        this.default = true;\n        this.groups = [];\n        this.quantity = 4;\n    }\n    get particles_nb() {\n        return this.quantity;\n    }\n    set particles_nb(value) {\n        this.quantity = value;\n    }\n    load(data) {\n        var _a;\n        if (data === undefined) {\n            return;\n        }\n        if (data.default !== undefined) {\n            this.default = data.default;\n        }\n        if (data.groups !== undefined) {\n            this.groups = data.groups.map((t) => t);\n        }\n        if (!this.groups.length) {\n            this.default = true;\n        }\n        const quantity = (_a = data.quantity) !== null && _a !== void 0 ? _a : data.particles_nb;\n        if (quantity !== undefined) {\n            this.quantity = quantity;\n        }\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
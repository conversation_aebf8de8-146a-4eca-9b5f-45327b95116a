{"ast": null, "code": "export class ClickEvent {\n  constructor() {\n    this.enable = false;\n    this.mode = [];\n  }\n  load(data) {\n    if (!data) {\n      return;\n    }\n    if (data.enable !== undefined) {\n      this.enable = data.enable;\n    }\n    if (data.mode !== undefined) {\n      this.mode = data.mode;\n    }\n  }\n}", "map": {"version": 3, "names": ["ClickEvent", "constructor", "enable", "mode", "load", "data", "undefined"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/engine/browser/Options/Classes/Interactivity/Events/ClickEvent.js"], "sourcesContent": ["export class ClickEvent {\n    constructor() {\n        this.enable = false;\n        this.mode = [];\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.enable !== undefined) {\n            this.enable = data.enable;\n        }\n        if (data.mode !== undefined) {\n            this.mode = data.mode;\n        }\n    }\n}\n"], "mappings": "AAAA,OAAO,MAAMA,UAAU,CAAC;EACpBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,MAAM,GAAG,KAAK;IACnB,IAAI,CAACC,IAAI,GAAG,EAAE;EAClB;EACAC,IAAIA,CAACC,IAAI,EAAE;IACP,IAAI,CAACA,IAAI,EAAE;MACP;IACJ;IACA,IAAIA,IAAI,CAACH,MAAM,KAAKI,SAAS,EAAE;MAC3B,IAAI,CAACJ,MAAM,GAAGG,IAAI,CAACH,MAAM;IAC7B;IACA,IAAIG,IAAI,CAACF,IAAI,KAAKG,SAAS,EAAE;MACzB,IAAI,CAACH,IAAI,GAAGE,IAAI,CAACF,IAAI;IACzB;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
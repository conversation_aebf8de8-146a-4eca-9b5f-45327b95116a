{"ast": null, "code": "import { getRangeValue } from \"@tsparticles/engine\";\nimport { drawPolygon } from \"./Utils.js\";\nconst defaultSides = 5;\nexport class PolygonDrawerBase {\n  draw(data) {\n    const {\n        particle,\n        radius\n      } = data,\n      start = this.getCenter(particle, radius),\n      side = this.getSidesData(particle, radius);\n    drawPolygon(data, start, side);\n  }\n  getSidesCount(particle) {\n    const polygon = particle.shapeData;\n    return Math.round(getRangeValue(polygon?.sides ?? defaultSides));\n  }\n}", "map": {"version": 3, "names": ["getRangeValue", "drawPolygon", "defaultSides", "PolygonDrawerBase", "draw", "data", "particle", "radius", "start", "getCenter", "side", "getSidesData", "getSidesCount", "polygon", "shapeData", "Math", "round", "sides"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/shape-polygon/browser/PolygonDrawerBase.js"], "sourcesContent": ["import { getRangeValue, } from \"@tsparticles/engine\";\nimport { drawPolygon } from \"./Utils.js\";\nconst defaultSides = 5;\nexport class PolygonDrawerBase {\n    draw(data) {\n        const { particle, radius } = data, start = this.getCenter(particle, radius), side = this.getSidesData(particle, radius);\n        drawPolygon(data, start, side);\n    }\n    getSidesCount(particle) {\n        const polygon = particle.shapeData;\n        return Math.round(getRangeValue(polygon?.sides ?? defaultSides));\n    }\n}\n"], "mappings": "AAAA,SAASA,aAAa,QAAS,qBAAqB;AACpD,SAASC,WAAW,QAAQ,YAAY;AACxC,MAAMC,YAAY,GAAG,CAAC;AACtB,OAAO,MAAMC,iBAAiB,CAAC;EAC3BC,IAAIA,CAACC,IAAI,EAAE;IACP,MAAM;QAAEC,QAAQ;QAAEC;MAAO,CAAC,GAAGF,IAAI;MAAEG,KAAK,GAAG,IAAI,CAACC,SAAS,CAACH,QAAQ,EAAEC,MAAM,CAAC;MAAEG,IAAI,GAAG,IAAI,CAACC,YAAY,CAACL,QAAQ,EAAEC,MAAM,CAAC;IACvHN,WAAW,CAACI,IAAI,EAAEG,KAAK,EAAEE,IAAI,CAAC;EAClC;EACAE,aAAaA,CAACN,QAAQ,EAAE;IACpB,MAAMO,OAAO,GAAGP,QAAQ,CAACQ,SAAS;IAClC,OAAOC,IAAI,CAACC,KAAK,CAAChB,aAAa,CAACa,OAAO,EAAEI,KAAK,IAAIf,YAAY,CAAC,CAAC;EACpE;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
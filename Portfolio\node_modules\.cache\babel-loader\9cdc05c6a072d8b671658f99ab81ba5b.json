{"ast": null, "code": "import { Range } from \"./Range\";\nexport class Rectangle extends Range {\n  constructor(x, y, width, height) {\n    super(x, y);\n    this.size = {\n      height: height,\n      width: width\n    };\n  }\n\n  contains(point) {\n    const w = this.size.width;\n    const h = this.size.height;\n    const pos = this.position;\n    return point.x >= pos.x && point.x <= pos.x + w && point.y >= pos.y && point.y <= pos.y + h;\n  }\n\n  intersects(range) {\n    const rect = range;\n    const circle = range;\n    const w = this.size.width;\n    const h = this.size.height;\n    const pos1 = this.position;\n    const pos2 = range.position;\n\n    if (circle.radius !== undefined) {\n      return circle.intersects(this);\n    } else if (rect.size !== undefined) {\n      const size2 = rect.size;\n      const w2 = size2.width;\n      const h2 = size2.height;\n      return pos2.x < pos1.x + w && pos2.x + w2 > pos1.x && pos2.y < pos1.y + h && pos2.y + h2 > pos1.y;\n    }\n\n    return false;\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Core/Utils/Rectangle.js"], "names": ["Range", "Rectangle", "constructor", "x", "y", "width", "height", "size", "contains", "point", "w", "h", "pos", "position", "intersects", "range", "rect", "circle", "pos1", "pos2", "radius", "undefined", "size2", "w2", "h2"], "mappings": "AAAA,SAASA,KAAT,QAAsB,SAAtB;AACA,OAAO,MAAMC,SAAN,SAAwBD,KAAxB,CAA8B;AACjCE,EAAAA,WAAW,CAACC,CAAD,EAAIC,CAAJ,EAAOC,KAAP,EAAcC,MAAd,EAAsB;AAC7B,UAAMH,CAAN,EAASC,CAAT;AACA,SAAKG,IAAL,GAAY;AACRD,MAAAA,MAAM,EAAEA,MADA;AAERD,MAAAA,KAAK,EAAEA;AAFC,KAAZ;AAIH;;AACDG,EAAAA,QAAQ,CAACC,KAAD,EAAQ;AACZ,UAAMC,CAAC,GAAG,KAAKH,IAAL,CAAUF,KAApB;AACA,UAAMM,CAAC,GAAG,KAAKJ,IAAL,CAAUD,MAApB;AACA,UAAMM,GAAG,GAAG,KAAKC,QAAjB;AACA,WAAOJ,KAAK,CAACN,CAAN,IAAWS,GAAG,CAACT,CAAf,IAAoBM,KAAK,CAACN,CAAN,IAAWS,GAAG,CAACT,CAAJ,GAAQO,CAAvC,IAA4CD,KAAK,CAACL,CAAN,IAAWQ,GAAG,CAACR,CAA3D,IAAgEK,KAAK,CAACL,CAAN,IAAWQ,GAAG,CAACR,CAAJ,GAAQO,CAA1F;AACH;;AACDG,EAAAA,UAAU,CAACC,KAAD,EAAQ;AACd,UAAMC,IAAI,GAAGD,KAAb;AACA,UAAME,MAAM,GAAGF,KAAf;AACA,UAAML,CAAC,GAAG,KAAKH,IAAL,CAAUF,KAApB;AACA,UAAMM,CAAC,GAAG,KAAKJ,IAAL,CAAUD,MAApB;AACA,UAAMY,IAAI,GAAG,KAAKL,QAAlB;AACA,UAAMM,IAAI,GAAGJ,KAAK,CAACF,QAAnB;;AACA,QAAII,MAAM,CAACG,MAAP,KAAkBC,SAAtB,EAAiC;AAC7B,aAAOJ,MAAM,CAACH,UAAP,CAAkB,IAAlB,CAAP;AACH,KAFD,MAGK,IAAIE,IAAI,CAACT,IAAL,KAAcc,SAAlB,EAA6B;AAC9B,YAAMC,KAAK,GAAGN,IAAI,CAACT,IAAnB;AACA,YAAMgB,EAAE,GAAGD,KAAK,CAACjB,KAAjB;AACA,YAAMmB,EAAE,GAAGF,KAAK,CAAChB,MAAjB;AACA,aAAOa,IAAI,CAAChB,CAAL,GAASe,IAAI,CAACf,CAAL,GAASO,CAAlB,IAAuBS,IAAI,CAAChB,CAAL,GAASoB,EAAT,GAAcL,IAAI,CAACf,CAA1C,IAA+CgB,IAAI,CAACf,CAAL,GAASc,IAAI,CAACd,CAAL,GAASO,CAAjE,IAAsEQ,IAAI,CAACf,CAAL,GAASoB,EAAT,GAAcN,IAAI,CAACd,CAAhG;AACH;;AACD,WAAO,KAAP;AACH;;AA/BgC", "sourcesContent": ["import { Range } from \"./Range\";\nexport class Rectangle extends Range {\n    constructor(x, y, width, height) {\n        super(x, y);\n        this.size = {\n            height: height,\n            width: width,\n        };\n    }\n    contains(point) {\n        const w = this.size.width;\n        const h = this.size.height;\n        const pos = this.position;\n        return point.x >= pos.x && point.x <= pos.x + w && point.y >= pos.y && point.y <= pos.y + h;\n    }\n    intersects(range) {\n        const rect = range;\n        const circle = range;\n        const w = this.size.width;\n        const h = this.size.height;\n        const pos1 = this.position;\n        const pos2 = range.position;\n        if (circle.radius !== undefined) {\n            return circle.intersects(this);\n        }\n        else if (rect.size !== undefined) {\n            const size2 = rect.size;\n            const w2 = size2.width;\n            const h2 = size2.height;\n            return pos2.x < pos1.x + w && pos2.x + w2 > pos1.x && pos2.y < pos1.y + h && pos2.y + h2 > pos1.y;\n        }\n        return false;\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
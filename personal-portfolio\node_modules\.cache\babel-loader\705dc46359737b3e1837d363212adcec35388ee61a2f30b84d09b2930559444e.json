{"ast": null, "code": "import { AnimationOptions, RangedAnimationOptions } from \"./AnimationOptions.js\";\nimport { setRangeValue } from \"../../Utils/NumberUtils.js\";\nexport class ValueWithRandom {\n  constructor() {\n    this.value = 0;\n  }\n  load(data) {\n    if (!data) {\n      return;\n    }\n    if (data.value !== undefined) {\n      this.value = setRangeValue(data.value);\n    }\n  }\n}\nexport class AnimationValueWithRandom extends ValueWithRandom {\n  constructor() {\n    super();\n    this.animation = new AnimationOptions();\n  }\n  load(data) {\n    super.load(data);\n    if (!data) {\n      return;\n    }\n    const animation = data.animation;\n    if (animation !== undefined) {\n      this.animation.load(animation);\n    }\n  }\n}\nexport class RangedAnimationValueWithRandom extends AnimationValueWithRandom {\n  constructor() {\n    super();\n    this.animation = new RangedAnimationOptions();\n  }\n  load(data) {\n    super.load(data);\n  }\n}", "map": {"version": 3, "names": ["AnimationOptions", "RangedAnimationOptions", "setRangeValue", "ValueWithRandom", "constructor", "value", "load", "data", "undefined", "AnimationValueWithRandom", "animation", "RangedAnimationValueWithRandom"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/engine/browser/Options/Classes/ValueWithRandom.js"], "sourcesContent": ["import { AnimationOptions, RangedAnimationOptions } from \"./AnimationOptions.js\";\nimport { setRangeValue } from \"../../Utils/NumberUtils.js\";\nexport class ValueWithRandom {\n    constructor() {\n        this.value = 0;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.value !== undefined) {\n            this.value = setRangeValue(data.value);\n        }\n    }\n}\nexport class AnimationValueWithRandom extends ValueWithRandom {\n    constructor() {\n        super();\n        this.animation = new AnimationOptions();\n    }\n    load(data) {\n        super.load(data);\n        if (!data) {\n            return;\n        }\n        const animation = data.animation;\n        if (animation !== undefined) {\n            this.animation.load(animation);\n        }\n    }\n}\nexport class RangedAnimationValueWithRandom extends AnimationValueWithRandom {\n    constructor() {\n        super();\n        this.animation = new RangedAnimationOptions();\n    }\n    load(data) {\n        super.load(data);\n    }\n}\n"], "mappings": "AAAA,SAASA,gBAAgB,EAAEC,sBAAsB,QAAQ,uBAAuB;AAChF,SAASC,aAAa,QAAQ,4BAA4B;AAC1D,OAAO,MAAMC,eAAe,CAAC;EACzBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,KAAK,GAAG,CAAC;EAClB;EACAC,IAAIA,CAACC,IAAI,EAAE;IACP,IAAI,CAACA,IAAI,EAAE;MACP;IACJ;IACA,IAAIA,IAAI,CAACF,KAAK,KAAKG,SAAS,EAAE;MAC1B,IAAI,CAACH,KAAK,GAAGH,aAAa,CAACK,IAAI,CAACF,KAAK,CAAC;IAC1C;EACJ;AACJ;AACA,OAAO,MAAMI,wBAAwB,SAASN,eAAe,CAAC;EAC1DC,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,CAAC;IACP,IAAI,CAACM,SAAS,GAAG,IAAIV,gBAAgB,CAAC,CAAC;EAC3C;EACAM,IAAIA,CAACC,IAAI,EAAE;IACP,KAAK,CAACD,IAAI,CAACC,IAAI,CAAC;IAChB,IAAI,CAACA,IAAI,EAAE;MACP;IACJ;IACA,MAAMG,SAAS,GAAGH,IAAI,CAACG,SAAS;IAChC,IAAIA,SAAS,KAAKF,SAAS,EAAE;MACzB,IAAI,CAACE,SAAS,CAACJ,IAAI,CAACI,SAAS,CAAC;IAClC;EACJ;AACJ;AACA,OAAO,MAAMC,8BAA8B,SAASF,wBAAwB,CAAC;EACzEL,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,CAAC;IACP,IAAI,CAACM,SAAS,GAAG,IAAIT,sBAAsB,CAAC,CAAC;EACjD;EACAK,IAAIA,CAACC,IAAI,EAAE;IACP,KAAK,CAACD,IAAI,CAACC,IAAI,CAAC;EACpB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ImagePreloaderPlugin = void 0;
const Preload_1 = require("./Options/Classes/Preload");
class ImagePreloaderPlugin {
    constructor(engine) {
        this.id = "imagePreloader";
        this._engine = engine;
    }
    getPlugin() {
        return {};
    }
    loadOptions(options, source) {
        if (!source || !source.preload) {
            return;
        }
        if (!options.preload) {
            options.preload = [];
        }
        const preloadOptions = options.preload;
        for (const item of source.preload) {
            const existing = preloadOptions.find((t) => t.name === item.name || t.src === item.src);
            if (existing) {
                existing.load(item);
            }
            else {
                const preload = new Preload_1.Preload();
                preload.load(item);
                preloadOptions.push(preload);
            }
        }
    }
    needsPlugin() {
        return true;
    }
}
exports.ImagePreloaderPlugin = ImagePreloaderPlugin;

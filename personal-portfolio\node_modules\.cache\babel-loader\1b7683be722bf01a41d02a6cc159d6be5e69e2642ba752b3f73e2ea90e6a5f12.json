{"ast": null, "code": "import { PolygonDrawerBase } from \"./PolygonDrawerBase.js\";\nconst sidesCenterFactor = 3.5,\n  yFactor = 2.66,\n  sidesFactor = 3;\nexport class PolygonDrawer extends PolygonDrawerBase {\n  constructor() {\n    super(...arguments);\n    this.validTypes = [\"polygon\"];\n  }\n  getCenter(particle, radius) {\n    return {\n      x: -radius / (particle.sides / sidesCenterFactor),\n      y: -radius / (yFactor / sidesCenterFactor)\n    };\n  }\n  getSidesData(particle, radius) {\n    const sides = particle.sides;\n    return {\n      count: {\n        denominator: 1,\n        numerator: sides\n      },\n      length: radius * yFactor / (sides / sidesFactor)\n    };\n  }\n}", "map": {"version": 3, "names": ["PolygonDrawerBase", "sidesCenterFactor", "yFactor", "sidesFactor", "PolygonDrawer", "constructor", "arguments", "validTypes", "getCenter", "particle", "radius", "x", "sides", "y", "getSidesData", "count", "denominator", "numerator", "length"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/shape-polygon/browser/PolygonDrawer.js"], "sourcesContent": ["import { PolygonDrawerBase } from \"./PolygonDrawerBase.js\";\nconst sidesCenterFactor = 3.5, yFactor = 2.66, sidesFactor = 3;\nexport class PolygonDrawer extends PolygonDrawerBase {\n    constructor() {\n        super(...arguments);\n        this.validTypes = [\"polygon\"];\n    }\n    getCenter(particle, radius) {\n        return {\n            x: -radius / (particle.sides / sidesCenterFactor),\n            y: -radius / (yFactor / sidesCenterFactor),\n        };\n    }\n    getSidesData(particle, radius) {\n        const sides = particle.sides;\n        return {\n            count: {\n                denominator: 1,\n                numerator: sides,\n            },\n            length: (radius * yFactor) / (sides / sidesFactor),\n        };\n    }\n}\n"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,wBAAwB;AAC1D,MAAMC,iBAAiB,GAAG,GAAG;EAAEC,OAAO,GAAG,IAAI;EAAEC,WAAW,GAAG,CAAC;AAC9D,OAAO,MAAMC,aAAa,SAASJ,iBAAiB,CAAC;EACjDK,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGC,SAAS,CAAC;IACnB,IAAI,CAACC,UAAU,GAAG,CAAC,SAAS,CAAC;EACjC;EACAC,SAASA,CAACC,QAAQ,EAAEC,MAAM,EAAE;IACxB,OAAO;MACHC,CAAC,EAAE,CAACD,MAAM,IAAID,QAAQ,CAACG,KAAK,GAAGX,iBAAiB,CAAC;MACjDY,CAAC,EAAE,CAACH,MAAM,IAAIR,OAAO,GAAGD,iBAAiB;IAC7C,CAAC;EACL;EACAa,YAAYA,CAACL,QAAQ,EAAEC,MAAM,EAAE;IAC3B,MAAME,KAAK,GAAGH,QAAQ,CAACG,KAAK;IAC5B,OAAO;MACHG,KAAK,EAAE;QACHC,WAAW,EAAE,CAAC;QACdC,SAAS,EAAEL;MACf,CAAC;MACDM,MAAM,EAAGR,MAAM,GAAGR,OAAO,IAAKU,KAAK,GAAGT,WAAW;IACrD,CAAC;EACL;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
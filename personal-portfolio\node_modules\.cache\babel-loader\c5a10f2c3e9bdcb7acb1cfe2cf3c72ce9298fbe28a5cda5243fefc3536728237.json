{"ast": null, "code": "import { PolygonDrawerBase } from \"./PolygonDrawerBase\";\nexport class PolygonDrawer extends PolygonDrawerBase {\n  getCenter(particle, radius) {\n    return {\n      x: -radius / (particle.sides / 3.5),\n      y: -radius / (2.66 / 3.5)\n    };\n  }\n  getSidesData(particle, radius) {\n    const sides = particle.sides;\n    return {\n      count: {\n        denominator: 1,\n        numerator: sides\n      },\n      length: radius * 2.66 / (sides / 3)\n    };\n  }\n}", "map": {"version": 3, "names": ["PolygonDrawerBase", "PolygonDrawer", "getCenter", "particle", "radius", "x", "sides", "y", "getSidesData", "count", "denominator", "numerator", "length"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/tsparticles-shape-polygon/esm/PolygonDrawer.js"], "sourcesContent": ["import { PolygonDrawerBase } from \"./PolygonDrawerBase\";\nexport class PolygonDrawer extends PolygonDrawerBase {\n    getCenter(particle, radius) {\n        return {\n            x: -radius / (particle.sides / 3.5),\n            y: -radius / (2.66 / 3.5),\n        };\n    }\n    getSidesData(particle, radius) {\n        const sides = particle.sides;\n        return {\n            count: {\n                denominator: 1,\n                numerator: sides,\n            },\n            length: (radius * 2.66) / (sides / 3),\n        };\n    }\n}\n"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,qBAAqB;AACvD,OAAO,MAAMC,aAAa,SAASD,iBAAiB,CAAC;EACjDE,SAASA,CAACC,QAAQ,EAAEC,MAAM,EAAE;IACxB,OAAO;MACHC,CAAC,EAAE,CAACD,MAAM,IAAID,QAAQ,CAACG,KAAK,GAAG,GAAG,CAAC;MACnCC,CAAC,EAAE,CAACH,MAAM,IAAI,IAAI,GAAG,GAAG;IAC5B,CAAC;EACL;EACAI,YAAYA,CAACL,QAAQ,EAAEC,MAAM,EAAE;IAC3B,MAAME,KAAK,GAAGH,QAAQ,CAACG,KAAK;IAC5B,OAAO;MACHG,KAAK,EAAE;QACHC,WAAW,EAAE,CAAC;QACdC,SAAS,EAAEL;MACf,CAAC;MACDM,MAAM,EAAGR,MAAM,GAAG,IAAI,IAAKE,KAAK,GAAG,CAAC;IACxC,CAAC;EACL;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
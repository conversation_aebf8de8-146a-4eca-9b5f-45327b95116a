{"ast": null, "code": "import { BounceOutMode } from \"./BounceOutMode\";\nimport { DestroyOutMode } from \"./DestroyOutMode\";\nimport { NoneOutMode } from \"./NoneOutMode\";\nimport { OutOutMode } from \"./OutOutMode\";\nexport class OutOfCanvasUpdater {\n  constructor(container) {\n    this.container = container;\n    this._updateOutMode = (particle, delta, outMode, direction) => {\n      for (const updater of this.updaters) {\n        updater.update(particle, direction, delta, outMode);\n      }\n    };\n    this.updaters = [new BounceOutMode(container), new DestroyOutMode(container), new OutOutMode(container), new NoneOutMode(container)];\n  }\n  init() {}\n  isEnabled(particle) {\n    return !particle.destroyed && !particle.spawning;\n  }\n  update(particle, delta) {\n    const outModes = particle.options.move.outModes;\n    this._updateOutMode(particle, delta, outModes.bottom ?? outModes.default, \"bottom\");\n    this._updateOutMode(particle, delta, outModes.left ?? outModes.default, \"left\");\n    this._updateOutMode(particle, delta, outModes.right ?? outModes.default, \"right\");\n    this._updateOutMode(particle, delta, outModes.top ?? outModes.default, \"top\");\n  }\n}", "map": {"version": 3, "names": ["BounceOutMode", "DestroyOutMode", "NoneOutMode", "OutOutMode", "OutOfCanvasUpdater", "constructor", "container", "_updateOutMode", "particle", "delta", "outMode", "direction", "updater", "updaters", "update", "init", "isEnabled", "destroyed", "spawning", "outModes", "options", "move", "bottom", "default", "left", "right", "top"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/tsparticles-updater-out-modes/esm/OutOfCanvasUpdater.js"], "sourcesContent": ["import { BounceOutMode } from \"./BounceOutMode\";\nimport { DestroyOutMode } from \"./DestroyOutMode\";\nimport { NoneOutMode } from \"./NoneOutMode\";\nimport { OutOutMode } from \"./OutOutMode\";\nexport class OutOfCanvasUpdater {\n    constructor(container) {\n        this.container = container;\n        this._updateOutMode = (particle, delta, outMode, direction) => {\n            for (const updater of this.updaters) {\n                updater.update(particle, direction, delta, outMode);\n            }\n        };\n        this.updaters = [\n            new BounceOutMode(container),\n            new DestroyOutMode(container),\n            new OutOutMode(container),\n            new NoneOutMode(container),\n        ];\n    }\n    init() {\n    }\n    isEnabled(particle) {\n        return !particle.destroyed && !particle.spawning;\n    }\n    update(particle, delta) {\n        const outModes = particle.options.move.outModes;\n        this._updateOutMode(particle, delta, outModes.bottom ?? outModes.default, \"bottom\");\n        this._updateOutMode(particle, delta, outModes.left ?? outModes.default, \"left\");\n        this._updateOutMode(particle, delta, outModes.right ?? outModes.default, \"right\");\n        this._updateOutMode(particle, delta, outModes.top ?? outModes.default, \"top\");\n    }\n}\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,UAAU,QAAQ,cAAc;AACzC,OAAO,MAAMC,kBAAkB,CAAC;EAC5BC,WAAWA,CAACC,SAAS,EAAE;IACnB,IAAI,CAACA,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,cAAc,GAAG,CAACC,QAAQ,EAAEC,KAAK,EAAEC,OAAO,EAAEC,SAAS,KAAK;MAC3D,KAAK,MAAMC,OAAO,IAAI,IAAI,CAACC,QAAQ,EAAE;QACjCD,OAAO,CAACE,MAAM,CAACN,QAAQ,EAAEG,SAAS,EAAEF,KAAK,EAAEC,OAAO,CAAC;MACvD;IACJ,CAAC;IACD,IAAI,CAACG,QAAQ,GAAG,CACZ,IAAIb,aAAa,CAACM,SAAS,CAAC,EAC5B,IAAIL,cAAc,CAACK,SAAS,CAAC,EAC7B,IAAIH,UAAU,CAACG,SAAS,CAAC,EACzB,IAAIJ,WAAW,CAACI,SAAS,CAAC,CAC7B;EACL;EACAS,IAAIA,CAAA,EAAG,CACP;EACAC,SAASA,CAACR,QAAQ,EAAE;IAChB,OAAO,CAACA,QAAQ,CAACS,SAAS,IAAI,CAACT,QAAQ,CAACU,QAAQ;EACpD;EACAJ,MAAMA,CAACN,QAAQ,EAAEC,KAAK,EAAE;IACpB,MAAMU,QAAQ,GAAGX,QAAQ,CAACY,OAAO,CAACC,IAAI,CAACF,QAAQ;IAC/C,IAAI,CAACZ,cAAc,CAACC,QAAQ,EAAEC,KAAK,EAAEU,QAAQ,CAACG,MAAM,IAAIH,QAAQ,CAACI,OAAO,EAAE,QAAQ,CAAC;IACnF,IAAI,CAAChB,cAAc,CAACC,QAAQ,EAAEC,KAAK,EAAEU,QAAQ,CAACK,IAAI,IAAIL,QAAQ,CAACI,OAAO,EAAE,MAAM,CAAC;IAC/E,IAAI,CAAChB,cAAc,CAACC,QAAQ,EAAEC,KAAK,EAAEU,QAAQ,CAACM,KAAK,IAAIN,QAAQ,CAACI,OAAO,EAAE,OAAO,CAAC;IACjF,IAAI,CAAChB,cAAc,CAACC,QAAQ,EAAEC,KAAK,EAAEU,QAAQ,CAACO,GAAG,IAAIP,QAAQ,CAACI,OAAO,EAAE,KAAK,CAAC;EACjF;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
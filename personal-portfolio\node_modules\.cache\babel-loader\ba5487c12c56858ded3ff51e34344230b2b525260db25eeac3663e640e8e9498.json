{"ast": null, "code": "import { errorPrefix, generatedAttribute } from \"./Utils/Constants.js\";\nimport { executeOnSingleOrMultiple, getLogger, itemFromSingleOrMultiple } from \"../Utils/Utils.js\";\nimport { Container } from \"./Container.js\";\nimport { EventDispatcher } from \"../Utils/EventDispatcher.js\";\nimport { EventType } from \"../Enums/Types/EventType.js\";\nimport { getRandom } from \"../Utils/NumberUtils.js\";\nasync function getItemsFromInitializer(container, map, initializers, force = false) {\n  let res = map.get(container);\n  if (!res || force) {\n    res = await Promise.all([...initializers.values()].map(t => t(container)));\n    map.set(container, res);\n  }\n  return res;\n}\nasync function getDataFromUrl(data) {\n  const url = itemFromSingleOrMultiple(data.url, data.index);\n  if (!url) {\n    return data.fallback;\n  }\n  const response = await fetch(url);\n  if (response.ok) {\n    return await response.json();\n  }\n  getLogger().error(`${errorPrefix} ${response.status} while retrieving config file`);\n  return data.fallback;\n}\nconst generatedTrue = \"true\",\n  generatedFalse = \"false\",\n  canvasTag = \"canvas\",\n  getCanvasFromContainer = domContainer => {\n    let canvasEl;\n    if (domContainer instanceof HTMLCanvasElement || domContainer.tagName.toLowerCase() === canvasTag) {\n      canvasEl = domContainer;\n      if (!canvasEl.dataset[generatedAttribute]) {\n        canvasEl.dataset[generatedAttribute] = generatedFalse;\n      }\n    } else {\n      const existingCanvases = domContainer.getElementsByTagName(canvasTag);\n      if (existingCanvases.length) {\n        const firstIndex = 0;\n        canvasEl = existingCanvases[firstIndex];\n        canvasEl.dataset[generatedAttribute] = generatedFalse;\n      } else {\n        canvasEl = document.createElement(canvasTag);\n        canvasEl.dataset[generatedAttribute] = generatedTrue;\n        domContainer.appendChild(canvasEl);\n      }\n    }\n    const fullPercent = \"100%\";\n    if (!canvasEl.style.width) {\n      canvasEl.style.width = fullPercent;\n    }\n    if (!canvasEl.style.height) {\n      canvasEl.style.height = fullPercent;\n    }\n    return canvasEl;\n  },\n  getDomContainer = (id, source) => {\n    let domContainer = source ?? document.getElementById(id);\n    if (domContainer) {\n      return domContainer;\n    }\n    domContainer = document.createElement(\"div\");\n    domContainer.id = id;\n    domContainer.dataset[generatedAttribute] = generatedTrue;\n    document.body.append(domContainer);\n    return domContainer;\n  };\nexport class Engine {\n  constructor() {\n    this._configs = new Map();\n    this._domArray = [];\n    this._eventDispatcher = new EventDispatcher();\n    this._initialized = false;\n    this.plugins = [];\n    this._initializers = {\n      interactors: new Map(),\n      movers: new Map(),\n      updaters: new Map()\n    };\n    this.interactors = new Map();\n    this.movers = new Map();\n    this.updaters = new Map();\n    this.presets = new Map();\n    this.effectDrawers = new Map();\n    this.shapeDrawers = new Map();\n    this.pathGenerators = new Map();\n  }\n  get configs() {\n    const res = {};\n    for (const [name, config] of this._configs) {\n      res[name] = config;\n    }\n    return res;\n  }\n  get items() {\n    return this._domArray;\n  }\n  get version() {\n    return \"3.5.0\";\n  }\n  addConfig(config) {\n    const key = config.key ?? config.name ?? \"default\";\n    this._configs.set(key, config);\n    this._eventDispatcher.dispatchEvent(EventType.configAdded, {\n      data: {\n        name: key,\n        config\n      }\n    });\n  }\n  async addEffect(effect, drawer, refresh = true) {\n    executeOnSingleOrMultiple(effect, type => {\n      if (!this.getEffectDrawer(type)) {\n        this.effectDrawers.set(type, drawer);\n      }\n    });\n    await this.refresh(refresh);\n  }\n  addEventListener(type, listener) {\n    this._eventDispatcher.addEventListener(type, listener);\n  }\n  async addInteractor(name, interactorInitializer, refresh = true) {\n    this._initializers.interactors.set(name, interactorInitializer);\n    await this.refresh(refresh);\n  }\n  async addMover(name, moverInitializer, refresh = true) {\n    this._initializers.movers.set(name, moverInitializer);\n    await this.refresh(refresh);\n  }\n  async addParticleUpdater(name, updaterInitializer, refresh = true) {\n    this._initializers.updaters.set(name, updaterInitializer);\n    await this.refresh(refresh);\n  }\n  async addPathGenerator(name, generator, refresh = true) {\n    if (!this.getPathGenerator(name)) {\n      this.pathGenerators.set(name, generator);\n    }\n    await this.refresh(refresh);\n  }\n  async addPlugin(plugin, refresh = true) {\n    if (!this.getPlugin(plugin.id)) {\n      this.plugins.push(plugin);\n    }\n    await this.refresh(refresh);\n  }\n  async addPreset(preset, options, override = false, refresh = true) {\n    if (override || !this.getPreset(preset)) {\n      this.presets.set(preset, options);\n    }\n    await this.refresh(refresh);\n  }\n  async addShape(drawer, refresh = true) {\n    for (const validType of drawer.validTypes) {\n      if (this.getShapeDrawer(validType)) {\n        continue;\n      }\n      this.shapeDrawers.set(validType, drawer);\n    }\n    await this.refresh(refresh);\n  }\n  clearPlugins(container) {\n    this.updaters.delete(container);\n    this.movers.delete(container);\n    this.interactors.delete(container);\n  }\n  dispatchEvent(type, args) {\n    this._eventDispatcher.dispatchEvent(type, args);\n  }\n  dom() {\n    return this.items;\n  }\n  domItem(index) {\n    return this.item(index);\n  }\n  async getAvailablePlugins(container) {\n    const res = new Map();\n    for (const plugin of this.plugins) {\n      if (plugin.needsPlugin(container.actualOptions)) {\n        res.set(plugin.id, await plugin.getPlugin(container));\n      }\n    }\n    return res;\n  }\n  getEffectDrawer(type) {\n    return this.effectDrawers.get(type);\n  }\n  async getInteractors(container, force = false) {\n    return getItemsFromInitializer(container, this.interactors, this._initializers.interactors, force);\n  }\n  async getMovers(container, force = false) {\n    return getItemsFromInitializer(container, this.movers, this._initializers.movers, force);\n  }\n  getPathGenerator(type) {\n    return this.pathGenerators.get(type);\n  }\n  getPlugin(plugin) {\n    return this.plugins.find(t => t.id === plugin);\n  }\n  getPreset(preset) {\n    return this.presets.get(preset);\n  }\n  getShapeDrawer(type) {\n    return this.shapeDrawers.get(type);\n  }\n  getSupportedEffects() {\n    return this.effectDrawers.keys();\n  }\n  getSupportedShapes() {\n    return this.shapeDrawers.keys();\n  }\n  async getUpdaters(container, force = false) {\n    return getItemsFromInitializer(container, this.updaters, this._initializers.updaters, force);\n  }\n  init() {\n    if (this._initialized) {\n      return;\n    }\n    this._initialized = true;\n  }\n  item(index) {\n    const {\n        items\n      } = this,\n      item = items[index];\n    if (!item || item.destroyed) {\n      const deleteCount = 1;\n      items.splice(index, deleteCount);\n      return;\n    }\n    return item;\n  }\n  async load(params) {\n    const randomFactor = 10000,\n      id = params.id ?? params.element?.id ?? `tsparticles${Math.floor(getRandom() * randomFactor)}`,\n      {\n        index,\n        url\n      } = params,\n      options = url ? await getDataFromUrl({\n        fallback: params.options,\n        url,\n        index\n      }) : params.options;\n    const currentOptions = itemFromSingleOrMultiple(options, index),\n      {\n        items\n      } = this,\n      oldIndex = items.findIndex(v => v.id.description === id),\n      minIndex = 0,\n      newItem = new Container(this, id, currentOptions);\n    if (oldIndex >= minIndex) {\n      const old = this.item(oldIndex),\n        one = 1,\n        none = 0,\n        deleteCount = old ? one : none;\n      if (old && !old.destroyed) {\n        old.destroy(false);\n      }\n      items.splice(oldIndex, deleteCount, newItem);\n    } else {\n      items.push(newItem);\n    }\n    const domContainer = getDomContainer(id, params.element),\n      canvasEl = getCanvasFromContainer(domContainer);\n    newItem.canvas.loadCanvas(canvasEl);\n    await newItem.start();\n    return newItem;\n  }\n  loadOptions(options, sourceOptions) {\n    this.plugins.forEach(plugin => plugin.loadOptions?.(options, sourceOptions));\n  }\n  loadParticlesOptions(container, options, ...sourceOptions) {\n    const updaters = this.updaters.get(container);\n    if (!updaters) {\n      return;\n    }\n    updaters.forEach(updater => updater.loadOptions?.(options, ...sourceOptions));\n  }\n  async refresh(refresh = true) {\n    if (!refresh) {\n      return;\n    }\n    await Promise.all(this.items.map(t => t.refresh()));\n  }\n  removeEventListener(type, listener) {\n    this._eventDispatcher.removeEventListener(type, listener);\n  }\n  setOnClickHandler(callback) {\n    const {\n      items\n    } = this;\n    if (!items.length) {\n      throw new Error(`${errorPrefix} can only set click handlers after calling tsParticles.load()`);\n    }\n    items.forEach(item => item.addClickHandler(callback));\n  }\n}", "map": {"version": 3, "names": ["errorPrefix", "generatedAttribute", "executeOnSingleOrMultiple", "<PERSON><PERSON><PERSON><PERSON>", "itemFromSingleOrMultiple", "Container", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventType", "getRandom", "getItemsFromInitializer", "container", "map", "initializers", "force", "res", "get", "Promise", "all", "values", "t", "set", "getDataFromUrl", "data", "url", "index", "fallback", "response", "fetch", "ok", "json", "error", "status", "generatedTrue", "generatedFalse", "canvasTag", "getCanvasFromContainer", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "canvasEl", "HTMLCanvasElement", "tagName", "toLowerCase", "dataset", "existingCanvases", "getElementsByTagName", "length", "firstIndex", "document", "createElement", "append<PERSON><PERSON><PERSON>", "fullPercent", "style", "width", "height", "getDomContainer", "id", "source", "getElementById", "body", "append", "Engine", "constructor", "_configs", "Map", "_dom<PERSON><PERSON><PERSON>", "_eventDispatcher", "_initialized", "plugins", "_initializers", "interactors", "movers", "updaters", "presets", "effectDrawers", "shapeDrawers", "pathGenerators", "configs", "name", "config", "items", "version", "addConfig", "key", "dispatchEvent", "configAdded", "addEffect", "effect", "drawer", "refresh", "type", "getEffectDrawer", "addEventListener", "listener", "addInteractor", "interactorInitializer", "addMover", "moverInitializer", "addParticleUpdater", "updaterInitializer", "addPathGenerator", "generator", "getPathGenerator", "addPlugin", "plugin", "getPlugin", "push", "addPreset", "preset", "options", "override", "getPreset", "addShape", "validType", "validTypes", "getShapeDrawer", "clearPlugins", "delete", "args", "dom", "domItem", "item", "getAvailablePlugins", "needsPlugin", "actualOptions", "getInteractors", "getMovers", "find", "getSupportedEffects", "keys", "getSupportedShapes", "getUpdaters", "init", "destroyed", "deleteCount", "splice", "load", "params", "randomFactor", "element", "Math", "floor", "currentOptions", "oldIndex", "findIndex", "v", "description", "minIndex", "newItem", "old", "one", "none", "destroy", "canvas", "loadCanvas", "start", "loadOptions", "sourceOptions", "for<PERSON>ach", "loadParticlesOptions", "updater", "removeEventListener", "setOnClickHandler", "callback", "Error", "addClickHandler"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/engine/browser/Core/Engine.js"], "sourcesContent": ["import { errorPrefix, generatedAttribute } from \"./Utils/Constants.js\";\nimport { executeOnSingleOrMultiple, getLogger, itemFromSingleOrMultiple } from \"../Utils/Utils.js\";\nimport { Container } from \"./Container.js\";\nimport { EventDispatcher } from \"../Utils/EventDispatcher.js\";\nimport { EventType } from \"../Enums/Types/EventType.js\";\nimport { getRandom } from \"../Utils/NumberUtils.js\";\nasync function getItemsFromInitializer(container, map, initializers, force = false) {\n    let res = map.get(container);\n    if (!res || force) {\n        res = await Promise.all([...initializers.values()].map(t => t(container)));\n        map.set(container, res);\n    }\n    return res;\n}\nasync function getDataFromUrl(data) {\n    const url = itemFromSingleOrMultiple(data.url, data.index);\n    if (!url) {\n        return data.fallback;\n    }\n    const response = await fetch(url);\n    if (response.ok) {\n        return (await response.json());\n    }\n    getLogger().error(`${errorPrefix} ${response.status} while retrieving config file`);\n    return data.fallback;\n}\nconst generatedTrue = \"true\", generatedFalse = \"false\", canvasTag = \"canvas\", getCanvasFromContainer = (domContainer) => {\n    let canvasEl;\n    if (domContainer instanceof HTMLCanvasElement || domContainer.tagName.toLowerCase() === canvasTag) {\n        canvasEl = domContainer;\n        if (!canvasEl.dataset[generatedAttribute]) {\n            canvasEl.dataset[generatedAttribute] = generatedFalse;\n        }\n    }\n    else {\n        const existingCanvases = domContainer.getElementsByTagName(canvasTag);\n        if (existingCanvases.length) {\n            const firstIndex = 0;\n            canvasEl = existingCanvases[firstIndex];\n            canvasEl.dataset[generatedAttribute] = generatedFalse;\n        }\n        else {\n            canvasEl = document.createElement(canvasTag);\n            canvasEl.dataset[generatedAttribute] = generatedTrue;\n            domContainer.appendChild(canvasEl);\n        }\n    }\n    const fullPercent = \"100%\";\n    if (!canvasEl.style.width) {\n        canvasEl.style.width = fullPercent;\n    }\n    if (!canvasEl.style.height) {\n        canvasEl.style.height = fullPercent;\n    }\n    return canvasEl;\n}, getDomContainer = (id, source) => {\n    let domContainer = source ?? document.getElementById(id);\n    if (domContainer) {\n        return domContainer;\n    }\n    domContainer = document.createElement(\"div\");\n    domContainer.id = id;\n    domContainer.dataset[generatedAttribute] = generatedTrue;\n    document.body.append(domContainer);\n    return domContainer;\n};\nexport class Engine {\n    constructor() {\n        this._configs = new Map();\n        this._domArray = [];\n        this._eventDispatcher = new EventDispatcher();\n        this._initialized = false;\n        this.plugins = [];\n        this._initializers = {\n            interactors: new Map(),\n            movers: new Map(),\n            updaters: new Map(),\n        };\n        this.interactors = new Map();\n        this.movers = new Map();\n        this.updaters = new Map();\n        this.presets = new Map();\n        this.effectDrawers = new Map();\n        this.shapeDrawers = new Map();\n        this.pathGenerators = new Map();\n    }\n    get configs() {\n        const res = {};\n        for (const [name, config] of this._configs) {\n            res[name] = config;\n        }\n        return res;\n    }\n    get items() {\n        return this._domArray;\n    }\n    get version() {\n        return \"3.5.0\";\n    }\n    addConfig(config) {\n        const key = config.key ?? config.name ?? \"default\";\n        this._configs.set(key, config);\n        this._eventDispatcher.dispatchEvent(EventType.configAdded, { data: { name: key, config } });\n    }\n    async addEffect(effect, drawer, refresh = true) {\n        executeOnSingleOrMultiple(effect, type => {\n            if (!this.getEffectDrawer(type)) {\n                this.effectDrawers.set(type, drawer);\n            }\n        });\n        await this.refresh(refresh);\n    }\n    addEventListener(type, listener) {\n        this._eventDispatcher.addEventListener(type, listener);\n    }\n    async addInteractor(name, interactorInitializer, refresh = true) {\n        this._initializers.interactors.set(name, interactorInitializer);\n        await this.refresh(refresh);\n    }\n    async addMover(name, moverInitializer, refresh = true) {\n        this._initializers.movers.set(name, moverInitializer);\n        await this.refresh(refresh);\n    }\n    async addParticleUpdater(name, updaterInitializer, refresh = true) {\n        this._initializers.updaters.set(name, updaterInitializer);\n        await this.refresh(refresh);\n    }\n    async addPathGenerator(name, generator, refresh = true) {\n        if (!this.getPathGenerator(name)) {\n            this.pathGenerators.set(name, generator);\n        }\n        await this.refresh(refresh);\n    }\n    async addPlugin(plugin, refresh = true) {\n        if (!this.getPlugin(plugin.id)) {\n            this.plugins.push(plugin);\n        }\n        await this.refresh(refresh);\n    }\n    async addPreset(preset, options, override = false, refresh = true) {\n        if (override || !this.getPreset(preset)) {\n            this.presets.set(preset, options);\n        }\n        await this.refresh(refresh);\n    }\n    async addShape(drawer, refresh = true) {\n        for (const validType of drawer.validTypes) {\n            if (this.getShapeDrawer(validType)) {\n                continue;\n            }\n            this.shapeDrawers.set(validType, drawer);\n        }\n        await this.refresh(refresh);\n    }\n    clearPlugins(container) {\n        this.updaters.delete(container);\n        this.movers.delete(container);\n        this.interactors.delete(container);\n    }\n    dispatchEvent(type, args) {\n        this._eventDispatcher.dispatchEvent(type, args);\n    }\n    dom() {\n        return this.items;\n    }\n    domItem(index) {\n        return this.item(index);\n    }\n    async getAvailablePlugins(container) {\n        const res = new Map();\n        for (const plugin of this.plugins) {\n            if (plugin.needsPlugin(container.actualOptions)) {\n                res.set(plugin.id, await plugin.getPlugin(container));\n            }\n        }\n        return res;\n    }\n    getEffectDrawer(type) {\n        return this.effectDrawers.get(type);\n    }\n    async getInteractors(container, force = false) {\n        return getItemsFromInitializer(container, this.interactors, this._initializers.interactors, force);\n    }\n    async getMovers(container, force = false) {\n        return getItemsFromInitializer(container, this.movers, this._initializers.movers, force);\n    }\n    getPathGenerator(type) {\n        return this.pathGenerators.get(type);\n    }\n    getPlugin(plugin) {\n        return this.plugins.find(t => t.id === plugin);\n    }\n    getPreset(preset) {\n        return this.presets.get(preset);\n    }\n    getShapeDrawer(type) {\n        return this.shapeDrawers.get(type);\n    }\n    getSupportedEffects() {\n        return this.effectDrawers.keys();\n    }\n    getSupportedShapes() {\n        return this.shapeDrawers.keys();\n    }\n    async getUpdaters(container, force = false) {\n        return getItemsFromInitializer(container, this.updaters, this._initializers.updaters, force);\n    }\n    init() {\n        if (this._initialized) {\n            return;\n        }\n        this._initialized = true;\n    }\n    item(index) {\n        const { items } = this, item = items[index];\n        if (!item || item.destroyed) {\n            const deleteCount = 1;\n            items.splice(index, deleteCount);\n            return;\n        }\n        return item;\n    }\n    async load(params) {\n        const randomFactor = 10000, id = params.id ?? params.element?.id ?? `tsparticles${Math.floor(getRandom() * randomFactor)}`, { index, url } = params, options = url ? await getDataFromUrl({ fallback: params.options, url, index }) : params.options;\n        const currentOptions = itemFromSingleOrMultiple(options, index), { items } = this, oldIndex = items.findIndex(v => v.id.description === id), minIndex = 0, newItem = new Container(this, id, currentOptions);\n        if (oldIndex >= minIndex) {\n            const old = this.item(oldIndex), one = 1, none = 0, deleteCount = old ? one : none;\n            if (old && !old.destroyed) {\n                old.destroy(false);\n            }\n            items.splice(oldIndex, deleteCount, newItem);\n        }\n        else {\n            items.push(newItem);\n        }\n        const domContainer = getDomContainer(id, params.element), canvasEl = getCanvasFromContainer(domContainer);\n        newItem.canvas.loadCanvas(canvasEl);\n        await newItem.start();\n        return newItem;\n    }\n    loadOptions(options, sourceOptions) {\n        this.plugins.forEach(plugin => plugin.loadOptions?.(options, sourceOptions));\n    }\n    loadParticlesOptions(container, options, ...sourceOptions) {\n        const updaters = this.updaters.get(container);\n        if (!updaters) {\n            return;\n        }\n        updaters.forEach(updater => updater.loadOptions?.(options, ...sourceOptions));\n    }\n    async refresh(refresh = true) {\n        if (!refresh) {\n            return;\n        }\n        await Promise.all(this.items.map(t => t.refresh()));\n    }\n    removeEventListener(type, listener) {\n        this._eventDispatcher.removeEventListener(type, listener);\n    }\n    setOnClickHandler(callback) {\n        const { items } = this;\n        if (!items.length) {\n            throw new Error(`${errorPrefix} can only set click handlers after calling tsParticles.load()`);\n        }\n        items.forEach(item => item.addClickHandler(callback));\n    }\n}\n"], "mappings": "AAAA,SAASA,WAAW,EAAEC,kBAAkB,QAAQ,sBAAsB;AACtE,SAASC,yBAAyB,EAAEC,SAAS,EAAEC,wBAAwB,QAAQ,mBAAmB;AAClG,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,eAAe,QAAQ,6BAA6B;AAC7D,SAASC,SAAS,QAAQ,6BAA6B;AACvD,SAASC,SAAS,QAAQ,yBAAyB;AACnD,eAAeC,uBAAuBA,CAACC,SAAS,EAAEC,GAAG,EAAEC,YAAY,EAAEC,KAAK,GAAG,KAAK,EAAE;EAChF,IAAIC,GAAG,GAAGH,GAAG,CAACI,GAAG,CAACL,SAAS,CAAC;EAC5B,IAAI,CAACI,GAAG,IAAID,KAAK,EAAE;IACfC,GAAG,GAAG,MAAME,OAAO,CAACC,GAAG,CAAC,CAAC,GAAGL,YAAY,CAACM,MAAM,CAAC,CAAC,CAAC,CAACP,GAAG,CAACQ,CAAC,IAAIA,CAAC,CAACT,SAAS,CAAC,CAAC,CAAC;IAC1EC,GAAG,CAACS,GAAG,CAACV,SAAS,EAAEI,GAAG,CAAC;EAC3B;EACA,OAAOA,GAAG;AACd;AACA,eAAeO,cAAcA,CAACC,IAAI,EAAE;EAChC,MAAMC,GAAG,GAAGnB,wBAAwB,CAACkB,IAAI,CAACC,GAAG,EAAED,IAAI,CAACE,KAAK,CAAC;EAC1D,IAAI,CAACD,GAAG,EAAE;IACN,OAAOD,IAAI,CAACG,QAAQ;EACxB;EACA,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAACJ,GAAG,CAAC;EACjC,IAAIG,QAAQ,CAACE,EAAE,EAAE;IACb,OAAQ,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;EACjC;EACA1B,SAAS,CAAC,CAAC,CAAC2B,KAAK,CAAC,GAAG9B,WAAW,IAAI0B,QAAQ,CAACK,MAAM,+BAA+B,CAAC;EACnF,OAAOT,IAAI,CAACG,QAAQ;AACxB;AACA,MAAMO,aAAa,GAAG,MAAM;EAAEC,cAAc,GAAG,OAAO;EAAEC,SAAS,GAAG,QAAQ;EAAEC,sBAAsB,GAAIC,YAAY,IAAK;IACrH,IAAIC,QAAQ;IACZ,IAAID,YAAY,YAAYE,iBAAiB,IAAIF,YAAY,CAACG,OAAO,CAACC,WAAW,CAAC,CAAC,KAAKN,SAAS,EAAE;MAC/FG,QAAQ,GAAGD,YAAY;MACvB,IAAI,CAACC,QAAQ,CAACI,OAAO,CAACxC,kBAAkB,CAAC,EAAE;QACvCoC,QAAQ,CAACI,OAAO,CAACxC,kBAAkB,CAAC,GAAGgC,cAAc;MACzD;IACJ,CAAC,MACI;MACD,MAAMS,gBAAgB,GAAGN,YAAY,CAACO,oBAAoB,CAACT,SAAS,CAAC;MACrE,IAAIQ,gBAAgB,CAACE,MAAM,EAAE;QACzB,MAAMC,UAAU,GAAG,CAAC;QACpBR,QAAQ,GAAGK,gBAAgB,CAACG,UAAU,CAAC;QACvCR,QAAQ,CAACI,OAAO,CAACxC,kBAAkB,CAAC,GAAGgC,cAAc;MACzD,CAAC,MACI;QACDI,QAAQ,GAAGS,QAAQ,CAACC,aAAa,CAACb,SAAS,CAAC;QAC5CG,QAAQ,CAACI,OAAO,CAACxC,kBAAkB,CAAC,GAAG+B,aAAa;QACpDI,YAAY,CAACY,WAAW,CAACX,QAAQ,CAAC;MACtC;IACJ;IACA,MAAMY,WAAW,GAAG,MAAM;IAC1B,IAAI,CAACZ,QAAQ,CAACa,KAAK,CAACC,KAAK,EAAE;MACvBd,QAAQ,CAACa,KAAK,CAACC,KAAK,GAAGF,WAAW;IACtC;IACA,IAAI,CAACZ,QAAQ,CAACa,KAAK,CAACE,MAAM,EAAE;MACxBf,QAAQ,CAACa,KAAK,CAACE,MAAM,GAAGH,WAAW;IACvC;IACA,OAAOZ,QAAQ;EACnB,CAAC;EAAEgB,eAAe,GAAGA,CAACC,EAAE,EAAEC,MAAM,KAAK;IACjC,IAAInB,YAAY,GAAGmB,MAAM,IAAIT,QAAQ,CAACU,cAAc,CAACF,EAAE,CAAC;IACxD,IAAIlB,YAAY,EAAE;MACd,OAAOA,YAAY;IACvB;IACAA,YAAY,GAAGU,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAC5CX,YAAY,CAACkB,EAAE,GAAGA,EAAE;IACpBlB,YAAY,CAACK,OAAO,CAACxC,kBAAkB,CAAC,GAAG+B,aAAa;IACxDc,QAAQ,CAACW,IAAI,CAACC,MAAM,CAACtB,YAAY,CAAC;IAClC,OAAOA,YAAY;EACvB,CAAC;AACD,OAAO,MAAMuB,MAAM,CAAC;EAChBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,QAAQ,GAAG,IAAIC,GAAG,CAAC,CAAC;IACzB,IAAI,CAACC,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,gBAAgB,GAAG,IAAI1D,eAAe,CAAC,CAAC;IAC7C,IAAI,CAAC2D,YAAY,GAAG,KAAK;IACzB,IAAI,CAACC,OAAO,GAAG,EAAE;IACjB,IAAI,CAACC,aAAa,GAAG;MACjBC,WAAW,EAAE,IAAIN,GAAG,CAAC,CAAC;MACtBO,MAAM,EAAE,IAAIP,GAAG,CAAC,CAAC;MACjBQ,QAAQ,EAAE,IAAIR,GAAG,CAAC;IACtB,CAAC;IACD,IAAI,CAACM,WAAW,GAAG,IAAIN,GAAG,CAAC,CAAC;IAC5B,IAAI,CAACO,MAAM,GAAG,IAAIP,GAAG,CAAC,CAAC;IACvB,IAAI,CAACQ,QAAQ,GAAG,IAAIR,GAAG,CAAC,CAAC;IACzB,IAAI,CAACS,OAAO,GAAG,IAAIT,GAAG,CAAC,CAAC;IACxB,IAAI,CAACU,aAAa,GAAG,IAAIV,GAAG,CAAC,CAAC;IAC9B,IAAI,CAACW,YAAY,GAAG,IAAIX,GAAG,CAAC,CAAC;IAC7B,IAAI,CAACY,cAAc,GAAG,IAAIZ,GAAG,CAAC,CAAC;EACnC;EACA,IAAIa,OAAOA,CAAA,EAAG;IACV,MAAM7D,GAAG,GAAG,CAAC,CAAC;IACd,KAAK,MAAM,CAAC8D,IAAI,EAAEC,MAAM,CAAC,IAAI,IAAI,CAAChB,QAAQ,EAAE;MACxC/C,GAAG,CAAC8D,IAAI,CAAC,GAAGC,MAAM;IACtB;IACA,OAAO/D,GAAG;EACd;EACA,IAAIgE,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACf,SAAS;EACzB;EACA,IAAIgB,OAAOA,CAAA,EAAG;IACV,OAAO,OAAO;EAClB;EACAC,SAASA,CAACH,MAAM,EAAE;IACd,MAAMI,GAAG,GAAGJ,MAAM,CAACI,GAAG,IAAIJ,MAAM,CAACD,IAAI,IAAI,SAAS;IAClD,IAAI,CAACf,QAAQ,CAACzC,GAAG,CAAC6D,GAAG,EAAEJ,MAAM,CAAC;IAC9B,IAAI,CAACb,gBAAgB,CAACkB,aAAa,CAAC3E,SAAS,CAAC4E,WAAW,EAAE;MAAE7D,IAAI,EAAE;QAAEsD,IAAI,EAAEK,GAAG;QAAEJ;MAAO;IAAE,CAAC,CAAC;EAC/F;EACA,MAAMO,SAASA,CAACC,MAAM,EAAEC,MAAM,EAAEC,OAAO,GAAG,IAAI,EAAE;IAC5CrF,yBAAyB,CAACmF,MAAM,EAAEG,IAAI,IAAI;MACtC,IAAI,CAAC,IAAI,CAACC,eAAe,CAACD,IAAI,CAAC,EAAE;QAC7B,IAAI,CAAChB,aAAa,CAACpD,GAAG,CAACoE,IAAI,EAAEF,MAAM,CAAC;MACxC;IACJ,CAAC,CAAC;IACF,MAAM,IAAI,CAACC,OAAO,CAACA,OAAO,CAAC;EAC/B;EACAG,gBAAgBA,CAACF,IAAI,EAAEG,QAAQ,EAAE;IAC7B,IAAI,CAAC3B,gBAAgB,CAAC0B,gBAAgB,CAACF,IAAI,EAAEG,QAAQ,CAAC;EAC1D;EACA,MAAMC,aAAaA,CAAChB,IAAI,EAAEiB,qBAAqB,EAAEN,OAAO,GAAG,IAAI,EAAE;IAC7D,IAAI,CAACpB,aAAa,CAACC,WAAW,CAAChD,GAAG,CAACwD,IAAI,EAAEiB,qBAAqB,CAAC;IAC/D,MAAM,IAAI,CAACN,OAAO,CAACA,OAAO,CAAC;EAC/B;EACA,MAAMO,QAAQA,CAAClB,IAAI,EAAEmB,gBAAgB,EAAER,OAAO,GAAG,IAAI,EAAE;IACnD,IAAI,CAACpB,aAAa,CAACE,MAAM,CAACjD,GAAG,CAACwD,IAAI,EAAEmB,gBAAgB,CAAC;IACrD,MAAM,IAAI,CAACR,OAAO,CAACA,OAAO,CAAC;EAC/B;EACA,MAAMS,kBAAkBA,CAACpB,IAAI,EAAEqB,kBAAkB,EAAEV,OAAO,GAAG,IAAI,EAAE;IAC/D,IAAI,CAACpB,aAAa,CAACG,QAAQ,CAAClD,GAAG,CAACwD,IAAI,EAAEqB,kBAAkB,CAAC;IACzD,MAAM,IAAI,CAACV,OAAO,CAACA,OAAO,CAAC;EAC/B;EACA,MAAMW,gBAAgBA,CAACtB,IAAI,EAAEuB,SAAS,EAAEZ,OAAO,GAAG,IAAI,EAAE;IACpD,IAAI,CAAC,IAAI,CAACa,gBAAgB,CAACxB,IAAI,CAAC,EAAE;MAC9B,IAAI,CAACF,cAAc,CAACtD,GAAG,CAACwD,IAAI,EAAEuB,SAAS,CAAC;IAC5C;IACA,MAAM,IAAI,CAACZ,OAAO,CAACA,OAAO,CAAC;EAC/B;EACA,MAAMc,SAASA,CAACC,MAAM,EAAEf,OAAO,GAAG,IAAI,EAAE;IACpC,IAAI,CAAC,IAAI,CAACgB,SAAS,CAACD,MAAM,CAAChD,EAAE,CAAC,EAAE;MAC5B,IAAI,CAACY,OAAO,CAACsC,IAAI,CAACF,MAAM,CAAC;IAC7B;IACA,MAAM,IAAI,CAACf,OAAO,CAACA,OAAO,CAAC;EAC/B;EACA,MAAMkB,SAASA,CAACC,MAAM,EAAEC,OAAO,EAAEC,QAAQ,GAAG,KAAK,EAAErB,OAAO,GAAG,IAAI,EAAE;IAC/D,IAAIqB,QAAQ,IAAI,CAAC,IAAI,CAACC,SAAS,CAACH,MAAM,CAAC,EAAE;MACrC,IAAI,CAACnC,OAAO,CAACnD,GAAG,CAACsF,MAAM,EAAEC,OAAO,CAAC;IACrC;IACA,MAAM,IAAI,CAACpB,OAAO,CAACA,OAAO,CAAC;EAC/B;EACA,MAAMuB,QAAQA,CAACxB,MAAM,EAAEC,OAAO,GAAG,IAAI,EAAE;IACnC,KAAK,MAAMwB,SAAS,IAAIzB,MAAM,CAAC0B,UAAU,EAAE;MACvC,IAAI,IAAI,CAACC,cAAc,CAACF,SAAS,CAAC,EAAE;QAChC;MACJ;MACA,IAAI,CAACtC,YAAY,CAACrD,GAAG,CAAC2F,SAAS,EAAEzB,MAAM,CAAC;IAC5C;IACA,MAAM,IAAI,CAACC,OAAO,CAACA,OAAO,CAAC;EAC/B;EACA2B,YAAYA,CAACxG,SAAS,EAAE;IACpB,IAAI,CAAC4D,QAAQ,CAAC6C,MAAM,CAACzG,SAAS,CAAC;IAC/B,IAAI,CAAC2D,MAAM,CAAC8C,MAAM,CAACzG,SAAS,CAAC;IAC7B,IAAI,CAAC0D,WAAW,CAAC+C,MAAM,CAACzG,SAAS,CAAC;EACtC;EACAwE,aAAaA,CAACM,IAAI,EAAE4B,IAAI,EAAE;IACtB,IAAI,CAACpD,gBAAgB,CAACkB,aAAa,CAACM,IAAI,EAAE4B,IAAI,CAAC;EACnD;EACAC,GAAGA,CAAA,EAAG;IACF,OAAO,IAAI,CAACvC,KAAK;EACrB;EACAwC,OAAOA,CAAC9F,KAAK,EAAE;IACX,OAAO,IAAI,CAAC+F,IAAI,CAAC/F,KAAK,CAAC;EAC3B;EACA,MAAMgG,mBAAmBA,CAAC9G,SAAS,EAAE;IACjC,MAAMI,GAAG,GAAG,IAAIgD,GAAG,CAAC,CAAC;IACrB,KAAK,MAAMwC,MAAM,IAAI,IAAI,CAACpC,OAAO,EAAE;MAC/B,IAAIoC,MAAM,CAACmB,WAAW,CAAC/G,SAAS,CAACgH,aAAa,CAAC,EAAE;QAC7C5G,GAAG,CAACM,GAAG,CAACkF,MAAM,CAAChD,EAAE,EAAE,MAAMgD,MAAM,CAACC,SAAS,CAAC7F,SAAS,CAAC,CAAC;MACzD;IACJ;IACA,OAAOI,GAAG;EACd;EACA2E,eAAeA,CAACD,IAAI,EAAE;IAClB,OAAO,IAAI,CAAChB,aAAa,CAACzD,GAAG,CAACyE,IAAI,CAAC;EACvC;EACA,MAAMmC,cAAcA,CAACjH,SAAS,EAAEG,KAAK,GAAG,KAAK,EAAE;IAC3C,OAAOJ,uBAAuB,CAACC,SAAS,EAAE,IAAI,CAAC0D,WAAW,EAAE,IAAI,CAACD,aAAa,CAACC,WAAW,EAAEvD,KAAK,CAAC;EACtG;EACA,MAAM+G,SAASA,CAAClH,SAAS,EAAEG,KAAK,GAAG,KAAK,EAAE;IACtC,OAAOJ,uBAAuB,CAACC,SAAS,EAAE,IAAI,CAAC2D,MAAM,EAAE,IAAI,CAACF,aAAa,CAACE,MAAM,EAAExD,KAAK,CAAC;EAC5F;EACAuF,gBAAgBA,CAACZ,IAAI,EAAE;IACnB,OAAO,IAAI,CAACd,cAAc,CAAC3D,GAAG,CAACyE,IAAI,CAAC;EACxC;EACAe,SAASA,CAACD,MAAM,EAAE;IACd,OAAO,IAAI,CAACpC,OAAO,CAAC2D,IAAI,CAAC1G,CAAC,IAAIA,CAAC,CAACmC,EAAE,KAAKgD,MAAM,CAAC;EAClD;EACAO,SAASA,CAACH,MAAM,EAAE;IACd,OAAO,IAAI,CAACnC,OAAO,CAACxD,GAAG,CAAC2F,MAAM,CAAC;EACnC;EACAO,cAAcA,CAACzB,IAAI,EAAE;IACjB,OAAO,IAAI,CAACf,YAAY,CAAC1D,GAAG,CAACyE,IAAI,CAAC;EACtC;EACAsC,mBAAmBA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACtD,aAAa,CAACuD,IAAI,CAAC,CAAC;EACpC;EACAC,kBAAkBA,CAAA,EAAG;IACjB,OAAO,IAAI,CAACvD,YAAY,CAACsD,IAAI,CAAC,CAAC;EACnC;EACA,MAAME,WAAWA,CAACvH,SAAS,EAAEG,KAAK,GAAG,KAAK,EAAE;IACxC,OAAOJ,uBAAuB,CAACC,SAAS,EAAE,IAAI,CAAC4D,QAAQ,EAAE,IAAI,CAACH,aAAa,CAACG,QAAQ,EAAEzD,KAAK,CAAC;EAChG;EACAqH,IAAIA,CAAA,EAAG;IACH,IAAI,IAAI,CAACjE,YAAY,EAAE;MACnB;IACJ;IACA,IAAI,CAACA,YAAY,GAAG,IAAI;EAC5B;EACAsD,IAAIA,CAAC/F,KAAK,EAAE;IACR,MAAM;QAAEsD;MAAM,CAAC,GAAG,IAAI;MAAEyC,IAAI,GAAGzC,KAAK,CAACtD,KAAK,CAAC;IAC3C,IAAI,CAAC+F,IAAI,IAAIA,IAAI,CAACY,SAAS,EAAE;MACzB,MAAMC,WAAW,GAAG,CAAC;MACrBtD,KAAK,CAACuD,MAAM,CAAC7G,KAAK,EAAE4G,WAAW,CAAC;MAChC;IACJ;IACA,OAAOb,IAAI;EACf;EACA,MAAMe,IAAIA,CAACC,MAAM,EAAE;IACf,MAAMC,YAAY,GAAG,KAAK;MAAElF,EAAE,GAAGiF,MAAM,CAACjF,EAAE,IAAIiF,MAAM,CAACE,OAAO,EAAEnF,EAAE,IAAI,cAAcoF,IAAI,CAACC,KAAK,CAACnI,SAAS,CAAC,CAAC,GAAGgI,YAAY,CAAC,EAAE;MAAE;QAAEhH,KAAK;QAAED;MAAI,CAAC,GAAGgH,MAAM;MAAE5B,OAAO,GAAGpF,GAAG,GAAG,MAAMF,cAAc,CAAC;QAAEI,QAAQ,EAAE8G,MAAM,CAAC5B,OAAO;QAAEpF,GAAG;QAAEC;MAAM,CAAC,CAAC,GAAG+G,MAAM,CAAC5B,OAAO;IACpP,MAAMiC,cAAc,GAAGxI,wBAAwB,CAACuG,OAAO,EAAEnF,KAAK,CAAC;MAAE;QAAEsD;MAAM,CAAC,GAAG,IAAI;MAAE+D,QAAQ,GAAG/D,KAAK,CAACgE,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACzF,EAAE,CAAC0F,WAAW,KAAK1F,EAAE,CAAC;MAAE2F,QAAQ,GAAG,CAAC;MAAEC,OAAO,GAAG,IAAI7I,SAAS,CAAC,IAAI,EAAEiD,EAAE,EAAEsF,cAAc,CAAC;IAC5M,IAAIC,QAAQ,IAAII,QAAQ,EAAE;MACtB,MAAME,GAAG,GAAG,IAAI,CAAC5B,IAAI,CAACsB,QAAQ,CAAC;QAAEO,GAAG,GAAG,CAAC;QAAEC,IAAI,GAAG,CAAC;QAAEjB,WAAW,GAAGe,GAAG,GAAGC,GAAG,GAAGC,IAAI;MAClF,IAAIF,GAAG,IAAI,CAACA,GAAG,CAAChB,SAAS,EAAE;QACvBgB,GAAG,CAACG,OAAO,CAAC,KAAK,CAAC;MACtB;MACAxE,KAAK,CAACuD,MAAM,CAACQ,QAAQ,EAAET,WAAW,EAAEc,OAAO,CAAC;IAChD,CAAC,MACI;MACDpE,KAAK,CAAC0B,IAAI,CAAC0C,OAAO,CAAC;IACvB;IACA,MAAM9G,YAAY,GAAGiB,eAAe,CAACC,EAAE,EAAEiF,MAAM,CAACE,OAAO,CAAC;MAAEpG,QAAQ,GAAGF,sBAAsB,CAACC,YAAY,CAAC;IACzG8G,OAAO,CAACK,MAAM,CAACC,UAAU,CAACnH,QAAQ,CAAC;IACnC,MAAM6G,OAAO,CAACO,KAAK,CAAC,CAAC;IACrB,OAAOP,OAAO;EAClB;EACAQ,WAAWA,CAAC/C,OAAO,EAAEgD,aAAa,EAAE;IAChC,IAAI,CAACzF,OAAO,CAAC0F,OAAO,CAACtD,MAAM,IAAIA,MAAM,CAACoD,WAAW,GAAG/C,OAAO,EAAEgD,aAAa,CAAC,CAAC;EAChF;EACAE,oBAAoBA,CAACnJ,SAAS,EAAEiG,OAAO,EAAE,GAAGgD,aAAa,EAAE;IACvD,MAAMrF,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACvD,GAAG,CAACL,SAAS,CAAC;IAC7C,IAAI,CAAC4D,QAAQ,EAAE;MACX;IACJ;IACAA,QAAQ,CAACsF,OAAO,CAACE,OAAO,IAAIA,OAAO,CAACJ,WAAW,GAAG/C,OAAO,EAAE,GAAGgD,aAAa,CAAC,CAAC;EACjF;EACA,MAAMpE,OAAOA,CAACA,OAAO,GAAG,IAAI,EAAE;IAC1B,IAAI,CAACA,OAAO,EAAE;MACV;IACJ;IACA,MAAMvE,OAAO,CAACC,GAAG,CAAC,IAAI,CAAC6D,KAAK,CAACnE,GAAG,CAACQ,CAAC,IAAIA,CAAC,CAACoE,OAAO,CAAC,CAAC,CAAC,CAAC;EACvD;EACAwE,mBAAmBA,CAACvE,IAAI,EAAEG,QAAQ,EAAE;IAChC,IAAI,CAAC3B,gBAAgB,CAAC+F,mBAAmB,CAACvE,IAAI,EAAEG,QAAQ,CAAC;EAC7D;EACAqE,iBAAiBA,CAACC,QAAQ,EAAE;IACxB,MAAM;MAAEnF;IAAM,CAAC,GAAG,IAAI;IACtB,IAAI,CAACA,KAAK,CAAClC,MAAM,EAAE;MACf,MAAM,IAAIsH,KAAK,CAAC,GAAGlK,WAAW,+DAA+D,CAAC;IAClG;IACA8E,KAAK,CAAC8E,OAAO,CAACrC,IAAI,IAAIA,IAAI,CAAC4C,eAAe,CAACF,QAAQ,CAAC,CAAC;EACzD;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
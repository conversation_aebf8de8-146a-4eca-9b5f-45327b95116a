{"ast": null, "code": "export class Point {\n  constructor(position, particle) {\n    this.position = position;\n    this.particle = particle;\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Core/Utils/Point.js"], "names": ["Point", "constructor", "position", "particle"], "mappings": "AAAA,OAAO,MAAMA,KAAN,CAAY;AACfC,EAAAA,WAAW,CAACC,QAAD,EAAWC,QAAX,EAAqB;AAC5B,SAAKD,QAAL,GAAgBA,QAAhB;AACA,SAAKC,QAAL,GAAgBA,QAAhB;AACH;;AAJc", "sourcesContent": ["export class Point {\n    constructor(position, particle) {\n        this.position = position;\n        this.particle = particle;\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
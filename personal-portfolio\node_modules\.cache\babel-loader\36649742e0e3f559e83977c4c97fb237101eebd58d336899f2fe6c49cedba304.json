{"ast": null, "code": "import { AnimationMode } from \"../../Enums/Modes/AnimationMode.js\";\nimport { StartValueType } from \"../../Enums/Types/StartValueType.js\";\nimport { setRangeValue } from \"../../Utils/NumberUtils.js\";\nexport class AnimationOptions {\n  constructor() {\n    this.count = 0;\n    this.enable = false;\n    this.speed = 1;\n    this.decay = 0;\n    this.delay = 0;\n    this.sync = false;\n  }\n  load(data) {\n    if (!data) {\n      return;\n    }\n    if (data.count !== undefined) {\n      this.count = setRangeValue(data.count);\n    }\n    if (data.enable !== undefined) {\n      this.enable = data.enable;\n    }\n    if (data.speed !== undefined) {\n      this.speed = setRangeValue(data.speed);\n    }\n    if (data.decay !== undefined) {\n      this.decay = setRangeValue(data.decay);\n    }\n    if (data.delay !== undefined) {\n      this.delay = setRangeValue(data.delay);\n    }\n    if (data.sync !== undefined) {\n      this.sync = data.sync;\n    }\n  }\n}\nexport class RangedAnimationOptions extends AnimationOptions {\n  constructor() {\n    super();\n    this.mode = AnimationMode.auto;\n    this.startValue = StartValueType.random;\n  }\n  load(data) {\n    super.load(data);\n    if (!data) {\n      return;\n    }\n    if (data.mode !== undefined) {\n      this.mode = data.mode;\n    }\n    if (data.startValue !== undefined) {\n      this.startValue = data.startValue;\n    }\n  }\n}", "map": {"version": 3, "names": ["AnimationMode", "StartValueType", "setRangeValue", "AnimationOptions", "constructor", "count", "enable", "speed", "decay", "delay", "sync", "load", "data", "undefined", "RangedAnimationOptions", "mode", "auto", "startValue", "random"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/engine/browser/Options/Classes/AnimationOptions.js"], "sourcesContent": ["import { AnimationMode } from \"../../Enums/Modes/AnimationMode.js\";\nimport { StartValueType } from \"../../Enums/Types/StartValueType.js\";\nimport { setRangeValue } from \"../../Utils/NumberUtils.js\";\nexport class AnimationOptions {\n    constructor() {\n        this.count = 0;\n        this.enable = false;\n        this.speed = 1;\n        this.decay = 0;\n        this.delay = 0;\n        this.sync = false;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.count !== undefined) {\n            this.count = setRangeValue(data.count);\n        }\n        if (data.enable !== undefined) {\n            this.enable = data.enable;\n        }\n        if (data.speed !== undefined) {\n            this.speed = setRangeValue(data.speed);\n        }\n        if (data.decay !== undefined) {\n            this.decay = setRangeValue(data.decay);\n        }\n        if (data.delay !== undefined) {\n            this.delay = setRangeValue(data.delay);\n        }\n        if (data.sync !== undefined) {\n            this.sync = data.sync;\n        }\n    }\n}\nexport class RangedAnimationOptions extends AnimationOptions {\n    constructor() {\n        super();\n        this.mode = AnimationMode.auto;\n        this.startValue = StartValueType.random;\n    }\n    load(data) {\n        super.load(data);\n        if (!data) {\n            return;\n        }\n        if (data.mode !== undefined) {\n            this.mode = data.mode;\n        }\n        if (data.startValue !== undefined) {\n            this.startValue = data.startValue;\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,oCAAoC;AAClE,SAASC,cAAc,QAAQ,qCAAqC;AACpE,SAASC,aAAa,QAAQ,4BAA4B;AAC1D,OAAO,MAAMC,gBAAgB,CAAC;EAC1BC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,KAAK,GAAG,CAAC;IACd,IAAI,CAACC,MAAM,GAAG,KAAK;IACnB,IAAI,CAACC,KAAK,GAAG,CAAC;IACd,IAAI,CAACC,KAAK,GAAG,CAAC;IACd,IAAI,CAACC,KAAK,GAAG,CAAC;IACd,IAAI,CAACC,IAAI,GAAG,KAAK;EACrB;EACAC,IAAIA,CAACC,IAAI,EAAE;IACP,IAAI,CAACA,IAAI,EAAE;MACP;IACJ;IACA,IAAIA,IAAI,CAACP,KAAK,KAAKQ,SAAS,EAAE;MAC1B,IAAI,CAACR,KAAK,GAAGH,aAAa,CAACU,IAAI,CAACP,KAAK,CAAC;IAC1C;IACA,IAAIO,IAAI,CAACN,MAAM,KAAKO,SAAS,EAAE;MAC3B,IAAI,CAACP,MAAM,GAAGM,IAAI,CAACN,MAAM;IAC7B;IACA,IAAIM,IAAI,CAACL,KAAK,KAAKM,SAAS,EAAE;MAC1B,IAAI,CAACN,KAAK,GAAGL,aAAa,CAACU,IAAI,CAACL,KAAK,CAAC;IAC1C;IACA,IAAIK,IAAI,CAACJ,KAAK,KAAKK,SAAS,EAAE;MAC1B,IAAI,CAACL,KAAK,GAAGN,aAAa,CAACU,IAAI,CAACJ,KAAK,CAAC;IAC1C;IACA,IAAII,IAAI,CAACH,KAAK,KAAKI,SAAS,EAAE;MAC1B,IAAI,CAACJ,KAAK,GAAGP,aAAa,CAACU,IAAI,CAACH,KAAK,CAAC;IAC1C;IACA,IAAIG,IAAI,CAACF,IAAI,KAAKG,SAAS,EAAE;MACzB,IAAI,CAACH,IAAI,GAAGE,IAAI,CAACF,IAAI;IACzB;EACJ;AACJ;AACA,OAAO,MAAMI,sBAAsB,SAASX,gBAAgB,CAAC;EACzDC,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,CAAC;IACP,IAAI,CAACW,IAAI,GAAGf,aAAa,CAACgB,IAAI;IAC9B,IAAI,CAACC,UAAU,GAAGhB,cAAc,CAACiB,MAAM;EAC3C;EACAP,IAAIA,CAACC,IAAI,EAAE;IACP,KAAK,CAACD,IAAI,CAACC,IAAI,CAAC;IAChB,IAAI,CAACA,IAAI,EAAE;MACP;IACJ;IACA,IAAIA,IAAI,CAACG,IAAI,KAAKF,SAAS,EAAE;MACzB,IAAI,CAACE,IAAI,GAAGH,IAAI,CAACG,IAAI;IACzB;IACA,IAAIH,IAAI,CAACK,UAAU,KAAKJ,SAAS,EAAE;MAC/B,IAAI,CAACI,UAAU,GAAGL,IAAI,CAACK,UAAU;IACrC;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { downloadSvgImage, loadImage } from \"./Utils.js\";\nimport { ImageDrawer } from \"./ImageDrawer.js\";\nimport { ImagePreloaderPlugin } from \"./ImagePreloader.js\";\nimport { errorPrefix } from \"@tsparticles/engine\";\nimport { loadGifImage } from \"./GifUtils/Utils.js\";\nconst extLength = 3;\nfunction addLoadImageToEngine(engine) {\n  if (engine.loadImage) {\n    return;\n  }\n  engine.loadImage = async data => {\n    if (!data.name && !data.src) {\n      throw new Error(`${errorPrefix} no image source provided`);\n    }\n    if (!engine.images) {\n      engine.images = [];\n    }\n    if (engine.images.find(t => t.name === data.name || t.source === data.src)) {\n      return;\n    }\n    try {\n      const image = {\n        gif: data.gif ?? false,\n        name: data.name ?? data.src,\n        source: data.src,\n        type: data.src.substring(data.src.length - extLength),\n        error: false,\n        loading: true,\n        replaceColor: data.replaceColor,\n        ratio: data.width && data.height ? data.width / data.height : undefined\n      };\n      engine.images.push(image);\n      let imageFunc;\n      if (data.gif) {\n        imageFunc = loadGifImage;\n      } else {\n        imageFunc = data.replaceColor ? downloadSvgImage : loadImage;\n      }\n      await imageFunc(image);\n    } catch {\n      throw new Error(`${errorPrefix} ${data.name ?? data.src} not found`);\n    }\n  };\n}\nexport async function loadImageShape(engine, refresh = true) {\n  addLoadImageToEngine(engine);\n  const preloader = new ImagePreloaderPlugin(engine);\n  await engine.addPlugin(preloader, refresh);\n  await engine.addShape(new ImageDrawer(engine), refresh);\n}", "map": {"version": 3, "names": ["downloadSvgImage", "loadImage", "ImageDrawer", "ImagePreloaderPlugin", "errorPrefix", "loadGifImage", "extLength", "addLoadImageToEngine", "engine", "data", "name", "src", "Error", "images", "find", "t", "source", "image", "gif", "type", "substring", "length", "error", "loading", "replaceColor", "ratio", "width", "height", "undefined", "push", "imageFunc", "loadImageShape", "refresh", "preloader", "addPlugin", "addShape"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/shape-image/browser/index.js"], "sourcesContent": ["import { downloadSvgImage, loadImage } from \"./Utils.js\";\nimport { ImageDrawer } from \"./ImageDrawer.js\";\nimport { ImagePreloaderPlugin } from \"./ImagePreloader.js\";\nimport { errorPrefix } from \"@tsparticles/engine\";\nimport { loadGifImage } from \"./GifUtils/Utils.js\";\nconst extLength = 3;\nfunction addLoadImageToEngine(engine) {\n    if (engine.loadImage) {\n        return;\n    }\n    engine.loadImage = async (data) => {\n        if (!data.name && !data.src) {\n            throw new Error(`${errorPrefix} no image source provided`);\n        }\n        if (!engine.images) {\n            engine.images = [];\n        }\n        if (engine.images.find((t) => t.name === data.name || t.source === data.src)) {\n            return;\n        }\n        try {\n            const image = {\n                gif: data.gif ?? false,\n                name: data.name ?? data.src,\n                source: data.src,\n                type: data.src.substring(data.src.length - extLength),\n                error: false,\n                loading: true,\n                replaceColor: data.replaceColor,\n                ratio: data.width && data.height ? data.width / data.height : undefined,\n            };\n            engine.images.push(image);\n            let imageFunc;\n            if (data.gif) {\n                imageFunc = loadGifImage;\n            }\n            else {\n                imageFunc = data.replaceColor ? downloadSvgImage : loadImage;\n            }\n            await imageFunc(image);\n        }\n        catch {\n            throw new Error(`${errorPrefix} ${data.name ?? data.src} not found`);\n        }\n    };\n}\nexport async function loadImageShape(engine, refresh = true) {\n    addLoadImageToEngine(engine);\n    const preloader = new ImagePreloaderPlugin(engine);\n    await engine.addPlugin(preloader, refresh);\n    await engine.addShape(new ImageDrawer(engine), refresh);\n}\n"], "mappings": "AAAA,SAASA,gBAAgB,EAAEC,SAAS,QAAQ,YAAY;AACxD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,oBAAoB,QAAQ,qBAAqB;AAC1D,SAASC,WAAW,QAAQ,qBAAqB;AACjD,SAASC,YAAY,QAAQ,qBAAqB;AAClD,MAAMC,SAAS,GAAG,CAAC;AACnB,SAASC,oBAAoBA,CAACC,MAAM,EAAE;EAClC,IAAIA,MAAM,CAACP,SAAS,EAAE;IAClB;EACJ;EACAO,MAAM,CAACP,SAAS,GAAG,MAAOQ,IAAI,IAAK;IAC/B,IAAI,CAACA,IAAI,CAACC,IAAI,IAAI,CAACD,IAAI,CAACE,GAAG,EAAE;MACzB,MAAM,IAAIC,KAAK,CAAC,GAAGR,WAAW,2BAA2B,CAAC;IAC9D;IACA,IAAI,CAACI,MAAM,CAACK,MAAM,EAAE;MAChBL,MAAM,CAACK,MAAM,GAAG,EAAE;IACtB;IACA,IAAIL,MAAM,CAACK,MAAM,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACL,IAAI,KAAKD,IAAI,CAACC,IAAI,IAAIK,CAAC,CAACC,MAAM,KAAKP,IAAI,CAACE,GAAG,CAAC,EAAE;MAC1E;IACJ;IACA,IAAI;MACA,MAAMM,KAAK,GAAG;QACVC,GAAG,EAAET,IAAI,CAACS,GAAG,IAAI,KAAK;QACtBR,IAAI,EAAED,IAAI,CAACC,IAAI,IAAID,IAAI,CAACE,GAAG;QAC3BK,MAAM,EAAEP,IAAI,CAACE,GAAG;QAChBQ,IAAI,EAAEV,IAAI,CAACE,GAAG,CAACS,SAAS,CAACX,IAAI,CAACE,GAAG,CAACU,MAAM,GAAGf,SAAS,CAAC;QACrDgB,KAAK,EAAE,KAAK;QACZC,OAAO,EAAE,IAAI;QACbC,YAAY,EAAEf,IAAI,CAACe,YAAY;QAC/BC,KAAK,EAAEhB,IAAI,CAACiB,KAAK,IAAIjB,IAAI,CAACkB,MAAM,GAAGlB,IAAI,CAACiB,KAAK,GAAGjB,IAAI,CAACkB,MAAM,GAAGC;MAClE,CAAC;MACDpB,MAAM,CAACK,MAAM,CAACgB,IAAI,CAACZ,KAAK,CAAC;MACzB,IAAIa,SAAS;MACb,IAAIrB,IAAI,CAACS,GAAG,EAAE;QACVY,SAAS,GAAGzB,YAAY;MAC5B,CAAC,MACI;QACDyB,SAAS,GAAGrB,IAAI,CAACe,YAAY,GAAGxB,gBAAgB,GAAGC,SAAS;MAChE;MACA,MAAM6B,SAAS,CAACb,KAAK,CAAC;IAC1B,CAAC,CACD,MAAM;MACF,MAAM,IAAIL,KAAK,CAAC,GAAGR,WAAW,IAAIK,IAAI,CAACC,IAAI,IAAID,IAAI,CAACE,GAAG,YAAY,CAAC;IACxE;EACJ,CAAC;AACL;AACA,OAAO,eAAeoB,cAAcA,CAACvB,MAAM,EAAEwB,OAAO,GAAG,IAAI,EAAE;EACzDzB,oBAAoB,CAACC,MAAM,CAAC;EAC5B,MAAMyB,SAAS,GAAG,IAAI9B,oBAAoB,CAACK,MAAM,CAAC;EAClD,MAAMA,MAAM,CAAC0B,SAAS,CAACD,SAAS,EAAED,OAAO,CAAC;EAC1C,MAAMxB,MAAM,CAAC2B,QAAQ,CAAC,IAAIjC,WAAW,CAACM,MAAM,CAAC,EAAEwB,OAAO,CAAC;AAC3D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { Bounce } from \"../Bounce/Bounce\";\nimport { CollisionsOverlap } from \"./CollisionsOverlap\";\nexport class Collisions {\n  constructor() {\n    this.bounce = new Bounce();\n    this.enable = false;\n    this.mode = \"bounce\";\n    this.overlap = new CollisionsOverlap();\n  }\n\n  load(data) {\n    if (data === undefined) {\n      return;\n    }\n\n    this.bounce.load(data.bounce);\n\n    if (data.enable !== undefined) {\n      this.enable = data.enable;\n    }\n\n    if (data.mode !== undefined) {\n      this.mode = data.mode;\n    }\n\n    this.overlap.load(data.overlap);\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Options/Classes/Particles/Collisions/Collisions.js"], "names": ["<PERSON><PERSON><PERSON>", "CollisionsOverlap", "Collisions", "constructor", "bounce", "enable", "mode", "overlap", "load", "data", "undefined"], "mappings": "AAAA,SAASA,MAAT,QAAuB,kBAAvB;AACA,SAASC,iBAAT,QAAkC,qBAAlC;AACA,OAAO,MAAMC,UAAN,CAAiB;AACpBC,EAAAA,WAAW,GAAG;AACV,SAAKC,MAAL,GAAc,IAAIJ,MAAJ,EAAd;AACA,SAAKK,MAAL,GAAc,KAAd;AACA,SAAKC,IAAL,GAAY,QAAZ;AACA,SAAKC,OAAL,GAAe,IAAIN,iBAAJ,EAAf;AACH;;AACDO,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,QAAIA,IAAI,KAAKC,SAAb,EAAwB;AACpB;AACH;;AACD,SAAKN,MAAL,CAAYI,IAAZ,CAAiBC,IAAI,CAACL,MAAtB;;AACA,QAAIK,IAAI,CAACJ,MAAL,KAAgBK,SAApB,EAA+B;AAC3B,WAAKL,MAAL,GAAcI,IAAI,CAACJ,MAAnB;AACH;;AACD,QAAII,IAAI,CAACH,IAAL,KAAcI,SAAlB,EAA6B;AACzB,WAAKJ,IAAL,GAAYG,IAAI,CAACH,IAAjB;AACH;;AACD,SAAKC,OAAL,CAAaC,IAAb,CAAkBC,IAAI,CAACF,OAAvB;AACH;;AAnBmB", "sourcesContent": ["import { Bounce } from \"../Bounce/Bounce\";\nimport { CollisionsOverlap } from \"./CollisionsOverlap\";\nexport class Collisions {\n    constructor() {\n        this.bounce = new Bounce();\n        this.enable = false;\n        this.mode = \"bounce\";\n        this.overlap = new CollisionsOverlap();\n    }\n    load(data) {\n        if (data === undefined) {\n            return;\n        }\n        this.bounce.load(data.bounce);\n        if (data.enable !== undefined) {\n            this.enable = data.enable;\n        }\n        if (data.mode !== undefined) {\n            this.mode = data.mode;\n        }\n        this.overlap.load(data.overlap);\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
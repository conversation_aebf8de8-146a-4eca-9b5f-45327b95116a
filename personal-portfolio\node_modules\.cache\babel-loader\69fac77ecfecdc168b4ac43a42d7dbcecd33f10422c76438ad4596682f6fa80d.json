{"ast": null, "code": "export var OutMode;\n(function (OutMode) {\n  OutMode[\"bounce\"] = \"bounce\";\n  OutMode[\"none\"] = \"none\";\n  OutMode[\"out\"] = \"out\";\n  OutMode[\"destroy\"] = \"destroy\";\n  OutMode[\"split\"] = \"split\";\n})(OutMode || (OutMode = {}));", "map": {"version": 3, "names": ["OutMode"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/engine/browser/Enums/Modes/OutMode.js"], "sourcesContent": ["export var OutMode;\n(function (OutMode) {\n    OutMode[\"bounce\"] = \"bounce\";\n    OutMode[\"none\"] = \"none\";\n    OutMode[\"out\"] = \"out\";\n    OutMode[\"destroy\"] = \"destroy\";\n    OutMode[\"split\"] = \"split\";\n})(OutMode || (OutMode = {}));\n"], "mappings": "AAAA,OAAO,IAAIA,OAAO;AAClB,CAAC,UAAUA,OAAO,EAAE;EAChBA,OAAO,CAAC,QAAQ,CAAC,GAAG,QAAQ;EAC5BA,OAAO,CAAC,MAAM,CAAC,GAAG,MAAM;EACxBA,OAAO,CAAC,KAAK,CAAC,GAAG,KAAK;EACtBA,OAAO,CAAC,SAAS,CAAC,GAAG,SAAS;EAC9BA,OAAO,CAAC,OAAO,CAAC,GAAG,OAAO;AAC9B,CAAC,EAAEA,OAAO,KAAKA,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
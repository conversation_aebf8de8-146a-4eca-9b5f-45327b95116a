{"ast": null, "code": "import { Collider } from \"./Collider.js\";\nexport async function loadParticlesCollisionsInteraction(engine, refresh = true) {\n  await engine.addInteractor(\"particlesCollisions\", container => {\n    return Promise.resolve(new Collider(container));\n  }, refresh);\n}", "map": {"version": 3, "names": ["Collider", "loadParticlesCollisionsInteraction", "engine", "refresh", "addInteractor", "container", "Promise", "resolve"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/interaction-particles-collisions/browser/index.js"], "sourcesContent": ["import { Collider } from \"./Collider.js\";\nexport async function loadParticlesCollisionsInteraction(engine, refresh = true) {\n    await engine.addInteractor(\"particlesCollisions\", container => {\n        return Promise.resolve(new Collider(container));\n    }, refresh);\n}\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,eAAe;AACxC,OAAO,eAAeC,kCAAkCA,CAACC,MAAM,EAAEC,OAAO,GAAG,IAAI,EAAE;EAC7E,MAAMD,MAAM,CAACE,aAAa,CAAC,qBAAqB,EAAEC,SAAS,IAAI;IAC3D,OAAOC,OAAO,CAACC,OAAO,CAAC,IAAIP,QAAQ,CAACK,SAAS,CAAC,CAAC;EACnD,CAAC,EAAEF,OAAO,CAAC;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
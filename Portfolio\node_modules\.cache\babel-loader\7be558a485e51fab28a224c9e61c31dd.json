{"ast": null, "code": "import { deepExtend } from \"../../Utils\";\nexport class Responsive {\n  constructor() {\n    this.maxWidth = Infinity;\n    this.options = {};\n    this.mode = \"canvas\";\n  }\n\n  load(data) {\n    if (!data) {\n      return;\n    }\n\n    if (data.maxWidth !== undefined) {\n      this.maxWidth = data.maxWidth;\n    }\n\n    if (data.mode !== undefined) {\n      if (data.mode === \"screen\") {\n        this.mode = \"screen\";\n      } else {\n        this.mode = \"canvas\";\n      }\n    }\n\n    if (data.options !== undefined) {\n      this.options = deepExtend({}, data.options);\n    }\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Options/Classes/Responsive.js"], "names": ["deepExtend", "Responsive", "constructor", "max<PERSON><PERSON><PERSON>", "Infinity", "options", "mode", "load", "data", "undefined"], "mappings": "AAAA,SAASA,UAAT,QAA2B,aAA3B;AACA,OAAO,MAAMC,UAAN,CAAiB;AACpBC,EAAAA,WAAW,GAAG;AACV,SAAKC,QAAL,GAAgBC,QAAhB;AACA,SAAKC,OAAL,GAAe,EAAf;AACA,SAAKC,IAAL,GAAY,QAAZ;AACH;;AACDC,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,QAAI,CAACA,IAAL,EAAW;AACP;AACH;;AACD,QAAIA,IAAI,CAACL,QAAL,KAAkBM,SAAtB,EAAiC;AAC7B,WAAKN,QAAL,GAAgBK,IAAI,CAACL,QAArB;AACH;;AACD,QAAIK,IAAI,CAACF,IAAL,KAAcG,SAAlB,EAA6B;AACzB,UAAID,IAAI,CAACF,IAAL,KAAc,QAAlB,EAA4B;AACxB,aAAKA,IAAL,GAAY,QAAZ;AACH,OAFD,MAGK;AACD,aAAKA,IAAL,GAAY,QAAZ;AACH;AACJ;;AACD,QAAIE,IAAI,CAACH,OAAL,KAAiBI,SAArB,EAAgC;AAC5B,WAAKJ,OAAL,GAAeL,UAAU,CAAC,EAAD,EAAKQ,IAAI,CAACH,OAAV,CAAzB;AACH;AACJ;;AAxBmB", "sourcesContent": ["import { deepExtend } from \"../../Utils\";\nexport class Responsive {\n    constructor() {\n        this.maxWidth = Infinity;\n        this.options = {};\n        this.mode = \"canvas\";\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.maxWidth !== undefined) {\n            this.maxWidth = data.maxWidth;\n        }\n        if (data.mode !== undefined) {\n            if (data.mode === \"screen\") {\n                this.mode = \"screen\";\n            }\n            else {\n                this.mode = \"canvas\";\n            }\n        }\n        if (data.options !== undefined) {\n            this.options = deepExtend({}, data.options);\n        }\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
{"ast": null, "code": "\"use strict\";\n\n(function () {\n  \"use strict\";\n\n  try {\n    if (typeof window === \"undefined\") return;\n\n    if (!(\"SVGPathSeg\" in window)) {\n      window.SVGPathSeg = function (type, typeAsLetter, owningPathSegList) {\n        this.pathSegType = type;\n        this.pathSegTypeAsLetter = typeAsLetter;\n        this._owningPathSegList = owningPathSegList;\n      };\n\n      window.SVGPathSeg.prototype.classname = \"SVGPathSeg\";\n      window.SVGPathSeg.PATHSEG_UNKNOWN = 0;\n      window.SVGPathSeg.PATHSEG_CLOSEPATH = 1;\n      window.SVGPathSeg.PATHSEG_MOVETO_ABS = 2;\n      window.SVGPathSeg.PATHSEG_MOVETO_REL = 3;\n      window.SVGPathSeg.PATHSEG_LINETO_ABS = 4;\n      window.SVGPathSeg.PATHSEG_LINETO_REL = 5;\n      window.SVGPathSeg.PATHSEG_CURVETO_CUBIC_ABS = 6;\n      window.SVGPathSeg.PATHSEG_CURVETO_CUBIC_REL = 7;\n      window.SVGPathSeg.PATHSEG_CURVETO_QUADRATIC_ABS = 8;\n      window.SVGPathSeg.PATHSEG_CURVETO_QUADRATIC_REL = 9;\n      window.SVGPathSeg.PATHSEG_ARC_ABS = 10;\n      window.SVGPathSeg.PATHSEG_ARC_REL = 11;\n      window.SVGPathSeg.PATHSEG_LINETO_HORIZONTAL_ABS = 12;\n      window.SVGPathSeg.PATHSEG_LINETO_HORIZONTAL_REL = 13;\n      window.SVGPathSeg.PATHSEG_LINETO_VERTICAL_ABS = 14;\n      window.SVGPathSeg.PATHSEG_LINETO_VERTICAL_REL = 15;\n      window.SVGPathSeg.PATHSEG_CURVETO_CUBIC_SMOOTH_ABS = 16;\n      window.SVGPathSeg.PATHSEG_CURVETO_CUBIC_SMOOTH_REL = 17;\n      window.SVGPathSeg.PATHSEG_CURVETO_QUADRATIC_SMOOTH_ABS = 18;\n      window.SVGPathSeg.PATHSEG_CURVETO_QUADRATIC_SMOOTH_REL = 19;\n\n      window.SVGPathSeg.prototype._segmentChanged = function () {\n        if (this._owningPathSegList) this._owningPathSegList.segmentChanged(this);\n      };\n\n      window.SVGPathSegClosePath = function (owningPathSegList) {\n        window.SVGPathSeg.call(this, window.SVGPathSeg.PATHSEG_CLOSEPATH, \"z\", owningPathSegList);\n      };\n\n      window.SVGPathSegClosePath.prototype = Object.create(window.SVGPathSeg.prototype);\n\n      window.SVGPathSegClosePath.prototype.toString = function () {\n        return \"[object SVGPathSegClosePath]\";\n      };\n\n      window.SVGPathSegClosePath.prototype._asPathString = function () {\n        return this.pathSegTypeAsLetter;\n      };\n\n      window.SVGPathSegClosePath.prototype.clone = function () {\n        return new window.SVGPathSegClosePath(undefined);\n      };\n\n      window.SVGPathSegMovetoAbs = function (owningPathSegList, x, y) {\n        window.SVGPathSeg.call(this, window.SVGPathSeg.PATHSEG_MOVETO_ABS, \"M\", owningPathSegList);\n        this._x = x;\n        this._y = y;\n      };\n\n      window.SVGPathSegMovetoAbs.prototype = Object.create(window.SVGPathSeg.prototype);\n\n      window.SVGPathSegMovetoAbs.prototype.toString = function () {\n        return \"[object SVGPathSegMovetoAbs]\";\n      };\n\n      window.SVGPathSegMovetoAbs.prototype._asPathString = function () {\n        return this.pathSegTypeAsLetter + \" \" + this._x + \" \" + this._y;\n      };\n\n      window.SVGPathSegMovetoAbs.prototype.clone = function () {\n        return new window.SVGPathSegMovetoAbs(undefined, this._x, this._y);\n      };\n\n      Object.defineProperty(window.SVGPathSegMovetoAbs.prototype, \"x\", {\n        get: function () {\n          return this._x;\n        },\n        set: function (x) {\n          this._x = x;\n\n          this._segmentChanged();\n        },\n        enumerable: true\n      });\n      Object.defineProperty(window.SVGPathSegMovetoAbs.prototype, \"y\", {\n        get: function () {\n          return this._y;\n        },\n        set: function (y) {\n          this._y = y;\n\n          this._segmentChanged();\n        },\n        enumerable: true\n      });\n\n      window.SVGPathSegMovetoRel = function (owningPathSegList, x, y) {\n        window.SVGPathSeg.call(this, window.SVGPathSeg.PATHSEG_MOVETO_REL, \"m\", owningPathSegList);\n        this._x = x;\n        this._y = y;\n      };\n\n      window.SVGPathSegMovetoRel.prototype = Object.create(window.SVGPathSeg.prototype);\n\n      window.SVGPathSegMovetoRel.prototype.toString = function () {\n        return \"[object SVGPathSegMovetoRel]\";\n      };\n\n      window.SVGPathSegMovetoRel.prototype._asPathString = function () {\n        return this.pathSegTypeAsLetter + \" \" + this._x + \" \" + this._y;\n      };\n\n      window.SVGPathSegMovetoRel.prototype.clone = function () {\n        return new window.SVGPathSegMovetoRel(undefined, this._x, this._y);\n      };\n\n      Object.defineProperty(window.SVGPathSegMovetoRel.prototype, \"x\", {\n        get: function () {\n          return this._x;\n        },\n        set: function (x) {\n          this._x = x;\n\n          this._segmentChanged();\n        },\n        enumerable: true\n      });\n      Object.defineProperty(window.SVGPathSegMovetoRel.prototype, \"y\", {\n        get: function () {\n          return this._y;\n        },\n        set: function (y) {\n          this._y = y;\n\n          this._segmentChanged();\n        },\n        enumerable: true\n      });\n\n      window.SVGPathSegLinetoAbs = function (owningPathSegList, x, y) {\n        window.SVGPathSeg.call(this, window.SVGPathSeg.PATHSEG_LINETO_ABS, \"L\", owningPathSegList);\n        this._x = x;\n        this._y = y;\n      };\n\n      window.SVGPathSegLinetoAbs.prototype = Object.create(window.SVGPathSeg.prototype);\n\n      window.SVGPathSegLinetoAbs.prototype.toString = function () {\n        return \"[object SVGPathSegLinetoAbs]\";\n      };\n\n      window.SVGPathSegLinetoAbs.prototype._asPathString = function () {\n        return this.pathSegTypeAsLetter + \" \" + this._x + \" \" + this._y;\n      };\n\n      window.SVGPathSegLinetoAbs.prototype.clone = function () {\n        return new window.SVGPathSegLinetoAbs(undefined, this._x, this._y);\n      };\n\n      Object.defineProperty(window.SVGPathSegLinetoAbs.prototype, \"x\", {\n        get: function () {\n          return this._x;\n        },\n        set: function (x) {\n          this._x = x;\n\n          this._segmentChanged();\n        },\n        enumerable: true\n      });\n      Object.defineProperty(window.SVGPathSegLinetoAbs.prototype, \"y\", {\n        get: function () {\n          return this._y;\n        },\n        set: function (y) {\n          this._y = y;\n\n          this._segmentChanged();\n        },\n        enumerable: true\n      });\n\n      window.SVGPathSegLinetoRel = function (owningPathSegList, x, y) {\n        window.SVGPathSeg.call(this, window.SVGPathSeg.PATHSEG_LINETO_REL, \"l\", owningPathSegList);\n        this._x = x;\n        this._y = y;\n      };\n\n      window.SVGPathSegLinetoRel.prototype = Object.create(window.SVGPathSeg.prototype);\n\n      window.SVGPathSegLinetoRel.prototype.toString = function () {\n        return \"[object SVGPathSegLinetoRel]\";\n      };\n\n      window.SVGPathSegLinetoRel.prototype._asPathString = function () {\n        return this.pathSegTypeAsLetter + \" \" + this._x + \" \" + this._y;\n      };\n\n      window.SVGPathSegLinetoRel.prototype.clone = function () {\n        return new window.SVGPathSegLinetoRel(undefined, this._x, this._y);\n      };\n\n      Object.defineProperty(window.SVGPathSegLinetoRel.prototype, \"x\", {\n        get: function () {\n          return this._x;\n        },\n        set: function (x) {\n          this._x = x;\n\n          this._segmentChanged();\n        },\n        enumerable: true\n      });\n      Object.defineProperty(window.SVGPathSegLinetoRel.prototype, \"y\", {\n        get: function () {\n          return this._y;\n        },\n        set: function (y) {\n          this._y = y;\n\n          this._segmentChanged();\n        },\n        enumerable: true\n      });\n\n      window.SVGPathSegCurvetoCubicAbs = function (owningPathSegList, x, y, x1, y1, x2, y2) {\n        window.SVGPathSeg.call(this, window.SVGPathSeg.PATHSEG_CURVETO_CUBIC_ABS, \"C\", owningPathSegList);\n        this._x = x;\n        this._y = y;\n        this._x1 = x1;\n        this._y1 = y1;\n        this._x2 = x2;\n        this._y2 = y2;\n      };\n\n      window.SVGPathSegCurvetoCubicAbs.prototype = Object.create(window.SVGPathSeg.prototype);\n\n      window.SVGPathSegCurvetoCubicAbs.prototype.toString = function () {\n        return \"[object SVGPathSegCurvetoCubicAbs]\";\n      };\n\n      window.SVGPathSegCurvetoCubicAbs.prototype._asPathString = function () {\n        return this.pathSegTypeAsLetter + \" \" + this._x1 + \" \" + this._y1 + \" \" + this._x2 + \" \" + this._y2 + \" \" + this._x + \" \" + this._y;\n      };\n\n      window.SVGPathSegCurvetoCubicAbs.prototype.clone = function () {\n        return new window.SVGPathSegCurvetoCubicAbs(undefined, this._x, this._y, this._x1, this._y1, this._x2, this._y2);\n      };\n\n      Object.defineProperty(window.SVGPathSegCurvetoCubicAbs.prototype, \"x\", {\n        get: function () {\n          return this._x;\n        },\n        set: function (x) {\n          this._x = x;\n\n          this._segmentChanged();\n        },\n        enumerable: true\n      });\n      Object.defineProperty(window.SVGPathSegCurvetoCubicAbs.prototype, \"y\", {\n        get: function () {\n          return this._y;\n        },\n        set: function (y) {\n          this._y = y;\n\n          this._segmentChanged();\n        },\n        enumerable: true\n      });\n      Object.defineProperty(window.SVGPathSegCurvetoCubicAbs.prototype, \"x1\", {\n        get: function () {\n          return this._x1;\n        },\n        set: function (x1) {\n          this._x1 = x1;\n\n          this._segmentChanged();\n        },\n        enumerable: true\n      });\n      Object.defineProperty(window.SVGPathSegCurvetoCubicAbs.prototype, \"y1\", {\n        get: function () {\n          return this._y1;\n        },\n        set: function (y1) {\n          this._y1 = y1;\n\n          this._segmentChanged();\n        },\n        enumerable: true\n      });\n      Object.defineProperty(window.SVGPathSegCurvetoCubicAbs.prototype, \"x2\", {\n        get: function () {\n          return this._x2;\n        },\n        set: function (x2) {\n          this._x2 = x2;\n\n          this._segmentChanged();\n        },\n        enumerable: true\n      });\n      Object.defineProperty(window.SVGPathSegCurvetoCubicAbs.prototype, \"y2\", {\n        get: function () {\n          return this._y2;\n        },\n        set: function (y2) {\n          this._y2 = y2;\n\n          this._segmentChanged();\n        },\n        enumerable: true\n      });\n\n      window.SVGPathSegCurvetoCubicRel = function (owningPathSegList, x, y, x1, y1, x2, y2) {\n        window.SVGPathSeg.call(this, window.SVGPathSeg.PATHSEG_CURVETO_CUBIC_REL, \"c\", owningPathSegList);\n        this._x = x;\n        this._y = y;\n        this._x1 = x1;\n        this._y1 = y1;\n        this._x2 = x2;\n        this._y2 = y2;\n      };\n\n      window.SVGPathSegCurvetoCubicRel.prototype = Object.create(window.SVGPathSeg.prototype);\n\n      window.SVGPathSegCurvetoCubicRel.prototype.toString = function () {\n        return \"[object SVGPathSegCurvetoCubicRel]\";\n      };\n\n      window.SVGPathSegCurvetoCubicRel.prototype._asPathString = function () {\n        return this.pathSegTypeAsLetter + \" \" + this._x1 + \" \" + this._y1 + \" \" + this._x2 + \" \" + this._y2 + \" \" + this._x + \" \" + this._y;\n      };\n\n      window.SVGPathSegCurvetoCubicRel.prototype.clone = function () {\n        return new window.SVGPathSegCurvetoCubicRel(undefined, this._x, this._y, this._x1, this._y1, this._x2, this._y2);\n      };\n\n      Object.defineProperty(window.SVGPathSegCurvetoCubicRel.prototype, \"x\", {\n        get: function () {\n          return this._x;\n        },\n        set: function (x) {\n          this._x = x;\n\n          this._segmentChanged();\n        },\n        enumerable: true\n      });\n      Object.defineProperty(window.SVGPathSegCurvetoCubicRel.prototype, \"y\", {\n        get: function () {\n          return this._y;\n        },\n        set: function (y) {\n          this._y = y;\n\n          this._segmentChanged();\n        },\n        enumerable: true\n      });\n      Object.defineProperty(window.SVGPathSegCurvetoCubicRel.prototype, \"x1\", {\n        get: function () {\n          return this._x1;\n        },\n        set: function (x1) {\n          this._x1 = x1;\n\n          this._segmentChanged();\n        },\n        enumerable: true\n      });\n      Object.defineProperty(window.SVGPathSegCurvetoCubicRel.prototype, \"y1\", {\n        get: function () {\n          return this._y1;\n        },\n        set: function (y1) {\n          this._y1 = y1;\n\n          this._segmentChanged();\n        },\n        enumerable: true\n      });\n      Object.defineProperty(window.SVGPathSegCurvetoCubicRel.prototype, \"x2\", {\n        get: function () {\n          return this._x2;\n        },\n        set: function (x2) {\n          this._x2 = x2;\n\n          this._segmentChanged();\n        },\n        enumerable: true\n      });\n      Object.defineProperty(window.SVGPathSegCurvetoCubicRel.prototype, \"y2\", {\n        get: function () {\n          return this._y2;\n        },\n        set: function (y2) {\n          this._y2 = y2;\n\n          this._segmentChanged();\n        },\n        enumerable: true\n      });\n\n      window.SVGPathSegCurvetoQuadraticAbs = function (owningPathSegList, x, y, x1, y1) {\n        window.SVGPathSeg.call(this, window.SVGPathSeg.PATHSEG_CURVETO_QUADRATIC_ABS, \"Q\", owningPathSegList);\n        this._x = x;\n        this._y = y;\n        this._x1 = x1;\n        this._y1 = y1;\n      };\n\n      window.SVGPathSegCurvetoQuadraticAbs.prototype = Object.create(window.SVGPathSeg.prototype);\n\n      window.SVGPathSegCurvetoQuadraticAbs.prototype.toString = function () {\n        return \"[object SVGPathSegCurvetoQuadraticAbs]\";\n      };\n\n      window.SVGPathSegCurvetoQuadraticAbs.prototype._asPathString = function () {\n        return this.pathSegTypeAsLetter + \" \" + this._x1 + \" \" + this._y1 + \" \" + this._x + \" \" + this._y;\n      };\n\n      window.SVGPathSegCurvetoQuadraticAbs.prototype.clone = function () {\n        return new window.SVGPathSegCurvetoQuadraticAbs(undefined, this._x, this._y, this._x1, this._y1);\n      };\n\n      Object.defineProperty(window.SVGPathSegCurvetoQuadraticAbs.prototype, \"x\", {\n        get: function () {\n          return this._x;\n        },\n        set: function (x) {\n          this._x = x;\n\n          this._segmentChanged();\n        },\n        enumerable: true\n      });\n      Object.defineProperty(window.SVGPathSegCurvetoQuadraticAbs.prototype, \"y\", {\n        get: function () {\n          return this._y;\n        },\n        set: function (y) {\n          this._y = y;\n\n          this._segmentChanged();\n        },\n        enumerable: true\n      });\n      Object.defineProperty(window.SVGPathSegCurvetoQuadraticAbs.prototype, \"x1\", {\n        get: function () {\n          return this._x1;\n        },\n        set: function (x1) {\n          this._x1 = x1;\n\n          this._segmentChanged();\n        },\n        enumerable: true\n      });\n      Object.defineProperty(window.SVGPathSegCurvetoQuadraticAbs.prototype, \"y1\", {\n        get: function () {\n          return this._y1;\n        },\n        set: function (y1) {\n          this._y1 = y1;\n\n          this._segmentChanged();\n        },\n        enumerable: true\n      });\n\n      window.SVGPathSegCurvetoQuadraticRel = function (owningPathSegList, x, y, x1, y1) {\n        window.SVGPathSeg.call(this, window.SVGPathSeg.PATHSEG_CURVETO_QUADRATIC_REL, \"q\", owningPathSegList);\n        this._x = x;\n        this._y = y;\n        this._x1 = x1;\n        this._y1 = y1;\n      };\n\n      window.SVGPathSegCurvetoQuadraticRel.prototype = Object.create(window.SVGPathSeg.prototype);\n\n      window.SVGPathSegCurvetoQuadraticRel.prototype.toString = function () {\n        return \"[object SVGPathSegCurvetoQuadraticRel]\";\n      };\n\n      window.SVGPathSegCurvetoQuadraticRel.prototype._asPathString = function () {\n        return this.pathSegTypeAsLetter + \" \" + this._x1 + \" \" + this._y1 + \" \" + this._x + \" \" + this._y;\n      };\n\n      window.SVGPathSegCurvetoQuadraticRel.prototype.clone = function () {\n        return new window.SVGPathSegCurvetoQuadraticRel(undefined, this._x, this._y, this._x1, this._y1);\n      };\n\n      Object.defineProperty(window.SVGPathSegCurvetoQuadraticRel.prototype, \"x\", {\n        get: function () {\n          return this._x;\n        },\n        set: function (x) {\n          this._x = x;\n\n          this._segmentChanged();\n        },\n        enumerable: true\n      });\n      Object.defineProperty(window.SVGPathSegCurvetoQuadraticRel.prototype, \"y\", {\n        get: function () {\n          return this._y;\n        },\n        set: function (y) {\n          this._y = y;\n\n          this._segmentChanged();\n        },\n        enumerable: true\n      });\n      Object.defineProperty(window.SVGPathSegCurvetoQuadraticRel.prototype, \"x1\", {\n        get: function () {\n          return this._x1;\n        },\n        set: function (x1) {\n          this._x1 = x1;\n\n          this._segmentChanged();\n        },\n        enumerable: true\n      });\n      Object.defineProperty(window.SVGPathSegCurvetoQuadraticRel.prototype, \"y1\", {\n        get: function () {\n          return this._y1;\n        },\n        set: function (y1) {\n          this._y1 = y1;\n\n          this._segmentChanged();\n        },\n        enumerable: true\n      });\n\n      window.SVGPathSegArcAbs = function (owningPathSegList, x, y, r1, r2, angle, largeArcFlag, sweepFlag) {\n        window.SVGPathSeg.call(this, window.SVGPathSeg.PATHSEG_ARC_ABS, \"A\", owningPathSegList);\n        this._x = x;\n        this._y = y;\n        this._r1 = r1;\n        this._r2 = r2;\n        this._angle = angle;\n        this._largeArcFlag = largeArcFlag;\n        this._sweepFlag = sweepFlag;\n      };\n\n      window.SVGPathSegArcAbs.prototype = Object.create(window.SVGPathSeg.prototype);\n\n      window.SVGPathSegArcAbs.prototype.toString = function () {\n        return \"[object SVGPathSegArcAbs]\";\n      };\n\n      window.SVGPathSegArcAbs.prototype._asPathString = function () {\n        return this.pathSegTypeAsLetter + \" \" + this._r1 + \" \" + this._r2 + \" \" + this._angle + \" \" + (this._largeArcFlag ? \"1\" : \"0\") + \" \" + (this._sweepFlag ? \"1\" : \"0\") + \" \" + this._x + \" \" + this._y;\n      };\n\n      window.SVGPathSegArcAbs.prototype.clone = function () {\n        return new window.SVGPathSegArcAbs(undefined, this._x, this._y, this._r1, this._r2, this._angle, this._largeArcFlag, this._sweepFlag);\n      };\n\n      Object.defineProperty(window.SVGPathSegArcAbs.prototype, \"x\", {\n        get: function () {\n          return this._x;\n        },\n        set: function (x) {\n          this._x = x;\n\n          this._segmentChanged();\n        },\n        enumerable: true\n      });\n      Object.defineProperty(window.SVGPathSegArcAbs.prototype, \"y\", {\n        get: function () {\n          return this._y;\n        },\n        set: function (y) {\n          this._y = y;\n\n          this._segmentChanged();\n        },\n        enumerable: true\n      });\n      Object.defineProperty(window.SVGPathSegArcAbs.prototype, \"r1\", {\n        get: function () {\n          return this._r1;\n        },\n        set: function (r1) {\n          this._r1 = r1;\n\n          this._segmentChanged();\n        },\n        enumerable: true\n      });\n      Object.defineProperty(window.SVGPathSegArcAbs.prototype, \"r2\", {\n        get: function () {\n          return this._r2;\n        },\n        set: function (r2) {\n          this._r2 = r2;\n\n          this._segmentChanged();\n        },\n        enumerable: true\n      });\n      Object.defineProperty(window.SVGPathSegArcAbs.prototype, \"angle\", {\n        get: function () {\n          return this._angle;\n        },\n        set: function (angle) {\n          this._angle = angle;\n\n          this._segmentChanged();\n        },\n        enumerable: true\n      });\n      Object.defineProperty(window.SVGPathSegArcAbs.prototype, \"largeArcFlag\", {\n        get: function () {\n          return this._largeArcFlag;\n        },\n        set: function (largeArcFlag) {\n          this._largeArcFlag = largeArcFlag;\n\n          this._segmentChanged();\n        },\n        enumerable: true\n      });\n      Object.defineProperty(window.SVGPathSegArcAbs.prototype, \"sweepFlag\", {\n        get: function () {\n          return this._sweepFlag;\n        },\n        set: function (sweepFlag) {\n          this._sweepFlag = sweepFlag;\n\n          this._segmentChanged();\n        },\n        enumerable: true\n      });\n\n      window.SVGPathSegArcRel = function (owningPathSegList, x, y, r1, r2, angle, largeArcFlag, sweepFlag) {\n        window.SVGPathSeg.call(this, window.SVGPathSeg.PATHSEG_ARC_REL, \"a\", owningPathSegList);\n        this._x = x;\n        this._y = y;\n        this._r1 = r1;\n        this._r2 = r2;\n        this._angle = angle;\n        this._largeArcFlag = largeArcFlag;\n        this._sweepFlag = sweepFlag;\n      };\n\n      window.SVGPathSegArcRel.prototype = Object.create(window.SVGPathSeg.prototype);\n\n      window.SVGPathSegArcRel.prototype.toString = function () {\n        return \"[object SVGPathSegArcRel]\";\n      };\n\n      window.SVGPathSegArcRel.prototype._asPathString = function () {\n        return this.pathSegTypeAsLetter + \" \" + this._r1 + \" \" + this._r2 + \" \" + this._angle + \" \" + (this._largeArcFlag ? \"1\" : \"0\") + \" \" + (this._sweepFlag ? \"1\" : \"0\") + \" \" + this._x + \" \" + this._y;\n      };\n\n      window.SVGPathSegArcRel.prototype.clone = function () {\n        return new window.SVGPathSegArcRel(undefined, this._x, this._y, this._r1, this._r2, this._angle, this._largeArcFlag, this._sweepFlag);\n      };\n\n      Object.defineProperty(window.SVGPathSegArcRel.prototype, \"x\", {\n        get: function () {\n          return this._x;\n        },\n        set: function (x) {\n          this._x = x;\n\n          this._segmentChanged();\n        },\n        enumerable: true\n      });\n      Object.defineProperty(window.SVGPathSegArcRel.prototype, \"y\", {\n        get: function () {\n          return this._y;\n        },\n        set: function (y) {\n          this._y = y;\n\n          this._segmentChanged();\n        },\n        enumerable: true\n      });\n      Object.defineProperty(window.SVGPathSegArcRel.prototype, \"r1\", {\n        get: function () {\n          return this._r1;\n        },\n        set: function (r1) {\n          this._r1 = r1;\n\n          this._segmentChanged();\n        },\n        enumerable: true\n      });\n      Object.defineProperty(window.SVGPathSegArcRel.prototype, \"r2\", {\n        get: function () {\n          return this._r2;\n        },\n        set: function (r2) {\n          this._r2 = r2;\n\n          this._segmentChanged();\n        },\n        enumerable: true\n      });\n      Object.defineProperty(window.SVGPathSegArcRel.prototype, \"angle\", {\n        get: function () {\n          return this._angle;\n        },\n        set: function (angle) {\n          this._angle = angle;\n\n          this._segmentChanged();\n        },\n        enumerable: true\n      });\n      Object.defineProperty(window.SVGPathSegArcRel.prototype, \"largeArcFlag\", {\n        get: function () {\n          return this._largeArcFlag;\n        },\n        set: function (largeArcFlag) {\n          this._largeArcFlag = largeArcFlag;\n\n          this._segmentChanged();\n        },\n        enumerable: true\n      });\n      Object.defineProperty(window.SVGPathSegArcRel.prototype, \"sweepFlag\", {\n        get: function () {\n          return this._sweepFlag;\n        },\n        set: function (sweepFlag) {\n          this._sweepFlag = sweepFlag;\n\n          this._segmentChanged();\n        },\n        enumerable: true\n      });\n\n      window.SVGPathSegLinetoHorizontalAbs = function (owningPathSegList, x) {\n        window.SVGPathSeg.call(this, window.SVGPathSeg.PATHSEG_LINETO_HORIZONTAL_ABS, \"H\", owningPathSegList);\n        this._x = x;\n      };\n\n      window.SVGPathSegLinetoHorizontalAbs.prototype = Object.create(window.SVGPathSeg.prototype);\n\n      window.SVGPathSegLinetoHorizontalAbs.prototype.toString = function () {\n        return \"[object SVGPathSegLinetoHorizontalAbs]\";\n      };\n\n      window.SVGPathSegLinetoHorizontalAbs.prototype._asPathString = function () {\n        return this.pathSegTypeAsLetter + \" \" + this._x;\n      };\n\n      window.SVGPathSegLinetoHorizontalAbs.prototype.clone = function () {\n        return new window.SVGPathSegLinetoHorizontalAbs(undefined, this._x);\n      };\n\n      Object.defineProperty(window.SVGPathSegLinetoHorizontalAbs.prototype, \"x\", {\n        get: function () {\n          return this._x;\n        },\n        set: function (x) {\n          this._x = x;\n\n          this._segmentChanged();\n        },\n        enumerable: true\n      });\n\n      window.SVGPathSegLinetoHorizontalRel = function (owningPathSegList, x) {\n        window.SVGPathSeg.call(this, window.SVGPathSeg.PATHSEG_LINETO_HORIZONTAL_REL, \"h\", owningPathSegList);\n        this._x = x;\n      };\n\n      window.SVGPathSegLinetoHorizontalRel.prototype = Object.create(window.SVGPathSeg.prototype);\n\n      window.SVGPathSegLinetoHorizontalRel.prototype.toString = function () {\n        return \"[object SVGPathSegLinetoHorizontalRel]\";\n      };\n\n      window.SVGPathSegLinetoHorizontalRel.prototype._asPathString = function () {\n        return this.pathSegTypeAsLetter + \" \" + this._x;\n      };\n\n      window.SVGPathSegLinetoHorizontalRel.prototype.clone = function () {\n        return new window.SVGPathSegLinetoHorizontalRel(undefined, this._x);\n      };\n\n      Object.defineProperty(window.SVGPathSegLinetoHorizontalRel.prototype, \"x\", {\n        get: function () {\n          return this._x;\n        },\n        set: function (x) {\n          this._x = x;\n\n          this._segmentChanged();\n        },\n        enumerable: true\n      });\n\n      window.SVGPathSegLinetoVerticalAbs = function (owningPathSegList, y) {\n        window.SVGPathSeg.call(this, window.SVGPathSeg.PATHSEG_LINETO_VERTICAL_ABS, \"V\", owningPathSegList);\n        this._y = y;\n      };\n\n      window.SVGPathSegLinetoVerticalAbs.prototype = Object.create(window.SVGPathSeg.prototype);\n\n      window.SVGPathSegLinetoVerticalAbs.prototype.toString = function () {\n        return \"[object SVGPathSegLinetoVerticalAbs]\";\n      };\n\n      window.SVGPathSegLinetoVerticalAbs.prototype._asPathString = function () {\n        return this.pathSegTypeAsLetter + \" \" + this._y;\n      };\n\n      window.SVGPathSegLinetoVerticalAbs.prototype.clone = function () {\n        return new window.SVGPathSegLinetoVerticalAbs(undefined, this._y);\n      };\n\n      Object.defineProperty(window.SVGPathSegLinetoVerticalAbs.prototype, \"y\", {\n        get: function () {\n          return this._y;\n        },\n        set: function (y) {\n          this._y = y;\n\n          this._segmentChanged();\n        },\n        enumerable: true\n      });\n\n      window.SVGPathSegLinetoVerticalRel = function (owningPathSegList, y) {\n        window.SVGPathSeg.call(this, window.SVGPathSeg.PATHSEG_LINETO_VERTICAL_REL, \"v\", owningPathSegList);\n        this._y = y;\n      };\n\n      window.SVGPathSegLinetoVerticalRel.prototype = Object.create(window.SVGPathSeg.prototype);\n\n      window.SVGPathSegLinetoVerticalRel.prototype.toString = function () {\n        return \"[object SVGPathSegLinetoVerticalRel]\";\n      };\n\n      window.SVGPathSegLinetoVerticalRel.prototype._asPathString = function () {\n        return this.pathSegTypeAsLetter + \" \" + this._y;\n      };\n\n      window.SVGPathSegLinetoVerticalRel.prototype.clone = function () {\n        return new window.SVGPathSegLinetoVerticalRel(undefined, this._y);\n      };\n\n      Object.defineProperty(window.SVGPathSegLinetoVerticalRel.prototype, \"y\", {\n        get: function () {\n          return this._y;\n        },\n        set: function (y) {\n          this._y = y;\n\n          this._segmentChanged();\n        },\n        enumerable: true\n      });\n\n      window.SVGPathSegCurvetoCubicSmoothAbs = function (owningPathSegList, x, y, x2, y2) {\n        window.SVGPathSeg.call(this, window.SVGPathSeg.PATHSEG_CURVETO_CUBIC_SMOOTH_ABS, \"S\", owningPathSegList);\n        this._x = x;\n        this._y = y;\n        this._x2 = x2;\n        this._y2 = y2;\n      };\n\n      window.SVGPathSegCurvetoCubicSmoothAbs.prototype = Object.create(window.SVGPathSeg.prototype);\n\n      window.SVGPathSegCurvetoCubicSmoothAbs.prototype.toString = function () {\n        return \"[object SVGPathSegCurvetoCubicSmoothAbs]\";\n      };\n\n      window.SVGPathSegCurvetoCubicSmoothAbs.prototype._asPathString = function () {\n        return this.pathSegTypeAsLetter + \" \" + this._x2 + \" \" + this._y2 + \" \" + this._x + \" \" + this._y;\n      };\n\n      window.SVGPathSegCurvetoCubicSmoothAbs.prototype.clone = function () {\n        return new window.SVGPathSegCurvetoCubicSmoothAbs(undefined, this._x, this._y, this._x2, this._y2);\n      };\n\n      Object.defineProperty(window.SVGPathSegCurvetoCubicSmoothAbs.prototype, \"x\", {\n        get: function () {\n          return this._x;\n        },\n        set: function (x) {\n          this._x = x;\n\n          this._segmentChanged();\n        },\n        enumerable: true\n      });\n      Object.defineProperty(window.SVGPathSegCurvetoCubicSmoothAbs.prototype, \"y\", {\n        get: function () {\n          return this._y;\n        },\n        set: function (y) {\n          this._y = y;\n\n          this._segmentChanged();\n        },\n        enumerable: true\n      });\n      Object.defineProperty(window.SVGPathSegCurvetoCubicSmoothAbs.prototype, \"x2\", {\n        get: function () {\n          return this._x2;\n        },\n        set: function (x2) {\n          this._x2 = x2;\n\n          this._segmentChanged();\n        },\n        enumerable: true\n      });\n      Object.defineProperty(window.SVGPathSegCurvetoCubicSmoothAbs.prototype, \"y2\", {\n        get: function () {\n          return this._y2;\n        },\n        set: function (y2) {\n          this._y2 = y2;\n\n          this._segmentChanged();\n        },\n        enumerable: true\n      });\n\n      window.SVGPathSegCurvetoCubicSmoothRel = function (owningPathSegList, x, y, x2, y2) {\n        window.SVGPathSeg.call(this, window.SVGPathSeg.PATHSEG_CURVETO_CUBIC_SMOOTH_REL, \"s\", owningPathSegList);\n        this._x = x;\n        this._y = y;\n        this._x2 = x2;\n        this._y2 = y2;\n      };\n\n      window.SVGPathSegCurvetoCubicSmoothRel.prototype = Object.create(window.SVGPathSeg.prototype);\n\n      window.SVGPathSegCurvetoCubicSmoothRel.prototype.toString = function () {\n        return \"[object SVGPathSegCurvetoCubicSmoothRel]\";\n      };\n\n      window.SVGPathSegCurvetoCubicSmoothRel.prototype._asPathString = function () {\n        return this.pathSegTypeAsLetter + \" \" + this._x2 + \" \" + this._y2 + \" \" + this._x + \" \" + this._y;\n      };\n\n      window.SVGPathSegCurvetoCubicSmoothRel.prototype.clone = function () {\n        return new window.SVGPathSegCurvetoCubicSmoothRel(undefined, this._x, this._y, this._x2, this._y2);\n      };\n\n      Object.defineProperty(window.SVGPathSegCurvetoCubicSmoothRel.prototype, \"x\", {\n        get: function () {\n          return this._x;\n        },\n        set: function (x) {\n          this._x = x;\n\n          this._segmentChanged();\n        },\n        enumerable: true\n      });\n      Object.defineProperty(window.SVGPathSegCurvetoCubicSmoothRel.prototype, \"y\", {\n        get: function () {\n          return this._y;\n        },\n        set: function (y) {\n          this._y = y;\n\n          this._segmentChanged();\n        },\n        enumerable: true\n      });\n      Object.defineProperty(window.SVGPathSegCurvetoCubicSmoothRel.prototype, \"x2\", {\n        get: function () {\n          return this._x2;\n        },\n        set: function (x2) {\n          this._x2 = x2;\n\n          this._segmentChanged();\n        },\n        enumerable: true\n      });\n      Object.defineProperty(window.SVGPathSegCurvetoCubicSmoothRel.prototype, \"y2\", {\n        get: function () {\n          return this._y2;\n        },\n        set: function (y2) {\n          this._y2 = y2;\n\n          this._segmentChanged();\n        },\n        enumerable: true\n      });\n\n      window.SVGPathSegCurvetoQuadraticSmoothAbs = function (owningPathSegList, x, y) {\n        window.SVGPathSeg.call(this, window.SVGPathSeg.PATHSEG_CURVETO_QUADRATIC_SMOOTH_ABS, \"T\", owningPathSegList);\n        this._x = x;\n        this._y = y;\n      };\n\n      window.SVGPathSegCurvetoQuadraticSmoothAbs.prototype = Object.create(window.SVGPathSeg.prototype);\n\n      window.SVGPathSegCurvetoQuadraticSmoothAbs.prototype.toString = function () {\n        return \"[object SVGPathSegCurvetoQuadraticSmoothAbs]\";\n      };\n\n      window.SVGPathSegCurvetoQuadraticSmoothAbs.prototype._asPathString = function () {\n        return this.pathSegTypeAsLetter + \" \" + this._x + \" \" + this._y;\n      };\n\n      window.SVGPathSegCurvetoQuadraticSmoothAbs.prototype.clone = function () {\n        return new window.SVGPathSegCurvetoQuadraticSmoothAbs(undefined, this._x, this._y);\n      };\n\n      Object.defineProperty(window.SVGPathSegCurvetoQuadraticSmoothAbs.prototype, \"x\", {\n        get: function () {\n          return this._x;\n        },\n        set: function (x) {\n          this._x = x;\n\n          this._segmentChanged();\n        },\n        enumerable: true\n      });\n      Object.defineProperty(window.SVGPathSegCurvetoQuadraticSmoothAbs.prototype, \"y\", {\n        get: function () {\n          return this._y;\n        },\n        set: function (y) {\n          this._y = y;\n\n          this._segmentChanged();\n        },\n        enumerable: true\n      });\n\n      window.SVGPathSegCurvetoQuadraticSmoothRel = function (owningPathSegList, x, y) {\n        window.SVGPathSeg.call(this, window.SVGPathSeg.PATHSEG_CURVETO_QUADRATIC_SMOOTH_REL, \"t\", owningPathSegList);\n        this._x = x;\n        this._y = y;\n      };\n\n      window.SVGPathSegCurvetoQuadraticSmoothRel.prototype = Object.create(window.SVGPathSeg.prototype);\n\n      window.SVGPathSegCurvetoQuadraticSmoothRel.prototype.toString = function () {\n        return \"[object SVGPathSegCurvetoQuadraticSmoothRel]\";\n      };\n\n      window.SVGPathSegCurvetoQuadraticSmoothRel.prototype._asPathString = function () {\n        return this.pathSegTypeAsLetter + \" \" + this._x + \" \" + this._y;\n      };\n\n      window.SVGPathSegCurvetoQuadraticSmoothRel.prototype.clone = function () {\n        return new window.SVGPathSegCurvetoQuadraticSmoothRel(undefined, this._x, this._y);\n      };\n\n      Object.defineProperty(window.SVGPathSegCurvetoQuadraticSmoothRel.prototype, \"x\", {\n        get: function () {\n          return this._x;\n        },\n        set: function (x) {\n          this._x = x;\n\n          this._segmentChanged();\n        },\n        enumerable: true\n      });\n      Object.defineProperty(window.SVGPathSegCurvetoQuadraticSmoothRel.prototype, \"y\", {\n        get: function () {\n          return this._y;\n        },\n        set: function (y) {\n          this._y = y;\n\n          this._segmentChanged();\n        },\n        enumerable: true\n      });\n\n      window.SVGPathElement.prototype.createSVGPathSegClosePath = function () {\n        return new window.SVGPathSegClosePath(undefined);\n      };\n\n      window.SVGPathElement.prototype.createSVGPathSegMovetoAbs = function (x, y) {\n        return new window.SVGPathSegMovetoAbs(undefined, x, y);\n      };\n\n      window.SVGPathElement.prototype.createSVGPathSegMovetoRel = function (x, y) {\n        return new window.SVGPathSegMovetoRel(undefined, x, y);\n      };\n\n      window.SVGPathElement.prototype.createSVGPathSegLinetoAbs = function (x, y) {\n        return new window.SVGPathSegLinetoAbs(undefined, x, y);\n      };\n\n      window.SVGPathElement.prototype.createSVGPathSegLinetoRel = function (x, y) {\n        return new window.SVGPathSegLinetoRel(undefined, x, y);\n      };\n\n      window.SVGPathElement.prototype.createSVGPathSegCurvetoCubicAbs = function (x, y, x1, y1, x2, y2) {\n        return new window.SVGPathSegCurvetoCubicAbs(undefined, x, y, x1, y1, x2, y2);\n      };\n\n      window.SVGPathElement.prototype.createSVGPathSegCurvetoCubicRel = function (x, y, x1, y1, x2, y2) {\n        return new window.SVGPathSegCurvetoCubicRel(undefined, x, y, x1, y1, x2, y2);\n      };\n\n      window.SVGPathElement.prototype.createSVGPathSegCurvetoQuadraticAbs = function (x, y, x1, y1) {\n        return new window.SVGPathSegCurvetoQuadraticAbs(undefined, x, y, x1, y1);\n      };\n\n      window.SVGPathElement.prototype.createSVGPathSegCurvetoQuadraticRel = function (x, y, x1, y1) {\n        return new window.SVGPathSegCurvetoQuadraticRel(undefined, x, y, x1, y1);\n      };\n\n      window.SVGPathElement.prototype.createSVGPathSegArcAbs = function (x, y, r1, r2, angle, largeArcFlag, sweepFlag) {\n        return new window.SVGPathSegArcAbs(undefined, x, y, r1, r2, angle, largeArcFlag, sweepFlag);\n      };\n\n      window.SVGPathElement.prototype.createSVGPathSegArcRel = function (x, y, r1, r2, angle, largeArcFlag, sweepFlag) {\n        return new window.SVGPathSegArcRel(undefined, x, y, r1, r2, angle, largeArcFlag, sweepFlag);\n      };\n\n      window.SVGPathElement.prototype.createSVGPathSegLinetoHorizontalAbs = function (x) {\n        return new window.SVGPathSegLinetoHorizontalAbs(undefined, x);\n      };\n\n      window.SVGPathElement.prototype.createSVGPathSegLinetoHorizontalRel = function (x) {\n        return new window.SVGPathSegLinetoHorizontalRel(undefined, x);\n      };\n\n      window.SVGPathElement.prototype.createSVGPathSegLinetoVerticalAbs = function (y) {\n        return new window.SVGPathSegLinetoVerticalAbs(undefined, y);\n      };\n\n      window.SVGPathElement.prototype.createSVGPathSegLinetoVerticalRel = function (y) {\n        return new window.SVGPathSegLinetoVerticalRel(undefined, y);\n      };\n\n      window.SVGPathElement.prototype.createSVGPathSegCurvetoCubicSmoothAbs = function (x, y, x2, y2) {\n        return new window.SVGPathSegCurvetoCubicSmoothAbs(undefined, x, y, x2, y2);\n      };\n\n      window.SVGPathElement.prototype.createSVGPathSegCurvetoCubicSmoothRel = function (x, y, x2, y2) {\n        return new window.SVGPathSegCurvetoCubicSmoothRel(undefined, x, y, x2, y2);\n      };\n\n      window.SVGPathElement.prototype.createSVGPathSegCurvetoQuadraticSmoothAbs = function (x, y) {\n        return new window.SVGPathSegCurvetoQuadraticSmoothAbs(undefined, x, y);\n      };\n\n      window.SVGPathElement.prototype.createSVGPathSegCurvetoQuadraticSmoothRel = function (x, y) {\n        return new window.SVGPathSegCurvetoQuadraticSmoothRel(undefined, x, y);\n      };\n\n      if (!(\"getPathSegAtLength\" in window.SVGPathElement.prototype)) {\n        window.SVGPathElement.prototype.getPathSegAtLength = function (distance) {\n          if (distance === undefined || !isFinite(distance)) throw \"Invalid arguments.\";\n          const measurementElement = document.createElementNS(\"http://www.w3.org/2000/svg\", \"path\");\n          measurementElement.setAttribute(\"d\", this.getAttribute(\"d\"));\n          let lastPathSegment = measurementElement.pathSegList.numberOfItems - 1;\n          if (lastPathSegment <= 0) return 0;\n\n          do {\n            measurementElement.pathSegList.removeItem(lastPathSegment);\n            if (distance > measurementElement.getTotalLength()) break;\n            lastPathSegment--;\n          } while (lastPathSegment > 0);\n\n          return lastPathSegment;\n        };\n      }\n    }\n\n    if (!(\"SVGPathSegList\" in window) || !(\"appendItem\" in window.SVGPathSegList.prototype)) {\n      window.SVGPathSegList = function (pathElement) {\n        this._pathElement = pathElement;\n        this._list = this._parsePath(this._pathElement.getAttribute(\"d\"));\n        this._mutationObserverConfig = {\n          attributes: true,\n          attributeFilter: [\"d\"]\n        };\n        this._pathElementMutationObserver = new MutationObserver(this._updateListFromPathMutations.bind(this));\n\n        this._pathElementMutationObserver.observe(this._pathElement, this._mutationObserverConfig);\n      };\n\n      window.SVGPathSegList.prototype.classname = \"SVGPathSegList\";\n      Object.defineProperty(window.SVGPathSegList.prototype, \"numberOfItems\", {\n        get: function () {\n          this._checkPathSynchronizedToList();\n\n          return this._list.length;\n        },\n        enumerable: true\n      });\n      Object.defineProperty(window.SVGPathSegList.prototype, \"length\", {\n        get: function () {\n          this._checkPathSynchronizedToList();\n\n          return this._list.length;\n        },\n        enumerable: true\n      });\n      Object.defineProperty(window.SVGPathElement.prototype, \"pathSegList\", {\n        get: function () {\n          if (!this._pathSegList) this._pathSegList = new window.SVGPathSegList(this);\n          return this._pathSegList;\n        },\n        enumerable: true\n      });\n      Object.defineProperty(window.SVGPathElement.prototype, \"normalizedPathSegList\", {\n        get: function () {\n          return this.pathSegList;\n        },\n        enumerable: true\n      });\n      Object.defineProperty(window.SVGPathElement.prototype, \"animatedPathSegList\", {\n        get: function () {\n          return this.pathSegList;\n        },\n        enumerable: true\n      });\n      Object.defineProperty(window.SVGPathElement.prototype, \"animatedNormalizedPathSegList\", {\n        get: function () {\n          return this.pathSegList;\n        },\n        enumerable: true\n      });\n\n      window.SVGPathSegList.prototype._checkPathSynchronizedToList = function () {\n        this._updateListFromPathMutations(this._pathElementMutationObserver.takeRecords());\n      };\n\n      window.SVGPathSegList.prototype._updateListFromPathMutations = function (mutationRecords) {\n        if (!this._pathElement) return;\n        let hasPathMutations = false;\n        mutationRecords.forEach(function (record) {\n          if (record.attributeName == \"d\") hasPathMutations = true;\n        });\n        if (hasPathMutations) this._list = this._parsePath(this._pathElement.getAttribute(\"d\"));\n      };\n\n      window.SVGPathSegList.prototype._writeListToPath = function () {\n        this._pathElementMutationObserver.disconnect();\n\n        this._pathElement.setAttribute(\"d\", window.SVGPathSegList._pathSegArrayAsString(this._list));\n\n        this._pathElementMutationObserver.observe(this._pathElement, this._mutationObserverConfig);\n      };\n\n      window.SVGPathSegList.prototype.segmentChanged = function (pathSeg) {\n        this._writeListToPath();\n      };\n\n      window.SVGPathSegList.prototype.clear = function () {\n        this._checkPathSynchronizedToList();\n\n        this._list.forEach(function (pathSeg) {\n          pathSeg._owningPathSegList = null;\n        });\n\n        this._list = [];\n\n        this._writeListToPath();\n      };\n\n      window.SVGPathSegList.prototype.initialize = function (newItem) {\n        this._checkPathSynchronizedToList();\n\n        this._list = [newItem];\n        newItem._owningPathSegList = this;\n\n        this._writeListToPath();\n\n        return newItem;\n      };\n\n      window.SVGPathSegList.prototype._checkValidIndex = function (index) {\n        if (isNaN(index) || index < 0 || index >= this.numberOfItems) throw \"INDEX_SIZE_ERR\";\n      };\n\n      window.SVGPathSegList.prototype.getItem = function (index) {\n        this._checkPathSynchronizedToList();\n\n        this._checkValidIndex(index);\n\n        return this._list[index];\n      };\n\n      window.SVGPathSegList.prototype.insertItemBefore = function (newItem, index) {\n        this._checkPathSynchronizedToList();\n\n        if (index > this.numberOfItems) index = this.numberOfItems;\n\n        if (newItem._owningPathSegList) {\n          newItem = newItem.clone();\n        }\n\n        this._list.splice(index, 0, newItem);\n\n        newItem._owningPathSegList = this;\n\n        this._writeListToPath();\n\n        return newItem;\n      };\n\n      window.SVGPathSegList.prototype.replaceItem = function (newItem, index) {\n        this._checkPathSynchronizedToList();\n\n        if (newItem._owningPathSegList) {\n          newItem = newItem.clone();\n        }\n\n        this._checkValidIndex(index);\n\n        this._list[index] = newItem;\n        newItem._owningPathSegList = this;\n\n        this._writeListToPath();\n\n        return newItem;\n      };\n\n      window.SVGPathSegList.prototype.removeItem = function (index) {\n        this._checkPathSynchronizedToList();\n\n        this._checkValidIndex(index);\n\n        const item = this._list[index];\n\n        this._list.splice(index, 1);\n\n        this._writeListToPath();\n\n        return item;\n      };\n\n      window.SVGPathSegList.prototype.appendItem = function (newItem) {\n        this._checkPathSynchronizedToList();\n\n        if (newItem._owningPathSegList) {\n          newItem = newItem.clone();\n        }\n\n        this._list.push(newItem);\n\n        newItem._owningPathSegList = this;\n\n        this._writeListToPath();\n\n        return newItem;\n      };\n\n      window.SVGPathSegList._pathSegArrayAsString = function (pathSegArray) {\n        let string = \"\";\n        let first = true;\n        pathSegArray.forEach(function (pathSeg) {\n          if (first) {\n            first = false;\n            string += pathSeg._asPathString();\n          } else {\n            string += \" \" + pathSeg._asPathString();\n          }\n        });\n        return string;\n      };\n\n      window.SVGPathSegList.prototype._parsePath = function (string) {\n        if (!string || string.length == 0) return [];\n        const owningPathSegList = this;\n\n        const Builder = function () {\n          this.pathSegList = [];\n        };\n\n        Builder.prototype.appendSegment = function (pathSeg) {\n          this.pathSegList.push(pathSeg);\n        };\n\n        const Source = function (string) {\n          this._string = string;\n          this._currentIndex = 0;\n          this._endIndex = this._string.length;\n          this._previousCommand = window.SVGPathSeg.PATHSEG_UNKNOWN;\n\n          this._skipOptionalSpaces();\n        };\n\n        Source.prototype._isCurrentSpace = function () {\n          const character = this._string[this._currentIndex];\n          return character <= \" \" && (character == \" \" || character == \"\\n\" || character == \"\\t\" || character == \"\\r\" || character == \"\\f\");\n        };\n\n        Source.prototype._skipOptionalSpaces = function () {\n          while (this._currentIndex < this._endIndex && this._isCurrentSpace()) this._currentIndex++;\n\n          return this._currentIndex < this._endIndex;\n        };\n\n        Source.prototype._skipOptionalSpacesOrDelimiter = function () {\n          if (this._currentIndex < this._endIndex && !this._isCurrentSpace() && this._string.charAt(this._currentIndex) != \",\") return false;\n\n          if (this._skipOptionalSpaces()) {\n            if (this._currentIndex < this._endIndex && this._string.charAt(this._currentIndex) == \",\") {\n              this._currentIndex++;\n\n              this._skipOptionalSpaces();\n            }\n          }\n\n          return this._currentIndex < this._endIndex;\n        };\n\n        Source.prototype.hasMoreData = function () {\n          return this._currentIndex < this._endIndex;\n        };\n\n        Source.prototype.peekSegmentType = function () {\n          const lookahead = this._string[this._currentIndex];\n          return this._pathSegTypeFromChar(lookahead);\n        };\n\n        Source.prototype._pathSegTypeFromChar = function (lookahead) {\n          switch (lookahead) {\n            case \"Z\":\n            case \"z\":\n              return window.SVGPathSeg.PATHSEG_CLOSEPATH;\n\n            case \"M\":\n              return window.SVGPathSeg.PATHSEG_MOVETO_ABS;\n\n            case \"m\":\n              return window.SVGPathSeg.PATHSEG_MOVETO_REL;\n\n            case \"L\":\n              return window.SVGPathSeg.PATHSEG_LINETO_ABS;\n\n            case \"l\":\n              return window.SVGPathSeg.PATHSEG_LINETO_REL;\n\n            case \"C\":\n              return window.SVGPathSeg.PATHSEG_CURVETO_CUBIC_ABS;\n\n            case \"c\":\n              return window.SVGPathSeg.PATHSEG_CURVETO_CUBIC_REL;\n\n            case \"Q\":\n              return window.SVGPathSeg.PATHSEG_CURVETO_QUADRATIC_ABS;\n\n            case \"q\":\n              return window.SVGPathSeg.PATHSEG_CURVETO_QUADRATIC_REL;\n\n            case \"A\":\n              return window.SVGPathSeg.PATHSEG_ARC_ABS;\n\n            case \"a\":\n              return window.SVGPathSeg.PATHSEG_ARC_REL;\n\n            case \"H\":\n              return window.SVGPathSeg.PATHSEG_LINETO_HORIZONTAL_ABS;\n\n            case \"h\":\n              return window.SVGPathSeg.PATHSEG_LINETO_HORIZONTAL_REL;\n\n            case \"V\":\n              return window.SVGPathSeg.PATHSEG_LINETO_VERTICAL_ABS;\n\n            case \"v\":\n              return window.SVGPathSeg.PATHSEG_LINETO_VERTICAL_REL;\n\n            case \"S\":\n              return window.SVGPathSeg.PATHSEG_CURVETO_CUBIC_SMOOTH_ABS;\n\n            case \"s\":\n              return window.SVGPathSeg.PATHSEG_CURVETO_CUBIC_SMOOTH_REL;\n\n            case \"T\":\n              return window.SVGPathSeg.PATHSEG_CURVETO_QUADRATIC_SMOOTH_ABS;\n\n            case \"t\":\n              return window.SVGPathSeg.PATHSEG_CURVETO_QUADRATIC_SMOOTH_REL;\n\n            default:\n              return window.SVGPathSeg.PATHSEG_UNKNOWN;\n          }\n        };\n\n        Source.prototype._nextCommandHelper = function (lookahead, previousCommand) {\n          if ((lookahead == \"+\" || lookahead == \"-\" || lookahead == \".\" || lookahead >= \"0\" && lookahead <= \"9\") && previousCommand != window.SVGPathSeg.PATHSEG_CLOSEPATH) {\n            if (previousCommand == window.SVGPathSeg.PATHSEG_MOVETO_ABS) return window.SVGPathSeg.PATHSEG_LINETO_ABS;\n            if (previousCommand == window.SVGPathSeg.PATHSEG_MOVETO_REL) return window.SVGPathSeg.PATHSEG_LINETO_REL;\n            return previousCommand;\n          }\n\n          return window.SVGPathSeg.PATHSEG_UNKNOWN;\n        };\n\n        Source.prototype.initialCommandIsMoveTo = function () {\n          if (!this.hasMoreData()) return true;\n          const command = this.peekSegmentType();\n          return command == window.SVGPathSeg.PATHSEG_MOVETO_ABS || command == window.SVGPathSeg.PATHSEG_MOVETO_REL;\n        };\n\n        Source.prototype._parseNumber = function () {\n          let exponent = 0;\n          let integer = 0;\n          let frac = 1;\n          let decimal = 0;\n          let sign = 1;\n          let expsign = 1;\n          const startIndex = this._currentIndex;\n\n          this._skipOptionalSpaces();\n\n          if (this._currentIndex < this._endIndex && this._string.charAt(this._currentIndex) == \"+\") this._currentIndex++;else if (this._currentIndex < this._endIndex && this._string.charAt(this._currentIndex) == \"-\") {\n            this._currentIndex++;\n            sign = -1;\n          }\n          if (this._currentIndex == this._endIndex || (this._string.charAt(this._currentIndex) < \"0\" || this._string.charAt(this._currentIndex) > \"9\") && this._string.charAt(this._currentIndex) != \".\") return undefined;\n          const startIntPartIndex = this._currentIndex;\n\n          while (this._currentIndex < this._endIndex && this._string.charAt(this._currentIndex) >= \"0\" && this._string.charAt(this._currentIndex) <= \"9\") this._currentIndex++;\n\n          if (this._currentIndex != startIntPartIndex) {\n            let scanIntPartIndex = this._currentIndex - 1;\n            let multiplier = 1;\n\n            while (scanIntPartIndex >= startIntPartIndex) {\n              integer += multiplier * (this._string.charAt(scanIntPartIndex--) - \"0\");\n              multiplier *= 10;\n            }\n          }\n\n          if (this._currentIndex < this._endIndex && this._string.charAt(this._currentIndex) == \".\") {\n            this._currentIndex++;\n            if (this._currentIndex >= this._endIndex || this._string.charAt(this._currentIndex) < \"0\" || this._string.charAt(this._currentIndex) > \"9\") return undefined;\n\n            while (this._currentIndex < this._endIndex && this._string.charAt(this._currentIndex) >= \"0\" && this._string.charAt(this._currentIndex) <= \"9\") {\n              frac *= 10;\n              decimal += (this._string.charAt(this._currentIndex) - \"0\") / frac;\n              this._currentIndex += 1;\n            }\n          }\n\n          if (this._currentIndex != startIndex && this._currentIndex + 1 < this._endIndex && (this._string.charAt(this._currentIndex) == \"e\" || this._string.charAt(this._currentIndex) == \"E\") && this._string.charAt(this._currentIndex + 1) != \"x\" && this._string.charAt(this._currentIndex + 1) != \"m\") {\n            this._currentIndex++;\n\n            if (this._string.charAt(this._currentIndex) == \"+\") {\n              this._currentIndex++;\n            } else if (this._string.charAt(this._currentIndex) == \"-\") {\n              this._currentIndex++;\n              expsign = -1;\n            }\n\n            if (this._currentIndex >= this._endIndex || this._string.charAt(this._currentIndex) < \"0\" || this._string.charAt(this._currentIndex) > \"9\") return undefined;\n\n            while (this._currentIndex < this._endIndex && this._string.charAt(this._currentIndex) >= \"0\" && this._string.charAt(this._currentIndex) <= \"9\") {\n              exponent *= 10;\n              exponent += this._string.charAt(this._currentIndex) - \"0\";\n              this._currentIndex++;\n            }\n          }\n\n          let number = integer + decimal;\n          number *= sign;\n          if (exponent) number *= Math.pow(10, expsign * exponent);\n          if (startIndex == this._currentIndex) return undefined;\n\n          this._skipOptionalSpacesOrDelimiter();\n\n          return number;\n        };\n\n        Source.prototype._parseArcFlag = function () {\n          if (this._currentIndex >= this._endIndex) return undefined;\n          let flag = false;\n\n          const flagChar = this._string.charAt(this._currentIndex++);\n\n          if (flagChar == \"0\") flag = false;else if (flagChar == \"1\") flag = true;else return undefined;\n\n          this._skipOptionalSpacesOrDelimiter();\n\n          return flag;\n        };\n\n        Source.prototype.parseSegment = function () {\n          const lookahead = this._string[this._currentIndex];\n\n          let command = this._pathSegTypeFromChar(lookahead);\n\n          if (command == window.SVGPathSeg.PATHSEG_UNKNOWN) {\n            if (this._previousCommand == window.SVGPathSeg.PATHSEG_UNKNOWN) return null;\n            command = this._nextCommandHelper(lookahead, this._previousCommand);\n            if (command == window.SVGPathSeg.PATHSEG_UNKNOWN) return null;\n          } else {\n            this._currentIndex++;\n          }\n\n          this._previousCommand = command;\n          let points;\n\n          switch (command) {\n            case window.SVGPathSeg.PATHSEG_MOVETO_REL:\n              return new window.SVGPathSegMovetoRel(owningPathSegList, this._parseNumber(), this._parseNumber());\n\n            case window.SVGPathSeg.PATHSEG_MOVETO_ABS:\n              return new window.SVGPathSegMovetoAbs(owningPathSegList, this._parseNumber(), this._parseNumber());\n\n            case window.SVGPathSeg.PATHSEG_LINETO_REL:\n              return new window.SVGPathSegLinetoRel(owningPathSegList, this._parseNumber(), this._parseNumber());\n\n            case window.SVGPathSeg.PATHSEG_LINETO_ABS:\n              return new window.SVGPathSegLinetoAbs(owningPathSegList, this._parseNumber(), this._parseNumber());\n\n            case window.SVGPathSeg.PATHSEG_LINETO_HORIZONTAL_REL:\n              return new window.SVGPathSegLinetoHorizontalRel(owningPathSegList, this._parseNumber());\n\n            case window.SVGPathSeg.PATHSEG_LINETO_HORIZONTAL_ABS:\n              return new window.SVGPathSegLinetoHorizontalAbs(owningPathSegList, this._parseNumber());\n\n            case window.SVGPathSeg.PATHSEG_LINETO_VERTICAL_REL:\n              return new window.SVGPathSegLinetoVerticalRel(owningPathSegList, this._parseNumber());\n\n            case window.SVGPathSeg.PATHSEG_LINETO_VERTICAL_ABS:\n              return new window.SVGPathSegLinetoVerticalAbs(owningPathSegList, this._parseNumber());\n\n            case window.SVGPathSeg.PATHSEG_CLOSEPATH:\n              this._skipOptionalSpaces();\n\n              return new window.SVGPathSegClosePath(owningPathSegList);\n\n            case window.SVGPathSeg.PATHSEG_CURVETO_CUBIC_REL:\n              points = {\n                x1: this._parseNumber(),\n                y1: this._parseNumber(),\n                x2: this._parseNumber(),\n                y2: this._parseNumber(),\n                x: this._parseNumber(),\n                y: this._parseNumber()\n              };\n              return new window.SVGPathSegCurvetoCubicRel(owningPathSegList, points.x, points.y, points.x1, points.y1, points.x2, points.y2);\n\n            case window.SVGPathSeg.PATHSEG_CURVETO_CUBIC_ABS:\n              points = {\n                x1: this._parseNumber(),\n                y1: this._parseNumber(),\n                x2: this._parseNumber(),\n                y2: this._parseNumber(),\n                x: this._parseNumber(),\n                y: this._parseNumber()\n              };\n              return new window.SVGPathSegCurvetoCubicAbs(owningPathSegList, points.x, points.y, points.x1, points.y1, points.x2, points.y2);\n\n            case window.SVGPathSeg.PATHSEG_CURVETO_CUBIC_SMOOTH_REL:\n              points = {\n                x2: this._parseNumber(),\n                y2: this._parseNumber(),\n                x: this._parseNumber(),\n                y: this._parseNumber()\n              };\n              return new window.SVGPathSegCurvetoCubicSmoothRel(owningPathSegList, points.x, points.y, points.x2, points.y2);\n\n            case window.SVGPathSeg.PATHSEG_CURVETO_CUBIC_SMOOTH_ABS:\n              points = {\n                x2: this._parseNumber(),\n                y2: this._parseNumber(),\n                x: this._parseNumber(),\n                y: this._parseNumber()\n              };\n              return new window.SVGPathSegCurvetoCubicSmoothAbs(owningPathSegList, points.x, points.y, points.x2, points.y2);\n\n            case window.SVGPathSeg.PATHSEG_CURVETO_QUADRATIC_REL:\n              points = {\n                x1: this._parseNumber(),\n                y1: this._parseNumber(),\n                x: this._parseNumber(),\n                y: this._parseNumber()\n              };\n              return new window.SVGPathSegCurvetoQuadraticRel(owningPathSegList, points.x, points.y, points.x1, points.y1);\n\n            case window.SVGPathSeg.PATHSEG_CURVETO_QUADRATIC_ABS:\n              points = {\n                x1: this._parseNumber(),\n                y1: this._parseNumber(),\n                x: this._parseNumber(),\n                y: this._parseNumber()\n              };\n              return new window.SVGPathSegCurvetoQuadraticAbs(owningPathSegList, points.x, points.y, points.x1, points.y1);\n\n            case window.SVGPathSeg.PATHSEG_CURVETO_QUADRATIC_SMOOTH_REL:\n              return new window.SVGPathSegCurvetoQuadraticSmoothRel(owningPathSegList, this._parseNumber(), this._parseNumber());\n\n            case window.SVGPathSeg.PATHSEG_CURVETO_QUADRATIC_SMOOTH_ABS:\n              return new window.SVGPathSegCurvetoQuadraticSmoothAbs(owningPathSegList, this._parseNumber(), this._parseNumber());\n\n            case window.SVGPathSeg.PATHSEG_ARC_REL:\n              points = {\n                x1: this._parseNumber(),\n                y1: this._parseNumber(),\n                arcAngle: this._parseNumber(),\n                arcLarge: this._parseArcFlag(),\n                arcSweep: this._parseArcFlag(),\n                x: this._parseNumber(),\n                y: this._parseNumber()\n              };\n              return new window.SVGPathSegArcRel(owningPathSegList, points.x, points.y, points.x1, points.y1, points.arcAngle, points.arcLarge, points.arcSweep);\n\n            case window.SVGPathSeg.PATHSEG_ARC_ABS:\n              points = {\n                x1: this._parseNumber(),\n                y1: this._parseNumber(),\n                arcAngle: this._parseNumber(),\n                arcLarge: this._parseArcFlag(),\n                arcSweep: this._parseArcFlag(),\n                x: this._parseNumber(),\n                y: this._parseNumber()\n              };\n              return new window.SVGPathSegArcAbs(owningPathSegList, points.x, points.y, points.x1, points.y1, points.arcAngle, points.arcLarge, points.arcSweep);\n\n            default:\n              throw \"Unknown path seg type.\";\n          }\n        };\n\n        const builder = new Builder();\n        const source = new Source(string);\n        if (!source.initialCommandIsMoveTo()) return [];\n\n        while (source.hasMoreData()) {\n          const pathSeg = source.parseSegment();\n          if (!pathSeg) return [];\n          builder.appendSegment(pathSeg);\n        }\n\n        return builder.pathSegList;\n      };\n    }\n  } catch (e) {\n    console.warn(\"An error occurred in tsParticles pathseg polyfill. If the Polygon Mask is not working, please open an issue here: https://github.com/matteobruni/tsparticles\", e);\n  }\n})();", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Plugins/PolygonMask/pathseg.js"], "names": ["window", "SVGPathSeg", "type", "typeAsLetter", "owningPathSegList", "pathSegType", "pathSegTypeAsLetter", "_owningPathSegList", "prototype", "classname", "PATHSEG_UNKNOWN", "PATHSEG_CLOSEPATH", "PATHSEG_MOVETO_ABS", "PATHSEG_MOVETO_REL", "PATHSEG_LINETO_ABS", "PATHSEG_LINETO_REL", "PATHSEG_CURVETO_CUBIC_ABS", "PATHSEG_CURVETO_CUBIC_REL", "PATHSEG_CURVETO_QUADRATIC_ABS", "PATHSEG_CURVETO_QUADRATIC_REL", "PATHSEG_ARC_ABS", "PATHSEG_ARC_REL", "PATHSEG_LINETO_HORIZONTAL_ABS", "PATHSEG_LINETO_HORIZONTAL_REL", "PATHSEG_LINETO_VERTICAL_ABS", "PATHSEG_LINETO_VERTICAL_REL", "PATHSEG_CURVETO_CUBIC_SMOOTH_ABS", "PATHSEG_CURVETO_CUBIC_SMOOTH_REL", "PATHSEG_CURVETO_QUADRATIC_SMOOTH_ABS", "PATHSEG_CURVETO_QUADRATIC_SMOOTH_REL", "_segmentChanged", "segmentChanged", "SVGPathSegClosePath", "call", "Object", "create", "toString", "_asPathString", "clone", "undefined", "SVGPathSegMovetoAbs", "x", "y", "_x", "_y", "defineProperty", "get", "set", "enumerable", "SVGPathSegMovetoRel", "SVGPathSegLinetoAbs", "SVGPathSegLinetoRel", "SVGPathSegCurvetoCubicAbs", "x1", "y1", "x2", "y2", "_x1", "_y1", "_x2", "_y2", "SVGPathSegCurvetoCubicRel", "SVGPathSegCurvetoQuadraticAbs", "SVGPathSegCurvetoQuadraticRel", "SVGPathSegArcAbs", "r1", "r2", "angle", "largeArcFlag", "sweepFlag", "_r1", "_r2", "_angle", "_largeArcFlag", "_sweepFlag", "SVGPathSegArcRel", "SVGPathSegLinetoHorizontalAbs", "SVGPathSegLinetoHorizontalRel", "SVGPathSegLinetoVerticalAbs", "SVGPathSegLinetoVerticalRel", "SVGPathSegCurvetoCubicSmoothAbs", "SVGPathSegCurvetoCubicSmoothRel", "SVGPathSegCurvetoQuadraticSmoothAbs", "SVGPathSegCurvetoQuadraticSmoothRel", "SVGPathElement", "createSVGPathSegClosePath", "createSVGPathSegMovetoAbs", "createSVGPathSegMovetoRel", "createSVGPathSegLinetoAbs", "createSVGPathSegLinetoRel", "createSVGPathSegCurvetoCubicAbs", "createSVGPathSegCurvetoCubicRel", "createSVGPathSegCurvetoQuadraticAbs", "createSVGPathSegCurvetoQuadraticRel", "createSVGPathSegArcAbs", "createSVGPathSegArcRel", "createSVGPathSegLinetoHorizontalAbs", "createSVGPathSegLinetoHorizontalRel", "createSVGPathSegLinetoVerticalAbs", "createSVGPathSegLinetoVerticalRel", "createSVGPathSegCurvetoCubicSmoothAbs", "createSVGPathSegCurvetoCubicSmoothRel", "createSVGPathSegCurvetoQuadraticSmoothAbs", "createSVGPathSegCurvetoQuadraticSmoothRel", "getPathSegAtLength", "distance", "isFinite", "measurementElement", "document", "createElementNS", "setAttribute", "getAttribute", "lastPathSegment", "pathSegList", "numberOfItems", "removeItem", "getTotalLength", "SVGPathSegList", "pathElement", "_pathElement", "_list", "_parsePath", "_mutationObserverConfig", "attributes", "attributeFilter", "_pathElementMutationObserver", "MutationObserver", "_updateListFromPathMutations", "bind", "observe", "_checkPathSynchronizedToList", "length", "_pathSegList", "takeRecords", "mutationRecords", "hasPathMutations", "for<PERSON>ach", "record", "attributeName", "_writeListToPath", "disconnect", "_pathSegArrayAsString", "pathSeg", "clear", "initialize", "newItem", "_checkValidIndex", "index", "isNaN", "getItem", "insertItemBefore", "splice", "replaceItem", "item", "appendItem", "push", "pathSegArray", "string", "first", "Builder", "appendSegment", "Source", "_string", "_currentIndex", "_endIndex", "_previousCommand", "_skipOptionalSpaces", "_isCurrentSpace", "character", "_skipOptionalSpacesOrDelimiter", "char<PERSON>t", "hasMoreData", "peekSegmentType", "<PERSON><PERSON><PERSON>", "_pathSegTypeFromChar", "_nextCommandHelper", "previousCommand", "initialCommandIsMoveTo", "command", "_parseNumber", "exponent", "integer", "frac", "decimal", "sign", "expsign", "startIndex", "startIntPartIndex", "scanIntPartIndex", "multiplier", "number", "Math", "pow", "_parseArcFlag", "flag", "flagChar", "parseSegment", "points", "arcAngle", "arc<PERSON>arge", "arcSweep", "builder", "source", "e", "console", "warn"], "mappings": "AAAA;;AACA,CAAC,YAAY;AACT;;AACA,MAAI;AACA,QAAI,OAAOA,MAAP,KAAkB,WAAtB,EACI;;AACJ,QAAI,EAAE,gBAAgBA,MAAlB,CAAJ,EAA+B;AAC3BA,MAAAA,MAAM,CAACC,UAAP,GAAoB,UAAUC,IAAV,EAAgBC,YAAhB,EAA8BC,iBAA9B,EAAiD;AACjE,aAAKC,WAAL,GAAmBH,IAAnB;AACA,aAAKI,mBAAL,GAA2BH,YAA3B;AACA,aAAKI,kBAAL,GAA0BH,iBAA1B;AACH,OAJD;;AAKAJ,MAAAA,MAAM,CAACC,UAAP,CAAkBO,SAAlB,CAA4BC,SAA5B,GAAwC,YAAxC;AACAT,MAAAA,MAAM,CAACC,UAAP,CAAkBS,eAAlB,GAAoC,CAApC;AACAV,MAAAA,MAAM,CAACC,UAAP,CAAkBU,iBAAlB,GAAsC,CAAtC;AACAX,MAAAA,MAAM,CAACC,UAAP,CAAkBW,kBAAlB,GAAuC,CAAvC;AACAZ,MAAAA,MAAM,CAACC,UAAP,CAAkBY,kBAAlB,GAAuC,CAAvC;AACAb,MAAAA,MAAM,CAACC,UAAP,CAAkBa,kBAAlB,GAAuC,CAAvC;AACAd,MAAAA,MAAM,CAACC,UAAP,CAAkBc,kBAAlB,GAAuC,CAAvC;AACAf,MAAAA,MAAM,CAACC,UAAP,CAAkBe,yBAAlB,GAA8C,CAA9C;AACAhB,MAAAA,MAAM,CAACC,UAAP,CAAkBgB,yBAAlB,GAA8C,CAA9C;AACAjB,MAAAA,MAAM,CAACC,UAAP,CAAkBiB,6BAAlB,GAAkD,CAAlD;AACAlB,MAAAA,MAAM,CAACC,UAAP,CAAkBkB,6BAAlB,GAAkD,CAAlD;AACAnB,MAAAA,MAAM,CAACC,UAAP,CAAkBmB,eAAlB,GAAoC,EAApC;AACApB,MAAAA,MAAM,CAACC,UAAP,CAAkBoB,eAAlB,GAAoC,EAApC;AACArB,MAAAA,MAAM,CAACC,UAAP,CAAkBqB,6BAAlB,GAAkD,EAAlD;AACAtB,MAAAA,MAAM,CAACC,UAAP,CAAkBsB,6BAAlB,GAAkD,EAAlD;AACAvB,MAAAA,MAAM,CAACC,UAAP,CAAkBuB,2BAAlB,GAAgD,EAAhD;AACAxB,MAAAA,MAAM,CAACC,UAAP,CAAkBwB,2BAAlB,GAAgD,EAAhD;AACAzB,MAAAA,MAAM,CAACC,UAAP,CAAkByB,gCAAlB,GAAqD,EAArD;AACA1B,MAAAA,MAAM,CAACC,UAAP,CAAkB0B,gCAAlB,GAAqD,EAArD;AACA3B,MAAAA,MAAM,CAACC,UAAP,CAAkB2B,oCAAlB,GAAyD,EAAzD;AACA5B,MAAAA,MAAM,CAACC,UAAP,CAAkB4B,oCAAlB,GAAyD,EAAzD;;AACA7B,MAAAA,MAAM,CAACC,UAAP,CAAkBO,SAAlB,CAA4BsB,eAA5B,GAA8C,YAAY;AACtD,YAAI,KAAKvB,kBAAT,EACI,KAAKA,kBAAL,CAAwBwB,cAAxB,CAAuC,IAAvC;AACP,OAHD;;AAIA/B,MAAAA,MAAM,CAACgC,mBAAP,GAA6B,UAAU5B,iBAAV,EAA6B;AACtDJ,QAAAA,MAAM,CAACC,UAAP,CAAkBgC,IAAlB,CAAuB,IAAvB,EAA6BjC,MAAM,CAACC,UAAP,CAAkBU,iBAA/C,EAAkE,GAAlE,EAAuEP,iBAAvE;AACH,OAFD;;AAGAJ,MAAAA,MAAM,CAACgC,mBAAP,CAA2BxB,SAA3B,GAAuC0B,MAAM,CAACC,MAAP,CAAcnC,MAAM,CAACC,UAAP,CAAkBO,SAAhC,CAAvC;;AACAR,MAAAA,MAAM,CAACgC,mBAAP,CAA2BxB,SAA3B,CAAqC4B,QAArC,GAAgD,YAAY;AACxD,eAAO,8BAAP;AACH,OAFD;;AAGApC,MAAAA,MAAM,CAACgC,mBAAP,CAA2BxB,SAA3B,CAAqC6B,aAArC,GAAqD,YAAY;AAC7D,eAAO,KAAK/B,mBAAZ;AACH,OAFD;;AAGAN,MAAAA,MAAM,CAACgC,mBAAP,CAA2BxB,SAA3B,CAAqC8B,KAArC,GAA6C,YAAY;AACrD,eAAO,IAAItC,MAAM,CAACgC,mBAAX,CAA+BO,SAA/B,CAAP;AACH,OAFD;;AAGAvC,MAAAA,MAAM,CAACwC,mBAAP,GAA6B,UAAUpC,iBAAV,EAA6BqC,CAA7B,EAAgCC,CAAhC,EAAmC;AAC5D1C,QAAAA,MAAM,CAACC,UAAP,CAAkBgC,IAAlB,CAAuB,IAAvB,EAA6BjC,MAAM,CAACC,UAAP,CAAkBW,kBAA/C,EAAmE,GAAnE,EAAwER,iBAAxE;AACA,aAAKuC,EAAL,GAAUF,CAAV;AACA,aAAKG,EAAL,GAAUF,CAAV;AACH,OAJD;;AAKA1C,MAAAA,MAAM,CAACwC,mBAAP,CAA2BhC,SAA3B,GAAuC0B,MAAM,CAACC,MAAP,CAAcnC,MAAM,CAACC,UAAP,CAAkBO,SAAhC,CAAvC;;AACAR,MAAAA,MAAM,CAACwC,mBAAP,CAA2BhC,SAA3B,CAAqC4B,QAArC,GAAgD,YAAY;AACxD,eAAO,8BAAP;AACH,OAFD;;AAGApC,MAAAA,MAAM,CAACwC,mBAAP,CAA2BhC,SAA3B,CAAqC6B,aAArC,GAAqD,YAAY;AAC7D,eAAO,KAAK/B,mBAAL,GAA2B,GAA3B,GAAiC,KAAKqC,EAAtC,GAA2C,GAA3C,GAAiD,KAAKC,EAA7D;AACH,OAFD;;AAGA5C,MAAAA,MAAM,CAACwC,mBAAP,CAA2BhC,SAA3B,CAAqC8B,KAArC,GAA6C,YAAY;AACrD,eAAO,IAAItC,MAAM,CAACwC,mBAAX,CAA+BD,SAA/B,EAA0C,KAAKI,EAA/C,EAAmD,KAAKC,EAAxD,CAAP;AACH,OAFD;;AAGAV,MAAAA,MAAM,CAACW,cAAP,CAAsB7C,MAAM,CAACwC,mBAAP,CAA2BhC,SAAjD,EAA4D,GAA5D,EAAiE;AAC7DsC,QAAAA,GAAG,EAAE,YAAY;AACb,iBAAO,KAAKH,EAAZ;AACH,SAH4D;AAI7DI,QAAAA,GAAG,EAAE,UAAUN,CAAV,EAAa;AACd,eAAKE,EAAL,GAAUF,CAAV;;AACA,eAAKX,eAAL;AACH,SAP4D;AAQ7DkB,QAAAA,UAAU,EAAE;AARiD,OAAjE;AAUAd,MAAAA,MAAM,CAACW,cAAP,CAAsB7C,MAAM,CAACwC,mBAAP,CAA2BhC,SAAjD,EAA4D,GAA5D,EAAiE;AAC7DsC,QAAAA,GAAG,EAAE,YAAY;AACb,iBAAO,KAAKF,EAAZ;AACH,SAH4D;AAI7DG,QAAAA,GAAG,EAAE,UAAUL,CAAV,EAAa;AACd,eAAKE,EAAL,GAAUF,CAAV;;AACA,eAAKZ,eAAL;AACH,SAP4D;AAQ7DkB,QAAAA,UAAU,EAAE;AARiD,OAAjE;;AAUAhD,MAAAA,MAAM,CAACiD,mBAAP,GAA6B,UAAU7C,iBAAV,EAA6BqC,CAA7B,EAAgCC,CAAhC,EAAmC;AAC5D1C,QAAAA,MAAM,CAACC,UAAP,CAAkBgC,IAAlB,CAAuB,IAAvB,EAA6BjC,MAAM,CAACC,UAAP,CAAkBY,kBAA/C,EAAmE,GAAnE,EAAwET,iBAAxE;AACA,aAAKuC,EAAL,GAAUF,CAAV;AACA,aAAKG,EAAL,GAAUF,CAAV;AACH,OAJD;;AAKA1C,MAAAA,MAAM,CAACiD,mBAAP,CAA2BzC,SAA3B,GAAuC0B,MAAM,CAACC,MAAP,CAAcnC,MAAM,CAACC,UAAP,CAAkBO,SAAhC,CAAvC;;AACAR,MAAAA,MAAM,CAACiD,mBAAP,CAA2BzC,SAA3B,CAAqC4B,QAArC,GAAgD,YAAY;AACxD,eAAO,8BAAP;AACH,OAFD;;AAGApC,MAAAA,MAAM,CAACiD,mBAAP,CAA2BzC,SAA3B,CAAqC6B,aAArC,GAAqD,YAAY;AAC7D,eAAO,KAAK/B,mBAAL,GAA2B,GAA3B,GAAiC,KAAKqC,EAAtC,GAA2C,GAA3C,GAAiD,KAAKC,EAA7D;AACH,OAFD;;AAGA5C,MAAAA,MAAM,CAACiD,mBAAP,CAA2BzC,SAA3B,CAAqC8B,KAArC,GAA6C,YAAY;AACrD,eAAO,IAAItC,MAAM,CAACiD,mBAAX,CAA+BV,SAA/B,EAA0C,KAAKI,EAA/C,EAAmD,KAAKC,EAAxD,CAAP;AACH,OAFD;;AAGAV,MAAAA,MAAM,CAACW,cAAP,CAAsB7C,MAAM,CAACiD,mBAAP,CAA2BzC,SAAjD,EAA4D,GAA5D,EAAiE;AAC7DsC,QAAAA,GAAG,EAAE,YAAY;AACb,iBAAO,KAAKH,EAAZ;AACH,SAH4D;AAI7DI,QAAAA,GAAG,EAAE,UAAUN,CAAV,EAAa;AACd,eAAKE,EAAL,GAAUF,CAAV;;AACA,eAAKX,eAAL;AACH,SAP4D;AAQ7DkB,QAAAA,UAAU,EAAE;AARiD,OAAjE;AAUAd,MAAAA,MAAM,CAACW,cAAP,CAAsB7C,MAAM,CAACiD,mBAAP,CAA2BzC,SAAjD,EAA4D,GAA5D,EAAiE;AAC7DsC,QAAAA,GAAG,EAAE,YAAY;AACb,iBAAO,KAAKF,EAAZ;AACH,SAH4D;AAI7DG,QAAAA,GAAG,EAAE,UAAUL,CAAV,EAAa;AACd,eAAKE,EAAL,GAAUF,CAAV;;AACA,eAAKZ,eAAL;AACH,SAP4D;AAQ7DkB,QAAAA,UAAU,EAAE;AARiD,OAAjE;;AAUAhD,MAAAA,MAAM,CAACkD,mBAAP,GAA6B,UAAU9C,iBAAV,EAA6BqC,CAA7B,EAAgCC,CAAhC,EAAmC;AAC5D1C,QAAAA,MAAM,CAACC,UAAP,CAAkBgC,IAAlB,CAAuB,IAAvB,EAA6BjC,MAAM,CAACC,UAAP,CAAkBa,kBAA/C,EAAmE,GAAnE,EAAwEV,iBAAxE;AACA,aAAKuC,EAAL,GAAUF,CAAV;AACA,aAAKG,EAAL,GAAUF,CAAV;AACH,OAJD;;AAKA1C,MAAAA,MAAM,CAACkD,mBAAP,CAA2B1C,SAA3B,GAAuC0B,MAAM,CAACC,MAAP,CAAcnC,MAAM,CAACC,UAAP,CAAkBO,SAAhC,CAAvC;;AACAR,MAAAA,MAAM,CAACkD,mBAAP,CAA2B1C,SAA3B,CAAqC4B,QAArC,GAAgD,YAAY;AACxD,eAAO,8BAAP;AACH,OAFD;;AAGApC,MAAAA,MAAM,CAACkD,mBAAP,CAA2B1C,SAA3B,CAAqC6B,aAArC,GAAqD,YAAY;AAC7D,eAAO,KAAK/B,mBAAL,GAA2B,GAA3B,GAAiC,KAAKqC,EAAtC,GAA2C,GAA3C,GAAiD,KAAKC,EAA7D;AACH,OAFD;;AAGA5C,MAAAA,MAAM,CAACkD,mBAAP,CAA2B1C,SAA3B,CAAqC8B,KAArC,GAA6C,YAAY;AACrD,eAAO,IAAItC,MAAM,CAACkD,mBAAX,CAA+BX,SAA/B,EAA0C,KAAKI,EAA/C,EAAmD,KAAKC,EAAxD,CAAP;AACH,OAFD;;AAGAV,MAAAA,MAAM,CAACW,cAAP,CAAsB7C,MAAM,CAACkD,mBAAP,CAA2B1C,SAAjD,EAA4D,GAA5D,EAAiE;AAC7DsC,QAAAA,GAAG,EAAE,YAAY;AACb,iBAAO,KAAKH,EAAZ;AACH,SAH4D;AAI7DI,QAAAA,GAAG,EAAE,UAAUN,CAAV,EAAa;AACd,eAAKE,EAAL,GAAUF,CAAV;;AACA,eAAKX,eAAL;AACH,SAP4D;AAQ7DkB,QAAAA,UAAU,EAAE;AARiD,OAAjE;AAUAd,MAAAA,MAAM,CAACW,cAAP,CAAsB7C,MAAM,CAACkD,mBAAP,CAA2B1C,SAAjD,EAA4D,GAA5D,EAAiE;AAC7DsC,QAAAA,GAAG,EAAE,YAAY;AACb,iBAAO,KAAKF,EAAZ;AACH,SAH4D;AAI7DG,QAAAA,GAAG,EAAE,UAAUL,CAAV,EAAa;AACd,eAAKE,EAAL,GAAUF,CAAV;;AACA,eAAKZ,eAAL;AACH,SAP4D;AAQ7DkB,QAAAA,UAAU,EAAE;AARiD,OAAjE;;AAUAhD,MAAAA,MAAM,CAACmD,mBAAP,GAA6B,UAAU/C,iBAAV,EAA6BqC,CAA7B,EAAgCC,CAAhC,EAAmC;AAC5D1C,QAAAA,MAAM,CAACC,UAAP,CAAkBgC,IAAlB,CAAuB,IAAvB,EAA6BjC,MAAM,CAACC,UAAP,CAAkBc,kBAA/C,EAAmE,GAAnE,EAAwEX,iBAAxE;AACA,aAAKuC,EAAL,GAAUF,CAAV;AACA,aAAKG,EAAL,GAAUF,CAAV;AACH,OAJD;;AAKA1C,MAAAA,MAAM,CAACmD,mBAAP,CAA2B3C,SAA3B,GAAuC0B,MAAM,CAACC,MAAP,CAAcnC,MAAM,CAACC,UAAP,CAAkBO,SAAhC,CAAvC;;AACAR,MAAAA,MAAM,CAACmD,mBAAP,CAA2B3C,SAA3B,CAAqC4B,QAArC,GAAgD,YAAY;AACxD,eAAO,8BAAP;AACH,OAFD;;AAGApC,MAAAA,MAAM,CAACmD,mBAAP,CAA2B3C,SAA3B,CAAqC6B,aAArC,GAAqD,YAAY;AAC7D,eAAO,KAAK/B,mBAAL,GAA2B,GAA3B,GAAiC,KAAKqC,EAAtC,GAA2C,GAA3C,GAAiD,KAAKC,EAA7D;AACH,OAFD;;AAGA5C,MAAAA,MAAM,CAACmD,mBAAP,CAA2B3C,SAA3B,CAAqC8B,KAArC,GAA6C,YAAY;AACrD,eAAO,IAAItC,MAAM,CAACmD,mBAAX,CAA+BZ,SAA/B,EAA0C,KAAKI,EAA/C,EAAmD,KAAKC,EAAxD,CAAP;AACH,OAFD;;AAGAV,MAAAA,MAAM,CAACW,cAAP,CAAsB7C,MAAM,CAACmD,mBAAP,CAA2B3C,SAAjD,EAA4D,GAA5D,EAAiE;AAC7DsC,QAAAA,GAAG,EAAE,YAAY;AACb,iBAAO,KAAKH,EAAZ;AACH,SAH4D;AAI7DI,QAAAA,GAAG,EAAE,UAAUN,CAAV,EAAa;AACd,eAAKE,EAAL,GAAUF,CAAV;;AACA,eAAKX,eAAL;AACH,SAP4D;AAQ7DkB,QAAAA,UAAU,EAAE;AARiD,OAAjE;AAUAd,MAAAA,MAAM,CAACW,cAAP,CAAsB7C,MAAM,CAACmD,mBAAP,CAA2B3C,SAAjD,EAA4D,GAA5D,EAAiE;AAC7DsC,QAAAA,GAAG,EAAE,YAAY;AACb,iBAAO,KAAKF,EAAZ;AACH,SAH4D;AAI7DG,QAAAA,GAAG,EAAE,UAAUL,CAAV,EAAa;AACd,eAAKE,EAAL,GAAUF,CAAV;;AACA,eAAKZ,eAAL;AACH,SAP4D;AAQ7DkB,QAAAA,UAAU,EAAE;AARiD,OAAjE;;AAUAhD,MAAAA,MAAM,CAACoD,yBAAP,GAAmC,UAAUhD,iBAAV,EAA6BqC,CAA7B,EAAgCC,CAAhC,EAAmCW,EAAnC,EAAuCC,EAAvC,EAA2CC,EAA3C,EAA+CC,EAA/C,EAAmD;AAClFxD,QAAAA,MAAM,CAACC,UAAP,CAAkBgC,IAAlB,CAAuB,IAAvB,EAA6BjC,MAAM,CAACC,UAAP,CAAkBe,yBAA/C,EAA0E,GAA1E,EAA+EZ,iBAA/E;AACA,aAAKuC,EAAL,GAAUF,CAAV;AACA,aAAKG,EAAL,GAAUF,CAAV;AACA,aAAKe,GAAL,GAAWJ,EAAX;AACA,aAAKK,GAAL,GAAWJ,EAAX;AACA,aAAKK,GAAL,GAAWJ,EAAX;AACA,aAAKK,GAAL,GAAWJ,EAAX;AACH,OARD;;AASAxD,MAAAA,MAAM,CAACoD,yBAAP,CAAiC5C,SAAjC,GAA6C0B,MAAM,CAACC,MAAP,CAAcnC,MAAM,CAACC,UAAP,CAAkBO,SAAhC,CAA7C;;AACAR,MAAAA,MAAM,CAACoD,yBAAP,CAAiC5C,SAAjC,CAA2C4B,QAA3C,GAAsD,YAAY;AAC9D,eAAO,oCAAP;AACH,OAFD;;AAGApC,MAAAA,MAAM,CAACoD,yBAAP,CAAiC5C,SAAjC,CAA2C6B,aAA3C,GAA2D,YAAY;AACnE,eAAQ,KAAK/B,mBAAL,GACJ,GADI,GAEJ,KAAKmD,GAFD,GAGJ,GAHI,GAIJ,KAAKC,GAJD,GAKJ,GALI,GAMJ,KAAKC,GAND,GAOJ,GAPI,GAQJ,KAAKC,GARD,GASJ,GATI,GAUJ,KAAKjB,EAVD,GAWJ,GAXI,GAYJ,KAAKC,EAZT;AAaH,OAdD;;AAeA5C,MAAAA,MAAM,CAACoD,yBAAP,CAAiC5C,SAAjC,CAA2C8B,KAA3C,GAAmD,YAAY;AAC3D,eAAO,IAAItC,MAAM,CAACoD,yBAAX,CAAqCb,SAArC,EAAgD,KAAKI,EAArD,EAAyD,KAAKC,EAA9D,EAAkE,KAAKa,GAAvE,EAA4E,KAAKC,GAAjF,EAAsF,KAAKC,GAA3F,EAAgG,KAAKC,GAArG,CAAP;AACH,OAFD;;AAGA1B,MAAAA,MAAM,CAACW,cAAP,CAAsB7C,MAAM,CAACoD,yBAAP,CAAiC5C,SAAvD,EAAkE,GAAlE,EAAuE;AACnEsC,QAAAA,GAAG,EAAE,YAAY;AACb,iBAAO,KAAKH,EAAZ;AACH,SAHkE;AAInEI,QAAAA,GAAG,EAAE,UAAUN,CAAV,EAAa;AACd,eAAKE,EAAL,GAAUF,CAAV;;AACA,eAAKX,eAAL;AACH,SAPkE;AAQnEkB,QAAAA,UAAU,EAAE;AARuD,OAAvE;AAUAd,MAAAA,MAAM,CAACW,cAAP,CAAsB7C,MAAM,CAACoD,yBAAP,CAAiC5C,SAAvD,EAAkE,GAAlE,EAAuE;AACnEsC,QAAAA,GAAG,EAAE,YAAY;AACb,iBAAO,KAAKF,EAAZ;AACH,SAHkE;AAInEG,QAAAA,GAAG,EAAE,UAAUL,CAAV,EAAa;AACd,eAAKE,EAAL,GAAUF,CAAV;;AACA,eAAKZ,eAAL;AACH,SAPkE;AAQnEkB,QAAAA,UAAU,EAAE;AARuD,OAAvE;AAUAd,MAAAA,MAAM,CAACW,cAAP,CAAsB7C,MAAM,CAACoD,yBAAP,CAAiC5C,SAAvD,EAAkE,IAAlE,EAAwE;AACpEsC,QAAAA,GAAG,EAAE,YAAY;AACb,iBAAO,KAAKW,GAAZ;AACH,SAHmE;AAIpEV,QAAAA,GAAG,EAAE,UAAUM,EAAV,EAAc;AACf,eAAKI,GAAL,GAAWJ,EAAX;;AACA,eAAKvB,eAAL;AACH,SAPmE;AAQpEkB,QAAAA,UAAU,EAAE;AARwD,OAAxE;AAUAd,MAAAA,MAAM,CAACW,cAAP,CAAsB7C,MAAM,CAACoD,yBAAP,CAAiC5C,SAAvD,EAAkE,IAAlE,EAAwE;AACpEsC,QAAAA,GAAG,EAAE,YAAY;AACb,iBAAO,KAAKY,GAAZ;AACH,SAHmE;AAIpEX,QAAAA,GAAG,EAAE,UAAUO,EAAV,EAAc;AACf,eAAKI,GAAL,GAAWJ,EAAX;;AACA,eAAKxB,eAAL;AACH,SAPmE;AAQpEkB,QAAAA,UAAU,EAAE;AARwD,OAAxE;AAUAd,MAAAA,MAAM,CAACW,cAAP,CAAsB7C,MAAM,CAACoD,yBAAP,CAAiC5C,SAAvD,EAAkE,IAAlE,EAAwE;AACpEsC,QAAAA,GAAG,EAAE,YAAY;AACb,iBAAO,KAAKa,GAAZ;AACH,SAHmE;AAIpEZ,QAAAA,GAAG,EAAE,UAAUQ,EAAV,EAAc;AACf,eAAKI,GAAL,GAAWJ,EAAX;;AACA,eAAKzB,eAAL;AACH,SAPmE;AAQpEkB,QAAAA,UAAU,EAAE;AARwD,OAAxE;AAUAd,MAAAA,MAAM,CAACW,cAAP,CAAsB7C,MAAM,CAACoD,yBAAP,CAAiC5C,SAAvD,EAAkE,IAAlE,EAAwE;AACpEsC,QAAAA,GAAG,EAAE,YAAY;AACb,iBAAO,KAAKc,GAAZ;AACH,SAHmE;AAIpEb,QAAAA,GAAG,EAAE,UAAUS,EAAV,EAAc;AACf,eAAKI,GAAL,GAAWJ,EAAX;;AACA,eAAK1B,eAAL;AACH,SAPmE;AAQpEkB,QAAAA,UAAU,EAAE;AARwD,OAAxE;;AAUAhD,MAAAA,MAAM,CAAC6D,yBAAP,GAAmC,UAAUzD,iBAAV,EAA6BqC,CAA7B,EAAgCC,CAAhC,EAAmCW,EAAnC,EAAuCC,EAAvC,EAA2CC,EAA3C,EAA+CC,EAA/C,EAAmD;AAClFxD,QAAAA,MAAM,CAACC,UAAP,CAAkBgC,IAAlB,CAAuB,IAAvB,EAA6BjC,MAAM,CAACC,UAAP,CAAkBgB,yBAA/C,EAA0E,GAA1E,EAA+Eb,iBAA/E;AACA,aAAKuC,EAAL,GAAUF,CAAV;AACA,aAAKG,EAAL,GAAUF,CAAV;AACA,aAAKe,GAAL,GAAWJ,EAAX;AACA,aAAKK,GAAL,GAAWJ,EAAX;AACA,aAAKK,GAAL,GAAWJ,EAAX;AACA,aAAKK,GAAL,GAAWJ,EAAX;AACH,OARD;;AASAxD,MAAAA,MAAM,CAAC6D,yBAAP,CAAiCrD,SAAjC,GAA6C0B,MAAM,CAACC,MAAP,CAAcnC,MAAM,CAACC,UAAP,CAAkBO,SAAhC,CAA7C;;AACAR,MAAAA,MAAM,CAAC6D,yBAAP,CAAiCrD,SAAjC,CAA2C4B,QAA3C,GAAsD,YAAY;AAC9D,eAAO,oCAAP;AACH,OAFD;;AAGApC,MAAAA,MAAM,CAAC6D,yBAAP,CAAiCrD,SAAjC,CAA2C6B,aAA3C,GAA2D,YAAY;AACnE,eAAQ,KAAK/B,mBAAL,GACJ,GADI,GAEJ,KAAKmD,GAFD,GAGJ,GAHI,GAIJ,KAAKC,GAJD,GAKJ,GALI,GAMJ,KAAKC,GAND,GAOJ,GAPI,GAQJ,KAAKC,GARD,GASJ,GATI,GAUJ,KAAKjB,EAVD,GAWJ,GAXI,GAYJ,KAAKC,EAZT;AAaH,OAdD;;AAeA5C,MAAAA,MAAM,CAAC6D,yBAAP,CAAiCrD,SAAjC,CAA2C8B,KAA3C,GAAmD,YAAY;AAC3D,eAAO,IAAItC,MAAM,CAAC6D,yBAAX,CAAqCtB,SAArC,EAAgD,KAAKI,EAArD,EAAyD,KAAKC,EAA9D,EAAkE,KAAKa,GAAvE,EAA4E,KAAKC,GAAjF,EAAsF,KAAKC,GAA3F,EAAgG,KAAKC,GAArG,CAAP;AACH,OAFD;;AAGA1B,MAAAA,MAAM,CAACW,cAAP,CAAsB7C,MAAM,CAAC6D,yBAAP,CAAiCrD,SAAvD,EAAkE,GAAlE,EAAuE;AACnEsC,QAAAA,GAAG,EAAE,YAAY;AACb,iBAAO,KAAKH,EAAZ;AACH,SAHkE;AAInEI,QAAAA,GAAG,EAAE,UAAUN,CAAV,EAAa;AACd,eAAKE,EAAL,GAAUF,CAAV;;AACA,eAAKX,eAAL;AACH,SAPkE;AAQnEkB,QAAAA,UAAU,EAAE;AARuD,OAAvE;AAUAd,MAAAA,MAAM,CAACW,cAAP,CAAsB7C,MAAM,CAAC6D,yBAAP,CAAiCrD,SAAvD,EAAkE,GAAlE,EAAuE;AACnEsC,QAAAA,GAAG,EAAE,YAAY;AACb,iBAAO,KAAKF,EAAZ;AACH,SAHkE;AAInEG,QAAAA,GAAG,EAAE,UAAUL,CAAV,EAAa;AACd,eAAKE,EAAL,GAAUF,CAAV;;AACA,eAAKZ,eAAL;AACH,SAPkE;AAQnEkB,QAAAA,UAAU,EAAE;AARuD,OAAvE;AAUAd,MAAAA,MAAM,CAACW,cAAP,CAAsB7C,MAAM,CAAC6D,yBAAP,CAAiCrD,SAAvD,EAAkE,IAAlE,EAAwE;AACpEsC,QAAAA,GAAG,EAAE,YAAY;AACb,iBAAO,KAAKW,GAAZ;AACH,SAHmE;AAIpEV,QAAAA,GAAG,EAAE,UAAUM,EAAV,EAAc;AACf,eAAKI,GAAL,GAAWJ,EAAX;;AACA,eAAKvB,eAAL;AACH,SAPmE;AAQpEkB,QAAAA,UAAU,EAAE;AARwD,OAAxE;AAUAd,MAAAA,MAAM,CAACW,cAAP,CAAsB7C,MAAM,CAAC6D,yBAAP,CAAiCrD,SAAvD,EAAkE,IAAlE,EAAwE;AACpEsC,QAAAA,GAAG,EAAE,YAAY;AACb,iBAAO,KAAKY,GAAZ;AACH,SAHmE;AAIpEX,QAAAA,GAAG,EAAE,UAAUO,EAAV,EAAc;AACf,eAAKI,GAAL,GAAWJ,EAAX;;AACA,eAAKxB,eAAL;AACH,SAPmE;AAQpEkB,QAAAA,UAAU,EAAE;AARwD,OAAxE;AAUAd,MAAAA,MAAM,CAACW,cAAP,CAAsB7C,MAAM,CAAC6D,yBAAP,CAAiCrD,SAAvD,EAAkE,IAAlE,EAAwE;AACpEsC,QAAAA,GAAG,EAAE,YAAY;AACb,iBAAO,KAAKa,GAAZ;AACH,SAHmE;AAIpEZ,QAAAA,GAAG,EAAE,UAAUQ,EAAV,EAAc;AACf,eAAKI,GAAL,GAAWJ,EAAX;;AACA,eAAKzB,eAAL;AACH,SAPmE;AAQpEkB,QAAAA,UAAU,EAAE;AARwD,OAAxE;AAUAd,MAAAA,MAAM,CAACW,cAAP,CAAsB7C,MAAM,CAAC6D,yBAAP,CAAiCrD,SAAvD,EAAkE,IAAlE,EAAwE;AACpEsC,QAAAA,GAAG,EAAE,YAAY;AACb,iBAAO,KAAKc,GAAZ;AACH,SAHmE;AAIpEb,QAAAA,GAAG,EAAE,UAAUS,EAAV,EAAc;AACf,eAAKI,GAAL,GAAWJ,EAAX;;AACA,eAAK1B,eAAL;AACH,SAPmE;AAQpEkB,QAAAA,UAAU,EAAE;AARwD,OAAxE;;AAUAhD,MAAAA,MAAM,CAAC8D,6BAAP,GAAuC,UAAU1D,iBAAV,EAA6BqC,CAA7B,EAAgCC,CAAhC,EAAmCW,EAAnC,EAAuCC,EAAvC,EAA2C;AAC9EtD,QAAAA,MAAM,CAACC,UAAP,CAAkBgC,IAAlB,CAAuB,IAAvB,EAA6BjC,MAAM,CAACC,UAAP,CAAkBiB,6BAA/C,EAA8E,GAA9E,EAAmFd,iBAAnF;AACA,aAAKuC,EAAL,GAAUF,CAAV;AACA,aAAKG,EAAL,GAAUF,CAAV;AACA,aAAKe,GAAL,GAAWJ,EAAX;AACA,aAAKK,GAAL,GAAWJ,EAAX;AACH,OAND;;AAOAtD,MAAAA,MAAM,CAAC8D,6BAAP,CAAqCtD,SAArC,GAAiD0B,MAAM,CAACC,MAAP,CAAcnC,MAAM,CAACC,UAAP,CAAkBO,SAAhC,CAAjD;;AACAR,MAAAA,MAAM,CAAC8D,6BAAP,CAAqCtD,SAArC,CAA+C4B,QAA/C,GAA0D,YAAY;AAClE,eAAO,wCAAP;AACH,OAFD;;AAGApC,MAAAA,MAAM,CAAC8D,6BAAP,CAAqCtD,SAArC,CAA+C6B,aAA/C,GAA+D,YAAY;AACvE,eAAO,KAAK/B,mBAAL,GAA2B,GAA3B,GAAiC,KAAKmD,GAAtC,GAA4C,GAA5C,GAAkD,KAAKC,GAAvD,GAA6D,GAA7D,GAAmE,KAAKf,EAAxE,GAA6E,GAA7E,GAAmF,KAAKC,EAA/F;AACH,OAFD;;AAGA5C,MAAAA,MAAM,CAAC8D,6BAAP,CAAqCtD,SAArC,CAA+C8B,KAA/C,GAAuD,YAAY;AAC/D,eAAO,IAAItC,MAAM,CAAC8D,6BAAX,CAAyCvB,SAAzC,EAAoD,KAAKI,EAAzD,EAA6D,KAAKC,EAAlE,EAAsE,KAAKa,GAA3E,EAAgF,KAAKC,GAArF,CAAP;AACH,OAFD;;AAGAxB,MAAAA,MAAM,CAACW,cAAP,CAAsB7C,MAAM,CAAC8D,6BAAP,CAAqCtD,SAA3D,EAAsE,GAAtE,EAA2E;AACvEsC,QAAAA,GAAG,EAAE,YAAY;AACb,iBAAO,KAAKH,EAAZ;AACH,SAHsE;AAIvEI,QAAAA,GAAG,EAAE,UAAUN,CAAV,EAAa;AACd,eAAKE,EAAL,GAAUF,CAAV;;AACA,eAAKX,eAAL;AACH,SAPsE;AAQvEkB,QAAAA,UAAU,EAAE;AAR2D,OAA3E;AAUAd,MAAAA,MAAM,CAACW,cAAP,CAAsB7C,MAAM,CAAC8D,6BAAP,CAAqCtD,SAA3D,EAAsE,GAAtE,EAA2E;AACvEsC,QAAAA,GAAG,EAAE,YAAY;AACb,iBAAO,KAAKF,EAAZ;AACH,SAHsE;AAIvEG,QAAAA,GAAG,EAAE,UAAUL,CAAV,EAAa;AACd,eAAKE,EAAL,GAAUF,CAAV;;AACA,eAAKZ,eAAL;AACH,SAPsE;AAQvEkB,QAAAA,UAAU,EAAE;AAR2D,OAA3E;AAUAd,MAAAA,MAAM,CAACW,cAAP,CAAsB7C,MAAM,CAAC8D,6BAAP,CAAqCtD,SAA3D,EAAsE,IAAtE,EAA4E;AACxEsC,QAAAA,GAAG,EAAE,YAAY;AACb,iBAAO,KAAKW,GAAZ;AACH,SAHuE;AAIxEV,QAAAA,GAAG,EAAE,UAAUM,EAAV,EAAc;AACf,eAAKI,GAAL,GAAWJ,EAAX;;AACA,eAAKvB,eAAL;AACH,SAPuE;AAQxEkB,QAAAA,UAAU,EAAE;AAR4D,OAA5E;AAUAd,MAAAA,MAAM,CAACW,cAAP,CAAsB7C,MAAM,CAAC8D,6BAAP,CAAqCtD,SAA3D,EAAsE,IAAtE,EAA4E;AACxEsC,QAAAA,GAAG,EAAE,YAAY;AACb,iBAAO,KAAKY,GAAZ;AACH,SAHuE;AAIxEX,QAAAA,GAAG,EAAE,UAAUO,EAAV,EAAc;AACf,eAAKI,GAAL,GAAWJ,EAAX;;AACA,eAAKxB,eAAL;AACH,SAPuE;AAQxEkB,QAAAA,UAAU,EAAE;AAR4D,OAA5E;;AAUAhD,MAAAA,MAAM,CAAC+D,6BAAP,GAAuC,UAAU3D,iBAAV,EAA6BqC,CAA7B,EAAgCC,CAAhC,EAAmCW,EAAnC,EAAuCC,EAAvC,EAA2C;AAC9EtD,QAAAA,MAAM,CAACC,UAAP,CAAkBgC,IAAlB,CAAuB,IAAvB,EAA6BjC,MAAM,CAACC,UAAP,CAAkBkB,6BAA/C,EAA8E,GAA9E,EAAmFf,iBAAnF;AACA,aAAKuC,EAAL,GAAUF,CAAV;AACA,aAAKG,EAAL,GAAUF,CAAV;AACA,aAAKe,GAAL,GAAWJ,EAAX;AACA,aAAKK,GAAL,GAAWJ,EAAX;AACH,OAND;;AAOAtD,MAAAA,MAAM,CAAC+D,6BAAP,CAAqCvD,SAArC,GAAiD0B,MAAM,CAACC,MAAP,CAAcnC,MAAM,CAACC,UAAP,CAAkBO,SAAhC,CAAjD;;AACAR,MAAAA,MAAM,CAAC+D,6BAAP,CAAqCvD,SAArC,CAA+C4B,QAA/C,GAA0D,YAAY;AAClE,eAAO,wCAAP;AACH,OAFD;;AAGApC,MAAAA,MAAM,CAAC+D,6BAAP,CAAqCvD,SAArC,CAA+C6B,aAA/C,GAA+D,YAAY;AACvE,eAAO,KAAK/B,mBAAL,GAA2B,GAA3B,GAAiC,KAAKmD,GAAtC,GAA4C,GAA5C,GAAkD,KAAKC,GAAvD,GAA6D,GAA7D,GAAmE,KAAKf,EAAxE,GAA6E,GAA7E,GAAmF,KAAKC,EAA/F;AACH,OAFD;;AAGA5C,MAAAA,MAAM,CAAC+D,6BAAP,CAAqCvD,SAArC,CAA+C8B,KAA/C,GAAuD,YAAY;AAC/D,eAAO,IAAItC,MAAM,CAAC+D,6BAAX,CAAyCxB,SAAzC,EAAoD,KAAKI,EAAzD,EAA6D,KAAKC,EAAlE,EAAsE,KAAKa,GAA3E,EAAgF,KAAKC,GAArF,CAAP;AACH,OAFD;;AAGAxB,MAAAA,MAAM,CAACW,cAAP,CAAsB7C,MAAM,CAAC+D,6BAAP,CAAqCvD,SAA3D,EAAsE,GAAtE,EAA2E;AACvEsC,QAAAA,GAAG,EAAE,YAAY;AACb,iBAAO,KAAKH,EAAZ;AACH,SAHsE;AAIvEI,QAAAA,GAAG,EAAE,UAAUN,CAAV,EAAa;AACd,eAAKE,EAAL,GAAUF,CAAV;;AACA,eAAKX,eAAL;AACH,SAPsE;AAQvEkB,QAAAA,UAAU,EAAE;AAR2D,OAA3E;AAUAd,MAAAA,MAAM,CAACW,cAAP,CAAsB7C,MAAM,CAAC+D,6BAAP,CAAqCvD,SAA3D,EAAsE,GAAtE,EAA2E;AACvEsC,QAAAA,GAAG,EAAE,YAAY;AACb,iBAAO,KAAKF,EAAZ;AACH,SAHsE;AAIvEG,QAAAA,GAAG,EAAE,UAAUL,CAAV,EAAa;AACd,eAAKE,EAAL,GAAUF,CAAV;;AACA,eAAKZ,eAAL;AACH,SAPsE;AAQvEkB,QAAAA,UAAU,EAAE;AAR2D,OAA3E;AAUAd,MAAAA,MAAM,CAACW,cAAP,CAAsB7C,MAAM,CAAC+D,6BAAP,CAAqCvD,SAA3D,EAAsE,IAAtE,EAA4E;AACxEsC,QAAAA,GAAG,EAAE,YAAY;AACb,iBAAO,KAAKW,GAAZ;AACH,SAHuE;AAIxEV,QAAAA,GAAG,EAAE,UAAUM,EAAV,EAAc;AACf,eAAKI,GAAL,GAAWJ,EAAX;;AACA,eAAKvB,eAAL;AACH,SAPuE;AAQxEkB,QAAAA,UAAU,EAAE;AAR4D,OAA5E;AAUAd,MAAAA,MAAM,CAACW,cAAP,CAAsB7C,MAAM,CAAC+D,6BAAP,CAAqCvD,SAA3D,EAAsE,IAAtE,EAA4E;AACxEsC,QAAAA,GAAG,EAAE,YAAY;AACb,iBAAO,KAAKY,GAAZ;AACH,SAHuE;AAIxEX,QAAAA,GAAG,EAAE,UAAUO,EAAV,EAAc;AACf,eAAKI,GAAL,GAAWJ,EAAX;;AACA,eAAKxB,eAAL;AACH,SAPuE;AAQxEkB,QAAAA,UAAU,EAAE;AAR4D,OAA5E;;AAUAhD,MAAAA,MAAM,CAACgE,gBAAP,GAA0B,UAAU5D,iBAAV,EAA6BqC,CAA7B,EAAgCC,CAAhC,EAAmCuB,EAAnC,EAAuCC,EAAvC,EAA2CC,KAA3C,EAAkDC,YAAlD,EAAgEC,SAAhE,EAA2E;AACjGrE,QAAAA,MAAM,CAACC,UAAP,CAAkBgC,IAAlB,CAAuB,IAAvB,EAA6BjC,MAAM,CAACC,UAAP,CAAkBmB,eAA/C,EAAgE,GAAhE,EAAqEhB,iBAArE;AACA,aAAKuC,EAAL,GAAUF,CAAV;AACA,aAAKG,EAAL,GAAUF,CAAV;AACA,aAAK4B,GAAL,GAAWL,EAAX;AACA,aAAKM,GAAL,GAAWL,EAAX;AACA,aAAKM,MAAL,GAAcL,KAAd;AACA,aAAKM,aAAL,GAAqBL,YAArB;AACA,aAAKM,UAAL,GAAkBL,SAAlB;AACH,OATD;;AAUArE,MAAAA,MAAM,CAACgE,gBAAP,CAAwBxD,SAAxB,GAAoC0B,MAAM,CAACC,MAAP,CAAcnC,MAAM,CAACC,UAAP,CAAkBO,SAAhC,CAApC;;AACAR,MAAAA,MAAM,CAACgE,gBAAP,CAAwBxD,SAAxB,CAAkC4B,QAAlC,GAA6C,YAAY;AACrD,eAAO,2BAAP;AACH,OAFD;;AAGApC,MAAAA,MAAM,CAACgE,gBAAP,CAAwBxD,SAAxB,CAAkC6B,aAAlC,GAAkD,YAAY;AAC1D,eAAQ,KAAK/B,mBAAL,GACJ,GADI,GAEJ,KAAKgE,GAFD,GAGJ,GAHI,GAIJ,KAAKC,GAJD,GAKJ,GALI,GAMJ,KAAKC,MAND,GAOJ,GAPI,IAQH,KAAKC,aAAL,GAAqB,GAArB,GAA2B,GARxB,IASJ,GATI,IAUH,KAAKC,UAAL,GAAkB,GAAlB,GAAwB,GAVrB,IAWJ,GAXI,GAYJ,KAAK/B,EAZD,GAaJ,GAbI,GAcJ,KAAKC,EAdT;AAeH,OAhBD;;AAiBA5C,MAAAA,MAAM,CAACgE,gBAAP,CAAwBxD,SAAxB,CAAkC8B,KAAlC,GAA0C,YAAY;AAClD,eAAO,IAAItC,MAAM,CAACgE,gBAAX,CAA4BzB,SAA5B,EAAuC,KAAKI,EAA5C,EAAgD,KAAKC,EAArD,EAAyD,KAAK0B,GAA9D,EAAmE,KAAKC,GAAxE,EAA6E,KAAKC,MAAlF,EAA0F,KAAKC,aAA/F,EAA8G,KAAKC,UAAnH,CAAP;AACH,OAFD;;AAGAxC,MAAAA,MAAM,CAACW,cAAP,CAAsB7C,MAAM,CAACgE,gBAAP,CAAwBxD,SAA9C,EAAyD,GAAzD,EAA8D;AAC1DsC,QAAAA,GAAG,EAAE,YAAY;AACb,iBAAO,KAAKH,EAAZ;AACH,SAHyD;AAI1DI,QAAAA,GAAG,EAAE,UAAUN,CAAV,EAAa;AACd,eAAKE,EAAL,GAAUF,CAAV;;AACA,eAAKX,eAAL;AACH,SAPyD;AAQ1DkB,QAAAA,UAAU,EAAE;AAR8C,OAA9D;AAUAd,MAAAA,MAAM,CAACW,cAAP,CAAsB7C,MAAM,CAACgE,gBAAP,CAAwBxD,SAA9C,EAAyD,GAAzD,EAA8D;AAC1DsC,QAAAA,GAAG,EAAE,YAAY;AACb,iBAAO,KAAKF,EAAZ;AACH,SAHyD;AAI1DG,QAAAA,GAAG,EAAE,UAAUL,CAAV,EAAa;AACd,eAAKE,EAAL,GAAUF,CAAV;;AACA,eAAKZ,eAAL;AACH,SAPyD;AAQ1DkB,QAAAA,UAAU,EAAE;AAR8C,OAA9D;AAUAd,MAAAA,MAAM,CAACW,cAAP,CAAsB7C,MAAM,CAACgE,gBAAP,CAAwBxD,SAA9C,EAAyD,IAAzD,EAA+D;AAC3DsC,QAAAA,GAAG,EAAE,YAAY;AACb,iBAAO,KAAKwB,GAAZ;AACH,SAH0D;AAI3DvB,QAAAA,GAAG,EAAE,UAAUkB,EAAV,EAAc;AACf,eAAKK,GAAL,GAAWL,EAAX;;AACA,eAAKnC,eAAL;AACH,SAP0D;AAQ3DkB,QAAAA,UAAU,EAAE;AAR+C,OAA/D;AAUAd,MAAAA,MAAM,CAACW,cAAP,CAAsB7C,MAAM,CAACgE,gBAAP,CAAwBxD,SAA9C,EAAyD,IAAzD,EAA+D;AAC3DsC,QAAAA,GAAG,EAAE,YAAY;AACb,iBAAO,KAAKyB,GAAZ;AACH,SAH0D;AAI3DxB,QAAAA,GAAG,EAAE,UAAUmB,EAAV,EAAc;AACf,eAAKK,GAAL,GAAWL,EAAX;;AACA,eAAKpC,eAAL;AACH,SAP0D;AAQ3DkB,QAAAA,UAAU,EAAE;AAR+C,OAA/D;AAUAd,MAAAA,MAAM,CAACW,cAAP,CAAsB7C,MAAM,CAACgE,gBAAP,CAAwBxD,SAA9C,EAAyD,OAAzD,EAAkE;AAC9DsC,QAAAA,GAAG,EAAE,YAAY;AACb,iBAAO,KAAK0B,MAAZ;AACH,SAH6D;AAI9DzB,QAAAA,GAAG,EAAE,UAAUoB,KAAV,EAAiB;AAClB,eAAKK,MAAL,GAAcL,KAAd;;AACA,eAAKrC,eAAL;AACH,SAP6D;AAQ9DkB,QAAAA,UAAU,EAAE;AARkD,OAAlE;AAUAd,MAAAA,MAAM,CAACW,cAAP,CAAsB7C,MAAM,CAACgE,gBAAP,CAAwBxD,SAA9C,EAAyD,cAAzD,EAAyE;AACrEsC,QAAAA,GAAG,EAAE,YAAY;AACb,iBAAO,KAAK2B,aAAZ;AACH,SAHoE;AAIrE1B,QAAAA,GAAG,EAAE,UAAUqB,YAAV,EAAwB;AACzB,eAAKK,aAAL,GAAqBL,YAArB;;AACA,eAAKtC,eAAL;AACH,SAPoE;AAQrEkB,QAAAA,UAAU,EAAE;AARyD,OAAzE;AAUAd,MAAAA,MAAM,CAACW,cAAP,CAAsB7C,MAAM,CAACgE,gBAAP,CAAwBxD,SAA9C,EAAyD,WAAzD,EAAsE;AAClEsC,QAAAA,GAAG,EAAE,YAAY;AACb,iBAAO,KAAK4B,UAAZ;AACH,SAHiE;AAIlE3B,QAAAA,GAAG,EAAE,UAAUsB,SAAV,EAAqB;AACtB,eAAKK,UAAL,GAAkBL,SAAlB;;AACA,eAAKvC,eAAL;AACH,SAPiE;AAQlEkB,QAAAA,UAAU,EAAE;AARsD,OAAtE;;AAUAhD,MAAAA,MAAM,CAAC2E,gBAAP,GAA0B,UAAUvE,iBAAV,EAA6BqC,CAA7B,EAAgCC,CAAhC,EAAmCuB,EAAnC,EAAuCC,EAAvC,EAA2CC,KAA3C,EAAkDC,YAAlD,EAAgEC,SAAhE,EAA2E;AACjGrE,QAAAA,MAAM,CAACC,UAAP,CAAkBgC,IAAlB,CAAuB,IAAvB,EAA6BjC,MAAM,CAACC,UAAP,CAAkBoB,eAA/C,EAAgE,GAAhE,EAAqEjB,iBAArE;AACA,aAAKuC,EAAL,GAAUF,CAAV;AACA,aAAKG,EAAL,GAAUF,CAAV;AACA,aAAK4B,GAAL,GAAWL,EAAX;AACA,aAAKM,GAAL,GAAWL,EAAX;AACA,aAAKM,MAAL,GAAcL,KAAd;AACA,aAAKM,aAAL,GAAqBL,YAArB;AACA,aAAKM,UAAL,GAAkBL,SAAlB;AACH,OATD;;AAUArE,MAAAA,MAAM,CAAC2E,gBAAP,CAAwBnE,SAAxB,GAAoC0B,MAAM,CAACC,MAAP,CAAcnC,MAAM,CAACC,UAAP,CAAkBO,SAAhC,CAApC;;AACAR,MAAAA,MAAM,CAAC2E,gBAAP,CAAwBnE,SAAxB,CAAkC4B,QAAlC,GAA6C,YAAY;AACrD,eAAO,2BAAP;AACH,OAFD;;AAGApC,MAAAA,MAAM,CAAC2E,gBAAP,CAAwBnE,SAAxB,CAAkC6B,aAAlC,GAAkD,YAAY;AAC1D,eAAQ,KAAK/B,mBAAL,GACJ,GADI,GAEJ,KAAKgE,GAFD,GAGJ,GAHI,GAIJ,KAAKC,GAJD,GAKJ,GALI,GAMJ,KAAKC,MAND,GAOJ,GAPI,IAQH,KAAKC,aAAL,GAAqB,GAArB,GAA2B,GARxB,IASJ,GATI,IAUH,KAAKC,UAAL,GAAkB,GAAlB,GAAwB,GAVrB,IAWJ,GAXI,GAYJ,KAAK/B,EAZD,GAaJ,GAbI,GAcJ,KAAKC,EAdT;AAeH,OAhBD;;AAiBA5C,MAAAA,MAAM,CAAC2E,gBAAP,CAAwBnE,SAAxB,CAAkC8B,KAAlC,GAA0C,YAAY;AAClD,eAAO,IAAItC,MAAM,CAAC2E,gBAAX,CAA4BpC,SAA5B,EAAuC,KAAKI,EAA5C,EAAgD,KAAKC,EAArD,EAAyD,KAAK0B,GAA9D,EAAmE,KAAKC,GAAxE,EAA6E,KAAKC,MAAlF,EAA0F,KAAKC,aAA/F,EAA8G,KAAKC,UAAnH,CAAP;AACH,OAFD;;AAGAxC,MAAAA,MAAM,CAACW,cAAP,CAAsB7C,MAAM,CAAC2E,gBAAP,CAAwBnE,SAA9C,EAAyD,GAAzD,EAA8D;AAC1DsC,QAAAA,GAAG,EAAE,YAAY;AACb,iBAAO,KAAKH,EAAZ;AACH,SAHyD;AAI1DI,QAAAA,GAAG,EAAE,UAAUN,CAAV,EAAa;AACd,eAAKE,EAAL,GAAUF,CAAV;;AACA,eAAKX,eAAL;AACH,SAPyD;AAQ1DkB,QAAAA,UAAU,EAAE;AAR8C,OAA9D;AAUAd,MAAAA,MAAM,CAACW,cAAP,CAAsB7C,MAAM,CAAC2E,gBAAP,CAAwBnE,SAA9C,EAAyD,GAAzD,EAA8D;AAC1DsC,QAAAA,GAAG,EAAE,YAAY;AACb,iBAAO,KAAKF,EAAZ;AACH,SAHyD;AAI1DG,QAAAA,GAAG,EAAE,UAAUL,CAAV,EAAa;AACd,eAAKE,EAAL,GAAUF,CAAV;;AACA,eAAKZ,eAAL;AACH,SAPyD;AAQ1DkB,QAAAA,UAAU,EAAE;AAR8C,OAA9D;AAUAd,MAAAA,MAAM,CAACW,cAAP,CAAsB7C,MAAM,CAAC2E,gBAAP,CAAwBnE,SAA9C,EAAyD,IAAzD,EAA+D;AAC3DsC,QAAAA,GAAG,EAAE,YAAY;AACb,iBAAO,KAAKwB,GAAZ;AACH,SAH0D;AAI3DvB,QAAAA,GAAG,EAAE,UAAUkB,EAAV,EAAc;AACf,eAAKK,GAAL,GAAWL,EAAX;;AACA,eAAKnC,eAAL;AACH,SAP0D;AAQ3DkB,QAAAA,UAAU,EAAE;AAR+C,OAA/D;AAUAd,MAAAA,MAAM,CAACW,cAAP,CAAsB7C,MAAM,CAAC2E,gBAAP,CAAwBnE,SAA9C,EAAyD,IAAzD,EAA+D;AAC3DsC,QAAAA,GAAG,EAAE,YAAY;AACb,iBAAO,KAAKyB,GAAZ;AACH,SAH0D;AAI3DxB,QAAAA,GAAG,EAAE,UAAUmB,EAAV,EAAc;AACf,eAAKK,GAAL,GAAWL,EAAX;;AACA,eAAKpC,eAAL;AACH,SAP0D;AAQ3DkB,QAAAA,UAAU,EAAE;AAR+C,OAA/D;AAUAd,MAAAA,MAAM,CAACW,cAAP,CAAsB7C,MAAM,CAAC2E,gBAAP,CAAwBnE,SAA9C,EAAyD,OAAzD,EAAkE;AAC9DsC,QAAAA,GAAG,EAAE,YAAY;AACb,iBAAO,KAAK0B,MAAZ;AACH,SAH6D;AAI9DzB,QAAAA,GAAG,EAAE,UAAUoB,KAAV,EAAiB;AAClB,eAAKK,MAAL,GAAcL,KAAd;;AACA,eAAKrC,eAAL;AACH,SAP6D;AAQ9DkB,QAAAA,UAAU,EAAE;AARkD,OAAlE;AAUAd,MAAAA,MAAM,CAACW,cAAP,CAAsB7C,MAAM,CAAC2E,gBAAP,CAAwBnE,SAA9C,EAAyD,cAAzD,EAAyE;AACrEsC,QAAAA,GAAG,EAAE,YAAY;AACb,iBAAO,KAAK2B,aAAZ;AACH,SAHoE;AAIrE1B,QAAAA,GAAG,EAAE,UAAUqB,YAAV,EAAwB;AACzB,eAAKK,aAAL,GAAqBL,YAArB;;AACA,eAAKtC,eAAL;AACH,SAPoE;AAQrEkB,QAAAA,UAAU,EAAE;AARyD,OAAzE;AAUAd,MAAAA,MAAM,CAACW,cAAP,CAAsB7C,MAAM,CAAC2E,gBAAP,CAAwBnE,SAA9C,EAAyD,WAAzD,EAAsE;AAClEsC,QAAAA,GAAG,EAAE,YAAY;AACb,iBAAO,KAAK4B,UAAZ;AACH,SAHiE;AAIlE3B,QAAAA,GAAG,EAAE,UAAUsB,SAAV,EAAqB;AACtB,eAAKK,UAAL,GAAkBL,SAAlB;;AACA,eAAKvC,eAAL;AACH,SAPiE;AAQlEkB,QAAAA,UAAU,EAAE;AARsD,OAAtE;;AAUAhD,MAAAA,MAAM,CAAC4E,6BAAP,GAAuC,UAAUxE,iBAAV,EAA6BqC,CAA7B,EAAgC;AACnEzC,QAAAA,MAAM,CAACC,UAAP,CAAkBgC,IAAlB,CAAuB,IAAvB,EAA6BjC,MAAM,CAACC,UAAP,CAAkBqB,6BAA/C,EAA8E,GAA9E,EAAmFlB,iBAAnF;AACA,aAAKuC,EAAL,GAAUF,CAAV;AACH,OAHD;;AAIAzC,MAAAA,MAAM,CAAC4E,6BAAP,CAAqCpE,SAArC,GAAiD0B,MAAM,CAACC,MAAP,CAAcnC,MAAM,CAACC,UAAP,CAAkBO,SAAhC,CAAjD;;AACAR,MAAAA,MAAM,CAAC4E,6BAAP,CAAqCpE,SAArC,CAA+C4B,QAA/C,GAA0D,YAAY;AAClE,eAAO,wCAAP;AACH,OAFD;;AAGApC,MAAAA,MAAM,CAAC4E,6BAAP,CAAqCpE,SAArC,CAA+C6B,aAA/C,GAA+D,YAAY;AACvE,eAAO,KAAK/B,mBAAL,GAA2B,GAA3B,GAAiC,KAAKqC,EAA7C;AACH,OAFD;;AAGA3C,MAAAA,MAAM,CAAC4E,6BAAP,CAAqCpE,SAArC,CAA+C8B,KAA/C,GAAuD,YAAY;AAC/D,eAAO,IAAItC,MAAM,CAAC4E,6BAAX,CAAyCrC,SAAzC,EAAoD,KAAKI,EAAzD,CAAP;AACH,OAFD;;AAGAT,MAAAA,MAAM,CAACW,cAAP,CAAsB7C,MAAM,CAAC4E,6BAAP,CAAqCpE,SAA3D,EAAsE,GAAtE,EAA2E;AACvEsC,QAAAA,GAAG,EAAE,YAAY;AACb,iBAAO,KAAKH,EAAZ;AACH,SAHsE;AAIvEI,QAAAA,GAAG,EAAE,UAAUN,CAAV,EAAa;AACd,eAAKE,EAAL,GAAUF,CAAV;;AACA,eAAKX,eAAL;AACH,SAPsE;AAQvEkB,QAAAA,UAAU,EAAE;AAR2D,OAA3E;;AAUAhD,MAAAA,MAAM,CAAC6E,6BAAP,GAAuC,UAAUzE,iBAAV,EAA6BqC,CAA7B,EAAgC;AACnEzC,QAAAA,MAAM,CAACC,UAAP,CAAkBgC,IAAlB,CAAuB,IAAvB,EAA6BjC,MAAM,CAACC,UAAP,CAAkBsB,6BAA/C,EAA8E,GAA9E,EAAmFnB,iBAAnF;AACA,aAAKuC,EAAL,GAAUF,CAAV;AACH,OAHD;;AAIAzC,MAAAA,MAAM,CAAC6E,6BAAP,CAAqCrE,SAArC,GAAiD0B,MAAM,CAACC,MAAP,CAAcnC,MAAM,CAACC,UAAP,CAAkBO,SAAhC,CAAjD;;AACAR,MAAAA,MAAM,CAAC6E,6BAAP,CAAqCrE,SAArC,CAA+C4B,QAA/C,GAA0D,YAAY;AAClE,eAAO,wCAAP;AACH,OAFD;;AAGApC,MAAAA,MAAM,CAAC6E,6BAAP,CAAqCrE,SAArC,CAA+C6B,aAA/C,GAA+D,YAAY;AACvE,eAAO,KAAK/B,mBAAL,GAA2B,GAA3B,GAAiC,KAAKqC,EAA7C;AACH,OAFD;;AAGA3C,MAAAA,MAAM,CAAC6E,6BAAP,CAAqCrE,SAArC,CAA+C8B,KAA/C,GAAuD,YAAY;AAC/D,eAAO,IAAItC,MAAM,CAAC6E,6BAAX,CAAyCtC,SAAzC,EAAoD,KAAKI,EAAzD,CAAP;AACH,OAFD;;AAGAT,MAAAA,MAAM,CAACW,cAAP,CAAsB7C,MAAM,CAAC6E,6BAAP,CAAqCrE,SAA3D,EAAsE,GAAtE,EAA2E;AACvEsC,QAAAA,GAAG,EAAE,YAAY;AACb,iBAAO,KAAKH,EAAZ;AACH,SAHsE;AAIvEI,QAAAA,GAAG,EAAE,UAAUN,CAAV,EAAa;AACd,eAAKE,EAAL,GAAUF,CAAV;;AACA,eAAKX,eAAL;AACH,SAPsE;AAQvEkB,QAAAA,UAAU,EAAE;AAR2D,OAA3E;;AAUAhD,MAAAA,MAAM,CAAC8E,2BAAP,GAAqC,UAAU1E,iBAAV,EAA6BsC,CAA7B,EAAgC;AACjE1C,QAAAA,MAAM,CAACC,UAAP,CAAkBgC,IAAlB,CAAuB,IAAvB,EAA6BjC,MAAM,CAACC,UAAP,CAAkBuB,2BAA/C,EAA4E,GAA5E,EAAiFpB,iBAAjF;AACA,aAAKwC,EAAL,GAAUF,CAAV;AACH,OAHD;;AAIA1C,MAAAA,MAAM,CAAC8E,2BAAP,CAAmCtE,SAAnC,GAA+C0B,MAAM,CAACC,MAAP,CAAcnC,MAAM,CAACC,UAAP,CAAkBO,SAAhC,CAA/C;;AACAR,MAAAA,MAAM,CAAC8E,2BAAP,CAAmCtE,SAAnC,CAA6C4B,QAA7C,GAAwD,YAAY;AAChE,eAAO,sCAAP;AACH,OAFD;;AAGApC,MAAAA,MAAM,CAAC8E,2BAAP,CAAmCtE,SAAnC,CAA6C6B,aAA7C,GAA6D,YAAY;AACrE,eAAO,KAAK/B,mBAAL,GAA2B,GAA3B,GAAiC,KAAKsC,EAA7C;AACH,OAFD;;AAGA5C,MAAAA,MAAM,CAAC8E,2BAAP,CAAmCtE,SAAnC,CAA6C8B,KAA7C,GAAqD,YAAY;AAC7D,eAAO,IAAItC,MAAM,CAAC8E,2BAAX,CAAuCvC,SAAvC,EAAkD,KAAKK,EAAvD,CAAP;AACH,OAFD;;AAGAV,MAAAA,MAAM,CAACW,cAAP,CAAsB7C,MAAM,CAAC8E,2BAAP,CAAmCtE,SAAzD,EAAoE,GAApE,EAAyE;AACrEsC,QAAAA,GAAG,EAAE,YAAY;AACb,iBAAO,KAAKF,EAAZ;AACH,SAHoE;AAIrEG,QAAAA,GAAG,EAAE,UAAUL,CAAV,EAAa;AACd,eAAKE,EAAL,GAAUF,CAAV;;AACA,eAAKZ,eAAL;AACH,SAPoE;AAQrEkB,QAAAA,UAAU,EAAE;AARyD,OAAzE;;AAUAhD,MAAAA,MAAM,CAAC+E,2BAAP,GAAqC,UAAU3E,iBAAV,EAA6BsC,CAA7B,EAAgC;AACjE1C,QAAAA,MAAM,CAACC,UAAP,CAAkBgC,IAAlB,CAAuB,IAAvB,EAA6BjC,MAAM,CAACC,UAAP,CAAkBwB,2BAA/C,EAA4E,GAA5E,EAAiFrB,iBAAjF;AACA,aAAKwC,EAAL,GAAUF,CAAV;AACH,OAHD;;AAIA1C,MAAAA,MAAM,CAAC+E,2BAAP,CAAmCvE,SAAnC,GAA+C0B,MAAM,CAACC,MAAP,CAAcnC,MAAM,CAACC,UAAP,CAAkBO,SAAhC,CAA/C;;AACAR,MAAAA,MAAM,CAAC+E,2BAAP,CAAmCvE,SAAnC,CAA6C4B,QAA7C,GAAwD,YAAY;AAChE,eAAO,sCAAP;AACH,OAFD;;AAGApC,MAAAA,MAAM,CAAC+E,2BAAP,CAAmCvE,SAAnC,CAA6C6B,aAA7C,GAA6D,YAAY;AACrE,eAAO,KAAK/B,mBAAL,GAA2B,GAA3B,GAAiC,KAAKsC,EAA7C;AACH,OAFD;;AAGA5C,MAAAA,MAAM,CAAC+E,2BAAP,CAAmCvE,SAAnC,CAA6C8B,KAA7C,GAAqD,YAAY;AAC7D,eAAO,IAAItC,MAAM,CAAC+E,2BAAX,CAAuCxC,SAAvC,EAAkD,KAAKK,EAAvD,CAAP;AACH,OAFD;;AAGAV,MAAAA,MAAM,CAACW,cAAP,CAAsB7C,MAAM,CAAC+E,2BAAP,CAAmCvE,SAAzD,EAAoE,GAApE,EAAyE;AACrEsC,QAAAA,GAAG,EAAE,YAAY;AACb,iBAAO,KAAKF,EAAZ;AACH,SAHoE;AAIrEG,QAAAA,GAAG,EAAE,UAAUL,CAAV,EAAa;AACd,eAAKE,EAAL,GAAUF,CAAV;;AACA,eAAKZ,eAAL;AACH,SAPoE;AAQrEkB,QAAAA,UAAU,EAAE;AARyD,OAAzE;;AAUAhD,MAAAA,MAAM,CAACgF,+BAAP,GAAyC,UAAU5E,iBAAV,EAA6BqC,CAA7B,EAAgCC,CAAhC,EAAmCa,EAAnC,EAAuCC,EAAvC,EAA2C;AAChFxD,QAAAA,MAAM,CAACC,UAAP,CAAkBgC,IAAlB,CAAuB,IAAvB,EAA6BjC,MAAM,CAACC,UAAP,CAAkByB,gCAA/C,EAAiF,GAAjF,EAAsFtB,iBAAtF;AACA,aAAKuC,EAAL,GAAUF,CAAV;AACA,aAAKG,EAAL,GAAUF,CAAV;AACA,aAAKiB,GAAL,GAAWJ,EAAX;AACA,aAAKK,GAAL,GAAWJ,EAAX;AACH,OAND;;AAOAxD,MAAAA,MAAM,CAACgF,+BAAP,CAAuCxE,SAAvC,GAAmD0B,MAAM,CAACC,MAAP,CAAcnC,MAAM,CAACC,UAAP,CAAkBO,SAAhC,CAAnD;;AACAR,MAAAA,MAAM,CAACgF,+BAAP,CAAuCxE,SAAvC,CAAiD4B,QAAjD,GAA4D,YAAY;AACpE,eAAO,0CAAP;AACH,OAFD;;AAGApC,MAAAA,MAAM,CAACgF,+BAAP,CAAuCxE,SAAvC,CAAiD6B,aAAjD,GAAiE,YAAY;AACzE,eAAO,KAAK/B,mBAAL,GAA2B,GAA3B,GAAiC,KAAKqD,GAAtC,GAA4C,GAA5C,GAAkD,KAAKC,GAAvD,GAA6D,GAA7D,GAAmE,KAAKjB,EAAxE,GAA6E,GAA7E,GAAmF,KAAKC,EAA/F;AACH,OAFD;;AAGA5C,MAAAA,MAAM,CAACgF,+BAAP,CAAuCxE,SAAvC,CAAiD8B,KAAjD,GAAyD,YAAY;AACjE,eAAO,IAAItC,MAAM,CAACgF,+BAAX,CAA2CzC,SAA3C,EAAsD,KAAKI,EAA3D,EAA+D,KAAKC,EAApE,EAAwE,KAAKe,GAA7E,EAAkF,KAAKC,GAAvF,CAAP;AACH,OAFD;;AAGA1B,MAAAA,MAAM,CAACW,cAAP,CAAsB7C,MAAM,CAACgF,+BAAP,CAAuCxE,SAA7D,EAAwE,GAAxE,EAA6E;AACzEsC,QAAAA,GAAG,EAAE,YAAY;AACb,iBAAO,KAAKH,EAAZ;AACH,SAHwE;AAIzEI,QAAAA,GAAG,EAAE,UAAUN,CAAV,EAAa;AACd,eAAKE,EAAL,GAAUF,CAAV;;AACA,eAAKX,eAAL;AACH,SAPwE;AAQzEkB,QAAAA,UAAU,EAAE;AAR6D,OAA7E;AAUAd,MAAAA,MAAM,CAACW,cAAP,CAAsB7C,MAAM,CAACgF,+BAAP,CAAuCxE,SAA7D,EAAwE,GAAxE,EAA6E;AACzEsC,QAAAA,GAAG,EAAE,YAAY;AACb,iBAAO,KAAKF,EAAZ;AACH,SAHwE;AAIzEG,QAAAA,GAAG,EAAE,UAAUL,CAAV,EAAa;AACd,eAAKE,EAAL,GAAUF,CAAV;;AACA,eAAKZ,eAAL;AACH,SAPwE;AAQzEkB,QAAAA,UAAU,EAAE;AAR6D,OAA7E;AAUAd,MAAAA,MAAM,CAACW,cAAP,CAAsB7C,MAAM,CAACgF,+BAAP,CAAuCxE,SAA7D,EAAwE,IAAxE,EAA8E;AAC1EsC,QAAAA,GAAG,EAAE,YAAY;AACb,iBAAO,KAAKa,GAAZ;AACH,SAHyE;AAI1EZ,QAAAA,GAAG,EAAE,UAAUQ,EAAV,EAAc;AACf,eAAKI,GAAL,GAAWJ,EAAX;;AACA,eAAKzB,eAAL;AACH,SAPyE;AAQ1EkB,QAAAA,UAAU,EAAE;AAR8D,OAA9E;AAUAd,MAAAA,MAAM,CAACW,cAAP,CAAsB7C,MAAM,CAACgF,+BAAP,CAAuCxE,SAA7D,EAAwE,IAAxE,EAA8E;AAC1EsC,QAAAA,GAAG,EAAE,YAAY;AACb,iBAAO,KAAKc,GAAZ;AACH,SAHyE;AAI1Eb,QAAAA,GAAG,EAAE,UAAUS,EAAV,EAAc;AACf,eAAKI,GAAL,GAAWJ,EAAX;;AACA,eAAK1B,eAAL;AACH,SAPyE;AAQ1EkB,QAAAA,UAAU,EAAE;AAR8D,OAA9E;;AAUAhD,MAAAA,MAAM,CAACiF,+BAAP,GAAyC,UAAU7E,iBAAV,EAA6BqC,CAA7B,EAAgCC,CAAhC,EAAmCa,EAAnC,EAAuCC,EAAvC,EAA2C;AAChFxD,QAAAA,MAAM,CAACC,UAAP,CAAkBgC,IAAlB,CAAuB,IAAvB,EAA6BjC,MAAM,CAACC,UAAP,CAAkB0B,gCAA/C,EAAiF,GAAjF,EAAsFvB,iBAAtF;AACA,aAAKuC,EAAL,GAAUF,CAAV;AACA,aAAKG,EAAL,GAAUF,CAAV;AACA,aAAKiB,GAAL,GAAWJ,EAAX;AACA,aAAKK,GAAL,GAAWJ,EAAX;AACH,OAND;;AAOAxD,MAAAA,MAAM,CAACiF,+BAAP,CAAuCzE,SAAvC,GAAmD0B,MAAM,CAACC,MAAP,CAAcnC,MAAM,CAACC,UAAP,CAAkBO,SAAhC,CAAnD;;AACAR,MAAAA,MAAM,CAACiF,+BAAP,CAAuCzE,SAAvC,CAAiD4B,QAAjD,GAA4D,YAAY;AACpE,eAAO,0CAAP;AACH,OAFD;;AAGApC,MAAAA,MAAM,CAACiF,+BAAP,CAAuCzE,SAAvC,CAAiD6B,aAAjD,GAAiE,YAAY;AACzE,eAAO,KAAK/B,mBAAL,GAA2B,GAA3B,GAAiC,KAAKqD,GAAtC,GAA4C,GAA5C,GAAkD,KAAKC,GAAvD,GAA6D,GAA7D,GAAmE,KAAKjB,EAAxE,GAA6E,GAA7E,GAAmF,KAAKC,EAA/F;AACH,OAFD;;AAGA5C,MAAAA,MAAM,CAACiF,+BAAP,CAAuCzE,SAAvC,CAAiD8B,KAAjD,GAAyD,YAAY;AACjE,eAAO,IAAItC,MAAM,CAACiF,+BAAX,CAA2C1C,SAA3C,EAAsD,KAAKI,EAA3D,EAA+D,KAAKC,EAApE,EAAwE,KAAKe,GAA7E,EAAkF,KAAKC,GAAvF,CAAP;AACH,OAFD;;AAGA1B,MAAAA,MAAM,CAACW,cAAP,CAAsB7C,MAAM,CAACiF,+BAAP,CAAuCzE,SAA7D,EAAwE,GAAxE,EAA6E;AACzEsC,QAAAA,GAAG,EAAE,YAAY;AACb,iBAAO,KAAKH,EAAZ;AACH,SAHwE;AAIzEI,QAAAA,GAAG,EAAE,UAAUN,CAAV,EAAa;AACd,eAAKE,EAAL,GAAUF,CAAV;;AACA,eAAKX,eAAL;AACH,SAPwE;AAQzEkB,QAAAA,UAAU,EAAE;AAR6D,OAA7E;AAUAd,MAAAA,MAAM,CAACW,cAAP,CAAsB7C,MAAM,CAACiF,+BAAP,CAAuCzE,SAA7D,EAAwE,GAAxE,EAA6E;AACzEsC,QAAAA,GAAG,EAAE,YAAY;AACb,iBAAO,KAAKF,EAAZ;AACH,SAHwE;AAIzEG,QAAAA,GAAG,EAAE,UAAUL,CAAV,EAAa;AACd,eAAKE,EAAL,GAAUF,CAAV;;AACA,eAAKZ,eAAL;AACH,SAPwE;AAQzEkB,QAAAA,UAAU,EAAE;AAR6D,OAA7E;AAUAd,MAAAA,MAAM,CAACW,cAAP,CAAsB7C,MAAM,CAACiF,+BAAP,CAAuCzE,SAA7D,EAAwE,IAAxE,EAA8E;AAC1EsC,QAAAA,GAAG,EAAE,YAAY;AACb,iBAAO,KAAKa,GAAZ;AACH,SAHyE;AAI1EZ,QAAAA,GAAG,EAAE,UAAUQ,EAAV,EAAc;AACf,eAAKI,GAAL,GAAWJ,EAAX;;AACA,eAAKzB,eAAL;AACH,SAPyE;AAQ1EkB,QAAAA,UAAU,EAAE;AAR8D,OAA9E;AAUAd,MAAAA,MAAM,CAACW,cAAP,CAAsB7C,MAAM,CAACiF,+BAAP,CAAuCzE,SAA7D,EAAwE,IAAxE,EAA8E;AAC1EsC,QAAAA,GAAG,EAAE,YAAY;AACb,iBAAO,KAAKc,GAAZ;AACH,SAHyE;AAI1Eb,QAAAA,GAAG,EAAE,UAAUS,EAAV,EAAc;AACf,eAAKI,GAAL,GAAWJ,EAAX;;AACA,eAAK1B,eAAL;AACH,SAPyE;AAQ1EkB,QAAAA,UAAU,EAAE;AAR8D,OAA9E;;AAUAhD,MAAAA,MAAM,CAACkF,mCAAP,GAA6C,UAAU9E,iBAAV,EAA6BqC,CAA7B,EAAgCC,CAAhC,EAAmC;AAC5E1C,QAAAA,MAAM,CAACC,UAAP,CAAkBgC,IAAlB,CAAuB,IAAvB,EAA6BjC,MAAM,CAACC,UAAP,CAAkB2B,oCAA/C,EAAqF,GAArF,EAA0FxB,iBAA1F;AACA,aAAKuC,EAAL,GAAUF,CAAV;AACA,aAAKG,EAAL,GAAUF,CAAV;AACH,OAJD;;AAKA1C,MAAAA,MAAM,CAACkF,mCAAP,CAA2C1E,SAA3C,GAAuD0B,MAAM,CAACC,MAAP,CAAcnC,MAAM,CAACC,UAAP,CAAkBO,SAAhC,CAAvD;;AACAR,MAAAA,MAAM,CAACkF,mCAAP,CAA2C1E,SAA3C,CAAqD4B,QAArD,GAAgE,YAAY;AACxE,eAAO,8CAAP;AACH,OAFD;;AAGApC,MAAAA,MAAM,CAACkF,mCAAP,CAA2C1E,SAA3C,CAAqD6B,aAArD,GAAqE,YAAY;AAC7E,eAAO,KAAK/B,mBAAL,GAA2B,GAA3B,GAAiC,KAAKqC,EAAtC,GAA2C,GAA3C,GAAiD,KAAKC,EAA7D;AACH,OAFD;;AAGA5C,MAAAA,MAAM,CAACkF,mCAAP,CAA2C1E,SAA3C,CAAqD8B,KAArD,GAA6D,YAAY;AACrE,eAAO,IAAItC,MAAM,CAACkF,mCAAX,CAA+C3C,SAA/C,EAA0D,KAAKI,EAA/D,EAAmE,KAAKC,EAAxE,CAAP;AACH,OAFD;;AAGAV,MAAAA,MAAM,CAACW,cAAP,CAAsB7C,MAAM,CAACkF,mCAAP,CAA2C1E,SAAjE,EAA4E,GAA5E,EAAiF;AAC7EsC,QAAAA,GAAG,EAAE,YAAY;AACb,iBAAO,KAAKH,EAAZ;AACH,SAH4E;AAI7EI,QAAAA,GAAG,EAAE,UAAUN,CAAV,EAAa;AACd,eAAKE,EAAL,GAAUF,CAAV;;AACA,eAAKX,eAAL;AACH,SAP4E;AAQ7EkB,QAAAA,UAAU,EAAE;AARiE,OAAjF;AAUAd,MAAAA,MAAM,CAACW,cAAP,CAAsB7C,MAAM,CAACkF,mCAAP,CAA2C1E,SAAjE,EAA4E,GAA5E,EAAiF;AAC7EsC,QAAAA,GAAG,EAAE,YAAY;AACb,iBAAO,KAAKF,EAAZ;AACH,SAH4E;AAI7EG,QAAAA,GAAG,EAAE,UAAUL,CAAV,EAAa;AACd,eAAKE,EAAL,GAAUF,CAAV;;AACA,eAAKZ,eAAL;AACH,SAP4E;AAQ7EkB,QAAAA,UAAU,EAAE;AARiE,OAAjF;;AAUAhD,MAAAA,MAAM,CAACmF,mCAAP,GAA6C,UAAU/E,iBAAV,EAA6BqC,CAA7B,EAAgCC,CAAhC,EAAmC;AAC5E1C,QAAAA,MAAM,CAACC,UAAP,CAAkBgC,IAAlB,CAAuB,IAAvB,EAA6BjC,MAAM,CAACC,UAAP,CAAkB4B,oCAA/C,EAAqF,GAArF,EAA0FzB,iBAA1F;AACA,aAAKuC,EAAL,GAAUF,CAAV;AACA,aAAKG,EAAL,GAAUF,CAAV;AACH,OAJD;;AAKA1C,MAAAA,MAAM,CAACmF,mCAAP,CAA2C3E,SAA3C,GAAuD0B,MAAM,CAACC,MAAP,CAAcnC,MAAM,CAACC,UAAP,CAAkBO,SAAhC,CAAvD;;AACAR,MAAAA,MAAM,CAACmF,mCAAP,CAA2C3E,SAA3C,CAAqD4B,QAArD,GAAgE,YAAY;AACxE,eAAO,8CAAP;AACH,OAFD;;AAGApC,MAAAA,MAAM,CAACmF,mCAAP,CAA2C3E,SAA3C,CAAqD6B,aAArD,GAAqE,YAAY;AAC7E,eAAO,KAAK/B,mBAAL,GAA2B,GAA3B,GAAiC,KAAKqC,EAAtC,GAA2C,GAA3C,GAAiD,KAAKC,EAA7D;AACH,OAFD;;AAGA5C,MAAAA,MAAM,CAACmF,mCAAP,CAA2C3E,SAA3C,CAAqD8B,KAArD,GAA6D,YAAY;AACrE,eAAO,IAAItC,MAAM,CAACmF,mCAAX,CAA+C5C,SAA/C,EAA0D,KAAKI,EAA/D,EAAmE,KAAKC,EAAxE,CAAP;AACH,OAFD;;AAGAV,MAAAA,MAAM,CAACW,cAAP,CAAsB7C,MAAM,CAACmF,mCAAP,CAA2C3E,SAAjE,EAA4E,GAA5E,EAAiF;AAC7EsC,QAAAA,GAAG,EAAE,YAAY;AACb,iBAAO,KAAKH,EAAZ;AACH,SAH4E;AAI7EI,QAAAA,GAAG,EAAE,UAAUN,CAAV,EAAa;AACd,eAAKE,EAAL,GAAUF,CAAV;;AACA,eAAKX,eAAL;AACH,SAP4E;AAQ7EkB,QAAAA,UAAU,EAAE;AARiE,OAAjF;AAUAd,MAAAA,MAAM,CAACW,cAAP,CAAsB7C,MAAM,CAACmF,mCAAP,CAA2C3E,SAAjE,EAA4E,GAA5E,EAAiF;AAC7EsC,QAAAA,GAAG,EAAE,YAAY;AACb,iBAAO,KAAKF,EAAZ;AACH,SAH4E;AAI7EG,QAAAA,GAAG,EAAE,UAAUL,CAAV,EAAa;AACd,eAAKE,EAAL,GAAUF,CAAV;;AACA,eAAKZ,eAAL;AACH,SAP4E;AAQ7EkB,QAAAA,UAAU,EAAE;AARiE,OAAjF;;AAUAhD,MAAAA,MAAM,CAACoF,cAAP,CAAsB5E,SAAtB,CAAgC6E,yBAAhC,GAA4D,YAAY;AACpE,eAAO,IAAIrF,MAAM,CAACgC,mBAAX,CAA+BO,SAA/B,CAAP;AACH,OAFD;;AAGAvC,MAAAA,MAAM,CAACoF,cAAP,CAAsB5E,SAAtB,CAAgC8E,yBAAhC,GAA4D,UAAU7C,CAAV,EAAaC,CAAb,EAAgB;AACxE,eAAO,IAAI1C,MAAM,CAACwC,mBAAX,CAA+BD,SAA/B,EAA0CE,CAA1C,EAA6CC,CAA7C,CAAP;AACH,OAFD;;AAGA1C,MAAAA,MAAM,CAACoF,cAAP,CAAsB5E,SAAtB,CAAgC+E,yBAAhC,GAA4D,UAAU9C,CAAV,EAAaC,CAAb,EAAgB;AACxE,eAAO,IAAI1C,MAAM,CAACiD,mBAAX,CAA+BV,SAA/B,EAA0CE,CAA1C,EAA6CC,CAA7C,CAAP;AACH,OAFD;;AAGA1C,MAAAA,MAAM,CAACoF,cAAP,CAAsB5E,SAAtB,CAAgCgF,yBAAhC,GAA4D,UAAU/C,CAAV,EAAaC,CAAb,EAAgB;AACxE,eAAO,IAAI1C,MAAM,CAACkD,mBAAX,CAA+BX,SAA/B,EAA0CE,CAA1C,EAA6CC,CAA7C,CAAP;AACH,OAFD;;AAGA1C,MAAAA,MAAM,CAACoF,cAAP,CAAsB5E,SAAtB,CAAgCiF,yBAAhC,GAA4D,UAAUhD,CAAV,EAAaC,CAAb,EAAgB;AACxE,eAAO,IAAI1C,MAAM,CAACmD,mBAAX,CAA+BZ,SAA/B,EAA0CE,CAA1C,EAA6CC,CAA7C,CAAP;AACH,OAFD;;AAGA1C,MAAAA,MAAM,CAACoF,cAAP,CAAsB5E,SAAtB,CAAgCkF,+BAAhC,GAAkE,UAAUjD,CAAV,EAAaC,CAAb,EAAgBW,EAAhB,EAAoBC,EAApB,EAAwBC,EAAxB,EAA4BC,EAA5B,EAAgC;AAC9F,eAAO,IAAIxD,MAAM,CAACoD,yBAAX,CAAqCb,SAArC,EAAgDE,CAAhD,EAAmDC,CAAnD,EAAsDW,EAAtD,EAA0DC,EAA1D,EAA8DC,EAA9D,EAAkEC,EAAlE,CAAP;AACH,OAFD;;AAGAxD,MAAAA,MAAM,CAACoF,cAAP,CAAsB5E,SAAtB,CAAgCmF,+BAAhC,GAAkE,UAAUlD,CAAV,EAAaC,CAAb,EAAgBW,EAAhB,EAAoBC,EAApB,EAAwBC,EAAxB,EAA4BC,EAA5B,EAAgC;AAC9F,eAAO,IAAIxD,MAAM,CAAC6D,yBAAX,CAAqCtB,SAArC,EAAgDE,CAAhD,EAAmDC,CAAnD,EAAsDW,EAAtD,EAA0DC,EAA1D,EAA8DC,EAA9D,EAAkEC,EAAlE,CAAP;AACH,OAFD;;AAGAxD,MAAAA,MAAM,CAACoF,cAAP,CAAsB5E,SAAtB,CAAgCoF,mCAAhC,GAAsE,UAAUnD,CAAV,EAAaC,CAAb,EAAgBW,EAAhB,EAAoBC,EAApB,EAAwB;AAC1F,eAAO,IAAItD,MAAM,CAAC8D,6BAAX,CAAyCvB,SAAzC,EAAoDE,CAApD,EAAuDC,CAAvD,EAA0DW,EAA1D,EAA8DC,EAA9D,CAAP;AACH,OAFD;;AAGAtD,MAAAA,MAAM,CAACoF,cAAP,CAAsB5E,SAAtB,CAAgCqF,mCAAhC,GAAsE,UAAUpD,CAAV,EAAaC,CAAb,EAAgBW,EAAhB,EAAoBC,EAApB,EAAwB;AAC1F,eAAO,IAAItD,MAAM,CAAC+D,6BAAX,CAAyCxB,SAAzC,EAAoDE,CAApD,EAAuDC,CAAvD,EAA0DW,EAA1D,EAA8DC,EAA9D,CAAP;AACH,OAFD;;AAGAtD,MAAAA,MAAM,CAACoF,cAAP,CAAsB5E,SAAtB,CAAgCsF,sBAAhC,GAAyD,UAAUrD,CAAV,EAAaC,CAAb,EAAgBuB,EAAhB,EAAoBC,EAApB,EAAwBC,KAAxB,EAA+BC,YAA/B,EAA6CC,SAA7C,EAAwD;AAC7G,eAAO,IAAIrE,MAAM,CAACgE,gBAAX,CAA4BzB,SAA5B,EAAuCE,CAAvC,EAA0CC,CAA1C,EAA6CuB,EAA7C,EAAiDC,EAAjD,EAAqDC,KAArD,EAA4DC,YAA5D,EAA0EC,SAA1E,CAAP;AACH,OAFD;;AAGArE,MAAAA,MAAM,CAACoF,cAAP,CAAsB5E,SAAtB,CAAgCuF,sBAAhC,GAAyD,UAAUtD,CAAV,EAAaC,CAAb,EAAgBuB,EAAhB,EAAoBC,EAApB,EAAwBC,KAAxB,EAA+BC,YAA/B,EAA6CC,SAA7C,EAAwD;AAC7G,eAAO,IAAIrE,MAAM,CAAC2E,gBAAX,CAA4BpC,SAA5B,EAAuCE,CAAvC,EAA0CC,CAA1C,EAA6CuB,EAA7C,EAAiDC,EAAjD,EAAqDC,KAArD,EAA4DC,YAA5D,EAA0EC,SAA1E,CAAP;AACH,OAFD;;AAGArE,MAAAA,MAAM,CAACoF,cAAP,CAAsB5E,SAAtB,CAAgCwF,mCAAhC,GAAsE,UAAUvD,CAAV,EAAa;AAC/E,eAAO,IAAIzC,MAAM,CAAC4E,6BAAX,CAAyCrC,SAAzC,EAAoDE,CAApD,CAAP;AACH,OAFD;;AAGAzC,MAAAA,MAAM,CAACoF,cAAP,CAAsB5E,SAAtB,CAAgCyF,mCAAhC,GAAsE,UAAUxD,CAAV,EAAa;AAC/E,eAAO,IAAIzC,MAAM,CAAC6E,6BAAX,CAAyCtC,SAAzC,EAAoDE,CAApD,CAAP;AACH,OAFD;;AAGAzC,MAAAA,MAAM,CAACoF,cAAP,CAAsB5E,SAAtB,CAAgC0F,iCAAhC,GAAoE,UAAUxD,CAAV,EAAa;AAC7E,eAAO,IAAI1C,MAAM,CAAC8E,2BAAX,CAAuCvC,SAAvC,EAAkDG,CAAlD,CAAP;AACH,OAFD;;AAGA1C,MAAAA,MAAM,CAACoF,cAAP,CAAsB5E,SAAtB,CAAgC2F,iCAAhC,GAAoE,UAAUzD,CAAV,EAAa;AAC7E,eAAO,IAAI1C,MAAM,CAAC+E,2BAAX,CAAuCxC,SAAvC,EAAkDG,CAAlD,CAAP;AACH,OAFD;;AAGA1C,MAAAA,MAAM,CAACoF,cAAP,CAAsB5E,SAAtB,CAAgC4F,qCAAhC,GAAwE,UAAU3D,CAAV,EAAaC,CAAb,EAAgBa,EAAhB,EAAoBC,EAApB,EAAwB;AAC5F,eAAO,IAAIxD,MAAM,CAACgF,+BAAX,CAA2CzC,SAA3C,EAAsDE,CAAtD,EAAyDC,CAAzD,EAA4Da,EAA5D,EAAgEC,EAAhE,CAAP;AACH,OAFD;;AAGAxD,MAAAA,MAAM,CAACoF,cAAP,CAAsB5E,SAAtB,CAAgC6F,qCAAhC,GAAwE,UAAU5D,CAAV,EAAaC,CAAb,EAAgBa,EAAhB,EAAoBC,EAApB,EAAwB;AAC5F,eAAO,IAAIxD,MAAM,CAACiF,+BAAX,CAA2C1C,SAA3C,EAAsDE,CAAtD,EAAyDC,CAAzD,EAA4Da,EAA5D,EAAgEC,EAAhE,CAAP;AACH,OAFD;;AAGAxD,MAAAA,MAAM,CAACoF,cAAP,CAAsB5E,SAAtB,CAAgC8F,yCAAhC,GAA4E,UAAU7D,CAAV,EAAaC,CAAb,EAAgB;AACxF,eAAO,IAAI1C,MAAM,CAACkF,mCAAX,CAA+C3C,SAA/C,EAA0DE,CAA1D,EAA6DC,CAA7D,CAAP;AACH,OAFD;;AAGA1C,MAAAA,MAAM,CAACoF,cAAP,CAAsB5E,SAAtB,CAAgC+F,yCAAhC,GAA4E,UAAU9D,CAAV,EAAaC,CAAb,EAAgB;AACxF,eAAO,IAAI1C,MAAM,CAACmF,mCAAX,CAA+C5C,SAA/C,EAA0DE,CAA1D,EAA6DC,CAA7D,CAAP;AACH,OAFD;;AAGA,UAAI,EAAE,wBAAwB1C,MAAM,CAACoF,cAAP,CAAsB5E,SAAhD,CAAJ,EAAgE;AAC5DR,QAAAA,MAAM,CAACoF,cAAP,CAAsB5E,SAAtB,CAAgCgG,kBAAhC,GAAqD,UAAUC,QAAV,EAAoB;AACrE,cAAIA,QAAQ,KAAKlE,SAAb,IAA0B,CAACmE,QAAQ,CAACD,QAAD,CAAvC,EACI,MAAM,oBAAN;AACJ,gBAAME,kBAAkB,GAAGC,QAAQ,CAACC,eAAT,CAAyB,4BAAzB,EAAuD,MAAvD,CAA3B;AACAF,UAAAA,kBAAkB,CAACG,YAAnB,CAAgC,GAAhC,EAAqC,KAAKC,YAAL,CAAkB,GAAlB,CAArC;AACA,cAAIC,eAAe,GAAGL,kBAAkB,CAACM,WAAnB,CAA+BC,aAA/B,GAA+C,CAArE;AACA,cAAIF,eAAe,IAAI,CAAvB,EACI,OAAO,CAAP;;AACJ,aAAG;AACCL,YAAAA,kBAAkB,CAACM,WAAnB,CAA+BE,UAA/B,CAA0CH,eAA1C;AACA,gBAAIP,QAAQ,GAAGE,kBAAkB,CAACS,cAAnB,EAAf,EACI;AACJJ,YAAAA,eAAe;AAClB,WALD,QAKSA,eAAe,GAAG,CAL3B;;AAMA,iBAAOA,eAAP;AACH,SAfD;AAgBH;AACJ;;AACD,QAAI,EAAE,oBAAoBhH,MAAtB,KAAiC,EAAE,gBAAgBA,MAAM,CAACqH,cAAP,CAAsB7G,SAAxC,CAArC,EAAyF;AACrFR,MAAAA,MAAM,CAACqH,cAAP,GAAwB,UAAUC,WAAV,EAAuB;AAC3C,aAAKC,YAAL,GAAoBD,WAApB;AACA,aAAKE,KAAL,GAAa,KAAKC,UAAL,CAAgB,KAAKF,YAAL,CAAkBR,YAAlB,CAA+B,GAA/B,CAAhB,CAAb;AACA,aAAKW,uBAAL,GAA+B;AAAEC,UAAAA,UAAU,EAAE,IAAd;AAAoBC,UAAAA,eAAe,EAAE,CAAC,GAAD;AAArC,SAA/B;AACA,aAAKC,4BAAL,GAAoC,IAAIC,gBAAJ,CAAqB,KAAKC,4BAAL,CAAkCC,IAAlC,CAAuC,IAAvC,CAArB,CAApC;;AACA,aAAKH,4BAAL,CAAkCI,OAAlC,CAA0C,KAAKV,YAA/C,EAA6D,KAAKG,uBAAlE;AACH,OAND;;AAOA1H,MAAAA,MAAM,CAACqH,cAAP,CAAsB7G,SAAtB,CAAgCC,SAAhC,GAA4C,gBAA5C;AACAyB,MAAAA,MAAM,CAACW,cAAP,CAAsB7C,MAAM,CAACqH,cAAP,CAAsB7G,SAA5C,EAAuD,eAAvD,EAAwE;AACpEsC,QAAAA,GAAG,EAAE,YAAY;AACb,eAAKoF,4BAAL;;AACA,iBAAO,KAAKV,KAAL,CAAWW,MAAlB;AACH,SAJmE;AAKpEnF,QAAAA,UAAU,EAAE;AALwD,OAAxE;AAOAd,MAAAA,MAAM,CAACW,cAAP,CAAsB7C,MAAM,CAACqH,cAAP,CAAsB7G,SAA5C,EAAuD,QAAvD,EAAiE;AAC7DsC,QAAAA,GAAG,EAAE,YAAY;AACb,eAAKoF,4BAAL;;AACA,iBAAO,KAAKV,KAAL,CAAWW,MAAlB;AACH,SAJ4D;AAK7DnF,QAAAA,UAAU,EAAE;AALiD,OAAjE;AAOAd,MAAAA,MAAM,CAACW,cAAP,CAAsB7C,MAAM,CAACoF,cAAP,CAAsB5E,SAA5C,EAAuD,aAAvD,EAAsE;AAClEsC,QAAAA,GAAG,EAAE,YAAY;AACb,cAAI,CAAC,KAAKsF,YAAV,EACI,KAAKA,YAAL,GAAoB,IAAIpI,MAAM,CAACqH,cAAX,CAA0B,IAA1B,CAApB;AACJ,iBAAO,KAAKe,YAAZ;AACH,SALiE;AAMlEpF,QAAAA,UAAU,EAAE;AANsD,OAAtE;AAQAd,MAAAA,MAAM,CAACW,cAAP,CAAsB7C,MAAM,CAACoF,cAAP,CAAsB5E,SAA5C,EAAuD,uBAAvD,EAAgF;AAC5EsC,QAAAA,GAAG,EAAE,YAAY;AACb,iBAAO,KAAKmE,WAAZ;AACH,SAH2E;AAI5EjE,QAAAA,UAAU,EAAE;AAJgE,OAAhF;AAMAd,MAAAA,MAAM,CAACW,cAAP,CAAsB7C,MAAM,CAACoF,cAAP,CAAsB5E,SAA5C,EAAuD,qBAAvD,EAA8E;AAC1EsC,QAAAA,GAAG,EAAE,YAAY;AACb,iBAAO,KAAKmE,WAAZ;AACH,SAHyE;AAI1EjE,QAAAA,UAAU,EAAE;AAJ8D,OAA9E;AAMAd,MAAAA,MAAM,CAACW,cAAP,CAAsB7C,MAAM,CAACoF,cAAP,CAAsB5E,SAA5C,EAAuD,+BAAvD,EAAwF;AACpFsC,QAAAA,GAAG,EAAE,YAAY;AACb,iBAAO,KAAKmE,WAAZ;AACH,SAHmF;AAIpFjE,QAAAA,UAAU,EAAE;AAJwE,OAAxF;;AAMAhD,MAAAA,MAAM,CAACqH,cAAP,CAAsB7G,SAAtB,CAAgC0H,4BAAhC,GAA+D,YAAY;AACvE,aAAKH,4BAAL,CAAkC,KAAKF,4BAAL,CAAkCQ,WAAlC,EAAlC;AACH,OAFD;;AAGArI,MAAAA,MAAM,CAACqH,cAAP,CAAsB7G,SAAtB,CAAgCuH,4BAAhC,GAA+D,UAAUO,eAAV,EAA2B;AACtF,YAAI,CAAC,KAAKf,YAAV,EACI;AACJ,YAAIgB,gBAAgB,GAAG,KAAvB;AACAD,QAAAA,eAAe,CAACE,OAAhB,CAAwB,UAAUC,MAAV,EAAkB;AACtC,cAAIA,MAAM,CAACC,aAAP,IAAwB,GAA5B,EACIH,gBAAgB,GAAG,IAAnB;AACP,SAHD;AAIA,YAAIA,gBAAJ,EACI,KAAKf,KAAL,GAAa,KAAKC,UAAL,CAAgB,KAAKF,YAAL,CAAkBR,YAAlB,CAA+B,GAA/B,CAAhB,CAAb;AACP,OAVD;;AAWA/G,MAAAA,MAAM,CAACqH,cAAP,CAAsB7G,SAAtB,CAAgCmI,gBAAhC,GAAmD,YAAY;AAC3D,aAAKd,4BAAL,CAAkCe,UAAlC;;AACA,aAAKrB,YAAL,CAAkBT,YAAlB,CAA+B,GAA/B,EAAoC9G,MAAM,CAACqH,cAAP,CAAsBwB,qBAAtB,CAA4C,KAAKrB,KAAjD,CAApC;;AACA,aAAKK,4BAAL,CAAkCI,OAAlC,CAA0C,KAAKV,YAA/C,EAA6D,KAAKG,uBAAlE;AACH,OAJD;;AAKA1H,MAAAA,MAAM,CAACqH,cAAP,CAAsB7G,SAAtB,CAAgCuB,cAAhC,GAAiD,UAAU+G,OAAV,EAAmB;AAChE,aAAKH,gBAAL;AACH,OAFD;;AAGA3I,MAAAA,MAAM,CAACqH,cAAP,CAAsB7G,SAAtB,CAAgCuI,KAAhC,GAAwC,YAAY;AAChD,aAAKb,4BAAL;;AACA,aAAKV,KAAL,CAAWgB,OAAX,CAAmB,UAAUM,OAAV,EAAmB;AAClCA,UAAAA,OAAO,CAACvI,kBAAR,GAA6B,IAA7B;AACH,SAFD;;AAGA,aAAKiH,KAAL,GAAa,EAAb;;AACA,aAAKmB,gBAAL;AACH,OAPD;;AAQA3I,MAAAA,MAAM,CAACqH,cAAP,CAAsB7G,SAAtB,CAAgCwI,UAAhC,GAA6C,UAAUC,OAAV,EAAmB;AAC5D,aAAKf,4BAAL;;AACA,aAAKV,KAAL,GAAa,CAACyB,OAAD,CAAb;AACAA,QAAAA,OAAO,CAAC1I,kBAAR,GAA6B,IAA7B;;AACA,aAAKoI,gBAAL;;AACA,eAAOM,OAAP;AACH,OAND;;AAOAjJ,MAAAA,MAAM,CAACqH,cAAP,CAAsB7G,SAAtB,CAAgC0I,gBAAhC,GAAmD,UAAUC,KAAV,EAAiB;AAChE,YAAIC,KAAK,CAACD,KAAD,CAAL,IAAgBA,KAAK,GAAG,CAAxB,IAA6BA,KAAK,IAAI,KAAKjC,aAA/C,EACI,MAAM,gBAAN;AACP,OAHD;;AAIAlH,MAAAA,MAAM,CAACqH,cAAP,CAAsB7G,SAAtB,CAAgC6I,OAAhC,GAA0C,UAAUF,KAAV,EAAiB;AACvD,aAAKjB,4BAAL;;AACA,aAAKgB,gBAAL,CAAsBC,KAAtB;;AACA,eAAO,KAAK3B,KAAL,CAAW2B,KAAX,CAAP;AACH,OAJD;;AAKAnJ,MAAAA,MAAM,CAACqH,cAAP,CAAsB7G,SAAtB,CAAgC8I,gBAAhC,GAAmD,UAAUL,OAAV,EAAmBE,KAAnB,EAA0B;AACzE,aAAKjB,4BAAL;;AACA,YAAIiB,KAAK,GAAG,KAAKjC,aAAjB,EACIiC,KAAK,GAAG,KAAKjC,aAAb;;AACJ,YAAI+B,OAAO,CAAC1I,kBAAZ,EAAgC;AAC5B0I,UAAAA,OAAO,GAAGA,OAAO,CAAC3G,KAAR,EAAV;AACH;;AACD,aAAKkF,KAAL,CAAW+B,MAAX,CAAkBJ,KAAlB,EAAyB,CAAzB,EAA4BF,OAA5B;;AACAA,QAAAA,OAAO,CAAC1I,kBAAR,GAA6B,IAA7B;;AACA,aAAKoI,gBAAL;;AACA,eAAOM,OAAP;AACH,OAXD;;AAYAjJ,MAAAA,MAAM,CAACqH,cAAP,CAAsB7G,SAAtB,CAAgCgJ,WAAhC,GAA8C,UAAUP,OAAV,EAAmBE,KAAnB,EAA0B;AACpE,aAAKjB,4BAAL;;AACA,YAAIe,OAAO,CAAC1I,kBAAZ,EAAgC;AAC5B0I,UAAAA,OAAO,GAAGA,OAAO,CAAC3G,KAAR,EAAV;AACH;;AACD,aAAK4G,gBAAL,CAAsBC,KAAtB;;AACA,aAAK3B,KAAL,CAAW2B,KAAX,IAAoBF,OAApB;AACAA,QAAAA,OAAO,CAAC1I,kBAAR,GAA6B,IAA7B;;AACA,aAAKoI,gBAAL;;AACA,eAAOM,OAAP;AACH,OAVD;;AAWAjJ,MAAAA,MAAM,CAACqH,cAAP,CAAsB7G,SAAtB,CAAgC2G,UAAhC,GAA6C,UAAUgC,KAAV,EAAiB;AAC1D,aAAKjB,4BAAL;;AACA,aAAKgB,gBAAL,CAAsBC,KAAtB;;AACA,cAAMM,IAAI,GAAG,KAAKjC,KAAL,CAAW2B,KAAX,CAAb;;AACA,aAAK3B,KAAL,CAAW+B,MAAX,CAAkBJ,KAAlB,EAAyB,CAAzB;;AACA,aAAKR,gBAAL;;AACA,eAAOc,IAAP;AACH,OAPD;;AAQAzJ,MAAAA,MAAM,CAACqH,cAAP,CAAsB7G,SAAtB,CAAgCkJ,UAAhC,GAA6C,UAAUT,OAAV,EAAmB;AAC5D,aAAKf,4BAAL;;AACA,YAAIe,OAAO,CAAC1I,kBAAZ,EAAgC;AAC5B0I,UAAAA,OAAO,GAAGA,OAAO,CAAC3G,KAAR,EAAV;AACH;;AACD,aAAKkF,KAAL,CAAWmC,IAAX,CAAgBV,OAAhB;;AACAA,QAAAA,OAAO,CAAC1I,kBAAR,GAA6B,IAA7B;;AACA,aAAKoI,gBAAL;;AACA,eAAOM,OAAP;AACH,OATD;;AAUAjJ,MAAAA,MAAM,CAACqH,cAAP,CAAsBwB,qBAAtB,GAA8C,UAAUe,YAAV,EAAwB;AAClE,YAAIC,MAAM,GAAG,EAAb;AACA,YAAIC,KAAK,GAAG,IAAZ;AACAF,QAAAA,YAAY,CAACpB,OAAb,CAAqB,UAAUM,OAAV,EAAmB;AACpC,cAAIgB,KAAJ,EAAW;AACPA,YAAAA,KAAK,GAAG,KAAR;AACAD,YAAAA,MAAM,IAAIf,OAAO,CAACzG,aAAR,EAAV;AACH,WAHD,MAIK;AACDwH,YAAAA,MAAM,IAAI,MAAMf,OAAO,CAACzG,aAAR,EAAhB;AACH;AACJ,SARD;AASA,eAAOwH,MAAP;AACH,OAbD;;AAcA7J,MAAAA,MAAM,CAACqH,cAAP,CAAsB7G,SAAtB,CAAgCiH,UAAhC,GAA6C,UAAUoC,MAAV,EAAkB;AAC3D,YAAI,CAACA,MAAD,IAAWA,MAAM,CAAC1B,MAAP,IAAiB,CAAhC,EACI,OAAO,EAAP;AACJ,cAAM/H,iBAAiB,GAAG,IAA1B;;AACA,cAAM2J,OAAO,GAAG,YAAY;AACxB,eAAK9C,WAAL,GAAmB,EAAnB;AACH,SAFD;;AAGA8C,QAAAA,OAAO,CAACvJ,SAAR,CAAkBwJ,aAAlB,GAAkC,UAAUlB,OAAV,EAAmB;AACjD,eAAK7B,WAAL,CAAiB0C,IAAjB,CAAsBb,OAAtB;AACH,SAFD;;AAGA,cAAMmB,MAAM,GAAG,UAAUJ,MAAV,EAAkB;AAC7B,eAAKK,OAAL,GAAeL,MAAf;AACA,eAAKM,aAAL,GAAqB,CAArB;AACA,eAAKC,SAAL,GAAiB,KAAKF,OAAL,CAAa/B,MAA9B;AACA,eAAKkC,gBAAL,GAAwBrK,MAAM,CAACC,UAAP,CAAkBS,eAA1C;;AACA,eAAK4J,mBAAL;AACH,SAND;;AAOAL,QAAAA,MAAM,CAACzJ,SAAP,CAAiB+J,eAAjB,GAAmC,YAAY;AAC3C,gBAAMC,SAAS,GAAG,KAAKN,OAAL,CAAa,KAAKC,aAAlB,CAAlB;AACA,iBAAQK,SAAS,IAAI,GAAb,KACHA,SAAS,IAAI,GAAb,IACGA,SAAS,IAAI,IADhB,IAEGA,SAAS,IAAI,IAFhB,IAGGA,SAAS,IAAI,IAHhB,IAIGA,SAAS,IAAI,IALb,CAAR;AAMH,SARD;;AASAP,QAAAA,MAAM,CAACzJ,SAAP,CAAiB8J,mBAAjB,GAAuC,YAAY;AAC/C,iBAAO,KAAKH,aAAL,GAAqB,KAAKC,SAA1B,IAAuC,KAAKG,eAAL,EAA9C,EACI,KAAKJ,aAAL;;AACJ,iBAAO,KAAKA,aAAL,GAAqB,KAAKC,SAAjC;AACH,SAJD;;AAKAH,QAAAA,MAAM,CAACzJ,SAAP,CAAiBiK,8BAAjB,GAAkD,YAAY;AAC1D,cAAI,KAAKN,aAAL,GAAqB,KAAKC,SAA1B,IACA,CAAC,KAAKG,eAAL,EADD,IAEA,KAAKL,OAAL,CAAaQ,MAAb,CAAoB,KAAKP,aAAzB,KAA2C,GAF/C,EAGI,OAAO,KAAP;;AACJ,cAAI,KAAKG,mBAAL,EAAJ,EAAgC;AAC5B,gBAAI,KAAKH,aAAL,GAAqB,KAAKC,SAA1B,IAAuC,KAAKF,OAAL,CAAaQ,MAAb,CAAoB,KAAKP,aAAzB,KAA2C,GAAtF,EAA2F;AACvF,mBAAKA,aAAL;;AACA,mBAAKG,mBAAL;AACH;AACJ;;AACD,iBAAO,KAAKH,aAAL,GAAqB,KAAKC,SAAjC;AACH,SAZD;;AAaAH,QAAAA,MAAM,CAACzJ,SAAP,CAAiBmK,WAAjB,GAA+B,YAAY;AACvC,iBAAO,KAAKR,aAAL,GAAqB,KAAKC,SAAjC;AACH,SAFD;;AAGAH,QAAAA,MAAM,CAACzJ,SAAP,CAAiBoK,eAAjB,GAAmC,YAAY;AAC3C,gBAAMC,SAAS,GAAG,KAAKX,OAAL,CAAa,KAAKC,aAAlB,CAAlB;AACA,iBAAO,KAAKW,oBAAL,CAA0BD,SAA1B,CAAP;AACH,SAHD;;AAIAZ,QAAAA,MAAM,CAACzJ,SAAP,CAAiBsK,oBAAjB,GAAwC,UAAUD,SAAV,EAAqB;AACzD,kBAAQA,SAAR;AACI,iBAAK,GAAL;AACA,iBAAK,GAAL;AACI,qBAAO7K,MAAM,CAACC,UAAP,CAAkBU,iBAAzB;;AACJ,iBAAK,GAAL;AACI,qBAAOX,MAAM,CAACC,UAAP,CAAkBW,kBAAzB;;AACJ,iBAAK,GAAL;AACI,qBAAOZ,MAAM,CAACC,UAAP,CAAkBY,kBAAzB;;AACJ,iBAAK,GAAL;AACI,qBAAOb,MAAM,CAACC,UAAP,CAAkBa,kBAAzB;;AACJ,iBAAK,GAAL;AACI,qBAAOd,MAAM,CAACC,UAAP,CAAkBc,kBAAzB;;AACJ,iBAAK,GAAL;AACI,qBAAOf,MAAM,CAACC,UAAP,CAAkBe,yBAAzB;;AACJ,iBAAK,GAAL;AACI,qBAAOhB,MAAM,CAACC,UAAP,CAAkBgB,yBAAzB;;AACJ,iBAAK,GAAL;AACI,qBAAOjB,MAAM,CAACC,UAAP,CAAkBiB,6BAAzB;;AACJ,iBAAK,GAAL;AACI,qBAAOlB,MAAM,CAACC,UAAP,CAAkBkB,6BAAzB;;AACJ,iBAAK,GAAL;AACI,qBAAOnB,MAAM,CAACC,UAAP,CAAkBmB,eAAzB;;AACJ,iBAAK,GAAL;AACI,qBAAOpB,MAAM,CAACC,UAAP,CAAkBoB,eAAzB;;AACJ,iBAAK,GAAL;AACI,qBAAOrB,MAAM,CAACC,UAAP,CAAkBqB,6BAAzB;;AACJ,iBAAK,GAAL;AACI,qBAAOtB,MAAM,CAACC,UAAP,CAAkBsB,6BAAzB;;AACJ,iBAAK,GAAL;AACI,qBAAOvB,MAAM,CAACC,UAAP,CAAkBuB,2BAAzB;;AACJ,iBAAK,GAAL;AACI,qBAAOxB,MAAM,CAACC,UAAP,CAAkBwB,2BAAzB;;AACJ,iBAAK,GAAL;AACI,qBAAOzB,MAAM,CAACC,UAAP,CAAkByB,gCAAzB;;AACJ,iBAAK,GAAL;AACI,qBAAO1B,MAAM,CAACC,UAAP,CAAkB0B,gCAAzB;;AACJ,iBAAK,GAAL;AACI,qBAAO3B,MAAM,CAACC,UAAP,CAAkB2B,oCAAzB;;AACJ,iBAAK,GAAL;AACI,qBAAO5B,MAAM,CAACC,UAAP,CAAkB4B,oCAAzB;;AACJ;AACI,qBAAO7B,MAAM,CAACC,UAAP,CAAkBS,eAAzB;AAzCR;AA2CH,SA5CD;;AA6CAuJ,QAAAA,MAAM,CAACzJ,SAAP,CAAiBuK,kBAAjB,GAAsC,UAAUF,SAAV,EAAqBG,eAArB,EAAsC;AACxE,cAAI,CAACH,SAAS,IAAI,GAAb,IACDA,SAAS,IAAI,GADZ,IAEDA,SAAS,IAAI,GAFZ,IAGAA,SAAS,IAAI,GAAb,IAAoBA,SAAS,IAAI,GAHlC,KAIAG,eAAe,IAAIhL,MAAM,CAACC,UAAP,CAAkBU,iBAJzC,EAI4D;AACxD,gBAAIqK,eAAe,IAAIhL,MAAM,CAACC,UAAP,CAAkBW,kBAAzC,EACI,OAAOZ,MAAM,CAACC,UAAP,CAAkBa,kBAAzB;AACJ,gBAAIkK,eAAe,IAAIhL,MAAM,CAACC,UAAP,CAAkBY,kBAAzC,EACI,OAAOb,MAAM,CAACC,UAAP,CAAkBc,kBAAzB;AACJ,mBAAOiK,eAAP;AACH;;AACD,iBAAOhL,MAAM,CAACC,UAAP,CAAkBS,eAAzB;AACH,SAbD;;AAcAuJ,QAAAA,MAAM,CAACzJ,SAAP,CAAiByK,sBAAjB,GAA0C,YAAY;AAClD,cAAI,CAAC,KAAKN,WAAL,EAAL,EACI,OAAO,IAAP;AACJ,gBAAMO,OAAO,GAAG,KAAKN,eAAL,EAAhB;AACA,iBAAQM,OAAO,IAAIlL,MAAM,CAACC,UAAP,CAAkBW,kBAA7B,IACJsK,OAAO,IAAIlL,MAAM,CAACC,UAAP,CAAkBY,kBADjC;AAEH,SAND;;AAOAoJ,QAAAA,MAAM,CAACzJ,SAAP,CAAiB2K,YAAjB,GAAgC,YAAY;AACxC,cAAIC,QAAQ,GAAG,CAAf;AACA,cAAIC,OAAO,GAAG,CAAd;AACA,cAAIC,IAAI,GAAG,CAAX;AACA,cAAIC,OAAO,GAAG,CAAd;AACA,cAAIC,IAAI,GAAG,CAAX;AACA,cAAIC,OAAO,GAAG,CAAd;AACA,gBAAMC,UAAU,GAAG,KAAKvB,aAAxB;;AACA,eAAKG,mBAAL;;AACA,cAAI,KAAKH,aAAL,GAAqB,KAAKC,SAA1B,IAAuC,KAAKF,OAAL,CAAaQ,MAAb,CAAoB,KAAKP,aAAzB,KAA2C,GAAtF,EACI,KAAKA,aAAL,GADJ,KAEK,IAAI,KAAKA,aAAL,GAAqB,KAAKC,SAA1B,IAAuC,KAAKF,OAAL,CAAaQ,MAAb,CAAoB,KAAKP,aAAzB,KAA2C,GAAtF,EAA2F;AAC5F,iBAAKA,aAAL;AACAqB,YAAAA,IAAI,GAAG,CAAC,CAAR;AACH;AACD,cAAI,KAAKrB,aAAL,IAAsB,KAAKC,SAA3B,IACC,CAAC,KAAKF,OAAL,CAAaQ,MAAb,CAAoB,KAAKP,aAAzB,IAA0C,GAA1C,IACE,KAAKD,OAAL,CAAaQ,MAAb,CAAoB,KAAKP,aAAzB,IAA0C,GAD7C,KAEG,KAAKD,OAAL,CAAaQ,MAAb,CAAoB,KAAKP,aAAzB,KAA2C,GAHnD,EAII,OAAO5H,SAAP;AACJ,gBAAMoJ,iBAAiB,GAAG,KAAKxB,aAA/B;;AACA,iBAAO,KAAKA,aAAL,GAAqB,KAAKC,SAA1B,IACH,KAAKF,OAAL,CAAaQ,MAAb,CAAoB,KAAKP,aAAzB,KAA2C,GADxC,IAEH,KAAKD,OAAL,CAAaQ,MAAb,CAAoB,KAAKP,aAAzB,KAA2C,GAF/C,EAGI,KAAKA,aAAL;;AACJ,cAAI,KAAKA,aAAL,IAAsBwB,iBAA1B,EAA6C;AACzC,gBAAIC,gBAAgB,GAAG,KAAKzB,aAAL,GAAqB,CAA5C;AACA,gBAAI0B,UAAU,GAAG,CAAjB;;AACA,mBAAOD,gBAAgB,IAAID,iBAA3B,EAA8C;AAC1CN,cAAAA,OAAO,IAAIQ,UAAU,IAAI,KAAK3B,OAAL,CAAaQ,MAAb,CAAoBkB,gBAAgB,EAApC,IAA0C,GAA9C,CAArB;AACAC,cAAAA,UAAU,IAAI,EAAd;AACH;AACJ;;AACD,cAAI,KAAK1B,aAAL,GAAqB,KAAKC,SAA1B,IAAuC,KAAKF,OAAL,CAAaQ,MAAb,CAAoB,KAAKP,aAAzB,KAA2C,GAAtF,EAA2F;AACvF,iBAAKA,aAAL;AACA,gBAAI,KAAKA,aAAL,IAAsB,KAAKC,SAA3B,IACA,KAAKF,OAAL,CAAaQ,MAAb,CAAoB,KAAKP,aAAzB,IAA0C,GAD1C,IAEA,KAAKD,OAAL,CAAaQ,MAAb,CAAoB,KAAKP,aAAzB,IAA0C,GAF9C,EAGI,OAAO5H,SAAP;;AACJ,mBAAO,KAAK4H,aAAL,GAAqB,KAAKC,SAA1B,IACH,KAAKF,OAAL,CAAaQ,MAAb,CAAoB,KAAKP,aAAzB,KAA2C,GADxC,IAEH,KAAKD,OAAL,CAAaQ,MAAb,CAAoB,KAAKP,aAAzB,KAA2C,GAF/C,EAEoD;AAChDmB,cAAAA,IAAI,IAAI,EAAR;AACAC,cAAAA,OAAO,IAAI,CAAC,KAAKrB,OAAL,CAAaQ,MAAb,CAAoB,KAAKP,aAAzB,IAA0C,GAA3C,IAAkDmB,IAA7D;AACA,mBAAKnB,aAAL,IAAsB,CAAtB;AACH;AACJ;;AACD,cAAI,KAAKA,aAAL,IAAsBuB,UAAtB,IACA,KAAKvB,aAAL,GAAqB,CAArB,GAAyB,KAAKC,SAD9B,KAEC,KAAKF,OAAL,CAAaQ,MAAb,CAAoB,KAAKP,aAAzB,KAA2C,GAA3C,IACG,KAAKD,OAAL,CAAaQ,MAAb,CAAoB,KAAKP,aAAzB,KAA2C,GAH/C,KAIA,KAAKD,OAAL,CAAaQ,MAAb,CAAoB,KAAKP,aAAL,GAAqB,CAAzC,KAA+C,GAJ/C,IAKA,KAAKD,OAAL,CAAaQ,MAAb,CAAoB,KAAKP,aAAL,GAAqB,CAAzC,KAA+C,GALnD,EAKwD;AACpD,iBAAKA,aAAL;;AACA,gBAAI,KAAKD,OAAL,CAAaQ,MAAb,CAAoB,KAAKP,aAAzB,KAA2C,GAA/C,EAAoD;AAChD,mBAAKA,aAAL;AACH,aAFD,MAGK,IAAI,KAAKD,OAAL,CAAaQ,MAAb,CAAoB,KAAKP,aAAzB,KAA2C,GAA/C,EAAoD;AACrD,mBAAKA,aAAL;AACAsB,cAAAA,OAAO,GAAG,CAAC,CAAX;AACH;;AACD,gBAAI,KAAKtB,aAAL,IAAsB,KAAKC,SAA3B,IACA,KAAKF,OAAL,CAAaQ,MAAb,CAAoB,KAAKP,aAAzB,IAA0C,GAD1C,IAEA,KAAKD,OAAL,CAAaQ,MAAb,CAAoB,KAAKP,aAAzB,IAA0C,GAF9C,EAGI,OAAO5H,SAAP;;AACJ,mBAAO,KAAK4H,aAAL,GAAqB,KAAKC,SAA1B,IACH,KAAKF,OAAL,CAAaQ,MAAb,CAAoB,KAAKP,aAAzB,KAA2C,GADxC,IAEH,KAAKD,OAAL,CAAaQ,MAAb,CAAoB,KAAKP,aAAzB,KAA2C,GAF/C,EAEoD;AAChDiB,cAAAA,QAAQ,IAAI,EAAZ;AACAA,cAAAA,QAAQ,IAAI,KAAKlB,OAAL,CAAaQ,MAAb,CAAoB,KAAKP,aAAzB,IAA0C,GAAtD;AACA,mBAAKA,aAAL;AACH;AACJ;;AACD,cAAI2B,MAAM,GAAGT,OAAO,GAAGE,OAAvB;AACAO,UAAAA,MAAM,IAAIN,IAAV;AACA,cAAIJ,QAAJ,EACIU,MAAM,IAAIC,IAAI,CAACC,GAAL,CAAS,EAAT,EAAaP,OAAO,GAAGL,QAAvB,CAAV;AACJ,cAAIM,UAAU,IAAI,KAAKvB,aAAvB,EACI,OAAO5H,SAAP;;AACJ,eAAKkI,8BAAL;;AACA,iBAAOqB,MAAP;AACH,SAjFD;;AAkFA7B,QAAAA,MAAM,CAACzJ,SAAP,CAAiByL,aAAjB,GAAiC,YAAY;AACzC,cAAI,KAAK9B,aAAL,IAAsB,KAAKC,SAA/B,EACI,OAAO7H,SAAP;AACJ,cAAI2J,IAAI,GAAG,KAAX;;AACA,gBAAMC,QAAQ,GAAG,KAAKjC,OAAL,CAAaQ,MAAb,CAAoB,KAAKP,aAAL,EAApB,CAAjB;;AACA,cAAIgC,QAAQ,IAAI,GAAhB,EACID,IAAI,GAAG,KAAP,CADJ,KAEK,IAAIC,QAAQ,IAAI,GAAhB,EACDD,IAAI,GAAG,IAAP,CADC,KAGD,OAAO3J,SAAP;;AACJ,eAAKkI,8BAAL;;AACA,iBAAOyB,IAAP;AACH,SAbD;;AAcAjC,QAAAA,MAAM,CAACzJ,SAAP,CAAiB4L,YAAjB,GAAgC,YAAY;AACxC,gBAAMvB,SAAS,GAAG,KAAKX,OAAL,CAAa,KAAKC,aAAlB,CAAlB;;AACA,cAAIe,OAAO,GAAG,KAAKJ,oBAAL,CAA0BD,SAA1B,CAAd;;AACA,cAAIK,OAAO,IAAIlL,MAAM,CAACC,UAAP,CAAkBS,eAAjC,EAAkD;AAC9C,gBAAI,KAAK2J,gBAAL,IAAyBrK,MAAM,CAACC,UAAP,CAAkBS,eAA/C,EACI,OAAO,IAAP;AACJwK,YAAAA,OAAO,GAAG,KAAKH,kBAAL,CAAwBF,SAAxB,EAAmC,KAAKR,gBAAxC,CAAV;AACA,gBAAIa,OAAO,IAAIlL,MAAM,CAACC,UAAP,CAAkBS,eAAjC,EACI,OAAO,IAAP;AACP,WAND,MAOK;AACD,iBAAKyJ,aAAL;AACH;;AACD,eAAKE,gBAAL,GAAwBa,OAAxB;AACA,cAAImB,MAAJ;;AACA,kBAAQnB,OAAR;AACI,iBAAKlL,MAAM,CAACC,UAAP,CAAkBY,kBAAvB;AACI,qBAAO,IAAIb,MAAM,CAACiD,mBAAX,CAA+B7C,iBAA/B,EAAkD,KAAK+K,YAAL,EAAlD,EAAuE,KAAKA,YAAL,EAAvE,CAAP;;AACJ,iBAAKnL,MAAM,CAACC,UAAP,CAAkBW,kBAAvB;AACI,qBAAO,IAAIZ,MAAM,CAACwC,mBAAX,CAA+BpC,iBAA/B,EAAkD,KAAK+K,YAAL,EAAlD,EAAuE,KAAKA,YAAL,EAAvE,CAAP;;AACJ,iBAAKnL,MAAM,CAACC,UAAP,CAAkBc,kBAAvB;AACI,qBAAO,IAAIf,MAAM,CAACmD,mBAAX,CAA+B/C,iBAA/B,EAAkD,KAAK+K,YAAL,EAAlD,EAAuE,KAAKA,YAAL,EAAvE,CAAP;;AACJ,iBAAKnL,MAAM,CAACC,UAAP,CAAkBa,kBAAvB;AACI,qBAAO,IAAId,MAAM,CAACkD,mBAAX,CAA+B9C,iBAA/B,EAAkD,KAAK+K,YAAL,EAAlD,EAAuE,KAAKA,YAAL,EAAvE,CAAP;;AACJ,iBAAKnL,MAAM,CAACC,UAAP,CAAkBsB,6BAAvB;AACI,qBAAO,IAAIvB,MAAM,CAAC6E,6BAAX,CAAyCzE,iBAAzC,EAA4D,KAAK+K,YAAL,EAA5D,CAAP;;AACJ,iBAAKnL,MAAM,CAACC,UAAP,CAAkBqB,6BAAvB;AACI,qBAAO,IAAItB,MAAM,CAAC4E,6BAAX,CAAyCxE,iBAAzC,EAA4D,KAAK+K,YAAL,EAA5D,CAAP;;AACJ,iBAAKnL,MAAM,CAACC,UAAP,CAAkBwB,2BAAvB;AACI,qBAAO,IAAIzB,MAAM,CAAC+E,2BAAX,CAAuC3E,iBAAvC,EAA0D,KAAK+K,YAAL,EAA1D,CAAP;;AACJ,iBAAKnL,MAAM,CAACC,UAAP,CAAkBuB,2BAAvB;AACI,qBAAO,IAAIxB,MAAM,CAAC8E,2BAAX,CAAuC1E,iBAAvC,EAA0D,KAAK+K,YAAL,EAA1D,CAAP;;AACJ,iBAAKnL,MAAM,CAACC,UAAP,CAAkBU,iBAAvB;AACI,mBAAK2J,mBAAL;;AACA,qBAAO,IAAItK,MAAM,CAACgC,mBAAX,CAA+B5B,iBAA/B,CAAP;;AACJ,iBAAKJ,MAAM,CAACC,UAAP,CAAkBgB,yBAAvB;AACIoL,cAAAA,MAAM,GAAG;AACLhJ,gBAAAA,EAAE,EAAE,KAAK8H,YAAL,EADC;AAEL7H,gBAAAA,EAAE,EAAE,KAAK6H,YAAL,EAFC;AAGL5H,gBAAAA,EAAE,EAAE,KAAK4H,YAAL,EAHC;AAIL3H,gBAAAA,EAAE,EAAE,KAAK2H,YAAL,EAJC;AAKL1I,gBAAAA,CAAC,EAAE,KAAK0I,YAAL,EALE;AAMLzI,gBAAAA,CAAC,EAAE,KAAKyI,YAAL;AANE,eAAT;AAQA,qBAAO,IAAInL,MAAM,CAAC6D,yBAAX,CAAqCzD,iBAArC,EAAwDiM,MAAM,CAAC5J,CAA/D,EAAkE4J,MAAM,CAAC3J,CAAzE,EAA4E2J,MAAM,CAAChJ,EAAnF,EAAuFgJ,MAAM,CAAC/I,EAA9F,EAAkG+I,MAAM,CAAC9I,EAAzG,EAA6G8I,MAAM,CAAC7I,EAApH,CAAP;;AACJ,iBAAKxD,MAAM,CAACC,UAAP,CAAkBe,yBAAvB;AACIqL,cAAAA,MAAM,GAAG;AACLhJ,gBAAAA,EAAE,EAAE,KAAK8H,YAAL,EADC;AAEL7H,gBAAAA,EAAE,EAAE,KAAK6H,YAAL,EAFC;AAGL5H,gBAAAA,EAAE,EAAE,KAAK4H,YAAL,EAHC;AAIL3H,gBAAAA,EAAE,EAAE,KAAK2H,YAAL,EAJC;AAKL1I,gBAAAA,CAAC,EAAE,KAAK0I,YAAL,EALE;AAMLzI,gBAAAA,CAAC,EAAE,KAAKyI,YAAL;AANE,eAAT;AAQA,qBAAO,IAAInL,MAAM,CAACoD,yBAAX,CAAqChD,iBAArC,EAAwDiM,MAAM,CAAC5J,CAA/D,EAAkE4J,MAAM,CAAC3J,CAAzE,EAA4E2J,MAAM,CAAChJ,EAAnF,EAAuFgJ,MAAM,CAAC/I,EAA9F,EAAkG+I,MAAM,CAAC9I,EAAzG,EAA6G8I,MAAM,CAAC7I,EAApH,CAAP;;AACJ,iBAAKxD,MAAM,CAACC,UAAP,CAAkB0B,gCAAvB;AACI0K,cAAAA,MAAM,GAAG;AACL9I,gBAAAA,EAAE,EAAE,KAAK4H,YAAL,EADC;AAEL3H,gBAAAA,EAAE,EAAE,KAAK2H,YAAL,EAFC;AAGL1I,gBAAAA,CAAC,EAAE,KAAK0I,YAAL,EAHE;AAILzI,gBAAAA,CAAC,EAAE,KAAKyI,YAAL;AAJE,eAAT;AAMA,qBAAO,IAAInL,MAAM,CAACiF,+BAAX,CAA2C7E,iBAA3C,EAA8DiM,MAAM,CAAC5J,CAArE,EAAwE4J,MAAM,CAAC3J,CAA/E,EAAkF2J,MAAM,CAAC9I,EAAzF,EAA6F8I,MAAM,CAAC7I,EAApG,CAAP;;AACJ,iBAAKxD,MAAM,CAACC,UAAP,CAAkByB,gCAAvB;AACI2K,cAAAA,MAAM,GAAG;AACL9I,gBAAAA,EAAE,EAAE,KAAK4H,YAAL,EADC;AAEL3H,gBAAAA,EAAE,EAAE,KAAK2H,YAAL,EAFC;AAGL1I,gBAAAA,CAAC,EAAE,KAAK0I,YAAL,EAHE;AAILzI,gBAAAA,CAAC,EAAE,KAAKyI,YAAL;AAJE,eAAT;AAMA,qBAAO,IAAInL,MAAM,CAACgF,+BAAX,CAA2C5E,iBAA3C,EAA8DiM,MAAM,CAAC5J,CAArE,EAAwE4J,MAAM,CAAC3J,CAA/E,EAAkF2J,MAAM,CAAC9I,EAAzF,EAA6F8I,MAAM,CAAC7I,EAApG,CAAP;;AACJ,iBAAKxD,MAAM,CAACC,UAAP,CAAkBkB,6BAAvB;AACIkL,cAAAA,MAAM,GAAG;AACLhJ,gBAAAA,EAAE,EAAE,KAAK8H,YAAL,EADC;AAEL7H,gBAAAA,EAAE,EAAE,KAAK6H,YAAL,EAFC;AAGL1I,gBAAAA,CAAC,EAAE,KAAK0I,YAAL,EAHE;AAILzI,gBAAAA,CAAC,EAAE,KAAKyI,YAAL;AAJE,eAAT;AAMA,qBAAO,IAAInL,MAAM,CAAC+D,6BAAX,CAAyC3D,iBAAzC,EAA4DiM,MAAM,CAAC5J,CAAnE,EAAsE4J,MAAM,CAAC3J,CAA7E,EAAgF2J,MAAM,CAAChJ,EAAvF,EAA2FgJ,MAAM,CAAC/I,EAAlG,CAAP;;AACJ,iBAAKtD,MAAM,CAACC,UAAP,CAAkBiB,6BAAvB;AACImL,cAAAA,MAAM,GAAG;AACLhJ,gBAAAA,EAAE,EAAE,KAAK8H,YAAL,EADC;AAEL7H,gBAAAA,EAAE,EAAE,KAAK6H,YAAL,EAFC;AAGL1I,gBAAAA,CAAC,EAAE,KAAK0I,YAAL,EAHE;AAILzI,gBAAAA,CAAC,EAAE,KAAKyI,YAAL;AAJE,eAAT;AAMA,qBAAO,IAAInL,MAAM,CAAC8D,6BAAX,CAAyC1D,iBAAzC,EAA4DiM,MAAM,CAAC5J,CAAnE,EAAsE4J,MAAM,CAAC3J,CAA7E,EAAgF2J,MAAM,CAAChJ,EAAvF,EAA2FgJ,MAAM,CAAC/I,EAAlG,CAAP;;AACJ,iBAAKtD,MAAM,CAACC,UAAP,CAAkB4B,oCAAvB;AACI,qBAAO,IAAI7B,MAAM,CAACmF,mCAAX,CAA+C/E,iBAA/C,EAAkE,KAAK+K,YAAL,EAAlE,EAAuF,KAAKA,YAAL,EAAvF,CAAP;;AACJ,iBAAKnL,MAAM,CAACC,UAAP,CAAkB2B,oCAAvB;AACI,qBAAO,IAAI5B,MAAM,CAACkF,mCAAX,CAA+C9E,iBAA/C,EAAkE,KAAK+K,YAAL,EAAlE,EAAuF,KAAKA,YAAL,EAAvF,CAAP;;AACJ,iBAAKnL,MAAM,CAACC,UAAP,CAAkBoB,eAAvB;AACIgL,cAAAA,MAAM,GAAG;AACLhJ,gBAAAA,EAAE,EAAE,KAAK8H,YAAL,EADC;AAEL7H,gBAAAA,EAAE,EAAE,KAAK6H,YAAL,EAFC;AAGLmB,gBAAAA,QAAQ,EAAE,KAAKnB,YAAL,EAHL;AAILoB,gBAAAA,QAAQ,EAAE,KAAKN,aAAL,EAJL;AAKLO,gBAAAA,QAAQ,EAAE,KAAKP,aAAL,EALL;AAMLxJ,gBAAAA,CAAC,EAAE,KAAK0I,YAAL,EANE;AAOLzI,gBAAAA,CAAC,EAAE,KAAKyI,YAAL;AAPE,eAAT;AASA,qBAAO,IAAInL,MAAM,CAAC2E,gBAAX,CAA4BvE,iBAA5B,EAA+CiM,MAAM,CAAC5J,CAAtD,EAAyD4J,MAAM,CAAC3J,CAAhE,EAAmE2J,MAAM,CAAChJ,EAA1E,EAA8EgJ,MAAM,CAAC/I,EAArF,EAAyF+I,MAAM,CAACC,QAAhG,EAA0GD,MAAM,CAACE,QAAjH,EAA2HF,MAAM,CAACG,QAAlI,CAAP;;AACJ,iBAAKxM,MAAM,CAACC,UAAP,CAAkBmB,eAAvB;AACIiL,cAAAA,MAAM,GAAG;AACLhJ,gBAAAA,EAAE,EAAE,KAAK8H,YAAL,EADC;AAEL7H,gBAAAA,EAAE,EAAE,KAAK6H,YAAL,EAFC;AAGLmB,gBAAAA,QAAQ,EAAE,KAAKnB,YAAL,EAHL;AAILoB,gBAAAA,QAAQ,EAAE,KAAKN,aAAL,EAJL;AAKLO,gBAAAA,QAAQ,EAAE,KAAKP,aAAL,EALL;AAMLxJ,gBAAAA,CAAC,EAAE,KAAK0I,YAAL,EANE;AAOLzI,gBAAAA,CAAC,EAAE,KAAKyI,YAAL;AAPE,eAAT;AASA,qBAAO,IAAInL,MAAM,CAACgE,gBAAX,CAA4B5D,iBAA5B,EAA+CiM,MAAM,CAAC5J,CAAtD,EAAyD4J,MAAM,CAAC3J,CAAhE,EAAmE2J,MAAM,CAAChJ,EAA1E,EAA8EgJ,MAAM,CAAC/I,EAArF,EAAyF+I,MAAM,CAACC,QAAhG,EAA0GD,MAAM,CAACE,QAAjH,EAA2HF,MAAM,CAACG,QAAlI,CAAP;;AACJ;AACI,oBAAM,wBAAN;AAnGR;AAqGH,SApHD;;AAqHA,cAAMC,OAAO,GAAG,IAAI1C,OAAJ,EAAhB;AACA,cAAM2C,MAAM,GAAG,IAAIzC,MAAJ,CAAWJ,MAAX,CAAf;AACA,YAAI,CAAC6C,MAAM,CAACzB,sBAAP,EAAL,EACI,OAAO,EAAP;;AACJ,eAAOyB,MAAM,CAAC/B,WAAP,EAAP,EAA6B;AACzB,gBAAM7B,OAAO,GAAG4D,MAAM,CAACN,YAAP,EAAhB;AACA,cAAI,CAACtD,OAAL,EACI,OAAO,EAAP;AACJ2D,UAAAA,OAAO,CAACzC,aAAR,CAAsBlB,OAAtB;AACH;;AACD,eAAO2D,OAAO,CAACxF,WAAf;AACH,OArVD;AAsVH;AACJ,GApgDD,CAqgDA,OAAO0F,CAAP,EAAU;AACNC,IAAAA,OAAO,CAACC,IAAR,CAAa,8JAAb,EAA6KF,CAA7K;AACH;AACJ,CA1gDD", "sourcesContent": ["\"use strict\";\n(function () {\n    \"use strict\";\n    try {\n        if (typeof window === \"undefined\")\n            return;\n        if (!(\"SVGPathSeg\" in window)) {\n            window.SVGPathSeg = function (type, typeAsLetter, owningPathSegList) {\n                this.pathSegType = type;\n                this.pathSegTypeAsLetter = typeAsLetter;\n                this._owningPathSegList = owningPathSegList;\n            };\n            window.SVGPathSeg.prototype.classname = \"SVGPathSeg\";\n            window.SVGPathSeg.PATHSEG_UNKNOWN = 0;\n            window.SVGPathSeg.PATHSEG_CLOSEPATH = 1;\n            window.SVGPathSeg.PATHSEG_MOVETO_ABS = 2;\n            window.SVGPathSeg.PATHSEG_MOVETO_REL = 3;\n            window.SVGPathSeg.PATHSEG_LINETO_ABS = 4;\n            window.SVGPathSeg.PATHSEG_LINETO_REL = 5;\n            window.SVGPathSeg.PATHSEG_CURVETO_CUBIC_ABS = 6;\n            window.SVGPathSeg.PATHSEG_CURVETO_CUBIC_REL = 7;\n            window.SVGPathSeg.PATHSEG_CURVETO_QUADRATIC_ABS = 8;\n            window.SVGPathSeg.PATHSEG_CURVETO_QUADRATIC_REL = 9;\n            window.SVGPathSeg.PATHSEG_ARC_ABS = 10;\n            window.SVGPathSeg.PATHSEG_ARC_REL = 11;\n            window.SVGPathSeg.PATHSEG_LINETO_HORIZONTAL_ABS = 12;\n            window.SVGPathSeg.PATHSEG_LINETO_HORIZONTAL_REL = 13;\n            window.SVGPathSeg.PATHSEG_LINETO_VERTICAL_ABS = 14;\n            window.SVGPathSeg.PATHSEG_LINETO_VERTICAL_REL = 15;\n            window.SVGPathSeg.PATHSEG_CURVETO_CUBIC_SMOOTH_ABS = 16;\n            window.SVGPathSeg.PATHSEG_CURVETO_CUBIC_SMOOTH_REL = 17;\n            window.SVGPathSeg.PATHSEG_CURVETO_QUADRATIC_SMOOTH_ABS = 18;\n            window.SVGPathSeg.PATHSEG_CURVETO_QUADRATIC_SMOOTH_REL = 19;\n            window.SVGPathSeg.prototype._segmentChanged = function () {\n                if (this._owningPathSegList)\n                    this._owningPathSegList.segmentChanged(this);\n            };\n            window.SVGPathSegClosePath = function (owningPathSegList) {\n                window.SVGPathSeg.call(this, window.SVGPathSeg.PATHSEG_CLOSEPATH, \"z\", owningPathSegList);\n            };\n            window.SVGPathSegClosePath.prototype = Object.create(window.SVGPathSeg.prototype);\n            window.SVGPathSegClosePath.prototype.toString = function () {\n                return \"[object SVGPathSegClosePath]\";\n            };\n            window.SVGPathSegClosePath.prototype._asPathString = function () {\n                return this.pathSegTypeAsLetter;\n            };\n            window.SVGPathSegClosePath.prototype.clone = function () {\n                return new window.SVGPathSegClosePath(undefined);\n            };\n            window.SVGPathSegMovetoAbs = function (owningPathSegList, x, y) {\n                window.SVGPathSeg.call(this, window.SVGPathSeg.PATHSEG_MOVETO_ABS, \"M\", owningPathSegList);\n                this._x = x;\n                this._y = y;\n            };\n            window.SVGPathSegMovetoAbs.prototype = Object.create(window.SVGPathSeg.prototype);\n            window.SVGPathSegMovetoAbs.prototype.toString = function () {\n                return \"[object SVGPathSegMovetoAbs]\";\n            };\n            window.SVGPathSegMovetoAbs.prototype._asPathString = function () {\n                return this.pathSegTypeAsLetter + \" \" + this._x + \" \" + this._y;\n            };\n            window.SVGPathSegMovetoAbs.prototype.clone = function () {\n                return new window.SVGPathSegMovetoAbs(undefined, this._x, this._y);\n            };\n            Object.defineProperty(window.SVGPathSegMovetoAbs.prototype, \"x\", {\n                get: function () {\n                    return this._x;\n                },\n                set: function (x) {\n                    this._x = x;\n                    this._segmentChanged();\n                },\n                enumerable: true,\n            });\n            Object.defineProperty(window.SVGPathSegMovetoAbs.prototype, \"y\", {\n                get: function () {\n                    return this._y;\n                },\n                set: function (y) {\n                    this._y = y;\n                    this._segmentChanged();\n                },\n                enumerable: true,\n            });\n            window.SVGPathSegMovetoRel = function (owningPathSegList, x, y) {\n                window.SVGPathSeg.call(this, window.SVGPathSeg.PATHSEG_MOVETO_REL, \"m\", owningPathSegList);\n                this._x = x;\n                this._y = y;\n            };\n            window.SVGPathSegMovetoRel.prototype = Object.create(window.SVGPathSeg.prototype);\n            window.SVGPathSegMovetoRel.prototype.toString = function () {\n                return \"[object SVGPathSegMovetoRel]\";\n            };\n            window.SVGPathSegMovetoRel.prototype._asPathString = function () {\n                return this.pathSegTypeAsLetter + \" \" + this._x + \" \" + this._y;\n            };\n            window.SVGPathSegMovetoRel.prototype.clone = function () {\n                return new window.SVGPathSegMovetoRel(undefined, this._x, this._y);\n            };\n            Object.defineProperty(window.SVGPathSegMovetoRel.prototype, \"x\", {\n                get: function () {\n                    return this._x;\n                },\n                set: function (x) {\n                    this._x = x;\n                    this._segmentChanged();\n                },\n                enumerable: true,\n            });\n            Object.defineProperty(window.SVGPathSegMovetoRel.prototype, \"y\", {\n                get: function () {\n                    return this._y;\n                },\n                set: function (y) {\n                    this._y = y;\n                    this._segmentChanged();\n                },\n                enumerable: true,\n            });\n            window.SVGPathSegLinetoAbs = function (owningPathSegList, x, y) {\n                window.SVGPathSeg.call(this, window.SVGPathSeg.PATHSEG_LINETO_ABS, \"L\", owningPathSegList);\n                this._x = x;\n                this._y = y;\n            };\n            window.SVGPathSegLinetoAbs.prototype = Object.create(window.SVGPathSeg.prototype);\n            window.SVGPathSegLinetoAbs.prototype.toString = function () {\n                return \"[object SVGPathSegLinetoAbs]\";\n            };\n            window.SVGPathSegLinetoAbs.prototype._asPathString = function () {\n                return this.pathSegTypeAsLetter + \" \" + this._x + \" \" + this._y;\n            };\n            window.SVGPathSegLinetoAbs.prototype.clone = function () {\n                return new window.SVGPathSegLinetoAbs(undefined, this._x, this._y);\n            };\n            Object.defineProperty(window.SVGPathSegLinetoAbs.prototype, \"x\", {\n                get: function () {\n                    return this._x;\n                },\n                set: function (x) {\n                    this._x = x;\n                    this._segmentChanged();\n                },\n                enumerable: true,\n            });\n            Object.defineProperty(window.SVGPathSegLinetoAbs.prototype, \"y\", {\n                get: function () {\n                    return this._y;\n                },\n                set: function (y) {\n                    this._y = y;\n                    this._segmentChanged();\n                },\n                enumerable: true,\n            });\n            window.SVGPathSegLinetoRel = function (owningPathSegList, x, y) {\n                window.SVGPathSeg.call(this, window.SVGPathSeg.PATHSEG_LINETO_REL, \"l\", owningPathSegList);\n                this._x = x;\n                this._y = y;\n            };\n            window.SVGPathSegLinetoRel.prototype = Object.create(window.SVGPathSeg.prototype);\n            window.SVGPathSegLinetoRel.prototype.toString = function () {\n                return \"[object SVGPathSegLinetoRel]\";\n            };\n            window.SVGPathSegLinetoRel.prototype._asPathString = function () {\n                return this.pathSegTypeAsLetter + \" \" + this._x + \" \" + this._y;\n            };\n            window.SVGPathSegLinetoRel.prototype.clone = function () {\n                return new window.SVGPathSegLinetoRel(undefined, this._x, this._y);\n            };\n            Object.defineProperty(window.SVGPathSegLinetoRel.prototype, \"x\", {\n                get: function () {\n                    return this._x;\n                },\n                set: function (x) {\n                    this._x = x;\n                    this._segmentChanged();\n                },\n                enumerable: true,\n            });\n            Object.defineProperty(window.SVGPathSegLinetoRel.prototype, \"y\", {\n                get: function () {\n                    return this._y;\n                },\n                set: function (y) {\n                    this._y = y;\n                    this._segmentChanged();\n                },\n                enumerable: true,\n            });\n            window.SVGPathSegCurvetoCubicAbs = function (owningPathSegList, x, y, x1, y1, x2, y2) {\n                window.SVGPathSeg.call(this, window.SVGPathSeg.PATHSEG_CURVETO_CUBIC_ABS, \"C\", owningPathSegList);\n                this._x = x;\n                this._y = y;\n                this._x1 = x1;\n                this._y1 = y1;\n                this._x2 = x2;\n                this._y2 = y2;\n            };\n            window.SVGPathSegCurvetoCubicAbs.prototype = Object.create(window.SVGPathSeg.prototype);\n            window.SVGPathSegCurvetoCubicAbs.prototype.toString = function () {\n                return \"[object SVGPathSegCurvetoCubicAbs]\";\n            };\n            window.SVGPathSegCurvetoCubicAbs.prototype._asPathString = function () {\n                return (this.pathSegTypeAsLetter +\n                    \" \" +\n                    this._x1 +\n                    \" \" +\n                    this._y1 +\n                    \" \" +\n                    this._x2 +\n                    \" \" +\n                    this._y2 +\n                    \" \" +\n                    this._x +\n                    \" \" +\n                    this._y);\n            };\n            window.SVGPathSegCurvetoCubicAbs.prototype.clone = function () {\n                return new window.SVGPathSegCurvetoCubicAbs(undefined, this._x, this._y, this._x1, this._y1, this._x2, this._y2);\n            };\n            Object.defineProperty(window.SVGPathSegCurvetoCubicAbs.prototype, \"x\", {\n                get: function () {\n                    return this._x;\n                },\n                set: function (x) {\n                    this._x = x;\n                    this._segmentChanged();\n                },\n                enumerable: true,\n            });\n            Object.defineProperty(window.SVGPathSegCurvetoCubicAbs.prototype, \"y\", {\n                get: function () {\n                    return this._y;\n                },\n                set: function (y) {\n                    this._y = y;\n                    this._segmentChanged();\n                },\n                enumerable: true,\n            });\n            Object.defineProperty(window.SVGPathSegCurvetoCubicAbs.prototype, \"x1\", {\n                get: function () {\n                    return this._x1;\n                },\n                set: function (x1) {\n                    this._x1 = x1;\n                    this._segmentChanged();\n                },\n                enumerable: true,\n            });\n            Object.defineProperty(window.SVGPathSegCurvetoCubicAbs.prototype, \"y1\", {\n                get: function () {\n                    return this._y1;\n                },\n                set: function (y1) {\n                    this._y1 = y1;\n                    this._segmentChanged();\n                },\n                enumerable: true,\n            });\n            Object.defineProperty(window.SVGPathSegCurvetoCubicAbs.prototype, \"x2\", {\n                get: function () {\n                    return this._x2;\n                },\n                set: function (x2) {\n                    this._x2 = x2;\n                    this._segmentChanged();\n                },\n                enumerable: true,\n            });\n            Object.defineProperty(window.SVGPathSegCurvetoCubicAbs.prototype, \"y2\", {\n                get: function () {\n                    return this._y2;\n                },\n                set: function (y2) {\n                    this._y2 = y2;\n                    this._segmentChanged();\n                },\n                enumerable: true,\n            });\n            window.SVGPathSegCurvetoCubicRel = function (owningPathSegList, x, y, x1, y1, x2, y2) {\n                window.SVGPathSeg.call(this, window.SVGPathSeg.PATHSEG_CURVETO_CUBIC_REL, \"c\", owningPathSegList);\n                this._x = x;\n                this._y = y;\n                this._x1 = x1;\n                this._y1 = y1;\n                this._x2 = x2;\n                this._y2 = y2;\n            };\n            window.SVGPathSegCurvetoCubicRel.prototype = Object.create(window.SVGPathSeg.prototype);\n            window.SVGPathSegCurvetoCubicRel.prototype.toString = function () {\n                return \"[object SVGPathSegCurvetoCubicRel]\";\n            };\n            window.SVGPathSegCurvetoCubicRel.prototype._asPathString = function () {\n                return (this.pathSegTypeAsLetter +\n                    \" \" +\n                    this._x1 +\n                    \" \" +\n                    this._y1 +\n                    \" \" +\n                    this._x2 +\n                    \" \" +\n                    this._y2 +\n                    \" \" +\n                    this._x +\n                    \" \" +\n                    this._y);\n            };\n            window.SVGPathSegCurvetoCubicRel.prototype.clone = function () {\n                return new window.SVGPathSegCurvetoCubicRel(undefined, this._x, this._y, this._x1, this._y1, this._x2, this._y2);\n            };\n            Object.defineProperty(window.SVGPathSegCurvetoCubicRel.prototype, \"x\", {\n                get: function () {\n                    return this._x;\n                },\n                set: function (x) {\n                    this._x = x;\n                    this._segmentChanged();\n                },\n                enumerable: true,\n            });\n            Object.defineProperty(window.SVGPathSegCurvetoCubicRel.prototype, \"y\", {\n                get: function () {\n                    return this._y;\n                },\n                set: function (y) {\n                    this._y = y;\n                    this._segmentChanged();\n                },\n                enumerable: true,\n            });\n            Object.defineProperty(window.SVGPathSegCurvetoCubicRel.prototype, \"x1\", {\n                get: function () {\n                    return this._x1;\n                },\n                set: function (x1) {\n                    this._x1 = x1;\n                    this._segmentChanged();\n                },\n                enumerable: true,\n            });\n            Object.defineProperty(window.SVGPathSegCurvetoCubicRel.prototype, \"y1\", {\n                get: function () {\n                    return this._y1;\n                },\n                set: function (y1) {\n                    this._y1 = y1;\n                    this._segmentChanged();\n                },\n                enumerable: true,\n            });\n            Object.defineProperty(window.SVGPathSegCurvetoCubicRel.prototype, \"x2\", {\n                get: function () {\n                    return this._x2;\n                },\n                set: function (x2) {\n                    this._x2 = x2;\n                    this._segmentChanged();\n                },\n                enumerable: true,\n            });\n            Object.defineProperty(window.SVGPathSegCurvetoCubicRel.prototype, \"y2\", {\n                get: function () {\n                    return this._y2;\n                },\n                set: function (y2) {\n                    this._y2 = y2;\n                    this._segmentChanged();\n                },\n                enumerable: true,\n            });\n            window.SVGPathSegCurvetoQuadraticAbs = function (owningPathSegList, x, y, x1, y1) {\n                window.SVGPathSeg.call(this, window.SVGPathSeg.PATHSEG_CURVETO_QUADRATIC_ABS, \"Q\", owningPathSegList);\n                this._x = x;\n                this._y = y;\n                this._x1 = x1;\n                this._y1 = y1;\n            };\n            window.SVGPathSegCurvetoQuadraticAbs.prototype = Object.create(window.SVGPathSeg.prototype);\n            window.SVGPathSegCurvetoQuadraticAbs.prototype.toString = function () {\n                return \"[object SVGPathSegCurvetoQuadraticAbs]\";\n            };\n            window.SVGPathSegCurvetoQuadraticAbs.prototype._asPathString = function () {\n                return this.pathSegTypeAsLetter + \" \" + this._x1 + \" \" + this._y1 + \" \" + this._x + \" \" + this._y;\n            };\n            window.SVGPathSegCurvetoQuadraticAbs.prototype.clone = function () {\n                return new window.SVGPathSegCurvetoQuadraticAbs(undefined, this._x, this._y, this._x1, this._y1);\n            };\n            Object.defineProperty(window.SVGPathSegCurvetoQuadraticAbs.prototype, \"x\", {\n                get: function () {\n                    return this._x;\n                },\n                set: function (x) {\n                    this._x = x;\n                    this._segmentChanged();\n                },\n                enumerable: true,\n            });\n            Object.defineProperty(window.SVGPathSegCurvetoQuadraticAbs.prototype, \"y\", {\n                get: function () {\n                    return this._y;\n                },\n                set: function (y) {\n                    this._y = y;\n                    this._segmentChanged();\n                },\n                enumerable: true,\n            });\n            Object.defineProperty(window.SVGPathSegCurvetoQuadraticAbs.prototype, \"x1\", {\n                get: function () {\n                    return this._x1;\n                },\n                set: function (x1) {\n                    this._x1 = x1;\n                    this._segmentChanged();\n                },\n                enumerable: true,\n            });\n            Object.defineProperty(window.SVGPathSegCurvetoQuadraticAbs.prototype, \"y1\", {\n                get: function () {\n                    return this._y1;\n                },\n                set: function (y1) {\n                    this._y1 = y1;\n                    this._segmentChanged();\n                },\n                enumerable: true,\n            });\n            window.SVGPathSegCurvetoQuadraticRel = function (owningPathSegList, x, y, x1, y1) {\n                window.SVGPathSeg.call(this, window.SVGPathSeg.PATHSEG_CURVETO_QUADRATIC_REL, \"q\", owningPathSegList);\n                this._x = x;\n                this._y = y;\n                this._x1 = x1;\n                this._y1 = y1;\n            };\n            window.SVGPathSegCurvetoQuadraticRel.prototype = Object.create(window.SVGPathSeg.prototype);\n            window.SVGPathSegCurvetoQuadraticRel.prototype.toString = function () {\n                return \"[object SVGPathSegCurvetoQuadraticRel]\";\n            };\n            window.SVGPathSegCurvetoQuadraticRel.prototype._asPathString = function () {\n                return this.pathSegTypeAsLetter + \" \" + this._x1 + \" \" + this._y1 + \" \" + this._x + \" \" + this._y;\n            };\n            window.SVGPathSegCurvetoQuadraticRel.prototype.clone = function () {\n                return new window.SVGPathSegCurvetoQuadraticRel(undefined, this._x, this._y, this._x1, this._y1);\n            };\n            Object.defineProperty(window.SVGPathSegCurvetoQuadraticRel.prototype, \"x\", {\n                get: function () {\n                    return this._x;\n                },\n                set: function (x) {\n                    this._x = x;\n                    this._segmentChanged();\n                },\n                enumerable: true,\n            });\n            Object.defineProperty(window.SVGPathSegCurvetoQuadraticRel.prototype, \"y\", {\n                get: function () {\n                    return this._y;\n                },\n                set: function (y) {\n                    this._y = y;\n                    this._segmentChanged();\n                },\n                enumerable: true,\n            });\n            Object.defineProperty(window.SVGPathSegCurvetoQuadraticRel.prototype, \"x1\", {\n                get: function () {\n                    return this._x1;\n                },\n                set: function (x1) {\n                    this._x1 = x1;\n                    this._segmentChanged();\n                },\n                enumerable: true,\n            });\n            Object.defineProperty(window.SVGPathSegCurvetoQuadraticRel.prototype, \"y1\", {\n                get: function () {\n                    return this._y1;\n                },\n                set: function (y1) {\n                    this._y1 = y1;\n                    this._segmentChanged();\n                },\n                enumerable: true,\n            });\n            window.SVGPathSegArcAbs = function (owningPathSegList, x, y, r1, r2, angle, largeArcFlag, sweepFlag) {\n                window.SVGPathSeg.call(this, window.SVGPathSeg.PATHSEG_ARC_ABS, \"A\", owningPathSegList);\n                this._x = x;\n                this._y = y;\n                this._r1 = r1;\n                this._r2 = r2;\n                this._angle = angle;\n                this._largeArcFlag = largeArcFlag;\n                this._sweepFlag = sweepFlag;\n            };\n            window.SVGPathSegArcAbs.prototype = Object.create(window.SVGPathSeg.prototype);\n            window.SVGPathSegArcAbs.prototype.toString = function () {\n                return \"[object SVGPathSegArcAbs]\";\n            };\n            window.SVGPathSegArcAbs.prototype._asPathString = function () {\n                return (this.pathSegTypeAsLetter +\n                    \" \" +\n                    this._r1 +\n                    \" \" +\n                    this._r2 +\n                    \" \" +\n                    this._angle +\n                    \" \" +\n                    (this._largeArcFlag ? \"1\" : \"0\") +\n                    \" \" +\n                    (this._sweepFlag ? \"1\" : \"0\") +\n                    \" \" +\n                    this._x +\n                    \" \" +\n                    this._y);\n            };\n            window.SVGPathSegArcAbs.prototype.clone = function () {\n                return new window.SVGPathSegArcAbs(undefined, this._x, this._y, this._r1, this._r2, this._angle, this._largeArcFlag, this._sweepFlag);\n            };\n            Object.defineProperty(window.SVGPathSegArcAbs.prototype, \"x\", {\n                get: function () {\n                    return this._x;\n                },\n                set: function (x) {\n                    this._x = x;\n                    this._segmentChanged();\n                },\n                enumerable: true,\n            });\n            Object.defineProperty(window.SVGPathSegArcAbs.prototype, \"y\", {\n                get: function () {\n                    return this._y;\n                },\n                set: function (y) {\n                    this._y = y;\n                    this._segmentChanged();\n                },\n                enumerable: true,\n            });\n            Object.defineProperty(window.SVGPathSegArcAbs.prototype, \"r1\", {\n                get: function () {\n                    return this._r1;\n                },\n                set: function (r1) {\n                    this._r1 = r1;\n                    this._segmentChanged();\n                },\n                enumerable: true,\n            });\n            Object.defineProperty(window.SVGPathSegArcAbs.prototype, \"r2\", {\n                get: function () {\n                    return this._r2;\n                },\n                set: function (r2) {\n                    this._r2 = r2;\n                    this._segmentChanged();\n                },\n                enumerable: true,\n            });\n            Object.defineProperty(window.SVGPathSegArcAbs.prototype, \"angle\", {\n                get: function () {\n                    return this._angle;\n                },\n                set: function (angle) {\n                    this._angle = angle;\n                    this._segmentChanged();\n                },\n                enumerable: true,\n            });\n            Object.defineProperty(window.SVGPathSegArcAbs.prototype, \"largeArcFlag\", {\n                get: function () {\n                    return this._largeArcFlag;\n                },\n                set: function (largeArcFlag) {\n                    this._largeArcFlag = largeArcFlag;\n                    this._segmentChanged();\n                },\n                enumerable: true,\n            });\n            Object.defineProperty(window.SVGPathSegArcAbs.prototype, \"sweepFlag\", {\n                get: function () {\n                    return this._sweepFlag;\n                },\n                set: function (sweepFlag) {\n                    this._sweepFlag = sweepFlag;\n                    this._segmentChanged();\n                },\n                enumerable: true,\n            });\n            window.SVGPathSegArcRel = function (owningPathSegList, x, y, r1, r2, angle, largeArcFlag, sweepFlag) {\n                window.SVGPathSeg.call(this, window.SVGPathSeg.PATHSEG_ARC_REL, \"a\", owningPathSegList);\n                this._x = x;\n                this._y = y;\n                this._r1 = r1;\n                this._r2 = r2;\n                this._angle = angle;\n                this._largeArcFlag = largeArcFlag;\n                this._sweepFlag = sweepFlag;\n            };\n            window.SVGPathSegArcRel.prototype = Object.create(window.SVGPathSeg.prototype);\n            window.SVGPathSegArcRel.prototype.toString = function () {\n                return \"[object SVGPathSegArcRel]\";\n            };\n            window.SVGPathSegArcRel.prototype._asPathString = function () {\n                return (this.pathSegTypeAsLetter +\n                    \" \" +\n                    this._r1 +\n                    \" \" +\n                    this._r2 +\n                    \" \" +\n                    this._angle +\n                    \" \" +\n                    (this._largeArcFlag ? \"1\" : \"0\") +\n                    \" \" +\n                    (this._sweepFlag ? \"1\" : \"0\") +\n                    \" \" +\n                    this._x +\n                    \" \" +\n                    this._y);\n            };\n            window.SVGPathSegArcRel.prototype.clone = function () {\n                return new window.SVGPathSegArcRel(undefined, this._x, this._y, this._r1, this._r2, this._angle, this._largeArcFlag, this._sweepFlag);\n            };\n            Object.defineProperty(window.SVGPathSegArcRel.prototype, \"x\", {\n                get: function () {\n                    return this._x;\n                },\n                set: function (x) {\n                    this._x = x;\n                    this._segmentChanged();\n                },\n                enumerable: true,\n            });\n            Object.defineProperty(window.SVGPathSegArcRel.prototype, \"y\", {\n                get: function () {\n                    return this._y;\n                },\n                set: function (y) {\n                    this._y = y;\n                    this._segmentChanged();\n                },\n                enumerable: true,\n            });\n            Object.defineProperty(window.SVGPathSegArcRel.prototype, \"r1\", {\n                get: function () {\n                    return this._r1;\n                },\n                set: function (r1) {\n                    this._r1 = r1;\n                    this._segmentChanged();\n                },\n                enumerable: true,\n            });\n            Object.defineProperty(window.SVGPathSegArcRel.prototype, \"r2\", {\n                get: function () {\n                    return this._r2;\n                },\n                set: function (r2) {\n                    this._r2 = r2;\n                    this._segmentChanged();\n                },\n                enumerable: true,\n            });\n            Object.defineProperty(window.SVGPathSegArcRel.prototype, \"angle\", {\n                get: function () {\n                    return this._angle;\n                },\n                set: function (angle) {\n                    this._angle = angle;\n                    this._segmentChanged();\n                },\n                enumerable: true,\n            });\n            Object.defineProperty(window.SVGPathSegArcRel.prototype, \"largeArcFlag\", {\n                get: function () {\n                    return this._largeArcFlag;\n                },\n                set: function (largeArcFlag) {\n                    this._largeArcFlag = largeArcFlag;\n                    this._segmentChanged();\n                },\n                enumerable: true,\n            });\n            Object.defineProperty(window.SVGPathSegArcRel.prototype, \"sweepFlag\", {\n                get: function () {\n                    return this._sweepFlag;\n                },\n                set: function (sweepFlag) {\n                    this._sweepFlag = sweepFlag;\n                    this._segmentChanged();\n                },\n                enumerable: true,\n            });\n            window.SVGPathSegLinetoHorizontalAbs = function (owningPathSegList, x) {\n                window.SVGPathSeg.call(this, window.SVGPathSeg.PATHSEG_LINETO_HORIZONTAL_ABS, \"H\", owningPathSegList);\n                this._x = x;\n            };\n            window.SVGPathSegLinetoHorizontalAbs.prototype = Object.create(window.SVGPathSeg.prototype);\n            window.SVGPathSegLinetoHorizontalAbs.prototype.toString = function () {\n                return \"[object SVGPathSegLinetoHorizontalAbs]\";\n            };\n            window.SVGPathSegLinetoHorizontalAbs.prototype._asPathString = function () {\n                return this.pathSegTypeAsLetter + \" \" + this._x;\n            };\n            window.SVGPathSegLinetoHorizontalAbs.prototype.clone = function () {\n                return new window.SVGPathSegLinetoHorizontalAbs(undefined, this._x);\n            };\n            Object.defineProperty(window.SVGPathSegLinetoHorizontalAbs.prototype, \"x\", {\n                get: function () {\n                    return this._x;\n                },\n                set: function (x) {\n                    this._x = x;\n                    this._segmentChanged();\n                },\n                enumerable: true,\n            });\n            window.SVGPathSegLinetoHorizontalRel = function (owningPathSegList, x) {\n                window.SVGPathSeg.call(this, window.SVGPathSeg.PATHSEG_LINETO_HORIZONTAL_REL, \"h\", owningPathSegList);\n                this._x = x;\n            };\n            window.SVGPathSegLinetoHorizontalRel.prototype = Object.create(window.SVGPathSeg.prototype);\n            window.SVGPathSegLinetoHorizontalRel.prototype.toString = function () {\n                return \"[object SVGPathSegLinetoHorizontalRel]\";\n            };\n            window.SVGPathSegLinetoHorizontalRel.prototype._asPathString = function () {\n                return this.pathSegTypeAsLetter + \" \" + this._x;\n            };\n            window.SVGPathSegLinetoHorizontalRel.prototype.clone = function () {\n                return new window.SVGPathSegLinetoHorizontalRel(undefined, this._x);\n            };\n            Object.defineProperty(window.SVGPathSegLinetoHorizontalRel.prototype, \"x\", {\n                get: function () {\n                    return this._x;\n                },\n                set: function (x) {\n                    this._x = x;\n                    this._segmentChanged();\n                },\n                enumerable: true,\n            });\n            window.SVGPathSegLinetoVerticalAbs = function (owningPathSegList, y) {\n                window.SVGPathSeg.call(this, window.SVGPathSeg.PATHSEG_LINETO_VERTICAL_ABS, \"V\", owningPathSegList);\n                this._y = y;\n            };\n            window.SVGPathSegLinetoVerticalAbs.prototype = Object.create(window.SVGPathSeg.prototype);\n            window.SVGPathSegLinetoVerticalAbs.prototype.toString = function () {\n                return \"[object SVGPathSegLinetoVerticalAbs]\";\n            };\n            window.SVGPathSegLinetoVerticalAbs.prototype._asPathString = function () {\n                return this.pathSegTypeAsLetter + \" \" + this._y;\n            };\n            window.SVGPathSegLinetoVerticalAbs.prototype.clone = function () {\n                return new window.SVGPathSegLinetoVerticalAbs(undefined, this._y);\n            };\n            Object.defineProperty(window.SVGPathSegLinetoVerticalAbs.prototype, \"y\", {\n                get: function () {\n                    return this._y;\n                },\n                set: function (y) {\n                    this._y = y;\n                    this._segmentChanged();\n                },\n                enumerable: true,\n            });\n            window.SVGPathSegLinetoVerticalRel = function (owningPathSegList, y) {\n                window.SVGPathSeg.call(this, window.SVGPathSeg.PATHSEG_LINETO_VERTICAL_REL, \"v\", owningPathSegList);\n                this._y = y;\n            };\n            window.SVGPathSegLinetoVerticalRel.prototype = Object.create(window.SVGPathSeg.prototype);\n            window.SVGPathSegLinetoVerticalRel.prototype.toString = function () {\n                return \"[object SVGPathSegLinetoVerticalRel]\";\n            };\n            window.SVGPathSegLinetoVerticalRel.prototype._asPathString = function () {\n                return this.pathSegTypeAsLetter + \" \" + this._y;\n            };\n            window.SVGPathSegLinetoVerticalRel.prototype.clone = function () {\n                return new window.SVGPathSegLinetoVerticalRel(undefined, this._y);\n            };\n            Object.defineProperty(window.SVGPathSegLinetoVerticalRel.prototype, \"y\", {\n                get: function () {\n                    return this._y;\n                },\n                set: function (y) {\n                    this._y = y;\n                    this._segmentChanged();\n                },\n                enumerable: true,\n            });\n            window.SVGPathSegCurvetoCubicSmoothAbs = function (owningPathSegList, x, y, x2, y2) {\n                window.SVGPathSeg.call(this, window.SVGPathSeg.PATHSEG_CURVETO_CUBIC_SMOOTH_ABS, \"S\", owningPathSegList);\n                this._x = x;\n                this._y = y;\n                this._x2 = x2;\n                this._y2 = y2;\n            };\n            window.SVGPathSegCurvetoCubicSmoothAbs.prototype = Object.create(window.SVGPathSeg.prototype);\n            window.SVGPathSegCurvetoCubicSmoothAbs.prototype.toString = function () {\n                return \"[object SVGPathSegCurvetoCubicSmoothAbs]\";\n            };\n            window.SVGPathSegCurvetoCubicSmoothAbs.prototype._asPathString = function () {\n                return this.pathSegTypeAsLetter + \" \" + this._x2 + \" \" + this._y2 + \" \" + this._x + \" \" + this._y;\n            };\n            window.SVGPathSegCurvetoCubicSmoothAbs.prototype.clone = function () {\n                return new window.SVGPathSegCurvetoCubicSmoothAbs(undefined, this._x, this._y, this._x2, this._y2);\n            };\n            Object.defineProperty(window.SVGPathSegCurvetoCubicSmoothAbs.prototype, \"x\", {\n                get: function () {\n                    return this._x;\n                },\n                set: function (x) {\n                    this._x = x;\n                    this._segmentChanged();\n                },\n                enumerable: true,\n            });\n            Object.defineProperty(window.SVGPathSegCurvetoCubicSmoothAbs.prototype, \"y\", {\n                get: function () {\n                    return this._y;\n                },\n                set: function (y) {\n                    this._y = y;\n                    this._segmentChanged();\n                },\n                enumerable: true,\n            });\n            Object.defineProperty(window.SVGPathSegCurvetoCubicSmoothAbs.prototype, \"x2\", {\n                get: function () {\n                    return this._x2;\n                },\n                set: function (x2) {\n                    this._x2 = x2;\n                    this._segmentChanged();\n                },\n                enumerable: true,\n            });\n            Object.defineProperty(window.SVGPathSegCurvetoCubicSmoothAbs.prototype, \"y2\", {\n                get: function () {\n                    return this._y2;\n                },\n                set: function (y2) {\n                    this._y2 = y2;\n                    this._segmentChanged();\n                },\n                enumerable: true,\n            });\n            window.SVGPathSegCurvetoCubicSmoothRel = function (owningPathSegList, x, y, x2, y2) {\n                window.SVGPathSeg.call(this, window.SVGPathSeg.PATHSEG_CURVETO_CUBIC_SMOOTH_REL, \"s\", owningPathSegList);\n                this._x = x;\n                this._y = y;\n                this._x2 = x2;\n                this._y2 = y2;\n            };\n            window.SVGPathSegCurvetoCubicSmoothRel.prototype = Object.create(window.SVGPathSeg.prototype);\n            window.SVGPathSegCurvetoCubicSmoothRel.prototype.toString = function () {\n                return \"[object SVGPathSegCurvetoCubicSmoothRel]\";\n            };\n            window.SVGPathSegCurvetoCubicSmoothRel.prototype._asPathString = function () {\n                return this.pathSegTypeAsLetter + \" \" + this._x2 + \" \" + this._y2 + \" \" + this._x + \" \" + this._y;\n            };\n            window.SVGPathSegCurvetoCubicSmoothRel.prototype.clone = function () {\n                return new window.SVGPathSegCurvetoCubicSmoothRel(undefined, this._x, this._y, this._x2, this._y2);\n            };\n            Object.defineProperty(window.SVGPathSegCurvetoCubicSmoothRel.prototype, \"x\", {\n                get: function () {\n                    return this._x;\n                },\n                set: function (x) {\n                    this._x = x;\n                    this._segmentChanged();\n                },\n                enumerable: true,\n            });\n            Object.defineProperty(window.SVGPathSegCurvetoCubicSmoothRel.prototype, \"y\", {\n                get: function () {\n                    return this._y;\n                },\n                set: function (y) {\n                    this._y = y;\n                    this._segmentChanged();\n                },\n                enumerable: true,\n            });\n            Object.defineProperty(window.SVGPathSegCurvetoCubicSmoothRel.prototype, \"x2\", {\n                get: function () {\n                    return this._x2;\n                },\n                set: function (x2) {\n                    this._x2 = x2;\n                    this._segmentChanged();\n                },\n                enumerable: true,\n            });\n            Object.defineProperty(window.SVGPathSegCurvetoCubicSmoothRel.prototype, \"y2\", {\n                get: function () {\n                    return this._y2;\n                },\n                set: function (y2) {\n                    this._y2 = y2;\n                    this._segmentChanged();\n                },\n                enumerable: true,\n            });\n            window.SVGPathSegCurvetoQuadraticSmoothAbs = function (owningPathSegList, x, y) {\n                window.SVGPathSeg.call(this, window.SVGPathSeg.PATHSEG_CURVETO_QUADRATIC_SMOOTH_ABS, \"T\", owningPathSegList);\n                this._x = x;\n                this._y = y;\n            };\n            window.SVGPathSegCurvetoQuadraticSmoothAbs.prototype = Object.create(window.SVGPathSeg.prototype);\n            window.SVGPathSegCurvetoQuadraticSmoothAbs.prototype.toString = function () {\n                return \"[object SVGPathSegCurvetoQuadraticSmoothAbs]\";\n            };\n            window.SVGPathSegCurvetoQuadraticSmoothAbs.prototype._asPathString = function () {\n                return this.pathSegTypeAsLetter + \" \" + this._x + \" \" + this._y;\n            };\n            window.SVGPathSegCurvetoQuadraticSmoothAbs.prototype.clone = function () {\n                return new window.SVGPathSegCurvetoQuadraticSmoothAbs(undefined, this._x, this._y);\n            };\n            Object.defineProperty(window.SVGPathSegCurvetoQuadraticSmoothAbs.prototype, \"x\", {\n                get: function () {\n                    return this._x;\n                },\n                set: function (x) {\n                    this._x = x;\n                    this._segmentChanged();\n                },\n                enumerable: true,\n            });\n            Object.defineProperty(window.SVGPathSegCurvetoQuadraticSmoothAbs.prototype, \"y\", {\n                get: function () {\n                    return this._y;\n                },\n                set: function (y) {\n                    this._y = y;\n                    this._segmentChanged();\n                },\n                enumerable: true,\n            });\n            window.SVGPathSegCurvetoQuadraticSmoothRel = function (owningPathSegList, x, y) {\n                window.SVGPathSeg.call(this, window.SVGPathSeg.PATHSEG_CURVETO_QUADRATIC_SMOOTH_REL, \"t\", owningPathSegList);\n                this._x = x;\n                this._y = y;\n            };\n            window.SVGPathSegCurvetoQuadraticSmoothRel.prototype = Object.create(window.SVGPathSeg.prototype);\n            window.SVGPathSegCurvetoQuadraticSmoothRel.prototype.toString = function () {\n                return \"[object SVGPathSegCurvetoQuadraticSmoothRel]\";\n            };\n            window.SVGPathSegCurvetoQuadraticSmoothRel.prototype._asPathString = function () {\n                return this.pathSegTypeAsLetter + \" \" + this._x + \" \" + this._y;\n            };\n            window.SVGPathSegCurvetoQuadraticSmoothRel.prototype.clone = function () {\n                return new window.SVGPathSegCurvetoQuadraticSmoothRel(undefined, this._x, this._y);\n            };\n            Object.defineProperty(window.SVGPathSegCurvetoQuadraticSmoothRel.prototype, \"x\", {\n                get: function () {\n                    return this._x;\n                },\n                set: function (x) {\n                    this._x = x;\n                    this._segmentChanged();\n                },\n                enumerable: true,\n            });\n            Object.defineProperty(window.SVGPathSegCurvetoQuadraticSmoothRel.prototype, \"y\", {\n                get: function () {\n                    return this._y;\n                },\n                set: function (y) {\n                    this._y = y;\n                    this._segmentChanged();\n                },\n                enumerable: true,\n            });\n            window.SVGPathElement.prototype.createSVGPathSegClosePath = function () {\n                return new window.SVGPathSegClosePath(undefined);\n            };\n            window.SVGPathElement.prototype.createSVGPathSegMovetoAbs = function (x, y) {\n                return new window.SVGPathSegMovetoAbs(undefined, x, y);\n            };\n            window.SVGPathElement.prototype.createSVGPathSegMovetoRel = function (x, y) {\n                return new window.SVGPathSegMovetoRel(undefined, x, y);\n            };\n            window.SVGPathElement.prototype.createSVGPathSegLinetoAbs = function (x, y) {\n                return new window.SVGPathSegLinetoAbs(undefined, x, y);\n            };\n            window.SVGPathElement.prototype.createSVGPathSegLinetoRel = function (x, y) {\n                return new window.SVGPathSegLinetoRel(undefined, x, y);\n            };\n            window.SVGPathElement.prototype.createSVGPathSegCurvetoCubicAbs = function (x, y, x1, y1, x2, y2) {\n                return new window.SVGPathSegCurvetoCubicAbs(undefined, x, y, x1, y1, x2, y2);\n            };\n            window.SVGPathElement.prototype.createSVGPathSegCurvetoCubicRel = function (x, y, x1, y1, x2, y2) {\n                return new window.SVGPathSegCurvetoCubicRel(undefined, x, y, x1, y1, x2, y2);\n            };\n            window.SVGPathElement.prototype.createSVGPathSegCurvetoQuadraticAbs = function (x, y, x1, y1) {\n                return new window.SVGPathSegCurvetoQuadraticAbs(undefined, x, y, x1, y1);\n            };\n            window.SVGPathElement.prototype.createSVGPathSegCurvetoQuadraticRel = function (x, y, x1, y1) {\n                return new window.SVGPathSegCurvetoQuadraticRel(undefined, x, y, x1, y1);\n            };\n            window.SVGPathElement.prototype.createSVGPathSegArcAbs = function (x, y, r1, r2, angle, largeArcFlag, sweepFlag) {\n                return new window.SVGPathSegArcAbs(undefined, x, y, r1, r2, angle, largeArcFlag, sweepFlag);\n            };\n            window.SVGPathElement.prototype.createSVGPathSegArcRel = function (x, y, r1, r2, angle, largeArcFlag, sweepFlag) {\n                return new window.SVGPathSegArcRel(undefined, x, y, r1, r2, angle, largeArcFlag, sweepFlag);\n            };\n            window.SVGPathElement.prototype.createSVGPathSegLinetoHorizontalAbs = function (x) {\n                return new window.SVGPathSegLinetoHorizontalAbs(undefined, x);\n            };\n            window.SVGPathElement.prototype.createSVGPathSegLinetoHorizontalRel = function (x) {\n                return new window.SVGPathSegLinetoHorizontalRel(undefined, x);\n            };\n            window.SVGPathElement.prototype.createSVGPathSegLinetoVerticalAbs = function (y) {\n                return new window.SVGPathSegLinetoVerticalAbs(undefined, y);\n            };\n            window.SVGPathElement.prototype.createSVGPathSegLinetoVerticalRel = function (y) {\n                return new window.SVGPathSegLinetoVerticalRel(undefined, y);\n            };\n            window.SVGPathElement.prototype.createSVGPathSegCurvetoCubicSmoothAbs = function (x, y, x2, y2) {\n                return new window.SVGPathSegCurvetoCubicSmoothAbs(undefined, x, y, x2, y2);\n            };\n            window.SVGPathElement.prototype.createSVGPathSegCurvetoCubicSmoothRel = function (x, y, x2, y2) {\n                return new window.SVGPathSegCurvetoCubicSmoothRel(undefined, x, y, x2, y2);\n            };\n            window.SVGPathElement.prototype.createSVGPathSegCurvetoQuadraticSmoothAbs = function (x, y) {\n                return new window.SVGPathSegCurvetoQuadraticSmoothAbs(undefined, x, y);\n            };\n            window.SVGPathElement.prototype.createSVGPathSegCurvetoQuadraticSmoothRel = function (x, y) {\n                return new window.SVGPathSegCurvetoQuadraticSmoothRel(undefined, x, y);\n            };\n            if (!(\"getPathSegAtLength\" in window.SVGPathElement.prototype)) {\n                window.SVGPathElement.prototype.getPathSegAtLength = function (distance) {\n                    if (distance === undefined || !isFinite(distance))\n                        throw \"Invalid arguments.\";\n                    const measurementElement = document.createElementNS(\"http://www.w3.org/2000/svg\", \"path\");\n                    measurementElement.setAttribute(\"d\", this.getAttribute(\"d\"));\n                    let lastPathSegment = measurementElement.pathSegList.numberOfItems - 1;\n                    if (lastPathSegment <= 0)\n                        return 0;\n                    do {\n                        measurementElement.pathSegList.removeItem(lastPathSegment);\n                        if (distance > measurementElement.getTotalLength())\n                            break;\n                        lastPathSegment--;\n                    } while (lastPathSegment > 0);\n                    return lastPathSegment;\n                };\n            }\n        }\n        if (!(\"SVGPathSegList\" in window) || !(\"appendItem\" in window.SVGPathSegList.prototype)) {\n            window.SVGPathSegList = function (pathElement) {\n                this._pathElement = pathElement;\n                this._list = this._parsePath(this._pathElement.getAttribute(\"d\"));\n                this._mutationObserverConfig = { attributes: true, attributeFilter: [\"d\"] };\n                this._pathElementMutationObserver = new MutationObserver(this._updateListFromPathMutations.bind(this));\n                this._pathElementMutationObserver.observe(this._pathElement, this._mutationObserverConfig);\n            };\n            window.SVGPathSegList.prototype.classname = \"SVGPathSegList\";\n            Object.defineProperty(window.SVGPathSegList.prototype, \"numberOfItems\", {\n                get: function () {\n                    this._checkPathSynchronizedToList();\n                    return this._list.length;\n                },\n                enumerable: true,\n            });\n            Object.defineProperty(window.SVGPathSegList.prototype, \"length\", {\n                get: function () {\n                    this._checkPathSynchronizedToList();\n                    return this._list.length;\n                },\n                enumerable: true,\n            });\n            Object.defineProperty(window.SVGPathElement.prototype, \"pathSegList\", {\n                get: function () {\n                    if (!this._pathSegList)\n                        this._pathSegList = new window.SVGPathSegList(this);\n                    return this._pathSegList;\n                },\n                enumerable: true,\n            });\n            Object.defineProperty(window.SVGPathElement.prototype, \"normalizedPathSegList\", {\n                get: function () {\n                    return this.pathSegList;\n                },\n                enumerable: true,\n            });\n            Object.defineProperty(window.SVGPathElement.prototype, \"animatedPathSegList\", {\n                get: function () {\n                    return this.pathSegList;\n                },\n                enumerable: true,\n            });\n            Object.defineProperty(window.SVGPathElement.prototype, \"animatedNormalizedPathSegList\", {\n                get: function () {\n                    return this.pathSegList;\n                },\n                enumerable: true,\n            });\n            window.SVGPathSegList.prototype._checkPathSynchronizedToList = function () {\n                this._updateListFromPathMutations(this._pathElementMutationObserver.takeRecords());\n            };\n            window.SVGPathSegList.prototype._updateListFromPathMutations = function (mutationRecords) {\n                if (!this._pathElement)\n                    return;\n                let hasPathMutations = false;\n                mutationRecords.forEach(function (record) {\n                    if (record.attributeName == \"d\")\n                        hasPathMutations = true;\n                });\n                if (hasPathMutations)\n                    this._list = this._parsePath(this._pathElement.getAttribute(\"d\"));\n            };\n            window.SVGPathSegList.prototype._writeListToPath = function () {\n                this._pathElementMutationObserver.disconnect();\n                this._pathElement.setAttribute(\"d\", window.SVGPathSegList._pathSegArrayAsString(this._list));\n                this._pathElementMutationObserver.observe(this._pathElement, this._mutationObserverConfig);\n            };\n            window.SVGPathSegList.prototype.segmentChanged = function (pathSeg) {\n                this._writeListToPath();\n            };\n            window.SVGPathSegList.prototype.clear = function () {\n                this._checkPathSynchronizedToList();\n                this._list.forEach(function (pathSeg) {\n                    pathSeg._owningPathSegList = null;\n                });\n                this._list = [];\n                this._writeListToPath();\n            };\n            window.SVGPathSegList.prototype.initialize = function (newItem) {\n                this._checkPathSynchronizedToList();\n                this._list = [newItem];\n                newItem._owningPathSegList = this;\n                this._writeListToPath();\n                return newItem;\n            };\n            window.SVGPathSegList.prototype._checkValidIndex = function (index) {\n                if (isNaN(index) || index < 0 || index >= this.numberOfItems)\n                    throw \"INDEX_SIZE_ERR\";\n            };\n            window.SVGPathSegList.prototype.getItem = function (index) {\n                this._checkPathSynchronizedToList();\n                this._checkValidIndex(index);\n                return this._list[index];\n            };\n            window.SVGPathSegList.prototype.insertItemBefore = function (newItem, index) {\n                this._checkPathSynchronizedToList();\n                if (index > this.numberOfItems)\n                    index = this.numberOfItems;\n                if (newItem._owningPathSegList) {\n                    newItem = newItem.clone();\n                }\n                this._list.splice(index, 0, newItem);\n                newItem._owningPathSegList = this;\n                this._writeListToPath();\n                return newItem;\n            };\n            window.SVGPathSegList.prototype.replaceItem = function (newItem, index) {\n                this._checkPathSynchronizedToList();\n                if (newItem._owningPathSegList) {\n                    newItem = newItem.clone();\n                }\n                this._checkValidIndex(index);\n                this._list[index] = newItem;\n                newItem._owningPathSegList = this;\n                this._writeListToPath();\n                return newItem;\n            };\n            window.SVGPathSegList.prototype.removeItem = function (index) {\n                this._checkPathSynchronizedToList();\n                this._checkValidIndex(index);\n                const item = this._list[index];\n                this._list.splice(index, 1);\n                this._writeListToPath();\n                return item;\n            };\n            window.SVGPathSegList.prototype.appendItem = function (newItem) {\n                this._checkPathSynchronizedToList();\n                if (newItem._owningPathSegList) {\n                    newItem = newItem.clone();\n                }\n                this._list.push(newItem);\n                newItem._owningPathSegList = this;\n                this._writeListToPath();\n                return newItem;\n            };\n            window.SVGPathSegList._pathSegArrayAsString = function (pathSegArray) {\n                let string = \"\";\n                let first = true;\n                pathSegArray.forEach(function (pathSeg) {\n                    if (first) {\n                        first = false;\n                        string += pathSeg._asPathString();\n                    }\n                    else {\n                        string += \" \" + pathSeg._asPathString();\n                    }\n                });\n                return string;\n            };\n            window.SVGPathSegList.prototype._parsePath = function (string) {\n                if (!string || string.length == 0)\n                    return [];\n                const owningPathSegList = this;\n                const Builder = function () {\n                    this.pathSegList = [];\n                };\n                Builder.prototype.appendSegment = function (pathSeg) {\n                    this.pathSegList.push(pathSeg);\n                };\n                const Source = function (string) {\n                    this._string = string;\n                    this._currentIndex = 0;\n                    this._endIndex = this._string.length;\n                    this._previousCommand = window.SVGPathSeg.PATHSEG_UNKNOWN;\n                    this._skipOptionalSpaces();\n                };\n                Source.prototype._isCurrentSpace = function () {\n                    const character = this._string[this._currentIndex];\n                    return (character <= \" \" &&\n                        (character == \" \" ||\n                            character == \"\\n\" ||\n                            character == \"\\t\" ||\n                            character == \"\\r\" ||\n                            character == \"\\f\"));\n                };\n                Source.prototype._skipOptionalSpaces = function () {\n                    while (this._currentIndex < this._endIndex && this._isCurrentSpace())\n                        this._currentIndex++;\n                    return this._currentIndex < this._endIndex;\n                };\n                Source.prototype._skipOptionalSpacesOrDelimiter = function () {\n                    if (this._currentIndex < this._endIndex &&\n                        !this._isCurrentSpace() &&\n                        this._string.charAt(this._currentIndex) != \",\")\n                        return false;\n                    if (this._skipOptionalSpaces()) {\n                        if (this._currentIndex < this._endIndex && this._string.charAt(this._currentIndex) == \",\") {\n                            this._currentIndex++;\n                            this._skipOptionalSpaces();\n                        }\n                    }\n                    return this._currentIndex < this._endIndex;\n                };\n                Source.prototype.hasMoreData = function () {\n                    return this._currentIndex < this._endIndex;\n                };\n                Source.prototype.peekSegmentType = function () {\n                    const lookahead = this._string[this._currentIndex];\n                    return this._pathSegTypeFromChar(lookahead);\n                };\n                Source.prototype._pathSegTypeFromChar = function (lookahead) {\n                    switch (lookahead) {\n                        case \"Z\":\n                        case \"z\":\n                            return window.SVGPathSeg.PATHSEG_CLOSEPATH;\n                        case \"M\":\n                            return window.SVGPathSeg.PATHSEG_MOVETO_ABS;\n                        case \"m\":\n                            return window.SVGPathSeg.PATHSEG_MOVETO_REL;\n                        case \"L\":\n                            return window.SVGPathSeg.PATHSEG_LINETO_ABS;\n                        case \"l\":\n                            return window.SVGPathSeg.PATHSEG_LINETO_REL;\n                        case \"C\":\n                            return window.SVGPathSeg.PATHSEG_CURVETO_CUBIC_ABS;\n                        case \"c\":\n                            return window.SVGPathSeg.PATHSEG_CURVETO_CUBIC_REL;\n                        case \"Q\":\n                            return window.SVGPathSeg.PATHSEG_CURVETO_QUADRATIC_ABS;\n                        case \"q\":\n                            return window.SVGPathSeg.PATHSEG_CURVETO_QUADRATIC_REL;\n                        case \"A\":\n                            return window.SVGPathSeg.PATHSEG_ARC_ABS;\n                        case \"a\":\n                            return window.SVGPathSeg.PATHSEG_ARC_REL;\n                        case \"H\":\n                            return window.SVGPathSeg.PATHSEG_LINETO_HORIZONTAL_ABS;\n                        case \"h\":\n                            return window.SVGPathSeg.PATHSEG_LINETO_HORIZONTAL_REL;\n                        case \"V\":\n                            return window.SVGPathSeg.PATHSEG_LINETO_VERTICAL_ABS;\n                        case \"v\":\n                            return window.SVGPathSeg.PATHSEG_LINETO_VERTICAL_REL;\n                        case \"S\":\n                            return window.SVGPathSeg.PATHSEG_CURVETO_CUBIC_SMOOTH_ABS;\n                        case \"s\":\n                            return window.SVGPathSeg.PATHSEG_CURVETO_CUBIC_SMOOTH_REL;\n                        case \"T\":\n                            return window.SVGPathSeg.PATHSEG_CURVETO_QUADRATIC_SMOOTH_ABS;\n                        case \"t\":\n                            return window.SVGPathSeg.PATHSEG_CURVETO_QUADRATIC_SMOOTH_REL;\n                        default:\n                            return window.SVGPathSeg.PATHSEG_UNKNOWN;\n                    }\n                };\n                Source.prototype._nextCommandHelper = function (lookahead, previousCommand) {\n                    if ((lookahead == \"+\" ||\n                        lookahead == \"-\" ||\n                        lookahead == \".\" ||\n                        (lookahead >= \"0\" && lookahead <= \"9\")) &&\n                        previousCommand != window.SVGPathSeg.PATHSEG_CLOSEPATH) {\n                        if (previousCommand == window.SVGPathSeg.PATHSEG_MOVETO_ABS)\n                            return window.SVGPathSeg.PATHSEG_LINETO_ABS;\n                        if (previousCommand == window.SVGPathSeg.PATHSEG_MOVETO_REL)\n                            return window.SVGPathSeg.PATHSEG_LINETO_REL;\n                        return previousCommand;\n                    }\n                    return window.SVGPathSeg.PATHSEG_UNKNOWN;\n                };\n                Source.prototype.initialCommandIsMoveTo = function () {\n                    if (!this.hasMoreData())\n                        return true;\n                    const command = this.peekSegmentType();\n                    return (command == window.SVGPathSeg.PATHSEG_MOVETO_ABS ||\n                        command == window.SVGPathSeg.PATHSEG_MOVETO_REL);\n                };\n                Source.prototype._parseNumber = function () {\n                    let exponent = 0;\n                    let integer = 0;\n                    let frac = 1;\n                    let decimal = 0;\n                    let sign = 1;\n                    let expsign = 1;\n                    const startIndex = this._currentIndex;\n                    this._skipOptionalSpaces();\n                    if (this._currentIndex < this._endIndex && this._string.charAt(this._currentIndex) == \"+\")\n                        this._currentIndex++;\n                    else if (this._currentIndex < this._endIndex && this._string.charAt(this._currentIndex) == \"-\") {\n                        this._currentIndex++;\n                        sign = -1;\n                    }\n                    if (this._currentIndex == this._endIndex ||\n                        ((this._string.charAt(this._currentIndex) < \"0\" ||\n                            this._string.charAt(this._currentIndex) > \"9\") &&\n                            this._string.charAt(this._currentIndex) != \".\"))\n                        return undefined;\n                    const startIntPartIndex = this._currentIndex;\n                    while (this._currentIndex < this._endIndex &&\n                        this._string.charAt(this._currentIndex) >= \"0\" &&\n                        this._string.charAt(this._currentIndex) <= \"9\")\n                        this._currentIndex++;\n                    if (this._currentIndex != startIntPartIndex) {\n                        let scanIntPartIndex = this._currentIndex - 1;\n                        let multiplier = 1;\n                        while (scanIntPartIndex >= startIntPartIndex) {\n                            integer += multiplier * (this._string.charAt(scanIntPartIndex--) - \"0\");\n                            multiplier *= 10;\n                        }\n                    }\n                    if (this._currentIndex < this._endIndex && this._string.charAt(this._currentIndex) == \".\") {\n                        this._currentIndex++;\n                        if (this._currentIndex >= this._endIndex ||\n                            this._string.charAt(this._currentIndex) < \"0\" ||\n                            this._string.charAt(this._currentIndex) > \"9\")\n                            return undefined;\n                        while (this._currentIndex < this._endIndex &&\n                            this._string.charAt(this._currentIndex) >= \"0\" &&\n                            this._string.charAt(this._currentIndex) <= \"9\") {\n                            frac *= 10;\n                            decimal += (this._string.charAt(this._currentIndex) - \"0\") / frac;\n                            this._currentIndex += 1;\n                        }\n                    }\n                    if (this._currentIndex != startIndex &&\n                        this._currentIndex + 1 < this._endIndex &&\n                        (this._string.charAt(this._currentIndex) == \"e\" ||\n                            this._string.charAt(this._currentIndex) == \"E\") &&\n                        this._string.charAt(this._currentIndex + 1) != \"x\" &&\n                        this._string.charAt(this._currentIndex + 1) != \"m\") {\n                        this._currentIndex++;\n                        if (this._string.charAt(this._currentIndex) == \"+\") {\n                            this._currentIndex++;\n                        }\n                        else if (this._string.charAt(this._currentIndex) == \"-\") {\n                            this._currentIndex++;\n                            expsign = -1;\n                        }\n                        if (this._currentIndex >= this._endIndex ||\n                            this._string.charAt(this._currentIndex) < \"0\" ||\n                            this._string.charAt(this._currentIndex) > \"9\")\n                            return undefined;\n                        while (this._currentIndex < this._endIndex &&\n                            this._string.charAt(this._currentIndex) >= \"0\" &&\n                            this._string.charAt(this._currentIndex) <= \"9\") {\n                            exponent *= 10;\n                            exponent += this._string.charAt(this._currentIndex) - \"0\";\n                            this._currentIndex++;\n                        }\n                    }\n                    let number = integer + decimal;\n                    number *= sign;\n                    if (exponent)\n                        number *= Math.pow(10, expsign * exponent);\n                    if (startIndex == this._currentIndex)\n                        return undefined;\n                    this._skipOptionalSpacesOrDelimiter();\n                    return number;\n                };\n                Source.prototype._parseArcFlag = function () {\n                    if (this._currentIndex >= this._endIndex)\n                        return undefined;\n                    let flag = false;\n                    const flagChar = this._string.charAt(this._currentIndex++);\n                    if (flagChar == \"0\")\n                        flag = false;\n                    else if (flagChar == \"1\")\n                        flag = true;\n                    else\n                        return undefined;\n                    this._skipOptionalSpacesOrDelimiter();\n                    return flag;\n                };\n                Source.prototype.parseSegment = function () {\n                    const lookahead = this._string[this._currentIndex];\n                    let command = this._pathSegTypeFromChar(lookahead);\n                    if (command == window.SVGPathSeg.PATHSEG_UNKNOWN) {\n                        if (this._previousCommand == window.SVGPathSeg.PATHSEG_UNKNOWN)\n                            return null;\n                        command = this._nextCommandHelper(lookahead, this._previousCommand);\n                        if (command == window.SVGPathSeg.PATHSEG_UNKNOWN)\n                            return null;\n                    }\n                    else {\n                        this._currentIndex++;\n                    }\n                    this._previousCommand = command;\n                    let points;\n                    switch (command) {\n                        case window.SVGPathSeg.PATHSEG_MOVETO_REL:\n                            return new window.SVGPathSegMovetoRel(owningPathSegList, this._parseNumber(), this._parseNumber());\n                        case window.SVGPathSeg.PATHSEG_MOVETO_ABS:\n                            return new window.SVGPathSegMovetoAbs(owningPathSegList, this._parseNumber(), this._parseNumber());\n                        case window.SVGPathSeg.PATHSEG_LINETO_REL:\n                            return new window.SVGPathSegLinetoRel(owningPathSegList, this._parseNumber(), this._parseNumber());\n                        case window.SVGPathSeg.PATHSEG_LINETO_ABS:\n                            return new window.SVGPathSegLinetoAbs(owningPathSegList, this._parseNumber(), this._parseNumber());\n                        case window.SVGPathSeg.PATHSEG_LINETO_HORIZONTAL_REL:\n                            return new window.SVGPathSegLinetoHorizontalRel(owningPathSegList, this._parseNumber());\n                        case window.SVGPathSeg.PATHSEG_LINETO_HORIZONTAL_ABS:\n                            return new window.SVGPathSegLinetoHorizontalAbs(owningPathSegList, this._parseNumber());\n                        case window.SVGPathSeg.PATHSEG_LINETO_VERTICAL_REL:\n                            return new window.SVGPathSegLinetoVerticalRel(owningPathSegList, this._parseNumber());\n                        case window.SVGPathSeg.PATHSEG_LINETO_VERTICAL_ABS:\n                            return new window.SVGPathSegLinetoVerticalAbs(owningPathSegList, this._parseNumber());\n                        case window.SVGPathSeg.PATHSEG_CLOSEPATH:\n                            this._skipOptionalSpaces();\n                            return new window.SVGPathSegClosePath(owningPathSegList);\n                        case window.SVGPathSeg.PATHSEG_CURVETO_CUBIC_REL:\n                            points = {\n                                x1: this._parseNumber(),\n                                y1: this._parseNumber(),\n                                x2: this._parseNumber(),\n                                y2: this._parseNumber(),\n                                x: this._parseNumber(),\n                                y: this._parseNumber(),\n                            };\n                            return new window.SVGPathSegCurvetoCubicRel(owningPathSegList, points.x, points.y, points.x1, points.y1, points.x2, points.y2);\n                        case window.SVGPathSeg.PATHSEG_CURVETO_CUBIC_ABS:\n                            points = {\n                                x1: this._parseNumber(),\n                                y1: this._parseNumber(),\n                                x2: this._parseNumber(),\n                                y2: this._parseNumber(),\n                                x: this._parseNumber(),\n                                y: this._parseNumber(),\n                            };\n                            return new window.SVGPathSegCurvetoCubicAbs(owningPathSegList, points.x, points.y, points.x1, points.y1, points.x2, points.y2);\n                        case window.SVGPathSeg.PATHSEG_CURVETO_CUBIC_SMOOTH_REL:\n                            points = {\n                                x2: this._parseNumber(),\n                                y2: this._parseNumber(),\n                                x: this._parseNumber(),\n                                y: this._parseNumber(),\n                            };\n                            return new window.SVGPathSegCurvetoCubicSmoothRel(owningPathSegList, points.x, points.y, points.x2, points.y2);\n                        case window.SVGPathSeg.PATHSEG_CURVETO_CUBIC_SMOOTH_ABS:\n                            points = {\n                                x2: this._parseNumber(),\n                                y2: this._parseNumber(),\n                                x: this._parseNumber(),\n                                y: this._parseNumber(),\n                            };\n                            return new window.SVGPathSegCurvetoCubicSmoothAbs(owningPathSegList, points.x, points.y, points.x2, points.y2);\n                        case window.SVGPathSeg.PATHSEG_CURVETO_QUADRATIC_REL:\n                            points = {\n                                x1: this._parseNumber(),\n                                y1: this._parseNumber(),\n                                x: this._parseNumber(),\n                                y: this._parseNumber(),\n                            };\n                            return new window.SVGPathSegCurvetoQuadraticRel(owningPathSegList, points.x, points.y, points.x1, points.y1);\n                        case window.SVGPathSeg.PATHSEG_CURVETO_QUADRATIC_ABS:\n                            points = {\n                                x1: this._parseNumber(),\n                                y1: this._parseNumber(),\n                                x: this._parseNumber(),\n                                y: this._parseNumber(),\n                            };\n                            return new window.SVGPathSegCurvetoQuadraticAbs(owningPathSegList, points.x, points.y, points.x1, points.y1);\n                        case window.SVGPathSeg.PATHSEG_CURVETO_QUADRATIC_SMOOTH_REL:\n                            return new window.SVGPathSegCurvetoQuadraticSmoothRel(owningPathSegList, this._parseNumber(), this._parseNumber());\n                        case window.SVGPathSeg.PATHSEG_CURVETO_QUADRATIC_SMOOTH_ABS:\n                            return new window.SVGPathSegCurvetoQuadraticSmoothAbs(owningPathSegList, this._parseNumber(), this._parseNumber());\n                        case window.SVGPathSeg.PATHSEG_ARC_REL:\n                            points = {\n                                x1: this._parseNumber(),\n                                y1: this._parseNumber(),\n                                arcAngle: this._parseNumber(),\n                                arcLarge: this._parseArcFlag(),\n                                arcSweep: this._parseArcFlag(),\n                                x: this._parseNumber(),\n                                y: this._parseNumber(),\n                            };\n                            return new window.SVGPathSegArcRel(owningPathSegList, points.x, points.y, points.x1, points.y1, points.arcAngle, points.arcLarge, points.arcSweep);\n                        case window.SVGPathSeg.PATHSEG_ARC_ABS:\n                            points = {\n                                x1: this._parseNumber(),\n                                y1: this._parseNumber(),\n                                arcAngle: this._parseNumber(),\n                                arcLarge: this._parseArcFlag(),\n                                arcSweep: this._parseArcFlag(),\n                                x: this._parseNumber(),\n                                y: this._parseNumber(),\n                            };\n                            return new window.SVGPathSegArcAbs(owningPathSegList, points.x, points.y, points.x1, points.y1, points.arcAngle, points.arcLarge, points.arcSweep);\n                        default:\n                            throw \"Unknown path seg type.\";\n                    }\n                };\n                const builder = new Builder();\n                const source = new Source(string);\n                if (!source.initialCommandIsMoveTo())\n                    return [];\n                while (source.hasMoreData()) {\n                    const pathSeg = source.parseSegment();\n                    if (!pathSeg)\n                        return [];\n                    builder.appendSegment(pathSeg);\n                }\n                return builder.pathSegList;\n            };\n        }\n    }\n    catch (e) {\n        console.warn(\"An error occurred in tsParticles pathseg polyfill. If the Polygon Mask is not working, please open an issue here: https://github.com/matteobruni/tsparticles\", e);\n    }\n})();\n"]}, "metadata": {}, "sourceType": "script"}
{"ast": null, "code": "import { MoveDirection } from \"../../../../Enums/Directions/MoveDirection.js\";\nimport { isNumber, isObject } from \"../../../../Utils/TypeUtils.js\";\nimport { MoveAngle } from \"./MoveAngle.js\";\nimport { MoveAttract } from \"./MoveAttract.js\";\nimport { MoveCenter } from \"./MoveCenter.js\";\nimport { MoveGravity } from \"./MoveGravity.js\";\nimport { MovePath } from \"./Path/MovePath.js\";\nimport { MoveTrail } from \"./MoveTrail.js\";\nimport { OutModes } from \"./OutModes.js\";\nimport { Spin } from \"./Spin.js\";\nimport { setRangeValue } from \"../../../../Utils/NumberUtils.js\";\nexport class Move {\n  constructor() {\n    this.angle = new MoveAngle();\n    this.attract = new MoveAttract();\n    this.center = new MoveCenter();\n    this.decay = 0;\n    this.distance = {};\n    this.direction = MoveDirection.none;\n    this.drift = 0;\n    this.enable = false;\n    this.gravity = new MoveGravity();\n    this.path = new MovePath();\n    this.outModes = new OutModes();\n    this.random = false;\n    this.size = false;\n    this.speed = 2;\n    this.spin = new Spin();\n    this.straight = false;\n    this.trail = new MoveTrail();\n    this.vibrate = false;\n    this.warp = false;\n  }\n  load(data) {\n    if (!data) {\n      return;\n    }\n    this.angle.load(isNumber(data.angle) ? {\n      value: data.angle\n    } : data.angle);\n    this.attract.load(data.attract);\n    this.center.load(data.center);\n    if (data.decay !== undefined) {\n      this.decay = setRangeValue(data.decay);\n    }\n    if (data.direction !== undefined) {\n      this.direction = data.direction;\n    }\n    if (data.distance !== undefined) {\n      this.distance = isNumber(data.distance) ? {\n        horizontal: data.distance,\n        vertical: data.distance\n      } : {\n        ...data.distance\n      };\n    }\n    if (data.drift !== undefined) {\n      this.drift = setRangeValue(data.drift);\n    }\n    if (data.enable !== undefined) {\n      this.enable = data.enable;\n    }\n    this.gravity.load(data.gravity);\n    const outModes = data.outModes;\n    if (outModes !== undefined) {\n      if (isObject(outModes)) {\n        this.outModes.load(outModes);\n      } else {\n        this.outModes.load({\n          default: outModes\n        });\n      }\n    }\n    this.path.load(data.path);\n    if (data.random !== undefined) {\n      this.random = data.random;\n    }\n    if (data.size !== undefined) {\n      this.size = data.size;\n    }\n    if (data.speed !== undefined) {\n      this.speed = setRangeValue(data.speed);\n    }\n    this.spin.load(data.spin);\n    if (data.straight !== undefined) {\n      this.straight = data.straight;\n    }\n    this.trail.load(data.trail);\n    if (data.vibrate !== undefined) {\n      this.vibrate = data.vibrate;\n    }\n    if (data.warp !== undefined) {\n      this.warp = data.warp;\n    }\n  }\n}", "map": {"version": 3, "names": ["MoveDirection", "isNumber", "isObject", "MoveAngle", "MoveAttract", "MoveCenter", "MoveGravity", "MovePath", "MoveTrail", "OutModes", "Spin", "setRangeValue", "Move", "constructor", "angle", "attract", "center", "decay", "distance", "direction", "none", "drift", "enable", "gravity", "path", "outModes", "random", "size", "speed", "spin", "straight", "trail", "vibrate", "warp", "load", "data", "value", "undefined", "horizontal", "vertical", "default"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/engine/browser/Options/Classes/Particles/Move/Move.js"], "sourcesContent": ["import { MoveDirection } from \"../../../../Enums/Directions/MoveDirection.js\";\nimport { isNumber, isObject } from \"../../../../Utils/TypeUtils.js\";\nimport { MoveAngle } from \"./MoveAngle.js\";\nimport { MoveAttract } from \"./MoveAttract.js\";\nimport { MoveCenter } from \"./MoveCenter.js\";\nimport { MoveGravity } from \"./MoveGravity.js\";\nimport { MovePath } from \"./Path/MovePath.js\";\nimport { MoveTrail } from \"./MoveTrail.js\";\nimport { OutModes } from \"./OutModes.js\";\nimport { Spin } from \"./Spin.js\";\nimport { setRangeValue } from \"../../../../Utils/NumberUtils.js\";\nexport class Move {\n    constructor() {\n        this.angle = new MoveAngle();\n        this.attract = new MoveAttract();\n        this.center = new MoveCenter();\n        this.decay = 0;\n        this.distance = {};\n        this.direction = MoveDirection.none;\n        this.drift = 0;\n        this.enable = false;\n        this.gravity = new MoveGravity();\n        this.path = new MovePath();\n        this.outModes = new OutModes();\n        this.random = false;\n        this.size = false;\n        this.speed = 2;\n        this.spin = new Spin();\n        this.straight = false;\n        this.trail = new MoveTrail();\n        this.vibrate = false;\n        this.warp = false;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        this.angle.load(isNumber(data.angle) ? { value: data.angle } : data.angle);\n        this.attract.load(data.attract);\n        this.center.load(data.center);\n        if (data.decay !== undefined) {\n            this.decay = setRangeValue(data.decay);\n        }\n        if (data.direction !== undefined) {\n            this.direction = data.direction;\n        }\n        if (data.distance !== undefined) {\n            this.distance = isNumber(data.distance)\n                ? {\n                    horizontal: data.distance,\n                    vertical: data.distance,\n                }\n                : { ...data.distance };\n        }\n        if (data.drift !== undefined) {\n            this.drift = setRangeValue(data.drift);\n        }\n        if (data.enable !== undefined) {\n            this.enable = data.enable;\n        }\n        this.gravity.load(data.gravity);\n        const outModes = data.outModes;\n        if (outModes !== undefined) {\n            if (isObject(outModes)) {\n                this.outModes.load(outModes);\n            }\n            else {\n                this.outModes.load({\n                    default: outModes,\n                });\n            }\n        }\n        this.path.load(data.path);\n        if (data.random !== undefined) {\n            this.random = data.random;\n        }\n        if (data.size !== undefined) {\n            this.size = data.size;\n        }\n        if (data.speed !== undefined) {\n            this.speed = setRangeValue(data.speed);\n        }\n        this.spin.load(data.spin);\n        if (data.straight !== undefined) {\n            this.straight = data.straight;\n        }\n        this.trail.load(data.trail);\n        if (data.vibrate !== undefined) {\n            this.vibrate = data.vibrate;\n        }\n        if (data.warp !== undefined) {\n            this.warp = data.warp;\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,+CAA+C;AAC7E,SAASC,QAAQ,EAAEC,QAAQ,QAAQ,gCAAgC;AACnE,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,UAAU,QAAQ,iBAAiB;AAC5C,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,QAAQ,QAAQ,oBAAoB;AAC7C,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,IAAI,QAAQ,WAAW;AAChC,SAASC,aAAa,QAAQ,kCAAkC;AAChE,OAAO,MAAMC,IAAI,CAAC;EACdC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,KAAK,GAAG,IAAIX,SAAS,CAAC,CAAC;IAC5B,IAAI,CAACY,OAAO,GAAG,IAAIX,WAAW,CAAC,CAAC;IAChC,IAAI,CAACY,MAAM,GAAG,IAAIX,UAAU,CAAC,CAAC;IAC9B,IAAI,CAACY,KAAK,GAAG,CAAC;IACd,IAAI,CAACC,QAAQ,GAAG,CAAC,CAAC;IAClB,IAAI,CAACC,SAAS,GAAGnB,aAAa,CAACoB,IAAI;IACnC,IAAI,CAACC,KAAK,GAAG,CAAC;IACd,IAAI,CAACC,MAAM,GAAG,KAAK;IACnB,IAAI,CAACC,OAAO,GAAG,IAAIjB,WAAW,CAAC,CAAC;IAChC,IAAI,CAACkB,IAAI,GAAG,IAAIjB,QAAQ,CAAC,CAAC;IAC1B,IAAI,CAACkB,QAAQ,GAAG,IAAIhB,QAAQ,CAAC,CAAC;IAC9B,IAAI,CAACiB,MAAM,GAAG,KAAK;IACnB,IAAI,CAACC,IAAI,GAAG,KAAK;IACjB,IAAI,CAACC,KAAK,GAAG,CAAC;IACd,IAAI,CAACC,IAAI,GAAG,IAAInB,IAAI,CAAC,CAAC;IACtB,IAAI,CAACoB,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,KAAK,GAAG,IAAIvB,SAAS,CAAC,CAAC;IAC5B,IAAI,CAACwB,OAAO,GAAG,KAAK;IACpB,IAAI,CAACC,IAAI,GAAG,KAAK;EACrB;EACAC,IAAIA,CAACC,IAAI,EAAE;IACP,IAAI,CAACA,IAAI,EAAE;MACP;IACJ;IACA,IAAI,CAACrB,KAAK,CAACoB,IAAI,CAACjC,QAAQ,CAACkC,IAAI,CAACrB,KAAK,CAAC,GAAG;MAAEsB,KAAK,EAAED,IAAI,CAACrB;IAAM,CAAC,GAAGqB,IAAI,CAACrB,KAAK,CAAC;IAC1E,IAAI,CAACC,OAAO,CAACmB,IAAI,CAACC,IAAI,CAACpB,OAAO,CAAC;IAC/B,IAAI,CAACC,MAAM,CAACkB,IAAI,CAACC,IAAI,CAACnB,MAAM,CAAC;IAC7B,IAAImB,IAAI,CAAClB,KAAK,KAAKoB,SAAS,EAAE;MAC1B,IAAI,CAACpB,KAAK,GAAGN,aAAa,CAACwB,IAAI,CAAClB,KAAK,CAAC;IAC1C;IACA,IAAIkB,IAAI,CAAChB,SAAS,KAAKkB,SAAS,EAAE;MAC9B,IAAI,CAAClB,SAAS,GAAGgB,IAAI,CAAChB,SAAS;IACnC;IACA,IAAIgB,IAAI,CAACjB,QAAQ,KAAKmB,SAAS,EAAE;MAC7B,IAAI,CAACnB,QAAQ,GAAGjB,QAAQ,CAACkC,IAAI,CAACjB,QAAQ,CAAC,GACjC;QACEoB,UAAU,EAAEH,IAAI,CAACjB,QAAQ;QACzBqB,QAAQ,EAAEJ,IAAI,CAACjB;MACnB,CAAC,GACC;QAAE,GAAGiB,IAAI,CAACjB;MAAS,CAAC;IAC9B;IACA,IAAIiB,IAAI,CAACd,KAAK,KAAKgB,SAAS,EAAE;MAC1B,IAAI,CAAChB,KAAK,GAAGV,aAAa,CAACwB,IAAI,CAACd,KAAK,CAAC;IAC1C;IACA,IAAIc,IAAI,CAACb,MAAM,KAAKe,SAAS,EAAE;MAC3B,IAAI,CAACf,MAAM,GAAGa,IAAI,CAACb,MAAM;IAC7B;IACA,IAAI,CAACC,OAAO,CAACW,IAAI,CAACC,IAAI,CAACZ,OAAO,CAAC;IAC/B,MAAME,QAAQ,GAAGU,IAAI,CAACV,QAAQ;IAC9B,IAAIA,QAAQ,KAAKY,SAAS,EAAE;MACxB,IAAInC,QAAQ,CAACuB,QAAQ,CAAC,EAAE;QACpB,IAAI,CAACA,QAAQ,CAACS,IAAI,CAACT,QAAQ,CAAC;MAChC,CAAC,MACI;QACD,IAAI,CAACA,QAAQ,CAACS,IAAI,CAAC;UACfM,OAAO,EAAEf;QACb,CAAC,CAAC;MACN;IACJ;IACA,IAAI,CAACD,IAAI,CAACU,IAAI,CAACC,IAAI,CAACX,IAAI,CAAC;IACzB,IAAIW,IAAI,CAACT,MAAM,KAAKW,SAAS,EAAE;MAC3B,IAAI,CAACX,MAAM,GAAGS,IAAI,CAACT,MAAM;IAC7B;IACA,IAAIS,IAAI,CAACR,IAAI,KAAKU,SAAS,EAAE;MACzB,IAAI,CAACV,IAAI,GAAGQ,IAAI,CAACR,IAAI;IACzB;IACA,IAAIQ,IAAI,CAACP,KAAK,KAAKS,SAAS,EAAE;MAC1B,IAAI,CAACT,KAAK,GAAGjB,aAAa,CAACwB,IAAI,CAACP,KAAK,CAAC;IAC1C;IACA,IAAI,CAACC,IAAI,CAACK,IAAI,CAACC,IAAI,CAACN,IAAI,CAAC;IACzB,IAAIM,IAAI,CAACL,QAAQ,KAAKO,SAAS,EAAE;MAC7B,IAAI,CAACP,QAAQ,GAAGK,IAAI,CAACL,QAAQ;IACjC;IACA,IAAI,CAACC,KAAK,CAACG,IAAI,CAACC,IAAI,CAACJ,KAAK,CAAC;IAC3B,IAAII,IAAI,CAACH,OAAO,KAAKK,SAAS,EAAE;MAC5B,IAAI,CAACL,OAAO,GAAGG,IAAI,CAACH,OAAO;IAC/B;IACA,IAAIG,IAAI,CAACF,IAAI,KAAKI,SAAS,EAAE;MACzB,IAAI,CAACJ,IAAI,GAAGE,IAAI,CAACF,IAAI;IACzB;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
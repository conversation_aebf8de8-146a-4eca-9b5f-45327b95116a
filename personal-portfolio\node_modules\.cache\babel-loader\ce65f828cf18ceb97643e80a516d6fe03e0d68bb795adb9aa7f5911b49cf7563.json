{"ast": null, "code": "import { getRangeValue, parseAlpha } from \"./NumberUtils.js\";\nimport { hslToRgb, hslaToRgba } from \"./ColorUtils.js\";\nvar HslIndexes;\n(function (HslIndexes) {\n  HslIndexes[HslIndexes[\"h\"] = 1] = \"h\";\n  HslIndexes[HslIndexes[\"s\"] = 2] = \"s\";\n  HslIndexes[HslIndexes[\"l\"] = 3] = \"l\";\n  HslIndexes[HslIndexes[\"a\"] = 5] = \"a\";\n})(HslIndexes || (HslIndexes = {}));\nexport class HslColorManager {\n  constructor() {\n    this.key = \"hsl\";\n    this.stringPrefix = \"hsl\";\n  }\n  handleColor(color) {\n    const colorValue = color.value,\n      hslColor = colorValue.hsl ?? color.value;\n    if (hslColor.h !== undefined && hslColor.s !== undefined && hslColor.l !== undefined) {\n      return hslToRgb(hslColor);\n    }\n  }\n  handleRangeColor(color) {\n    const colorValue = color.value,\n      hslColor = colorValue.hsl ?? color.value;\n    if (hslColor.h !== undefined && hslColor.l !== undefined) {\n      return hslToRgb({\n        h: getRangeValue(hslColor.h),\n        l: getRangeValue(hslColor.l),\n        s: getRangeValue(hslColor.s)\n      });\n    }\n  }\n  parseString(input) {\n    if (!input.startsWith(\"hsl\")) {\n      return;\n    }\n    const regex = /hsla?\\(\\s*(\\d+)\\s*,\\s*(\\d+)%\\s*,\\s*(\\d+)%\\s*(,\\s*([\\d.%]+)\\s*)?\\)/i,\n      result = regex.exec(input),\n      minLength = 4,\n      defaultAlpha = 1,\n      radix = 10;\n    return result ? hslaToRgba({\n      a: result.length > minLength ? parseAlpha(result[HslIndexes.a]) : defaultAlpha,\n      h: parseInt(result[HslIndexes.h], radix),\n      l: parseInt(result[HslIndexes.l], radix),\n      s: parseInt(result[HslIndexes.s], radix)\n    }) : undefined;\n  }\n}", "map": {"version": 3, "names": ["getRangeValue", "parseAlpha", "hslToRgb", "hslaToRgba", "HslIndexes", "HslColorManager", "constructor", "key", "stringPrefix", "handleColor", "color", "colorValue", "value", "hslColor", "hsl", "h", "undefined", "s", "l", "handleRangeColor", "parseString", "input", "startsWith", "regex", "result", "exec", "<PERSON><PERSON><PERSON><PERSON>", "defaultAlpha", "radix", "a", "length", "parseInt"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/engine/browser/Utils/HslColorManager.js"], "sourcesContent": ["import { getRangeValue, parseAlpha } from \"./NumberUtils.js\";\nimport { hslToRgb, hslaToRgba } from \"./ColorUtils.js\";\nvar HslIndexes;\n(function (HslIndexes) {\n    HslIndexes[HslIndexes[\"h\"] = 1] = \"h\";\n    HslIndexes[HslIndexes[\"s\"] = 2] = \"s\";\n    HslIndexes[HslIndexes[\"l\"] = 3] = \"l\";\n    HslIndexes[HslIndexes[\"a\"] = 5] = \"a\";\n})(HslIndexes || (HslIndexes = {}));\nexport class HslColorManager {\n    constructor() {\n        this.key = \"hsl\";\n        this.stringPrefix = \"hsl\";\n    }\n    handleColor(color) {\n        const colorValue = color.value, hslColor = colorValue.hsl ?? color.value;\n        if (hslColor.h !== undefined && hslColor.s !== undefined && hslColor.l !== undefined) {\n            return hslToRgb(hslColor);\n        }\n    }\n    handleRangeColor(color) {\n        const colorValue = color.value, hslColor = colorValue.hsl ?? color.value;\n        if (hslColor.h !== undefined && hslColor.l !== undefined) {\n            return hslToRgb({\n                h: getRangeValue(hslColor.h),\n                l: getRangeValue(hslColor.l),\n                s: getRangeValue(hslColor.s),\n            });\n        }\n    }\n    parseString(input) {\n        if (!input.startsWith(\"hsl\")) {\n            return;\n        }\n        const regex = /hsla?\\(\\s*(\\d+)\\s*,\\s*(\\d+)%\\s*,\\s*(\\d+)%\\s*(,\\s*([\\d.%]+)\\s*)?\\)/i, result = regex.exec(input), minLength = 4, defaultAlpha = 1, radix = 10;\n        return result\n            ? hslaToRgba({\n                a: result.length > minLength ? parseAlpha(result[HslIndexes.a]) : defaultAlpha,\n                h: parseInt(result[HslIndexes.h], radix),\n                l: parseInt(result[HslIndexes.l], radix),\n                s: parseInt(result[HslIndexes.s], radix),\n            })\n            : undefined;\n    }\n}\n"], "mappings": "AAAA,SAASA,aAAa,EAAEC,UAAU,QAAQ,kBAAkB;AAC5D,SAASC,QAAQ,EAAEC,UAAU,QAAQ,iBAAiB;AACtD,IAAIC,UAAU;AACd,CAAC,UAAUA,UAAU,EAAE;EACnBA,UAAU,CAACA,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG;EACrCA,UAAU,CAACA,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG;EACrCA,UAAU,CAACA,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG;EACrCA,UAAU,CAACA,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG;AACzC,CAAC,EAAEA,UAAU,KAAKA,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC;AACnC,OAAO,MAAMC,eAAe,CAAC;EACzBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,GAAG,GAAG,KAAK;IAChB,IAAI,CAACC,YAAY,GAAG,KAAK;EAC7B;EACAC,WAAWA,CAACC,KAAK,EAAE;IACf,MAAMC,UAAU,GAAGD,KAAK,CAACE,KAAK;MAAEC,QAAQ,GAAGF,UAAU,CAACG,GAAG,IAAIJ,KAAK,CAACE,KAAK;IACxE,IAAIC,QAAQ,CAACE,CAAC,KAAKC,SAAS,IAAIH,QAAQ,CAACI,CAAC,KAAKD,SAAS,IAAIH,QAAQ,CAACK,CAAC,KAAKF,SAAS,EAAE;MAClF,OAAOd,QAAQ,CAACW,QAAQ,CAAC;IAC7B;EACJ;EACAM,gBAAgBA,CAACT,KAAK,EAAE;IACpB,MAAMC,UAAU,GAAGD,KAAK,CAACE,KAAK;MAAEC,QAAQ,GAAGF,UAAU,CAACG,GAAG,IAAIJ,KAAK,CAACE,KAAK;IACxE,IAAIC,QAAQ,CAACE,CAAC,KAAKC,SAAS,IAAIH,QAAQ,CAACK,CAAC,KAAKF,SAAS,EAAE;MACtD,OAAOd,QAAQ,CAAC;QACZa,CAAC,EAAEf,aAAa,CAACa,QAAQ,CAACE,CAAC,CAAC;QAC5BG,CAAC,EAAElB,aAAa,CAACa,QAAQ,CAACK,CAAC,CAAC;QAC5BD,CAAC,EAAEjB,aAAa,CAACa,QAAQ,CAACI,CAAC;MAC/B,CAAC,CAAC;IACN;EACJ;EACAG,WAAWA,CAACC,KAAK,EAAE;IACf,IAAI,CAACA,KAAK,CAACC,UAAU,CAAC,KAAK,CAAC,EAAE;MAC1B;IACJ;IACA,MAAMC,KAAK,GAAG,oEAAoE;MAAEC,MAAM,GAAGD,KAAK,CAACE,IAAI,CAACJ,KAAK,CAAC;MAAEK,SAAS,GAAG,CAAC;MAAEC,YAAY,GAAG,CAAC;MAAEC,KAAK,GAAG,EAAE;IAC3J,OAAOJ,MAAM,GACPrB,UAAU,CAAC;MACT0B,CAAC,EAAEL,MAAM,CAACM,MAAM,GAAGJ,SAAS,GAAGzB,UAAU,CAACuB,MAAM,CAACpB,UAAU,CAACyB,CAAC,CAAC,CAAC,GAAGF,YAAY;MAC9EZ,CAAC,EAAEgB,QAAQ,CAACP,MAAM,CAACpB,UAAU,CAACW,CAAC,CAAC,EAAEa,KAAK,CAAC;MACxCV,CAAC,EAAEa,QAAQ,CAACP,MAAM,CAACpB,UAAU,CAACc,CAAC,CAAC,EAAEU,KAAK,CAAC;MACxCX,CAAC,EAAEc,QAAQ,CAACP,MAAM,CAACpB,UAAU,CAACa,CAAC,CAAC,EAAEW,KAAK;IAC3C,CAAC,CAAC,GACAZ,SAAS;EACnB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
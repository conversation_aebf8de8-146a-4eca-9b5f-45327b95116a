{"ast": null, "code": "import { BubbleBase } from \"./BubbleBase.js\";\nexport class BubbleDiv extends BubbleBase {\n  constructor() {\n    super();\n    this.selectors = [];\n  }\n  load(data) {\n    super.load(data);\n    if (!data) {\n      return;\n    }\n    if (data.selectors !== undefined) {\n      this.selectors = data.selectors;\n    }\n  }\n}", "map": {"version": 3, "names": ["BubbleBase", "BubbleDiv", "constructor", "selectors", "load", "data", "undefined"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/interaction-external-bubble/browser/Options/Classes/BubbleDiv.js"], "sourcesContent": ["import { BubbleBase } from \"./BubbleBase.js\";\nexport class BubbleDiv extends BubbleBase {\n    constructor() {\n        super();\n        this.selectors = [];\n    }\n    load(data) {\n        super.load(data);\n        if (!data) {\n            return;\n        }\n        if (data.selectors !== undefined) {\n            this.selectors = data.selectors;\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,iBAAiB;AAC5C,OAAO,MAAMC,SAAS,SAASD,UAAU,CAAC;EACtCE,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,SAAS,GAAG,EAAE;EACvB;EACAC,IAAIA,CAACC,IAAI,EAAE;IACP,KAAK,CAACD,IAAI,CAACC,IAAI,CAAC;IAChB,IAAI,CAACA,IAAI,EAAE;MACP;IACJ;IACA,IAAIA,IAAI,CAACF,SAAS,KAAKG,SAAS,EAAE;MAC9B,IAAI,CAACH,SAAS,GAAGE,IAAI,CAACF,SAAS;IACnC;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
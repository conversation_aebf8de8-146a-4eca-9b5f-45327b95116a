{"ast": null, "code": "export function isBoolean(arg) {\n  return typeof arg === \"boolean\";\n}\nexport function isString(arg) {\n  return typeof arg === \"string\";\n}\nexport function isNumber(arg) {\n  return typeof arg === \"number\";\n}\nexport function isFunction(arg) {\n  return typeof arg === \"function\";\n}\nexport function isObject(arg) {\n  return typeof arg === \"object\" && arg !== null;\n}\nexport function isArray(arg) {\n  return Array.isArray(arg);\n}", "map": {"version": 3, "names": ["isBoolean", "arg", "isString", "isNumber", "isFunction", "isObject", "isArray", "Array"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/engine/browser/Utils/TypeUtils.js"], "sourcesContent": ["export function isBoolean(arg) {\n    return typeof arg === \"boolean\";\n}\nexport function isString(arg) {\n    return typeof arg === \"string\";\n}\nexport function isNumber(arg) {\n    return typeof arg === \"number\";\n}\nexport function isFunction(arg) {\n    return typeof arg === \"function\";\n}\nexport function isObject(arg) {\n    return typeof arg === \"object\" && arg !== null;\n}\nexport function isArray(arg) {\n    return Array.isArray(arg);\n}\n"], "mappings": "AAAA,OAAO,SAASA,SAASA,CAACC,GAAG,EAAE;EAC3B,OAAO,OAAOA,GAAG,KAAK,SAAS;AACnC;AACA,OAAO,SAASC,QAAQA,CAACD,GAAG,EAAE;EAC1B,OAAO,OAAOA,GAAG,KAAK,QAAQ;AAClC;AACA,OAAO,SAASE,QAAQA,CAACF,GAAG,EAAE;EAC1B,OAAO,OAAOA,GAAG,KAAK,QAAQ;AAClC;AACA,OAAO,SAASG,UAAUA,CAACH,GAAG,EAAE;EAC5B,OAAO,OAAOA,GAAG,KAAK,UAAU;AACpC;AACA,OAAO,SAASI,QAAQA,CAACJ,GAAG,EAAE;EAC1B,OAAO,OAAOA,GAAG,KAAK,QAAQ,IAAIA,GAAG,KAAK,IAAI;AAClD;AACA,OAAO,SAASK,OAAOA,CAACL,GAAG,EAAE;EACzB,OAAOM,KAAK,CAACD,OAAO,CAACL,GAAG,CAAC;AAC7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
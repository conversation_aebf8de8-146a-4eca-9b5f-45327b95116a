{"ast": null, "code": "import { OutMode, OutModeDirection, getRangeValue } from \"@tsparticles/engine\";\nconst minVelocity = 0,\n  boundsMin = 0;\nexport function bounceHorizontal(data) {\n  if (data.outMode !== OutMode.bounce && data.outMode !== OutMode.split || data.direction !== OutModeDirection.left && data.direction !== OutModeDirection.right) {\n    return;\n  }\n  if (data.bounds.right < boundsMin && data.direction === OutModeDirection.left) {\n    data.particle.position.x = data.size + data.offset.x;\n  } else if (data.bounds.left > data.canvasSize.width && data.direction === OutModeDirection.right) {\n    data.particle.position.x = data.canvasSize.width - data.size - data.offset.x;\n  }\n  const velocity = data.particle.velocity.x;\n  let bounced = false;\n  if (data.direction === OutModeDirection.right && data.bounds.right >= data.canvasSize.width && velocity > minVelocity || data.direction === OutModeDirection.left && data.bounds.left <= boundsMin && velocity < minVelocity) {\n    const newVelocity = getRangeValue(data.particle.options.bounce.horizontal.value);\n    data.particle.velocity.x *= -newVelocity;\n    bounced = true;\n  }\n  if (!bounced) {\n    return;\n  }\n  const minPos = data.offset.x + data.size;\n  if (data.bounds.right >= data.canvasSize.width && data.direction === OutModeDirection.right) {\n    data.particle.position.x = data.canvasSize.width - minPos;\n  } else if (data.bounds.left <= boundsMin && data.direction === OutModeDirection.left) {\n    data.particle.position.x = minPos;\n  }\n  if (data.outMode === OutMode.split) {\n    data.particle.destroy();\n  }\n}\nexport function bounceVertical(data) {\n  if (data.outMode !== OutMode.bounce && data.outMode !== OutMode.split || data.direction !== OutModeDirection.bottom && data.direction !== OutModeDirection.top) {\n    return;\n  }\n  if (data.bounds.bottom < boundsMin && data.direction === OutModeDirection.top) {\n    data.particle.position.y = data.size + data.offset.y;\n  } else if (data.bounds.top > data.canvasSize.height && data.direction === OutModeDirection.bottom) {\n    data.particle.position.y = data.canvasSize.height - data.size - data.offset.y;\n  }\n  const velocity = data.particle.velocity.y;\n  let bounced = false;\n  if (data.direction === OutModeDirection.bottom && data.bounds.bottom >= data.canvasSize.height && velocity > minVelocity || data.direction === OutModeDirection.top && data.bounds.top <= boundsMin && velocity < minVelocity) {\n    const newVelocity = getRangeValue(data.particle.options.bounce.vertical.value);\n    data.particle.velocity.y *= -newVelocity;\n    bounced = true;\n  }\n  if (!bounced) {\n    return;\n  }\n  const minPos = data.offset.y + data.size;\n  if (data.bounds.bottom >= data.canvasSize.height && data.direction === OutModeDirection.bottom) {\n    data.particle.position.y = data.canvasSize.height - minPos;\n  } else if (data.bounds.top <= boundsMin && data.direction === OutModeDirection.top) {\n    data.particle.position.y = minPos;\n  }\n  if (data.outMode === OutMode.split) {\n    data.particle.destroy();\n  }\n}", "map": {"version": 3, "names": ["OutMode", "OutModeDirection", "getRangeValue", "minVelocity", "boundsMin", "bounceHorizontal", "data", "outMode", "bounce", "split", "direction", "left", "right", "bounds", "particle", "position", "x", "size", "offset", "canvasSize", "width", "velocity", "bounced", "newVelocity", "options", "horizontal", "value", "minPos", "destroy", "bounceVertical", "bottom", "top", "y", "height", "vertical"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/updater-out-modes/browser/Utils.js"], "sourcesContent": ["import { OutMode, OutModeDirection, getRangeValue } from \"@tsparticles/engine\";\nconst minVelocity = 0, boundsMin = 0;\nexport function bounceHorizontal(data) {\n    if ((data.outMode !== OutMode.bounce && data.outMode !== OutMode.split) ||\n        (data.direction !== OutModeDirection.left && data.direction !== OutModeDirection.right)) {\n        return;\n    }\n    if (data.bounds.right < boundsMin && data.direction === OutModeDirection.left) {\n        data.particle.position.x = data.size + data.offset.x;\n    }\n    else if (data.bounds.left > data.canvasSize.width && data.direction === OutModeDirection.right) {\n        data.particle.position.x = data.canvasSize.width - data.size - data.offset.x;\n    }\n    const velocity = data.particle.velocity.x;\n    let bounced = false;\n    if ((data.direction === OutModeDirection.right &&\n        data.bounds.right >= data.canvasSize.width &&\n        velocity > minVelocity) ||\n        (data.direction === OutModeDirection.left && data.bounds.left <= boundsMin && velocity < minVelocity)) {\n        const newVelocity = getRangeValue(data.particle.options.bounce.horizontal.value);\n        data.particle.velocity.x *= -newVelocity;\n        bounced = true;\n    }\n    if (!bounced) {\n        return;\n    }\n    const minPos = data.offset.x + data.size;\n    if (data.bounds.right >= data.canvasSize.width && data.direction === OutModeDirection.right) {\n        data.particle.position.x = data.canvasSize.width - minPos;\n    }\n    else if (data.bounds.left <= boundsMin && data.direction === OutModeDirection.left) {\n        data.particle.position.x = minPos;\n    }\n    if (data.outMode === OutMode.split) {\n        data.particle.destroy();\n    }\n}\nexport function bounceVertical(data) {\n    if ((data.outMode !== OutMode.bounce && data.outMode !== OutMode.split) ||\n        (data.direction !== OutModeDirection.bottom && data.direction !== OutModeDirection.top)) {\n        return;\n    }\n    if (data.bounds.bottom < boundsMin && data.direction === OutModeDirection.top) {\n        data.particle.position.y = data.size + data.offset.y;\n    }\n    else if (data.bounds.top > data.canvasSize.height && data.direction === OutModeDirection.bottom) {\n        data.particle.position.y = data.canvasSize.height - data.size - data.offset.y;\n    }\n    const velocity = data.particle.velocity.y;\n    let bounced = false;\n    if ((data.direction === OutModeDirection.bottom &&\n        data.bounds.bottom >= data.canvasSize.height &&\n        velocity > minVelocity) ||\n        (data.direction === OutModeDirection.top && data.bounds.top <= boundsMin && velocity < minVelocity)) {\n        const newVelocity = getRangeValue(data.particle.options.bounce.vertical.value);\n        data.particle.velocity.y *= -newVelocity;\n        bounced = true;\n    }\n    if (!bounced) {\n        return;\n    }\n    const minPos = data.offset.y + data.size;\n    if (data.bounds.bottom >= data.canvasSize.height && data.direction === OutModeDirection.bottom) {\n        data.particle.position.y = data.canvasSize.height - minPos;\n    }\n    else if (data.bounds.top <= boundsMin && data.direction === OutModeDirection.top) {\n        data.particle.position.y = minPos;\n    }\n    if (data.outMode === OutMode.split) {\n        data.particle.destroy();\n    }\n}\n"], "mappings": "AAAA,SAASA,OAAO,EAAEC,gBAAgB,EAAEC,aAAa,QAAQ,qBAAqB;AAC9E,MAAMC,WAAW,GAAG,CAAC;EAAEC,SAAS,GAAG,CAAC;AACpC,OAAO,SAASC,gBAAgBA,CAACC,IAAI,EAAE;EACnC,IAAKA,IAAI,CAACC,OAAO,KAAKP,OAAO,CAACQ,MAAM,IAAIF,IAAI,CAACC,OAAO,KAAKP,OAAO,CAACS,KAAK,IACjEH,IAAI,CAACI,SAAS,KAAKT,gBAAgB,CAACU,IAAI,IAAIL,IAAI,CAACI,SAAS,KAAKT,gBAAgB,CAACW,KAAM,EAAE;IACzF;EACJ;EACA,IAAIN,IAAI,CAACO,MAAM,CAACD,KAAK,GAAGR,SAAS,IAAIE,IAAI,CAACI,SAAS,KAAKT,gBAAgB,CAACU,IAAI,EAAE;IAC3EL,IAAI,CAACQ,QAAQ,CAACC,QAAQ,CAACC,CAAC,GAAGV,IAAI,CAACW,IAAI,GAAGX,IAAI,CAACY,MAAM,CAACF,CAAC;EACxD,CAAC,MACI,IAAIV,IAAI,CAACO,MAAM,CAACF,IAAI,GAAGL,IAAI,CAACa,UAAU,CAACC,KAAK,IAAId,IAAI,CAACI,SAAS,KAAKT,gBAAgB,CAACW,KAAK,EAAE;IAC5FN,IAAI,CAACQ,QAAQ,CAACC,QAAQ,CAACC,CAAC,GAAGV,IAAI,CAACa,UAAU,CAACC,KAAK,GAAGd,IAAI,CAACW,IAAI,GAAGX,IAAI,CAACY,MAAM,CAACF,CAAC;EAChF;EACA,MAAMK,QAAQ,GAAGf,IAAI,CAACQ,QAAQ,CAACO,QAAQ,CAACL,CAAC;EACzC,IAAIM,OAAO,GAAG,KAAK;EACnB,IAAKhB,IAAI,CAACI,SAAS,KAAKT,gBAAgB,CAACW,KAAK,IAC1CN,IAAI,CAACO,MAAM,CAACD,KAAK,IAAIN,IAAI,CAACa,UAAU,CAACC,KAAK,IAC1CC,QAAQ,GAAGlB,WAAW,IACrBG,IAAI,CAACI,SAAS,KAAKT,gBAAgB,CAACU,IAAI,IAAIL,IAAI,CAACO,MAAM,CAACF,IAAI,IAAIP,SAAS,IAAIiB,QAAQ,GAAGlB,WAAY,EAAE;IACvG,MAAMoB,WAAW,GAAGrB,aAAa,CAACI,IAAI,CAACQ,QAAQ,CAACU,OAAO,CAAChB,MAAM,CAACiB,UAAU,CAACC,KAAK,CAAC;IAChFpB,IAAI,CAACQ,QAAQ,CAACO,QAAQ,CAACL,CAAC,IAAI,CAACO,WAAW;IACxCD,OAAO,GAAG,IAAI;EAClB;EACA,IAAI,CAACA,OAAO,EAAE;IACV;EACJ;EACA,MAAMK,MAAM,GAAGrB,IAAI,CAACY,MAAM,CAACF,CAAC,GAAGV,IAAI,CAACW,IAAI;EACxC,IAAIX,IAAI,CAACO,MAAM,CAACD,KAAK,IAAIN,IAAI,CAACa,UAAU,CAACC,KAAK,IAAId,IAAI,CAACI,SAAS,KAAKT,gBAAgB,CAACW,KAAK,EAAE;IACzFN,IAAI,CAACQ,QAAQ,CAACC,QAAQ,CAACC,CAAC,GAAGV,IAAI,CAACa,UAAU,CAACC,KAAK,GAAGO,MAAM;EAC7D,CAAC,MACI,IAAIrB,IAAI,CAACO,MAAM,CAACF,IAAI,IAAIP,SAAS,IAAIE,IAAI,CAACI,SAAS,KAAKT,gBAAgB,CAACU,IAAI,EAAE;IAChFL,IAAI,CAACQ,QAAQ,CAACC,QAAQ,CAACC,CAAC,GAAGW,MAAM;EACrC;EACA,IAAIrB,IAAI,CAACC,OAAO,KAAKP,OAAO,CAACS,KAAK,EAAE;IAChCH,IAAI,CAACQ,QAAQ,CAACc,OAAO,CAAC,CAAC;EAC3B;AACJ;AACA,OAAO,SAASC,cAAcA,CAACvB,IAAI,EAAE;EACjC,IAAKA,IAAI,CAACC,OAAO,KAAKP,OAAO,CAACQ,MAAM,IAAIF,IAAI,CAACC,OAAO,KAAKP,OAAO,CAACS,KAAK,IACjEH,IAAI,CAACI,SAAS,KAAKT,gBAAgB,CAAC6B,MAAM,IAAIxB,IAAI,CAACI,SAAS,KAAKT,gBAAgB,CAAC8B,GAAI,EAAE;IACzF;EACJ;EACA,IAAIzB,IAAI,CAACO,MAAM,CAACiB,MAAM,GAAG1B,SAAS,IAAIE,IAAI,CAACI,SAAS,KAAKT,gBAAgB,CAAC8B,GAAG,EAAE;IAC3EzB,IAAI,CAACQ,QAAQ,CAACC,QAAQ,CAACiB,CAAC,GAAG1B,IAAI,CAACW,IAAI,GAAGX,IAAI,CAACY,MAAM,CAACc,CAAC;EACxD,CAAC,MACI,IAAI1B,IAAI,CAACO,MAAM,CAACkB,GAAG,GAAGzB,IAAI,CAACa,UAAU,CAACc,MAAM,IAAI3B,IAAI,CAACI,SAAS,KAAKT,gBAAgB,CAAC6B,MAAM,EAAE;IAC7FxB,IAAI,CAACQ,QAAQ,CAACC,QAAQ,CAACiB,CAAC,GAAG1B,IAAI,CAACa,UAAU,CAACc,MAAM,GAAG3B,IAAI,CAACW,IAAI,GAAGX,IAAI,CAACY,MAAM,CAACc,CAAC;EACjF;EACA,MAAMX,QAAQ,GAAGf,IAAI,CAACQ,QAAQ,CAACO,QAAQ,CAACW,CAAC;EACzC,IAAIV,OAAO,GAAG,KAAK;EACnB,IAAKhB,IAAI,CAACI,SAAS,KAAKT,gBAAgB,CAAC6B,MAAM,IAC3CxB,IAAI,CAACO,MAAM,CAACiB,MAAM,IAAIxB,IAAI,CAACa,UAAU,CAACc,MAAM,IAC5CZ,QAAQ,GAAGlB,WAAW,IACrBG,IAAI,CAACI,SAAS,KAAKT,gBAAgB,CAAC8B,GAAG,IAAIzB,IAAI,CAACO,MAAM,CAACkB,GAAG,IAAI3B,SAAS,IAAIiB,QAAQ,GAAGlB,WAAY,EAAE;IACrG,MAAMoB,WAAW,GAAGrB,aAAa,CAACI,IAAI,CAACQ,QAAQ,CAACU,OAAO,CAAChB,MAAM,CAAC0B,QAAQ,CAACR,KAAK,CAAC;IAC9EpB,IAAI,CAACQ,QAAQ,CAACO,QAAQ,CAACW,CAAC,IAAI,CAACT,WAAW;IACxCD,OAAO,GAAG,IAAI;EAClB;EACA,IAAI,CAACA,OAAO,EAAE;IACV;EACJ;EACA,MAAMK,MAAM,GAAGrB,IAAI,CAACY,MAAM,CAACc,CAAC,GAAG1B,IAAI,CAACW,IAAI;EACxC,IAAIX,IAAI,CAACO,MAAM,CAACiB,MAAM,IAAIxB,IAAI,CAACa,UAAU,CAACc,MAAM,IAAI3B,IAAI,CAACI,SAAS,KAAKT,gBAAgB,CAAC6B,MAAM,EAAE;IAC5FxB,IAAI,CAACQ,QAAQ,CAACC,QAAQ,CAACiB,CAAC,GAAG1B,IAAI,CAACa,UAAU,CAACc,MAAM,GAAGN,MAAM;EAC9D,CAAC,MACI,IAAIrB,IAAI,CAACO,MAAM,CAACkB,GAAG,IAAI3B,SAAS,IAAIE,IAAI,CAACI,SAAS,KAAKT,gBAAgB,CAAC8B,GAAG,EAAE;IAC9EzB,IAAI,CAACQ,QAAQ,CAACC,QAAQ,CAACiB,CAAC,GAAGL,MAAM;EACrC;EACA,IAAIrB,IAAI,CAACC,OAAO,KAAKP,OAAO,CAACS,KAAK,EAAE;IAChCH,IAAI,CAACQ,QAAQ,CAACc,OAAO,CAAC,CAAC;EAC3B;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
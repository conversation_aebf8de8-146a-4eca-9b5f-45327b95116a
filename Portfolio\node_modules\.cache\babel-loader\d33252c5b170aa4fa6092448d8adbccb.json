{"ast": null, "code": "import { RotateAnimation } from \"./RotateAnimation\";\nimport { ValueWithRandom } from \"../../ValueWithRandom\";\nexport class Rotate extends ValueWithRandom {\n  constructor() {\n    super();\n    this.animation = new RotateAnimation();\n    this.direction = \"clockwise\";\n    this.path = false;\n    this.value = 0;\n  }\n\n  load(data) {\n    if (!data) {\n      return;\n    }\n\n    super.load(data);\n\n    if (data.direction !== undefined) {\n      this.direction = data.direction;\n    }\n\n    this.animation.load(data.animation);\n\n    if (data.path !== undefined) {\n      this.path = data.path;\n    }\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Options/Classes/Particles/Rotate/Rotate.js"], "names": ["RotateAnimation", "ValueWithRandom", "Rotate", "constructor", "animation", "direction", "path", "value", "load", "data", "undefined"], "mappings": "AAAA,SAASA,eAAT,QAAgC,mBAAhC;AACA,SAASC,eAAT,QAAgC,uBAAhC;AACA,OAAO,MAAMC,MAAN,SAAqBD,eAArB,CAAqC;AACxCE,EAAAA,WAAW,GAAG;AACV;AACA,SAAKC,SAAL,GAAiB,IAAIJ,eAAJ,EAAjB;AACA,SAAKK,SAAL,GAAiB,WAAjB;AACA,SAAKC,IAAL,GAAY,KAAZ;AACA,SAAKC,KAAL,GAAa,CAAb;AACH;;AACDC,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,QAAI,CAACA,IAAL,EAAW;AACP;AACH;;AACD,UAAMD,IAAN,CAAWC,IAAX;;AACA,QAAIA,IAAI,CAACJ,SAAL,KAAmBK,SAAvB,EAAkC;AAC9B,WAAKL,SAAL,GAAiBI,IAAI,CAACJ,SAAtB;AACH;;AACD,SAAKD,SAAL,CAAeI,IAAf,CAAoBC,IAAI,CAACL,SAAzB;;AACA,QAAIK,IAAI,CAACH,IAAL,KAAcI,SAAlB,EAA6B;AACzB,WAAKJ,IAAL,GAAYG,IAAI,CAACH,IAAjB;AACH;AACJ;;AApBuC", "sourcesContent": ["import { RotateAnimation } from \"./RotateAnimation\";\nimport { ValueWithRandom } from \"../../ValueWithRandom\";\nexport class Rotate extends ValueWithRandom {\n    constructor() {\n        super();\n        this.animation = new RotateAnimation();\n        this.direction = \"clockwise\";\n        this.path = false;\n        this.value = 0;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        super.load(data);\n        if (data.direction !== undefined) {\n            this.direction = data.direction;\n        }\n        this.animation.load(data.animation);\n        if (data.path !== undefined) {\n            this.path = data.path;\n        }\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
{"ast": null, "code": "import { clamp } from \"@tsparticles/engine\";\nexport function calculateBubbleValue(particleValue, modeValue, optionsValue, ratio) {\n  if (modeValue >= optionsValue) {\n    const value = particleValue + (modeValue - optionsValue) * ratio;\n    return clamp(value, particleValue, modeValue);\n  } else if (modeValue < optionsValue) {\n    const value = particleValue - (optionsValue - modeValue) * ratio;\n    return clamp(value, modeValue, particleValue);\n  }\n}", "map": {"version": 3, "names": ["clamp", "calculateBubbleValue", "particleValue", "modeValue", "optionsValue", "ratio", "value"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/interaction-external-bubble/browser/Utils.js"], "sourcesContent": ["import { clamp } from \"@tsparticles/engine\";\nexport function calculateBubbleValue(particleValue, modeValue, optionsValue, ratio) {\n    if (modeValue >= optionsValue) {\n        const value = particleValue + (modeValue - optionsValue) * ratio;\n        return clamp(value, particleValue, modeValue);\n    }\n    else if (modeValue < optionsValue) {\n        const value = particleValue - (optionsValue - modeValue) * ratio;\n        return clamp(value, modeValue, particleValue);\n    }\n}\n"], "mappings": "AAAA,SAASA,KAAK,QAAQ,qBAAqB;AAC3C,OAAO,SAASC,oBAAoBA,CAACC,aAAa,EAAEC,SAAS,EAAEC,YAAY,EAAEC,KAAK,EAAE;EAChF,IAAIF,SAAS,IAAIC,YAAY,EAAE;IAC3B,MAAME,KAAK,GAAGJ,aAAa,GAAG,CAACC,SAAS,GAAGC,YAAY,IAAIC,KAAK;IAChE,OAAOL,KAAK,CAACM,KAAK,EAAEJ,aAAa,EAAEC,SAAS,CAAC;EACjD,CAAC,MACI,IAAIA,SAAS,GAAGC,YAAY,EAAE;IAC/B,MAAME,KAAK,GAAGJ,aAAa,GAAG,CAACE,YAAY,GAAGD,SAAS,IAAIE,KAAK;IAChE,OAAOL,KAAK,CAACM,KAAK,EAAEH,SAAS,EAAED,aAAa,CAAC;EACjD;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
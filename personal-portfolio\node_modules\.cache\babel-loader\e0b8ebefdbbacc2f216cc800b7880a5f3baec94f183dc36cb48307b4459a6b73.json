{"ast": null, "code": "import { OptionsColor } from \"@tsparticles/engine\";\nexport class LinksShadow {\n  constructor() {\n    this.blur = 5;\n    this.color = new OptionsColor();\n    this.color.value = \"#000\";\n    this.enable = false;\n  }\n  load(data) {\n    if (!data) {\n      return;\n    }\n    if (data.blur !== undefined) {\n      this.blur = data.blur;\n    }\n    this.color = OptionsColor.create(this.color, data.color);\n    if (data.enable !== undefined) {\n      this.enable = data.enable;\n    }\n  }\n}", "map": {"version": 3, "names": ["OptionsColor", "LinksShadow", "constructor", "blur", "color", "value", "enable", "load", "data", "undefined", "create"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/interaction-particles-links/browser/Options/Classes/LinksShadow.js"], "sourcesContent": ["import { OptionsColor } from \"@tsparticles/engine\";\nexport class LinksShadow {\n    constructor() {\n        this.blur = 5;\n        this.color = new OptionsColor();\n        this.color.value = \"#000\";\n        this.enable = false;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.blur !== undefined) {\n            this.blur = data.blur;\n        }\n        this.color = OptionsColor.create(this.color, data.color);\n        if (data.enable !== undefined) {\n            this.enable = data.enable;\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,qBAAqB;AAClD,OAAO,MAAMC,WAAW,CAAC;EACrBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,IAAI,GAAG,CAAC;IACb,IAAI,CAACC,KAAK,GAAG,IAAIJ,YAAY,CAAC,CAAC;IAC/B,IAAI,CAACI,KAAK,CAACC,KAAK,GAAG,MAAM;IACzB,IAAI,CAACC,MAAM,GAAG,KAAK;EACvB;EACAC,IAAIA,CAACC,IAAI,EAAE;IACP,IAAI,CAACA,IAAI,EAAE;MACP;IACJ;IACA,IAAIA,IAAI,CAACL,IAAI,KAAKM,SAAS,EAAE;MACzB,IAAI,CAACN,IAAI,GAAGK,IAAI,CAACL,IAAI;IACzB;IACA,IAAI,CAACC,KAAK,GAAGJ,YAAY,CAACU,MAAM,CAAC,IAAI,CAACN,KAAK,EAAEI,IAAI,CAACJ,KAAK,CAAC;IACxD,IAAII,IAAI,CAACF,MAAM,KAAKG,SAAS,EAAE;MAC3B,IAAI,CAACH,MAAM,GAAGE,IAAI,CAACF,MAAM;IAC7B;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { OptionsColor, setRangeValue } from \"@tsparticles/engine\";\nexport class TwinkleValues {\n  constructor() {\n    this.enable = false;\n    this.frequency = 0.05;\n    this.opacity = 1;\n  }\n  load(data) {\n    if (!data) {\n      return;\n    }\n    if (data.color !== undefined) {\n      this.color = OptionsColor.create(this.color, data.color);\n    }\n    if (data.enable !== undefined) {\n      this.enable = data.enable;\n    }\n    if (data.frequency !== undefined) {\n      this.frequency = data.frequency;\n    }\n    if (data.opacity !== undefined) {\n      this.opacity = setRangeValue(data.opacity);\n    }\n  }\n}", "map": {"version": 3, "names": ["OptionsColor", "setRangeValue", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "constructor", "enable", "frequency", "opacity", "load", "data", "color", "undefined", "create"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/updater-twinkle/browser/Options/Classes/TwinkleValues.js"], "sourcesContent": ["import { OptionsColor, setRangeValue, } from \"@tsparticles/engine\";\nexport class TwinkleValues {\n    constructor() {\n        this.enable = false;\n        this.frequency = 0.05;\n        this.opacity = 1;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.color !== undefined) {\n            this.color = OptionsColor.create(this.color, data.color);\n        }\n        if (data.enable !== undefined) {\n            this.enable = data.enable;\n        }\n        if (data.frequency !== undefined) {\n            this.frequency = data.frequency;\n        }\n        if (data.opacity !== undefined) {\n            this.opacity = setRangeValue(data.opacity);\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,YAAY,EAAEC,aAAa,QAAS,qBAAqB;AAClE,OAAO,MAAMC,aAAa,CAAC;EACvBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,MAAM,GAAG,KAAK;IACnB,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,OAAO,GAAG,CAAC;EACpB;EACAC,IAAIA,CAACC,IAAI,EAAE;IACP,IAAI,CAACA,IAAI,EAAE;MACP;IACJ;IACA,IAAIA,IAAI,CAACC,KAAK,KAAKC,SAAS,EAAE;MAC1B,IAAI,CAACD,KAAK,GAAGT,YAAY,CAACW,MAAM,CAAC,IAAI,CAACF,KAAK,EAAED,IAAI,CAACC,KAAK,CAAC;IAC5D;IACA,IAAID,IAAI,CAACJ,MAAM,KAAKM,SAAS,EAAE;MAC3B,IAAI,CAACN,MAAM,GAAGI,IAAI,CAACJ,MAAM;IAC7B;IACA,IAAII,IAAI,CAACH,SAAS,KAAKK,SAAS,EAAE;MAC9B,IAAI,CAACL,SAAS,GAAGG,IAAI,CAACH,SAAS;IACnC;IACA,IAAIG,IAAI,CAACF,OAAO,KAAKI,SAAS,EAAE;MAC5B,IAAI,CAACJ,OAAO,GAAGL,aAAa,CAACO,IAAI,CAACF,OAAO,CAAC;IAC9C;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
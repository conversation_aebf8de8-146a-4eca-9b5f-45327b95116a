{"ast": null, "code": "import { errorPrefix, getLogger, getStyleFromHsl } from \"@tsparticles/engine\";\nconst stringStart = 0,\n  defaultOpacity = 1;\nconst currentColorRegex = /(#(?:[0-9a-f]{2}){2,4}|(#[0-9a-f]{3})|(rgb|hsl)a?\\((-?\\d+%?[,\\s]+){2,3}\\s*[\\d.]+%?\\))|currentcolor/gi;\nfunction replaceColorSvg(imageShape, color, opacity) {\n  const {\n    svgData\n  } = imageShape;\n  if (!svgData) {\n    return \"\";\n  }\n  const colorStyle = getStyleFromHsl(color, opacity);\n  if (svgData.includes(\"fill\")) {\n    return svgData.replace(currentColorRegex, () => colorStyle);\n  }\n  const preFillIndex = svgData.indexOf(\">\");\n  return `${svgData.substring(stringStart, preFillIndex)} fill=\"${colorStyle}\"${svgData.substring(preFillIndex)}`;\n}\nexport async function loadImage(image) {\n  return new Promise(resolve => {\n    image.loading = true;\n    const img = new Image();\n    image.element = img;\n    img.addEventListener(\"load\", () => {\n      image.loading = false;\n      resolve();\n    });\n    img.addEventListener(\"error\", () => {\n      image.element = undefined;\n      image.error = true;\n      image.loading = false;\n      getLogger().error(`${errorPrefix} loading image: ${image.source}`);\n      resolve();\n    });\n    img.src = image.source;\n  });\n}\nexport async function downloadSvgImage(image) {\n  if (image.type !== \"svg\") {\n    await loadImage(image);\n    return;\n  }\n  image.loading = true;\n  const response = await fetch(image.source);\n  if (!response.ok) {\n    getLogger().error(`${errorPrefix} Image not found`);\n    image.error = true;\n  } else {\n    image.svgData = await response.text();\n  }\n  image.loading = false;\n}\nexport function replaceImageColor(image, imageData, color, particle) {\n  const svgColoredData = replaceColorSvg(image, color, particle.opacity?.value ?? defaultOpacity),\n    imageRes = {\n      color,\n      gif: imageData.gif,\n      data: {\n        ...image,\n        svgData: svgColoredData\n      },\n      loaded: false,\n      ratio: imageData.width / imageData.height,\n      replaceColor: imageData.replaceColor,\n      source: imageData.src\n    };\n  return new Promise(resolve => {\n    const svg = new Blob([svgColoredData], {\n        type: \"image/svg+xml\"\n      }),\n      domUrl = URL || window.URL || window.webkitURL || window,\n      url = domUrl.createObjectURL(svg),\n      img = new Image();\n    img.addEventListener(\"load\", () => {\n      imageRes.loaded = true;\n      imageRes.element = img;\n      resolve(imageRes);\n      domUrl.revokeObjectURL(url);\n    });\n    const errorHandler = async () => {\n      domUrl.revokeObjectURL(url);\n      const img2 = {\n        ...image,\n        error: false,\n        loading: true\n      };\n      await loadImage(img2);\n      imageRes.loaded = true;\n      imageRes.element = img2.element;\n      resolve(imageRes);\n    };\n    img.addEventListener(\"error\", () => void errorHandler());\n    img.src = url;\n  });\n}", "map": {"version": 3, "names": ["errorPrefix", "<PERSON><PERSON><PERSON><PERSON>", "getStyleFromHsl", "stringStart", "defaultOpacity", "currentColorRegex", "replaceColorSvg", "imageShape", "color", "opacity", "svgData", "colorStyle", "includes", "replace", "preFillIndex", "indexOf", "substring", "loadImage", "image", "Promise", "resolve", "loading", "img", "Image", "element", "addEventListener", "undefined", "error", "source", "src", "downloadSvgImage", "type", "response", "fetch", "ok", "text", "replaceImageColor", "imageData", "particle", "svgColoredData", "value", "imageRes", "gif", "data", "loaded", "ratio", "width", "height", "replaceColor", "svg", "Blob", "domUrl", "URL", "window", "webkitURL", "url", "createObjectURL", "revokeObjectURL", "<PERSON><PERSON><PERSON><PERSON>", "img2"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/shape-image/browser/Utils.js"], "sourcesContent": ["import { errorPrefix, getLogger, getStyleFromHsl } from \"@tsparticles/engine\";\nconst stringStart = 0, defaultOpacity = 1;\nconst currentColorRegex = /(#(?:[0-9a-f]{2}){2,4}|(#[0-9a-f]{3})|(rgb|hsl)a?\\((-?\\d+%?[,\\s]+){2,3}\\s*[\\d.]+%?\\))|currentcolor/gi;\nfunction replaceColorSvg(imageShape, color, opacity) {\n    const { svgData } = imageShape;\n    if (!svgData) {\n        return \"\";\n    }\n    const colorStyle = getStyleFromHsl(color, opacity);\n    if (svgData.includes(\"fill\")) {\n        return svgData.replace(currentColorRegex, () => colorStyle);\n    }\n    const preFillIndex = svgData.indexOf(\">\");\n    return `${svgData.substring(stringStart, preFillIndex)} fill=\"${colorStyle}\"${svgData.substring(preFillIndex)}`;\n}\nexport async function loadImage(image) {\n    return new Promise((resolve) => {\n        image.loading = true;\n        const img = new Image();\n        image.element = img;\n        img.addEventListener(\"load\", () => {\n            image.loading = false;\n            resolve();\n        });\n        img.addEventListener(\"error\", () => {\n            image.element = undefined;\n            image.error = true;\n            image.loading = false;\n            getLogger().error(`${errorPrefix} loading image: ${image.source}`);\n            resolve();\n        });\n        img.src = image.source;\n    });\n}\nexport async function downloadSvgImage(image) {\n    if (image.type !== \"svg\") {\n        await loadImage(image);\n        return;\n    }\n    image.loading = true;\n    const response = await fetch(image.source);\n    if (!response.ok) {\n        getLogger().error(`${errorPrefix} Image not found`);\n        image.error = true;\n    }\n    else {\n        image.svgData = await response.text();\n    }\n    image.loading = false;\n}\nexport function replaceImageColor(image, imageData, color, particle) {\n    const svgColoredData = replaceColorSvg(image, color, particle.opacity?.value ?? defaultOpacity), imageRes = {\n        color,\n        gif: imageData.gif,\n        data: {\n            ...image,\n            svgData: svgColoredData,\n        },\n        loaded: false,\n        ratio: imageData.width / imageData.height,\n        replaceColor: imageData.replaceColor,\n        source: imageData.src,\n    };\n    return new Promise(resolve => {\n        const svg = new Blob([svgColoredData], { type: \"image/svg+xml\" }), domUrl = URL || window.URL || window.webkitURL || window, url = domUrl.createObjectURL(svg), img = new Image();\n        img.addEventListener(\"load\", () => {\n            imageRes.loaded = true;\n            imageRes.element = img;\n            resolve(imageRes);\n            domUrl.revokeObjectURL(url);\n        });\n        const errorHandler = async () => {\n            domUrl.revokeObjectURL(url);\n            const img2 = {\n                ...image,\n                error: false,\n                loading: true,\n            };\n            await loadImage(img2);\n            imageRes.loaded = true;\n            imageRes.element = img2.element;\n            resolve(imageRes);\n        };\n        img.addEventListener(\"error\", () => void errorHandler());\n        img.src = url;\n    });\n}\n"], "mappings": "AAAA,SAASA,WAAW,EAAEC,SAAS,EAAEC,eAAe,QAAQ,qBAAqB;AAC7E,MAAMC,WAAW,GAAG,CAAC;EAAEC,cAAc,GAAG,CAAC;AACzC,MAAMC,iBAAiB,GAAG,sGAAsG;AAChI,SAASC,eAAeA,CAACC,UAAU,EAAEC,KAAK,EAAEC,OAAO,EAAE;EACjD,MAAM;IAAEC;EAAQ,CAAC,GAAGH,UAAU;EAC9B,IAAI,CAACG,OAAO,EAAE;IACV,OAAO,EAAE;EACb;EACA,MAAMC,UAAU,GAAGT,eAAe,CAACM,KAAK,EAAEC,OAAO,CAAC;EAClD,IAAIC,OAAO,CAACE,QAAQ,CAAC,MAAM,CAAC,EAAE;IAC1B,OAAOF,OAAO,CAACG,OAAO,CAACR,iBAAiB,EAAE,MAAMM,UAAU,CAAC;EAC/D;EACA,MAAMG,YAAY,GAAGJ,OAAO,CAACK,OAAO,CAAC,GAAG,CAAC;EACzC,OAAO,GAAGL,OAAO,CAACM,SAAS,CAACb,WAAW,EAAEW,YAAY,CAAC,UAAUH,UAAU,IAAID,OAAO,CAACM,SAAS,CAACF,YAAY,CAAC,EAAE;AACnH;AACA,OAAO,eAAeG,SAASA,CAACC,KAAK,EAAE;EACnC,OAAO,IAAIC,OAAO,CAAEC,OAAO,IAAK;IAC5BF,KAAK,CAACG,OAAO,GAAG,IAAI;IACpB,MAAMC,GAAG,GAAG,IAAIC,KAAK,CAAC,CAAC;IACvBL,KAAK,CAACM,OAAO,GAAGF,GAAG;IACnBA,GAAG,CAACG,gBAAgB,CAAC,MAAM,EAAE,MAAM;MAC/BP,KAAK,CAACG,OAAO,GAAG,KAAK;MACrBD,OAAO,CAAC,CAAC;IACb,CAAC,CAAC;IACFE,GAAG,CAACG,gBAAgB,CAAC,OAAO,EAAE,MAAM;MAChCP,KAAK,CAACM,OAAO,GAAGE,SAAS;MACzBR,KAAK,CAACS,KAAK,GAAG,IAAI;MAClBT,KAAK,CAACG,OAAO,GAAG,KAAK;MACrBpB,SAAS,CAAC,CAAC,CAAC0B,KAAK,CAAC,GAAG3B,WAAW,mBAAmBkB,KAAK,CAACU,MAAM,EAAE,CAAC;MAClER,OAAO,CAAC,CAAC;IACb,CAAC,CAAC;IACFE,GAAG,CAACO,GAAG,GAAGX,KAAK,CAACU,MAAM;EAC1B,CAAC,CAAC;AACN;AACA,OAAO,eAAeE,gBAAgBA,CAACZ,KAAK,EAAE;EAC1C,IAAIA,KAAK,CAACa,IAAI,KAAK,KAAK,EAAE;IACtB,MAAMd,SAAS,CAACC,KAAK,CAAC;IACtB;EACJ;EACAA,KAAK,CAACG,OAAO,GAAG,IAAI;EACpB,MAAMW,QAAQ,GAAG,MAAMC,KAAK,CAACf,KAAK,CAACU,MAAM,CAAC;EAC1C,IAAI,CAACI,QAAQ,CAACE,EAAE,EAAE;IACdjC,SAAS,CAAC,CAAC,CAAC0B,KAAK,CAAC,GAAG3B,WAAW,kBAAkB,CAAC;IACnDkB,KAAK,CAACS,KAAK,GAAG,IAAI;EACtB,CAAC,MACI;IACDT,KAAK,CAACR,OAAO,GAAG,MAAMsB,QAAQ,CAACG,IAAI,CAAC,CAAC;EACzC;EACAjB,KAAK,CAACG,OAAO,GAAG,KAAK;AACzB;AACA,OAAO,SAASe,iBAAiBA,CAAClB,KAAK,EAAEmB,SAAS,EAAE7B,KAAK,EAAE8B,QAAQ,EAAE;EACjE,MAAMC,cAAc,GAAGjC,eAAe,CAACY,KAAK,EAAEV,KAAK,EAAE8B,QAAQ,CAAC7B,OAAO,EAAE+B,KAAK,IAAIpC,cAAc,CAAC;IAAEqC,QAAQ,GAAG;MACxGjC,KAAK;MACLkC,GAAG,EAAEL,SAAS,CAACK,GAAG;MAClBC,IAAI,EAAE;QACF,GAAGzB,KAAK;QACRR,OAAO,EAAE6B;MACb,CAAC;MACDK,MAAM,EAAE,KAAK;MACbC,KAAK,EAAER,SAAS,CAACS,KAAK,GAAGT,SAAS,CAACU,MAAM;MACzCC,YAAY,EAAEX,SAAS,CAACW,YAAY;MACpCpB,MAAM,EAAES,SAAS,CAACR;IACtB,CAAC;EACD,OAAO,IAAIV,OAAO,CAACC,OAAO,IAAI;IAC1B,MAAM6B,GAAG,GAAG,IAAIC,IAAI,CAAC,CAACX,cAAc,CAAC,EAAE;QAAER,IAAI,EAAE;MAAgB,CAAC,CAAC;MAAEoB,MAAM,GAAGC,GAAG,IAAIC,MAAM,CAACD,GAAG,IAAIC,MAAM,CAACC,SAAS,IAAID,MAAM;MAAEE,GAAG,GAAGJ,MAAM,CAACK,eAAe,CAACP,GAAG,CAAC;MAAE3B,GAAG,GAAG,IAAIC,KAAK,CAAC,CAAC;IACjLD,GAAG,CAACG,gBAAgB,CAAC,MAAM,EAAE,MAAM;MAC/BgB,QAAQ,CAACG,MAAM,GAAG,IAAI;MACtBH,QAAQ,CAACjB,OAAO,GAAGF,GAAG;MACtBF,OAAO,CAACqB,QAAQ,CAAC;MACjBU,MAAM,CAACM,eAAe,CAACF,GAAG,CAAC;IAC/B,CAAC,CAAC;IACF,MAAMG,YAAY,GAAG,MAAAA,CAAA,KAAY;MAC7BP,MAAM,CAACM,eAAe,CAACF,GAAG,CAAC;MAC3B,MAAMI,IAAI,GAAG;QACT,GAAGzC,KAAK;QACRS,KAAK,EAAE,KAAK;QACZN,OAAO,EAAE;MACb,CAAC;MACD,MAAMJ,SAAS,CAAC0C,IAAI,CAAC;MACrBlB,QAAQ,CAACG,MAAM,GAAG,IAAI;MACtBH,QAAQ,CAACjB,OAAO,GAAGmC,IAAI,CAACnC,OAAO;MAC/BJ,OAAO,CAACqB,QAAQ,CAAC;IACrB,CAAC;IACDnB,GAAG,CAACG,gBAAgB,CAAC,OAAO,EAAE,MAAM,KAAKiC,YAAY,CAAC,CAAC,CAAC;IACxDpC,GAAG,CAACO,GAAG,GAAG0B,GAAG;EACjB,CAAC,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
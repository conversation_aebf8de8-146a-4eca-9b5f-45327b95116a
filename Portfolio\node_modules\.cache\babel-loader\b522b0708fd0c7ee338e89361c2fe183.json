{"ast": null, "code": "import buildLocalizeFn from \"../../../_lib/buildLocalizeFn/index.js\";\nvar eraValues = {\n  narrow: ['B', 'A'],\n  abbreviated: ['BC', 'AD'],\n  wide: ['Before <PERSON>', '<PERSON><PERSON>']\n};\nvar quarterValues = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['Q1', 'Q2', 'Q3', 'Q4'],\n  wide: ['1st quarter', '2nd quarter', '3rd quarter', '4th quarter']\n}; // Note: in English, the names of days of the week and months are capitalized.\n// If you are making a new locale based on this one, check if the same is true for the language you're working on.\n// Generally, formatted dates should look like they are in the middle of a sentence,\n// e.g. in Spanish language the weekdays and months should be in the lowercase.\n\nvar monthValues = {\n  narrow: ['J', 'F', 'M', 'A', 'M', 'J', 'J', 'A', 'S', 'O', 'N', 'D'],\n  abbreviated: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],\n  wide: ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December']\n};\nvar dayValues = {\n  narrow: ['S', 'M', 'T', 'W', 'T', 'F', 'S'],\n  short: ['Su', 'Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa'],\n  abbreviated: ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'],\n  wide: ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']\n};\nvar dayPeriodValues = {\n  narrow: {\n    am: 'a',\n    pm: 'p',\n    midnight: 'mi',\n    noon: 'n',\n    morning: 'morning',\n    afternoon: 'afternoon',\n    evening: 'evening',\n    night: 'night'\n  },\n  abbreviated: {\n    am: 'AM',\n    pm: 'PM',\n    midnight: 'midnight',\n    noon: 'noon',\n    morning: 'morning',\n    afternoon: 'afternoon',\n    evening: 'evening',\n    night: 'night'\n  },\n  wide: {\n    am: 'a.m.',\n    pm: 'p.m.',\n    midnight: 'midnight',\n    noon: 'noon',\n    morning: 'morning',\n    afternoon: 'afternoon',\n    evening: 'evening',\n    night: 'night'\n  }\n};\nvar formattingDayPeriodValues = {\n  narrow: {\n    am: 'a',\n    pm: 'p',\n    midnight: 'mi',\n    noon: 'n',\n    morning: 'in the morning',\n    afternoon: 'in the afternoon',\n    evening: 'in the evening',\n    night: 'at night'\n  },\n  abbreviated: {\n    am: 'AM',\n    pm: 'PM',\n    midnight: 'midnight',\n    noon: 'noon',\n    morning: 'in the morning',\n    afternoon: 'in the afternoon',\n    evening: 'in the evening',\n    night: 'at night'\n  },\n  wide: {\n    am: 'a.m.',\n    pm: 'p.m.',\n    midnight: 'midnight',\n    noon: 'noon',\n    morning: 'in the morning',\n    afternoon: 'in the afternoon',\n    evening: 'in the evening',\n    night: 'at night'\n  }\n};\n\nvar ordinalNumber = function (dirtyNumber, _options) {\n  var number = Number(dirtyNumber); // If ordinal numbers depend on context, for example,\n  // if they are different for different grammatical genders,\n  // use `options.unit`.\n  //\n  // `unit` can be 'year', 'quarter', 'month', 'week', 'date', 'dayOfYear',\n  // 'day', 'hour', 'minute', 'second'.\n\n  var rem100 = number % 100;\n\n  if (rem100 > 20 || rem100 < 10) {\n    switch (rem100 % 10) {\n      case 1:\n        return number + 'st';\n\n      case 2:\n        return number + 'nd';\n\n      case 3:\n        return number + 'rd';\n    }\n  }\n\n  return number + 'th';\n};\n\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function (quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: 'wide'\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: 'wide',\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: 'wide'\n  })\n};\nexport default localize;", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/date-fns/esm/locale/en-US/_lib/localize/index.js"], "names": ["buildLocalizeFn", "<PERSON><PERSON><PERSON><PERSON>", "narrow", "abbreviated", "wide", "quarterValues", "month<PERSON><PERSON><PERSON>", "dayV<PERSON><PERSON>", "short", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "formattingDayPeriodValues", "ordinalNumber", "dirtyNumber", "_options", "number", "Number", "rem100", "localize", "era", "values", "defaultWidth", "quarter", "argument<PERSON>allback", "month", "day", "<PERSON><PERSON><PERSON><PERSON>", "formattingValues", "defaultFormattingWidth"], "mappings": "AAAA,OAAOA,eAAP,MAA4B,wCAA5B;AACA,IAAIC,SAAS,GAAG;AACdC,EAAAA,MAAM,EAAE,CAAC,GAAD,EAAM,GAAN,CADM;AAEdC,EAAAA,WAAW,EAAE,CAAC,IAAD,EAAO,IAAP,CAFC;AAGdC,EAAAA,IAAI,EAAE,CAAC,eAAD,EAAkB,aAAlB;AAHQ,CAAhB;AAKA,IAAIC,aAAa,GAAG;AAClBH,EAAAA,MAAM,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,EAAgB,GAAhB,CADU;AAElBC,EAAAA,WAAW,EAAE,CAAC,IAAD,EAAO,IAAP,EAAa,IAAb,EAAmB,IAAnB,CAFK;AAGlBC,EAAAA,IAAI,EAAE,CAAC,aAAD,EAAgB,aAAhB,EAA+B,aAA/B,EAA8C,aAA9C;AAHY,CAApB,C,CAIG;AACH;AACA;AACA;;AAEA,IAAIE,WAAW,GAAG;AAChBJ,EAAAA,MAAM,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,EAAgB,GAAhB,EAAqB,GAArB,EAA0B,GAA1B,EAA+B,GAA/B,EAAoC,GAApC,EAAyC,GAAzC,EAA8C,GAA9C,EAAmD,GAAnD,EAAwD,GAAxD,CADQ;AAEhBC,EAAAA,WAAW,EAAE,CAAC,KAAD,EAAQ,KAAR,EAAe,KAAf,EAAsB,KAAtB,EAA6B,KAA7B,EAAoC,KAApC,EAA2C,KAA3C,EAAkD,KAAlD,EAAyD,KAAzD,EAAgE,KAAhE,EAAuE,KAAvE,EAA8E,KAA9E,CAFG;AAGhBC,EAAAA,IAAI,EAAE,CAAC,SAAD,EAAY,UAAZ,EAAwB,OAAxB,EAAiC,OAAjC,EAA0C,KAA1C,EAAiD,MAAjD,EAAyD,MAAzD,EAAiE,QAAjE,EAA2E,WAA3E,EAAwF,SAAxF,EAAmG,UAAnG,EAA+G,UAA/G;AAHU,CAAlB;AAKA,IAAIG,SAAS,GAAG;AACdL,EAAAA,MAAM,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,EAAgB,GAAhB,EAAqB,GAArB,EAA0B,GAA1B,EAA+B,GAA/B,CADM;AAEdM,EAAAA,KAAK,EAAE,CAAC,IAAD,EAAO,IAAP,EAAa,IAAb,EAAmB,IAAnB,EAAyB,IAAzB,EAA+B,IAA/B,EAAqC,IAArC,CAFO;AAGdL,EAAAA,WAAW,EAAE,CAAC,KAAD,EAAQ,KAAR,EAAe,KAAf,EAAsB,KAAtB,EAA6B,KAA7B,EAAoC,KAApC,EAA2C,KAA3C,CAHC;AAIdC,EAAAA,IAAI,EAAE,CAAC,QAAD,EAAW,QAAX,EAAqB,SAArB,EAAgC,WAAhC,EAA6C,UAA7C,EAAyD,QAAzD,EAAmE,UAAnE;AAJQ,CAAhB;AAMA,IAAIK,eAAe,GAAG;AACpBP,EAAAA,MAAM,EAAE;AACNQ,IAAAA,EAAE,EAAE,GADE;AAENC,IAAAA,EAAE,EAAE,GAFE;AAGNC,IAAAA,QAAQ,EAAE,IAHJ;AAINC,IAAAA,IAAI,EAAE,GAJA;AAKNC,IAAAA,OAAO,EAAE,SALH;AAMNC,IAAAA,SAAS,EAAE,WANL;AAONC,IAAAA,OAAO,EAAE,SAPH;AAQNC,IAAAA,KAAK,EAAE;AARD,GADY;AAWpBd,EAAAA,WAAW,EAAE;AACXO,IAAAA,EAAE,EAAE,IADO;AAEXC,IAAAA,EAAE,EAAE,IAFO;AAGXC,IAAAA,QAAQ,EAAE,UAHC;AAIXC,IAAAA,IAAI,EAAE,MAJK;AAKXC,IAAAA,OAAO,EAAE,SALE;AAMXC,IAAAA,SAAS,EAAE,WANA;AAOXC,IAAAA,OAAO,EAAE,SAPE;AAQXC,IAAAA,KAAK,EAAE;AARI,GAXO;AAqBpBb,EAAAA,IAAI,EAAE;AACJM,IAAAA,EAAE,EAAE,MADA;AAEJC,IAAAA,EAAE,EAAE,MAFA;AAGJC,IAAAA,QAAQ,EAAE,UAHN;AAIJC,IAAAA,IAAI,EAAE,MAJF;AAKJC,IAAAA,OAAO,EAAE,SALL;AAMJC,IAAAA,SAAS,EAAE,WANP;AAOJC,IAAAA,OAAO,EAAE,SAPL;AAQJC,IAAAA,KAAK,EAAE;AARH;AArBc,CAAtB;AAgCA,IAAIC,yBAAyB,GAAG;AAC9BhB,EAAAA,MAAM,EAAE;AACNQ,IAAAA,EAAE,EAAE,GADE;AAENC,IAAAA,EAAE,EAAE,GAFE;AAGNC,IAAAA,QAAQ,EAAE,IAHJ;AAINC,IAAAA,IAAI,EAAE,GAJA;AAKNC,IAAAA,OAAO,EAAE,gBALH;AAMNC,IAAAA,SAAS,EAAE,kBANL;AAONC,IAAAA,OAAO,EAAE,gBAPH;AAQNC,IAAAA,KAAK,EAAE;AARD,GADsB;AAW9Bd,EAAAA,WAAW,EAAE;AACXO,IAAAA,EAAE,EAAE,IADO;AAEXC,IAAAA,EAAE,EAAE,IAFO;AAGXC,IAAAA,QAAQ,EAAE,UAHC;AAIXC,IAAAA,IAAI,EAAE,MAJK;AAKXC,IAAAA,OAAO,EAAE,gBALE;AAMXC,IAAAA,SAAS,EAAE,kBANA;AAOXC,IAAAA,OAAO,EAAE,gBAPE;AAQXC,IAAAA,KAAK,EAAE;AARI,GAXiB;AAqB9Bb,EAAAA,IAAI,EAAE;AACJM,IAAAA,EAAE,EAAE,MADA;AAEJC,IAAAA,EAAE,EAAE,MAFA;AAGJC,IAAAA,QAAQ,EAAE,UAHN;AAIJC,IAAAA,IAAI,EAAE,MAJF;AAKJC,IAAAA,OAAO,EAAE,gBALL;AAMJC,IAAAA,SAAS,EAAE,kBANP;AAOJC,IAAAA,OAAO,EAAE,gBAPL;AAQJC,IAAAA,KAAK,EAAE;AARH;AArBwB,CAAhC;;AAiCA,IAAIE,aAAa,GAAG,UAAUC,WAAV,EAAuBC,QAAvB,EAAiC;AACnD,MAAIC,MAAM,GAAGC,MAAM,CAACH,WAAD,CAAnB,CADmD,CACjB;AAClC;AACA;AACA;AACA;AACA;;AAEA,MAAII,MAAM,GAAGF,MAAM,GAAG,GAAtB;;AAEA,MAAIE,MAAM,GAAG,EAAT,IAAeA,MAAM,GAAG,EAA5B,EAAgC;AAC9B,YAAQA,MAAM,GAAG,EAAjB;AACE,WAAK,CAAL;AACE,eAAOF,MAAM,GAAG,IAAhB;;AAEF,WAAK,CAAL;AACE,eAAOA,MAAM,GAAG,IAAhB;;AAEF,WAAK,CAAL;AACE,eAAOA,MAAM,GAAG,IAAhB;AARJ;AAUD;;AAED,SAAOA,MAAM,GAAG,IAAhB;AACD,CAxBD;;AA0BA,IAAIG,QAAQ,GAAG;AACbN,EAAAA,aAAa,EAAEA,aADF;AAEbO,EAAAA,GAAG,EAAE1B,eAAe,CAAC;AACnB2B,IAAAA,MAAM,EAAE1B,SADW;AAEnB2B,IAAAA,YAAY,EAAE;AAFK,GAAD,CAFP;AAMbC,EAAAA,OAAO,EAAE7B,eAAe,CAAC;AACvB2B,IAAAA,MAAM,EAAEtB,aADe;AAEvBuB,IAAAA,YAAY,EAAE,MAFS;AAGvBE,IAAAA,gBAAgB,EAAE,UAAUD,OAAV,EAAmB;AACnC,aAAOA,OAAO,GAAG,CAAjB;AACD;AALsB,GAAD,CANX;AAabE,EAAAA,KAAK,EAAE/B,eAAe,CAAC;AACrB2B,IAAAA,MAAM,EAAErB,WADa;AAErBsB,IAAAA,YAAY,EAAE;AAFO,GAAD,CAbT;AAiBbI,EAAAA,GAAG,EAAEhC,eAAe,CAAC;AACnB2B,IAAAA,MAAM,EAAEpB,SADW;AAEnBqB,IAAAA,YAAY,EAAE;AAFK,GAAD,CAjBP;AAqBbK,EAAAA,SAAS,EAAEjC,eAAe,CAAC;AACzB2B,IAAAA,MAAM,EAAElB,eADiB;AAEzBmB,IAAAA,YAAY,EAAE,MAFW;AAGzBM,IAAAA,gBAAgB,EAAEhB,yBAHO;AAIzBiB,IAAAA,sBAAsB,EAAE;AAJC,GAAD;AArBb,CAAf;AA4BA,eAAeV,QAAf", "sourcesContent": ["import buildLocalizeFn from \"../../../_lib/buildLocalizeFn/index.js\";\nvar eraValues = {\n  narrow: ['B', 'A'],\n  abbreviated: ['BC', 'AD'],\n  wide: ['Before <PERSON>', '<PERSON><PERSON>']\n};\nvar quarterValues = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['Q1', 'Q2', 'Q3', 'Q4'],\n  wide: ['1st quarter', '2nd quarter', '3rd quarter', '4th quarter']\n}; // Note: in English, the names of days of the week and months are capitalized.\n// If you are making a new locale based on this one, check if the same is true for the language you're working on.\n// Generally, formatted dates should look like they are in the middle of a sentence,\n// e.g. in Spanish language the weekdays and months should be in the lowercase.\n\nvar monthValues = {\n  narrow: ['J', 'F', 'M', 'A', 'M', 'J', 'J', 'A', 'S', 'O', 'N', 'D'],\n  abbreviated: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],\n  wide: ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December']\n};\nvar dayValues = {\n  narrow: ['S', 'M', 'T', 'W', 'T', 'F', 'S'],\n  short: ['Su', 'Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa'],\n  abbreviated: ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'],\n  wide: ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']\n};\nvar dayPeriodValues = {\n  narrow: {\n    am: 'a',\n    pm: 'p',\n    midnight: 'mi',\n    noon: 'n',\n    morning: 'morning',\n    afternoon: 'afternoon',\n    evening: 'evening',\n    night: 'night'\n  },\n  abbreviated: {\n    am: 'AM',\n    pm: 'PM',\n    midnight: 'midnight',\n    noon: 'noon',\n    morning: 'morning',\n    afternoon: 'afternoon',\n    evening: 'evening',\n    night: 'night'\n  },\n  wide: {\n    am: 'a.m.',\n    pm: 'p.m.',\n    midnight: 'midnight',\n    noon: 'noon',\n    morning: 'morning',\n    afternoon: 'afternoon',\n    evening: 'evening',\n    night: 'night'\n  }\n};\nvar formattingDayPeriodValues = {\n  narrow: {\n    am: 'a',\n    pm: 'p',\n    midnight: 'mi',\n    noon: 'n',\n    morning: 'in the morning',\n    afternoon: 'in the afternoon',\n    evening: 'in the evening',\n    night: 'at night'\n  },\n  abbreviated: {\n    am: 'AM',\n    pm: 'PM',\n    midnight: 'midnight',\n    noon: 'noon',\n    morning: 'in the morning',\n    afternoon: 'in the afternoon',\n    evening: 'in the evening',\n    night: 'at night'\n  },\n  wide: {\n    am: 'a.m.',\n    pm: 'p.m.',\n    midnight: 'midnight',\n    noon: 'noon',\n    morning: 'in the morning',\n    afternoon: 'in the afternoon',\n    evening: 'in the evening',\n    night: 'at night'\n  }\n};\n\nvar ordinalNumber = function (dirtyNumber, _options) {\n  var number = Number(dirtyNumber); // If ordinal numbers depend on context, for example,\n  // if they are different for different grammatical genders,\n  // use `options.unit`.\n  //\n  // `unit` can be 'year', 'quarter', 'month', 'week', 'date', 'dayOfYear',\n  // 'day', 'hour', 'minute', 'second'.\n\n  var rem100 = number % 100;\n\n  if (rem100 > 20 || rem100 < 10) {\n    switch (rem100 % 10) {\n      case 1:\n        return number + 'st';\n\n      case 2:\n        return number + 'nd';\n\n      case 3:\n        return number + 'rd';\n    }\n  }\n\n  return number + 'th';\n};\n\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function (quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: 'wide'\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: 'wide',\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: 'wide'\n  })\n};\nexport default localize;"]}, "metadata": {}, "sourceType": "module"}
{"ast": null, "code": "import { LifeUpdater } from \"./LifeUpdater\";\nexport async function loadLifeUpdater(engine, refresh = true) {\n  await engine.addParticleUpdater(\"life\", container => new LifeUpdater(container), refresh);\n}", "map": {"version": 3, "names": ["LifeUpdater", "loadLifeUpdater", "engine", "refresh", "addParticleUpdater", "container"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/tsparticles-updater-life/esm/index.js"], "sourcesContent": ["import { LifeUpdater } from \"./LifeUpdater\";\nexport async function loadLifeUpdater(engine, refresh = true) {\n    await engine.addParticleUpdater(\"life\", (container) => new LifeUpdater(container), refresh);\n}\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,eAAe;AAC3C,OAAO,eAAeC,eAAeA,CAACC,MAAM,EAAEC,OAAO,GAAG,IAAI,EAAE;EAC1D,MAAMD,MAAM,CAACE,kBAAkB,CAAC,MAAM,EAAGC,SAAS,IAAK,IAAIL,WAAW,CAACK,SAAS,CAAC,EAAEF,OAAO,CAAC;AAC/F", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
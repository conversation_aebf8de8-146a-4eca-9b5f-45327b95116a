{"ast": null, "code": "import getUTCISOWeekYear from \"../getUTCISOWeekYear/index.js\";\nimport startOfUTCISOWeek from \"../startOfUTCISOWeek/index.js\";\nimport requiredArgs from \"../requiredArgs/index.js\"; // This function will be a part of public API when UTC function will be implemented.\n// See issue: https://github.com/date-fns/date-fns/issues/376\n\nexport default function startOfUTCISOWeekYear(dirtyDate) {\n  requiredArgs(1, arguments);\n  var year = getUTCISOWeekYear(dirtyDate);\n  var fourthOfJanuary = new Date(0);\n  fourthOfJanuary.setUTCFullYear(year, 0, 4);\n  fourthOfJanuary.setUTCHours(0, 0, 0, 0);\n  var date = startOfUTCISOWeek(fourthOfJanuary);\n  return date;\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/date-fns/esm/_lib/startOfUTCISOWeekYear/index.js"], "names": ["getUTCISOWeekYear", "startOfUTCISOWeek", "requiredArgs", "startOfUTCISOWeekYear", "dirtyDate", "arguments", "year", "fourthOfJanuary", "Date", "setUTCFullYear", "setUTCHours", "date"], "mappings": "AAAA,OAAOA,iBAAP,MAA8B,+BAA9B;AACA,OAAOC,iBAAP,MAA8B,+BAA9B;AACA,OAAOC,YAAP,MAAyB,0BAAzB,C,CAAqD;AACrD;;AAEA,eAAe,SAASC,qBAAT,CAA+BC,SAA/B,EAA0C;AACvDF,EAAAA,YAAY,CAAC,CAAD,EAAIG,SAAJ,CAAZ;AACA,MAAIC,IAAI,GAAGN,iBAAiB,CAACI,SAAD,CAA5B;AACA,MAAIG,eAAe,GAAG,IAAIC,IAAJ,CAAS,CAAT,CAAtB;AACAD,EAAAA,eAAe,CAACE,cAAhB,CAA+BH,IAA/B,EAAqC,CAArC,EAAwC,CAAxC;AACAC,EAAAA,eAAe,CAACG,WAAhB,CAA4B,CAA5B,EAA+B,CAA/B,EAAkC,CAAlC,EAAqC,CAArC;AACA,MAAIC,IAAI,GAAGV,iBAAiB,CAACM,eAAD,CAA5B;AACA,SAAOI,IAAP;AACD", "sourcesContent": ["import getUTCISOWeekYear from \"../getUTCISOWeekYear/index.js\";\nimport startOfUTCISOWeek from \"../startOfUTCISOWeek/index.js\";\nimport requiredArgs from \"../requiredArgs/index.js\"; // This function will be a part of public API when UTC function will be implemented.\n// See issue: https://github.com/date-fns/date-fns/issues/376\n\nexport default function startOfUTCISOWeekYear(dirtyDate) {\n  requiredArgs(1, arguments);\n  var year = getUTCISOWeekYear(dirtyDate);\n  var fourthOfJanuary = new Date(0);\n  fourthOfJanuary.setUTCFullYear(year, 0, 4);\n  fourthOfJanuary.setUTCHours(0, 0, 0, 0);\n  var date = startOfUTCISOWeek(fourthOfJanuary);\n  return date;\n}"]}, "metadata": {}, "sourceType": "module"}
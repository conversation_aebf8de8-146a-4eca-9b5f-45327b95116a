/*! For license information please see tsparticles.shape.image.min.js.LICENSE.txt */
!function(e,t){if("object"==typeof exports&&"object"==typeof module)module.exports=t(require("tsparticles-engine"));else if("function"==typeof define&&define.amd)define(["tsparticles-engine"],t);else{var a="object"==typeof exports?t(require("tsparticles-engine")):t(e.window);for(var o in a)("object"==typeof exports?exports:e)[o]=a[o]}}(this,(e=>(()=>{"use strict";var t={961:t=>{t.exports=e}},a={};function o(e){var i=a[e];if(void 0!==i)return i.exports;var r=a[e]={exports:{}};return t[e](r,r.exports,o),r.exports}o.d=(e,t)=>{for(var a in t)o.o(t,a)&&!o.o(e,a)&&Object.defineProperty(e,a,{enumerable:!0,get:t[a]})},o.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),o.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var i={};return(()=>{o.r(i),o.d(i,{loadImageShape:()=>u});var e=o(961);const t=[0,4,2,1],a=[8,8,4,2];class r{constructor(e){this.pos=0,this.data=new Uint8ClampedArray(e)}getString(e){const t=this.data.slice(this.pos,this.pos+e);return this.pos+=t.length,t.reduce(((e,t)=>e+String.fromCharCode(t)),"")}nextByte(){return this.data[this.pos++]}nextTwoBytes(){return this.pos+=2,this.data[this.pos-2]+(this.data[this.pos-1]<<8)}readSubBlocks(){let e="",t=0;do{t=this.data[this.pos++];for(let a=t;--a>=0;e+=String.fromCharCode(this.data[this.pos++]));}while(0!==t);return e}readSubBlocksBin(){let e=0,t=0;for(let a=0;0!==(e=this.data[this.pos+a]);a+=e+1)t+=e;const a=new Uint8Array(t);for(let t=0;0!==(e=this.data[this.pos++]);)for(let o=e;--o>=0;a[t++]=this.data[this.pos++]);return a}skipSubBlocks(){for(;0!==this.data[this.pos];this.pos+=this.data[this.pos]+1);this.pos++}}function n(e,t){const a=[];for(let o=0;o<t;o++)a.push({r:e.data[e.pos],g:e.data[e.pos+1],b:e.data[e.pos+2]}),e.pos+=3;return a}async function s(e,o,i,r,s,l){switch(e.nextByte()){case 59:return!0;case 44:await async function(e,o,i,r,s,l){const c=o.frames[r(!0)];c.left=e.nextTwoBytes(),c.top=e.nextTwoBytes(),c.width=e.nextTwoBytes(),c.height=e.nextTwoBytes();const g=e.nextByte(),h=128==(128&g),d=64==(64&g);c.sortFlag=32==(32&g),c.reserved=(24&g)>>>3;const f=1<<1+(7&g);h&&(c.localColorTable=n(e,f));const p=e=>{const{r:t,g:a,b:r}=(h?c.localColorTable:o.globalColorTable)[e];return{r:t,g:a,b:r,a:e===s(null)?i?~~((t+a+r)/3):0:255}},m=(()=>{try{return new ImageData(c.width,c.height,{colorSpace:"srgb"})}catch(e){if(e instanceof DOMException&&"IndexSizeError"===e.name)return null;throw e}})();if(null==m)throw new EvalError("GIF frame size is to large");const u=e.nextByte(),w=e.readSubBlocksBin(),y=1<<u,b=(e,t)=>{const a=e>>>3,o=7&e;return(w[a]+(w[a+1]<<8)+(w[a+2]<<16)&(1<<t)-1<<o)>>>o};if(d){for(let i=0,n=u+1,s=0,g=[[0]],h=0;h<4;h++){if(t[h]<c.height)for(let e=0,o=0;;){const r=i;if(i=b(s,n),s+=n+1,i===y){n=u+1,g.length=y+2;for(let e=0;e<g.length;e++)g[e]=e<y?[e]:[]}else{i>=g.length?g.push(g[r].concat(g[r][0])):r!==y&&g.push(g[r].concat(g[i][0]));for(let r=0;r<g[i].length;r++){const{r:n,g:s,b:l,a:d}=p(g[i][r]);m.data.set([n,s,l,d],t[h]*c.width+a[h]*o+e%(4*c.width)),e+=4}g.length===1<<n&&n<12&&n++}if(e===4*c.width*(o+1)&&(o++,t[h]+a[h]*o>=c.height))break}l?.(e.pos/(e.data.length-1),r(!1)+1,m,{x:c.left,y:c.top},{width:o.width,height:o.height})}c.image=m,c.bitmap=await createImageBitmap(m)}else{for(let e=0,t=u+1,a=0,o=[[0]],i=-4;;){const r=e;if(e=b(a,t),a+=t,e===y){t=u+1,o.length=y+2;for(let e=0;e<o.length;e++)o[e]=e<y?[e]:[]}else{if(e===y+1)break;e>=o.length?o.push(o[r].concat(o[r][0])):r!==y&&o.push(o[r].concat(o[e][0]));for(let t=0;t<o[e].length;t++){const{r:a,g:r,b:n,a:s}=p(o[e][t]);m.data.set([a,r,n,s],i+=4)}o.length>=1<<t&&t<12&&t++}}c.image=m,c.bitmap=await createImageBitmap(m),l?.((e.pos+1)/e.data.length,r(!1)+1,c.image,{x:c.left,y:c.top},{width:o.width,height:o.height})}}(e,o,i,r,s,l);break;case 33:await async function(e,t,a,o){switch(e.nextByte()){case 249:{const i=t.frames[a(!1)];e.pos++;const r=e.nextByte();i.GCreserved=(224&r)>>>5,i.disposalMethod=(28&r)>>>2,i.userInputDelayFlag=2==(2&r);const n=1==(1&r);i.delayTime=10*e.nextTwoBytes();const s=e.nextByte();n&&o(s),e.pos++;break}case 255:{e.pos++;const a={identifier:e.getString(8),authenticationCode:e.getString(3),data:e.readSubBlocksBin()};t.applicationExtensions.push(a);break}case 254:t.comments.push([a(!1),e.readSubBlocks()]);break;case 1:if(0===t.globalColorTable.length)throw new EvalError("plain text extension without global color table");e.pos++,t.frames[a(!1)].plainTextData={left:e.nextTwoBytes(),top:e.nextTwoBytes(),width:e.nextTwoBytes(),height:e.nextTwoBytes(),charSize:{width:e.nextTwoBytes(),height:e.nextTwoBytes()},foregroundColor:e.nextByte(),backgroundColor:e.nextByte(),text:e.readSubBlocks()};break;default:e.skipSubBlocks()}}(e,o,r,s);break;default:throw new EvalError("undefined block found")}return!1}const l=/(#(?:[0-9a-f]{2}){2,4}|(#[0-9a-f]{3})|(rgb|hsl)a?\((-?\d+%?[,\s]+){2,3}\s*[\d.]+%?\))|currentcolor/gi;async function c(t){return new Promise((a=>{t.loading=!0;const o=new Image;t.element=o,o.addEventListener("load",(()=>{t.loading=!1,a()})),o.addEventListener("error",(()=>{t.element=void 0,t.error=!0,t.loading=!1,(0,e.getLogger)().error(`${e.errorPrefix} loading image: ${t.source}`),a()})),o.src=t.source}))}async function g(e){if("gif"===e.type){e.loading=!0;try{e.gifData=await async function(e,t,a){a||(a=!1);const o=await fetch(e);if(!o.ok&&404===o.status)throw new EvalError("file not found");const i=await o.arrayBuffer(),l={width:0,height:0,totalTime:0,colorRes:0,pixelAspectRatio:0,frames:[],sortFlag:!1,globalColorTable:[],backgroundImage:new ImageData(1,1,{colorSpace:"srgb"}),comments:[],applicationExtensions:[]},c=new r(new Uint8ClampedArray(i));if("GIF89a"!==c.getString(6))throw new Error("not a supported GIF file");l.width=c.nextTwoBytes(),l.height=c.nextTwoBytes();const g=c.nextByte(),h=128==(128&g);l.colorRes=(112&g)>>>4,l.sortFlag=8==(8&g);const d=1<<1+(7&g),f=c.nextByte();l.pixelAspectRatio=c.nextByte(),0!==l.pixelAspectRatio&&(l.pixelAspectRatio=(l.pixelAspectRatio+15)/64),h&&(l.globalColorTable=n(c,d));const p=(()=>{try{return new ImageData(l.width,l.height,{colorSpace:"srgb"})}catch(e){if(e instanceof DOMException&&"IndexSizeError"===e.name)return null;throw e}})();if(null==p)throw new Error("GIF frame size is to large");const{r:m,g:u,b:w}=l.globalColorTable[f];p.data.set(h?[m,u,w,255]:[0,0,0,0]);for(let e=4;e<p.data.length;e*=2)p.data.copyWithin(e,0,e);l.backgroundImage=p;let y=-1,b=!0,x=-1;const v=e=>(e&&(b=!0),y),C=e=>(null!=e&&(x=e),x);try{do{b&&(l.frames.push({left:0,top:0,width:0,height:0,disposalMethod:0,image:new ImageData(1,1,{colorSpace:"srgb"}),plainTextData:null,userInputDelayFlag:!1,delayTime:0,sortFlag:!1,localColorTable:[],reserved:0,GCreserved:0}),y++,x=-1,b=!1)}while(!await s(c,l,a,v,C,t));l.frames.length--;for(const e of l.frames){if(e.userInputDelayFlag&&0===e.delayTime){l.totalTime=1/0;break}l.totalTime+=e.delayTime}return l}catch(e){if(e instanceof EvalError)throw new Error(`error while parsing frame ${y} "${e.message}"`);throw e}}(e.source),e.gifLoopCount=function(e){for(const t of e.applicationExtensions)if(t.identifier+t.authenticationCode==="NETSCAPE2.0")return t.data[1]+(t.data[2]<<8);return NaN}(e.gifData)??0,0===e.gifLoopCount&&(e.gifLoopCount=1/0)}catch{e.error=!0}e.loading=!1}else await c(e)}async function h(t){if("svg"!==t.type)return void await c(t);t.loading=!0;const a=await fetch(t.source);a.ok?t.svgData=await a.text():((0,e.getLogger)().error(`${e.errorPrefix} Image not found`),t.error=!0),t.loading=!1}function d(t,a,o,i){const r=function(t,a,o){const{svgData:i}=t;if(!i)return"";const r=(0,e.getStyleFromHsl)(a,o);if(i.includes("fill"))return i.replace(l,(()=>r));const n=i.indexOf(">");return`${i.substring(0,n)} fill="${r}"${i.substring(n)}`}(t,o,i.opacity?.value??1),n={color:o,gif:a.gif,data:{...t,svgData:r},loaded:!1,ratio:a.width/a.height,replaceColor:a.replaceColor??a.replace_color,source:a.src};return new Promise((e=>{const a=new Blob([r],{type:"image/svg+xml"}),o=URL||window.URL||window.webkitURL||window,i=o.createObjectURL(a),s=new Image;s.addEventListener("load",(()=>{n.loaded=!0,n.element=s,e(n),o.revokeObjectURL(i)})),s.addEventListener("error",(async()=>{o.revokeObjectURL(i);const a={...t,error:!1,loading:!0};await c(a),n.loaded=!0,n.element=a.element,e(n)})),s.src=i}))}class f{constructor(t){this.loadImageShape=async t=>{if(!this._engine.loadImage)throw new Error(`${e.errorPrefix} image shape not initialized`);await this._engine.loadImage({gif:t.gif,name:t.name,replaceColor:t.replaceColor??t.replace_color??!1,src:t.src})},this._engine=t}addImage(e){this._engine.images||(this._engine.images=[]),this._engine.images.push(e)}draw(e,t,a,o,i){const r=t.image,n=r?.element;if(r){if(e.globalAlpha=o,r.gif&&r.gifData){const o=new OffscreenCanvas(r.gifData.width,r.gifData.height),n=o.getContext("2d");if(!n)throw new Error("could not create offscreen canvas context");n.imageSmoothingQuality="low",n.imageSmoothingEnabled=!1,n.clearRect(0,0,o.width,o.height),void 0===t.gifLoopCount&&(t.gifLoopCount=r.gifLoopCount??0);let s=t.gifFrame??0;const l={x:.5*-r.gifData.width,y:.5*-r.gifData.height},c=r.gifData.frames[s];if(void 0===t.gifTime&&(t.gifTime=0),!c.bitmap)return;switch(e.scale(a/r.gifData.width,a/r.gifData.height),c.disposalMethod){case 4:case 5:case 6:case 7:case 0:n.drawImage(c.bitmap,c.left,c.top),e.drawImage(o,l.x,l.y),n.clearRect(0,0,o.width,o.height);break;case 1:n.drawImage(c.bitmap,c.left,c.top),e.drawImage(o,l.x,l.y);break;case 2:n.drawImage(c.bitmap,c.left,c.top),e.drawImage(o,l.x,l.y),n.clearRect(0,0,o.width,o.height),0===r.gifData.globalColorTable.length?n.putImageData(r.gifData.frames[0].image,l.x+c.left,l.y+c.top):n.putImageData(r.gifData.backgroundImage,l.x,l.y);break;case 3:{const t=n.getImageData(0,0,o.width,o.height);n.drawImage(c.bitmap,c.left,c.top),e.drawImage(o,l.x,l.y),n.clearRect(0,0,o.width,o.height),n.putImageData(t,0,0)}}if(t.gifTime+=i.value,t.gifTime>c.delayTime){if(t.gifTime-=c.delayTime,++s>=r.gifData.frames.length){if(--t.gifLoopCount<=0)return;s=0,n.clearRect(0,0,o.width,o.height)}t.gifFrame=s}e.scale(r.gifData.width/a,r.gifData.height/a)}else if(n){const t=r.ratio,o={x:-a,y:-a};e.drawImage(n,o.x,o.y,2*a,2*a/t)}e.globalAlpha=1}}getSidesCount(){return 12}async init(e){const t=e.actualOptions;if(t.preload&&this._engine.loadImage)for(const e of t.preload)await this._engine.loadImage(e)}loadShape(e){if("image"!==e.shape&&"images"!==e.shape)return;this._engine.images||(this._engine.images=[]);const t=e.shapeData;this._engine.images.find((e=>e.name===t.name||e.source===t.src))||this.loadImageShape(t).then((()=>{this.loadShape(e)}))}particleInit(e,t){if("image"!==t.shape&&"images"!==t.shape)return;this._engine.images||(this._engine.images=[]);const a=this._engine.images,o=t.shapeData,i=t.getFillColor(),r=a.find((e=>e.name===o.name||e.source===o.src));if(!r)return;const n=o.replaceColor??o.replace_color??r.replaceColor;r.loading?setTimeout((()=>{this.particleInit(e,t)})):(async()=>{let e;e=r.svgData&&i?await d(r,o,i,t):{color:i,data:r,element:r.element,gif:r.gif,gifData:r.gifData,gifLoopCount:r.gifLoopCount,loaded:!0,ratio:o.width&&o.height?o.width/o.height:r.ratio??1,replaceColor:n,source:o.src},e.ratio||(e.ratio=1);const a={image:e,fill:o.fill??t.fill,close:o.close??t.close};t.image=a.image,t.fill=a.fill,t.close=a.close})()}}class p{constructor(){this.src="",this.gif=!1}load(e){e&&(void 0!==e.gif&&(this.gif=e.gif),void 0!==e.height&&(this.height=e.height),void 0!==e.name&&(this.name=e.name),void 0!==e.replaceColor&&(this.replaceColor=e.replaceColor),void 0!==e.src&&(this.src=e.src),void 0!==e.width&&(this.width=e.width))}}class m{constructor(e){this.id="imagePreloader",this._engine=e}getPlugin(){return{}}loadOptions(e,t){if(!t||!t.preload)return;e.preload||(e.preload=[]);const a=e.preload;for(const e of t.preload){const t=a.find((t=>t.name===e.name||t.src===e.src));if(t)t.load(e);else{const t=new p;t.load(e),a.push(t)}}}needsPlugin(){return!0}}async function u(t,a=!0){!function(t){t.loadImage||(t.loadImage=async a=>{if(!a.name&&!a.src)throw new Error(`${e.errorPrefix} no image source provided`);if(t.images||(t.images=[]),!t.images.find((e=>e.name===a.name||e.source===a.src)))try{const e={gif:a.gif??!1,name:a.name??a.src,source:a.src,type:a.src.substring(a.src.length-3),error:!1,loading:!0,replaceColor:a.replaceColor,ratio:a.width&&a.height?a.width/a.height:void 0};t.images.push(e);const o=a.gif?g:a.replaceColor?h:c;await o(e)}catch{throw new Error(`${e.errorPrefix} ${a.name??a.src} not found`)}})}(t);const o=new m(t);await t.addPlugin(o,a),await t.addShape(["image","images"],new f(t),a)}})(),i})()));
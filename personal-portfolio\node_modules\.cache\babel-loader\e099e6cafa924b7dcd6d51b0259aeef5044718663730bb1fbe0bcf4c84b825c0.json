{"ast": null, "code": "import { getValue } from \"tsparticles-engine\";\nexport function bounceHorizontal(data) {\n  if (data.outMode !== \"bounce\" && data.outMode !== \"bounce-horizontal\" && data.outMode !== \"bounceHorizontal\" && data.outMode !== \"split\" || data.direction !== \"left\" && data.direction !== \"right\") {\n    return;\n  }\n  if (data.bounds.right < 0 && data.direction === \"left\") {\n    data.particle.position.x = data.size + data.offset.x;\n  } else if (data.bounds.left > data.canvasSize.width && data.direction === \"right\") {\n    data.particle.position.x = data.canvasSize.width - data.size - data.offset.x;\n  }\n  const velocity = data.particle.velocity.x;\n  let bounced = false;\n  if (data.direction === \"right\" && data.bounds.right >= data.canvasSize.width && velocity > 0 || data.direction === \"left\" && data.bounds.left <= 0 && velocity < 0) {\n    const newVelocity = getValue(data.particle.options.bounce.horizontal);\n    data.particle.velocity.x *= -newVelocity;\n    bounced = true;\n  }\n  if (!bounced) {\n    return;\n  }\n  const minPos = data.offset.x + data.size;\n  if (data.bounds.right >= data.canvasSize.width && data.direction === \"right\") {\n    data.particle.position.x = data.canvasSize.width - minPos;\n  } else if (data.bounds.left <= 0 && data.direction === \"left\") {\n    data.particle.position.x = minPos;\n  }\n  if (data.outMode === \"split\") {\n    data.particle.destroy();\n  }\n}\nexport function bounceVertical(data) {\n  if (data.outMode !== \"bounce\" && data.outMode !== \"bounce-vertical\" && data.outMode !== \"bounceVertical\" && data.outMode !== \"split\" || data.direction !== \"bottom\" && data.direction !== \"top\") {\n    return;\n  }\n  if (data.bounds.bottom < 0 && data.direction === \"top\") {\n    data.particle.position.y = data.size + data.offset.y;\n  } else if (data.bounds.top > data.canvasSize.height && data.direction === \"bottom\") {\n    data.particle.position.y = data.canvasSize.height - data.size - data.offset.y;\n  }\n  const velocity = data.particle.velocity.y;\n  let bounced = false;\n  if (data.direction === \"bottom\" && data.bounds.bottom >= data.canvasSize.height && velocity > 0 || data.direction === \"top\" && data.bounds.top <= 0 && velocity < 0) {\n    const newVelocity = getValue(data.particle.options.bounce.vertical);\n    data.particle.velocity.y *= -newVelocity;\n    bounced = true;\n  }\n  if (!bounced) {\n    return;\n  }\n  const minPos = data.offset.y + data.size;\n  if (data.bounds.bottom >= data.canvasSize.height && data.direction === \"bottom\") {\n    data.particle.position.y = data.canvasSize.height - minPos;\n  } else if (data.bounds.top <= 0 && data.direction === \"top\") {\n    data.particle.position.y = minPos;\n  }\n  if (data.outMode === \"split\") {\n    data.particle.destroy();\n  }\n}", "map": {"version": 3, "names": ["getValue", "bounceHorizontal", "data", "outMode", "direction", "bounds", "right", "particle", "position", "x", "size", "offset", "left", "canvasSize", "width", "velocity", "bounced", "newVelocity", "options", "bounce", "horizontal", "minPos", "destroy", "bounceVertical", "bottom", "y", "top", "height", "vertical"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/tsparticles-updater-out-modes/esm/Utils.js"], "sourcesContent": ["import { getValue } from \"tsparticles-engine\";\nexport function bounceHorizontal(data) {\n    if ((data.outMode !== \"bounce\" &&\n        data.outMode !== \"bounce-horizontal\" &&\n        data.outMode !== \"bounceHorizontal\" &&\n        data.outMode !== \"split\") ||\n        (data.direction !== \"left\" && data.direction !== \"right\")) {\n        return;\n    }\n    if (data.bounds.right < 0 && data.direction === \"left\") {\n        data.particle.position.x = data.size + data.offset.x;\n    }\n    else if (data.bounds.left > data.canvasSize.width && data.direction === \"right\") {\n        data.particle.position.x = data.canvasSize.width - data.size - data.offset.x;\n    }\n    const velocity = data.particle.velocity.x;\n    let bounced = false;\n    if ((data.direction === \"right\" && data.bounds.right >= data.canvasSize.width && velocity > 0) ||\n        (data.direction === \"left\" && data.bounds.left <= 0 && velocity < 0)) {\n        const newVelocity = getValue(data.particle.options.bounce.horizontal);\n        data.particle.velocity.x *= -newVelocity;\n        bounced = true;\n    }\n    if (!bounced) {\n        return;\n    }\n    const minPos = data.offset.x + data.size;\n    if (data.bounds.right >= data.canvasSize.width && data.direction === \"right\") {\n        data.particle.position.x = data.canvasSize.width - minPos;\n    }\n    else if (data.bounds.left <= 0 && data.direction === \"left\") {\n        data.particle.position.x = minPos;\n    }\n    if (data.outMode === \"split\") {\n        data.particle.destroy();\n    }\n}\nexport function bounceVertical(data) {\n    if ((data.outMode !== \"bounce\" &&\n        data.outMode !== \"bounce-vertical\" &&\n        data.outMode !== \"bounceVertical\" &&\n        data.outMode !== \"split\") ||\n        (data.direction !== \"bottom\" && data.direction !== \"top\")) {\n        return;\n    }\n    if (data.bounds.bottom < 0 && data.direction === \"top\") {\n        data.particle.position.y = data.size + data.offset.y;\n    }\n    else if (data.bounds.top > data.canvasSize.height && data.direction === \"bottom\") {\n        data.particle.position.y = data.canvasSize.height - data.size - data.offset.y;\n    }\n    const velocity = data.particle.velocity.y;\n    let bounced = false;\n    if ((data.direction === \"bottom\" && data.bounds.bottom >= data.canvasSize.height && velocity > 0) ||\n        (data.direction === \"top\" && data.bounds.top <= 0 && velocity < 0)) {\n        const newVelocity = getValue(data.particle.options.bounce.vertical);\n        data.particle.velocity.y *= -newVelocity;\n        bounced = true;\n    }\n    if (!bounced) {\n        return;\n    }\n    const minPos = data.offset.y + data.size;\n    if (data.bounds.bottom >= data.canvasSize.height && data.direction === \"bottom\") {\n        data.particle.position.y = data.canvasSize.height - minPos;\n    }\n    else if (data.bounds.top <= 0 && data.direction === \"top\") {\n        data.particle.position.y = minPos;\n    }\n    if (data.outMode === \"split\") {\n        data.particle.destroy();\n    }\n}\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,oBAAoB;AAC7C,OAAO,SAASC,gBAAgBA,CAACC,IAAI,EAAE;EACnC,IAAKA,IAAI,CAACC,OAAO,KAAK,QAAQ,IAC1BD,IAAI,CAACC,OAAO,KAAK,mBAAmB,IACpCD,IAAI,CAACC,OAAO,KAAK,kBAAkB,IACnCD,IAAI,CAACC,OAAO,KAAK,OAAO,IACvBD,IAAI,CAACE,SAAS,KAAK,MAAM,IAAIF,IAAI,CAACE,SAAS,KAAK,OAAQ,EAAE;IAC3D;EACJ;EACA,IAAIF,IAAI,CAACG,MAAM,CAACC,KAAK,GAAG,CAAC,IAAIJ,IAAI,CAACE,SAAS,KAAK,MAAM,EAAE;IACpDF,IAAI,CAACK,QAAQ,CAACC,QAAQ,CAACC,CAAC,GAAGP,IAAI,CAACQ,IAAI,GAAGR,IAAI,CAACS,MAAM,CAACF,CAAC;EACxD,CAAC,MACI,IAAIP,IAAI,CAACG,MAAM,CAACO,IAAI,GAAGV,IAAI,CAACW,UAAU,CAACC,KAAK,IAAIZ,IAAI,CAACE,SAAS,KAAK,OAAO,EAAE;IAC7EF,IAAI,CAACK,QAAQ,CAACC,QAAQ,CAACC,CAAC,GAAGP,IAAI,CAACW,UAAU,CAACC,KAAK,GAAGZ,IAAI,CAACQ,IAAI,GAAGR,IAAI,CAACS,MAAM,CAACF,CAAC;EAChF;EACA,MAAMM,QAAQ,GAAGb,IAAI,CAACK,QAAQ,CAACQ,QAAQ,CAACN,CAAC;EACzC,IAAIO,OAAO,GAAG,KAAK;EACnB,IAAKd,IAAI,CAACE,SAAS,KAAK,OAAO,IAAIF,IAAI,CAACG,MAAM,CAACC,KAAK,IAAIJ,IAAI,CAACW,UAAU,CAACC,KAAK,IAAIC,QAAQ,GAAG,CAAC,IACxFb,IAAI,CAACE,SAAS,KAAK,MAAM,IAAIF,IAAI,CAACG,MAAM,CAACO,IAAI,IAAI,CAAC,IAAIG,QAAQ,GAAG,CAAE,EAAE;IACtE,MAAME,WAAW,GAAGjB,QAAQ,CAACE,IAAI,CAACK,QAAQ,CAACW,OAAO,CAACC,MAAM,CAACC,UAAU,CAAC;IACrElB,IAAI,CAACK,QAAQ,CAACQ,QAAQ,CAACN,CAAC,IAAI,CAACQ,WAAW;IACxCD,OAAO,GAAG,IAAI;EAClB;EACA,IAAI,CAACA,OAAO,EAAE;IACV;EACJ;EACA,MAAMK,MAAM,GAAGnB,IAAI,CAACS,MAAM,CAACF,CAAC,GAAGP,IAAI,CAACQ,IAAI;EACxC,IAAIR,IAAI,CAACG,MAAM,CAACC,KAAK,IAAIJ,IAAI,CAACW,UAAU,CAACC,KAAK,IAAIZ,IAAI,CAACE,SAAS,KAAK,OAAO,EAAE;IAC1EF,IAAI,CAACK,QAAQ,CAACC,QAAQ,CAACC,CAAC,GAAGP,IAAI,CAACW,UAAU,CAACC,KAAK,GAAGO,MAAM;EAC7D,CAAC,MACI,IAAInB,IAAI,CAACG,MAAM,CAACO,IAAI,IAAI,CAAC,IAAIV,IAAI,CAACE,SAAS,KAAK,MAAM,EAAE;IACzDF,IAAI,CAACK,QAAQ,CAACC,QAAQ,CAACC,CAAC,GAAGY,MAAM;EACrC;EACA,IAAInB,IAAI,CAACC,OAAO,KAAK,OAAO,EAAE;IAC1BD,IAAI,CAACK,QAAQ,CAACe,OAAO,CAAC,CAAC;EAC3B;AACJ;AACA,OAAO,SAASC,cAAcA,CAACrB,IAAI,EAAE;EACjC,IAAKA,IAAI,CAACC,OAAO,KAAK,QAAQ,IAC1BD,IAAI,CAACC,OAAO,KAAK,iBAAiB,IAClCD,IAAI,CAACC,OAAO,KAAK,gBAAgB,IACjCD,IAAI,CAACC,OAAO,KAAK,OAAO,IACvBD,IAAI,CAACE,SAAS,KAAK,QAAQ,IAAIF,IAAI,CAACE,SAAS,KAAK,KAAM,EAAE;IAC3D;EACJ;EACA,IAAIF,IAAI,CAACG,MAAM,CAACmB,MAAM,GAAG,CAAC,IAAItB,IAAI,CAACE,SAAS,KAAK,KAAK,EAAE;IACpDF,IAAI,CAACK,QAAQ,CAACC,QAAQ,CAACiB,CAAC,GAAGvB,IAAI,CAACQ,IAAI,GAAGR,IAAI,CAACS,MAAM,CAACc,CAAC;EACxD,CAAC,MACI,IAAIvB,IAAI,CAACG,MAAM,CAACqB,GAAG,GAAGxB,IAAI,CAACW,UAAU,CAACc,MAAM,IAAIzB,IAAI,CAACE,SAAS,KAAK,QAAQ,EAAE;IAC9EF,IAAI,CAACK,QAAQ,CAACC,QAAQ,CAACiB,CAAC,GAAGvB,IAAI,CAACW,UAAU,CAACc,MAAM,GAAGzB,IAAI,CAACQ,IAAI,GAAGR,IAAI,CAACS,MAAM,CAACc,CAAC;EACjF;EACA,MAAMV,QAAQ,GAAGb,IAAI,CAACK,QAAQ,CAACQ,QAAQ,CAACU,CAAC;EACzC,IAAIT,OAAO,GAAG,KAAK;EACnB,IAAKd,IAAI,CAACE,SAAS,KAAK,QAAQ,IAAIF,IAAI,CAACG,MAAM,CAACmB,MAAM,IAAItB,IAAI,CAACW,UAAU,CAACc,MAAM,IAAIZ,QAAQ,GAAG,CAAC,IAC3Fb,IAAI,CAACE,SAAS,KAAK,KAAK,IAAIF,IAAI,CAACG,MAAM,CAACqB,GAAG,IAAI,CAAC,IAAIX,QAAQ,GAAG,CAAE,EAAE;IACpE,MAAME,WAAW,GAAGjB,QAAQ,CAACE,IAAI,CAACK,QAAQ,CAACW,OAAO,CAACC,MAAM,CAACS,QAAQ,CAAC;IACnE1B,IAAI,CAACK,QAAQ,CAACQ,QAAQ,CAACU,CAAC,IAAI,CAACR,WAAW;IACxCD,OAAO,GAAG,IAAI;EAClB;EACA,IAAI,CAACA,OAAO,EAAE;IACV;EACJ;EACA,MAAMK,MAAM,GAAGnB,IAAI,CAACS,MAAM,CAACc,CAAC,GAAGvB,IAAI,CAACQ,IAAI;EACxC,IAAIR,IAAI,CAACG,MAAM,CAACmB,MAAM,IAAItB,IAAI,CAACW,UAAU,CAACc,MAAM,IAAIzB,IAAI,CAACE,SAAS,KAAK,QAAQ,EAAE;IAC7EF,IAAI,CAACK,QAAQ,CAACC,QAAQ,CAACiB,CAAC,GAAGvB,IAAI,CAACW,UAAU,CAACc,MAAM,GAAGN,MAAM;EAC9D,CAAC,MACI,IAAInB,IAAI,CAACG,MAAM,CAACqB,GAAG,IAAI,CAAC,IAAIxB,IAAI,CAACE,SAAS,KAAK,KAAK,EAAE;IACvDF,IAAI,CAACK,QAAQ,CAACC,QAAQ,CAACiB,CAAC,GAAGJ,MAAM;EACrC;EACA,IAAInB,IAAI,CAACC,OAAO,KAAK,OAAO,EAAE;IAC1BD,IAAI,CAACK,QAAQ,CAACe,OAAO,CAAC,CAAC;EAC3B;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
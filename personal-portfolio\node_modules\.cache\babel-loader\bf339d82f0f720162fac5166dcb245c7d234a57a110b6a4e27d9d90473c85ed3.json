{"ast": null, "code": "export var EventType;\n(function (EventType) {\n  EventType[\"configAdded\"] = \"configAdded\";\n  EventType[\"containerInit\"] = \"containerInit\";\n  EventType[\"particlesSetup\"] = \"particlesSetup\";\n  EventType[\"containerStarted\"] = \"containerStarted\";\n  EventType[\"containerStopped\"] = \"containerStopped\";\n  EventType[\"containerDestroyed\"] = \"containerDestroyed\";\n  EventType[\"containerPaused\"] = \"containerPaused\";\n  EventType[\"containerPlay\"] = \"containerPlay\";\n  EventType[\"containerBuilt\"] = \"containerBuilt\";\n  EventType[\"particleAdded\"] = \"particleAdded\";\n  EventType[\"particleDestroyed\"] = \"particleDestroyed\";\n  EventType[\"particleRemoved\"] = \"particleRemoved\";\n})(EventType || (EventType = {}));", "map": {"version": 3, "names": ["EventType"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/engine/browser/Enums/Types/EventType.js"], "sourcesContent": ["export var EventType;\n(function (EventType) {\n    EventType[\"configAdded\"] = \"configAdded\";\n    EventType[\"containerInit\"] = \"containerInit\";\n    EventType[\"particlesSetup\"] = \"particlesSetup\";\n    EventType[\"containerStarted\"] = \"containerStarted\";\n    EventType[\"containerStopped\"] = \"containerStopped\";\n    EventType[\"containerDestroyed\"] = \"containerDestroyed\";\n    EventType[\"containerPaused\"] = \"containerPaused\";\n    EventType[\"containerPlay\"] = \"containerPlay\";\n    EventType[\"containerBuilt\"] = \"containerBuilt\";\n    EventType[\"particleAdded\"] = \"particleAdded\";\n    EventType[\"particleDestroyed\"] = \"particleDestroyed\";\n    EventType[\"particleRemoved\"] = \"particleRemoved\";\n})(EventType || (EventType = {}));\n"], "mappings": "AAAA,OAAO,IAAIA,SAAS;AACpB,CAAC,UAAUA,SAAS,EAAE;EAClBA,SAAS,CAAC,aAAa,CAAC,GAAG,aAAa;EACxCA,SAAS,CAAC,eAAe,CAAC,GAAG,eAAe;EAC5CA,SAAS,CAAC,gBAAgB,CAAC,GAAG,gBAAgB;EAC9CA,SAAS,CAAC,kBAAkB,CAAC,GAAG,kBAAkB;EAClDA,SAAS,CAAC,kBAAkB,CAAC,GAAG,kBAAkB;EAClDA,SAAS,CAAC,oBAAoB,CAAC,GAAG,oBAAoB;EACtDA,SAAS,CAAC,iBAAiB,CAAC,GAAG,iBAAiB;EAChDA,SAAS,CAAC,eAAe,CAAC,GAAG,eAAe;EAC5CA,SAAS,CAAC,gBAAgB,CAAC,GAAG,gBAAgB;EAC9CA,SAAS,CAAC,eAAe,CAAC,GAAG,eAAe;EAC5CA,SAAS,CAAC,mBAAmB,CAAC,GAAG,mBAAmB;EACpDA,SAAS,CAAC,iBAAiB,CAAC,GAAG,iBAAiB;AACpD,CAAC,EAAEA,SAAS,KAAKA,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
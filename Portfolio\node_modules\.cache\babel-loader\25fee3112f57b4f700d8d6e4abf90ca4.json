{"ast": null, "code": "import { getRangeValue, randomInRange, setRangeValue } from \"../../Utils\";\nexport class LifeUpdater {\n  constructor(container) {\n    this.container = container;\n  }\n\n  init() {}\n\n  isEnabled(particle) {\n    return !particle.destroyed;\n  }\n\n  update(particle, delta) {\n    if (!this.isEnabled(particle)) {\n      return;\n    }\n\n    const life = particle.life;\n    let justSpawned = false;\n\n    if (particle.spawning) {\n      life.delayTime += delta.value;\n\n      if (life.delayTime >= particle.life.delay) {\n        justSpawned = true;\n        particle.spawning = false;\n        life.delayTime = 0;\n        life.time = 0;\n      } else {\n        return;\n      }\n    }\n\n    if (life.duration === -1) {\n      return;\n    }\n\n    if (particle.spawning) {\n      return;\n    }\n\n    if (justSpawned) {\n      life.time = 0;\n    } else {\n      life.time += delta.value;\n    }\n\n    if (life.time < life.duration) {\n      return;\n    }\n\n    life.time = 0;\n\n    if (particle.life.count > 0) {\n      particle.life.count--;\n    }\n\n    if (particle.life.count === 0) {\n      particle.destroy();\n      return;\n    }\n\n    const canvasSize = this.container.canvas.size,\n          widthRange = setRangeValue(0, canvasSize.width),\n          heightRange = setRangeValue(0, canvasSize.width);\n    particle.position.x = randomInRange(widthRange);\n    particle.position.y = randomInRange(heightRange);\n    particle.spawning = true;\n    life.delayTime = 0;\n    life.time = 0;\n    particle.reset();\n    const lifeOptions = particle.options.life;\n    life.delay = getRangeValue(lifeOptions.delay.value) * 1000;\n    life.duration = getRangeValue(lifeOptions.duration.value) * 1000;\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Updaters/Life/LifeUpdater.js"], "names": ["getRangeValue", "randomInRange", "setRangeValue", "LifeUpdater", "constructor", "container", "init", "isEnabled", "particle", "destroyed", "update", "delta", "life", "justSpawned", "spawning", "delayTime", "value", "delay", "time", "duration", "count", "destroy", "canvasSize", "canvas", "size", "widthRange", "width", "heightRange", "position", "x", "y", "reset", "lifeOptions", "options"], "mappings": "AAAA,SAASA,aAAT,EAAwBC,aAAxB,EAAuCC,aAAvC,QAA4D,aAA5D;AACA,OAAO,MAAMC,WAAN,CAAkB;AACrBC,EAAAA,WAAW,CAACC,SAAD,EAAY;AACnB,SAAKA,SAAL,GAAiBA,SAAjB;AACH;;AACDC,EAAAA,IAAI,GAAG,CACN;;AACDC,EAAAA,SAAS,CAACC,QAAD,EAAW;AAChB,WAAO,CAACA,QAAQ,CAACC,SAAjB;AACH;;AACDC,EAAAA,MAAM,CAACF,QAAD,EAAWG,KAAX,EAAkB;AACpB,QAAI,CAAC,KAAKJ,SAAL,CAAeC,QAAf,CAAL,EAA+B;AAC3B;AACH;;AACD,UAAMI,IAAI,GAAGJ,QAAQ,CAACI,IAAtB;AACA,QAAIC,WAAW,GAAG,KAAlB;;AACA,QAAIL,QAAQ,CAACM,QAAb,EAAuB;AACnBF,MAAAA,IAAI,CAACG,SAAL,IAAkBJ,KAAK,CAACK,KAAxB;;AACA,UAAIJ,IAAI,CAACG,SAAL,IAAkBP,QAAQ,CAACI,IAAT,CAAcK,KAApC,EAA2C;AACvCJ,QAAAA,WAAW,GAAG,IAAd;AACAL,QAAAA,QAAQ,CAACM,QAAT,GAAoB,KAApB;AACAF,QAAAA,IAAI,CAACG,SAAL,GAAiB,CAAjB;AACAH,QAAAA,IAAI,CAACM,IAAL,GAAY,CAAZ;AACH,OALD,MAMK;AACD;AACH;AACJ;;AACD,QAAIN,IAAI,CAACO,QAAL,KAAkB,CAAC,CAAvB,EAA0B;AACtB;AACH;;AACD,QAAIX,QAAQ,CAACM,QAAb,EAAuB;AACnB;AACH;;AACD,QAAID,WAAJ,EAAiB;AACbD,MAAAA,IAAI,CAACM,IAAL,GAAY,CAAZ;AACH,KAFD,MAGK;AACDN,MAAAA,IAAI,CAACM,IAAL,IAAaP,KAAK,CAACK,KAAnB;AACH;;AACD,QAAIJ,IAAI,CAACM,IAAL,GAAYN,IAAI,CAACO,QAArB,EAA+B;AAC3B;AACH;;AACDP,IAAAA,IAAI,CAACM,IAAL,GAAY,CAAZ;;AACA,QAAIV,QAAQ,CAACI,IAAT,CAAcQ,KAAd,GAAsB,CAA1B,EAA6B;AACzBZ,MAAAA,QAAQ,CAACI,IAAT,CAAcQ,KAAd;AACH;;AACD,QAAIZ,QAAQ,CAACI,IAAT,CAAcQ,KAAd,KAAwB,CAA5B,EAA+B;AAC3BZ,MAAAA,QAAQ,CAACa,OAAT;AACA;AACH;;AACD,UAAMC,UAAU,GAAG,KAAKjB,SAAL,CAAekB,MAAf,CAAsBC,IAAzC;AAAA,UAA+CC,UAAU,GAAGvB,aAAa,CAAC,CAAD,EAAIoB,UAAU,CAACI,KAAf,CAAzE;AAAA,UAAgGC,WAAW,GAAGzB,aAAa,CAAC,CAAD,EAAIoB,UAAU,CAACI,KAAf,CAA3H;AACAlB,IAAAA,QAAQ,CAACoB,QAAT,CAAkBC,CAAlB,GAAsB5B,aAAa,CAACwB,UAAD,CAAnC;AACAjB,IAAAA,QAAQ,CAACoB,QAAT,CAAkBE,CAAlB,GAAsB7B,aAAa,CAAC0B,WAAD,CAAnC;AACAnB,IAAAA,QAAQ,CAACM,QAAT,GAAoB,IAApB;AACAF,IAAAA,IAAI,CAACG,SAAL,GAAiB,CAAjB;AACAH,IAAAA,IAAI,CAACM,IAAL,GAAY,CAAZ;AACAV,IAAAA,QAAQ,CAACuB,KAAT;AACA,UAAMC,WAAW,GAAGxB,QAAQ,CAACyB,OAAT,CAAiBrB,IAArC;AACAA,IAAAA,IAAI,CAACK,KAAL,GAAajB,aAAa,CAACgC,WAAW,CAACf,KAAZ,CAAkBD,KAAnB,CAAb,GAAyC,IAAtD;AACAJ,IAAAA,IAAI,CAACO,QAAL,GAAgBnB,aAAa,CAACgC,WAAW,CAACb,QAAZ,CAAqBH,KAAtB,CAAb,GAA4C,IAA5D;AACH;;AA5DoB", "sourcesContent": ["import { getRangeValue, randomInRange, setRangeValue } from \"../../Utils\";\nexport class LifeUpdater {\n    constructor(container) {\n        this.container = container;\n    }\n    init() {\n    }\n    isEnabled(particle) {\n        return !particle.destroyed;\n    }\n    update(particle, delta) {\n        if (!this.isEnabled(particle)) {\n            return;\n        }\n        const life = particle.life;\n        let justSpawned = false;\n        if (particle.spawning) {\n            life.delayTime += delta.value;\n            if (life.delayTime >= particle.life.delay) {\n                justSpawned = true;\n                particle.spawning = false;\n                life.delayTime = 0;\n                life.time = 0;\n            }\n            else {\n                return;\n            }\n        }\n        if (life.duration === -1) {\n            return;\n        }\n        if (particle.spawning) {\n            return;\n        }\n        if (justSpawned) {\n            life.time = 0;\n        }\n        else {\n            life.time += delta.value;\n        }\n        if (life.time < life.duration) {\n            return;\n        }\n        life.time = 0;\n        if (particle.life.count > 0) {\n            particle.life.count--;\n        }\n        if (particle.life.count === 0) {\n            particle.destroy();\n            return;\n        }\n        const canvasSize = this.container.canvas.size, widthRange = setRangeValue(0, canvasSize.width), heightRange = setRangeValue(0, canvasSize.width);\n        particle.position.x = randomInRange(widthRange);\n        particle.position.y = randomInRange(heightRange);\n        particle.spawning = true;\n        life.delayTime = 0;\n        life.time = 0;\n        particle.reset();\n        const lifeOptions = particle.options.life;\n        life.delay = getRangeValue(lifeOptions.delay.value) * 1000;\n        life.duration = getRangeValue(lifeOptions.duration.value) * 1000;\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
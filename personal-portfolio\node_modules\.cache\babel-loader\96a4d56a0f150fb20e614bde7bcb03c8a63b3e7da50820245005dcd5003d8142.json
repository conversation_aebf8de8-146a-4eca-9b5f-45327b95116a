{"ast": null, "code": "import { OptionsColor } from \"@tsparticles/engine\";\nexport class GrabLinks {\n  constructor() {\n    this.blink = false;\n    this.consent = false;\n    this.opacity = 1;\n  }\n  load(data) {\n    if (!data) {\n      return;\n    }\n    if (data.blink !== undefined) {\n      this.blink = data.blink;\n    }\n    if (data.color !== undefined) {\n      this.color = OptionsColor.create(this.color, data.color);\n    }\n    if (data.consent !== undefined) {\n      this.consent = data.consent;\n    }\n    if (data.opacity !== undefined) {\n      this.opacity = data.opacity;\n    }\n  }\n}", "map": {"version": 3, "names": ["OptionsColor", "GrabLinks", "constructor", "blink", "consent", "opacity", "load", "data", "undefined", "color", "create"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/interaction-external-grab/browser/Options/Classes/GrabLinks.js"], "sourcesContent": ["import { OptionsColor } from \"@tsparticles/engine\";\nexport class GrabLinks {\n    constructor() {\n        this.blink = false;\n        this.consent = false;\n        this.opacity = 1;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.blink !== undefined) {\n            this.blink = data.blink;\n        }\n        if (data.color !== undefined) {\n            this.color = OptionsColor.create(this.color, data.color);\n        }\n        if (data.consent !== undefined) {\n            this.consent = data.consent;\n        }\n        if (data.opacity !== undefined) {\n            this.opacity = data.opacity;\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,qBAAqB;AAClD,OAAO,MAAMC,SAAS,CAAC;EACnBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,KAAK,GAAG,KAAK;IAClB,IAAI,CAACC,OAAO,GAAG,KAAK;IACpB,IAAI,CAACC,OAAO,GAAG,CAAC;EACpB;EACAC,IAAIA,CAACC,IAAI,EAAE;IACP,IAAI,CAACA,IAAI,EAAE;MACP;IACJ;IACA,IAAIA,IAAI,CAACJ,KAAK,KAAKK,SAAS,EAAE;MAC1B,IAAI,CAACL,KAAK,GAAGI,IAAI,CAACJ,KAAK;IAC3B;IACA,IAAII,IAAI,CAACE,KAAK,KAAKD,SAAS,EAAE;MAC1B,IAAI,CAACC,KAAK,GAAGT,YAAY,CAACU,MAAM,CAAC,IAAI,CAACD,KAAK,EAAEF,IAAI,CAACE,KAAK,CAAC;IAC5D;IACA,IAAIF,IAAI,CAACH,OAAO,KAAKI,SAAS,EAAE;MAC5B,IAAI,CAACJ,OAAO,GAAGG,IAAI,CAACH,OAAO;IAC/B;IACA,IAAIG,IAAI,CAACF,OAAO,KAAKG,SAAS,EAAE;MAC5B,IAAI,CAACH,OAAO,GAAGE,IAAI,CAACF,OAAO;IAC/B;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { Slower } from \"./Slower.js\";\nexport async function loadExternalSlowInteraction(engine, refresh = true) {\n  await engine.addInteractor(\"externalSlow\", container => {\n    return Promise.resolve(new Slower(container));\n  }, refresh);\n}\nexport * from \"./Options/Classes/Slow.js\";\nexport * from \"./Options/Interfaces/ISlow.js\";", "map": {"version": 3, "names": ["Slower", "loadExternalSlowInteraction", "engine", "refresh", "addInteractor", "container", "Promise", "resolve"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/interaction-external-slow/browser/index.js"], "sourcesContent": ["import { Slower } from \"./Slower.js\";\nexport async function loadExternalSlowInteraction(engine, refresh = true) {\n    await engine.addInteractor(\"externalSlow\", container => {\n        return Promise.resolve(new Slower(container));\n    }, refresh);\n}\nexport * from \"./Options/Classes/Slow.js\";\nexport * from \"./Options/Interfaces/ISlow.js\";\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,aAAa;AACpC,OAAO,eAAeC,2BAA2BA,CAACC,MAAM,EAAEC,OAAO,GAAG,IAAI,EAAE;EACtE,MAAMD,MAAM,CAACE,aAAa,CAAC,cAAc,EAAEC,SAAS,IAAI;IACpD,OAAOC,OAAO,CAACC,OAAO,CAAC,IAAIP,MAAM,CAACK,SAAS,CAAC,CAAC;EACjD,CAAC,EAAEF,OAAO,CAAC;AACf;AACA,cAAc,2BAA2B;AACzC,cAAc,+BAA+B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { clamp } from \"../../Utils\";\n\nfunction checkDestroy(particle, value, minValue, maxValue) {\n  switch (particle.options.size.animation.destroy) {\n    case \"max\":\n      if (value >= maxValue) {\n        particle.destroy();\n      }\n\n      break;\n\n    case \"min\":\n      if (value <= minValue) {\n        particle.destroy();\n      }\n\n      break;\n  }\n}\n\nfunction updateSize(particle, delta) {\n  var _a, _b, _c, _d;\n\n  const sizeVelocity = ((_a = particle.size.velocity) !== null && _a !== void 0 ? _a : 0) * delta.factor;\n  const minValue = particle.size.min;\n  const maxValue = particle.size.max;\n\n  if (particle.destroyed || !particle.size.enable || ((_b = particle.size.maxLoops) !== null && _b !== void 0 ? _b : 0) > 0 && ((_c = particle.size.loops) !== null && _c !== void 0 ? _c : 0) > ((_d = particle.size.maxLoops) !== null && _d !== void 0 ? _d : 0)) {\n    return;\n  }\n\n  switch (particle.size.status) {\n    case 0:\n      if (particle.size.value >= maxValue) {\n        particle.size.status = 1;\n\n        if (!particle.size.loops) {\n          particle.size.loops = 0;\n        }\n\n        particle.size.loops++;\n      } else {\n        particle.size.value += sizeVelocity;\n      }\n\n      break;\n\n    case 1:\n      if (particle.size.value <= minValue) {\n        particle.size.status = 0;\n\n        if (!particle.size.loops) {\n          particle.size.loops = 0;\n        }\n\n        particle.size.loops++;\n      } else {\n        particle.size.value -= sizeVelocity;\n      }\n\n  }\n\n  checkDestroy(particle, particle.size.value, minValue, maxValue);\n\n  if (!particle.destroyed) {\n    particle.size.value = clamp(particle.size.value, minValue, maxValue);\n  }\n}\n\nexport class SizeUpdater {\n  init() {}\n\n  isEnabled(particle) {\n    var _a, _b, _c, _d;\n\n    return !particle.destroyed && !particle.spawning && particle.size.enable && (((_a = particle.size.maxLoops) !== null && _a !== void 0 ? _a : 0) <= 0 || ((_b = particle.size.maxLoops) !== null && _b !== void 0 ? _b : 0) > 0 && ((_c = particle.size.loops) !== null && _c !== void 0 ? _c : 0) < ((_d = particle.size.maxLoops) !== null && _d !== void 0 ? _d : 0));\n  }\n\n  update(particle, delta) {\n    if (!this.isEnabled(particle)) {\n      return;\n    }\n\n    updateSize(particle, delta);\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Updaters/Size/SizeUpdater.js"], "names": ["clamp", "checkDestroy", "particle", "value", "minValue", "maxValue", "options", "size", "animation", "destroy", "updateSize", "delta", "_a", "_b", "_c", "_d", "sizeVelocity", "velocity", "factor", "min", "max", "destroyed", "enable", "max<PERSON><PERSON>s", "loops", "status", "SizeUpdater", "init", "isEnabled", "spawning", "update"], "mappings": "AAAA,SAASA,KAAT,QAAsB,aAAtB;;AACA,SAASC,YAAT,CAAsBC,QAAtB,EAAgCC,KAAhC,EAAuCC,QAAvC,EAAiDC,QAAjD,EAA2D;AACvD,UAAQH,QAAQ,CAACI,OAAT,CAAiBC,IAAjB,CAAsBC,SAAtB,CAAgCC,OAAxC;AACI,SAAK,KAAL;AACI,UAAIN,KAAK,IAAIE,QAAb,EAAuB;AACnBH,QAAAA,QAAQ,CAACO,OAAT;AACH;;AACD;;AACJ,SAAK,KAAL;AACI,UAAIN,KAAK,IAAIC,QAAb,EAAuB;AACnBF,QAAAA,QAAQ,CAACO,OAAT;AACH;;AACD;AAVR;AAYH;;AACD,SAASC,UAAT,CAAoBR,QAApB,EAA8BS,KAA9B,EAAqC;AACjC,MAAIC,EAAJ,EAAQC,EAAR,EAAYC,EAAZ,EAAgBC,EAAhB;;AACA,QAAMC,YAAY,GAAG,CAAC,CAACJ,EAAE,GAAGV,QAAQ,CAACK,IAAT,CAAcU,QAApB,MAAkC,IAAlC,IAA0CL,EAAE,KAAK,KAAK,CAAtD,GAA0DA,EAA1D,GAA+D,CAAhE,IAAqED,KAAK,CAACO,MAAhG;AACA,QAAMd,QAAQ,GAAGF,QAAQ,CAACK,IAAT,CAAcY,GAA/B;AACA,QAAMd,QAAQ,GAAGH,QAAQ,CAACK,IAAT,CAAca,GAA/B;;AACA,MAAIlB,QAAQ,CAACmB,SAAT,IACA,CAACnB,QAAQ,CAACK,IAAT,CAAce,MADf,IAEC,CAAC,CAACT,EAAE,GAAGX,QAAQ,CAACK,IAAT,CAAcgB,QAApB,MAAkC,IAAlC,IAA0CV,EAAE,KAAK,KAAK,CAAtD,GAA0DA,EAA1D,GAA+D,CAAhE,IAAqE,CAArE,IAA0E,CAAC,CAACC,EAAE,GAAGZ,QAAQ,CAACK,IAAT,CAAciB,KAApB,MAA+B,IAA/B,IAAuCV,EAAE,KAAK,KAAK,CAAnD,GAAuDA,EAAvD,GAA4D,CAA7D,KAAmE,CAACC,EAAE,GAAGb,QAAQ,CAACK,IAAT,CAAcgB,QAApB,MAAkC,IAAlC,IAA0CR,EAAE,KAAK,KAAK,CAAtD,GAA0DA,EAA1D,GAA+D,CAAlI,CAF/E,EAEsN;AAClN;AACH;;AACD,UAAQb,QAAQ,CAACK,IAAT,CAAckB,MAAtB;AACI,SAAK,CAAL;AACI,UAAIvB,QAAQ,CAACK,IAAT,CAAcJ,KAAd,IAAuBE,QAA3B,EAAqC;AACjCH,QAAAA,QAAQ,CAACK,IAAT,CAAckB,MAAd,GAAuB,CAAvB;;AACA,YAAI,CAACvB,QAAQ,CAACK,IAAT,CAAciB,KAAnB,EAA0B;AACtBtB,UAAAA,QAAQ,CAACK,IAAT,CAAciB,KAAd,GAAsB,CAAtB;AACH;;AACDtB,QAAAA,QAAQ,CAACK,IAAT,CAAciB,KAAd;AACH,OAND,MAOK;AACDtB,QAAAA,QAAQ,CAACK,IAAT,CAAcJ,KAAd,IAAuBa,YAAvB;AACH;;AACD;;AACJ,SAAK,CAAL;AACI,UAAId,QAAQ,CAACK,IAAT,CAAcJ,KAAd,IAAuBC,QAA3B,EAAqC;AACjCF,QAAAA,QAAQ,CAACK,IAAT,CAAckB,MAAd,GAAuB,CAAvB;;AACA,YAAI,CAACvB,QAAQ,CAACK,IAAT,CAAciB,KAAnB,EAA0B;AACtBtB,UAAAA,QAAQ,CAACK,IAAT,CAAciB,KAAd,GAAsB,CAAtB;AACH;;AACDtB,QAAAA,QAAQ,CAACK,IAAT,CAAciB,KAAd;AACH,OAND,MAOK;AACDtB,QAAAA,QAAQ,CAACK,IAAT,CAAcJ,KAAd,IAAuBa,YAAvB;AACH;;AAvBT;;AAyBAf,EAAAA,YAAY,CAACC,QAAD,EAAWA,QAAQ,CAACK,IAAT,CAAcJ,KAAzB,EAAgCC,QAAhC,EAA0CC,QAA1C,CAAZ;;AACA,MAAI,CAACH,QAAQ,CAACmB,SAAd,EAAyB;AACrBnB,IAAAA,QAAQ,CAACK,IAAT,CAAcJ,KAAd,GAAsBH,KAAK,CAACE,QAAQ,CAACK,IAAT,CAAcJ,KAAf,EAAsBC,QAAtB,EAAgCC,QAAhC,CAA3B;AACH;AACJ;;AACD,OAAO,MAAMqB,WAAN,CAAkB;AACrBC,EAAAA,IAAI,GAAG,CACN;;AACDC,EAAAA,SAAS,CAAC1B,QAAD,EAAW;AAChB,QAAIU,EAAJ,EAAQC,EAAR,EAAYC,EAAZ,EAAgBC,EAAhB;;AACA,WAAQ,CAACb,QAAQ,CAACmB,SAAV,IACJ,CAACnB,QAAQ,CAAC2B,QADN,IAEJ3B,QAAQ,CAACK,IAAT,CAAce,MAFV,KAGH,CAAC,CAACV,EAAE,GAAGV,QAAQ,CAACK,IAAT,CAAcgB,QAApB,MAAkC,IAAlC,IAA0CX,EAAE,KAAK,KAAK,CAAtD,GAA0DA,EAA1D,GAA+D,CAAhE,KAAsE,CAAtE,IACI,CAAC,CAACC,EAAE,GAAGX,QAAQ,CAACK,IAAT,CAAcgB,QAApB,MAAkC,IAAlC,IAA0CV,EAAE,KAAK,KAAK,CAAtD,GAA0DA,EAA1D,GAA+D,CAAhE,IAAqE,CAArE,IAA0E,CAAC,CAACC,EAAE,GAAGZ,QAAQ,CAACK,IAAT,CAAciB,KAApB,MAA+B,IAA/B,IAAuCV,EAAE,KAAK,KAAK,CAAnD,GAAuDA,EAAvD,GAA4D,CAA7D,KAAmE,CAACC,EAAE,GAAGb,QAAQ,CAACK,IAAT,CAAcgB,QAApB,MAAkC,IAAlC,IAA0CR,EAAE,KAAK,KAAK,CAAtD,GAA0DA,EAA1D,GAA+D,CAAlI,CAJ3E,CAAR;AAKH;;AACDe,EAAAA,MAAM,CAAC5B,QAAD,EAAWS,KAAX,EAAkB;AACpB,QAAI,CAAC,KAAKiB,SAAL,CAAe1B,QAAf,CAAL,EAA+B;AAC3B;AACH;;AACDQ,IAAAA,UAAU,CAACR,QAAD,EAAWS,KAAX,CAAV;AACH;;AAhBoB", "sourcesContent": ["import { clamp } from \"../../Utils\";\nfunction checkDestroy(particle, value, minValue, maxValue) {\n    switch (particle.options.size.animation.destroy) {\n        case \"max\":\n            if (value >= maxValue) {\n                particle.destroy();\n            }\n            break;\n        case \"min\":\n            if (value <= minValue) {\n                particle.destroy();\n            }\n            break;\n    }\n}\nfunction updateSize(particle, delta) {\n    var _a, _b, _c, _d;\n    const sizeVelocity = ((_a = particle.size.velocity) !== null && _a !== void 0 ? _a : 0) * delta.factor;\n    const minValue = particle.size.min;\n    const maxValue = particle.size.max;\n    if (particle.destroyed ||\n        !particle.size.enable ||\n        (((_b = particle.size.maxLoops) !== null && _b !== void 0 ? _b : 0) > 0 && ((_c = particle.size.loops) !== null && _c !== void 0 ? _c : 0) > ((_d = particle.size.maxLoops) !== null && _d !== void 0 ? _d : 0))) {\n        return;\n    }\n    switch (particle.size.status) {\n        case 0:\n            if (particle.size.value >= maxValue) {\n                particle.size.status = 1;\n                if (!particle.size.loops) {\n                    particle.size.loops = 0;\n                }\n                particle.size.loops++;\n            }\n            else {\n                particle.size.value += sizeVelocity;\n            }\n            break;\n        case 1:\n            if (particle.size.value <= minValue) {\n                particle.size.status = 0;\n                if (!particle.size.loops) {\n                    particle.size.loops = 0;\n                }\n                particle.size.loops++;\n            }\n            else {\n                particle.size.value -= sizeVelocity;\n            }\n    }\n    checkDestroy(particle, particle.size.value, minValue, maxValue);\n    if (!particle.destroyed) {\n        particle.size.value = clamp(particle.size.value, minValue, maxValue);\n    }\n}\nexport class SizeUpdater {\n    init() {\n    }\n    isEnabled(particle) {\n        var _a, _b, _c, _d;\n        return (!particle.destroyed &&\n            !particle.spawning &&\n            particle.size.enable &&\n            (((_a = particle.size.maxLoops) !== null && _a !== void 0 ? _a : 0) <= 0 ||\n                (((_b = particle.size.maxLoops) !== null && _b !== void 0 ? _b : 0) > 0 && ((_c = particle.size.loops) !== null && _c !== void 0 ? _c : 0) < ((_d = particle.size.maxLoops) !== null && _d !== void 0 ? _d : 0))));\n    }\n    update(particle, delta) {\n        if (!this.isEnabled(particle)) {\n            return;\n        }\n        updateSize(particle, delta);\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
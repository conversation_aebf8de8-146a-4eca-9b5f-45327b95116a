{"ast": null, "code": "import { DestroyBounds } from \"./DestroyBounds.js\";\nimport { DestroyMode } from \"../../Enums/DestroyMode.js\";\nimport { Split } from \"./Split.js\";\nexport class Destroy {\n  constructor() {\n    this.bounds = new DestroyBounds();\n    this.mode = DestroyMode.none;\n    this.split = new Split();\n  }\n  load(data) {\n    if (!data) {\n      return;\n    }\n    if (data.mode) {\n      this.mode = data.mode;\n    }\n    if (data.bounds) {\n      this.bounds.load(data.bounds);\n    }\n    this.split.load(data.split);\n  }\n}", "map": {"version": 3, "names": ["DestroyBounds", "DestroyMode", "Split", "Destroy", "constructor", "bounds", "mode", "none", "split", "load", "data"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/updater-destroy/browser/Options/Classes/Destroy.js"], "sourcesContent": ["import { DestroyBounds } from \"./DestroyBounds.js\";\nimport { DestroyMode } from \"../../Enums/DestroyMode.js\";\nimport { Split } from \"./Split.js\";\nexport class Destroy {\n    constructor() {\n        this.bounds = new DestroyBounds();\n        this.mode = DestroyMode.none;\n        this.split = new Split();\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.mode) {\n            this.mode = data.mode;\n        }\n        if (data.bounds) {\n            this.bounds.load(data.bounds);\n        }\n        this.split.load(data.split);\n    }\n}\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,oBAAoB;AAClD,SAASC,WAAW,QAAQ,4BAA4B;AACxD,SAASC,KAAK,QAAQ,YAAY;AAClC,OAAO,MAAMC,OAAO,CAAC;EACjBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,MAAM,GAAG,IAAIL,aAAa,CAAC,CAAC;IACjC,IAAI,CAACM,IAAI,GAAGL,WAAW,CAACM,IAAI;IAC5B,IAAI,CAACC,KAAK,GAAG,IAAIN,KAAK,CAAC,CAAC;EAC5B;EACAO,IAAIA,CAACC,IAAI,EAAE;IACP,IAAI,CAACA,IAAI,EAAE;MACP;IACJ;IACA,IAAIA,IAAI,CAACJ,IAAI,EAAE;MACX,IAAI,CAACA,IAAI,GAAGI,IAAI,CAACJ,IAAI;IACzB;IACA,IAAII,IAAI,CAACL,MAAM,EAAE;MACb,IAAI,CAACA,MAAM,CAACI,IAAI,CAACC,IAAI,CAACL,MAAM,CAAC;IACjC;IACA,IAAI,CAACG,KAAK,CAACC,IAAI,CAACC,IAAI,CAACF,KAAK,CAAC;EAC/B;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
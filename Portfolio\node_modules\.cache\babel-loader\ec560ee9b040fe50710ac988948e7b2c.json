{"ast": null, "code": "export class Constants {}\nConstants.generatedAttribute = \"generated\";\nConstants.randomColorValue = \"random\";\nConstants.midColorValue = \"mid\";\nConstants.touchEndEvent = \"touchend\";\nConstants.mouseDownEvent = \"mousedown\";\nConstants.mouseUpEvent = \"mouseup\";\nConstants.mouseMoveEvent = \"mousemove\";\nConstants.touchStartEvent = \"touchstart\";\nConstants.touchMoveEvent = \"touchmove\";\nConstants.mouseLeaveEvent = \"mouseleave\";\nConstants.mouseOutEvent = \"mouseout\";\nConstants.touchCancelEvent = \"touchcancel\";\nConstants.resizeEvent = \"resize\";\nConstants.visibilityChangeEvent = \"visibilitychange\";\nConstants.noPolygonDataLoaded = \"No polygon data loaded.\";\nConstants.noPolygonFound = \"No polygon found, you need to specify SVG url in config.\";", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Core/Utils/Constants.js"], "names": ["Constants", "generatedAttribute", "randomColorValue", "midColorValue", "touchEndEvent", "mouseDownEvent", "mouseUpEvent", "mouseMoveEvent", "touchStartEvent", "touchMoveEvent", "mouseLeaveEvent", "mouseOutEvent", "touchCancelEvent", "resizeEvent", "visibilityChangeEvent", "noPolygonDataLoaded", "noPolygonFound"], "mappings": "AAAA,OAAO,MAAMA,SAAN,CAAgB;AAEvBA,SAAS,CAACC,kBAAV,GAA+B,WAA/B;AACAD,SAAS,CAACE,gBAAV,GAA6B,QAA7B;AACAF,SAAS,CAACG,aAAV,GAA0B,KAA1B;AACAH,SAAS,CAACI,aAAV,GAA0B,UAA1B;AACAJ,SAAS,CAACK,cAAV,GAA2B,WAA3B;AACAL,SAAS,CAACM,YAAV,GAAyB,SAAzB;AACAN,SAAS,CAACO,cAAV,GAA2B,WAA3B;AACAP,SAAS,CAACQ,eAAV,GAA4B,YAA5B;AACAR,SAAS,CAACS,cAAV,GAA2B,WAA3B;AACAT,SAAS,CAACU,eAAV,GAA4B,YAA5B;AACAV,SAAS,CAACW,aAAV,GAA0B,UAA1B;AACAX,SAAS,CAACY,gBAAV,GAA6B,aAA7B;AACAZ,SAAS,CAACa,WAAV,GAAwB,QAAxB;AACAb,SAAS,CAACc,qBAAV,GAAkC,kBAAlC;AACAd,SAAS,CAACe,mBAAV,GAAgC,yBAAhC;AACAf,SAAS,CAACgB,cAAV,GAA2B,0DAA3B", "sourcesContent": ["export class Constants {\n}\nConstants.generatedAttribute = \"generated\";\nConstants.randomColorValue = \"random\";\nConstants.midColorValue = \"mid\";\nConstants.touchEndEvent = \"touchend\";\nConstants.mouseDownEvent = \"mousedown\";\nConstants.mouseUpEvent = \"mouseup\";\nConstants.mouseMoveEvent = \"mousemove\";\nConstants.touchStartEvent = \"touchstart\";\nConstants.touchMoveEvent = \"touchmove\";\nConstants.mouseLeaveEvent = \"mouseleave\";\nConstants.mouseOutEvent = \"mouseout\";\nConstants.touchCancelEvent = \"touchcancel\";\nConstants.resizeEvent = \"resize\";\nConstants.visibilityChangeEvent = \"visibilitychange\";\nConstants.noPolygonDataLoaded = \"No polygon data loaded.\";\nConstants.noPolygonFound = \"No polygon found, you need to specify SVG url in config.\";\n"]}, "metadata": {}, "sourceType": "module"}
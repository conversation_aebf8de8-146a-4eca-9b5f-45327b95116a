{"ast": null, "code": "import { RangedAnimationValueWithRandom } from \"../../ValueWithRandom.js\";\nimport { SizeAnimation } from \"./SizeAnimation.js\";\nexport class Size extends RangedAnimationValueWithRandom {\n  constructor() {\n    super();\n    this.animation = new SizeAnimation();\n    this.value = 3;\n  }\n  load(data) {\n    super.load(data);\n    if (!data) {\n      return;\n    }\n    const animation = data.animation;\n    if (animation !== undefined) {\n      this.animation.load(animation);\n    }\n  }\n}", "map": {"version": 3, "names": ["RangedAnimationValueWithRandom", "SizeAnimation", "Size", "constructor", "animation", "value", "load", "data", "undefined"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/engine/browser/Options/Classes/Particles/Size/Size.js"], "sourcesContent": ["import { RangedAnimationValueWithRandom } from \"../../ValueWithRandom.js\";\nimport { SizeAnimation } from \"./SizeAnimation.js\";\nexport class Size extends RangedAnimationValueWithRandom {\n    constructor() {\n        super();\n        this.animation = new SizeAnimation();\n        this.value = 3;\n    }\n    load(data) {\n        super.load(data);\n        if (!data) {\n            return;\n        }\n        const animation = data.animation;\n        if (animation !== undefined) {\n            this.animation.load(animation);\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,8BAA8B,QAAQ,0BAA0B;AACzE,SAASC,aAAa,QAAQ,oBAAoB;AAClD,OAAO,MAAMC,IAAI,SAASF,8BAA8B,CAAC;EACrDG,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,SAAS,GAAG,IAAIH,aAAa,CAAC,CAAC;IACpC,IAAI,CAACI,KAAK,GAAG,CAAC;EAClB;EACAC,IAAIA,CAACC,IAAI,EAAE;IACP,KAAK,CAACD,IAAI,CAACC,IAAI,CAAC;IAChB,IAAI,CAACA,IAAI,EAAE;MACP;IACJ;IACA,MAAMH,SAAS,GAAGG,IAAI,CAACH,SAAS;IAChC,IAAIA,SAAS,KAAKI,SAAS,EAAE;MACzB,IAAI,CAACJ,SAAS,CAACE,IAAI,CAACF,SAAS,CAAC;IAClC;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "export class EmitterSize {\n  constructor() {\n    this.mode = \"percent\";\n    this.height = 0;\n    this.width = 0;\n  }\n\n  load(data) {\n    if (data === undefined) {\n      return;\n    }\n\n    if (data.mode !== undefined) {\n      this.mode = data.mode;\n    }\n\n    if (data.height !== undefined) {\n      this.height = data.height;\n    }\n\n    if (data.width !== undefined) {\n      this.width = data.width;\n    }\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Plugins/Emitters/Options/Classes/EmitterSize.js"], "names": ["EmitterSize", "constructor", "mode", "height", "width", "load", "data", "undefined"], "mappings": "AAAA,OAAO,MAAMA,WAAN,CAAkB;AACrBC,EAAAA,WAAW,GAAG;AACV,SAAKC,IAAL,GAAY,SAAZ;AACA,SAAKC,MAAL,GAAc,CAAd;AACA,SAAKC,KAAL,GAAa,CAAb;AACH;;AACDC,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,QAAIA,IAAI,KAAKC,SAAb,EAAwB;AACpB;AACH;;AACD,QAAID,IAAI,CAACJ,IAAL,KAAcK,SAAlB,EAA6B;AACzB,WAAKL,IAAL,GAAYI,IAAI,CAACJ,IAAjB;AACH;;AACD,QAAII,IAAI,CAACH,MAAL,KAAgBI,SAApB,EAA+B;AAC3B,WAAKJ,MAAL,GAAcG,IAAI,CAACH,MAAnB;AACH;;AACD,QAAIG,IAAI,CAACF,KAAL,KAAeG,SAAnB,EAA8B;AAC1B,WAAKH,KAAL,GAAaE,IAAI,CAACF,KAAlB;AACH;AACJ;;AAnBoB", "sourcesContent": ["export class EmitterSize {\n    constructor() {\n        this.mode = \"percent\";\n        this.height = 0;\n        this.width = 0;\n    }\n    load(data) {\n        if (data === undefined) {\n            return;\n        }\n        if (data.mode !== undefined) {\n            this.mode = data.mode;\n        }\n        if (data.height !== undefined) {\n            this.height = data.height;\n        }\n        if (data.width !== undefined) {\n            this.width = data.width;\n        }\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
{"ast": null, "code": "export default function buildMatchPatternFn(args) {\n  return function (string) {\n    var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    var matchResult = string.match(args.matchPattern);\n    if (!matchResult) return null;\n    var matchedString = matchResult[0];\n    var parseResult = string.match(args.parsePattern);\n    if (!parseResult) return null;\n    var value = args.valueCallback ? args.valueCallback(parseResult[0]) : parseResult[0];\n    value = options.valueCallback ? options.valueCallback(value) : value;\n    var rest = string.slice(matchedString.length);\n    return {\n      value: value,\n      rest: rest\n    };\n  };\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/date-fns/esm/locale/_lib/buildMatchPatternFn/index.js"], "names": ["buildMatchPatternFn", "args", "string", "options", "arguments", "length", "undefined", "matchResult", "match", "matchPattern", "matchedString", "parseResult", "parsePattern", "value", "valueCallback", "rest", "slice"], "mappings": "AAAA,eAAe,SAASA,mBAAT,CAA6BC,IAA7B,EAAmC;AAChD,SAAO,UAAUC,MAAV,EAAkB;AACvB,QAAIC,OAAO,GAAGC,SAAS,CAACC,MAAV,GAAmB,CAAnB,IAAwBD,SAAS,CAAC,CAAD,CAAT,KAAiBE,SAAzC,GAAqDF,SAAS,CAAC,CAAD,CAA9D,GAAoE,EAAlF;AACA,QAAIG,WAAW,GAAGL,MAAM,CAACM,KAAP,CAAaP,IAAI,CAACQ,YAAlB,CAAlB;AACA,QAAI,CAACF,WAAL,EAAkB,OAAO,IAAP;AAClB,QAAIG,aAAa,GAAGH,WAAW,CAAC,CAAD,CAA/B;AACA,QAAII,WAAW,GAAGT,MAAM,CAACM,KAAP,CAAaP,IAAI,CAACW,YAAlB,CAAlB;AACA,QAAI,CAACD,WAAL,EAAkB,OAAO,IAAP;AAClB,QAAIE,KAAK,GAAGZ,IAAI,CAACa,aAAL,GAAqBb,IAAI,CAACa,aAAL,CAAmBH,WAAW,CAAC,CAAD,CAA9B,CAArB,GAA0DA,WAAW,CAAC,CAAD,CAAjF;AACAE,IAAAA,KAAK,GAAGV,OAAO,CAACW,aAAR,GAAwBX,OAAO,CAACW,aAAR,CAAsBD,KAAtB,CAAxB,GAAuDA,KAA/D;AACA,QAAIE,IAAI,GAAGb,MAAM,CAACc,KAAP,CAAaN,aAAa,CAACL,MAA3B,CAAX;AACA,WAAO;AACLQ,MAAAA,KAAK,EAAEA,KADF;AAELE,MAAAA,IAAI,EAAEA;AAFD,KAAP;AAID,GAdD;AAeD", "sourcesContent": ["export default function buildMatchPatternFn(args) {\n  return function (string) {\n    var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    var matchResult = string.match(args.matchPattern);\n    if (!matchResult) return null;\n    var matchedString = matchResult[0];\n    var parseResult = string.match(args.parsePattern);\n    if (!parseResult) return null;\n    var value = args.valueCallback ? args.valueCallback(parseResult[0]) : parseResult[0];\n    value = options.valueCallback ? options.valueCallback(value) : value;\n    var rest = string.slice(matchedString.length);\n    return {\n      value: value,\n      rest: rest\n    };\n  };\n}"]}, "metadata": {}, "sourceType": "module"}
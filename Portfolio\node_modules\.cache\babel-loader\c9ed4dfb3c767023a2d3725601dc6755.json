{"ast": null, "code": "import toDate from \"../../toDate/index.js\";\nimport requiredArgs from \"../requiredArgs/index.js\";\nimport startOfUTCISOWeek from \"../startOfUTCISOWeek/index.js\"; // This function will be a part of public API when UTC function will be implemented.\n// See issue: https://github.com/date-fns/date-fns/issues/376\n\nexport default function getUTCISOWeekYear(dirtyDate) {\n  requiredArgs(1, arguments);\n  var date = toDate(dirtyDate);\n  var year = date.getUTCFullYear();\n  var fourthOfJanuaryOfNextYear = new Date(0);\n  fourthOfJanuaryOfNextYear.setUTCFullYear(year + 1, 0, 4);\n  fourthOfJanuaryOfNextYear.setUTCHours(0, 0, 0, 0);\n  var startOfNextYear = startOfUTCISOWeek(fourthOfJanuaryOfNextYear);\n  var fourthOfJanuaryOfThisYear = new Date(0);\n  fourthOfJanuaryOfThisYear.setUTCFullYear(year, 0, 4);\n  fourthOfJanuaryOfThisYear.setUTCHours(0, 0, 0, 0);\n  var startOfThisYear = startOfUTCISOWeek(fourthOfJanuaryOfThisYear);\n\n  if (date.getTime() >= startOfNextYear.getTime()) {\n    return year + 1;\n  } else if (date.getTime() >= startOfThisYear.getTime()) {\n    return year;\n  } else {\n    return year - 1;\n  }\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/date-fns/esm/_lib/getUTCISOWeekYear/index.js"], "names": ["toDate", "requiredArgs", "startOfUTCISOWeek", "getUTCISOWeekYear", "dirtyDate", "arguments", "date", "year", "getUTCFullYear", "fourthOfJanuaryOfNextYear", "Date", "setUTCFullYear", "setUTCHours", "startOfNextYear", "fourthOfJanuaryOfThisYear", "startOfThisYear", "getTime"], "mappings": "AAAA,OAAOA,MAAP,MAAmB,uBAAnB;AACA,OAAOC,YAAP,MAAyB,0BAAzB;AACA,OAAOC,iBAAP,MAA8B,+BAA9B,C,CAA+D;AAC/D;;AAEA,eAAe,SAASC,iBAAT,CAA2BC,SAA3B,EAAsC;AACnDH,EAAAA,YAAY,CAAC,CAAD,EAAII,SAAJ,CAAZ;AACA,MAAIC,IAAI,GAAGN,MAAM,CAACI,SAAD,CAAjB;AACA,MAAIG,IAAI,GAAGD,IAAI,CAACE,cAAL,EAAX;AACA,MAAIC,yBAAyB,GAAG,IAAIC,IAAJ,CAAS,CAAT,CAAhC;AACAD,EAAAA,yBAAyB,CAACE,cAA1B,CAAyCJ,IAAI,GAAG,CAAhD,EAAmD,CAAnD,EAAsD,CAAtD;AACAE,EAAAA,yBAAyB,CAACG,WAA1B,CAAsC,CAAtC,EAAyC,CAAzC,EAA4C,CAA5C,EAA+C,CAA/C;AACA,MAAIC,eAAe,GAAGX,iBAAiB,CAACO,yBAAD,CAAvC;AACA,MAAIK,yBAAyB,GAAG,IAAIJ,IAAJ,CAAS,CAAT,CAAhC;AACAI,EAAAA,yBAAyB,CAACH,cAA1B,CAAyCJ,IAAzC,EAA+C,CAA/C,EAAkD,CAAlD;AACAO,EAAAA,yBAAyB,CAACF,WAA1B,CAAsC,CAAtC,EAAyC,CAAzC,EAA4C,CAA5C,EAA+C,CAA/C;AACA,MAAIG,eAAe,GAAGb,iBAAiB,CAACY,yBAAD,CAAvC;;AAEA,MAAIR,IAAI,CAACU,OAAL,MAAkBH,eAAe,CAACG,OAAhB,EAAtB,EAAiD;AAC/C,WAAOT,IAAI,GAAG,CAAd;AACD,GAFD,MAEO,IAAID,IAAI,CAACU,OAAL,MAAkBD,eAAe,CAACC,OAAhB,EAAtB,EAAiD;AACtD,WAAOT,IAAP;AACD,GAFM,MAEA;AACL,WAAOA,IAAI,GAAG,CAAd;AACD;AACF", "sourcesContent": ["import toDate from \"../../toDate/index.js\";\nimport requiredArgs from \"../requiredArgs/index.js\";\nimport startOfUTCISOWeek from \"../startOfUTCISOWeek/index.js\"; // This function will be a part of public API when UTC function will be implemented.\n// See issue: https://github.com/date-fns/date-fns/issues/376\n\nexport default function getUTCISOWeekYear(dirtyDate) {\n  requiredArgs(1, arguments);\n  var date = toDate(dirtyDate);\n  var year = date.getUTCFullYear();\n  var fourthOfJanuaryOfNextYear = new Date(0);\n  fourthOfJanuaryOfNextYear.setUTCFullYear(year + 1, 0, 4);\n  fourthOfJanuaryOfNextYear.setUTCHours(0, 0, 0, 0);\n  var startOfNextYear = startOfUTCISOWeek(fourthOfJanuaryOfNextYear);\n  var fourthOfJanuaryOfThisYear = new Date(0);\n  fourthOfJanuaryOfThisYear.setUTCFullYear(year, 0, 4);\n  fourthOfJanuaryOfThisYear.setUTCHours(0, 0, 0, 0);\n  var startOfThisYear = startOfUTCISOWeek(fourthOfJanuaryOfThisYear);\n\n  if (date.getTime() >= startOfNextYear.getTime()) {\n    return year + 1;\n  } else if (date.getTime() >= startOfThisYear.getTime()) {\n    return year;\n  } else {\n    return year - 1;\n  }\n}"]}, "metadata": {}, "sourceType": "module"}
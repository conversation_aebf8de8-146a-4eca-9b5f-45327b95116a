{"ast": null, "code": "export class Bounce {\n  constructor() {\n    this.distance = 200;\n  }\n\n  load(data) {\n    if (!data) {\n      return;\n    }\n\n    if (data.distance !== undefined) {\n      this.distance = data.distance;\n    }\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Options/Classes/Interactivity/Modes/Bounce.js"], "names": ["<PERSON><PERSON><PERSON>", "constructor", "distance", "load", "data", "undefined"], "mappings": "AAAA,OAAO,MAAMA,MAAN,CAAa;AAChBC,EAAAA,WAAW,GAAG;AACV,SAAKC,QAAL,GAAgB,GAAhB;AACH;;AACDC,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,QAAI,CAACA,IAAL,EAAW;AACP;AACH;;AACD,QAAIA,IAAI,CAACF,QAAL,KAAkBG,SAAtB,EAAiC;AAC7B,WAAKH,QAAL,GAAgBE,IAAI,CAACF,QAArB;AACH;AACJ;;AAXe", "sourcesContent": ["export class Bounce {\n    constructor() {\n        this.distance = 200;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.distance !== undefined) {\n            this.distance = data.distance;\n        }\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
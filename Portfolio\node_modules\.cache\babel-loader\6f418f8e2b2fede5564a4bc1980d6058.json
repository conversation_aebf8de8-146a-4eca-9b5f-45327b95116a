{"ast": null, "code": "export class AbsorberSizeLimit {\n  constructor() {\n    this.radius = 0;\n    this.mass = 0;\n  }\n\n  load(data) {\n    if (!data) {\n      return;\n    }\n\n    if (data.mass !== undefined) {\n      this.mass = data.mass;\n    }\n\n    if (data.radius !== undefined) {\n      this.radius = data.radius;\n    }\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Plugins/Absorbers/Options/Classes/AbsorberSizeLimit.js"], "names": ["AbsorberSizeLimit", "constructor", "radius", "mass", "load", "data", "undefined"], "mappings": "AAAA,OAAO,MAAMA,iBAAN,CAAwB;AAC3BC,EAAAA,WAAW,GAAG;AACV,SAAKC,MAAL,GAAc,CAAd;AACA,SAAKC,IAAL,GAAY,CAAZ;AACH;;AACDC,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,QAAI,CAACA,IAAL,EAAW;AACP;AACH;;AACD,QAAIA,IAAI,CAACF,IAAL,KAAcG,SAAlB,EAA6B;AACzB,WAAKH,IAAL,GAAYE,IAAI,CAACF,IAAjB;AACH;;AACD,QAAIE,IAAI,CAACH,MAAL,KAAgBI,SAApB,EAA+B;AAC3B,WAAKJ,MAAL,GAAcG,IAAI,CAACH,MAAnB;AACH;AACJ;;AAf0B", "sourcesContent": ["export class AbsorberSizeLimit {\n    constructor() {\n        this.radius = 0;\n        this.mass = 0;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.mass !== undefined) {\n            this.mass = data.mass;\n        }\n        if (data.radius !== undefined) {\n            this.radius = data.radius;\n        }\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
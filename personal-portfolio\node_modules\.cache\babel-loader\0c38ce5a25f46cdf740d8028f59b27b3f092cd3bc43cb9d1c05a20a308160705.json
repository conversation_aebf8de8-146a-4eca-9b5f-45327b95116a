{"ast": null, "code": "import { itemFromSingleOrMultiple } from \"@tsparticles/engine\";\nconst double = 2,\n  half = 0.5;\nexport function drawText(data) {\n  const {\n      context,\n      particle,\n      radius,\n      opacity\n    } = data,\n    character = particle.shapeData;\n  if (!character) {\n    return;\n  }\n  const textData = character.value;\n  if (textData === undefined) {\n    return;\n  }\n  if (particle.text === undefined) {\n    particle.text = itemFromSingleOrMultiple(textData, particle.randomIndexData);\n  }\n  const text = particle.text,\n    style = character.style ?? \"\",\n    weight = character.weight ?? \"400\",\n    size = Math.round(radius) * double,\n    font = character.font ?? \"Verdana\",\n    fill = particle.shapeFill;\n  const lines = text?.split(\"\\n\");\n  if (!lines) {\n    return;\n  }\n  context.font = `${style} ${weight} ${size}px \"${font}\"`;\n  context.globalAlpha = opacity;\n  for (let i = 0; i < lines.length; i++) {\n    drawLine(context, lines[i], radius, opacity, i, fill);\n  }\n  context.globalAlpha = 1;\n}\nfunction drawLine(context, line, radius, opacity, index, fill) {\n  const offsetX = line.length * radius * half,\n    pos = {\n      x: -offsetX,\n      y: radius * half\n    },\n    diameter = radius * double;\n  if (fill) {\n    context.fillText(line, pos.x, pos.y + diameter * index);\n  } else {\n    context.strokeText(line, pos.x, pos.y + diameter * index);\n  }\n}", "map": {"version": 3, "names": ["itemFromSingleOrMultiple", "double", "half", "drawText", "data", "context", "particle", "radius", "opacity", "character", "shapeData", "textData", "value", "undefined", "text", "randomIndexData", "style", "weight", "size", "Math", "round", "font", "fill", "shapeFill", "lines", "split", "globalAlpha", "i", "length", "drawLine", "line", "index", "offsetX", "pos", "x", "y", "diameter", "fillText", "strokeText"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/shape-text/browser/Utils.js"], "sourcesContent": ["import { itemFromSingleOrMultiple } from \"@tsparticles/engine\";\nconst double = 2, half = 0.5;\nexport function drawText(data) {\n    const { context, particle, radius, opacity } = data, character = particle.shapeData;\n    if (!character) {\n        return;\n    }\n    const textData = character.value;\n    if (textData === undefined) {\n        return;\n    }\n    if (particle.text === undefined) {\n        particle.text = itemFromSingleOrMultiple(textData, particle.randomIndexData);\n    }\n    const text = particle.text, style = character.style ?? \"\", weight = character.weight ?? \"400\", size = Math.round(radius) * double, font = character.font ?? \"Verdana\", fill = particle.shapeFill;\n    const lines = text?.split(\"\\n\");\n    if (!lines) {\n        return;\n    }\n    context.font = `${style} ${weight} ${size}px \"${font}\"`;\n    context.globalAlpha = opacity;\n    for (let i = 0; i < lines.length; i++) {\n        drawLine(context, lines[i], radius, opacity, i, fill);\n    }\n    context.globalAlpha = 1;\n}\nfunction drawLine(context, line, radius, opacity, index, fill) {\n    const offsetX = line.length * radius * half, pos = {\n        x: -offsetX,\n        y: radius * half,\n    }, diameter = radius * double;\n    if (fill) {\n        context.fillText(line, pos.x, pos.y + diameter * index);\n    }\n    else {\n        context.strokeText(line, pos.x, pos.y + diameter * index);\n    }\n}\n"], "mappings": "AAAA,SAASA,wBAAwB,QAAQ,qBAAqB;AAC9D,MAAMC,MAAM,GAAG,CAAC;EAAEC,IAAI,GAAG,GAAG;AAC5B,OAAO,SAASC,QAAQA,CAACC,IAAI,EAAE;EAC3B,MAAM;MAAEC,OAAO;MAAEC,QAAQ;MAAEC,MAAM;MAAEC;IAAQ,CAAC,GAAGJ,IAAI;IAAEK,SAAS,GAAGH,QAAQ,CAACI,SAAS;EACnF,IAAI,CAACD,SAAS,EAAE;IACZ;EACJ;EACA,MAAME,QAAQ,GAAGF,SAAS,CAACG,KAAK;EAChC,IAAID,QAAQ,KAAKE,SAAS,EAAE;IACxB;EACJ;EACA,IAAIP,QAAQ,CAACQ,IAAI,KAAKD,SAAS,EAAE;IAC7BP,QAAQ,CAACQ,IAAI,GAAGd,wBAAwB,CAACW,QAAQ,EAAEL,QAAQ,CAACS,eAAe,CAAC;EAChF;EACA,MAAMD,IAAI,GAAGR,QAAQ,CAACQ,IAAI;IAAEE,KAAK,GAAGP,SAAS,CAACO,KAAK,IAAI,EAAE;IAAEC,MAAM,GAAGR,SAAS,CAACQ,MAAM,IAAI,KAAK;IAAEC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACb,MAAM,CAAC,GAAGN,MAAM;IAAEoB,IAAI,GAAGZ,SAAS,CAACY,IAAI,IAAI,SAAS;IAAEC,IAAI,GAAGhB,QAAQ,CAACiB,SAAS;EAChM,MAAMC,KAAK,GAAGV,IAAI,EAAEW,KAAK,CAAC,IAAI,CAAC;EAC/B,IAAI,CAACD,KAAK,EAAE;IACR;EACJ;EACAnB,OAAO,CAACgB,IAAI,GAAG,GAAGL,KAAK,IAAIC,MAAM,IAAIC,IAAI,OAAOG,IAAI,GAAG;EACvDhB,OAAO,CAACqB,WAAW,GAAGlB,OAAO;EAC7B,KAAK,IAAImB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,KAAK,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;IACnCE,QAAQ,CAACxB,OAAO,EAAEmB,KAAK,CAACG,CAAC,CAAC,EAAEpB,MAAM,EAAEC,OAAO,EAAEmB,CAAC,EAAEL,IAAI,CAAC;EACzD;EACAjB,OAAO,CAACqB,WAAW,GAAG,CAAC;AAC3B;AACA,SAASG,QAAQA,CAACxB,OAAO,EAAEyB,IAAI,EAAEvB,MAAM,EAAEC,OAAO,EAAEuB,KAAK,EAAET,IAAI,EAAE;EAC3D,MAAMU,OAAO,GAAGF,IAAI,CAACF,MAAM,GAAGrB,MAAM,GAAGL,IAAI;IAAE+B,GAAG,GAAG;MAC/CC,CAAC,EAAE,CAACF,OAAO;MACXG,CAAC,EAAE5B,MAAM,GAAGL;IAChB,CAAC;IAAEkC,QAAQ,GAAG7B,MAAM,GAAGN,MAAM;EAC7B,IAAIqB,IAAI,EAAE;IACNjB,OAAO,CAACgC,QAAQ,CAACP,IAAI,EAAEG,GAAG,CAACC,CAAC,EAAED,GAAG,CAACE,CAAC,GAAGC,QAAQ,GAAGL,KAAK,CAAC;EAC3D,CAAC,MACI;IACD1B,OAAO,CAACiC,UAAU,CAACR,IAAI,EAAEG,GAAG,CAACC,CAAC,EAAED,GAAG,CAACE,CAAC,GAAGC,QAAQ,GAAGL,KAAK,CAAC;EAC7D;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
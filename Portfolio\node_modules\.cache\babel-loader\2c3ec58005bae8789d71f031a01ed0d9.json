{"ast": null, "code": "import buildFormatLongFn from \"../../../_lib/buildFormatLongFn/index.js\";\nvar dateFormats = {\n  full: 'EEEE, MMMM do, y',\n  long: 'MMMM do, y',\n  medium: 'MMM d, y',\n  short: 'MM/dd/yyyy'\n};\nvar timeFormats = {\n  full: 'h:mm:ss a zzzz',\n  long: 'h:mm:ss a z',\n  medium: 'h:mm:ss a',\n  short: 'h:mm a'\n};\nvar dateTimeFormats = {\n  full: \"{{date}} 'at' {{time}}\",\n  long: \"{{date}} 'at' {{time}}\",\n  medium: '{{date}}, {{time}}',\n  short: '{{date}}, {{time}}'\n};\nvar formatLong = {\n  date: buildFormatLongFn({\n    formats: dateFormats,\n    defaultWidth: 'full'\n  }),\n  time: buildFormatLongFn({\n    formats: timeFormats,\n    defaultWidth: 'full'\n  }),\n  dateTime: buildFormatLongFn({\n    formats: dateTimeFormats,\n    defaultWidth: 'full'\n  })\n};\nexport default formatLong;", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/date-fns/esm/locale/en-US/_lib/formatLong/index.js"], "names": ["buildFormatLongFn", "dateFormats", "full", "long", "medium", "short", "timeFormats", "dateTimeFormats", "formatLong", "date", "formats", "defaultWidth", "time", "dateTime"], "mappings": "AAAA,OAAOA,iBAAP,MAA8B,0CAA9B;AACA,IAAIC,WAAW,GAAG;AAChBC,EAAAA,IAAI,EAAE,kBADU;AAEhBC,EAAAA,IAAI,EAAE,YAFU;AAGhBC,EAAAA,MAAM,EAAE,UAHQ;AAIhBC,EAAAA,KAAK,EAAE;AAJS,CAAlB;AAMA,IAAIC,WAAW,GAAG;AAChBJ,EAAAA,IAAI,EAAE,gBADU;AAEhBC,EAAAA,IAAI,EAAE,aAFU;AAGhBC,EAAAA,MAAM,EAAE,WAHQ;AAIhBC,EAAAA,KAAK,EAAE;AAJS,CAAlB;AAMA,IAAIE,eAAe,GAAG;AACpBL,EAAAA,IAAI,EAAE,wBADc;AAEpBC,EAAAA,IAAI,EAAE,wBAFc;AAGpBC,EAAAA,MAAM,EAAE,oBAHY;AAIpBC,EAAAA,KAAK,EAAE;AAJa,CAAtB;AAMA,IAAIG,UAAU,GAAG;AACfC,EAAAA,IAAI,EAAET,iBAAiB,CAAC;AACtBU,IAAAA,OAAO,EAAET,WADa;AAEtBU,IAAAA,YAAY,EAAE;AAFQ,GAAD,CADR;AAKfC,EAAAA,IAAI,EAAEZ,iBAAiB,CAAC;AACtBU,IAAAA,OAAO,EAAEJ,WADa;AAEtBK,IAAAA,YAAY,EAAE;AAFQ,GAAD,CALR;AASfE,EAAAA,QAAQ,EAAEb,iBAAiB,CAAC;AAC1BU,IAAAA,OAAO,EAAEH,eADiB;AAE1BI,IAAAA,YAAY,EAAE;AAFY,GAAD;AATZ,CAAjB;AAcA,eAAeH,UAAf", "sourcesContent": ["import buildFormatLongFn from \"../../../_lib/buildFormatLongFn/index.js\";\nvar dateFormats = {\n  full: 'EEEE, MMMM do, y',\n  long: 'MMMM do, y',\n  medium: 'MMM d, y',\n  short: 'MM/dd/yyyy'\n};\nvar timeFormats = {\n  full: 'h:mm:ss a zzzz',\n  long: 'h:mm:ss a z',\n  medium: 'h:mm:ss a',\n  short: 'h:mm a'\n};\nvar dateTimeFormats = {\n  full: \"{{date}} 'at' {{time}}\",\n  long: \"{{date}} 'at' {{time}}\",\n  medium: '{{date}}, {{time}}',\n  short: '{{date}}, {{time}}'\n};\nvar formatLong = {\n  date: buildFormatLongFn({\n    formats: dateFormats,\n    defaultWidth: 'full'\n  }),\n  time: buildFormatLongFn({\n    formats: timeFormats,\n    defaultWidth: 'full'\n  }),\n  dateTime: buildFormatLongFn({\n    formats: dateTimeFormats,\n    defaultWidth: 'full'\n  })\n};\nexport default formatLong;"]}, "metadata": {}, "sourceType": "module"}
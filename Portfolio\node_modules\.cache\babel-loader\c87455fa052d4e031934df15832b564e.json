{"ast": null, "code": "export class ParticlesInteractorBase {\n  constructor(container) {\n    this.container = container;\n    this.type = 1;\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Core/Utils/ParticlesInteractorBase.js"], "names": ["ParticlesInteractorBase", "constructor", "container", "type"], "mappings": "AAAA,OAAO,MAAMA,uBAAN,CAA8B;AACjCC,EAAAA,WAAW,CAACC,SAAD,EAAY;AACnB,SAAKA,SAAL,GAAiBA,SAAjB;AACA,SAAKC,IAAL,GAAY,CAAZ;AACH;;AAJgC", "sourcesContent": ["export class ParticlesInteractorBase {\n    constructor(container) {\n        this.container = container;\n        this.type = 1;\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
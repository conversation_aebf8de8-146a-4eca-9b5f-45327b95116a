{"ast": null, "code": "import { Events } from \"./Events/Events.js\";\nimport { InteractivityDetect } from \"../../../Enums/InteractivityDetect.js\";\nimport { Modes } from \"./Modes/Modes.js\";\nexport class Interactivity {\n  constructor(engine, container) {\n    this.detectsOn = InteractivityDetect.window;\n    this.events = new Events();\n    this.modes = new Modes(engine, container);\n  }\n  load(data) {\n    if (!data) {\n      return;\n    }\n    const detectsOn = data.detectsOn;\n    if (detectsOn !== undefined) {\n      this.detectsOn = detectsOn;\n    }\n    this.events.load(data.events);\n    this.modes.load(data.modes);\n  }\n}", "map": {"version": 3, "names": ["Events", "InteractivityDetect", "Modes", "Interactivity", "constructor", "engine", "container", "detectsOn", "window", "events", "modes", "load", "data", "undefined"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/engine/browser/Options/Classes/Interactivity/Interactivity.js"], "sourcesContent": ["import { Events } from \"./Events/Events.js\";\nimport { InteractivityDetect } from \"../../../Enums/InteractivityDetect.js\";\nimport { Modes } from \"./Modes/Modes.js\";\nexport class Interactivity {\n    constructor(engine, container) {\n        this.detectsOn = InteractivityDetect.window;\n        this.events = new Events();\n        this.modes = new Modes(engine, container);\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        const detectsOn = data.detectsOn;\n        if (detectsOn !== undefined) {\n            this.detectsOn = detectsOn;\n        }\n        this.events.load(data.events);\n        this.modes.load(data.modes);\n    }\n}\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,oBAAoB;AAC3C,SAASC,mBAAmB,QAAQ,uCAAuC;AAC3E,SAASC,KAAK,QAAQ,kBAAkB;AACxC,OAAO,MAAMC,aAAa,CAAC;EACvBC,WAAWA,CAACC,MAAM,EAAEC,SAAS,EAAE;IAC3B,IAAI,CAACC,SAAS,GAAGN,mBAAmB,CAACO,MAAM;IAC3C,IAAI,CAACC,MAAM,GAAG,IAAIT,MAAM,CAAC,CAAC;IAC1B,IAAI,CAACU,KAAK,GAAG,IAAIR,KAAK,CAACG,MAAM,EAAEC,SAAS,CAAC;EAC7C;EACAK,IAAIA,CAACC,IAAI,EAAE;IACP,IAAI,CAACA,IAAI,EAAE;MACP;IACJ;IACA,MAAML,SAAS,GAAGK,IAAI,CAACL,SAAS;IAChC,IAAIA,SAAS,KAAKM,SAAS,EAAE;MACzB,IAAI,CAACN,SAAS,GAAGA,SAAS;IAC9B;IACA,IAAI,CAACE,MAAM,CAACE,IAAI,CAACC,IAAI,CAACH,MAAM,CAAC;IAC7B,IAAI,CAACC,KAAK,CAACC,IAAI,CAACC,IAAI,CAACF,KAAK,CAAC;EAC/B;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
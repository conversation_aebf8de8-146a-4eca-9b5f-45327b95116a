{"ast": null, "code": "import { getRangeMax, getRangeValue } from \"@tsparticles/engine\";\nimport { applyDistance, getProximitySpeedFactor, initSpin, move, spin } from \"./Utils.js\";\nconst diffFactor = 2,\n  defaultSizeFactor = 1,\n  defaultDeltaFactor = 1;\nexport class BaseMover {\n  init(particle) {\n    const options = particle.options,\n      gravityOptions = options.move.gravity;\n    particle.gravity = {\n      enable: gravityOptions.enable,\n      acceleration: getRangeValue(gravityOptions.acceleration),\n      inverse: gravityOptions.inverse\n    };\n    initSpin(particle);\n  }\n  isEnabled(particle) {\n    return !particle.destroyed && particle.options.move.enable;\n  }\n  move(particle, delta) {\n    const particleOptions = particle.options,\n      moveOptions = particleOptions.move;\n    if (!moveOptions.enable) {\n      return;\n    }\n    const container = particle.container,\n      pxRatio = container.retina.pixelRatio;\n    particle.retina.moveSpeed ??= getRangeValue(moveOptions.speed) * pxRatio;\n    particle.retina.moveDrift ??= getRangeValue(particle.options.move.drift) * pxRatio;\n    const slowFactor = getProximitySpeedFactor(particle),\n      baseSpeed = particle.retina.moveSpeed * container.retina.reduceFactor,\n      moveDrift = particle.retina.moveDrift,\n      maxSize = getRangeMax(particleOptions.size.value) * pxRatio,\n      sizeFactor = moveOptions.size ? particle.getRadius() / maxSize : defaultSizeFactor,\n      deltaFactor = delta.factor || defaultDeltaFactor,\n      moveSpeed = baseSpeed * sizeFactor * slowFactor * deltaFactor / diffFactor,\n      maxSpeed = particle.retina.maxSpeed ?? container.retina.maxSpeed;\n    if (moveOptions.spin.enable) {\n      spin(particle, moveSpeed);\n    } else {\n      move(particle, moveOptions, moveSpeed, maxSpeed, moveDrift, delta);\n    }\n    applyDistance(particle);\n  }\n}", "map": {"version": 3, "names": ["getRangeMax", "getRangeValue", "applyDistance", "getProximitySpeedFactor", "initSpin", "move", "spin", "diffFactor", "defaultSizeFactor", "defaultDeltaFactor", "BaseMover", "init", "particle", "options", "gravityOptions", "gravity", "enable", "acceleration", "inverse", "isEnabled", "destroyed", "delta", "particleOptions", "moveOptions", "container", "pxRatio", "retina", "pixelRatio", "moveSpeed", "speed", "moveDrift", "drift", "slowFactor", "baseSpeed", "reduceFactor", "maxSize", "size", "value", "sizeFactor", "getRadius", "deltaFactor", "factor", "maxSpeed"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/move-base/browser/BaseMover.js"], "sourcesContent": ["import { getRangeMax, getRangeValue } from \"@tsparticles/engine\";\nimport { applyDistance, getProximitySpeedFactor, initSpin, move, spin } from \"./Utils.js\";\nconst diffFactor = 2, defaultSizeFactor = 1, defaultDeltaFactor = 1;\nexport class BaseMover {\n    init(particle) {\n        const options = particle.options, gravityOptions = options.move.gravity;\n        particle.gravity = {\n            enable: gravityOptions.enable,\n            acceleration: getRangeValue(gravityOptions.acceleration),\n            inverse: gravityOptions.inverse,\n        };\n        initSpin(particle);\n    }\n    isEnabled(particle) {\n        return !particle.destroyed && particle.options.move.enable;\n    }\n    move(particle, delta) {\n        const particleOptions = particle.options, moveOptions = particleOptions.move;\n        if (!moveOptions.enable) {\n            return;\n        }\n        const container = particle.container, pxRatio = container.retina.pixelRatio;\n        particle.retina.moveSpeed ??= getRangeValue(moveOptions.speed) * pxRatio;\n        particle.retina.moveDrift ??= getRangeValue(particle.options.move.drift) * pxRatio;\n        const slowFactor = getProximitySpeedFactor(particle), baseSpeed = particle.retina.moveSpeed * container.retina.reduceFactor, moveDrift = particle.retina.moveDrift, maxSize = getRangeMax(particleOptions.size.value) * pxRatio, sizeFactor = moveOptions.size ? particle.getRadius() / maxSize : defaultSizeFactor, deltaFactor = delta.factor || defaultDeltaFactor, moveSpeed = (baseSpeed * sizeFactor * slowFactor * deltaFactor) / diffFactor, maxSpeed = particle.retina.maxSpeed ?? container.retina.maxSpeed;\n        if (moveOptions.spin.enable) {\n            spin(particle, moveSpeed);\n        }\n        else {\n            move(particle, moveOptions, moveSpeed, maxSpeed, moveDrift, delta);\n        }\n        applyDistance(particle);\n    }\n}\n"], "mappings": "AAAA,SAASA,WAAW,EAAEC,aAAa,QAAQ,qBAAqB;AAChE,SAASC,aAAa,EAAEC,uBAAuB,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,IAAI,QAAQ,YAAY;AACzF,MAAMC,UAAU,GAAG,CAAC;EAAEC,iBAAiB,GAAG,CAAC;EAAEC,kBAAkB,GAAG,CAAC;AACnE,OAAO,MAAMC,SAAS,CAAC;EACnBC,IAAIA,CAACC,QAAQ,EAAE;IACX,MAAMC,OAAO,GAAGD,QAAQ,CAACC,OAAO;MAAEC,cAAc,GAAGD,OAAO,CAACR,IAAI,CAACU,OAAO;IACvEH,QAAQ,CAACG,OAAO,GAAG;MACfC,MAAM,EAAEF,cAAc,CAACE,MAAM;MAC7BC,YAAY,EAAEhB,aAAa,CAACa,cAAc,CAACG,YAAY,CAAC;MACxDC,OAAO,EAAEJ,cAAc,CAACI;IAC5B,CAAC;IACDd,QAAQ,CAACQ,QAAQ,CAAC;EACtB;EACAO,SAASA,CAACP,QAAQ,EAAE;IAChB,OAAO,CAACA,QAAQ,CAACQ,SAAS,IAAIR,QAAQ,CAACC,OAAO,CAACR,IAAI,CAACW,MAAM;EAC9D;EACAX,IAAIA,CAACO,QAAQ,EAAES,KAAK,EAAE;IAClB,MAAMC,eAAe,GAAGV,QAAQ,CAACC,OAAO;MAAEU,WAAW,GAAGD,eAAe,CAACjB,IAAI;IAC5E,IAAI,CAACkB,WAAW,CAACP,MAAM,EAAE;MACrB;IACJ;IACA,MAAMQ,SAAS,GAAGZ,QAAQ,CAACY,SAAS;MAAEC,OAAO,GAAGD,SAAS,CAACE,MAAM,CAACC,UAAU;IAC3Ef,QAAQ,CAACc,MAAM,CAACE,SAAS,KAAK3B,aAAa,CAACsB,WAAW,CAACM,KAAK,CAAC,GAAGJ,OAAO;IACxEb,QAAQ,CAACc,MAAM,CAACI,SAAS,KAAK7B,aAAa,CAACW,QAAQ,CAACC,OAAO,CAACR,IAAI,CAAC0B,KAAK,CAAC,GAAGN,OAAO;IAClF,MAAMO,UAAU,GAAG7B,uBAAuB,CAACS,QAAQ,CAAC;MAAEqB,SAAS,GAAGrB,QAAQ,CAACc,MAAM,CAACE,SAAS,GAAGJ,SAAS,CAACE,MAAM,CAACQ,YAAY;MAAEJ,SAAS,GAAGlB,QAAQ,CAACc,MAAM,CAACI,SAAS;MAAEK,OAAO,GAAGnC,WAAW,CAACsB,eAAe,CAACc,IAAI,CAACC,KAAK,CAAC,GAAGZ,OAAO;MAAEa,UAAU,GAAGf,WAAW,CAACa,IAAI,GAAGxB,QAAQ,CAAC2B,SAAS,CAAC,CAAC,GAAGJ,OAAO,GAAG3B,iBAAiB;MAAEgC,WAAW,GAAGnB,KAAK,CAACoB,MAAM,IAAIhC,kBAAkB;MAAEmB,SAAS,GAAIK,SAAS,GAAGK,UAAU,GAAGN,UAAU,GAAGQ,WAAW,GAAIjC,UAAU;MAAEmC,QAAQ,GAAG9B,QAAQ,CAACc,MAAM,CAACgB,QAAQ,IAAIlB,SAAS,CAACE,MAAM,CAACgB,QAAQ;IACrf,IAAInB,WAAW,CAACjB,IAAI,CAACU,MAAM,EAAE;MACzBV,IAAI,CAACM,QAAQ,EAAEgB,SAAS,CAAC;IAC7B,CAAC,MACI;MACDvB,IAAI,CAACO,QAAQ,EAAEW,WAAW,EAAEK,SAAS,EAAEc,QAAQ,EAAEZ,SAAS,EAAET,KAAK,CAAC;IACtE;IACAnB,aAAa,CAACU,QAAQ,CAAC;EAC3B;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
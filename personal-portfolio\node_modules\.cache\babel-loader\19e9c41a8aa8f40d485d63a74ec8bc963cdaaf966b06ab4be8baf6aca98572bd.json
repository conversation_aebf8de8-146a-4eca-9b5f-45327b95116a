{"ast": null, "code": "export class Slow {\n  constructor() {\n    this.factor = 3;\n    this.radius = 200;\n  }\n  load(data) {\n    if (!data) {\n      return;\n    }\n    if (data.factor !== undefined) {\n      this.factor = data.factor;\n    }\n    if (data.radius !== undefined) {\n      this.radius = data.radius;\n    }\n  }\n}", "map": {"version": 3, "names": ["Slow", "constructor", "factor", "radius", "load", "data", "undefined"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/interaction-external-slow/browser/Options/Classes/Slow.js"], "sourcesContent": ["export class Slow {\n    constructor() {\n        this.factor = 3;\n        this.radius = 200;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.factor !== undefined) {\n            this.factor = data.factor;\n        }\n        if (data.radius !== undefined) {\n            this.radius = data.radius;\n        }\n    }\n}\n"], "mappings": "AAAA,OAAO,MAAMA,IAAI,CAAC;EACdC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,MAAM,GAAG,CAAC;IACf,IAAI,CAACC,MAAM,GAAG,GAAG;EACrB;EACAC,IAAIA,CAACC,IAAI,EAAE;IACP,IAAI,CAACA,IAAI,EAAE;MACP;IACJ;IACA,IAAIA,IAAI,CAACH,MAAM,KAAKI,SAAS,EAAE;MAC3B,IAAI,CAACJ,MAAM,GAAGG,IAAI,CAACH,MAAM;IAC7B;IACA,IAAIG,IAAI,CAACF,MAAM,KAAKG,SAAS,EAAE;MAC3B,IAAI,CAACH,MAAM,GAAGE,IAAI,CAACF,MAAM;IAC7B;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
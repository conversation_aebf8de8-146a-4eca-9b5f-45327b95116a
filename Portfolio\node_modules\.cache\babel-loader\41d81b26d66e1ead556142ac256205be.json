{"ast": null, "code": "export default function buildLocalizeFn(args) {\n  return function (dirtyIndex, dirtyOptions) {\n    var options = dirtyOptions || {};\n    var context = options.context ? String(options.context) : 'standalone';\n    var valuesArray;\n\n    if (context === 'formatting' && args.formattingValues) {\n      var defaultWidth = args.defaultFormattingWidth || args.defaultWidth;\n      var width = options.width ? String(options.width) : defaultWidth;\n      valuesArray = args.formattingValues[width] || args.formattingValues[defaultWidth];\n    } else {\n      var _defaultWidth = args.defaultWidth;\n\n      var _width = options.width ? String(options.width) : args.defaultWidth;\n\n      valuesArray = args.values[_width] || args.values[_defaultWidth];\n    }\n\n    var index = args.argumentCallback ? args.argumentCallback(dirtyIndex) : dirtyIndex; // @ts-ignore: For some reason TypeScript just don't want to match it, no matter how hard we try. I challenge you to try to remove it!\n\n    return valuesArray[index];\n  };\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/date-fns/esm/locale/_lib/buildLocalizeFn/index.js"], "names": ["buildLocalizeFn", "args", "dirtyIndex", "dirtyOptions", "options", "context", "String", "valuesArray", "formattingValues", "defaultWidth", "defaultFormattingWidth", "width", "_defaultWidth", "_width", "values", "index", "argument<PERSON>allback"], "mappings": "AAAA,eAAe,SAASA,eAAT,CAAyBC,IAAzB,EAA+B;AAC5C,SAAO,UAAUC,UAAV,EAAsBC,YAAtB,EAAoC;AACzC,QAAIC,OAAO,GAAGD,YAAY,IAAI,EAA9B;AACA,QAAIE,OAAO,GAAGD,OAAO,CAACC,OAAR,GAAkBC,MAAM,CAACF,OAAO,CAACC,OAAT,CAAxB,GAA4C,YAA1D;AACA,QAAIE,WAAJ;;AAEA,QAAIF,OAAO,KAAK,YAAZ,IAA4BJ,IAAI,CAACO,gBAArC,EAAuD;AACrD,UAAIC,YAAY,GAAGR,IAAI,CAACS,sBAAL,IAA+BT,IAAI,CAACQ,YAAvD;AACA,UAAIE,KAAK,GAAGP,OAAO,CAACO,KAAR,GAAgBL,MAAM,CAACF,OAAO,CAACO,KAAT,CAAtB,GAAwCF,YAApD;AACAF,MAAAA,WAAW,GAAGN,IAAI,CAACO,gBAAL,CAAsBG,KAAtB,KAAgCV,IAAI,CAACO,gBAAL,CAAsBC,YAAtB,CAA9C;AACD,KAJD,MAIO;AACL,UAAIG,aAAa,GAAGX,IAAI,CAACQ,YAAzB;;AAEA,UAAII,MAAM,GAAGT,OAAO,CAACO,KAAR,GAAgBL,MAAM,CAACF,OAAO,CAACO,KAAT,CAAtB,GAAwCV,IAAI,CAACQ,YAA1D;;AAEAF,MAAAA,WAAW,GAAGN,IAAI,CAACa,MAAL,CAAYD,MAAZ,KAAuBZ,IAAI,CAACa,MAAL,CAAYF,aAAZ,CAArC;AACD;;AAED,QAAIG,KAAK,GAAGd,IAAI,CAACe,gBAAL,GAAwBf,IAAI,CAACe,gBAAL,CAAsBd,UAAtB,CAAxB,GAA4DA,UAAxE,CAjByC,CAiB2C;;AAEpF,WAAOK,WAAW,CAACQ,KAAD,CAAlB;AACD,GApBD;AAqBD", "sourcesContent": ["export default function buildLocalizeFn(args) {\n  return function (dirtyIndex, dirtyOptions) {\n    var options = dirtyOptions || {};\n    var context = options.context ? String(options.context) : 'standalone';\n    var valuesArray;\n\n    if (context === 'formatting' && args.formattingValues) {\n      var defaultWidth = args.defaultFormattingWidth || args.defaultWidth;\n      var width = options.width ? String(options.width) : defaultWidth;\n      valuesArray = args.formattingValues[width] || args.formattingValues[defaultWidth];\n    } else {\n      var _defaultWidth = args.defaultWidth;\n\n      var _width = options.width ? String(options.width) : args.defaultWidth;\n\n      valuesArray = args.values[_width] || args.values[_defaultWidth];\n    }\n\n    var index = args.argumentCallback ? args.argumentCallback(dirtyIndex) : dirtyIndex; // @ts-ignore: For some reason TypeScript just don't want to match it, no matter how hard we try. I challenge you to try to remove it!\n\n    return valuesArray[index];\n  };\n}"]}, "metadata": {}, "sourceType": "module"}
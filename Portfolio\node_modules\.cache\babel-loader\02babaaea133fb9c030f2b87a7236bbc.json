{"ast": null, "code": "export class OptionsColor {\n  constructor() {\n    this.value = \"#fff\";\n  }\n\n  static create(source, data) {\n    const color = new OptionsColor();\n    color.load(source);\n\n    if (data !== undefined) {\n      if (typeof data === \"string\" || data instanceof Array) {\n        color.load({\n          value: data\n        });\n      } else {\n        color.load(data);\n      }\n    }\n\n    return color;\n  }\n\n  load(data) {\n    if ((data === null || data === void 0 ? void 0 : data.value) === undefined) {\n      return;\n    }\n\n    this.value = data.value;\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Options/Classes/OptionsColor.js"], "names": ["OptionsColor", "constructor", "value", "create", "source", "data", "color", "load", "undefined", "Array"], "mappings": "AAAA,OAAO,MAAMA,YAAN,CAAmB;AACtBC,EAAAA,WAAW,GAAG;AACV,SAAKC,KAAL,GAAa,MAAb;AACH;;AACY,SAANC,MAAM,CAACC,MAAD,EAASC,IAAT,EAAe;AACxB,UAAMC,KAAK,GAAG,IAAIN,YAAJ,EAAd;AACAM,IAAAA,KAAK,CAACC,IAAN,CAAWH,MAAX;;AACA,QAAIC,IAAI,KAAKG,SAAb,EAAwB;AACpB,UAAI,OAAOH,IAAP,KAAgB,QAAhB,IAA4BA,IAAI,YAAYI,KAAhD,EAAuD;AACnDH,QAAAA,KAAK,CAACC,IAAN,CAAW;AAAEL,UAAAA,KAAK,EAAEG;AAAT,SAAX;AACH,OAFD,MAGK;AACDC,QAAAA,KAAK,CAACC,IAAN,CAAWF,IAAX;AACH;AACJ;;AACD,WAAOC,KAAP;AACH;;AACDC,EAAAA,IAAI,CAACF,IAAD,EAAO;AACP,QAAI,CAACA,IAAI,KAAK,IAAT,IAAiBA,IAAI,KAAK,KAAK,CAA/B,GAAmC,KAAK,CAAxC,GAA4CA,IAAI,CAACH,KAAlD,MAA6DM,SAAjE,EAA4E;AACxE;AACH;;AACD,SAAKN,KAAL,GAAaG,IAAI,CAACH,KAAlB;AACH;;AAtBqB", "sourcesContent": ["export class OptionsColor {\n    constructor() {\n        this.value = \"#fff\";\n    }\n    static create(source, data) {\n        const color = new OptionsColor();\n        color.load(source);\n        if (data !== undefined) {\n            if (typeof data === \"string\" || data instanceof Array) {\n                color.load({ value: data });\n            }\n            else {\n                color.load(data);\n            }\n        }\n        return color;\n    }\n    load(data) {\n        if ((data === null || data === void 0 ? void 0 : data.value) === undefined) {\n            return;\n        }\n        this.value = data.value;\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
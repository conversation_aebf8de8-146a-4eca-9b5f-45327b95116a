{"ast": null, "code": "import { setRangeValue } from \"@tsparticles/engine\";\nexport class EmitterLife {\n  constructor() {\n    this.wait = false;\n  }\n  load(data) {\n    if (!data) {\n      return;\n    }\n    if (data.count !== undefined) {\n      this.count = data.count;\n    }\n    if (data.delay !== undefined) {\n      this.delay = setRangeValue(data.delay);\n    }\n    if (data.duration !== undefined) {\n      this.duration = setRangeValue(data.duration);\n    }\n    if (data.wait !== undefined) {\n      this.wait = data.wait;\n    }\n  }\n}", "map": {"version": 3, "names": ["setRangeValue", "EmitterLife", "constructor", "wait", "load", "data", "count", "undefined", "delay", "duration"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/plugin-emitters/browser/Options/Classes/EmitterLife.js"], "sourcesContent": ["import { setRangeValue } from \"@tsparticles/engine\";\nexport class EmitterLife {\n    constructor() {\n        this.wait = false;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.count !== undefined) {\n            this.count = data.count;\n        }\n        if (data.delay !== undefined) {\n            this.delay = setRangeValue(data.delay);\n        }\n        if (data.duration !== undefined) {\n            this.duration = setRangeValue(data.duration);\n        }\n        if (data.wait !== undefined) {\n            this.wait = data.wait;\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,qBAAqB;AACnD,OAAO,MAAMC,WAAW,CAAC;EACrBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,IAAI,GAAG,KAAK;EACrB;EACAC,IAAIA,CAACC,IAAI,EAAE;IACP,IAAI,CAACA,IAAI,EAAE;MACP;IACJ;IACA,IAAIA,IAAI,CAACC,KAAK,KAAKC,SAAS,EAAE;MAC1B,IAAI,CAACD,KAAK,GAAGD,IAAI,CAACC,KAAK;IAC3B;IACA,IAAID,IAAI,CAACG,KAAK,KAAKD,SAAS,EAAE;MAC1B,IAAI,CAACC,KAAK,GAAGR,aAAa,CAACK,IAAI,CAACG,KAAK,CAAC;IAC1C;IACA,IAAIH,IAAI,CAACI,QAAQ,KAAKF,SAAS,EAAE;MAC7B,IAAI,CAACE,QAAQ,GAAGT,aAAa,CAACK,IAAI,CAACI,QAAQ,CAAC;IAChD;IACA,IAAIJ,IAAI,CAACF,IAAI,KAAKI,SAAS,EAAE;MACzB,IAAI,CAACJ,IAAI,GAAGE,IAAI,CAACF,IAAI;IACzB;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
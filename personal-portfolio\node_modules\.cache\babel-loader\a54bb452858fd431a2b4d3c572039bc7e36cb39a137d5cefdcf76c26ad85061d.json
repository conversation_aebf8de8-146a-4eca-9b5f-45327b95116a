{"ast": null, "code": "import { OptionsColor, executeOnSingleOrMultiple, isArray } from \"tsparticles-engine\";\nexport class BubbleBase {\n  constructor() {\n    this.distance = 200;\n    this.duration = 0.4;\n    this.mix = false;\n  }\n  load(data) {\n    if (!data) {\n      return;\n    }\n    if (data.distance !== undefined) {\n      this.distance = data.distance;\n    }\n    if (data.duration !== undefined) {\n      this.duration = data.duration;\n    }\n    if (data.mix !== undefined) {\n      this.mix = data.mix;\n    }\n    if (data.opacity !== undefined) {\n      this.opacity = data.opacity;\n    }\n    if (data.color !== undefined) {\n      const sourceColor = isArray(this.color) ? undefined : this.color;\n      this.color = executeOnSingleOrMultiple(data.color, color => {\n        return OptionsColor.create(sourceColor, color);\n      });\n    }\n    if (data.size !== undefined) {\n      this.size = data.size;\n    }\n  }\n}", "map": {"version": 3, "names": ["OptionsColor", "executeOnSingleOrMultiple", "isArray", "BubbleBase", "constructor", "distance", "duration", "mix", "load", "data", "undefined", "opacity", "color", "sourceColor", "create", "size"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/tsparticles-interaction-external-bubble/esm/Options/Classes/BubbleBase.js"], "sourcesContent": ["import { OptionsColor, executeOnSingleOr<PERSON>ultiple, isArray, } from \"tsparticles-engine\";\nexport class BubbleBase {\n    constructor() {\n        this.distance = 200;\n        this.duration = 0.4;\n        this.mix = false;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.distance !== undefined) {\n            this.distance = data.distance;\n        }\n        if (data.duration !== undefined) {\n            this.duration = data.duration;\n        }\n        if (data.mix !== undefined) {\n            this.mix = data.mix;\n        }\n        if (data.opacity !== undefined) {\n            this.opacity = data.opacity;\n        }\n        if (data.color !== undefined) {\n            const sourceColor = isArray(this.color) ? undefined : this.color;\n            this.color = executeOnSingleOrMultiple(data.color, (color) => {\n                return OptionsColor.create(sourceColor, color);\n            });\n        }\n        if (data.size !== undefined) {\n            this.size = data.size;\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,YAAY,EAAEC,yBAAyB,EAAEC,OAAO,QAAS,oBAAoB;AACtF,OAAO,MAAMC,UAAU,CAAC;EACpBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,QAAQ,GAAG,GAAG;IACnB,IAAI,CAACC,QAAQ,GAAG,GAAG;IACnB,IAAI,CAACC,GAAG,GAAG,KAAK;EACpB;EACAC,IAAIA,CAACC,IAAI,EAAE;IACP,IAAI,CAACA,IAAI,EAAE;MACP;IACJ;IACA,IAAIA,IAAI,CAACJ,QAAQ,KAAKK,SAAS,EAAE;MAC7B,IAAI,CAACL,QAAQ,GAAGI,IAAI,CAACJ,QAAQ;IACjC;IACA,IAAII,IAAI,CAACH,QAAQ,KAAKI,SAAS,EAAE;MAC7B,IAAI,CAACJ,QAAQ,GAAGG,IAAI,CAACH,QAAQ;IACjC;IACA,IAAIG,IAAI,CAACF,GAAG,KAAKG,SAAS,EAAE;MACxB,IAAI,CAACH,GAAG,GAAGE,IAAI,CAACF,GAAG;IACvB;IACA,IAAIE,IAAI,CAACE,OAAO,KAAKD,SAAS,EAAE;MAC5B,IAAI,CAACC,OAAO,GAAGF,IAAI,CAACE,OAAO;IAC/B;IACA,IAAIF,IAAI,CAACG,KAAK,KAAKF,SAAS,EAAE;MAC1B,MAAMG,WAAW,GAAGX,OAAO,CAAC,IAAI,CAACU,KAAK,CAAC,GAAGF,SAAS,GAAG,IAAI,CAACE,KAAK;MAChE,IAAI,CAACA,KAAK,GAAGX,yBAAyB,CAACQ,IAAI,CAACG,KAAK,EAAGA,KAAK,IAAK;QAC1D,OAAOZ,YAAY,CAACc,MAAM,CAACD,WAAW,EAAED,KAAK,CAAC;MAClD,CAAC,CAAC;IACN;IACA,IAAIH,IAAI,CAACM,IAAI,KAAKL,SAAS,EAAE;MACzB,IAAI,CAACK,IAAI,GAAGN,IAAI,CAACM,IAAI;IACzB;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
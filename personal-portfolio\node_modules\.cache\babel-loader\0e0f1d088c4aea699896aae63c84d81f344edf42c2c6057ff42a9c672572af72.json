{"ast": null, "code": "import { BaseMover } from \"./BaseMover.js\";\nexport async function loadBaseMover(engine, refresh = true) {\n  await engine.addMover(\"base\", () => {\n    return Promise.resolve(new BaseMover());\n  }, refresh);\n}", "map": {"version": 3, "names": ["BaseMover", "loadBaseMover", "engine", "refresh", "addMover", "Promise", "resolve"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/move-base/browser/index.js"], "sourcesContent": ["import { BaseMover } from \"./BaseMover.js\";\nexport async function loadBaseMover(engine, refresh = true) {\n    await engine.addMover(\"base\", () => {\n        return Promise.resolve(new BaseMover());\n    }, refresh);\n}\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,gBAAgB;AAC1C,OAAO,eAAeC,aAAaA,CAACC,MAAM,EAAEC,OAAO,GAAG,IAAI,EAAE;EACxD,MAAMD,MAAM,CAACE,QAAQ,CAAC,MAAM,EAAE,MAAM;IAChC,OAAOC,OAAO,CAACC,OAAO,CAAC,IAAIN,SAAS,CAAC,CAAC,CAAC;EAC3C,CAAC,EAAEG,OAAO,CAAC;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
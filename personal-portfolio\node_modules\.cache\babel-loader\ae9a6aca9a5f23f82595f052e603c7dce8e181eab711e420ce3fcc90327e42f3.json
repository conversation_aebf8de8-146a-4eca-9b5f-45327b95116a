{"ast": null, "code": "import { OutMode, ParticleOutType, Vector, getDistances, isPointInside } from \"@tsparticles/engine\";\nconst minVelocity = 0;\nexport class DestroyOutMode {\n  constructor(container) {\n    this.container = container;\n    this.modes = [OutMode.destroy];\n  }\n  update(particle, direction, _delta, outMode) {\n    if (!this.modes.includes(outMode)) {\n      return;\n    }\n    const container = this.container;\n    switch (particle.outType) {\n      case ParticleOutType.normal:\n      case ParticleOutType.outside:\n        if (isPointInside(particle.position, container.canvas.size, Vector.origin, particle.getRadius(), direction)) {\n          return;\n        }\n        break;\n      case ParticleOutType.inside:\n        {\n          const {\n              dx,\n              dy\n            } = getDistances(particle.position, particle.moveCenter),\n            {\n              x: vx,\n              y: vy\n            } = particle.velocity;\n          if (vx < minVelocity && dx > particle.moveCenter.radius || vy < minVelocity && dy > particle.moveCenter.radius || vx >= minVelocity && dx < -particle.moveCenter.radius || vy >= minVelocity && dy < -particle.moveCenter.radius) {\n            return;\n          }\n          break;\n        }\n    }\n    container.particles.remove(particle, undefined, true);\n  }\n}", "map": {"version": 3, "names": ["OutMode", "ParticleOutType", "Vector", "getDistances", "isPointInside", "minVelocity", "DestroyOutMode", "constructor", "container", "modes", "destroy", "update", "particle", "direction", "_delta", "outMode", "includes", "outType", "normal", "outside", "position", "canvas", "size", "origin", "getRadius", "inside", "dx", "dy", "moveCenter", "x", "vx", "y", "vy", "velocity", "radius", "particles", "remove", "undefined"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/updater-out-modes/browser/DestroyOutMode.js"], "sourcesContent": ["import { OutMode, ParticleOutType, Vector, getDistances, isPointInside, } from \"@tsparticles/engine\";\nconst minVelocity = 0;\nexport class DestroyOutMode {\n    constructor(container) {\n        this.container = container;\n        this.modes = [OutMode.destroy];\n    }\n    update(particle, direction, _delta, outMode) {\n        if (!this.modes.includes(outMode)) {\n            return;\n        }\n        const container = this.container;\n        switch (particle.outType) {\n            case ParticleOutType.normal:\n            case ParticleOutType.outside:\n                if (isPointInside(particle.position, container.canvas.size, Vector.origin, particle.getRadius(), direction)) {\n                    return;\n                }\n                break;\n            case ParticleOutType.inside: {\n                const { dx, dy } = getDistances(particle.position, particle.moveCenter), { x: vx, y: vy } = particle.velocity;\n                if ((vx < minVelocity && dx > particle.moveCenter.radius) ||\n                    (vy < minVelocity && dy > particle.moveCenter.radius) ||\n                    (vx >= minVelocity && dx < -particle.moveCenter.radius) ||\n                    (vy >= minVelocity && dy < -particle.moveCenter.radius)) {\n                    return;\n                }\n                break;\n            }\n        }\n        container.particles.remove(particle, undefined, true);\n    }\n}\n"], "mappings": "AAAA,SAASA,OAAO,EAAEC,eAAe,EAAEC,MAAM,EAAEC,YAAY,EAAEC,aAAa,QAAS,qBAAqB;AACpG,MAAMC,WAAW,GAAG,CAAC;AACrB,OAAO,MAAMC,cAAc,CAAC;EACxBC,WAAWA,CAACC,SAAS,EAAE;IACnB,IAAI,CAACA,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,KAAK,GAAG,CAACT,OAAO,CAACU,OAAO,CAAC;EAClC;EACAC,MAAMA,CAACC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,EAAEC,OAAO,EAAE;IACzC,IAAI,CAAC,IAAI,CAACN,KAAK,CAACO,QAAQ,CAACD,OAAO,CAAC,EAAE;MAC/B;IACJ;IACA,MAAMP,SAAS,GAAG,IAAI,CAACA,SAAS;IAChC,QAAQI,QAAQ,CAACK,OAAO;MACpB,KAAKhB,eAAe,CAACiB,MAAM;MAC3B,KAAKjB,eAAe,CAACkB,OAAO;QACxB,IAAIf,aAAa,CAACQ,QAAQ,CAACQ,QAAQ,EAAEZ,SAAS,CAACa,MAAM,CAACC,IAAI,EAAEpB,MAAM,CAACqB,MAAM,EAAEX,QAAQ,CAACY,SAAS,CAAC,CAAC,EAAEX,SAAS,CAAC,EAAE;UACzG;QACJ;QACA;MACJ,KAAKZ,eAAe,CAACwB,MAAM;QAAE;UACzB,MAAM;cAAEC,EAAE;cAAEC;YAAG,CAAC,GAAGxB,YAAY,CAACS,QAAQ,CAACQ,QAAQ,EAAER,QAAQ,CAACgB,UAAU,CAAC;YAAE;cAAEC,CAAC,EAAEC,EAAE;cAAEC,CAAC,EAAEC;YAAG,CAAC,GAAGpB,QAAQ,CAACqB,QAAQ;UAC7G,IAAKH,EAAE,GAAGzB,WAAW,IAAIqB,EAAE,GAAGd,QAAQ,CAACgB,UAAU,CAACM,MAAM,IACnDF,EAAE,GAAG3B,WAAW,IAAIsB,EAAE,GAAGf,QAAQ,CAACgB,UAAU,CAACM,MAAO,IACpDJ,EAAE,IAAIzB,WAAW,IAAIqB,EAAE,GAAG,CAACd,QAAQ,CAACgB,UAAU,CAACM,MAAO,IACtDF,EAAE,IAAI3B,WAAW,IAAIsB,EAAE,GAAG,CAACf,QAAQ,CAACgB,UAAU,CAACM,MAAO,EAAE;YACzD;UACJ;UACA;QACJ;IACJ;IACA1B,SAAS,CAAC2B,SAAS,CAACC,MAAM,CAACxB,QAAQ,EAAEyB,SAAS,EAAE,IAAI,CAAC;EACzD;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
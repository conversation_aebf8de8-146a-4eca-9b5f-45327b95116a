import { type Container, type ID<PERSON><PERSON>, type IShapeDrawer } from "tsparticles-engine";
import type { IImage, ImageParticle } from "./Utils";
import type { ImageContainer, ImageEngine } from "./types";
export declare class ImageDrawer implements IShapeDrawer {
    private readonly _engine;
    constructor(engine: ImageEngine);
    addImage(image: IImage): void;
    draw(context: CanvasRenderingContext2D, particle: ImageParticle, radius: number, opacity: number, delta: IDelta): void;
    getSidesCount(): number;
    init(container: ImageContainer): Promise<void>;
    loadShape(particle: ImageParticle): void;
    particleInit(container: Container, particle: ImageParticle): void;
    private readonly loadImageShape;
}

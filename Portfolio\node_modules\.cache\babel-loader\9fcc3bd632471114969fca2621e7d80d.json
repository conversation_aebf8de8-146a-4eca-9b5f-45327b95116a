{"ast": null, "code": "import { SizeUpdater } from \"./SizeUpdater\";\nexport async function loadSizeUpdater(engine) {\n  await engine.addParticleUpdater(\"size\", () => new SizeUpdater());\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Updaters/Size/index.js"], "names": ["SizeUpdater", "loadSizeUpdater", "engine", "addParticleUpdater"], "mappings": "AAAA,SAASA,WAAT,QAA4B,eAA5B;AACA,OAAO,eAAeC,eAAf,CAA+BC,MAA/B,EAAuC;AAC1C,QAAMA,MAAM,CAACC,kBAAP,CAA0B,MAA1B,EAAkC,MAAM,IAAIH,WAAJ,EAAxC,CAAN;AACH", "sourcesContent": ["import { SizeUpdater } from \"./SizeUpdater\";\nexport async function loadSizeUpdater(engine) {\n    await engine.addParticleUpdater(\"size\", () => new SizeUpdater());\n}\n"]}, "metadata": {}, "sourceType": "module"}
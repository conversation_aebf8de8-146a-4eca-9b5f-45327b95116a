{"ast": null, "code": "export class Random {\n  constructor() {\n    this.enable = false;\n    this.minimumValue = 0;\n  }\n\n  load(data) {\n    if (!data) {\n      return;\n    }\n\n    if (data.enable !== undefined) {\n      this.enable = data.enable;\n    }\n\n    if (data.minimumValue !== undefined) {\n      this.minimumValue = data.minimumValue;\n    }\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Options/Classes/Random.js"], "names": ["Random", "constructor", "enable", "minimumValue", "load", "data", "undefined"], "mappings": "AAAA,OAAO,MAAMA,MAAN,CAAa;AAChBC,EAAAA,WAAW,GAAG;AACV,SAAKC,MAAL,GAAc,KAAd;AACA,SAAKC,YAAL,GAAoB,CAApB;AACH;;AACDC,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,QAAI,CAACA,IAAL,EAAW;AACP;AACH;;AACD,QAAIA,IAAI,CAACH,MAAL,KAAgBI,SAApB,EAA+B;AAC3B,WAAKJ,MAAL,GAAcG,IAAI,CAACH,MAAnB;AACH;;AACD,QAAIG,IAAI,CAACF,YAAL,KAAsBG,SAA1B,EAAqC;AACjC,WAAKH,YAAL,GAAoBE,IAAI,CAACF,YAAzB;AACH;AACJ;;AAfe", "sourcesContent": ["export class Random {\n    constructor() {\n        this.enable = false;\n        this.minimumValue = 0;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.enable !== undefined) {\n            this.enable = data.enable;\n        }\n        if (data.minimumValue !== undefined) {\n            this.minimumValue = data.minimumValue;\n        }\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
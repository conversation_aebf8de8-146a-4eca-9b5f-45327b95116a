{"ast": null, "code": "export class Attract {\n  constructor() {\n    this.distance = 200;\n    this.duration = 0.4;\n    this.easing = \"ease-out-quad\";\n    this.factor = 1;\n    this.maxSpeed = 50;\n    this.speed = 1;\n  }\n\n  load(data) {\n    if (!data) {\n      return;\n    }\n\n    if (data.distance !== undefined) {\n      this.distance = data.distance;\n    }\n\n    if (data.duration !== undefined) {\n      this.duration = data.duration;\n    }\n\n    if (data.easing !== undefined) {\n      this.easing = data.easing;\n    }\n\n    if (data.factor !== undefined) {\n      this.factor = data.factor;\n    }\n\n    if (data.maxSpeed !== undefined) {\n      this.maxSpeed = data.maxSpeed;\n    }\n\n    if (data.speed !== undefined) {\n      this.speed = data.speed;\n    }\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Options/Classes/Interactivity/Modes/Attract.js"], "names": ["Attract", "constructor", "distance", "duration", "easing", "factor", "maxSpeed", "speed", "load", "data", "undefined"], "mappings": "AAAA,OAAO,MAAMA,OAAN,CAAc;AACjBC,EAAAA,WAAW,GAAG;AACV,SAAKC,QAAL,GAAgB,GAAhB;AACA,SAAKC,QAAL,GAAgB,GAAhB;AACA,SAAKC,MAAL,GAAc,eAAd;AACA,SAAKC,MAAL,GAAc,CAAd;AACA,SAAKC,QAAL,GAAgB,EAAhB;AACA,SAAKC,KAAL,GAAa,CAAb;AACH;;AACDC,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,QAAI,CAACA,IAAL,EAAW;AACP;AACH;;AACD,QAAIA,IAAI,CAACP,QAAL,KAAkBQ,SAAtB,EAAiC;AAC7B,WAAKR,QAAL,GAAgBO,IAAI,CAACP,QAArB;AACH;;AACD,QAAIO,IAAI,CAACN,QAAL,KAAkBO,SAAtB,EAAiC;AAC7B,WAAKP,QAAL,GAAgBM,IAAI,CAACN,QAArB;AACH;;AACD,QAAIM,IAAI,CAACL,MAAL,KAAgBM,SAApB,EAA+B;AAC3B,WAAKN,MAAL,GAAcK,IAAI,CAACL,MAAnB;AACH;;AACD,QAAIK,IAAI,CAACJ,MAAL,KAAgBK,SAApB,EAA+B;AAC3B,WAAKL,MAAL,GAAcI,IAAI,CAACJ,MAAnB;AACH;;AACD,QAAII,IAAI,CAACH,QAAL,KAAkBI,SAAtB,EAAiC;AAC7B,WAAKJ,QAAL,GAAgBG,IAAI,CAACH,QAArB;AACH;;AACD,QAAIG,IAAI,CAACF,KAAL,KAAeG,SAAnB,EAA8B;AAC1B,WAAKH,KAAL,GAAaE,IAAI,CAACF,KAAlB;AACH;AACJ;;AA/BgB", "sourcesContent": ["export class Attract {\n    constructor() {\n        this.distance = 200;\n        this.duration = 0.4;\n        this.easing = \"ease-out-quad\";\n        this.factor = 1;\n        this.maxSpeed = 50;\n        this.speed = 1;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.distance !== undefined) {\n            this.distance = data.distance;\n        }\n        if (data.duration !== undefined) {\n            this.duration = data.duration;\n        }\n        if (data.easing !== undefined) {\n            this.easing = data.easing;\n        }\n        if (data.factor !== undefined) {\n            this.factor = data.factor;\n        }\n        if (data.maxSpeed !== undefined) {\n            this.maxSpeed = data.maxSpeed;\n        }\n        if (data.speed !== undefined) {\n            this.speed = data.speed;\n        }\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
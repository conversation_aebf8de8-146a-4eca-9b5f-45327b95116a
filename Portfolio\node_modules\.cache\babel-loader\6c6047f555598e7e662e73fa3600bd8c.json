{"ast": null, "code": "import { GrabLinks } from \"./GrabLinks\";\nexport class Grab {\n  constructor() {\n    this.distance = 100;\n    this.links = new GrabLinks();\n  }\n\n  get line_linked() {\n    return this.links;\n  }\n\n  set line_linked(value) {\n    this.links = value;\n  }\n\n  get lineLinked() {\n    return this.links;\n  }\n\n  set lineLinked(value) {\n    this.links = value;\n  }\n\n  load(data) {\n    var _a, _b;\n\n    if (data === undefined) {\n      return;\n    }\n\n    if (data.distance !== undefined) {\n      this.distance = data.distance;\n    }\n\n    this.links.load((_b = (_a = data.links) !== null && _a !== void 0 ? _a : data.lineLinked) !== null && _b !== void 0 ? _b : data.line_linked);\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Options/Classes/Interactivity/Modes/Grab.js"], "names": ["GrabLinks", "<PERSON>rab", "constructor", "distance", "links", "line_linked", "value", "lineLinked", "load", "data", "_a", "_b", "undefined"], "mappings": "AAAA,SAASA,SAAT,QAA0B,aAA1B;AACA,OAAO,MAAMC,IAAN,CAAW;AACdC,EAAAA,WAAW,GAAG;AACV,SAAKC,QAAL,GAAgB,GAAhB;AACA,SAAKC,KAAL,GAAa,IAAIJ,SAAJ,EAAb;AACH;;AACc,MAAXK,WAAW,GAAG;AACd,WAAO,KAAKD,KAAZ;AACH;;AACc,MAAXC,WAAW,CAACC,KAAD,EAAQ;AACnB,SAAKF,KAAL,GAAaE,KAAb;AACH;;AACa,MAAVC,UAAU,GAAG;AACb,WAAO,KAAKH,KAAZ;AACH;;AACa,MAAVG,UAAU,CAACD,KAAD,EAAQ;AAClB,SAAKF,KAAL,GAAaE,KAAb;AACH;;AACDE,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,QAAIC,EAAJ,EAAQC,EAAR;;AACA,QAAIF,IAAI,KAAKG,SAAb,EAAwB;AACpB;AACH;;AACD,QAAIH,IAAI,CAACN,QAAL,KAAkBS,SAAtB,EAAiC;AAC7B,WAAKT,QAAL,GAAgBM,IAAI,CAACN,QAArB;AACH;;AACD,SAAKC,KAAL,CAAWI,IAAX,CAAgB,CAACG,EAAE,GAAG,CAACD,EAAE,GAAGD,IAAI,CAACL,KAAX,MAAsB,IAAtB,IAA8BM,EAAE,KAAK,KAAK,CAA1C,GAA8CA,EAA9C,GAAmDD,IAAI,CAACF,UAA9D,MAA8E,IAA9E,IAAsFI,EAAE,KAAK,KAAK,CAAlG,GAAsGA,EAAtG,GAA2GF,IAAI,CAACJ,WAAhI;AACH;;AA1Ba", "sourcesContent": ["import { GrabLinks } from \"./GrabLinks\";\nexport class Grab {\n    constructor() {\n        this.distance = 100;\n        this.links = new GrabLinks();\n    }\n    get line_linked() {\n        return this.links;\n    }\n    set line_linked(value) {\n        this.links = value;\n    }\n    get lineLinked() {\n        return this.links;\n    }\n    set lineLinked(value) {\n        this.links = value;\n    }\n    load(data) {\n        var _a, _b;\n        if (data === undefined) {\n            return;\n        }\n        if (data.distance !== undefined) {\n            this.distance = data.distance;\n        }\n        this.links.load((_b = (_a = data.links) !== null && _a !== void 0 ? _a : data.lineLinked) !== null && _b !== void 0 ? _b : data.line_linked);\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
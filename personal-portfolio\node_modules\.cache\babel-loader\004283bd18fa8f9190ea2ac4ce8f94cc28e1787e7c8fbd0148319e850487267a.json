{"ast": null, "code": "import { isSsr } from \"@tsparticles/engine\";\nconst half = 0.5;\nexport class ParallaxMover {\n  init() {}\n  isEnabled(particle) {\n    return !isSsr() && !particle.destroyed && particle.container.actualOptions.interactivity.events.onHover.parallax.enable;\n  }\n  move(particle) {\n    const container = particle.container,\n      options = container.actualOptions,\n      parallaxOptions = options.interactivity.events.onHover.parallax;\n    if (isSsr() || !parallaxOptions.enable) {\n      return;\n    }\n    const parallaxForce = parallaxOptions.force,\n      mousePos = container.interactivity.mouse.position;\n    if (!mousePos) {\n      return;\n    }\n    const canvasSize = container.canvas.size,\n      canvasCenter = {\n        x: canvasSize.width * half,\n        y: canvasSize.height * half\n      },\n      parallaxSmooth = parallaxOptions.smooth,\n      factor = particle.getRadius() / parallaxForce,\n      centerDistance = {\n        x: (mousePos.x - canvasCenter.x) * factor,\n        y: (mousePos.y - canvasCenter.y) * factor\n      },\n      {\n        offset\n      } = particle;\n    offset.x += (centerDistance.x - offset.x) / parallaxSmooth;\n    offset.y += (centerDistance.y - offset.y) / parallaxSmooth;\n  }\n}", "map": {"version": 3, "names": ["isSsr", "half", "ParallaxMover", "init", "isEnabled", "particle", "destroyed", "container", "actualOptions", "interactivity", "events", "onHover", "parallax", "enable", "move", "options", "parallaxOptions", "parallaxForce", "force", "mousePos", "mouse", "position", "canvasSize", "canvas", "size", "canvasCenter", "x", "width", "y", "height", "parallaxSmooth", "smooth", "factor", "getRadius", "centerDistance", "offset"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/move-parallax/browser/ParallaxMover.js"], "sourcesContent": ["import { isSsr } from \"@tsparticles/engine\";\nconst half = 0.5;\nexport class ParallaxMover {\n    init() {\n    }\n    isEnabled(particle) {\n        return (!isSsr() &&\n            !particle.destroyed &&\n            particle.container.actualOptions.interactivity.events.onHover.parallax.enable);\n    }\n    move(particle) {\n        const container = particle.container, options = container.actualOptions, parallaxOptions = options.interactivity.events.onHover.parallax;\n        if (isSsr() || !parallaxOptions.enable) {\n            return;\n        }\n        const parallaxForce = parallaxOptions.force, mousePos = container.interactivity.mouse.position;\n        if (!mousePos) {\n            return;\n        }\n        const canvasSize = container.canvas.size, canvasCenter = {\n            x: canvasSize.width * half,\n            y: canvasSize.height * half,\n        }, parallaxSmooth = parallaxOptions.smooth, factor = particle.getRadius() / parallaxForce, centerDistance = {\n            x: (mousePos.x - canvasCenter.x) * factor,\n            y: (mousePos.y - canvasCenter.y) * factor,\n        }, { offset } = particle;\n        offset.x += (centerDistance.x - offset.x) / parallaxSmooth;\n        offset.y += (centerDistance.y - offset.y) / parallaxSmooth;\n    }\n}\n"], "mappings": "AAAA,SAASA,KAAK,QAAQ,qBAAqB;AAC3C,MAAMC,IAAI,GAAG,GAAG;AAChB,OAAO,MAAMC,aAAa,CAAC;EACvBC,IAAIA,CAAA,EAAG,CACP;EACAC,SAASA,CAACC,QAAQ,EAAE;IAChB,OAAQ,CAACL,KAAK,CAAC,CAAC,IACZ,CAACK,QAAQ,CAACC,SAAS,IACnBD,QAAQ,CAACE,SAAS,CAACC,aAAa,CAACC,aAAa,CAACC,MAAM,CAACC,OAAO,CAACC,QAAQ,CAACC,MAAM;EACrF;EACAC,IAAIA,CAACT,QAAQ,EAAE;IACX,MAAME,SAAS,GAAGF,QAAQ,CAACE,SAAS;MAAEQ,OAAO,GAAGR,SAAS,CAACC,aAAa;MAAEQ,eAAe,GAAGD,OAAO,CAACN,aAAa,CAACC,MAAM,CAACC,OAAO,CAACC,QAAQ;IACxI,IAAIZ,KAAK,CAAC,CAAC,IAAI,CAACgB,eAAe,CAACH,MAAM,EAAE;MACpC;IACJ;IACA,MAAMI,aAAa,GAAGD,eAAe,CAACE,KAAK;MAAEC,QAAQ,GAAGZ,SAAS,CAACE,aAAa,CAACW,KAAK,CAACC,QAAQ;IAC9F,IAAI,CAACF,QAAQ,EAAE;MACX;IACJ;IACA,MAAMG,UAAU,GAAGf,SAAS,CAACgB,MAAM,CAACC,IAAI;MAAEC,YAAY,GAAG;QACrDC,CAAC,EAAEJ,UAAU,CAACK,KAAK,GAAG1B,IAAI;QAC1B2B,CAAC,EAAEN,UAAU,CAACO,MAAM,GAAG5B;MAC3B,CAAC;MAAE6B,cAAc,GAAGd,eAAe,CAACe,MAAM;MAAEC,MAAM,GAAG3B,QAAQ,CAAC4B,SAAS,CAAC,CAAC,GAAGhB,aAAa;MAAEiB,cAAc,GAAG;QACxGR,CAAC,EAAE,CAACP,QAAQ,CAACO,CAAC,GAAGD,YAAY,CAACC,CAAC,IAAIM,MAAM;QACzCJ,CAAC,EAAE,CAACT,QAAQ,CAACS,CAAC,GAAGH,YAAY,CAACG,CAAC,IAAII;MACvC,CAAC;MAAE;QAAEG;MAAO,CAAC,GAAG9B,QAAQ;IACxB8B,MAAM,CAACT,CAAC,IAAI,CAACQ,cAAc,CAACR,CAAC,GAAGS,MAAM,CAACT,CAAC,IAAII,cAAc;IAC1DK,MAAM,CAACP,CAAC,IAAI,CAACM,cAAc,CAACN,CAAC,GAAGO,MAAM,CAACP,CAAC,IAAIE,cAAc;EAC9D;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
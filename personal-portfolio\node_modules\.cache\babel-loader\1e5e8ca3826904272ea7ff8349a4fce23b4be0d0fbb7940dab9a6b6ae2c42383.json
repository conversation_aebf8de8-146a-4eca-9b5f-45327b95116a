{"ast": null, "code": "import { getRand<PERSON>, getRang<PERSON><PERSON><PERSON><PERSON>, randomInRange, setRangeValue } from \"tsparticles-engine\";\nimport { Life } from \"./Options/Classes/Life\";\nexport class LifeUpdater {\n  constructor(container) {\n    this.container = container;\n  }\n  init(particle) {\n    const container = this.container,\n      particlesOptions = particle.options,\n      lifeOptions = particlesOptions.life;\n    if (!lifeOptions) {\n      return;\n    }\n    particle.life = {\n      delay: container.retina.reduceFactor ? getRangeValue(lifeOptions.delay.value) * (lifeOptions.delay.sync ? 1 : getRandom()) / container.retina.reduceFactor * 1000 : 0,\n      delayTime: 0,\n      duration: container.retina.reduceFactor ? getRangeValue(lifeOptions.duration.value) * (lifeOptions.duration.sync ? 1 : getRandom()) / container.retina.reduceFactor * 1000 : 0,\n      time: 0,\n      count: lifeOptions.count\n    };\n    if (particle.life.duration <= 0) {\n      particle.life.duration = -1;\n    }\n    if (particle.life.count <= 0) {\n      particle.life.count = -1;\n    }\n    if (particle.life) {\n      particle.spawning = particle.life.delay > 0;\n    }\n  }\n  isEnabled(particle) {\n    return !particle.destroyed;\n  }\n  loadOptions(options, ...sources) {\n    if (!options.life) {\n      options.life = new Life();\n    }\n    for (const source of sources) {\n      options.life.load(source?.life);\n    }\n  }\n  update(particle, delta) {\n    if (!this.isEnabled(particle) || !particle.life) {\n      return;\n    }\n    const life = particle.life;\n    let justSpawned = false;\n    if (particle.spawning) {\n      life.delayTime += delta.value;\n      if (life.delayTime >= particle.life.delay) {\n        justSpawned = true;\n        particle.spawning = false;\n        life.delayTime = 0;\n        life.time = 0;\n      } else {\n        return;\n      }\n    }\n    if (life.duration === -1) {\n      return;\n    }\n    if (particle.spawning) {\n      return;\n    }\n    if (justSpawned) {\n      life.time = 0;\n    } else {\n      life.time += delta.value;\n    }\n    if (life.time < life.duration) {\n      return;\n    }\n    life.time = 0;\n    if (particle.life.count > 0) {\n      particle.life.count--;\n    }\n    if (particle.life.count === 0) {\n      particle.destroy();\n      return;\n    }\n    const canvasSize = this.container.canvas.size,\n      widthRange = setRangeValue(0, canvasSize.width),\n      heightRange = setRangeValue(0, canvasSize.width);\n    particle.position.x = randomInRange(widthRange);\n    particle.position.y = randomInRange(heightRange);\n    particle.spawning = true;\n    life.delayTime = 0;\n    life.time = 0;\n    particle.reset();\n    const lifeOptions = particle.options.life;\n    if (lifeOptions) {\n      life.delay = getRangeValue(lifeOptions.delay.value) * 1000;\n      life.duration = getRangeValue(lifeOptions.duration.value) * 1000;\n    }\n  }\n}", "map": {"version": 3, "names": ["getRandom", "getRangeValue", "randomInRange", "setRangeValue", "Life", "LifeUpdater", "constructor", "container", "init", "particle", "particlesOptions", "options", "lifeOptions", "life", "delay", "retina", "reduceFactor", "value", "sync", "delayTime", "duration", "time", "count", "spawning", "isEnabled", "destroyed", "loadOptions", "sources", "source", "load", "update", "delta", "justSpawned", "destroy", "canvasSize", "canvas", "size", "widthRange", "width", "heightRange", "position", "x", "y", "reset"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/tsparticles-updater-life/esm/LifeUpdater.js"], "sourcesContent": ["import { getRandom, getRange<PERSON><PERSON>ue, randomInRange, setRangeValue, } from \"tsparticles-engine\";\nimport { Life } from \"./Options/Classes/Life\";\nexport class LifeUpdater {\n    constructor(container) {\n        this.container = container;\n    }\n    init(particle) {\n        const container = this.container, particlesOptions = particle.options, lifeOptions = particlesOptions.life;\n        if (!lifeOptions) {\n            return;\n        }\n        particle.life = {\n            delay: container.retina.reduceFactor\n                ? ((getRangeValue(lifeOptions.delay.value) * (lifeOptions.delay.sync ? 1 : getRandom())) /\n                    container.retina.reduceFactor) *\n                    1000\n                : 0,\n            delayTime: 0,\n            duration: container.retina.reduceFactor\n                ? ((getRangeValue(lifeOptions.duration.value) * (lifeOptions.duration.sync ? 1 : getRandom())) /\n                    container.retina.reduceFactor) *\n                    1000\n                : 0,\n            time: 0,\n            count: lifeOptions.count,\n        };\n        if (particle.life.duration <= 0) {\n            particle.life.duration = -1;\n        }\n        if (particle.life.count <= 0) {\n            particle.life.count = -1;\n        }\n        if (particle.life) {\n            particle.spawning = particle.life.delay > 0;\n        }\n    }\n    isEnabled(particle) {\n        return !particle.destroyed;\n    }\n    loadOptions(options, ...sources) {\n        if (!options.life) {\n            options.life = new Life();\n        }\n        for (const source of sources) {\n            options.life.load(source?.life);\n        }\n    }\n    update(particle, delta) {\n        if (!this.isEnabled(particle) || !particle.life) {\n            return;\n        }\n        const life = particle.life;\n        let justSpawned = false;\n        if (particle.spawning) {\n            life.delayTime += delta.value;\n            if (life.delayTime >= particle.life.delay) {\n                justSpawned = true;\n                particle.spawning = false;\n                life.delayTime = 0;\n                life.time = 0;\n            }\n            else {\n                return;\n            }\n        }\n        if (life.duration === -1) {\n            return;\n        }\n        if (particle.spawning) {\n            return;\n        }\n        if (justSpawned) {\n            life.time = 0;\n        }\n        else {\n            life.time += delta.value;\n        }\n        if (life.time < life.duration) {\n            return;\n        }\n        life.time = 0;\n        if (particle.life.count > 0) {\n            particle.life.count--;\n        }\n        if (particle.life.count === 0) {\n            particle.destroy();\n            return;\n        }\n        const canvasSize = this.container.canvas.size, widthRange = setRangeValue(0, canvasSize.width), heightRange = setRangeValue(0, canvasSize.width);\n        particle.position.x = randomInRange(widthRange);\n        particle.position.y = randomInRange(heightRange);\n        particle.spawning = true;\n        life.delayTime = 0;\n        life.time = 0;\n        particle.reset();\n        const lifeOptions = particle.options.life;\n        if (lifeOptions) {\n            life.delay = getRangeValue(lifeOptions.delay.value) * 1000;\n            life.duration = getRangeValue(lifeOptions.duration.value) * 1000;\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,SAAS,EAAEC,aAAa,EAAEC,aAAa,EAAEC,aAAa,QAAS,oBAAoB;AAC5F,SAASC,IAAI,QAAQ,wBAAwB;AAC7C,OAAO,MAAMC,WAAW,CAAC;EACrBC,WAAWA,CAACC,SAAS,EAAE;IACnB,IAAI,CAACA,SAAS,GAAGA,SAAS;EAC9B;EACAC,IAAIA,CAACC,QAAQ,EAAE;IACX,MAAMF,SAAS,GAAG,IAAI,CAACA,SAAS;MAAEG,gBAAgB,GAAGD,QAAQ,CAACE,OAAO;MAAEC,WAAW,GAAGF,gBAAgB,CAACG,IAAI;IAC1G,IAAI,CAACD,WAAW,EAAE;MACd;IACJ;IACAH,QAAQ,CAACI,IAAI,GAAG;MACZC,KAAK,EAAEP,SAAS,CAACQ,MAAM,CAACC,YAAY,GAC5Bf,aAAa,CAACW,WAAW,CAACE,KAAK,CAACG,KAAK,CAAC,IAAIL,WAAW,CAACE,KAAK,CAACI,IAAI,GAAG,CAAC,GAAGlB,SAAS,CAAC,CAAC,CAAC,GACnFO,SAAS,CAACQ,MAAM,CAACC,YAAY,GAC7B,IAAI,GACN,CAAC;MACPG,SAAS,EAAE,CAAC;MACZC,QAAQ,EAAEb,SAAS,CAACQ,MAAM,CAACC,YAAY,GAC/Bf,aAAa,CAACW,WAAW,CAACQ,QAAQ,CAACH,KAAK,CAAC,IAAIL,WAAW,CAACQ,QAAQ,CAACF,IAAI,GAAG,CAAC,GAAGlB,SAAS,CAAC,CAAC,CAAC,GACzFO,SAAS,CAACQ,MAAM,CAACC,YAAY,GAC7B,IAAI,GACN,CAAC;MACPK,IAAI,EAAE,CAAC;MACPC,KAAK,EAAEV,WAAW,CAACU;IACvB,CAAC;IACD,IAAIb,QAAQ,CAACI,IAAI,CAACO,QAAQ,IAAI,CAAC,EAAE;MAC7BX,QAAQ,CAACI,IAAI,CAACO,QAAQ,GAAG,CAAC,CAAC;IAC/B;IACA,IAAIX,QAAQ,CAACI,IAAI,CAACS,KAAK,IAAI,CAAC,EAAE;MAC1Bb,QAAQ,CAACI,IAAI,CAACS,KAAK,GAAG,CAAC,CAAC;IAC5B;IACA,IAAIb,QAAQ,CAACI,IAAI,EAAE;MACfJ,QAAQ,CAACc,QAAQ,GAAGd,QAAQ,CAACI,IAAI,CAACC,KAAK,GAAG,CAAC;IAC/C;EACJ;EACAU,SAASA,CAACf,QAAQ,EAAE;IAChB,OAAO,CAACA,QAAQ,CAACgB,SAAS;EAC9B;EACAC,WAAWA,CAACf,OAAO,EAAE,GAAGgB,OAAO,EAAE;IAC7B,IAAI,CAAChB,OAAO,CAACE,IAAI,EAAE;MACfF,OAAO,CAACE,IAAI,GAAG,IAAIT,IAAI,CAAC,CAAC;IAC7B;IACA,KAAK,MAAMwB,MAAM,IAAID,OAAO,EAAE;MAC1BhB,OAAO,CAACE,IAAI,CAACgB,IAAI,CAACD,MAAM,EAAEf,IAAI,CAAC;IACnC;EACJ;EACAiB,MAAMA,CAACrB,QAAQ,EAAEsB,KAAK,EAAE;IACpB,IAAI,CAAC,IAAI,CAACP,SAAS,CAACf,QAAQ,CAAC,IAAI,CAACA,QAAQ,CAACI,IAAI,EAAE;MAC7C;IACJ;IACA,MAAMA,IAAI,GAAGJ,QAAQ,CAACI,IAAI;IAC1B,IAAImB,WAAW,GAAG,KAAK;IACvB,IAAIvB,QAAQ,CAACc,QAAQ,EAAE;MACnBV,IAAI,CAACM,SAAS,IAAIY,KAAK,CAACd,KAAK;MAC7B,IAAIJ,IAAI,CAACM,SAAS,IAAIV,QAAQ,CAACI,IAAI,CAACC,KAAK,EAAE;QACvCkB,WAAW,GAAG,IAAI;QAClBvB,QAAQ,CAACc,QAAQ,GAAG,KAAK;QACzBV,IAAI,CAACM,SAAS,GAAG,CAAC;QAClBN,IAAI,CAACQ,IAAI,GAAG,CAAC;MACjB,CAAC,MACI;QACD;MACJ;IACJ;IACA,IAAIR,IAAI,CAACO,QAAQ,KAAK,CAAC,CAAC,EAAE;MACtB;IACJ;IACA,IAAIX,QAAQ,CAACc,QAAQ,EAAE;MACnB;IACJ;IACA,IAAIS,WAAW,EAAE;MACbnB,IAAI,CAACQ,IAAI,GAAG,CAAC;IACjB,CAAC,MACI;MACDR,IAAI,CAACQ,IAAI,IAAIU,KAAK,CAACd,KAAK;IAC5B;IACA,IAAIJ,IAAI,CAACQ,IAAI,GAAGR,IAAI,CAACO,QAAQ,EAAE;MAC3B;IACJ;IACAP,IAAI,CAACQ,IAAI,GAAG,CAAC;IACb,IAAIZ,QAAQ,CAACI,IAAI,CAACS,KAAK,GAAG,CAAC,EAAE;MACzBb,QAAQ,CAACI,IAAI,CAACS,KAAK,EAAE;IACzB;IACA,IAAIb,QAAQ,CAACI,IAAI,CAACS,KAAK,KAAK,CAAC,EAAE;MAC3Bb,QAAQ,CAACwB,OAAO,CAAC,CAAC;MAClB;IACJ;IACA,MAAMC,UAAU,GAAG,IAAI,CAAC3B,SAAS,CAAC4B,MAAM,CAACC,IAAI;MAAEC,UAAU,GAAGlC,aAAa,CAAC,CAAC,EAAE+B,UAAU,CAACI,KAAK,CAAC;MAAEC,WAAW,GAAGpC,aAAa,CAAC,CAAC,EAAE+B,UAAU,CAACI,KAAK,CAAC;IAChJ7B,QAAQ,CAAC+B,QAAQ,CAACC,CAAC,GAAGvC,aAAa,CAACmC,UAAU,CAAC;IAC/C5B,QAAQ,CAAC+B,QAAQ,CAACE,CAAC,GAAGxC,aAAa,CAACqC,WAAW,CAAC;IAChD9B,QAAQ,CAACc,QAAQ,GAAG,IAAI;IACxBV,IAAI,CAACM,SAAS,GAAG,CAAC;IAClBN,IAAI,CAACQ,IAAI,GAAG,CAAC;IACbZ,QAAQ,CAACkC,KAAK,CAAC,CAAC;IAChB,MAAM/B,WAAW,GAAGH,QAAQ,CAACE,OAAO,CAACE,IAAI;IACzC,IAAID,WAAW,EAAE;MACbC,IAAI,CAACC,KAAK,GAAGb,aAAa,CAACW,WAAW,CAACE,KAAK,CAACG,KAAK,CAAC,GAAG,IAAI;MAC1DJ,IAAI,CAACO,QAAQ,GAAGnB,aAAa,CAACW,WAAW,CAACQ,QAAQ,CAACH,KAAK,CAAC,GAAG,IAAI;IACpE;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
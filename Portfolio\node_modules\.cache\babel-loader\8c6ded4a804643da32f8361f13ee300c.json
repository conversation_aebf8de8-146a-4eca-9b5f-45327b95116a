{"ast": null, "code": "import { colorToRgb, getDistances, getStyleFromRgb } from \"../../Utils\";\nimport { Vector } from \"../../Core\";\nexport function drawPolygonMask(context, rawData, stroke) {\n  const color = colorToRgb(stroke.color);\n\n  if (!color) {\n    return;\n  }\n\n  context.beginPath();\n  context.moveTo(rawData[0].x, rawData[0].y);\n\n  for (const item of rawData) {\n    context.lineTo(item.x, item.y);\n  }\n\n  context.closePath();\n  context.strokeStyle = getStyleFromRgb(color);\n  context.lineWidth = stroke.width;\n  context.stroke();\n}\nexport function drawPolygonMaskPath(context, path, stroke, position) {\n  context.translate(position.x, position.y);\n  const color = colorToRgb(stroke.color);\n\n  if (!color) {\n    return;\n  }\n\n  context.strokeStyle = getStyleFromRgb(color, stroke.opacity);\n  context.lineWidth = stroke.width;\n  context.stroke(path);\n}\nexport function parsePaths(paths, scale, offset) {\n  var _a;\n\n  const res = [];\n\n  for (const path of paths) {\n    const segments = path.element.pathSegList;\n    const len = (_a = segments === null || segments === void 0 ? void 0 : segments.numberOfItems) !== null && _a !== void 0 ? _a : 0;\n    const p = {\n      x: 0,\n      y: 0\n    };\n\n    for (let i = 0; i < len; i++) {\n      const segment = segments === null || segments === void 0 ? void 0 : segments.getItem(i);\n      const svgPathSeg = window.SVGPathSeg;\n\n      switch (segment === null || segment === void 0 ? void 0 : segment.pathSegType) {\n        case svgPathSeg.PATHSEG_MOVETO_ABS:\n        case svgPathSeg.PATHSEG_LINETO_ABS:\n        case svgPathSeg.PATHSEG_CURVETO_CUBIC_ABS:\n        case svgPathSeg.PATHSEG_CURVETO_QUADRATIC_ABS:\n        case svgPathSeg.PATHSEG_ARC_ABS:\n        case svgPathSeg.PATHSEG_CURVETO_CUBIC_SMOOTH_ABS:\n        case svgPathSeg.PATHSEG_CURVETO_QUADRATIC_SMOOTH_ABS:\n          {\n            const absSeg = segment;\n            p.x = absSeg.x;\n            p.y = absSeg.y;\n            break;\n          }\n\n        case svgPathSeg.PATHSEG_LINETO_HORIZONTAL_ABS:\n          p.x = segment.x;\n          break;\n\n        case svgPathSeg.PATHSEG_LINETO_VERTICAL_ABS:\n          p.y = segment.y;\n          break;\n\n        case svgPathSeg.PATHSEG_LINETO_REL:\n        case svgPathSeg.PATHSEG_MOVETO_REL:\n        case svgPathSeg.PATHSEG_CURVETO_CUBIC_REL:\n        case svgPathSeg.PATHSEG_CURVETO_QUADRATIC_REL:\n        case svgPathSeg.PATHSEG_ARC_REL:\n        case svgPathSeg.PATHSEG_CURVETO_CUBIC_SMOOTH_REL:\n        case svgPathSeg.PATHSEG_CURVETO_QUADRATIC_SMOOTH_REL:\n          {\n            const relSeg = segment;\n            p.x += relSeg.x;\n            p.y += relSeg.y;\n            break;\n          }\n\n        case svgPathSeg.PATHSEG_LINETO_HORIZONTAL_REL:\n          p.x += segment.x;\n          break;\n\n        case svgPathSeg.PATHSEG_LINETO_VERTICAL_REL:\n          p.y += segment.y;\n          break;\n\n        case svgPathSeg.PATHSEG_UNKNOWN:\n        case svgPathSeg.PATHSEG_CLOSEPATH:\n          continue;\n      }\n\n      res.push({\n        x: p.x * scale + offset.x,\n        y: p.y * scale + offset.y\n      });\n    }\n  }\n\n  return res;\n}\nexport function calcClosestPtOnSegment(s1, s2, pos) {\n  const {\n    dx,\n    dy\n  } = getDistances(pos, s1);\n  const {\n    dx: dxx,\n    dy: dyy\n  } = getDistances(s2, s1);\n  const t = (dx * dxx + dy * dyy) / (dxx ** 2 + dyy ** 2);\n  const res = {\n    x: s1.x + dxx * t,\n    y: s1.x + dyy * t,\n    isOnSegment: t >= 0 && t <= 1\n  };\n\n  if (t < 0) {\n    res.x = s1.x;\n    res.y = s1.y;\n  } else if (t > 1) {\n    res.x = s2.x;\n    res.y = s2.y;\n  }\n\n  return res;\n}\nexport function segmentBounce(start, stop, velocity) {\n  const {\n    dx,\n    dy\n  } = getDistances(start, stop);\n  const wallAngle = Math.atan2(dy, dx);\n  const wallNormal = Vector.create(Math.sin(wallAngle), -Math.cos(wallAngle));\n  const d = 2 * (velocity.x * wallNormal.x + velocity.y * wallNormal.y);\n  wallNormal.multTo(d);\n  velocity.subFrom(wallNormal);\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Plugins/PolygonMask/Utils.js"], "names": ["colorToRgb", "getDistances", "getStyleFromRgb", "Vector", "drawPolygonMask", "context", "rawData", "stroke", "color", "beginPath", "moveTo", "x", "y", "item", "lineTo", "closePath", "strokeStyle", "lineWidth", "width", "drawPolygonMaskPath", "path", "position", "translate", "opacity", "parsePaths", "paths", "scale", "offset", "_a", "res", "segments", "element", "pathSegList", "len", "numberOfItems", "p", "i", "segment", "getItem", "svgPathSeg", "window", "SVGPathSeg", "pathSegType", "PATHSEG_MOVETO_ABS", "PATHSEG_LINETO_ABS", "PATHSEG_CURVETO_CUBIC_ABS", "PATHSEG_CURVETO_QUADRATIC_ABS", "PATHSEG_ARC_ABS", "PATHSEG_CURVETO_CUBIC_SMOOTH_ABS", "PATHSEG_CURVETO_QUADRATIC_SMOOTH_ABS", "absSeg", "PATHSEG_LINETO_HORIZONTAL_ABS", "PATHSEG_LINETO_VERTICAL_ABS", "PATHSEG_LINETO_REL", "PATHSEG_MOVETO_REL", "PATHSEG_CURVETO_CUBIC_REL", "PATHSEG_CURVETO_QUADRATIC_REL", "PATHSEG_ARC_REL", "PATHSEG_CURVETO_CUBIC_SMOOTH_REL", "PATHSEG_CURVETO_QUADRATIC_SMOOTH_REL", "relSeg", "PATHSEG_LINETO_HORIZONTAL_REL", "PATHSEG_LINETO_VERTICAL_REL", "PATHSEG_UNKNOWN", "PATHSEG_CLOSEPATH", "push", "calcClosestPtOnSegment", "s1", "s2", "pos", "dx", "dy", "dxx", "dyy", "t", "isOnSegment", "segmentBounce", "start", "stop", "velocity", "wallAngle", "Math", "atan2", "wallNormal", "create", "sin", "cos", "d", "multTo", "subFrom"], "mappings": "AAAA,SAASA,UAAT,EAAqBC,YAArB,EAAmCC,eAAnC,QAA0D,aAA1D;AACA,SAASC,MAAT,QAAuB,YAAvB;AACA,OAAO,SAASC,eAAT,CAAyBC,OAAzB,EAAkCC,OAAlC,EAA2CC,MAA3C,EAAmD;AACtD,QAAMC,KAAK,GAAGR,UAAU,CAACO,MAAM,CAACC,KAAR,CAAxB;;AACA,MAAI,CAACA,KAAL,EAAY;AACR;AACH;;AACDH,EAAAA,OAAO,CAACI,SAAR;AACAJ,EAAAA,OAAO,CAACK,MAAR,CAAeJ,OAAO,CAAC,CAAD,CAAP,CAAWK,CAA1B,EAA6BL,OAAO,CAAC,CAAD,CAAP,CAAWM,CAAxC;;AACA,OAAK,MAAMC,IAAX,IAAmBP,OAAnB,EAA4B;AACxBD,IAAAA,OAAO,CAACS,MAAR,CAAeD,IAAI,CAACF,CAApB,EAAuBE,IAAI,CAACD,CAA5B;AACH;;AACDP,EAAAA,OAAO,CAACU,SAAR;AACAV,EAAAA,OAAO,CAACW,WAAR,GAAsBd,eAAe,CAACM,KAAD,CAArC;AACAH,EAAAA,OAAO,CAACY,SAAR,GAAoBV,MAAM,CAACW,KAA3B;AACAb,EAAAA,OAAO,CAACE,MAAR;AACH;AACD,OAAO,SAASY,mBAAT,CAA6Bd,OAA7B,EAAsCe,IAAtC,EAA4Cb,MAA5C,EAAoDc,QAApD,EAA8D;AACjEhB,EAAAA,OAAO,CAACiB,SAAR,CAAkBD,QAAQ,CAACV,CAA3B,EAA8BU,QAAQ,CAACT,CAAvC;AACA,QAAMJ,KAAK,GAAGR,UAAU,CAACO,MAAM,CAACC,KAAR,CAAxB;;AACA,MAAI,CAACA,KAAL,EAAY;AACR;AACH;;AACDH,EAAAA,OAAO,CAACW,WAAR,GAAsBd,eAAe,CAACM,KAAD,EAAQD,MAAM,CAACgB,OAAf,CAArC;AACAlB,EAAAA,OAAO,CAACY,SAAR,GAAoBV,MAAM,CAACW,KAA3B;AACAb,EAAAA,OAAO,CAACE,MAAR,CAAea,IAAf;AACH;AACD,OAAO,SAASI,UAAT,CAAoBC,KAApB,EAA2BC,KAA3B,EAAkCC,MAAlC,EAA0C;AAC7C,MAAIC,EAAJ;;AACA,QAAMC,GAAG,GAAG,EAAZ;;AACA,OAAK,MAAMT,IAAX,IAAmBK,KAAnB,EAA0B;AACtB,UAAMK,QAAQ,GAAGV,IAAI,CAACW,OAAL,CAAaC,WAA9B;AACA,UAAMC,GAAG,GAAG,CAACL,EAAE,GAAGE,QAAQ,KAAK,IAAb,IAAqBA,QAAQ,KAAK,KAAK,CAAvC,GAA2C,KAAK,CAAhD,GAAoDA,QAAQ,CAACI,aAAnE,MAAsF,IAAtF,IAA8FN,EAAE,KAAK,KAAK,CAA1G,GAA8GA,EAA9G,GAAmH,CAA/H;AACA,UAAMO,CAAC,GAAG;AACNxB,MAAAA,CAAC,EAAE,CADG;AAENC,MAAAA,CAAC,EAAE;AAFG,KAAV;;AAIA,SAAK,IAAIwB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGH,GAApB,EAAyBG,CAAC,EAA1B,EAA8B;AAC1B,YAAMC,OAAO,GAAGP,QAAQ,KAAK,IAAb,IAAqBA,QAAQ,KAAK,KAAK,CAAvC,GAA2C,KAAK,CAAhD,GAAoDA,QAAQ,CAACQ,OAAT,CAAiBF,CAAjB,CAApE;AACA,YAAMG,UAAU,GAAGC,MAAM,CAACC,UAA1B;;AACA,cAAQJ,OAAO,KAAK,IAAZ,IAAoBA,OAAO,KAAK,KAAK,CAArC,GAAyC,KAAK,CAA9C,GAAkDA,OAAO,CAACK,WAAlE;AACI,aAAKH,UAAU,CAACI,kBAAhB;AACA,aAAKJ,UAAU,CAACK,kBAAhB;AACA,aAAKL,UAAU,CAACM,yBAAhB;AACA,aAAKN,UAAU,CAACO,6BAAhB;AACA,aAAKP,UAAU,CAACQ,eAAhB;AACA,aAAKR,UAAU,CAACS,gCAAhB;AACA,aAAKT,UAAU,CAACU,oCAAhB;AAAsD;AAClD,kBAAMC,MAAM,GAAGb,OAAf;AACAF,YAAAA,CAAC,CAACxB,CAAF,GAAMuC,MAAM,CAACvC,CAAb;AACAwB,YAAAA,CAAC,CAACvB,CAAF,GAAMsC,MAAM,CAACtC,CAAb;AACA;AACH;;AACD,aAAK2B,UAAU,CAACY,6BAAhB;AACIhB,UAAAA,CAAC,CAACxB,CAAF,GAAM0B,OAAO,CAAC1B,CAAd;AACA;;AACJ,aAAK4B,UAAU,CAACa,2BAAhB;AACIjB,UAAAA,CAAC,CAACvB,CAAF,GAAMyB,OAAO,CAACzB,CAAd;AACA;;AACJ,aAAK2B,UAAU,CAACc,kBAAhB;AACA,aAAKd,UAAU,CAACe,kBAAhB;AACA,aAAKf,UAAU,CAACgB,yBAAhB;AACA,aAAKhB,UAAU,CAACiB,6BAAhB;AACA,aAAKjB,UAAU,CAACkB,eAAhB;AACA,aAAKlB,UAAU,CAACmB,gCAAhB;AACA,aAAKnB,UAAU,CAACoB,oCAAhB;AAAsD;AAClD,kBAAMC,MAAM,GAAGvB,OAAf;AACAF,YAAAA,CAAC,CAACxB,CAAF,IAAOiD,MAAM,CAACjD,CAAd;AACAwB,YAAAA,CAAC,CAACvB,CAAF,IAAOgD,MAAM,CAAChD,CAAd;AACA;AACH;;AACD,aAAK2B,UAAU,CAACsB,6BAAhB;AACI1B,UAAAA,CAAC,CAACxB,CAAF,IAAO0B,OAAO,CAAC1B,CAAf;AACA;;AACJ,aAAK4B,UAAU,CAACuB,2BAAhB;AACI3B,UAAAA,CAAC,CAACvB,CAAF,IAAOyB,OAAO,CAACzB,CAAf;AACA;;AACJ,aAAK2B,UAAU,CAACwB,eAAhB;AACA,aAAKxB,UAAU,CAACyB,iBAAhB;AACI;AAvCR;;AAyCAnC,MAAAA,GAAG,CAACoC,IAAJ,CAAS;AACLtD,QAAAA,CAAC,EAAEwB,CAAC,CAACxB,CAAF,GAAMe,KAAN,GAAcC,MAAM,CAAChB,CADnB;AAELC,QAAAA,CAAC,EAAEuB,CAAC,CAACvB,CAAF,GAAMc,KAAN,GAAcC,MAAM,CAACf;AAFnB,OAAT;AAIH;AACJ;;AACD,SAAOiB,GAAP;AACH;AACD,OAAO,SAASqC,sBAAT,CAAgCC,EAAhC,EAAoCC,EAApC,EAAwCC,GAAxC,EAA6C;AAChD,QAAM;AAAEC,IAAAA,EAAF;AAAMC,IAAAA;AAAN,MAAatE,YAAY,CAACoE,GAAD,EAAMF,EAAN,CAA/B;AACA,QAAM;AAAEG,IAAAA,EAAE,EAAEE,GAAN;AAAWD,IAAAA,EAAE,EAAEE;AAAf,MAAuBxE,YAAY,CAACmE,EAAD,EAAKD,EAAL,CAAzC;AACA,QAAMO,CAAC,GAAG,CAACJ,EAAE,GAAGE,GAAL,GAAWD,EAAE,GAAGE,GAAjB,KAAyBD,GAAG,IAAI,CAAP,GAAWC,GAAG,IAAI,CAA3C,CAAV;AACA,QAAM5C,GAAG,GAAG;AACRlB,IAAAA,CAAC,EAAEwD,EAAE,CAACxD,CAAH,GAAO6D,GAAG,GAAGE,CADR;AAER9D,IAAAA,CAAC,EAAEuD,EAAE,CAACxD,CAAH,GAAO8D,GAAG,GAAGC,CAFR;AAGRC,IAAAA,WAAW,EAAED,CAAC,IAAI,CAAL,IAAUA,CAAC,IAAI;AAHpB,GAAZ;;AAKA,MAAIA,CAAC,GAAG,CAAR,EAAW;AACP7C,IAAAA,GAAG,CAAClB,CAAJ,GAAQwD,EAAE,CAACxD,CAAX;AACAkB,IAAAA,GAAG,CAACjB,CAAJ,GAAQuD,EAAE,CAACvD,CAAX;AACH,GAHD,MAIK,IAAI8D,CAAC,GAAG,CAAR,EAAW;AACZ7C,IAAAA,GAAG,CAAClB,CAAJ,GAAQyD,EAAE,CAACzD,CAAX;AACAkB,IAAAA,GAAG,CAACjB,CAAJ,GAAQwD,EAAE,CAACxD,CAAX;AACH;;AACD,SAAOiB,GAAP;AACH;AACD,OAAO,SAAS+C,aAAT,CAAuBC,KAAvB,EAA8BC,IAA9B,EAAoCC,QAApC,EAA8C;AACjD,QAAM;AAAET,IAAAA,EAAF;AAAMC,IAAAA;AAAN,MAAatE,YAAY,CAAC4E,KAAD,EAAQC,IAAR,CAA/B;AACA,QAAME,SAAS,GAAGC,IAAI,CAACC,KAAL,CAAWX,EAAX,EAAeD,EAAf,CAAlB;AACA,QAAMa,UAAU,GAAGhF,MAAM,CAACiF,MAAP,CAAcH,IAAI,CAACI,GAAL,CAASL,SAAT,CAAd,EAAmC,CAACC,IAAI,CAACK,GAAL,CAASN,SAAT,CAApC,CAAnB;AACA,QAAMO,CAAC,GAAG,KAAKR,QAAQ,CAACpE,CAAT,GAAawE,UAAU,CAACxE,CAAxB,GAA4BoE,QAAQ,CAACnE,CAAT,GAAauE,UAAU,CAACvE,CAAzD,CAAV;AACAuE,EAAAA,UAAU,CAACK,MAAX,CAAkBD,CAAlB;AACAR,EAAAA,QAAQ,CAACU,OAAT,CAAiBN,UAAjB;AACH", "sourcesContent": ["import { colorToRgb, getDistances, getStyleFromRgb } from \"../../Utils\";\nimport { Vector } from \"../../Core\";\nexport function drawPolygonMask(context, rawData, stroke) {\n    const color = colorToRgb(stroke.color);\n    if (!color) {\n        return;\n    }\n    context.beginPath();\n    context.moveTo(rawData[0].x, rawData[0].y);\n    for (const item of rawData) {\n        context.lineTo(item.x, item.y);\n    }\n    context.closePath();\n    context.strokeStyle = getStyleFromRgb(color);\n    context.lineWidth = stroke.width;\n    context.stroke();\n}\nexport function drawPolygonMaskPath(context, path, stroke, position) {\n    context.translate(position.x, position.y);\n    const color = colorToRgb(stroke.color);\n    if (!color) {\n        return;\n    }\n    context.strokeStyle = getStyleFromRgb(color, stroke.opacity);\n    context.lineWidth = stroke.width;\n    context.stroke(path);\n}\nexport function parsePaths(paths, scale, offset) {\n    var _a;\n    const res = [];\n    for (const path of paths) {\n        const segments = path.element.pathSegList;\n        const len = (_a = segments === null || segments === void 0 ? void 0 : segments.numberOfItems) !== null && _a !== void 0 ? _a : 0;\n        const p = {\n            x: 0,\n            y: 0,\n        };\n        for (let i = 0; i < len; i++) {\n            const segment = segments === null || segments === void 0 ? void 0 : segments.getItem(i);\n            const svgPathSeg = window.SVGPathSeg;\n            switch (segment === null || segment === void 0 ? void 0 : segment.pathSegType) {\n                case svgPathSeg.PATHSEG_MOVETO_ABS:\n                case svgPathSeg.PATHSEG_LINETO_ABS:\n                case svgPathSeg.PATHSEG_CURVETO_CUBIC_ABS:\n                case svgPathSeg.PATHSEG_CURVETO_QUADRATIC_ABS:\n                case svgPathSeg.PATHSEG_ARC_ABS:\n                case svgPathSeg.PATHSEG_CURVETO_CUBIC_SMOOTH_ABS:\n                case svgPathSeg.PATHSEG_CURVETO_QUADRATIC_SMOOTH_ABS: {\n                    const absSeg = segment;\n                    p.x = absSeg.x;\n                    p.y = absSeg.y;\n                    break;\n                }\n                case svgPathSeg.PATHSEG_LINETO_HORIZONTAL_ABS:\n                    p.x = segment.x;\n                    break;\n                case svgPathSeg.PATHSEG_LINETO_VERTICAL_ABS:\n                    p.y = segment.y;\n                    break;\n                case svgPathSeg.PATHSEG_LINETO_REL:\n                case svgPathSeg.PATHSEG_MOVETO_REL:\n                case svgPathSeg.PATHSEG_CURVETO_CUBIC_REL:\n                case svgPathSeg.PATHSEG_CURVETO_QUADRATIC_REL:\n                case svgPathSeg.PATHSEG_ARC_REL:\n                case svgPathSeg.PATHSEG_CURVETO_CUBIC_SMOOTH_REL:\n                case svgPathSeg.PATHSEG_CURVETO_QUADRATIC_SMOOTH_REL: {\n                    const relSeg = segment;\n                    p.x += relSeg.x;\n                    p.y += relSeg.y;\n                    break;\n                }\n                case svgPathSeg.PATHSEG_LINETO_HORIZONTAL_REL:\n                    p.x += segment.x;\n                    break;\n                case svgPathSeg.PATHSEG_LINETO_VERTICAL_REL:\n                    p.y += segment.y;\n                    break;\n                case svgPathSeg.PATHSEG_UNKNOWN:\n                case svgPathSeg.PATHSEG_CLOSEPATH:\n                    continue;\n            }\n            res.push({\n                x: p.x * scale + offset.x,\n                y: p.y * scale + offset.y,\n            });\n        }\n    }\n    return res;\n}\nexport function calcClosestPtOnSegment(s1, s2, pos) {\n    const { dx, dy } = getDistances(pos, s1);\n    const { dx: dxx, dy: dyy } = getDistances(s2, s1);\n    const t = (dx * dxx + dy * dyy) / (dxx ** 2 + dyy ** 2);\n    const res = {\n        x: s1.x + dxx * t,\n        y: s1.x + dyy * t,\n        isOnSegment: t >= 0 && t <= 1,\n    };\n    if (t < 0) {\n        res.x = s1.x;\n        res.y = s1.y;\n    }\n    else if (t > 1) {\n        res.x = s2.x;\n        res.y = s2.y;\n    }\n    return res;\n}\nexport function segmentBounce(start, stop, velocity) {\n    const { dx, dy } = getDistances(start, stop);\n    const wallAngle = Math.atan2(dy, dx);\n    const wallNormal = Vector.create(Math.sin(wallAngle), -Math.cos(wallAngle));\n    const d = 2 * (velocity.x * wallNormal.x + velocity.y * wallNormal.y);\n    wallNormal.multTo(d);\n    velocity.subFrom(wallNormal);\n}\n"]}, "metadata": {}, "sourceType": "module"}
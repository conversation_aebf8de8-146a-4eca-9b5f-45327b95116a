{"ast": null, "code": "import { Vector, getDistances, isPointInside } from \"tsparticles-engine\";\nexport class DestroyOutMode {\n  constructor(container) {\n    this.container = container;\n    this.modes = [\"destroy\"];\n  }\n  update(particle, direction, _delta, outMode) {\n    if (!this.modes.includes(outMode)) {\n      return;\n    }\n    const container = this.container;\n    switch (particle.outType) {\n      case \"normal\":\n      case \"outside\":\n        if (isPointInside(particle.position, container.canvas.size, Vector.origin, particle.getRadius(), direction)) {\n          return;\n        }\n        break;\n      case \"inside\":\n        {\n          const {\n            dx,\n            dy\n          } = getDistances(particle.position, particle.moveCenter);\n          const {\n            x: vx,\n            y: vy\n          } = particle.velocity;\n          if (vx < 0 && dx > particle.moveCenter.radius || vy < 0 && dy > particle.moveCenter.radius || vx >= 0 && dx < -particle.moveCenter.radius || vy >= 0 && dy < -particle.moveCenter.radius) {\n            return;\n          }\n          break;\n        }\n    }\n    container.particles.remove(particle, undefined, true);\n  }\n}", "map": {"version": 3, "names": ["Vector", "getDistances", "isPointInside", "DestroyOutMode", "constructor", "container", "modes", "update", "particle", "direction", "_delta", "outMode", "includes", "outType", "position", "canvas", "size", "origin", "getRadius", "dx", "dy", "moveCenter", "x", "vx", "y", "vy", "velocity", "radius", "particles", "remove", "undefined"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/tsparticles-updater-out-modes/esm/DestroyOutMode.js"], "sourcesContent": ["import { Vector, getDistances, isPointInside, } from \"tsparticles-engine\";\nexport class DestroyOutMode {\n    constructor(container) {\n        this.container = container;\n        this.modes = [\"destroy\"];\n    }\n    update(particle, direction, _delta, outMode) {\n        if (!this.modes.includes(outMode)) {\n            return;\n        }\n        const container = this.container;\n        switch (particle.outType) {\n            case \"normal\":\n            case \"outside\":\n                if (isPointInside(particle.position, container.canvas.size, Vector.origin, particle.getRadius(), direction)) {\n                    return;\n                }\n                break;\n            case \"inside\": {\n                const { dx, dy } = getDistances(particle.position, particle.moveCenter);\n                const { x: vx, y: vy } = particle.velocity;\n                if ((vx < 0 && dx > particle.moveCenter.radius) ||\n                    (vy < 0 && dy > particle.moveCenter.radius) ||\n                    (vx >= 0 && dx < -particle.moveCenter.radius) ||\n                    (vy >= 0 && dy < -particle.moveCenter.radius)) {\n                    return;\n                }\n                break;\n            }\n        }\n        container.particles.remove(particle, undefined, true);\n    }\n}\n"], "mappings": "AAAA,SAASA,MAAM,EAAEC,YAAY,EAAEC,aAAa,QAAS,oBAAoB;AACzE,OAAO,MAAMC,cAAc,CAAC;EACxBC,WAAWA,CAACC,SAAS,EAAE;IACnB,IAAI,CAACA,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,KAAK,GAAG,CAAC,SAAS,CAAC;EAC5B;EACAC,MAAMA,CAACC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,EAAEC,OAAO,EAAE;IACzC,IAAI,CAAC,IAAI,CAACL,KAAK,CAACM,QAAQ,CAACD,OAAO,CAAC,EAAE;MAC/B;IACJ;IACA,MAAMN,SAAS,GAAG,IAAI,CAACA,SAAS;IAChC,QAAQG,QAAQ,CAACK,OAAO;MACpB,KAAK,QAAQ;MACb,KAAK,SAAS;QACV,IAAIX,aAAa,CAACM,QAAQ,CAACM,QAAQ,EAAET,SAAS,CAACU,MAAM,CAACC,IAAI,EAAEhB,MAAM,CAACiB,MAAM,EAAET,QAAQ,CAACU,SAAS,CAAC,CAAC,EAAET,SAAS,CAAC,EAAE;UACzG;QACJ;QACA;MACJ,KAAK,QAAQ;QAAE;UACX,MAAM;YAAEU,EAAE;YAAEC;UAAG,CAAC,GAAGnB,YAAY,CAACO,QAAQ,CAACM,QAAQ,EAAEN,QAAQ,CAACa,UAAU,CAAC;UACvE,MAAM;YAAEC,CAAC,EAAEC,EAAE;YAAEC,CAAC,EAAEC;UAAG,CAAC,GAAGjB,QAAQ,CAACkB,QAAQ;UAC1C,IAAKH,EAAE,GAAG,CAAC,IAAIJ,EAAE,GAAGX,QAAQ,CAACa,UAAU,CAACM,MAAM,IACzCF,EAAE,GAAG,CAAC,IAAIL,EAAE,GAAGZ,QAAQ,CAACa,UAAU,CAACM,MAAO,IAC1CJ,EAAE,IAAI,CAAC,IAAIJ,EAAE,GAAG,CAACX,QAAQ,CAACa,UAAU,CAACM,MAAO,IAC5CF,EAAE,IAAI,CAAC,IAAIL,EAAE,GAAG,CAACZ,QAAQ,CAACa,UAAU,CAACM,MAAO,EAAE;YAC/C;UACJ;UACA;QACJ;IACJ;IACAtB,SAAS,CAACuB,SAAS,CAACC,MAAM,CAACrB,QAAQ,EAAEsB,SAAS,EAAE,IAAI,CAAC;EACzD;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
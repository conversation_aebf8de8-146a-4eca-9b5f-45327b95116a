{"ast": null, "code": "import { Density } from \"./Density\";\nexport class ParticlesNumber {\n  constructor() {\n    this.density = new Density();\n    this.limit = 0;\n    this.value = 100;\n  }\n\n  get max() {\n    return this.limit;\n  }\n\n  set max(value) {\n    this.limit = value;\n  }\n\n  load(data) {\n    var _a;\n\n    if (data === undefined) {\n      return;\n    }\n\n    this.density.load(data.density);\n    const limit = (_a = data.limit) !== null && _a !== void 0 ? _a : data.max;\n\n    if (limit !== undefined) {\n      this.limit = limit;\n    }\n\n    if (data.value !== undefined) {\n      this.value = data.value;\n    }\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Options/Classes/Particles/Number/ParticlesNumber.js"], "names": ["Density", "ParticlesNumber", "constructor", "density", "limit", "value", "max", "load", "data", "_a", "undefined"], "mappings": "AAAA,SAASA,OAAT,QAAwB,WAAxB;AACA,OAAO,MAAMC,eAAN,CAAsB;AACzBC,EAAAA,WAAW,GAAG;AACV,SAAKC,OAAL,GAAe,IAAIH,OAAJ,EAAf;AACA,SAAKI,KAAL,GAAa,CAAb;AACA,SAAKC,KAAL,GAAa,GAAb;AACH;;AACM,MAAHC,GAAG,GAAG;AACN,WAAO,KAAKF,KAAZ;AACH;;AACM,MAAHE,GAAG,CAACD,KAAD,EAAQ;AACX,SAAKD,KAAL,GAAaC,KAAb;AACH;;AACDE,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,QAAIC,EAAJ;;AACA,QAAID,IAAI,KAAKE,SAAb,EAAwB;AACpB;AACH;;AACD,SAAKP,OAAL,CAAaI,IAAb,CAAkBC,IAAI,CAACL,OAAvB;AACA,UAAMC,KAAK,GAAG,CAACK,EAAE,GAAGD,IAAI,CAACJ,KAAX,MAAsB,IAAtB,IAA8BK,EAAE,KAAK,KAAK,CAA1C,GAA8CA,EAA9C,GAAmDD,IAAI,CAACF,GAAtE;;AACA,QAAIF,KAAK,KAAKM,SAAd,EAAyB;AACrB,WAAKN,KAAL,GAAaA,KAAb;AACH;;AACD,QAAII,IAAI,CAACH,KAAL,KAAeK,SAAnB,EAA8B;AAC1B,WAAKL,KAAL,GAAaG,IAAI,CAACH,KAAlB;AACH;AACJ;;AAzBwB", "sourcesContent": ["import { Density } from \"./Density\";\nexport class ParticlesNumber {\n    constructor() {\n        this.density = new Density();\n        this.limit = 0;\n        this.value = 100;\n    }\n    get max() {\n        return this.limit;\n    }\n    set max(value) {\n        this.limit = value;\n    }\n    load(data) {\n        var _a;\n        if (data === undefined) {\n            return;\n        }\n        this.density.load(data.density);\n        const limit = (_a = data.limit) !== null && _a !== void 0 ? _a : data.max;\n        if (limit !== undefined) {\n            this.limit = limit;\n        }\n        if (data.value !== undefined) {\n            this.value = data.value;\n        }\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
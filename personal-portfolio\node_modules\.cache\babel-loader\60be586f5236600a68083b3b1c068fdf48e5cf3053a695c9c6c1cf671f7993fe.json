{"ast": null, "code": "import { Vector, Vector3d } from \"./Utils/Vectors.js\";\nimport { calcExactPositionOrRandomFromSize, clamp, degToRad, getDistance, getParticleBaseVelocity, getParticleDirectionAngle, getRandom, getRangeValue, randomInRange, setRangeValue } from \"../Utils/NumberUtils.js\";\nimport { deepExtend, getPosition, initParticleNumericAnimationValue, isInArray, itemFromSingleOrMultiple } from \"../Utils/Utils.js\";\nimport { errorPrefix, millisecondsToSeconds } from \"./Utils/Constants.js\";\nimport { getHslFromAnimation, rangeColorToRgb } from \"../Utils/ColorUtils.js\";\nimport { EventType } from \"../Enums/Types/EventType.js\";\nimport { Interactivity } from \"../Options/Classes/Interactivity/Interactivity.js\";\nimport { MoveDirection } from \"../Enums/Directions/MoveDirection.js\";\nimport { OutMode } from \"../Enums/Modes/OutMode.js\";\nimport { ParticleOutType } from \"../Enums/Types/ParticleOutType.js\";\nimport { PixelMode } from \"../Enums/Modes/PixelMode.js\";\nimport { alterHsl } from \"../Utils/CanvasUtils.js\";\nimport { loadParticlesOptions } from \"../Utils/OptionsUtils.js\";\nconst defaultRetryCount = 0,\n  double = 2,\n  half = 0.5,\n  squareExp = 2,\n  randomString = \"random\";\nfunction loadEffectData(effect, effectOptions, id, reduceDuplicates) {\n  const effectData = effectOptions.options[effect];\n  if (!effectData) {\n    return;\n  }\n  return deepExtend({\n    close: effectOptions.close,\n    fill: effectOptions.fill\n  }, itemFromSingleOrMultiple(effectData, id, reduceDuplicates));\n}\nfunction loadShapeData(shape, shapeOptions, id, reduceDuplicates) {\n  const shapeData = shapeOptions.options[shape];\n  if (!shapeData) {\n    return;\n  }\n  return deepExtend({\n    close: shapeOptions.close,\n    fill: shapeOptions.fill\n  }, itemFromSingleOrMultiple(shapeData, id, reduceDuplicates));\n}\nfunction fixOutMode(data) {\n  if (!isInArray(data.outMode, data.checkModes)) {\n    return;\n  }\n  const diameter = data.radius * double;\n  if (data.coord > data.maxCoord - diameter) {\n    data.setCb(-data.radius);\n  } else if (data.coord < diameter) {\n    data.setCb(data.radius);\n  }\n}\nexport class Particle {\n  constructor(engine, container) {\n    this.container = container;\n    this._calcPosition = (container, position, zIndex, tryCount = defaultRetryCount) => {\n      for (const [, plugin] of container.plugins) {\n        const pluginPos = plugin.particlePosition !== undefined ? plugin.particlePosition(position, this) : undefined;\n        if (pluginPos) {\n          return Vector3d.create(pluginPos.x, pluginPos.y, zIndex);\n        }\n      }\n      const canvasSize = container.canvas.size,\n        exactPosition = calcExactPositionOrRandomFromSize({\n          size: canvasSize,\n          position: position\n        }),\n        pos = Vector3d.create(exactPosition.x, exactPosition.y, zIndex),\n        radius = this.getRadius(),\n        outModes = this.options.move.outModes,\n        fixHorizontal = outMode => {\n          fixOutMode({\n            outMode,\n            checkModes: [OutMode.bounce],\n            coord: pos.x,\n            maxCoord: container.canvas.size.width,\n            setCb: value => pos.x += value,\n            radius\n          });\n        },\n        fixVertical = outMode => {\n          fixOutMode({\n            outMode,\n            checkModes: [OutMode.bounce],\n            coord: pos.y,\n            maxCoord: container.canvas.size.height,\n            setCb: value => pos.y += value,\n            radius\n          });\n        };\n      fixHorizontal(outModes.left ?? outModes.default);\n      fixHorizontal(outModes.right ?? outModes.default);\n      fixVertical(outModes.top ?? outModes.default);\n      fixVertical(outModes.bottom ?? outModes.default);\n      if (this._checkOverlap(pos, tryCount)) {\n        const increment = 1;\n        return this._calcPosition(container, undefined, zIndex, tryCount + increment);\n      }\n      return pos;\n    };\n    this._calculateVelocity = () => {\n      const baseVelocity = getParticleBaseVelocity(this.direction),\n        res = baseVelocity.copy(),\n        moveOptions = this.options.move;\n      if (moveOptions.direction === MoveDirection.inside || moveOptions.direction === MoveDirection.outside) {\n        return res;\n      }\n      const rad = degToRad(getRangeValue(moveOptions.angle.value)),\n        radOffset = degToRad(getRangeValue(moveOptions.angle.offset)),\n        range = {\n          left: radOffset - rad * half,\n          right: radOffset + rad * half\n        };\n      if (!moveOptions.straight) {\n        res.angle += randomInRange(setRangeValue(range.left, range.right));\n      }\n      if (moveOptions.random && typeof moveOptions.speed === \"number\") {\n        res.length *= getRandom();\n      }\n      return res;\n    };\n    this._checkOverlap = (pos, tryCount = defaultRetryCount) => {\n      const collisionsOptions = this.options.collisions,\n        radius = this.getRadius();\n      if (!collisionsOptions.enable) {\n        return false;\n      }\n      const overlapOptions = collisionsOptions.overlap;\n      if (overlapOptions.enable) {\n        return false;\n      }\n      const retries = overlapOptions.retries,\n        minRetries = 0;\n      if (retries >= minRetries && tryCount > retries) {\n        throw new Error(`${errorPrefix} particle is overlapping and can't be placed`);\n      }\n      return !!this.container.particles.find(particle => getDistance(pos, particle.position) < radius + particle.getRadius());\n    };\n    this._getRollColor = color => {\n      if (!color || !this.roll || !this.backColor && !this.roll.alter) {\n        return color;\n      }\n      const rollFactor = 1,\n        none = 0,\n        backFactor = this.roll.horizontal && this.roll.vertical ? double * rollFactor : rollFactor,\n        backSum = this.roll.horizontal ? Math.PI * half : none,\n        rolled = Math.floor(((this.roll.angle ?? none) + backSum) / (Math.PI / backFactor)) % double;\n      if (!rolled) {\n        return color;\n      }\n      if (this.backColor) {\n        return this.backColor;\n      }\n      if (this.roll.alter) {\n        return alterHsl(color, this.roll.alter.type, this.roll.alter.value);\n      }\n      return color;\n    };\n    this._initPosition = position => {\n      const container = this.container,\n        zIndexValue = getRangeValue(this.options.zIndex.value),\n        minZ = 0;\n      this.position = this._calcPosition(container, position, clamp(zIndexValue, minZ, container.zLayers));\n      this.initialPosition = this.position.copy();\n      const canvasSize = container.canvas.size,\n        defaultRadius = 0;\n      this.moveCenter = {\n        ...getPosition(this.options.move.center, canvasSize),\n        radius: this.options.move.center.radius ?? defaultRadius,\n        mode: this.options.move.center.mode ?? PixelMode.percent\n      };\n      this.direction = getParticleDirectionAngle(this.options.move.direction, this.position, this.moveCenter);\n      switch (this.options.move.direction) {\n        case MoveDirection.inside:\n          this.outType = ParticleOutType.inside;\n          break;\n        case MoveDirection.outside:\n          this.outType = ParticleOutType.outside;\n          break;\n      }\n      this.offset = Vector.origin;\n    };\n    this._engine = engine;\n  }\n  destroy(override) {\n    if (this.unbreakable || this.destroyed) {\n      return;\n    }\n    this.destroyed = true;\n    this.bubble.inRange = false;\n    this.slow.inRange = false;\n    const container = this.container,\n      pathGenerator = this.pathGenerator,\n      shapeDrawer = container.shapeDrawers.get(this.shape);\n    shapeDrawer?.particleDestroy?.(this);\n    for (const [, plugin] of container.plugins) {\n      plugin.particleDestroyed?.(this, override);\n    }\n    for (const updater of container.particles.updaters) {\n      updater.particleDestroyed?.(this, override);\n    }\n    pathGenerator?.reset(this);\n    this._engine.dispatchEvent(EventType.particleDestroyed, {\n      container: this.container,\n      data: {\n        particle: this\n      }\n    });\n  }\n  draw(delta) {\n    const container = this.container,\n      canvas = container.canvas;\n    for (const [, plugin] of container.plugins) {\n      canvas.drawParticlePlugin(plugin, this, delta);\n    }\n    canvas.drawParticle(this, delta);\n  }\n  getFillColor() {\n    return this._getRollColor(this.bubble.color ?? getHslFromAnimation(this.color));\n  }\n  getMass() {\n    return this.getRadius() ** squareExp * Math.PI * half;\n  }\n  getPosition() {\n    return {\n      x: this.position.x + this.offset.x,\n      y: this.position.y + this.offset.y,\n      z: this.position.z\n    };\n  }\n  getRadius() {\n    return this.bubble.radius ?? this.size.value;\n  }\n  getStrokeColor() {\n    return this._getRollColor(this.bubble.color ?? getHslFromAnimation(this.strokeColor));\n  }\n  init(id, position, overrideOptions, group) {\n    const container = this.container,\n      engine = this._engine;\n    this.id = id;\n    this.group = group;\n    this.effectClose = true;\n    this.effectFill = true;\n    this.shapeClose = true;\n    this.shapeFill = true;\n    this.pathRotation = false;\n    this.lastPathTime = 0;\n    this.destroyed = false;\n    this.unbreakable = false;\n    this.isRotating = false;\n    this.rotation = 0;\n    this.misplaced = false;\n    this.retina = {\n      maxDistance: {}\n    };\n    this.outType = ParticleOutType.normal;\n    this.ignoresResizeRatio = true;\n    const pxRatio = container.retina.pixelRatio,\n      mainOptions = container.actualOptions,\n      particlesOptions = loadParticlesOptions(this._engine, container, mainOptions.particles),\n      {\n        reduceDuplicates\n      } = particlesOptions,\n      effectType = particlesOptions.effect.type,\n      shapeType = particlesOptions.shape.type;\n    this.effect = itemFromSingleOrMultiple(effectType, this.id, reduceDuplicates);\n    this.shape = itemFromSingleOrMultiple(shapeType, this.id, reduceDuplicates);\n    const effectOptions = particlesOptions.effect,\n      shapeOptions = particlesOptions.shape;\n    if (overrideOptions) {\n      if (overrideOptions.effect?.type) {\n        const overrideEffectType = overrideOptions.effect.type,\n          effect = itemFromSingleOrMultiple(overrideEffectType, this.id, reduceDuplicates);\n        if (effect) {\n          this.effect = effect;\n          effectOptions.load(overrideOptions.effect);\n        }\n      }\n      if (overrideOptions.shape?.type) {\n        const overrideShapeType = overrideOptions.shape.type,\n          shape = itemFromSingleOrMultiple(overrideShapeType, this.id, reduceDuplicates);\n        if (shape) {\n          this.shape = shape;\n          shapeOptions.load(overrideOptions.shape);\n        }\n      }\n    }\n    if (this.effect === randomString) {\n      const availableEffects = [...this.container.effectDrawers.keys()];\n      this.effect = availableEffects[Math.floor(Math.random() * availableEffects.length)];\n    }\n    if (this.shape === randomString) {\n      const availableShapes = [...this.container.shapeDrawers.keys()];\n      this.shape = availableShapes[Math.floor(Math.random() * availableShapes.length)];\n    }\n    this.effectData = loadEffectData(this.effect, effectOptions, this.id, reduceDuplicates);\n    this.shapeData = loadShapeData(this.shape, shapeOptions, this.id, reduceDuplicates);\n    particlesOptions.load(overrideOptions);\n    const effectData = this.effectData;\n    if (effectData) {\n      particlesOptions.load(effectData.particles);\n    }\n    const shapeData = this.shapeData;\n    if (shapeData) {\n      particlesOptions.load(shapeData.particles);\n    }\n    const interactivity = new Interactivity(engine, container);\n    interactivity.load(container.actualOptions.interactivity);\n    interactivity.load(particlesOptions.interactivity);\n    this.interactivity = interactivity;\n    this.effectFill = effectData?.fill ?? particlesOptions.effect.fill;\n    this.effectClose = effectData?.close ?? particlesOptions.effect.close;\n    this.shapeFill = shapeData?.fill ?? particlesOptions.shape.fill;\n    this.shapeClose = shapeData?.close ?? particlesOptions.shape.close;\n    this.options = particlesOptions;\n    const pathOptions = this.options.move.path;\n    this.pathDelay = getRangeValue(pathOptions.delay.value) * millisecondsToSeconds;\n    if (pathOptions.generator) {\n      this.pathGenerator = this._engine.getPathGenerator(pathOptions.generator);\n      if (this.pathGenerator && container.addPath(pathOptions.generator, this.pathGenerator)) {\n        this.pathGenerator.init(container);\n      }\n    }\n    container.retina.initParticle(this);\n    this.size = initParticleNumericAnimationValue(this.options.size, pxRatio);\n    this.bubble = {\n      inRange: false\n    };\n    this.slow = {\n      inRange: false,\n      factor: 1\n    };\n    this._initPosition(position);\n    this.initialVelocity = this._calculateVelocity();\n    this.velocity = this.initialVelocity.copy();\n    const decayOffset = 1;\n    this.moveDecay = decayOffset - getRangeValue(this.options.move.decay);\n    const particles = container.particles;\n    particles.setLastZIndex(this.position.z);\n    this.zIndexFactor = this.position.z / container.zLayers;\n    this.sides = 24;\n    let effectDrawer = container.effectDrawers.get(this.effect);\n    if (!effectDrawer) {\n      effectDrawer = this._engine.getEffectDrawer(this.effect);\n      if (effectDrawer) {\n        container.effectDrawers.set(this.effect, effectDrawer);\n      }\n    }\n    if (effectDrawer?.loadEffect) {\n      effectDrawer.loadEffect(this);\n    }\n    let shapeDrawer = container.shapeDrawers.get(this.shape);\n    if (!shapeDrawer) {\n      shapeDrawer = this._engine.getShapeDrawer(this.shape);\n      if (shapeDrawer) {\n        container.shapeDrawers.set(this.shape, shapeDrawer);\n      }\n    }\n    if (shapeDrawer?.loadShape) {\n      shapeDrawer.loadShape(this);\n    }\n    const sideCountFunc = shapeDrawer?.getSidesCount;\n    if (sideCountFunc) {\n      this.sides = sideCountFunc(this);\n    }\n    this.spawning = false;\n    this.shadowColor = rangeColorToRgb(this.options.shadow.color);\n    for (const updater of particles.updaters) {\n      updater.init(this);\n    }\n    for (const mover of particles.movers) {\n      mover.init?.(this);\n    }\n    effectDrawer?.particleInit?.(container, this);\n    shapeDrawer?.particleInit?.(container, this);\n    for (const [, plugin] of container.plugins) {\n      plugin.particleCreated?.(this);\n    }\n  }\n  isInsideCanvas() {\n    const radius = this.getRadius(),\n      canvasSize = this.container.canvas.size,\n      position = this.position;\n    return position.x >= -radius && position.y >= -radius && position.y <= canvasSize.height + radius && position.x <= canvasSize.width + radius;\n  }\n  isVisible() {\n    return !this.destroyed && !this.spawning && this.isInsideCanvas();\n  }\n  reset() {\n    for (const updater of this.container.particles.updaters) {\n      updater.reset?.(this);\n    }\n  }\n}", "map": {"version": 3, "names": ["Vector", "Vector3d", "calcExactPositionOrRandomFromSize", "clamp", "degToRad", "getDistance", "getParticleBaseVelocity", "getParticleDirectionAngle", "getRandom", "getRangeValue", "randomInRange", "setRangeValue", "deepExtend", "getPosition", "initParticleNumericAnimationValue", "isInArray", "itemFromSingleOrMultiple", "errorPrefix", "millisecondsToSeconds", "getHslFromAnimation", "rangeColorToRgb", "EventType", "Interactivity", "MoveDirection", "OutMode", "ParticleOutType", "PixelMode", "alterHsl", "loadParticlesOptions", "defaultRetryCount", "double", "half", "squareExp", "randomString", "loadEffectData", "effect", "effectOptions", "id", "reduceDuplicates", "effectData", "options", "close", "fill", "loadShapeData", "shape", "shapeOptions", "shapeData", "fixOutMode", "data", "outMode", "checkModes", "diameter", "radius", "coord", "maxCoord", "setCb", "Particle", "constructor", "engine", "container", "_calcPosition", "position", "zIndex", "tryCount", "plugin", "plugins", "pluginPos", "particlePosition", "undefined", "create", "x", "y", "canvasSize", "canvas", "size", "exactPosition", "pos", "getRadius", "outModes", "move", "fixHorizontal", "bounce", "width", "value", "fixVertical", "height", "left", "default", "right", "top", "bottom", "_checkOverlap", "increment", "_calculateVelocity", "baseVelocity", "direction", "res", "copy", "moveOptions", "inside", "outside", "rad", "angle", "radOffset", "offset", "range", "straight", "random", "speed", "length", "collisionsOptions", "collisions", "enable", "overlapOptions", "overlap", "retries", "minRetries", "Error", "particles", "find", "particle", "_getRollColor", "color", "roll", "backColor", "alter", "rollFactor", "none", "backFactor", "horizontal", "vertical", "backSum", "Math", "PI", "rolled", "floor", "type", "_initPosition", "zIndexValue", "minZ", "zLayers", "initialPosition", "defaultRadius", "moveCenter", "center", "mode", "percent", "outType", "origin", "_engine", "destroy", "override", "unbreakable", "destroyed", "bubble", "inRange", "slow", "pathGenerator", "shapeDrawer", "shapeDrawers", "get", "particleDestroy", "particleDestroyed", "updater", "updaters", "reset", "dispatchEvent", "draw", "delta", "drawParticlePlugin", "drawParticle", "getFillColor", "getMass", "z", "getStrokeColor", "strokeColor", "init", "overrideOptions", "group", "effectClose", "effectFill", "shapeClose", "shapeFill", "pathRotation", "lastPathTime", "isRotating", "rotation", "misplaced", "retina", "maxDistance", "normal", "ignoresResizeRatio", "pxRatio", "pixelRatio", "mainOptions", "actualOptions", "particlesOptions", "effectType", "shapeType", "overrideEffectType", "load", "overrideShapeType", "availableEffects", "effectDrawers", "keys", "availableShapes", "interactivity", "pathOptions", "path", "pathDelay", "delay", "generator", "getPathGenerator", "addPath", "initParticle", "factor", "initialVelocity", "velocity", "decayOffset", "moveDecay", "decay", "setLastZIndex", "zIndexFactor", "sides", "effectDrawer", "getEffectDrawer", "set", "loadEffect", "getShapeDrawer", "loadShape", "sideCountFunc", "getSidesCount", "spawning", "shadowColor", "shadow", "mover", "movers", "particleInit", "particleCreated", "isInsideCanvas", "isVisible"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/engine/browser/Core/Particle.js"], "sourcesContent": ["import { Vector, Vector3d } from \"./Utils/Vectors.js\";\nimport { calcExactPositionOrRandomFromSize, clamp, degToRad, getDistance, getParticleBaseVelocity, getParticleDirectionAngle, getRandom, getRangeValue, randomInRange, setRangeValue, } from \"../Utils/NumberUtils.js\";\nimport { deepExtend, getPosition, initParticleNumericAnimationValue, isInArray, itemFromSingleOrMultiple, } from \"../Utils/Utils.js\";\nimport { errorPrefix, millisecondsToSeconds } from \"./Utils/Constants.js\";\nimport { getHslFromAnimation, rangeColorToRgb } from \"../Utils/ColorUtils.js\";\nimport { EventType } from \"../Enums/Types/EventType.js\";\nimport { Interactivity } from \"../Options/Classes/Interactivity/Interactivity.js\";\nimport { MoveDirection } from \"../Enums/Directions/MoveDirection.js\";\nimport { OutMode } from \"../Enums/Modes/OutMode.js\";\nimport { ParticleOutType } from \"../Enums/Types/ParticleOutType.js\";\nimport { PixelMode } from \"../Enums/Modes/PixelMode.js\";\nimport { alterHsl } from \"../Utils/CanvasUtils.js\";\nimport { loadParticlesOptions } from \"../Utils/OptionsUtils.js\";\nconst defaultRetryCount = 0, double = 2, half = 0.5, squareExp = 2, randomString = \"random\";\nfunction loadEffectData(effect, effectOptions, id, reduceDuplicates) {\n    const effectData = effectOptions.options[effect];\n    if (!effectData) {\n        return;\n    }\n    return deepExtend({\n        close: effectOptions.close,\n        fill: effectOptions.fill,\n    }, itemFromSingleOrMultiple(effectData, id, reduceDuplicates));\n}\nfunction loadShapeData(shape, shapeOptions, id, reduceDuplicates) {\n    const shapeData = shapeOptions.options[shape];\n    if (!shapeData) {\n        return;\n    }\n    return deepExtend({\n        close: shapeOptions.close,\n        fill: shapeOptions.fill,\n    }, itemFromSingleOrMultiple(shapeData, id, reduceDuplicates));\n}\nfunction fixOutMode(data) {\n    if (!isInArray(data.outMode, data.checkModes)) {\n        return;\n    }\n    const diameter = data.radius * double;\n    if (data.coord > data.maxCoord - diameter) {\n        data.setCb(-data.radius);\n    }\n    else if (data.coord < diameter) {\n        data.setCb(data.radius);\n    }\n}\nexport class Particle {\n    constructor(engine, container) {\n        this.container = container;\n        this._calcPosition = (container, position, zIndex, tryCount = defaultRetryCount) => {\n            for (const [, plugin] of container.plugins) {\n                const pluginPos = plugin.particlePosition !== undefined ? plugin.particlePosition(position, this) : undefined;\n                if (pluginPos) {\n                    return Vector3d.create(pluginPos.x, pluginPos.y, zIndex);\n                }\n            }\n            const canvasSize = container.canvas.size, exactPosition = calcExactPositionOrRandomFromSize({\n                size: canvasSize,\n                position: position,\n            }), pos = Vector3d.create(exactPosition.x, exactPosition.y, zIndex), radius = this.getRadius(), outModes = this.options.move.outModes, fixHorizontal = (outMode) => {\n                fixOutMode({\n                    outMode,\n                    checkModes: [OutMode.bounce],\n                    coord: pos.x,\n                    maxCoord: container.canvas.size.width,\n                    setCb: (value) => (pos.x += value),\n                    radius,\n                });\n            }, fixVertical = (outMode) => {\n                fixOutMode({\n                    outMode,\n                    checkModes: [OutMode.bounce],\n                    coord: pos.y,\n                    maxCoord: container.canvas.size.height,\n                    setCb: (value) => (pos.y += value),\n                    radius,\n                });\n            };\n            fixHorizontal(outModes.left ?? outModes.default);\n            fixHorizontal(outModes.right ?? outModes.default);\n            fixVertical(outModes.top ?? outModes.default);\n            fixVertical(outModes.bottom ?? outModes.default);\n            if (this._checkOverlap(pos, tryCount)) {\n                const increment = 1;\n                return this._calcPosition(container, undefined, zIndex, tryCount + increment);\n            }\n            return pos;\n        };\n        this._calculateVelocity = () => {\n            const baseVelocity = getParticleBaseVelocity(this.direction), res = baseVelocity.copy(), moveOptions = this.options.move;\n            if (moveOptions.direction === MoveDirection.inside || moveOptions.direction === MoveDirection.outside) {\n                return res;\n            }\n            const rad = degToRad(getRangeValue(moveOptions.angle.value)), radOffset = degToRad(getRangeValue(moveOptions.angle.offset)), range = {\n                left: radOffset - rad * half,\n                right: radOffset + rad * half,\n            };\n            if (!moveOptions.straight) {\n                res.angle += randomInRange(setRangeValue(range.left, range.right));\n            }\n            if (moveOptions.random && typeof moveOptions.speed === \"number\") {\n                res.length *= getRandom();\n            }\n            return res;\n        };\n        this._checkOverlap = (pos, tryCount = defaultRetryCount) => {\n            const collisionsOptions = this.options.collisions, radius = this.getRadius();\n            if (!collisionsOptions.enable) {\n                return false;\n            }\n            const overlapOptions = collisionsOptions.overlap;\n            if (overlapOptions.enable) {\n                return false;\n            }\n            const retries = overlapOptions.retries, minRetries = 0;\n            if (retries >= minRetries && tryCount > retries) {\n                throw new Error(`${errorPrefix} particle is overlapping and can't be placed`);\n            }\n            return !!this.container.particles.find(particle => getDistance(pos, particle.position) < radius + particle.getRadius());\n        };\n        this._getRollColor = color => {\n            if (!color || !this.roll || (!this.backColor && !this.roll.alter)) {\n                return color;\n            }\n            const rollFactor = 1, none = 0, backFactor = this.roll.horizontal && this.roll.vertical ? double * rollFactor : rollFactor, backSum = this.roll.horizontal ? Math.PI * half : none, rolled = Math.floor(((this.roll.angle ?? none) + backSum) / (Math.PI / backFactor)) % double;\n            if (!rolled) {\n                return color;\n            }\n            if (this.backColor) {\n                return this.backColor;\n            }\n            if (this.roll.alter) {\n                return alterHsl(color, this.roll.alter.type, this.roll.alter.value);\n            }\n            return color;\n        };\n        this._initPosition = position => {\n            const container = this.container, zIndexValue = getRangeValue(this.options.zIndex.value), minZ = 0;\n            this.position = this._calcPosition(container, position, clamp(zIndexValue, minZ, container.zLayers));\n            this.initialPosition = this.position.copy();\n            const canvasSize = container.canvas.size, defaultRadius = 0;\n            this.moveCenter = {\n                ...getPosition(this.options.move.center, canvasSize),\n                radius: this.options.move.center.radius ?? defaultRadius,\n                mode: this.options.move.center.mode ?? PixelMode.percent,\n            };\n            this.direction = getParticleDirectionAngle(this.options.move.direction, this.position, this.moveCenter);\n            switch (this.options.move.direction) {\n                case MoveDirection.inside:\n                    this.outType = ParticleOutType.inside;\n                    break;\n                case MoveDirection.outside:\n                    this.outType = ParticleOutType.outside;\n                    break;\n            }\n            this.offset = Vector.origin;\n        };\n        this._engine = engine;\n    }\n    destroy(override) {\n        if (this.unbreakable || this.destroyed) {\n            return;\n        }\n        this.destroyed = true;\n        this.bubble.inRange = false;\n        this.slow.inRange = false;\n        const container = this.container, pathGenerator = this.pathGenerator, shapeDrawer = container.shapeDrawers.get(this.shape);\n        shapeDrawer?.particleDestroy?.(this);\n        for (const [, plugin] of container.plugins) {\n            plugin.particleDestroyed?.(this, override);\n        }\n        for (const updater of container.particles.updaters) {\n            updater.particleDestroyed?.(this, override);\n        }\n        pathGenerator?.reset(this);\n        this._engine.dispatchEvent(EventType.particleDestroyed, {\n            container: this.container,\n            data: {\n                particle: this,\n            },\n        });\n    }\n    draw(delta) {\n        const container = this.container, canvas = container.canvas;\n        for (const [, plugin] of container.plugins) {\n            canvas.drawParticlePlugin(plugin, this, delta);\n        }\n        canvas.drawParticle(this, delta);\n    }\n    getFillColor() {\n        return this._getRollColor(this.bubble.color ?? getHslFromAnimation(this.color));\n    }\n    getMass() {\n        return this.getRadius() ** squareExp * Math.PI * half;\n    }\n    getPosition() {\n        return {\n            x: this.position.x + this.offset.x,\n            y: this.position.y + this.offset.y,\n            z: this.position.z,\n        };\n    }\n    getRadius() {\n        return this.bubble.radius ?? this.size.value;\n    }\n    getStrokeColor() {\n        return this._getRollColor(this.bubble.color ?? getHslFromAnimation(this.strokeColor));\n    }\n    init(id, position, overrideOptions, group) {\n        const container = this.container, engine = this._engine;\n        this.id = id;\n        this.group = group;\n        this.effectClose = true;\n        this.effectFill = true;\n        this.shapeClose = true;\n        this.shapeFill = true;\n        this.pathRotation = false;\n        this.lastPathTime = 0;\n        this.destroyed = false;\n        this.unbreakable = false;\n        this.isRotating = false;\n        this.rotation = 0;\n        this.misplaced = false;\n        this.retina = {\n            maxDistance: {},\n        };\n        this.outType = ParticleOutType.normal;\n        this.ignoresResizeRatio = true;\n        const pxRatio = container.retina.pixelRatio, mainOptions = container.actualOptions, particlesOptions = loadParticlesOptions(this._engine, container, mainOptions.particles), { reduceDuplicates } = particlesOptions, effectType = particlesOptions.effect.type, shapeType = particlesOptions.shape.type;\n        this.effect = itemFromSingleOrMultiple(effectType, this.id, reduceDuplicates);\n        this.shape = itemFromSingleOrMultiple(shapeType, this.id, reduceDuplicates);\n        const effectOptions = particlesOptions.effect, shapeOptions = particlesOptions.shape;\n        if (overrideOptions) {\n            if (overrideOptions.effect?.type) {\n                const overrideEffectType = overrideOptions.effect.type, effect = itemFromSingleOrMultiple(overrideEffectType, this.id, reduceDuplicates);\n                if (effect) {\n                    this.effect = effect;\n                    effectOptions.load(overrideOptions.effect);\n                }\n            }\n            if (overrideOptions.shape?.type) {\n                const overrideShapeType = overrideOptions.shape.type, shape = itemFromSingleOrMultiple(overrideShapeType, this.id, reduceDuplicates);\n                if (shape) {\n                    this.shape = shape;\n                    shapeOptions.load(overrideOptions.shape);\n                }\n            }\n        }\n        if (this.effect === randomString) {\n            const availableEffects = [...this.container.effectDrawers.keys()];\n            this.effect = availableEffects[Math.floor(Math.random() * availableEffects.length)];\n        }\n        if (this.shape === randomString) {\n            const availableShapes = [...this.container.shapeDrawers.keys()];\n            this.shape = availableShapes[Math.floor(Math.random() * availableShapes.length)];\n        }\n        this.effectData = loadEffectData(this.effect, effectOptions, this.id, reduceDuplicates);\n        this.shapeData = loadShapeData(this.shape, shapeOptions, this.id, reduceDuplicates);\n        particlesOptions.load(overrideOptions);\n        const effectData = this.effectData;\n        if (effectData) {\n            particlesOptions.load(effectData.particles);\n        }\n        const shapeData = this.shapeData;\n        if (shapeData) {\n            particlesOptions.load(shapeData.particles);\n        }\n        const interactivity = new Interactivity(engine, container);\n        interactivity.load(container.actualOptions.interactivity);\n        interactivity.load(particlesOptions.interactivity);\n        this.interactivity = interactivity;\n        this.effectFill = effectData?.fill ?? particlesOptions.effect.fill;\n        this.effectClose = effectData?.close ?? particlesOptions.effect.close;\n        this.shapeFill = shapeData?.fill ?? particlesOptions.shape.fill;\n        this.shapeClose = shapeData?.close ?? particlesOptions.shape.close;\n        this.options = particlesOptions;\n        const pathOptions = this.options.move.path;\n        this.pathDelay = getRangeValue(pathOptions.delay.value) * millisecondsToSeconds;\n        if (pathOptions.generator) {\n            this.pathGenerator = this._engine.getPathGenerator(pathOptions.generator);\n            if (this.pathGenerator && container.addPath(pathOptions.generator, this.pathGenerator)) {\n                this.pathGenerator.init(container);\n            }\n        }\n        container.retina.initParticle(this);\n        this.size = initParticleNumericAnimationValue(this.options.size, pxRatio);\n        this.bubble = {\n            inRange: false,\n        };\n        this.slow = {\n            inRange: false,\n            factor: 1,\n        };\n        this._initPosition(position);\n        this.initialVelocity = this._calculateVelocity();\n        this.velocity = this.initialVelocity.copy();\n        const decayOffset = 1;\n        this.moveDecay = decayOffset - getRangeValue(this.options.move.decay);\n        const particles = container.particles;\n        particles.setLastZIndex(this.position.z);\n        this.zIndexFactor = this.position.z / container.zLayers;\n        this.sides = 24;\n        let effectDrawer = container.effectDrawers.get(this.effect);\n        if (!effectDrawer) {\n            effectDrawer = this._engine.getEffectDrawer(this.effect);\n            if (effectDrawer) {\n                container.effectDrawers.set(this.effect, effectDrawer);\n            }\n        }\n        if (effectDrawer?.loadEffect) {\n            effectDrawer.loadEffect(this);\n        }\n        let shapeDrawer = container.shapeDrawers.get(this.shape);\n        if (!shapeDrawer) {\n            shapeDrawer = this._engine.getShapeDrawer(this.shape);\n            if (shapeDrawer) {\n                container.shapeDrawers.set(this.shape, shapeDrawer);\n            }\n        }\n        if (shapeDrawer?.loadShape) {\n            shapeDrawer.loadShape(this);\n        }\n        const sideCountFunc = shapeDrawer?.getSidesCount;\n        if (sideCountFunc) {\n            this.sides = sideCountFunc(this);\n        }\n        this.spawning = false;\n        this.shadowColor = rangeColorToRgb(this.options.shadow.color);\n        for (const updater of particles.updaters) {\n            updater.init(this);\n        }\n        for (const mover of particles.movers) {\n            mover.init?.(this);\n        }\n        effectDrawer?.particleInit?.(container, this);\n        shapeDrawer?.particleInit?.(container, this);\n        for (const [, plugin] of container.plugins) {\n            plugin.particleCreated?.(this);\n        }\n    }\n    isInsideCanvas() {\n        const radius = this.getRadius(), canvasSize = this.container.canvas.size, position = this.position;\n        return (position.x >= -radius &&\n            position.y >= -radius &&\n            position.y <= canvasSize.height + radius &&\n            position.x <= canvasSize.width + radius);\n    }\n    isVisible() {\n        return !this.destroyed && !this.spawning && this.isInsideCanvas();\n    }\n    reset() {\n        for (const updater of this.container.particles.updaters) {\n            updater.reset?.(this);\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,MAAM,EAAEC,QAAQ,QAAQ,oBAAoB;AACrD,SAASC,iCAAiC,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,WAAW,EAAEC,uBAAuB,EAAEC,yBAAyB,EAAEC,SAAS,EAAEC,aAAa,EAAEC,aAAa,EAAEC,aAAa,QAAS,yBAAyB;AACtN,SAASC,UAAU,EAAEC,WAAW,EAAEC,iCAAiC,EAAEC,SAAS,EAAEC,wBAAwB,QAAS,mBAAmB;AACpI,SAASC,WAAW,EAAEC,qBAAqB,QAAQ,sBAAsB;AACzE,SAASC,mBAAmB,EAAEC,eAAe,QAAQ,wBAAwB;AAC7E,SAASC,SAAS,QAAQ,6BAA6B;AACvD,SAASC,aAAa,QAAQ,mDAAmD;AACjF,SAASC,aAAa,QAAQ,sCAAsC;AACpE,SAASC,OAAO,QAAQ,2BAA2B;AACnD,SAASC,eAAe,QAAQ,mCAAmC;AACnE,SAASC,SAAS,QAAQ,6BAA6B;AACvD,SAASC,QAAQ,QAAQ,yBAAyB;AAClD,SAASC,oBAAoB,QAAQ,0BAA0B;AAC/D,MAAMC,iBAAiB,GAAG,CAAC;EAAEC,MAAM,GAAG,CAAC;EAAEC,IAAI,GAAG,GAAG;EAAEC,SAAS,GAAG,CAAC;EAAEC,YAAY,GAAG,QAAQ;AAC3F,SAASC,cAAcA,CAACC,MAAM,EAAEC,aAAa,EAAEC,EAAE,EAAEC,gBAAgB,EAAE;EACjE,MAAMC,UAAU,GAAGH,aAAa,CAACI,OAAO,CAACL,MAAM,CAAC;EAChD,IAAI,CAACI,UAAU,EAAE;IACb;EACJ;EACA,OAAO3B,UAAU,CAAC;IACd6B,KAAK,EAAEL,aAAa,CAACK,KAAK;IAC1BC,IAAI,EAAEN,aAAa,CAACM;EACxB,CAAC,EAAE1B,wBAAwB,CAACuB,UAAU,EAAEF,EAAE,EAAEC,gBAAgB,CAAC,CAAC;AAClE;AACA,SAASK,aAAaA,CAACC,KAAK,EAAEC,YAAY,EAAER,EAAE,EAAEC,gBAAgB,EAAE;EAC9D,MAAMQ,SAAS,GAAGD,YAAY,CAACL,OAAO,CAACI,KAAK,CAAC;EAC7C,IAAI,CAACE,SAAS,EAAE;IACZ;EACJ;EACA,OAAOlC,UAAU,CAAC;IACd6B,KAAK,EAAEI,YAAY,CAACJ,KAAK;IACzBC,IAAI,EAAEG,YAAY,CAACH;EACvB,CAAC,EAAE1B,wBAAwB,CAAC8B,SAAS,EAAET,EAAE,EAAEC,gBAAgB,CAAC,CAAC;AACjE;AACA,SAASS,UAAUA,CAACC,IAAI,EAAE;EACtB,IAAI,CAACjC,SAAS,CAACiC,IAAI,CAACC,OAAO,EAAED,IAAI,CAACE,UAAU,CAAC,EAAE;IAC3C;EACJ;EACA,MAAMC,QAAQ,GAAGH,IAAI,CAACI,MAAM,GAAGtB,MAAM;EACrC,IAAIkB,IAAI,CAACK,KAAK,GAAGL,IAAI,CAACM,QAAQ,GAAGH,QAAQ,EAAE;IACvCH,IAAI,CAACO,KAAK,CAAC,CAACP,IAAI,CAACI,MAAM,CAAC;EAC5B,CAAC,MACI,IAAIJ,IAAI,CAACK,KAAK,GAAGF,QAAQ,EAAE;IAC5BH,IAAI,CAACO,KAAK,CAACP,IAAI,CAACI,MAAM,CAAC;EAC3B;AACJ;AACA,OAAO,MAAMI,QAAQ,CAAC;EAClBC,WAAWA,CAACC,MAAM,EAAEC,SAAS,EAAE;IAC3B,IAAI,CAACA,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,aAAa,GAAG,CAACD,SAAS,EAAEE,QAAQ,EAAEC,MAAM,EAAEC,QAAQ,GAAGlC,iBAAiB,KAAK;MAChF,KAAK,MAAM,GAAGmC,MAAM,CAAC,IAAIL,SAAS,CAACM,OAAO,EAAE;QACxC,MAAMC,SAAS,GAAGF,MAAM,CAACG,gBAAgB,KAAKC,SAAS,GAAGJ,MAAM,CAACG,gBAAgB,CAACN,QAAQ,EAAE,IAAI,CAAC,GAAGO,SAAS;QAC7G,IAAIF,SAAS,EAAE;UACX,OAAOjE,QAAQ,CAACoE,MAAM,CAACH,SAAS,CAACI,CAAC,EAAEJ,SAAS,CAACK,CAAC,EAAET,MAAM,CAAC;QAC5D;MACJ;MACA,MAAMU,UAAU,GAAGb,SAAS,CAACc,MAAM,CAACC,IAAI;QAAEC,aAAa,GAAGzE,iCAAiC,CAAC;UACxFwE,IAAI,EAAEF,UAAU;UAChBX,QAAQ,EAAEA;QACd,CAAC,CAAC;QAAEe,GAAG,GAAG3E,QAAQ,CAACoE,MAAM,CAACM,aAAa,CAACL,CAAC,EAAEK,aAAa,CAACJ,CAAC,EAAET,MAAM,CAAC;QAAEV,MAAM,GAAG,IAAI,CAACyB,SAAS,CAAC,CAAC;QAAEC,QAAQ,GAAG,IAAI,CAACtC,OAAO,CAACuC,IAAI,CAACD,QAAQ;QAAEE,aAAa,GAAI/B,OAAO,IAAK;UAChKF,UAAU,CAAC;YACPE,OAAO;YACPC,UAAU,EAAE,CAAC1B,OAAO,CAACyD,MAAM,CAAC;YAC5B5B,KAAK,EAAEuB,GAAG,CAACN,CAAC;YACZhB,QAAQ,EAAEK,SAAS,CAACc,MAAM,CAACC,IAAI,CAACQ,KAAK;YACrC3B,KAAK,EAAG4B,KAAK,IAAMP,GAAG,CAACN,CAAC,IAAIa,KAAM;YAClC/B;UACJ,CAAC,CAAC;QACN,CAAC;QAAEgC,WAAW,GAAInC,OAAO,IAAK;UAC1BF,UAAU,CAAC;YACPE,OAAO;YACPC,UAAU,EAAE,CAAC1B,OAAO,CAACyD,MAAM,CAAC;YAC5B5B,KAAK,EAAEuB,GAAG,CAACL,CAAC;YACZjB,QAAQ,EAAEK,SAAS,CAACc,MAAM,CAACC,IAAI,CAACW,MAAM;YACtC9B,KAAK,EAAG4B,KAAK,IAAMP,GAAG,CAACL,CAAC,IAAIY,KAAM;YAClC/B;UACJ,CAAC,CAAC;QACN,CAAC;MACD4B,aAAa,CAACF,QAAQ,CAACQ,IAAI,IAAIR,QAAQ,CAACS,OAAO,CAAC;MAChDP,aAAa,CAACF,QAAQ,CAACU,KAAK,IAAIV,QAAQ,CAACS,OAAO,CAAC;MACjDH,WAAW,CAACN,QAAQ,CAACW,GAAG,IAAIX,QAAQ,CAACS,OAAO,CAAC;MAC7CH,WAAW,CAACN,QAAQ,CAACY,MAAM,IAAIZ,QAAQ,CAACS,OAAO,CAAC;MAChD,IAAI,IAAI,CAACI,aAAa,CAACf,GAAG,EAAEb,QAAQ,CAAC,EAAE;QACnC,MAAM6B,SAAS,GAAG,CAAC;QACnB,OAAO,IAAI,CAAChC,aAAa,CAACD,SAAS,EAAES,SAAS,EAAEN,MAAM,EAAEC,QAAQ,GAAG6B,SAAS,CAAC;MACjF;MACA,OAAOhB,GAAG;IACd,CAAC;IACD,IAAI,CAACiB,kBAAkB,GAAG,MAAM;MAC5B,MAAMC,YAAY,GAAGxF,uBAAuB,CAAC,IAAI,CAACyF,SAAS,CAAC;QAAEC,GAAG,GAAGF,YAAY,CAACG,IAAI,CAAC,CAAC;QAAEC,WAAW,GAAG,IAAI,CAAC1D,OAAO,CAACuC,IAAI;MACxH,IAAImB,WAAW,CAACH,SAAS,KAAKxE,aAAa,CAAC4E,MAAM,IAAID,WAAW,CAACH,SAAS,KAAKxE,aAAa,CAAC6E,OAAO,EAAE;QACnG,OAAOJ,GAAG;MACd;MACA,MAAMK,GAAG,GAAGjG,QAAQ,CAACK,aAAa,CAACyF,WAAW,CAACI,KAAK,CAACnB,KAAK,CAAC,CAAC;QAAEoB,SAAS,GAAGnG,QAAQ,CAACK,aAAa,CAACyF,WAAW,CAACI,KAAK,CAACE,MAAM,CAAC,CAAC;QAAEC,KAAK,GAAG;UACjInB,IAAI,EAAEiB,SAAS,GAAGF,GAAG,GAAGtE,IAAI;UAC5ByD,KAAK,EAAEe,SAAS,GAAGF,GAAG,GAAGtE;QAC7B,CAAC;MACD,IAAI,CAACmE,WAAW,CAACQ,QAAQ,EAAE;QACvBV,GAAG,CAACM,KAAK,IAAI5F,aAAa,CAACC,aAAa,CAAC8F,KAAK,CAACnB,IAAI,EAAEmB,KAAK,CAACjB,KAAK,CAAC,CAAC;MACtE;MACA,IAAIU,WAAW,CAACS,MAAM,IAAI,OAAOT,WAAW,CAACU,KAAK,KAAK,QAAQ,EAAE;QAC7DZ,GAAG,CAACa,MAAM,IAAIrG,SAAS,CAAC,CAAC;MAC7B;MACA,OAAOwF,GAAG;IACd,CAAC;IACD,IAAI,CAACL,aAAa,GAAG,CAACf,GAAG,EAAEb,QAAQ,GAAGlC,iBAAiB,KAAK;MACxD,MAAMiF,iBAAiB,GAAG,IAAI,CAACtE,OAAO,CAACuE,UAAU;QAAE3D,MAAM,GAAG,IAAI,CAACyB,SAAS,CAAC,CAAC;MAC5E,IAAI,CAACiC,iBAAiB,CAACE,MAAM,EAAE;QAC3B,OAAO,KAAK;MAChB;MACA,MAAMC,cAAc,GAAGH,iBAAiB,CAACI,OAAO;MAChD,IAAID,cAAc,CAACD,MAAM,EAAE;QACvB,OAAO,KAAK;MAChB;MACA,MAAMG,OAAO,GAAGF,cAAc,CAACE,OAAO;QAAEC,UAAU,GAAG,CAAC;MACtD,IAAID,OAAO,IAAIC,UAAU,IAAIrD,QAAQ,GAAGoD,OAAO,EAAE;QAC7C,MAAM,IAAIE,KAAK,CAAC,GAAGpG,WAAW,8CAA8C,CAAC;MACjF;MACA,OAAO,CAAC,CAAC,IAAI,CAAC0C,SAAS,CAAC2D,SAAS,CAACC,IAAI,CAACC,QAAQ,IAAInH,WAAW,CAACuE,GAAG,EAAE4C,QAAQ,CAAC3D,QAAQ,CAAC,GAAGT,MAAM,GAAGoE,QAAQ,CAAC3C,SAAS,CAAC,CAAC,CAAC;IAC3H,CAAC;IACD,IAAI,CAAC4C,aAAa,GAAGC,KAAK,IAAI;MAC1B,IAAI,CAACA,KAAK,IAAI,CAAC,IAAI,CAACC,IAAI,IAAK,CAAC,IAAI,CAACC,SAAS,IAAI,CAAC,IAAI,CAACD,IAAI,CAACE,KAAM,EAAE;QAC/D,OAAOH,KAAK;MAChB;MACA,MAAMI,UAAU,GAAG,CAAC;QAAEC,IAAI,GAAG,CAAC;QAAEC,UAAU,GAAG,IAAI,CAACL,IAAI,CAACM,UAAU,IAAI,IAAI,CAACN,IAAI,CAACO,QAAQ,GAAGpG,MAAM,GAAGgG,UAAU,GAAGA,UAAU;QAAEK,OAAO,GAAG,IAAI,CAACR,IAAI,CAACM,UAAU,GAAGG,IAAI,CAACC,EAAE,GAAGtG,IAAI,GAAGgG,IAAI;QAAEO,MAAM,GAAGF,IAAI,CAACG,KAAK,CAAC,CAAC,CAAC,IAAI,CAACZ,IAAI,CAACrB,KAAK,IAAIyB,IAAI,IAAII,OAAO,KAAKC,IAAI,CAACC,EAAE,GAAGL,UAAU,CAAC,CAAC,GAAGlG,MAAM;MAChR,IAAI,CAACwG,MAAM,EAAE;QACT,OAAOZ,KAAK;MAChB;MACA,IAAI,IAAI,CAACE,SAAS,EAAE;QAChB,OAAO,IAAI,CAACA,SAAS;MACzB;MACA,IAAI,IAAI,CAACD,IAAI,CAACE,KAAK,EAAE;QACjB,OAAOlG,QAAQ,CAAC+F,KAAK,EAAE,IAAI,CAACC,IAAI,CAACE,KAAK,CAACW,IAAI,EAAE,IAAI,CAACb,IAAI,CAACE,KAAK,CAAC1C,KAAK,CAAC;MACvE;MACA,OAAOuC,KAAK;IAChB,CAAC;IACD,IAAI,CAACe,aAAa,GAAG5E,QAAQ,IAAI;MAC7B,MAAMF,SAAS,GAAG,IAAI,CAACA,SAAS;QAAE+E,WAAW,GAAGjI,aAAa,CAAC,IAAI,CAAC+B,OAAO,CAACsB,MAAM,CAACqB,KAAK,CAAC;QAAEwD,IAAI,GAAG,CAAC;MAClG,IAAI,CAAC9E,QAAQ,GAAG,IAAI,CAACD,aAAa,CAACD,SAAS,EAAEE,QAAQ,EAAE1D,KAAK,CAACuI,WAAW,EAAEC,IAAI,EAAEhF,SAAS,CAACiF,OAAO,CAAC,CAAC;MACpG,IAAI,CAACC,eAAe,GAAG,IAAI,CAAChF,QAAQ,CAACoC,IAAI,CAAC,CAAC;MAC3C,MAAMzB,UAAU,GAAGb,SAAS,CAACc,MAAM,CAACC,IAAI;QAAEoE,aAAa,GAAG,CAAC;MAC3D,IAAI,CAACC,UAAU,GAAG;QACd,GAAGlI,WAAW,CAAC,IAAI,CAAC2B,OAAO,CAACuC,IAAI,CAACiE,MAAM,EAAExE,UAAU,CAAC;QACpDpB,MAAM,EAAE,IAAI,CAACZ,OAAO,CAACuC,IAAI,CAACiE,MAAM,CAAC5F,MAAM,IAAI0F,aAAa;QACxDG,IAAI,EAAE,IAAI,CAACzG,OAAO,CAACuC,IAAI,CAACiE,MAAM,CAACC,IAAI,IAAIvH,SAAS,CAACwH;MACrD,CAAC;MACD,IAAI,CAACnD,SAAS,GAAGxF,yBAAyB,CAAC,IAAI,CAACiC,OAAO,CAACuC,IAAI,CAACgB,SAAS,EAAE,IAAI,CAAClC,QAAQ,EAAE,IAAI,CAACkF,UAAU,CAAC;MACvG,QAAQ,IAAI,CAACvG,OAAO,CAACuC,IAAI,CAACgB,SAAS;QAC/B,KAAKxE,aAAa,CAAC4E,MAAM;UACrB,IAAI,CAACgD,OAAO,GAAG1H,eAAe,CAAC0E,MAAM;UACrC;QACJ,KAAK5E,aAAa,CAAC6E,OAAO;UACtB,IAAI,CAAC+C,OAAO,GAAG1H,eAAe,CAAC2E,OAAO;UACtC;MACR;MACA,IAAI,CAACI,MAAM,GAAGxG,MAAM,CAACoJ,MAAM;IAC/B,CAAC;IACD,IAAI,CAACC,OAAO,GAAG3F,MAAM;EACzB;EACA4F,OAAOA,CAACC,QAAQ,EAAE;IACd,IAAI,IAAI,CAACC,WAAW,IAAI,IAAI,CAACC,SAAS,EAAE;MACpC;IACJ;IACA,IAAI,CAACA,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,MAAM,CAACC,OAAO,GAAG,KAAK;IAC3B,IAAI,CAACC,IAAI,CAACD,OAAO,GAAG,KAAK;IACzB,MAAMhG,SAAS,GAAG,IAAI,CAACA,SAAS;MAAEkG,aAAa,GAAG,IAAI,CAACA,aAAa;MAAEC,WAAW,GAAGnG,SAAS,CAACoG,YAAY,CAACC,GAAG,CAAC,IAAI,CAACpH,KAAK,CAAC;IAC1HkH,WAAW,EAAEG,eAAe,GAAG,IAAI,CAAC;IACpC,KAAK,MAAM,GAAGjG,MAAM,CAAC,IAAIL,SAAS,CAACM,OAAO,EAAE;MACxCD,MAAM,CAACkG,iBAAiB,GAAG,IAAI,EAAEX,QAAQ,CAAC;IAC9C;IACA,KAAK,MAAMY,OAAO,IAAIxG,SAAS,CAAC2D,SAAS,CAAC8C,QAAQ,EAAE;MAChDD,OAAO,CAACD,iBAAiB,GAAG,IAAI,EAAEX,QAAQ,CAAC;IAC/C;IACAM,aAAa,EAAEQ,KAAK,CAAC,IAAI,CAAC;IAC1B,IAAI,CAAChB,OAAO,CAACiB,aAAa,CAACjJ,SAAS,CAAC6I,iBAAiB,EAAE;MACpDvG,SAAS,EAAE,IAAI,CAACA,SAAS;MACzBX,IAAI,EAAE;QACFwE,QAAQ,EAAE;MACd;IACJ,CAAC,CAAC;EACN;EACA+C,IAAIA,CAACC,KAAK,EAAE;IACR,MAAM7G,SAAS,GAAG,IAAI,CAACA,SAAS;MAAEc,MAAM,GAAGd,SAAS,CAACc,MAAM;IAC3D,KAAK,MAAM,GAAGT,MAAM,CAAC,IAAIL,SAAS,CAACM,OAAO,EAAE;MACxCQ,MAAM,CAACgG,kBAAkB,CAACzG,MAAM,EAAE,IAAI,EAAEwG,KAAK,CAAC;IAClD;IACA/F,MAAM,CAACiG,YAAY,CAAC,IAAI,EAAEF,KAAK,CAAC;EACpC;EACAG,YAAYA,CAAA,EAAG;IACX,OAAO,IAAI,CAAClD,aAAa,CAAC,IAAI,CAACiC,MAAM,CAAChC,KAAK,IAAIvG,mBAAmB,CAAC,IAAI,CAACuG,KAAK,CAAC,CAAC;EACnF;EACAkD,OAAOA,CAAA,EAAG;IACN,OAAO,IAAI,CAAC/F,SAAS,CAAC,CAAC,IAAI7C,SAAS,GAAGoG,IAAI,CAACC,EAAE,GAAGtG,IAAI;EACzD;EACAlB,WAAWA,CAAA,EAAG;IACV,OAAO;MACHyD,CAAC,EAAE,IAAI,CAACT,QAAQ,CAACS,CAAC,GAAG,IAAI,CAACkC,MAAM,CAAClC,CAAC;MAClCC,CAAC,EAAE,IAAI,CAACV,QAAQ,CAACU,CAAC,GAAG,IAAI,CAACiC,MAAM,CAACjC,CAAC;MAClCsG,CAAC,EAAE,IAAI,CAAChH,QAAQ,CAACgH;IACrB,CAAC;EACL;EACAhG,SAASA,CAAA,EAAG;IACR,OAAO,IAAI,CAAC6E,MAAM,CAACtG,MAAM,IAAI,IAAI,CAACsB,IAAI,CAACS,KAAK;EAChD;EACA2F,cAAcA,CAAA,EAAG;IACb,OAAO,IAAI,CAACrD,aAAa,CAAC,IAAI,CAACiC,MAAM,CAAChC,KAAK,IAAIvG,mBAAmB,CAAC,IAAI,CAAC4J,WAAW,CAAC,CAAC;EACzF;EACAC,IAAIA,CAAC3I,EAAE,EAAEwB,QAAQ,EAAEoH,eAAe,EAAEC,KAAK,EAAE;IACvC,MAAMvH,SAAS,GAAG,IAAI,CAACA,SAAS;MAAED,MAAM,GAAG,IAAI,CAAC2F,OAAO;IACvD,IAAI,CAAChH,EAAE,GAAGA,EAAE;IACZ,IAAI,CAAC6I,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,WAAW,GAAG,IAAI;IACvB,IAAI,CAACC,UAAU,GAAG,IAAI;IACtB,IAAI,CAACC,UAAU,GAAG,IAAI;IACtB,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,YAAY,GAAG,KAAK;IACzB,IAAI,CAACC,YAAY,GAAG,CAAC;IACrB,IAAI,CAAC/B,SAAS,GAAG,KAAK;IACtB,IAAI,CAACD,WAAW,GAAG,KAAK;IACxB,IAAI,CAACiC,UAAU,GAAG,KAAK;IACvB,IAAI,CAACC,QAAQ,GAAG,CAAC;IACjB,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,MAAM,GAAG;MACVC,WAAW,EAAE,CAAC;IAClB,CAAC;IACD,IAAI,CAAC1C,OAAO,GAAG1H,eAAe,CAACqK,MAAM;IACrC,IAAI,CAACC,kBAAkB,GAAG,IAAI;IAC9B,MAAMC,OAAO,GAAGrI,SAAS,CAACiI,MAAM,CAACK,UAAU;MAAEC,WAAW,GAAGvI,SAAS,CAACwI,aAAa;MAAEC,gBAAgB,GAAGxK,oBAAoB,CAAC,IAAI,CAACyH,OAAO,EAAE1F,SAAS,EAAEuI,WAAW,CAAC5E,SAAS,CAAC;MAAE;QAAEhF;MAAiB,CAAC,GAAG8J,gBAAgB;MAAEC,UAAU,GAAGD,gBAAgB,CAACjK,MAAM,CAACqG,IAAI;MAAE8D,SAAS,GAAGF,gBAAgB,CAACxJ,KAAK,CAAC4F,IAAI;IACxS,IAAI,CAACrG,MAAM,GAAGnB,wBAAwB,CAACqL,UAAU,EAAE,IAAI,CAAChK,EAAE,EAAEC,gBAAgB,CAAC;IAC7E,IAAI,CAACM,KAAK,GAAG5B,wBAAwB,CAACsL,SAAS,EAAE,IAAI,CAACjK,EAAE,EAAEC,gBAAgB,CAAC;IAC3E,MAAMF,aAAa,GAAGgK,gBAAgB,CAACjK,MAAM;MAAEU,YAAY,GAAGuJ,gBAAgB,CAACxJ,KAAK;IACpF,IAAIqI,eAAe,EAAE;MACjB,IAAIA,eAAe,CAAC9I,MAAM,EAAEqG,IAAI,EAAE;QAC9B,MAAM+D,kBAAkB,GAAGtB,eAAe,CAAC9I,MAAM,CAACqG,IAAI;UAAErG,MAAM,GAAGnB,wBAAwB,CAACuL,kBAAkB,EAAE,IAAI,CAAClK,EAAE,EAAEC,gBAAgB,CAAC;QACxI,IAAIH,MAAM,EAAE;UACR,IAAI,CAACA,MAAM,GAAGA,MAAM;UACpBC,aAAa,CAACoK,IAAI,CAACvB,eAAe,CAAC9I,MAAM,CAAC;QAC9C;MACJ;MACA,IAAI8I,eAAe,CAACrI,KAAK,EAAE4F,IAAI,EAAE;QAC7B,MAAMiE,iBAAiB,GAAGxB,eAAe,CAACrI,KAAK,CAAC4F,IAAI;UAAE5F,KAAK,GAAG5B,wBAAwB,CAACyL,iBAAiB,EAAE,IAAI,CAACpK,EAAE,EAAEC,gBAAgB,CAAC;QACpI,IAAIM,KAAK,EAAE;UACP,IAAI,CAACA,KAAK,GAAGA,KAAK;UAClBC,YAAY,CAAC2J,IAAI,CAACvB,eAAe,CAACrI,KAAK,CAAC;QAC5C;MACJ;IACJ;IACA,IAAI,IAAI,CAACT,MAAM,KAAKF,YAAY,EAAE;MAC9B,MAAMyK,gBAAgB,GAAG,CAAC,GAAG,IAAI,CAAC/I,SAAS,CAACgJ,aAAa,CAACC,IAAI,CAAC,CAAC,CAAC;MACjE,IAAI,CAACzK,MAAM,GAAGuK,gBAAgB,CAACtE,IAAI,CAACG,KAAK,CAACH,IAAI,CAACzB,MAAM,CAAC,CAAC,GAAG+F,gBAAgB,CAAC7F,MAAM,CAAC,CAAC;IACvF;IACA,IAAI,IAAI,CAACjE,KAAK,KAAKX,YAAY,EAAE;MAC7B,MAAM4K,eAAe,GAAG,CAAC,GAAG,IAAI,CAAClJ,SAAS,CAACoG,YAAY,CAAC6C,IAAI,CAAC,CAAC,CAAC;MAC/D,IAAI,CAAChK,KAAK,GAAGiK,eAAe,CAACzE,IAAI,CAACG,KAAK,CAACH,IAAI,CAACzB,MAAM,CAAC,CAAC,GAAGkG,eAAe,CAAChG,MAAM,CAAC,CAAC;IACpF;IACA,IAAI,CAACtE,UAAU,GAAGL,cAAc,CAAC,IAAI,CAACC,MAAM,EAAEC,aAAa,EAAE,IAAI,CAACC,EAAE,EAAEC,gBAAgB,CAAC;IACvF,IAAI,CAACQ,SAAS,GAAGH,aAAa,CAAC,IAAI,CAACC,KAAK,EAAEC,YAAY,EAAE,IAAI,CAACR,EAAE,EAAEC,gBAAgB,CAAC;IACnF8J,gBAAgB,CAACI,IAAI,CAACvB,eAAe,CAAC;IACtC,MAAM1I,UAAU,GAAG,IAAI,CAACA,UAAU;IAClC,IAAIA,UAAU,EAAE;MACZ6J,gBAAgB,CAACI,IAAI,CAACjK,UAAU,CAAC+E,SAAS,CAAC;IAC/C;IACA,MAAMxE,SAAS,GAAG,IAAI,CAACA,SAAS;IAChC,IAAIA,SAAS,EAAE;MACXsJ,gBAAgB,CAACI,IAAI,CAAC1J,SAAS,CAACwE,SAAS,CAAC;IAC9C;IACA,MAAMwF,aAAa,GAAG,IAAIxL,aAAa,CAACoC,MAAM,EAAEC,SAAS,CAAC;IAC1DmJ,aAAa,CAACN,IAAI,CAAC7I,SAAS,CAACwI,aAAa,CAACW,aAAa,CAAC;IACzDA,aAAa,CAACN,IAAI,CAACJ,gBAAgB,CAACU,aAAa,CAAC;IAClD,IAAI,CAACA,aAAa,GAAGA,aAAa;IAClC,IAAI,CAAC1B,UAAU,GAAG7I,UAAU,EAAEG,IAAI,IAAI0J,gBAAgB,CAACjK,MAAM,CAACO,IAAI;IAClE,IAAI,CAACyI,WAAW,GAAG5I,UAAU,EAAEE,KAAK,IAAI2J,gBAAgB,CAACjK,MAAM,CAACM,KAAK;IACrE,IAAI,CAAC6I,SAAS,GAAGxI,SAAS,EAAEJ,IAAI,IAAI0J,gBAAgB,CAACxJ,KAAK,CAACF,IAAI;IAC/D,IAAI,CAAC2I,UAAU,GAAGvI,SAAS,EAAEL,KAAK,IAAI2J,gBAAgB,CAACxJ,KAAK,CAACH,KAAK;IAClE,IAAI,CAACD,OAAO,GAAG4J,gBAAgB;IAC/B,MAAMW,WAAW,GAAG,IAAI,CAACvK,OAAO,CAACuC,IAAI,CAACiI,IAAI;IAC1C,IAAI,CAACC,SAAS,GAAGxM,aAAa,CAACsM,WAAW,CAACG,KAAK,CAAC/H,KAAK,CAAC,GAAGjE,qBAAqB;IAC/E,IAAI6L,WAAW,CAACI,SAAS,EAAE;MACvB,IAAI,CAACtD,aAAa,GAAG,IAAI,CAACR,OAAO,CAAC+D,gBAAgB,CAACL,WAAW,CAACI,SAAS,CAAC;MACzE,IAAI,IAAI,CAACtD,aAAa,IAAIlG,SAAS,CAAC0J,OAAO,CAACN,WAAW,CAACI,SAAS,EAAE,IAAI,CAACtD,aAAa,CAAC,EAAE;QACpF,IAAI,CAACA,aAAa,CAACmB,IAAI,CAACrH,SAAS,CAAC;MACtC;IACJ;IACAA,SAAS,CAACiI,MAAM,CAAC0B,YAAY,CAAC,IAAI,CAAC;IACnC,IAAI,CAAC5I,IAAI,GAAG5D,iCAAiC,CAAC,IAAI,CAAC0B,OAAO,CAACkC,IAAI,EAAEsH,OAAO,CAAC;IACzE,IAAI,CAACtC,MAAM,GAAG;MACVC,OAAO,EAAE;IACb,CAAC;IACD,IAAI,CAACC,IAAI,GAAG;MACRD,OAAO,EAAE,KAAK;MACd4D,MAAM,EAAE;IACZ,CAAC;IACD,IAAI,CAAC9E,aAAa,CAAC5E,QAAQ,CAAC;IAC5B,IAAI,CAAC2J,eAAe,GAAG,IAAI,CAAC3H,kBAAkB,CAAC,CAAC;IAChD,IAAI,CAAC4H,QAAQ,GAAG,IAAI,CAACD,eAAe,CAACvH,IAAI,CAAC,CAAC;IAC3C,MAAMyH,WAAW,GAAG,CAAC;IACrB,IAAI,CAACC,SAAS,GAAGD,WAAW,GAAGjN,aAAa,CAAC,IAAI,CAAC+B,OAAO,CAACuC,IAAI,CAAC6I,KAAK,CAAC;IACrE,MAAMtG,SAAS,GAAG3D,SAAS,CAAC2D,SAAS;IACrCA,SAAS,CAACuG,aAAa,CAAC,IAAI,CAAChK,QAAQ,CAACgH,CAAC,CAAC;IACxC,IAAI,CAACiD,YAAY,GAAG,IAAI,CAACjK,QAAQ,CAACgH,CAAC,GAAGlH,SAAS,CAACiF,OAAO;IACvD,IAAI,CAACmF,KAAK,GAAG,EAAE;IACf,IAAIC,YAAY,GAAGrK,SAAS,CAACgJ,aAAa,CAAC3C,GAAG,CAAC,IAAI,CAAC7H,MAAM,CAAC;IAC3D,IAAI,CAAC6L,YAAY,EAAE;MACfA,YAAY,GAAG,IAAI,CAAC3E,OAAO,CAAC4E,eAAe,CAAC,IAAI,CAAC9L,MAAM,CAAC;MACxD,IAAI6L,YAAY,EAAE;QACdrK,SAAS,CAACgJ,aAAa,CAACuB,GAAG,CAAC,IAAI,CAAC/L,MAAM,EAAE6L,YAAY,CAAC;MAC1D;IACJ;IACA,IAAIA,YAAY,EAAEG,UAAU,EAAE;MAC1BH,YAAY,CAACG,UAAU,CAAC,IAAI,CAAC;IACjC;IACA,IAAIrE,WAAW,GAAGnG,SAAS,CAACoG,YAAY,CAACC,GAAG,CAAC,IAAI,CAACpH,KAAK,CAAC;IACxD,IAAI,CAACkH,WAAW,EAAE;MACdA,WAAW,GAAG,IAAI,CAACT,OAAO,CAAC+E,cAAc,CAAC,IAAI,CAACxL,KAAK,CAAC;MACrD,IAAIkH,WAAW,EAAE;QACbnG,SAAS,CAACoG,YAAY,CAACmE,GAAG,CAAC,IAAI,CAACtL,KAAK,EAAEkH,WAAW,CAAC;MACvD;IACJ;IACA,IAAIA,WAAW,EAAEuE,SAAS,EAAE;MACxBvE,WAAW,CAACuE,SAAS,CAAC,IAAI,CAAC;IAC/B;IACA,MAAMC,aAAa,GAAGxE,WAAW,EAAEyE,aAAa;IAChD,IAAID,aAAa,EAAE;MACf,IAAI,CAACP,KAAK,GAAGO,aAAa,CAAC,IAAI,CAAC;IACpC;IACA,IAAI,CAACE,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,WAAW,GAAGrN,eAAe,CAAC,IAAI,CAACoB,OAAO,CAACkM,MAAM,CAAChH,KAAK,CAAC;IAC7D,KAAK,MAAMyC,OAAO,IAAI7C,SAAS,CAAC8C,QAAQ,EAAE;MACtCD,OAAO,CAACa,IAAI,CAAC,IAAI,CAAC;IACtB;IACA,KAAK,MAAM2D,KAAK,IAAIrH,SAAS,CAACsH,MAAM,EAAE;MAClCD,KAAK,CAAC3D,IAAI,GAAG,IAAI,CAAC;IACtB;IACAgD,YAAY,EAAEa,YAAY,GAAGlL,SAAS,EAAE,IAAI,CAAC;IAC7CmG,WAAW,EAAE+E,YAAY,GAAGlL,SAAS,EAAE,IAAI,CAAC;IAC5C,KAAK,MAAM,GAAGK,MAAM,CAAC,IAAIL,SAAS,CAACM,OAAO,EAAE;MACxCD,MAAM,CAAC8K,eAAe,GAAG,IAAI,CAAC;IAClC;EACJ;EACAC,cAAcA,CAAA,EAAG;IACb,MAAM3L,MAAM,GAAG,IAAI,CAACyB,SAAS,CAAC,CAAC;MAAEL,UAAU,GAAG,IAAI,CAACb,SAAS,CAACc,MAAM,CAACC,IAAI;MAAEb,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAClG,OAAQA,QAAQ,CAACS,CAAC,IAAI,CAAClB,MAAM,IACzBS,QAAQ,CAACU,CAAC,IAAI,CAACnB,MAAM,IACrBS,QAAQ,CAACU,CAAC,IAAIC,UAAU,CAACa,MAAM,GAAGjC,MAAM,IACxCS,QAAQ,CAACS,CAAC,IAAIE,UAAU,CAACU,KAAK,GAAG9B,MAAM;EAC/C;EACA4L,SAASA,CAAA,EAAG;IACR,OAAO,CAAC,IAAI,CAACvF,SAAS,IAAI,CAAC,IAAI,CAAC+E,QAAQ,IAAI,IAAI,CAACO,cAAc,CAAC,CAAC;EACrE;EACA1E,KAAKA,CAAA,EAAG;IACJ,KAAK,MAAMF,OAAO,IAAI,IAAI,CAACxG,SAAS,CAAC2D,SAAS,CAAC8C,QAAQ,EAAE;MACrDD,OAAO,CAACE,KAAK,GAAG,IAAI,CAAC;IACzB;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { AnimationOptions } from \"./AnimationOptions.js\";\nimport { setRangeValue } from \"../../Utils/NumberUtils.js\";\nexport class ColorAnimation extends AnimationOptions {\n  constructor() {\n    super();\n    this.offset = 0;\n    this.sync = true;\n  }\n  load(data) {\n    super.load(data);\n    if (!data) {\n      return;\n    }\n    if (data.offset !== undefined) {\n      this.offset = setRangeValue(data.offset);\n    }\n  }\n}", "map": {"version": 3, "names": ["AnimationOptions", "setRangeValue", "ColorAnimation", "constructor", "offset", "sync", "load", "data", "undefined"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/engine/browser/Options/Classes/ColorAnimation.js"], "sourcesContent": ["import { AnimationOptions } from \"./AnimationOptions.js\";\nimport { setRangeValue } from \"../../Utils/NumberUtils.js\";\nexport class ColorAnimation extends AnimationOptions {\n    constructor() {\n        super();\n        this.offset = 0;\n        this.sync = true;\n    }\n    load(data) {\n        super.load(data);\n        if (!data) {\n            return;\n        }\n        if (data.offset !== undefined) {\n            this.offset = setRangeValue(data.offset);\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,uBAAuB;AACxD,SAASC,aAAa,QAAQ,4BAA4B;AAC1D,OAAO,MAAMC,cAAc,SAASF,gBAAgB,CAAC;EACjDG,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,MAAM,GAAG,CAAC;IACf,IAAI,CAACC,IAAI,GAAG,IAAI;EACpB;EACAC,IAAIA,CAACC,IAAI,EAAE;IACP,KAAK,CAACD,IAAI,CAACC,IAAI,CAAC;IAChB,IAAI,CAACA,IAAI,EAAE;MACP;IACJ;IACA,IAAIA,IAAI,CAACH,MAAM,KAAKI,SAAS,EAAE;MAC3B,IAAI,CAACJ,MAAM,GAAGH,aAAa,CAACM,IAAI,CAACH,MAAM,CAAC;IAC5C;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
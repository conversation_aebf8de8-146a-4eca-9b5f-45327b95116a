{"ast": null, "code": "import { deepExtend } from \"../../../../Utils/Utils.js\";\nimport { setRangeValue } from \"../../../../Utils/NumberUtils.js\";\nexport class Spin {\n  constructor() {\n    this.acceleration = 0;\n    this.enable = false;\n  }\n  load(data) {\n    if (!data) {\n      return;\n    }\n    if (data.acceleration !== undefined) {\n      this.acceleration = setRangeValue(data.acceleration);\n    }\n    if (data.enable !== undefined) {\n      this.enable = data.enable;\n    }\n    if (data.position) {\n      this.position = deepExtend({}, data.position);\n    }\n  }\n}", "map": {"version": 3, "names": ["deepExtend", "setRangeValue", "Spin", "constructor", "acceleration", "enable", "load", "data", "undefined", "position"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/engine/browser/Options/Classes/Particles/Move/Spin.js"], "sourcesContent": ["import { deepExtend } from \"../../../../Utils/Utils.js\";\nimport { setRangeValue } from \"../../../../Utils/NumberUtils.js\";\nexport class Spin {\n    constructor() {\n        this.acceleration = 0;\n        this.enable = false;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.acceleration !== undefined) {\n            this.acceleration = setRangeValue(data.acceleration);\n        }\n        if (data.enable !== undefined) {\n            this.enable = data.enable;\n        }\n        if (data.position) {\n            this.position = deepExtend({}, data.position);\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,4BAA4B;AACvD,SAASC,aAAa,QAAQ,kCAAkC;AAChE,OAAO,MAAMC,IAAI,CAAC;EACdC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,YAAY,GAAG,CAAC;IACrB,IAAI,CAACC,MAAM,GAAG,KAAK;EACvB;EACAC,IAAIA,CAACC,IAAI,EAAE;IACP,IAAI,CAACA,IAAI,EAAE;MACP;IACJ;IACA,IAAIA,IAAI,CAACH,YAAY,KAAKI,SAAS,EAAE;MACjC,IAAI,CAACJ,YAAY,GAAGH,aAAa,CAACM,IAAI,CAACH,YAAY,CAAC;IACxD;IACA,IAAIG,IAAI,CAACF,MAAM,KAAKG,SAAS,EAAE;MAC3B,IAAI,CAACH,MAAM,GAAGE,IAAI,CAACF,MAAM;IAC7B;IACA,IAAIE,IAAI,CAACE,QAAQ,EAAE;MACf,IAAI,CAACA,QAAQ,GAAGT,UAAU,CAAC,CAAC,CAAC,EAAEO,IAAI,CAACE,QAAQ,CAAC;IACjD;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
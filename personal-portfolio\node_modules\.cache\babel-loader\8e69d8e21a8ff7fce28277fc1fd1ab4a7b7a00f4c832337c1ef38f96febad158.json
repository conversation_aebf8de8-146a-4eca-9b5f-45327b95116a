{"ast": null, "code": "import { arrayRandomIndex, executeOnSingleOr<PERSON>ultiple, isArray, isNumber, itemFromArray } from \"@tsparticles/engine\";\nimport { Emitter } from \"./Options/Classes/Emitter.js\";\nimport { EmitterClickMode } from \"./Enums/EmitterClickMode.js\";\nimport { EmitterInstance } from \"./EmitterInstance.js\";\nexport class Emitters {\n  constructor(engine, container) {\n    this.container = container;\n    this._engine = engine;\n    this.array = [];\n    this.emitters = [];\n    this.interactivityEmitters = {\n      random: {\n        count: 1,\n        enable: false\n      },\n      value: []\n    };\n    const defaultIndex = 0;\n    container.getEmitter = idxOrName => idxOrName === undefined || isNumber(idxOrName) ? this.array[idxOrName ?? defaultIndex] : this.array.find(t => t.name === idxOrName);\n    container.addEmitter = async (options, position) => this.addEmitter(options, position);\n    container.removeEmitter = idxOrName => {\n      const emitter = container.getEmitter(idxOrName);\n      if (emitter) {\n        this.removeEmitter(emitter);\n      }\n    };\n    container.playEmitter = idxOrName => {\n      const emitter = container.getEmitter(idxOrName);\n      if (emitter) {\n        emitter.externalPlay();\n      }\n    };\n    container.pauseEmitter = idxOrName => {\n      const emitter = container.getEmitter(idxOrName);\n      if (emitter) {\n        emitter.externalPause();\n      }\n    };\n  }\n  async addEmitter(options, position) {\n    const emitterOptions = new Emitter();\n    emitterOptions.load(options);\n    const emitter = new EmitterInstance(this._engine, this, this.container, emitterOptions, position);\n    await emitter.init();\n    this.array.push(emitter);\n    return emitter;\n  }\n  handleClickMode(mode) {\n    const emitterOptions = this.emitters,\n      modeEmitters = this.interactivityEmitters;\n    if (mode !== EmitterClickMode.emitter) {\n      return;\n    }\n    let emittersModeOptions;\n    if (modeEmitters && isArray(modeEmitters.value)) {\n      const minLength = 0;\n      if (modeEmitters.value.length > minLength && modeEmitters.random.enable) {\n        emittersModeOptions = [];\n        const usedIndexes = [];\n        for (let i = 0; i < modeEmitters.random.count; i++) {\n          const idx = arrayRandomIndex(modeEmitters.value);\n          if (usedIndexes.includes(idx) && usedIndexes.length < modeEmitters.value.length) {\n            i--;\n            continue;\n          }\n          usedIndexes.push(idx);\n          emittersModeOptions.push(itemFromArray(modeEmitters.value, idx));\n        }\n      } else {\n        emittersModeOptions = modeEmitters.value;\n      }\n    } else {\n      emittersModeOptions = modeEmitters?.value;\n    }\n    const emittersOptions = emittersModeOptions ?? emitterOptions,\n      ePosition = this.container.interactivity.mouse.clickPosition;\n    void executeOnSingleOrMultiple(emittersOptions, async emitter => {\n      await this.addEmitter(emitter, ePosition);\n    });\n  }\n  async init() {\n    this.emitters = this.container.actualOptions.emitters;\n    this.interactivityEmitters = this.container.actualOptions.interactivity.modes.emitters;\n    if (!this.emitters) {\n      return;\n    }\n    if (isArray(this.emitters)) {\n      for (const emitterOptions of this.emitters) {\n        await this.addEmitter(emitterOptions);\n      }\n    } else {\n      await this.addEmitter(this.emitters);\n    }\n  }\n  pause() {\n    for (const emitter of this.array) {\n      emitter.pause();\n    }\n  }\n  play() {\n    for (const emitter of this.array) {\n      emitter.play();\n    }\n  }\n  removeEmitter(emitter) {\n    const index = this.array.indexOf(emitter),\n      minIndex = 0,\n      deleteCount = 1;\n    if (index >= minIndex) {\n      this.array.splice(index, deleteCount);\n    }\n  }\n  resize() {\n    for (const emitter of this.array) {\n      emitter.resize();\n    }\n  }\n  stop() {\n    this.array = [];\n  }\n  update(delta) {\n    for (const emitter of this.array) {\n      emitter.update(delta);\n    }\n  }\n}", "map": {"version": 3, "names": ["arrayRandomIndex", "executeOnSingleOrMultiple", "isArray", "isNumber", "itemFromArray", "Emitter", "EmitterClickMode", "EmitterInstance", "Emitters", "constructor", "engine", "container", "_engine", "array", "emitters", "interactivityEmitters", "random", "count", "enable", "value", "defaultIndex", "getEmitter", "idxOrName", "undefined", "find", "t", "name", "addEmitter", "options", "position", "removeEmitter", "emitter", "playEmitter", "externalPlay", "pauseEmitter", "externalPause", "emitterOptions", "load", "init", "push", "handleClickMode", "mode", "modeEmitters", "emittersModeOptions", "<PERSON><PERSON><PERSON><PERSON>", "length", "usedIndexes", "i", "idx", "includes", "emittersOptions", "ePosition", "interactivity", "mouse", "clickPosition", "actualOptions", "modes", "pause", "play", "index", "indexOf", "minIndex", "deleteCount", "splice", "resize", "stop", "update", "delta"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/plugin-emitters/browser/Emitters.js"], "sourcesContent": ["import { arrayRandomIndex, executeOnSingleOrMultiple, isArray, isNumber, itemFromArray, } from \"@tsparticles/engine\";\nimport { Emitter } from \"./Options/Classes/Emitter.js\";\nimport { EmitterClickMode } from \"./Enums/EmitterClickMode.js\";\nimport { EmitterInstance } from \"./EmitterInstance.js\";\nexport class Emitters {\n    constructor(engine, container) {\n        this.container = container;\n        this._engine = engine;\n        this.array = [];\n        this.emitters = [];\n        this.interactivityEmitters = {\n            random: {\n                count: 1,\n                enable: false,\n            },\n            value: [],\n        };\n        const defaultIndex = 0;\n        container.getEmitter = (idxOrName) => idxOrName === undefined || isNumber(idxOrName)\n            ? this.array[idxOrName ?? defaultIndex]\n            : this.array.find(t => t.name === idxOrName);\n        container.addEmitter = async (options, position) => this.addEmitter(options, position);\n        container.removeEmitter = (idxOrName) => {\n            const emitter = container.getEmitter(idxOrName);\n            if (emitter) {\n                this.removeEmitter(emitter);\n            }\n        };\n        container.playEmitter = (idxOrName) => {\n            const emitter = container.getEmitter(idxOrName);\n            if (emitter) {\n                emitter.externalPlay();\n            }\n        };\n        container.pauseEmitter = (idxOrName) => {\n            const emitter = container.getEmitter(idxOrName);\n            if (emitter) {\n                emitter.externalPause();\n            }\n        };\n    }\n    async addEmitter(options, position) {\n        const emitterOptions = new Emitter();\n        emitterOptions.load(options);\n        const emitter = new EmitterInstance(this._engine, this, this.container, emitterOptions, position);\n        await emitter.init();\n        this.array.push(emitter);\n        return emitter;\n    }\n    handleClickMode(mode) {\n        const emitterOptions = this.emitters, modeEmitters = this.interactivityEmitters;\n        if (mode !== EmitterClickMode.emitter) {\n            return;\n        }\n        let emittersModeOptions;\n        if (modeEmitters && isArray(modeEmitters.value)) {\n            const minLength = 0;\n            if (modeEmitters.value.length > minLength && modeEmitters.random.enable) {\n                emittersModeOptions = [];\n                const usedIndexes = [];\n                for (let i = 0; i < modeEmitters.random.count; i++) {\n                    const idx = arrayRandomIndex(modeEmitters.value);\n                    if (usedIndexes.includes(idx) && usedIndexes.length < modeEmitters.value.length) {\n                        i--;\n                        continue;\n                    }\n                    usedIndexes.push(idx);\n                    emittersModeOptions.push(itemFromArray(modeEmitters.value, idx));\n                }\n            }\n            else {\n                emittersModeOptions = modeEmitters.value;\n            }\n        }\n        else {\n            emittersModeOptions = modeEmitters?.value;\n        }\n        const emittersOptions = emittersModeOptions ?? emitterOptions, ePosition = this.container.interactivity.mouse.clickPosition;\n        void executeOnSingleOrMultiple(emittersOptions, async (emitter) => {\n            await this.addEmitter(emitter, ePosition);\n        });\n    }\n    async init() {\n        this.emitters = this.container.actualOptions.emitters;\n        this.interactivityEmitters = this.container.actualOptions.interactivity.modes.emitters;\n        if (!this.emitters) {\n            return;\n        }\n        if (isArray(this.emitters)) {\n            for (const emitterOptions of this.emitters) {\n                await this.addEmitter(emitterOptions);\n            }\n        }\n        else {\n            await this.addEmitter(this.emitters);\n        }\n    }\n    pause() {\n        for (const emitter of this.array) {\n            emitter.pause();\n        }\n    }\n    play() {\n        for (const emitter of this.array) {\n            emitter.play();\n        }\n    }\n    removeEmitter(emitter) {\n        const index = this.array.indexOf(emitter), minIndex = 0, deleteCount = 1;\n        if (index >= minIndex) {\n            this.array.splice(index, deleteCount);\n        }\n    }\n    resize() {\n        for (const emitter of this.array) {\n            emitter.resize();\n        }\n    }\n    stop() {\n        this.array = [];\n    }\n    update(delta) {\n        for (const emitter of this.array) {\n            emitter.update(delta);\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,gBAAgB,EAAEC,yBAAyB,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,aAAa,QAAS,qBAAqB;AACpH,SAASC,OAAO,QAAQ,8BAA8B;AACtD,SAASC,gBAAgB,QAAQ,6BAA6B;AAC9D,SAASC,eAAe,QAAQ,sBAAsB;AACtD,OAAO,MAAMC,QAAQ,CAAC;EAClBC,WAAWA,CAACC,MAAM,EAAEC,SAAS,EAAE;IAC3B,IAAI,CAACA,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,OAAO,GAAGF,MAAM;IACrB,IAAI,CAACG,KAAK,GAAG,EAAE;IACf,IAAI,CAACC,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACC,qBAAqB,GAAG;MACzBC,MAAM,EAAE;QACJC,KAAK,EAAE,CAAC;QACRC,MAAM,EAAE;MACZ,CAAC;MACDC,KAAK,EAAE;IACX,CAAC;IACD,MAAMC,YAAY,GAAG,CAAC;IACtBT,SAAS,CAACU,UAAU,GAAIC,SAAS,IAAKA,SAAS,KAAKC,SAAS,IAAIpB,QAAQ,CAACmB,SAAS,CAAC,GAC9E,IAAI,CAACT,KAAK,CAACS,SAAS,IAAIF,YAAY,CAAC,GACrC,IAAI,CAACP,KAAK,CAACW,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,KAAKJ,SAAS,CAAC;IAChDX,SAAS,CAACgB,UAAU,GAAG,OAAOC,OAAO,EAAEC,QAAQ,KAAK,IAAI,CAACF,UAAU,CAACC,OAAO,EAAEC,QAAQ,CAAC;IACtFlB,SAAS,CAACmB,aAAa,GAAIR,SAAS,IAAK;MACrC,MAAMS,OAAO,GAAGpB,SAAS,CAACU,UAAU,CAACC,SAAS,CAAC;MAC/C,IAAIS,OAAO,EAAE;QACT,IAAI,CAACD,aAAa,CAACC,OAAO,CAAC;MAC/B;IACJ,CAAC;IACDpB,SAAS,CAACqB,WAAW,GAAIV,SAAS,IAAK;MACnC,MAAMS,OAAO,GAAGpB,SAAS,CAACU,UAAU,CAACC,SAAS,CAAC;MAC/C,IAAIS,OAAO,EAAE;QACTA,OAAO,CAACE,YAAY,CAAC,CAAC;MAC1B;IACJ,CAAC;IACDtB,SAAS,CAACuB,YAAY,GAAIZ,SAAS,IAAK;MACpC,MAAMS,OAAO,GAAGpB,SAAS,CAACU,UAAU,CAACC,SAAS,CAAC;MAC/C,IAAIS,OAAO,EAAE;QACTA,OAAO,CAACI,aAAa,CAAC,CAAC;MAC3B;IACJ,CAAC;EACL;EACA,MAAMR,UAAUA,CAACC,OAAO,EAAEC,QAAQ,EAAE;IAChC,MAAMO,cAAc,GAAG,IAAI/B,OAAO,CAAC,CAAC;IACpC+B,cAAc,CAACC,IAAI,CAACT,OAAO,CAAC;IAC5B,MAAMG,OAAO,GAAG,IAAIxB,eAAe,CAAC,IAAI,CAACK,OAAO,EAAE,IAAI,EAAE,IAAI,CAACD,SAAS,EAAEyB,cAAc,EAAEP,QAAQ,CAAC;IACjG,MAAME,OAAO,CAACO,IAAI,CAAC,CAAC;IACpB,IAAI,CAACzB,KAAK,CAAC0B,IAAI,CAACR,OAAO,CAAC;IACxB,OAAOA,OAAO;EAClB;EACAS,eAAeA,CAACC,IAAI,EAAE;IAClB,MAAML,cAAc,GAAG,IAAI,CAACtB,QAAQ;MAAE4B,YAAY,GAAG,IAAI,CAAC3B,qBAAqB;IAC/E,IAAI0B,IAAI,KAAKnC,gBAAgB,CAACyB,OAAO,EAAE;MACnC;IACJ;IACA,IAAIY,mBAAmB;IACvB,IAAID,YAAY,IAAIxC,OAAO,CAACwC,YAAY,CAACvB,KAAK,CAAC,EAAE;MAC7C,MAAMyB,SAAS,GAAG,CAAC;MACnB,IAAIF,YAAY,CAACvB,KAAK,CAAC0B,MAAM,GAAGD,SAAS,IAAIF,YAAY,CAAC1B,MAAM,CAACE,MAAM,EAAE;QACrEyB,mBAAmB,GAAG,EAAE;QACxB,MAAMG,WAAW,GAAG,EAAE;QACtB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,YAAY,CAAC1B,MAAM,CAACC,KAAK,EAAE8B,CAAC,EAAE,EAAE;UAChD,MAAMC,GAAG,GAAGhD,gBAAgB,CAAC0C,YAAY,CAACvB,KAAK,CAAC;UAChD,IAAI2B,WAAW,CAACG,QAAQ,CAACD,GAAG,CAAC,IAAIF,WAAW,CAACD,MAAM,GAAGH,YAAY,CAACvB,KAAK,CAAC0B,MAAM,EAAE;YAC7EE,CAAC,EAAE;YACH;UACJ;UACAD,WAAW,CAACP,IAAI,CAACS,GAAG,CAAC;UACrBL,mBAAmB,CAACJ,IAAI,CAACnC,aAAa,CAACsC,YAAY,CAACvB,KAAK,EAAE6B,GAAG,CAAC,CAAC;QACpE;MACJ,CAAC,MACI;QACDL,mBAAmB,GAAGD,YAAY,CAACvB,KAAK;MAC5C;IACJ,CAAC,MACI;MACDwB,mBAAmB,GAAGD,YAAY,EAAEvB,KAAK;IAC7C;IACA,MAAM+B,eAAe,GAAGP,mBAAmB,IAAIP,cAAc;MAAEe,SAAS,GAAG,IAAI,CAACxC,SAAS,CAACyC,aAAa,CAACC,KAAK,CAACC,aAAa;IAC3H,KAAKrD,yBAAyB,CAACiD,eAAe,EAAE,MAAOnB,OAAO,IAAK;MAC/D,MAAM,IAAI,CAACJ,UAAU,CAACI,OAAO,EAAEoB,SAAS,CAAC;IAC7C,CAAC,CAAC;EACN;EACA,MAAMb,IAAIA,CAAA,EAAG;IACT,IAAI,CAACxB,QAAQ,GAAG,IAAI,CAACH,SAAS,CAAC4C,aAAa,CAACzC,QAAQ;IACrD,IAAI,CAACC,qBAAqB,GAAG,IAAI,CAACJ,SAAS,CAAC4C,aAAa,CAACH,aAAa,CAACI,KAAK,CAAC1C,QAAQ;IACtF,IAAI,CAAC,IAAI,CAACA,QAAQ,EAAE;MAChB;IACJ;IACA,IAAIZ,OAAO,CAAC,IAAI,CAACY,QAAQ,CAAC,EAAE;MACxB,KAAK,MAAMsB,cAAc,IAAI,IAAI,CAACtB,QAAQ,EAAE;QACxC,MAAM,IAAI,CAACa,UAAU,CAACS,cAAc,CAAC;MACzC;IACJ,CAAC,MACI;MACD,MAAM,IAAI,CAACT,UAAU,CAAC,IAAI,CAACb,QAAQ,CAAC;IACxC;EACJ;EACA2C,KAAKA,CAAA,EAAG;IACJ,KAAK,MAAM1B,OAAO,IAAI,IAAI,CAAClB,KAAK,EAAE;MAC9BkB,OAAO,CAAC0B,KAAK,CAAC,CAAC;IACnB;EACJ;EACAC,IAAIA,CAAA,EAAG;IACH,KAAK,MAAM3B,OAAO,IAAI,IAAI,CAAClB,KAAK,EAAE;MAC9BkB,OAAO,CAAC2B,IAAI,CAAC,CAAC;IAClB;EACJ;EACA5B,aAAaA,CAACC,OAAO,EAAE;IACnB,MAAM4B,KAAK,GAAG,IAAI,CAAC9C,KAAK,CAAC+C,OAAO,CAAC7B,OAAO,CAAC;MAAE8B,QAAQ,GAAG,CAAC;MAAEC,WAAW,GAAG,CAAC;IACxE,IAAIH,KAAK,IAAIE,QAAQ,EAAE;MACnB,IAAI,CAAChD,KAAK,CAACkD,MAAM,CAACJ,KAAK,EAAEG,WAAW,CAAC;IACzC;EACJ;EACAE,MAAMA,CAAA,EAAG;IACL,KAAK,MAAMjC,OAAO,IAAI,IAAI,CAAClB,KAAK,EAAE;MAC9BkB,OAAO,CAACiC,MAAM,CAAC,CAAC;IACpB;EACJ;EACAC,IAAIA,CAAA,EAAG;IACH,IAAI,CAACpD,KAAK,GAAG,EAAE;EACnB;EACAqD,MAAMA,CAACC,KAAK,EAAE;IACV,KAAK,MAAMpC,OAAO,IAAI,IAAI,CAAClB,KAAK,EAAE;MAC9BkB,OAAO,CAACmC,MAAM,CAACC,KAAK,CAAC;IACzB;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
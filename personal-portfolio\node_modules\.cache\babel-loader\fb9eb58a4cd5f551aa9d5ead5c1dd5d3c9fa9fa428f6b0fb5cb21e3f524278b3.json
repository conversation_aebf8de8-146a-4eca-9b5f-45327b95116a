{"ast": null, "code": "import { clamp, getRandom, getRangeMax, getRangeMin, getRangeValue, mix, randomInRange, setRangeValue } from \"./NumberUtils.js\";\nimport { isArray, isString } from \"./TypeUtils.js\";\nimport { millisecondsToSeconds, percentDenominator } from \"../Core/Utils/Constants.js\";\nimport { AnimationStatus } from \"../Enums/AnimationStatus.js\";\nimport { itemFromArray } from \"./Utils.js\";\nvar RgbIndexes;\n(function (RgbIndexes) {\n  RgbIndexes[RgbIndexes[\"r\"] = 1] = \"r\";\n  RgbIndexes[RgbIndexes[\"g\"] = 2] = \"g\";\n  RgbIndexes[RgbIndexes[\"b\"] = 3] = \"b\";\n  RgbIndexes[RgbIndexes[\"a\"] = 4] = \"a\";\n})(RgbIndexes || (RgbIndexes = {}));\nconst randomColorValue = \"random\",\n  midColorValue = \"mid\",\n  colorManagers = new Map();\nexport function addColorManager(manager) {\n  colorManagers.set(manager.key, manager);\n}\nfunction stringToRgba(input) {\n  for (const [, manager] of colorManagers) {\n    if (input.startsWith(manager.stringPrefix)) {\n      return manager.parseString(input);\n    }\n  }\n  const shorthandRegex = /^#?([a-f\\d])([a-f\\d])([a-f\\d])([a-f\\d])?$/i,\n    hexFixed = input.replace(shorthandRegex, (_, r, g, b, a) => {\n      return r + r + g + g + b + b + (a !== undefined ? a + a : \"\");\n    }),\n    regex = /^#?([a-f\\d]{2})([a-f\\d]{2})([a-f\\d]{2})([a-f\\d]{2})?$/i,\n    result = regex.exec(hexFixed),\n    radix = 16,\n    defaultAlpha = 1,\n    alphaFactor = 0xff;\n  return result ? {\n    a: result[RgbIndexes.a] !== undefined ? parseInt(result[RgbIndexes.a], radix) / alphaFactor : defaultAlpha,\n    b: parseInt(result[RgbIndexes.b], radix),\n    g: parseInt(result[RgbIndexes.g], radix),\n    r: parseInt(result[RgbIndexes.r], radix)\n  } : undefined;\n}\nexport function rangeColorToRgb(input, index, useIndex = true) {\n  if (!input) {\n    return;\n  }\n  const color = isString(input) ? {\n    value: input\n  } : input;\n  if (isString(color.value)) {\n    return colorToRgb(color.value, index, useIndex);\n  }\n  if (isArray(color.value)) {\n    return rangeColorToRgb({\n      value: itemFromArray(color.value, index, useIndex)\n    });\n  }\n  for (const [, manager] of colorManagers) {\n    const res = manager.handleRangeColor(color);\n    if (res) {\n      return res;\n    }\n  }\n}\nexport function colorToRgb(input, index, useIndex = true) {\n  if (!input) {\n    return;\n  }\n  const color = isString(input) ? {\n    value: input\n  } : input;\n  if (isString(color.value)) {\n    return color.value === randomColorValue ? getRandomRgbColor() : stringToRgb(color.value);\n  }\n  if (isArray(color.value)) {\n    return colorToRgb({\n      value: itemFromArray(color.value, index, useIndex)\n    });\n  }\n  for (const [, manager] of colorManagers) {\n    const res = manager.handleColor(color);\n    if (res) {\n      return res;\n    }\n  }\n}\nexport function colorToHsl(color, index, useIndex = true) {\n  const rgb = colorToRgb(color, index, useIndex);\n  return rgb ? rgbToHsl(rgb) : undefined;\n}\nexport function rangeColorToHsl(color, index, useIndex = true) {\n  const rgb = rangeColorToRgb(color, index, useIndex);\n  return rgb ? rgbToHsl(rgb) : undefined;\n}\nexport function rgbToHsl(color) {\n  const rgbMax = 255,\n    hMax = 360,\n    sMax = 100,\n    lMax = 100,\n    hMin = 0,\n    sMin = 0,\n    hPhase = 60,\n    half = 0.5,\n    double = 2,\n    r1 = color.r / rgbMax,\n    g1 = color.g / rgbMax,\n    b1 = color.b / rgbMax,\n    max = Math.max(r1, g1, b1),\n    min = Math.min(r1, g1, b1),\n    res = {\n      h: hMin,\n      l: (max + min) * half,\n      s: sMin\n    };\n  if (max !== min) {\n    res.s = res.l < half ? (max - min) / (max + min) : (max - min) / (double - max - min);\n    res.h = r1 === max ? (g1 - b1) / (max - min) : res.h = g1 === max ? double + (b1 - r1) / (max - min) : double * double + (r1 - g1) / (max - min);\n  }\n  res.l *= lMax;\n  res.s *= sMax;\n  res.h *= hPhase;\n  if (res.h < hMin) {\n    res.h += hMax;\n  }\n  if (res.h >= hMax) {\n    res.h -= hMax;\n  }\n  return res;\n}\nexport function stringToAlpha(input) {\n  return stringToRgba(input)?.a;\n}\nexport function stringToRgb(input) {\n  return stringToRgba(input);\n}\nexport function hslToRgb(hsl) {\n  const hMax = 360,\n    sMax = 100,\n    lMax = 100,\n    sMin = 0,\n    lMin = 0,\n    h = (hsl.h % hMax + hMax) % hMax,\n    s = Math.max(sMin, Math.min(sMax, hsl.s)),\n    l = Math.max(lMin, Math.min(lMax, hsl.l)),\n    hNormalized = h / hMax,\n    sNormalized = s / sMax,\n    lNormalized = l / lMax,\n    rgbFactor = 255,\n    triple = 3;\n  if (s === sMin) {\n    const grayscaleValue = Math.round(lNormalized * rgbFactor);\n    return {\n      r: grayscaleValue,\n      g: grayscaleValue,\n      b: grayscaleValue\n    };\n  }\n  const half = 0.5,\n    double = 2,\n    channel = (temp1, temp2, temp3) => {\n      const temp3Min = 0,\n        temp3Max = 1,\n        sextuple = 6;\n      if (temp3 < temp3Min) {\n        temp3++;\n      }\n      if (temp3 > temp3Max) {\n        temp3--;\n      }\n      if (temp3 * sextuple < temp3Max) {\n        return temp1 + (temp2 - temp1) * sextuple * temp3;\n      }\n      if (temp3 * double < temp3Max) {\n        return temp2;\n      }\n      if (temp3 * triple < temp3Max * double) {\n        const temp3Offset = double / triple;\n        return temp1 + (temp2 - temp1) * (temp3Offset - temp3) * sextuple;\n      }\n      return temp1;\n    },\n    sNormalizedOffset = 1,\n    temp1 = lNormalized < half ? lNormalized * (sNormalizedOffset + sNormalized) : lNormalized + sNormalized - lNormalized * sNormalized,\n    temp2 = double * lNormalized - temp1,\n    phaseNumerator = 1,\n    phaseThird = phaseNumerator / triple,\n    red = Math.min(rgbFactor, rgbFactor * channel(temp2, temp1, hNormalized + phaseThird)),\n    green = Math.min(rgbFactor, rgbFactor * channel(temp2, temp1, hNormalized)),\n    blue = Math.min(rgbFactor, rgbFactor * channel(temp2, temp1, hNormalized - phaseThird));\n  return {\n    r: Math.round(red),\n    g: Math.round(green),\n    b: Math.round(blue)\n  };\n}\nexport function hslaToRgba(hsla) {\n  const rgbResult = hslToRgb(hsla);\n  return {\n    a: hsla.a,\n    b: rgbResult.b,\n    g: rgbResult.g,\n    r: rgbResult.r\n  };\n}\nexport function getRandomRgbColor(min) {\n  const defaultMin = 0,\n    fixedMin = min ?? defaultMin,\n    rgbMax = 256;\n  return {\n    b: Math.floor(randomInRange(setRangeValue(fixedMin, rgbMax))),\n    g: Math.floor(randomInRange(setRangeValue(fixedMin, rgbMax))),\n    r: Math.floor(randomInRange(setRangeValue(fixedMin, rgbMax)))\n  };\n}\nexport function getStyleFromRgb(color, opacity) {\n  const defaultOpacity = 1;\n  return `rgba(${color.r}, ${color.g}, ${color.b}, ${opacity ?? defaultOpacity})`;\n}\nexport function getStyleFromHsl(color, opacity) {\n  const defaultOpacity = 1;\n  return `hsla(${color.h}, ${color.s}%, ${color.l}%, ${opacity ?? defaultOpacity})`;\n}\nexport function colorMix(color1, color2, size1, size2) {\n  let rgb1 = color1,\n    rgb2 = color2;\n  if (rgb1.r === undefined) {\n    rgb1 = hslToRgb(color1);\n  }\n  if (rgb2.r === undefined) {\n    rgb2 = hslToRgb(color2);\n  }\n  return {\n    b: mix(rgb1.b, rgb2.b, size1, size2),\n    g: mix(rgb1.g, rgb2.g, size1, size2),\n    r: mix(rgb1.r, rgb2.r, size1, size2)\n  };\n}\nexport function getLinkColor(p1, p2, linkColor) {\n  if (linkColor === randomColorValue) {\n    return getRandomRgbColor();\n  } else if (linkColor === midColorValue) {\n    const sourceColor = p1.getFillColor() ?? p1.getStrokeColor(),\n      destColor = p2?.getFillColor() ?? p2?.getStrokeColor();\n    if (sourceColor && destColor && p2) {\n      return colorMix(sourceColor, destColor, p1.getRadius(), p2.getRadius());\n    } else {\n      const hslColor = sourceColor ?? destColor;\n      if (hslColor) {\n        return hslToRgb(hslColor);\n      }\n    }\n  } else {\n    return linkColor;\n  }\n}\nexport function getLinkRandomColor(optColor, blink, consent) {\n  const color = isString(optColor) ? optColor : optColor.value;\n  if (color === randomColorValue) {\n    if (consent) {\n      return rangeColorToRgb({\n        value: color\n      });\n    }\n    if (blink) {\n      return randomColorValue;\n    }\n    return midColorValue;\n  } else if (color === midColorValue) {\n    return midColorValue;\n  } else {\n    return rangeColorToRgb({\n      value: color\n    });\n  }\n}\nexport function getHslFromAnimation(animation) {\n  return animation !== undefined ? {\n    h: animation.h.value,\n    s: animation.s.value,\n    l: animation.l.value\n  } : undefined;\n}\nexport function getHslAnimationFromHsl(hsl, animationOptions, reduceFactor) {\n  const resColor = {\n    h: {\n      enable: false,\n      value: hsl.h\n    },\n    s: {\n      enable: false,\n      value: hsl.s\n    },\n    l: {\n      enable: false,\n      value: hsl.l\n    }\n  };\n  if (animationOptions) {\n    setColorAnimation(resColor.h, animationOptions.h, reduceFactor);\n    setColorAnimation(resColor.s, animationOptions.s, reduceFactor);\n    setColorAnimation(resColor.l, animationOptions.l, reduceFactor);\n  }\n  return resColor;\n}\nfunction setColorAnimation(colorValue, colorAnimation, reduceFactor) {\n  colorValue.enable = colorAnimation.enable;\n  const defaultVelocity = 0,\n    decayOffset = 1,\n    defaultLoops = 0,\n    defaultTime = 0;\n  if (colorValue.enable) {\n    colorValue.velocity = getRangeValue(colorAnimation.speed) / percentDenominator * reduceFactor;\n    colorValue.decay = decayOffset - getRangeValue(colorAnimation.decay);\n    colorValue.status = AnimationStatus.increasing;\n    colorValue.loops = defaultLoops;\n    colorValue.maxLoops = getRangeValue(colorAnimation.count);\n    colorValue.time = defaultTime;\n    colorValue.delayTime = getRangeValue(colorAnimation.delay) * millisecondsToSeconds;\n    if (!colorAnimation.sync) {\n      colorValue.velocity *= getRandom();\n      colorValue.value *= getRandom();\n    }\n    colorValue.initialValue = colorValue.value;\n    colorValue.offset = setRangeValue(colorAnimation.offset);\n  } else {\n    colorValue.velocity = defaultVelocity;\n  }\n}\nexport function updateColorValue(data, range, decrease, delta) {\n  const minLoops = 0,\n    minDelay = 0,\n    identity = 1,\n    minVelocity = 0,\n    minOffset = 0,\n    velocityFactor = 3.6;\n  if (!data || !data.enable || (data.maxLoops ?? minLoops) > minLoops && (data.loops ?? minLoops) > (data.maxLoops ?? minLoops)) {\n    return;\n  }\n  if (!data.time) {\n    data.time = 0;\n  }\n  if ((data.delayTime ?? minDelay) > minDelay && data.time < (data.delayTime ?? minDelay)) {\n    data.time += delta.value;\n  }\n  if ((data.delayTime ?? minDelay) > minDelay && data.time < (data.delayTime ?? minDelay)) {\n    return;\n  }\n  const offset = data.offset ? randomInRange(data.offset) : minOffset,\n    velocity = (data.velocity ?? minVelocity) * delta.factor + offset * velocityFactor,\n    decay = data.decay ?? identity,\n    max = getRangeMax(range),\n    min = getRangeMin(range);\n  if (!decrease || data.status === AnimationStatus.increasing) {\n    data.value += velocity;\n    if (data.value > max) {\n      if (!data.loops) {\n        data.loops = 0;\n      }\n      data.loops++;\n      if (decrease) {\n        data.status = AnimationStatus.decreasing;\n      } else {\n        data.value -= max;\n      }\n    }\n  } else {\n    data.value -= velocity;\n    const minValue = 0;\n    if (data.value < minValue) {\n      if (!data.loops) {\n        data.loops = 0;\n      }\n      data.loops++;\n      data.status = AnimationStatus.increasing;\n    }\n  }\n  if (data.velocity && decay !== identity) {\n    data.velocity *= decay;\n  }\n  data.value = clamp(data.value, min, max);\n}\nexport function updateColor(color, delta) {\n  if (!color) {\n    return;\n  }\n  const {\n    h,\n    s,\n    l\n  } = color;\n  const ranges = {\n    h: {\n      min: 0,\n      max: 360\n    },\n    s: {\n      min: 0,\n      max: 100\n    },\n    l: {\n      min: 0,\n      max: 100\n    }\n  };\n  if (h) {\n    updateColorValue(h, ranges.h, false, delta);\n  }\n  if (s) {\n    updateColorValue(s, ranges.s, true, delta);\n  }\n  if (l) {\n    updateColorValue(l, ranges.l, true, delta);\n  }\n}", "map": {"version": 3, "names": ["clamp", "getRandom", "getRangeMax", "getRangeMin", "getRangeValue", "mix", "randomInRange", "setRangeValue", "isArray", "isString", "millisecondsToSeconds", "percentDenominator", "AnimationStatus", "itemFromArray", "RgbIndexes", "randomColorValue", "midColorValue", "colorManagers", "Map", "addColorManager", "manager", "set", "key", "stringToRgba", "input", "startsWith", "stringPrefix", "parseString", "shorthandRegex", "hexFixed", "replace", "_", "r", "g", "b", "a", "undefined", "regex", "result", "exec", "radix", "defaultAlpha", "alphaFactor", "parseInt", "rangeColorToRgb", "index", "useIndex", "color", "value", "colorToRgb", "res", "handleRangeColor", "getRandomRgbColor", "stringToRgb", "handleColor", "colorToHsl", "rgb", "rgbToHsl", "rangeColorToHsl", "rgbMax", "hMax", "sMax", "lMax", "hMin", "sMin", "hPhase", "half", "double", "r1", "g1", "b1", "max", "Math", "min", "h", "l", "s", "stringToAlpha", "hslToRgb", "hsl", "lMin", "hNormalized", "sNormalized", "lNormalized", "rgbFactor", "triple", "grayscaleValue", "round", "channel", "temp1", "temp2", "temp3", "temp3Min", "temp3Max", "sextuple", "temp3Offset", "sNormalizedOffset", "phaseNumerator", "phaseThird", "red", "green", "blue", "hslaToRgba", "hsla", "rgbResult", "defaultMin", "fixedMin", "floor", "getStyleFromRgb", "opacity", "defaultOpacity", "getStyleFromHsl", "colorMix", "color1", "color2", "size1", "size2", "rgb1", "rgb2", "getLinkColor", "p1", "p2", "linkColor", "sourceColor", "getFillColor", "getStrokeColor", "destColor", "getRadius", "hslColor", "getLinkRandomColor", "optColor", "blink", "consent", "getHslFromAnimation", "animation", "getHslAnimationFromHsl", "animationOptions", "reduceFactor", "resColor", "enable", "setColorAnimation", "colorValue", "colorAnimation", "defaultVelocity", "decayOffset", "defaultLoops", "defaultTime", "velocity", "speed", "decay", "status", "increasing", "loops", "max<PERSON><PERSON>s", "count", "time", "delayTime", "delay", "sync", "initialValue", "offset", "updateColorValue", "data", "range", "decrease", "delta", "minLoops", "min<PERSON>elay", "identity", "minVelocity", "minOffset", "velocityFactor", "factor", "decreasing", "minValue", "updateColor", "ranges"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/engine/browser/Utils/ColorUtils.js"], "sourcesContent": ["import { clamp, getRandom, getRangeMax, getRangeMin, getRangeValue, mix, randomInRange, setRangeValue, } from \"./NumberUtils.js\";\nimport { isArray, isString } from \"./TypeUtils.js\";\nimport { millisecondsToSeconds, percentDenominator } from \"../Core/Utils/Constants.js\";\nimport { AnimationStatus } from \"../Enums/AnimationStatus.js\";\nimport { itemFromArray } from \"./Utils.js\";\nvar RgbIndexes;\n(function (RgbIndexes) {\n    RgbIndexes[RgbIndexes[\"r\"] = 1] = \"r\";\n    RgbIndexes[RgbIndexes[\"g\"] = 2] = \"g\";\n    RgbIndexes[RgbIndexes[\"b\"] = 3] = \"b\";\n    RgbIndexes[RgbIndexes[\"a\"] = 4] = \"a\";\n})(RgbIndexes || (RgbIndexes = {}));\nconst randomColorValue = \"random\", midColorValue = \"mid\", colorManagers = new Map();\nexport function addColorManager(manager) {\n    colorManagers.set(manager.key, manager);\n}\nfunction stringToRgba(input) {\n    for (const [, manager] of colorManagers) {\n        if (input.startsWith(manager.stringPrefix)) {\n            return manager.parseString(input);\n        }\n    }\n    const shorthandRegex = /^#?([a-f\\d])([a-f\\d])([a-f\\d])([a-f\\d])?$/i, hexFixed = input.replace(shorthandRegex, (_, r, g, b, a) => {\n        return r + r + g + g + b + b + (a !== undefined ? a + a : \"\");\n    }), regex = /^#?([a-f\\d]{2})([a-f\\d]{2})([a-f\\d]{2})([a-f\\d]{2})?$/i, result = regex.exec(hexFixed), radix = 16, defaultAlpha = 1, alphaFactor = 0xff;\n    return result\n        ? {\n            a: result[RgbIndexes.a] !== undefined\n                ? parseInt(result[RgbIndexes.a], radix) / alphaFactor\n                : defaultAlpha,\n            b: parseInt(result[RgbIndexes.b], radix),\n            g: parseInt(result[RgbIndexes.g], radix),\n            r: parseInt(result[RgbIndexes.r], radix),\n        }\n        : undefined;\n}\nexport function rangeColorToRgb(input, index, useIndex = true) {\n    if (!input) {\n        return;\n    }\n    const color = isString(input) ? { value: input } : input;\n    if (isString(color.value)) {\n        return colorToRgb(color.value, index, useIndex);\n    }\n    if (isArray(color.value)) {\n        return rangeColorToRgb({\n            value: itemFromArray(color.value, index, useIndex),\n        });\n    }\n    for (const [, manager] of colorManagers) {\n        const res = manager.handleRangeColor(color);\n        if (res) {\n            return res;\n        }\n    }\n}\nexport function colorToRgb(input, index, useIndex = true) {\n    if (!input) {\n        return;\n    }\n    const color = isString(input) ? { value: input } : input;\n    if (isString(color.value)) {\n        return color.value === randomColorValue ? getRandomRgbColor() : stringToRgb(color.value);\n    }\n    if (isArray(color.value)) {\n        return colorToRgb({\n            value: itemFromArray(color.value, index, useIndex),\n        });\n    }\n    for (const [, manager] of colorManagers) {\n        const res = manager.handleColor(color);\n        if (res) {\n            return res;\n        }\n    }\n}\nexport function colorToHsl(color, index, useIndex = true) {\n    const rgb = colorToRgb(color, index, useIndex);\n    return rgb ? rgbToHsl(rgb) : undefined;\n}\nexport function rangeColorToHsl(color, index, useIndex = true) {\n    const rgb = rangeColorToRgb(color, index, useIndex);\n    return rgb ? rgbToHsl(rgb) : undefined;\n}\nexport function rgbToHsl(color) {\n    const rgbMax = 255, hMax = 360, sMax = 100, lMax = 100, hMin = 0, sMin = 0, hPhase = 60, half = 0.5, double = 2, r1 = color.r / rgbMax, g1 = color.g / rgbMax, b1 = color.b / rgbMax, max = Math.max(r1, g1, b1), min = Math.min(r1, g1, b1), res = {\n        h: hMin,\n        l: (max + min) * half,\n        s: sMin,\n    };\n    if (max !== min) {\n        res.s = res.l < half ? (max - min) / (max + min) : (max - min) / (double - max - min);\n        res.h =\n            r1 === max\n                ? (g1 - b1) / (max - min)\n                : (res.h = g1 === max ? double + (b1 - r1) / (max - min) : double * double + (r1 - g1) / (max - min));\n    }\n    res.l *= lMax;\n    res.s *= sMax;\n    res.h *= hPhase;\n    if (res.h < hMin) {\n        res.h += hMax;\n    }\n    if (res.h >= hMax) {\n        res.h -= hMax;\n    }\n    return res;\n}\nexport function stringToAlpha(input) {\n    return stringToRgba(input)?.a;\n}\nexport function stringToRgb(input) {\n    return stringToRgba(input);\n}\nexport function hslToRgb(hsl) {\n    const hMax = 360, sMax = 100, lMax = 100, sMin = 0, lMin = 0, h = ((hsl.h % hMax) + hMax) % hMax, s = Math.max(sMin, Math.min(sMax, hsl.s)), l = Math.max(lMin, Math.min(lMax, hsl.l)), hNormalized = h / hMax, sNormalized = s / sMax, lNormalized = l / lMax, rgbFactor = 255, triple = 3;\n    if (s === sMin) {\n        const grayscaleValue = Math.round(lNormalized * rgbFactor);\n        return { r: grayscaleValue, g: grayscaleValue, b: grayscaleValue };\n    }\n    const half = 0.5, double = 2, channel = (temp1, temp2, temp3) => {\n        const temp3Min = 0, temp3Max = 1, sextuple = 6;\n        if (temp3 < temp3Min) {\n            temp3++;\n        }\n        if (temp3 > temp3Max) {\n            temp3--;\n        }\n        if (temp3 * sextuple < temp3Max) {\n            return temp1 + (temp2 - temp1) * sextuple * temp3;\n        }\n        if (temp3 * double < temp3Max) {\n            return temp2;\n        }\n        if (temp3 * triple < temp3Max * double) {\n            const temp3Offset = double / triple;\n            return temp1 + (temp2 - temp1) * (temp3Offset - temp3) * sextuple;\n        }\n        return temp1;\n    }, sNormalizedOffset = 1, temp1 = lNormalized < half\n        ? lNormalized * (sNormalizedOffset + sNormalized)\n        : lNormalized + sNormalized - lNormalized * sNormalized, temp2 = double * lNormalized - temp1, phaseNumerator = 1, phaseThird = phaseNumerator / triple, red = Math.min(rgbFactor, rgbFactor * channel(temp2, temp1, hNormalized + phaseThird)), green = Math.min(rgbFactor, rgbFactor * channel(temp2, temp1, hNormalized)), blue = Math.min(rgbFactor, rgbFactor * channel(temp2, temp1, hNormalized - phaseThird));\n    return { r: Math.round(red), g: Math.round(green), b: Math.round(blue) };\n}\nexport function hslaToRgba(hsla) {\n    const rgbResult = hslToRgb(hsla);\n    return {\n        a: hsla.a,\n        b: rgbResult.b,\n        g: rgbResult.g,\n        r: rgbResult.r,\n    };\n}\nexport function getRandomRgbColor(min) {\n    const defaultMin = 0, fixedMin = min ?? defaultMin, rgbMax = 256;\n    return {\n        b: Math.floor(randomInRange(setRangeValue(fixedMin, rgbMax))),\n        g: Math.floor(randomInRange(setRangeValue(fixedMin, rgbMax))),\n        r: Math.floor(randomInRange(setRangeValue(fixedMin, rgbMax))),\n    };\n}\nexport function getStyleFromRgb(color, opacity) {\n    const defaultOpacity = 1;\n    return `rgba(${color.r}, ${color.g}, ${color.b}, ${opacity ?? defaultOpacity})`;\n}\nexport function getStyleFromHsl(color, opacity) {\n    const defaultOpacity = 1;\n    return `hsla(${color.h}, ${color.s}%, ${color.l}%, ${opacity ?? defaultOpacity})`;\n}\nexport function colorMix(color1, color2, size1, size2) {\n    let rgb1 = color1, rgb2 = color2;\n    if (rgb1.r === undefined) {\n        rgb1 = hslToRgb(color1);\n    }\n    if (rgb2.r === undefined) {\n        rgb2 = hslToRgb(color2);\n    }\n    return {\n        b: mix(rgb1.b, rgb2.b, size1, size2),\n        g: mix(rgb1.g, rgb2.g, size1, size2),\n        r: mix(rgb1.r, rgb2.r, size1, size2),\n    };\n}\nexport function getLinkColor(p1, p2, linkColor) {\n    if (linkColor === randomColorValue) {\n        return getRandomRgbColor();\n    }\n    else if (linkColor === midColorValue) {\n        const sourceColor = p1.getFillColor() ?? p1.getStrokeColor(), destColor = p2?.getFillColor() ?? p2?.getStrokeColor();\n        if (sourceColor && destColor && p2) {\n            return colorMix(sourceColor, destColor, p1.getRadius(), p2.getRadius());\n        }\n        else {\n            const hslColor = sourceColor ?? destColor;\n            if (hslColor) {\n                return hslToRgb(hslColor);\n            }\n        }\n    }\n    else {\n        return linkColor;\n    }\n}\nexport function getLinkRandomColor(optColor, blink, consent) {\n    const color = isString(optColor) ? optColor : optColor.value;\n    if (color === randomColorValue) {\n        if (consent) {\n            return rangeColorToRgb({\n                value: color,\n            });\n        }\n        if (blink) {\n            return randomColorValue;\n        }\n        return midColorValue;\n    }\n    else if (color === midColorValue) {\n        return midColorValue;\n    }\n    else {\n        return rangeColorToRgb({\n            value: color,\n        });\n    }\n}\nexport function getHslFromAnimation(animation) {\n    return animation !== undefined\n        ? {\n            h: animation.h.value,\n            s: animation.s.value,\n            l: animation.l.value,\n        }\n        : undefined;\n}\nexport function getHslAnimationFromHsl(hsl, animationOptions, reduceFactor) {\n    const resColor = {\n        h: {\n            enable: false,\n            value: hsl.h,\n        },\n        s: {\n            enable: false,\n            value: hsl.s,\n        },\n        l: {\n            enable: false,\n            value: hsl.l,\n        },\n    };\n    if (animationOptions) {\n        setColorAnimation(resColor.h, animationOptions.h, reduceFactor);\n        setColorAnimation(resColor.s, animationOptions.s, reduceFactor);\n        setColorAnimation(resColor.l, animationOptions.l, reduceFactor);\n    }\n    return resColor;\n}\nfunction setColorAnimation(colorValue, colorAnimation, reduceFactor) {\n    colorValue.enable = colorAnimation.enable;\n    const defaultVelocity = 0, decayOffset = 1, defaultLoops = 0, defaultTime = 0;\n    if (colorValue.enable) {\n        colorValue.velocity = (getRangeValue(colorAnimation.speed) / percentDenominator) * reduceFactor;\n        colorValue.decay = decayOffset - getRangeValue(colorAnimation.decay);\n        colorValue.status = AnimationStatus.increasing;\n        colorValue.loops = defaultLoops;\n        colorValue.maxLoops = getRangeValue(colorAnimation.count);\n        colorValue.time = defaultTime;\n        colorValue.delayTime = getRangeValue(colorAnimation.delay) * millisecondsToSeconds;\n        if (!colorAnimation.sync) {\n            colorValue.velocity *= getRandom();\n            colorValue.value *= getRandom();\n        }\n        colorValue.initialValue = colorValue.value;\n        colorValue.offset = setRangeValue(colorAnimation.offset);\n    }\n    else {\n        colorValue.velocity = defaultVelocity;\n    }\n}\nexport function updateColorValue(data, range, decrease, delta) {\n    const minLoops = 0, minDelay = 0, identity = 1, minVelocity = 0, minOffset = 0, velocityFactor = 3.6;\n    if (!data ||\n        !data.enable ||\n        ((data.maxLoops ?? minLoops) > minLoops && (data.loops ?? minLoops) > (data.maxLoops ?? minLoops))) {\n        return;\n    }\n    if (!data.time) {\n        data.time = 0;\n    }\n    if ((data.delayTime ?? minDelay) > minDelay && data.time < (data.delayTime ?? minDelay)) {\n        data.time += delta.value;\n    }\n    if ((data.delayTime ?? minDelay) > minDelay && data.time < (data.delayTime ?? minDelay)) {\n        return;\n    }\n    const offset = data.offset ? randomInRange(data.offset) : minOffset, velocity = (data.velocity ?? minVelocity) * delta.factor + offset * velocityFactor, decay = data.decay ?? identity, max = getRangeMax(range), min = getRangeMin(range);\n    if (!decrease || data.status === AnimationStatus.increasing) {\n        data.value += velocity;\n        if (data.value > max) {\n            if (!data.loops) {\n                data.loops = 0;\n            }\n            data.loops++;\n            if (decrease) {\n                data.status = AnimationStatus.decreasing;\n            }\n            else {\n                data.value -= max;\n            }\n        }\n    }\n    else {\n        data.value -= velocity;\n        const minValue = 0;\n        if (data.value < minValue) {\n            if (!data.loops) {\n                data.loops = 0;\n            }\n            data.loops++;\n            data.status = AnimationStatus.increasing;\n        }\n    }\n    if (data.velocity && decay !== identity) {\n        data.velocity *= decay;\n    }\n    data.value = clamp(data.value, min, max);\n}\nexport function updateColor(color, delta) {\n    if (!color) {\n        return;\n    }\n    const { h, s, l } = color;\n    const ranges = {\n        h: { min: 0, max: 360 },\n        s: { min: 0, max: 100 },\n        l: { min: 0, max: 100 },\n    };\n    if (h) {\n        updateColorValue(h, ranges.h, false, delta);\n    }\n    if (s) {\n        updateColorValue(s, ranges.s, true, delta);\n    }\n    if (l) {\n        updateColorValue(l, ranges.l, true, delta);\n    }\n}\n"], "mappings": "AAAA,SAASA,KAAK,EAAEC,SAAS,EAAEC,WAAW,EAAEC,WAAW,EAAEC,aAAa,EAAEC,GAAG,EAAEC,aAAa,EAAEC,aAAa,QAAS,kBAAkB;AAChI,SAASC,OAAO,EAAEC,QAAQ,QAAQ,gBAAgB;AAClD,SAASC,qBAAqB,EAAEC,kBAAkB,QAAQ,4BAA4B;AACtF,SAASC,eAAe,QAAQ,6BAA6B;AAC7D,SAASC,aAAa,QAAQ,YAAY;AAC1C,IAAIC,UAAU;AACd,CAAC,UAAUA,UAAU,EAAE;EACnBA,UAAU,CAACA,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG;EACrCA,UAAU,CAACA,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG;EACrCA,UAAU,CAACA,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG;EACrCA,UAAU,CAACA,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG;AACzC,CAAC,EAAEA,UAAU,KAAKA,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC;AACnC,MAAMC,gBAAgB,GAAG,QAAQ;EAAEC,aAAa,GAAG,KAAK;EAAEC,aAAa,GAAG,IAAIC,GAAG,CAAC,CAAC;AACnF,OAAO,SAASC,eAAeA,CAACC,OAAO,EAAE;EACrCH,aAAa,CAACI,GAAG,CAACD,OAAO,CAACE,GAAG,EAAEF,OAAO,CAAC;AAC3C;AACA,SAASG,YAAYA,CAACC,KAAK,EAAE;EACzB,KAAK,MAAM,GAAGJ,OAAO,CAAC,IAAIH,aAAa,EAAE;IACrC,IAAIO,KAAK,CAACC,UAAU,CAACL,OAAO,CAACM,YAAY,CAAC,EAAE;MACxC,OAAON,OAAO,CAACO,WAAW,CAACH,KAAK,CAAC;IACrC;EACJ;EACA,MAAMI,cAAc,GAAG,4CAA4C;IAAEC,QAAQ,GAAGL,KAAK,CAACM,OAAO,CAACF,cAAc,EAAE,CAACG,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,KAAK;MAC7H,OAAOH,CAAC,GAAGA,CAAC,GAAGC,CAAC,GAAGA,CAAC,GAAGC,CAAC,GAAGA,CAAC,IAAIC,CAAC,KAAKC,SAAS,GAAGD,CAAC,GAAGA,CAAC,GAAG,EAAE,CAAC;IACjE,CAAC,CAAC;IAAEE,KAAK,GAAG,wDAAwD;IAAEC,MAAM,GAAGD,KAAK,CAACE,IAAI,CAACV,QAAQ,CAAC;IAAEW,KAAK,GAAG,EAAE;IAAEC,YAAY,GAAG,CAAC;IAAEC,WAAW,GAAG,IAAI;EACrJ,OAAOJ,MAAM,GACP;IACEH,CAAC,EAAEG,MAAM,CAACxB,UAAU,CAACqB,CAAC,CAAC,KAAKC,SAAS,GAC/BO,QAAQ,CAACL,MAAM,CAACxB,UAAU,CAACqB,CAAC,CAAC,EAAEK,KAAK,CAAC,GAAGE,WAAW,GACnDD,YAAY;IAClBP,CAAC,EAAES,QAAQ,CAACL,MAAM,CAACxB,UAAU,CAACoB,CAAC,CAAC,EAAEM,KAAK,CAAC;IACxCP,CAAC,EAAEU,QAAQ,CAACL,MAAM,CAACxB,UAAU,CAACmB,CAAC,CAAC,EAAEO,KAAK,CAAC;IACxCR,CAAC,EAAEW,QAAQ,CAACL,MAAM,CAACxB,UAAU,CAACkB,CAAC,CAAC,EAAEQ,KAAK;EAC3C,CAAC,GACCJ,SAAS;AACnB;AACA,OAAO,SAASQ,eAAeA,CAACpB,KAAK,EAAEqB,KAAK,EAAEC,QAAQ,GAAG,IAAI,EAAE;EAC3D,IAAI,CAACtB,KAAK,EAAE;IACR;EACJ;EACA,MAAMuB,KAAK,GAAGtC,QAAQ,CAACe,KAAK,CAAC,GAAG;IAAEwB,KAAK,EAAExB;EAAM,CAAC,GAAGA,KAAK;EACxD,IAAIf,QAAQ,CAACsC,KAAK,CAACC,KAAK,CAAC,EAAE;IACvB,OAAOC,UAAU,CAACF,KAAK,CAACC,KAAK,EAAEH,KAAK,EAAEC,QAAQ,CAAC;EACnD;EACA,IAAItC,OAAO,CAACuC,KAAK,CAACC,KAAK,CAAC,EAAE;IACtB,OAAOJ,eAAe,CAAC;MACnBI,KAAK,EAAEnC,aAAa,CAACkC,KAAK,CAACC,KAAK,EAAEH,KAAK,EAAEC,QAAQ;IACrD,CAAC,CAAC;EACN;EACA,KAAK,MAAM,GAAG1B,OAAO,CAAC,IAAIH,aAAa,EAAE;IACrC,MAAMiC,GAAG,GAAG9B,OAAO,CAAC+B,gBAAgB,CAACJ,KAAK,CAAC;IAC3C,IAAIG,GAAG,EAAE;MACL,OAAOA,GAAG;IACd;EACJ;AACJ;AACA,OAAO,SAASD,UAAUA,CAACzB,KAAK,EAAEqB,KAAK,EAAEC,QAAQ,GAAG,IAAI,EAAE;EACtD,IAAI,CAACtB,KAAK,EAAE;IACR;EACJ;EACA,MAAMuB,KAAK,GAAGtC,QAAQ,CAACe,KAAK,CAAC,GAAG;IAAEwB,KAAK,EAAExB;EAAM,CAAC,GAAGA,KAAK;EACxD,IAAIf,QAAQ,CAACsC,KAAK,CAACC,KAAK,CAAC,EAAE;IACvB,OAAOD,KAAK,CAACC,KAAK,KAAKjC,gBAAgB,GAAGqC,iBAAiB,CAAC,CAAC,GAAGC,WAAW,CAACN,KAAK,CAACC,KAAK,CAAC;EAC5F;EACA,IAAIxC,OAAO,CAACuC,KAAK,CAACC,KAAK,CAAC,EAAE;IACtB,OAAOC,UAAU,CAAC;MACdD,KAAK,EAAEnC,aAAa,CAACkC,KAAK,CAACC,KAAK,EAAEH,KAAK,EAAEC,QAAQ;IACrD,CAAC,CAAC;EACN;EACA,KAAK,MAAM,GAAG1B,OAAO,CAAC,IAAIH,aAAa,EAAE;IACrC,MAAMiC,GAAG,GAAG9B,OAAO,CAACkC,WAAW,CAACP,KAAK,CAAC;IACtC,IAAIG,GAAG,EAAE;MACL,OAAOA,GAAG;IACd;EACJ;AACJ;AACA,OAAO,SAASK,UAAUA,CAACR,KAAK,EAAEF,KAAK,EAAEC,QAAQ,GAAG,IAAI,EAAE;EACtD,MAAMU,GAAG,GAAGP,UAAU,CAACF,KAAK,EAAEF,KAAK,EAAEC,QAAQ,CAAC;EAC9C,OAAOU,GAAG,GAAGC,QAAQ,CAACD,GAAG,CAAC,GAAGpB,SAAS;AAC1C;AACA,OAAO,SAASsB,eAAeA,CAACX,KAAK,EAAEF,KAAK,EAAEC,QAAQ,GAAG,IAAI,EAAE;EAC3D,MAAMU,GAAG,GAAGZ,eAAe,CAACG,KAAK,EAAEF,KAAK,EAAEC,QAAQ,CAAC;EACnD,OAAOU,GAAG,GAAGC,QAAQ,CAACD,GAAG,CAAC,GAAGpB,SAAS;AAC1C;AACA,OAAO,SAASqB,QAAQA,CAACV,KAAK,EAAE;EAC5B,MAAMY,MAAM,GAAG,GAAG;IAAEC,IAAI,GAAG,GAAG;IAAEC,IAAI,GAAG,GAAG;IAAEC,IAAI,GAAG,GAAG;IAAEC,IAAI,GAAG,CAAC;IAAEC,IAAI,GAAG,CAAC;IAAEC,MAAM,GAAG,EAAE;IAAEC,IAAI,GAAG,GAAG;IAAEC,MAAM,GAAG,CAAC;IAAEC,EAAE,GAAGrB,KAAK,CAACf,CAAC,GAAG2B,MAAM;IAAEU,EAAE,GAAGtB,KAAK,CAACd,CAAC,GAAG0B,MAAM;IAAEW,EAAE,GAAGvB,KAAK,CAACb,CAAC,GAAGyB,MAAM;IAAEY,GAAG,GAAGC,IAAI,CAACD,GAAG,CAACH,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC;IAAEG,GAAG,GAAGD,IAAI,CAACC,GAAG,CAACL,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC;IAAEpB,GAAG,GAAG;MAChPwB,CAAC,EAAEX,IAAI;MACPY,CAAC,EAAE,CAACJ,GAAG,GAAGE,GAAG,IAAIP,IAAI;MACrBU,CAAC,EAAEZ;IACP,CAAC;EACD,IAAIO,GAAG,KAAKE,GAAG,EAAE;IACbvB,GAAG,CAAC0B,CAAC,GAAG1B,GAAG,CAACyB,CAAC,GAAGT,IAAI,GAAG,CAACK,GAAG,GAAGE,GAAG,KAAKF,GAAG,GAAGE,GAAG,CAAC,GAAG,CAACF,GAAG,GAAGE,GAAG,KAAKN,MAAM,GAAGI,GAAG,GAAGE,GAAG,CAAC;IACrFvB,GAAG,CAACwB,CAAC,GACDN,EAAE,KAAKG,GAAG,GACJ,CAACF,EAAE,GAAGC,EAAE,KAAKC,GAAG,GAAGE,GAAG,CAAC,GACtBvB,GAAG,CAACwB,CAAC,GAAGL,EAAE,KAAKE,GAAG,GAAGJ,MAAM,GAAG,CAACG,EAAE,GAAGF,EAAE,KAAKG,GAAG,GAAGE,GAAG,CAAC,GAAGN,MAAM,GAAGA,MAAM,GAAG,CAACC,EAAE,GAAGC,EAAE,KAAKE,GAAG,GAAGE,GAAG,CAAE;EACjH;EACAvB,GAAG,CAACyB,CAAC,IAAIb,IAAI;EACbZ,GAAG,CAAC0B,CAAC,IAAIf,IAAI;EACbX,GAAG,CAACwB,CAAC,IAAIT,MAAM;EACf,IAAIf,GAAG,CAACwB,CAAC,GAAGX,IAAI,EAAE;IACdb,GAAG,CAACwB,CAAC,IAAId,IAAI;EACjB;EACA,IAAIV,GAAG,CAACwB,CAAC,IAAId,IAAI,EAAE;IACfV,GAAG,CAACwB,CAAC,IAAId,IAAI;EACjB;EACA,OAAOV,GAAG;AACd;AACA,OAAO,SAAS2B,aAAaA,CAACrD,KAAK,EAAE;EACjC,OAAOD,YAAY,CAACC,KAAK,CAAC,EAAEW,CAAC;AACjC;AACA,OAAO,SAASkB,WAAWA,CAAC7B,KAAK,EAAE;EAC/B,OAAOD,YAAY,CAACC,KAAK,CAAC;AAC9B;AACA,OAAO,SAASsD,QAAQA,CAACC,GAAG,EAAE;EAC1B,MAAMnB,IAAI,GAAG,GAAG;IAAEC,IAAI,GAAG,GAAG;IAAEC,IAAI,GAAG,GAAG;IAAEE,IAAI,GAAG,CAAC;IAAEgB,IAAI,GAAG,CAAC;IAAEN,CAAC,GAAG,CAAEK,GAAG,CAACL,CAAC,GAAGd,IAAI,GAAIA,IAAI,IAAIA,IAAI;IAAEgB,CAAC,GAAGJ,IAAI,CAACD,GAAG,CAACP,IAAI,EAAEQ,IAAI,CAACC,GAAG,CAACZ,IAAI,EAAEkB,GAAG,CAACH,CAAC,CAAC,CAAC;IAAED,CAAC,GAAGH,IAAI,CAACD,GAAG,CAACS,IAAI,EAAER,IAAI,CAACC,GAAG,CAACX,IAAI,EAAEiB,GAAG,CAACJ,CAAC,CAAC,CAAC;IAAEM,WAAW,GAAGP,CAAC,GAAGd,IAAI;IAAEsB,WAAW,GAAGN,CAAC,GAAGf,IAAI;IAAEsB,WAAW,GAAGR,CAAC,GAAGb,IAAI;IAAEsB,SAAS,GAAG,GAAG;IAAEC,MAAM,GAAG,CAAC;EAC3R,IAAIT,CAAC,KAAKZ,IAAI,EAAE;IACZ,MAAMsB,cAAc,GAAGd,IAAI,CAACe,KAAK,CAACJ,WAAW,GAAGC,SAAS,CAAC;IAC1D,OAAO;MAAEpD,CAAC,EAAEsD,cAAc;MAAErD,CAAC,EAAEqD,cAAc;MAAEpD,CAAC,EAAEoD;IAAe,CAAC;EACtE;EACA,MAAMpB,IAAI,GAAG,GAAG;IAAEC,MAAM,GAAG,CAAC;IAAEqB,OAAO,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAEC,KAAK,KAAK;MAC7D,MAAMC,QAAQ,GAAG,CAAC;QAAEC,QAAQ,GAAG,CAAC;QAAEC,QAAQ,GAAG,CAAC;MAC9C,IAAIH,KAAK,GAAGC,QAAQ,EAAE;QAClBD,KAAK,EAAE;MACX;MACA,IAAIA,KAAK,GAAGE,QAAQ,EAAE;QAClBF,KAAK,EAAE;MACX;MACA,IAAIA,KAAK,GAAGG,QAAQ,GAAGD,QAAQ,EAAE;QAC7B,OAAOJ,KAAK,GAAG,CAACC,KAAK,GAAGD,KAAK,IAAIK,QAAQ,GAAGH,KAAK;MACrD;MACA,IAAIA,KAAK,GAAGxB,MAAM,GAAG0B,QAAQ,EAAE;QAC3B,OAAOH,KAAK;MAChB;MACA,IAAIC,KAAK,GAAGN,MAAM,GAAGQ,QAAQ,GAAG1B,MAAM,EAAE;QACpC,MAAM4B,WAAW,GAAG5B,MAAM,GAAGkB,MAAM;QACnC,OAAOI,KAAK,GAAG,CAACC,KAAK,GAAGD,KAAK,KAAKM,WAAW,GAAGJ,KAAK,CAAC,GAAGG,QAAQ;MACrE;MACA,OAAOL,KAAK;IAChB,CAAC;IAAEO,iBAAiB,GAAG,CAAC;IAAEP,KAAK,GAAGN,WAAW,GAAGjB,IAAI,GAC9CiB,WAAW,IAAIa,iBAAiB,GAAGd,WAAW,CAAC,GAC/CC,WAAW,GAAGD,WAAW,GAAGC,WAAW,GAAGD,WAAW;IAAEQ,KAAK,GAAGvB,MAAM,GAAGgB,WAAW,GAAGM,KAAK;IAAEQ,cAAc,GAAG,CAAC;IAAEC,UAAU,GAAGD,cAAc,GAAGZ,MAAM;IAAEc,GAAG,GAAG3B,IAAI,CAACC,GAAG,CAACW,SAAS,EAAEA,SAAS,GAAGI,OAAO,CAACE,KAAK,EAAED,KAAK,EAAER,WAAW,GAAGiB,UAAU,CAAC,CAAC;IAAEE,KAAK,GAAG5B,IAAI,CAACC,GAAG,CAACW,SAAS,EAAEA,SAAS,GAAGI,OAAO,CAACE,KAAK,EAAED,KAAK,EAAER,WAAW,CAAC,CAAC;IAAEoB,IAAI,GAAG7B,IAAI,CAACC,GAAG,CAACW,SAAS,EAAEA,SAAS,GAAGI,OAAO,CAACE,KAAK,EAAED,KAAK,EAAER,WAAW,GAAGiB,UAAU,CAAC,CAAC;EACzZ,OAAO;IAAElE,CAAC,EAAEwC,IAAI,CAACe,KAAK,CAACY,GAAG,CAAC;IAAElE,CAAC,EAAEuC,IAAI,CAACe,KAAK,CAACa,KAAK,CAAC;IAAElE,CAAC,EAAEsC,IAAI,CAACe,KAAK,CAACc,IAAI;EAAE,CAAC;AAC5E;AACA,OAAO,SAASC,UAAUA,CAACC,IAAI,EAAE;EAC7B,MAAMC,SAAS,GAAG1B,QAAQ,CAACyB,IAAI,CAAC;EAChC,OAAO;IACHpE,CAAC,EAAEoE,IAAI,CAACpE,CAAC;IACTD,CAAC,EAAEsE,SAAS,CAACtE,CAAC;IACdD,CAAC,EAAEuE,SAAS,CAACvE,CAAC;IACdD,CAAC,EAAEwE,SAAS,CAACxE;EACjB,CAAC;AACL;AACA,OAAO,SAASoB,iBAAiBA,CAACqB,GAAG,EAAE;EACnC,MAAMgC,UAAU,GAAG,CAAC;IAAEC,QAAQ,GAAGjC,GAAG,IAAIgC,UAAU;IAAE9C,MAAM,GAAG,GAAG;EAChE,OAAO;IACHzB,CAAC,EAAEsC,IAAI,CAACmC,KAAK,CAACrG,aAAa,CAACC,aAAa,CAACmG,QAAQ,EAAE/C,MAAM,CAAC,CAAC,CAAC;IAC7D1B,CAAC,EAAEuC,IAAI,CAACmC,KAAK,CAACrG,aAAa,CAACC,aAAa,CAACmG,QAAQ,EAAE/C,MAAM,CAAC,CAAC,CAAC;IAC7D3B,CAAC,EAAEwC,IAAI,CAACmC,KAAK,CAACrG,aAAa,CAACC,aAAa,CAACmG,QAAQ,EAAE/C,MAAM,CAAC,CAAC;EAChE,CAAC;AACL;AACA,OAAO,SAASiD,eAAeA,CAAC7D,KAAK,EAAE8D,OAAO,EAAE;EAC5C,MAAMC,cAAc,GAAG,CAAC;EACxB,OAAO,QAAQ/D,KAAK,CAACf,CAAC,KAAKe,KAAK,CAACd,CAAC,KAAKc,KAAK,CAACb,CAAC,KAAK2E,OAAO,IAAIC,cAAc,GAAG;AACnF;AACA,OAAO,SAASC,eAAeA,CAAChE,KAAK,EAAE8D,OAAO,EAAE;EAC5C,MAAMC,cAAc,GAAG,CAAC;EACxB,OAAO,QAAQ/D,KAAK,CAAC2B,CAAC,KAAK3B,KAAK,CAAC6B,CAAC,MAAM7B,KAAK,CAAC4B,CAAC,MAAMkC,OAAO,IAAIC,cAAc,GAAG;AACrF;AACA,OAAO,SAASE,QAAQA,CAACC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,KAAK,EAAE;EACnD,IAAIC,IAAI,GAAGJ,MAAM;IAAEK,IAAI,GAAGJ,MAAM;EAChC,IAAIG,IAAI,CAACrF,CAAC,KAAKI,SAAS,EAAE;IACtBiF,IAAI,GAAGvC,QAAQ,CAACmC,MAAM,CAAC;EAC3B;EACA,IAAIK,IAAI,CAACtF,CAAC,KAAKI,SAAS,EAAE;IACtBkF,IAAI,GAAGxC,QAAQ,CAACoC,MAAM,CAAC;EAC3B;EACA,OAAO;IACHhF,CAAC,EAAE7B,GAAG,CAACgH,IAAI,CAACnF,CAAC,EAAEoF,IAAI,CAACpF,CAAC,EAAEiF,KAAK,EAAEC,KAAK,CAAC;IACpCnF,CAAC,EAAE5B,GAAG,CAACgH,IAAI,CAACpF,CAAC,EAAEqF,IAAI,CAACrF,CAAC,EAAEkF,KAAK,EAAEC,KAAK,CAAC;IACpCpF,CAAC,EAAE3B,GAAG,CAACgH,IAAI,CAACrF,CAAC,EAAEsF,IAAI,CAACtF,CAAC,EAAEmF,KAAK,EAAEC,KAAK;EACvC,CAAC;AACL;AACA,OAAO,SAASG,YAAYA,CAACC,EAAE,EAAEC,EAAE,EAAEC,SAAS,EAAE;EAC5C,IAAIA,SAAS,KAAK3G,gBAAgB,EAAE;IAChC,OAAOqC,iBAAiB,CAAC,CAAC;EAC9B,CAAC,MACI,IAAIsE,SAAS,KAAK1G,aAAa,EAAE;IAClC,MAAM2G,WAAW,GAAGH,EAAE,CAACI,YAAY,CAAC,CAAC,IAAIJ,EAAE,CAACK,cAAc,CAAC,CAAC;MAAEC,SAAS,GAAGL,EAAE,EAAEG,YAAY,CAAC,CAAC,IAAIH,EAAE,EAAEI,cAAc,CAAC,CAAC;IACpH,IAAIF,WAAW,IAAIG,SAAS,IAAIL,EAAE,EAAE;MAChC,OAAOT,QAAQ,CAACW,WAAW,EAAEG,SAAS,EAAEN,EAAE,CAACO,SAAS,CAAC,CAAC,EAAEN,EAAE,CAACM,SAAS,CAAC,CAAC,CAAC;IAC3E,CAAC,MACI;MACD,MAAMC,QAAQ,GAAGL,WAAW,IAAIG,SAAS;MACzC,IAAIE,QAAQ,EAAE;QACV,OAAOlD,QAAQ,CAACkD,QAAQ,CAAC;MAC7B;IACJ;EACJ,CAAC,MACI;IACD,OAAON,SAAS;EACpB;AACJ;AACA,OAAO,SAASO,kBAAkBA,CAACC,QAAQ,EAAEC,KAAK,EAAEC,OAAO,EAAE;EACzD,MAAMrF,KAAK,GAAGtC,QAAQ,CAACyH,QAAQ,CAAC,GAAGA,QAAQ,GAAGA,QAAQ,CAAClF,KAAK;EAC5D,IAAID,KAAK,KAAKhC,gBAAgB,EAAE;IAC5B,IAAIqH,OAAO,EAAE;MACT,OAAOxF,eAAe,CAAC;QACnBI,KAAK,EAAED;MACX,CAAC,CAAC;IACN;IACA,IAAIoF,KAAK,EAAE;MACP,OAAOpH,gBAAgB;IAC3B;IACA,OAAOC,aAAa;EACxB,CAAC,MACI,IAAI+B,KAAK,KAAK/B,aAAa,EAAE;IAC9B,OAAOA,aAAa;EACxB,CAAC,MACI;IACD,OAAO4B,eAAe,CAAC;MACnBI,KAAK,EAAED;IACX,CAAC,CAAC;EACN;AACJ;AACA,OAAO,SAASsF,mBAAmBA,CAACC,SAAS,EAAE;EAC3C,OAAOA,SAAS,KAAKlG,SAAS,GACxB;IACEsC,CAAC,EAAE4D,SAAS,CAAC5D,CAAC,CAAC1B,KAAK;IACpB4B,CAAC,EAAE0D,SAAS,CAAC1D,CAAC,CAAC5B,KAAK;IACpB2B,CAAC,EAAE2D,SAAS,CAAC3D,CAAC,CAAC3B;EACnB,CAAC,GACCZ,SAAS;AACnB;AACA,OAAO,SAASmG,sBAAsBA,CAACxD,GAAG,EAAEyD,gBAAgB,EAAEC,YAAY,EAAE;EACxE,MAAMC,QAAQ,GAAG;IACbhE,CAAC,EAAE;MACCiE,MAAM,EAAE,KAAK;MACb3F,KAAK,EAAE+B,GAAG,CAACL;IACf,CAAC;IACDE,CAAC,EAAE;MACC+D,MAAM,EAAE,KAAK;MACb3F,KAAK,EAAE+B,GAAG,CAACH;IACf,CAAC;IACDD,CAAC,EAAE;MACCgE,MAAM,EAAE,KAAK;MACb3F,KAAK,EAAE+B,GAAG,CAACJ;IACf;EACJ,CAAC;EACD,IAAI6D,gBAAgB,EAAE;IAClBI,iBAAiB,CAACF,QAAQ,CAAChE,CAAC,EAAE8D,gBAAgB,CAAC9D,CAAC,EAAE+D,YAAY,CAAC;IAC/DG,iBAAiB,CAACF,QAAQ,CAAC9D,CAAC,EAAE4D,gBAAgB,CAAC5D,CAAC,EAAE6D,YAAY,CAAC;IAC/DG,iBAAiB,CAACF,QAAQ,CAAC/D,CAAC,EAAE6D,gBAAgB,CAAC7D,CAAC,EAAE8D,YAAY,CAAC;EACnE;EACA,OAAOC,QAAQ;AACnB;AACA,SAASE,iBAAiBA,CAACC,UAAU,EAAEC,cAAc,EAAEL,YAAY,EAAE;EACjEI,UAAU,CAACF,MAAM,GAAGG,cAAc,CAACH,MAAM;EACzC,MAAMI,eAAe,GAAG,CAAC;IAAEC,WAAW,GAAG,CAAC;IAAEC,YAAY,GAAG,CAAC;IAAEC,WAAW,GAAG,CAAC;EAC7E,IAAIL,UAAU,CAACF,MAAM,EAAE;IACnBE,UAAU,CAACM,QAAQ,GAAI/I,aAAa,CAAC0I,cAAc,CAACM,KAAK,CAAC,GAAGzI,kBAAkB,GAAI8H,YAAY;IAC/FI,UAAU,CAACQ,KAAK,GAAGL,WAAW,GAAG5I,aAAa,CAAC0I,cAAc,CAACO,KAAK,CAAC;IACpER,UAAU,CAACS,MAAM,GAAG1I,eAAe,CAAC2I,UAAU;IAC9CV,UAAU,CAACW,KAAK,GAAGP,YAAY;IAC/BJ,UAAU,CAACY,QAAQ,GAAGrJ,aAAa,CAAC0I,cAAc,CAACY,KAAK,CAAC;IACzDb,UAAU,CAACc,IAAI,GAAGT,WAAW;IAC7BL,UAAU,CAACe,SAAS,GAAGxJ,aAAa,CAAC0I,cAAc,CAACe,KAAK,CAAC,GAAGnJ,qBAAqB;IAClF,IAAI,CAACoI,cAAc,CAACgB,IAAI,EAAE;MACtBjB,UAAU,CAACM,QAAQ,IAAIlJ,SAAS,CAAC,CAAC;MAClC4I,UAAU,CAAC7F,KAAK,IAAI/C,SAAS,CAAC,CAAC;IACnC;IACA4I,UAAU,CAACkB,YAAY,GAAGlB,UAAU,CAAC7F,KAAK;IAC1C6F,UAAU,CAACmB,MAAM,GAAGzJ,aAAa,CAACuI,cAAc,CAACkB,MAAM,CAAC;EAC5D,CAAC,MACI;IACDnB,UAAU,CAACM,QAAQ,GAAGJ,eAAe;EACzC;AACJ;AACA,OAAO,SAASkB,gBAAgBA,CAACC,IAAI,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,KAAK,EAAE;EAC3D,MAAMC,QAAQ,GAAG,CAAC;IAAEC,QAAQ,GAAG,CAAC;IAAEC,QAAQ,GAAG,CAAC;IAAEC,WAAW,GAAG,CAAC;IAAEC,SAAS,GAAG,CAAC;IAAEC,cAAc,GAAG,GAAG;EACpG,IAAI,CAACT,IAAI,IACL,CAACA,IAAI,CAACvB,MAAM,IACX,CAACuB,IAAI,CAACT,QAAQ,IAAIa,QAAQ,IAAIA,QAAQ,IAAI,CAACJ,IAAI,CAACV,KAAK,IAAIc,QAAQ,KAAKJ,IAAI,CAACT,QAAQ,IAAIa,QAAQ,CAAE,EAAE;IACpG;EACJ;EACA,IAAI,CAACJ,IAAI,CAACP,IAAI,EAAE;IACZO,IAAI,CAACP,IAAI,GAAG,CAAC;EACjB;EACA,IAAI,CAACO,IAAI,CAACN,SAAS,IAAIW,QAAQ,IAAIA,QAAQ,IAAIL,IAAI,CAACP,IAAI,IAAIO,IAAI,CAACN,SAAS,IAAIW,QAAQ,CAAC,EAAE;IACrFL,IAAI,CAACP,IAAI,IAAIU,KAAK,CAACrH,KAAK;EAC5B;EACA,IAAI,CAACkH,IAAI,CAACN,SAAS,IAAIW,QAAQ,IAAIA,QAAQ,IAAIL,IAAI,CAACP,IAAI,IAAIO,IAAI,CAACN,SAAS,IAAIW,QAAQ,CAAC,EAAE;IACrF;EACJ;EACA,MAAMP,MAAM,GAAGE,IAAI,CAACF,MAAM,GAAG1J,aAAa,CAAC4J,IAAI,CAACF,MAAM,CAAC,GAAGU,SAAS;IAAEvB,QAAQ,GAAG,CAACe,IAAI,CAACf,QAAQ,IAAIsB,WAAW,IAAIJ,KAAK,CAACO,MAAM,GAAGZ,MAAM,GAAGW,cAAc;IAAEtB,KAAK,GAAGa,IAAI,CAACb,KAAK,IAAImB,QAAQ;IAAEjG,GAAG,GAAGrE,WAAW,CAACiK,KAAK,CAAC;IAAE1F,GAAG,GAAGtE,WAAW,CAACgK,KAAK,CAAC;EAC3O,IAAI,CAACC,QAAQ,IAAIF,IAAI,CAACZ,MAAM,KAAK1I,eAAe,CAAC2I,UAAU,EAAE;IACzDW,IAAI,CAAClH,KAAK,IAAImG,QAAQ;IACtB,IAAIe,IAAI,CAAClH,KAAK,GAAGuB,GAAG,EAAE;MAClB,IAAI,CAAC2F,IAAI,CAACV,KAAK,EAAE;QACbU,IAAI,CAACV,KAAK,GAAG,CAAC;MAClB;MACAU,IAAI,CAACV,KAAK,EAAE;MACZ,IAAIY,QAAQ,EAAE;QACVF,IAAI,CAACZ,MAAM,GAAG1I,eAAe,CAACiK,UAAU;MAC5C,CAAC,MACI;QACDX,IAAI,CAAClH,KAAK,IAAIuB,GAAG;MACrB;IACJ;EACJ,CAAC,MACI;IACD2F,IAAI,CAAClH,KAAK,IAAImG,QAAQ;IACtB,MAAM2B,QAAQ,GAAG,CAAC;IAClB,IAAIZ,IAAI,CAAClH,KAAK,GAAG8H,QAAQ,EAAE;MACvB,IAAI,CAACZ,IAAI,CAACV,KAAK,EAAE;QACbU,IAAI,CAACV,KAAK,GAAG,CAAC;MAClB;MACAU,IAAI,CAACV,KAAK,EAAE;MACZU,IAAI,CAACZ,MAAM,GAAG1I,eAAe,CAAC2I,UAAU;IAC5C;EACJ;EACA,IAAIW,IAAI,CAACf,QAAQ,IAAIE,KAAK,KAAKmB,QAAQ,EAAE;IACrCN,IAAI,CAACf,QAAQ,IAAIE,KAAK;EAC1B;EACAa,IAAI,CAAClH,KAAK,GAAGhD,KAAK,CAACkK,IAAI,CAAClH,KAAK,EAAEyB,GAAG,EAAEF,GAAG,CAAC;AAC5C;AACA,OAAO,SAASwG,WAAWA,CAAChI,KAAK,EAAEsH,KAAK,EAAE;EACtC,IAAI,CAACtH,KAAK,EAAE;IACR;EACJ;EACA,MAAM;IAAE2B,CAAC;IAAEE,CAAC;IAAED;EAAE,CAAC,GAAG5B,KAAK;EACzB,MAAMiI,MAAM,GAAG;IACXtG,CAAC,EAAE;MAAED,GAAG,EAAE,CAAC;MAAEF,GAAG,EAAE;IAAI,CAAC;IACvBK,CAAC,EAAE;MAAEH,GAAG,EAAE,CAAC;MAAEF,GAAG,EAAE;IAAI,CAAC;IACvBI,CAAC,EAAE;MAAEF,GAAG,EAAE,CAAC;MAAEF,GAAG,EAAE;IAAI;EAC1B,CAAC;EACD,IAAIG,CAAC,EAAE;IACHuF,gBAAgB,CAACvF,CAAC,EAAEsG,MAAM,CAACtG,CAAC,EAAE,KAAK,EAAE2F,KAAK,CAAC;EAC/C;EACA,IAAIzF,CAAC,EAAE;IACHqF,gBAAgB,CAACrF,CAAC,EAAEoG,MAAM,CAACpG,CAAC,EAAE,IAAI,EAAEyF,KAAK,CAAC;EAC9C;EACA,IAAI1F,CAAC,EAAE;IACHsF,gBAAgB,CAACtF,CAAC,EAAEqG,MAAM,CAACrG,CAAC,EAAE,IAAI,EAAE0F,KAAK,CAAC;EAC9C;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
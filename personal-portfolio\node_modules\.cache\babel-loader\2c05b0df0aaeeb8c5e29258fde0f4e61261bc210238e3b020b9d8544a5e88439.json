{"ast": null, "code": "import { setRangeValue } from \"@tsparticles/engine\";\nexport class Push {\n  constructor() {\n    this.default = true;\n    this.groups = [];\n    this.quantity = 4;\n  }\n  load(data) {\n    if (!data) {\n      return;\n    }\n    if (data.default !== undefined) {\n      this.default = data.default;\n    }\n    if (data.groups !== undefined) {\n      this.groups = data.groups.map(t => t);\n    }\n    if (!this.groups.length) {\n      this.default = true;\n    }\n    const quantity = data.quantity;\n    if (quantity !== undefined) {\n      this.quantity = setRangeValue(quantity);\n    }\n  }\n}", "map": {"version": 3, "names": ["setRangeValue", "<PERSON><PERSON>", "constructor", "default", "groups", "quantity", "load", "data", "undefined", "map", "t", "length"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/interaction-external-push/browser/Options/Classes/Push.js"], "sourcesContent": ["import { setRangeValue } from \"@tsparticles/engine\";\nexport class Push {\n    constructor() {\n        this.default = true;\n        this.groups = [];\n        this.quantity = 4;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.default !== undefined) {\n            this.default = data.default;\n        }\n        if (data.groups !== undefined) {\n            this.groups = data.groups.map(t => t);\n        }\n        if (!this.groups.length) {\n            this.default = true;\n        }\n        const quantity = data.quantity;\n        if (quantity !== undefined) {\n            this.quantity = setRangeValue(quantity);\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,qBAAqB;AACnD,OAAO,MAAMC,IAAI,CAAC;EACdC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,MAAM,GAAG,EAAE;IAChB,IAAI,CAACC,QAAQ,GAAG,CAAC;EACrB;EACAC,IAAIA,CAACC,IAAI,EAAE;IACP,IAAI,CAACA,IAAI,EAAE;MACP;IACJ;IACA,IAAIA,IAAI,CAACJ,OAAO,KAAKK,SAAS,EAAE;MAC5B,IAAI,CAACL,OAAO,GAAGI,IAAI,CAACJ,OAAO;IAC/B;IACA,IAAII,IAAI,CAACH,MAAM,KAAKI,SAAS,EAAE;MAC3B,IAAI,CAACJ,MAAM,GAAGG,IAAI,CAACH,MAAM,CAACK,GAAG,CAACC,CAAC,IAAIA,CAAC,CAAC;IACzC;IACA,IAAI,CAAC,IAAI,CAACN,MAAM,CAACO,MAAM,EAAE;MACrB,IAAI,CAACR,OAAO,GAAG,IAAI;IACvB;IACA,MAAME,QAAQ,GAAGE,IAAI,CAACF,QAAQ;IAC9B,IAAIA,QAAQ,KAAKG,SAAS,EAAE;MACxB,IAAI,CAACH,QAAQ,GAAGL,aAAa,CAACK,QAAQ,CAAC;IAC3C;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
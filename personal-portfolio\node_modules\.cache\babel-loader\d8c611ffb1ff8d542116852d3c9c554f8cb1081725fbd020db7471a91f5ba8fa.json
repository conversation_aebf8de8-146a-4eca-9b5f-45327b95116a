{"ast": null, "code": "import { CollisionMode } from \"@tsparticles/engine\";\nimport { absorb } from \"./Absorb.js\";\nimport { bounce } from \"./Bounce.js\";\nimport { destroy } from \"./Destroy.js\";\nexport function resolveCollision(p1, p2, delta, pixelRatio) {\n  switch (p1.options.collisions.mode) {\n    case CollisionMode.absorb:\n      {\n        absorb(p1, p2, delta, pixelRatio);\n        break;\n      }\n    case CollisionMode.bounce:\n      {\n        bounce(p1, p2);\n        break;\n      }\n    case CollisionMode.destroy:\n      {\n        destroy(p1, p2);\n        break;\n      }\n  }\n}", "map": {"version": 3, "names": ["CollisionMode", "absorb", "bounce", "destroy", "resolveCollision", "p1", "p2", "delta", "pixelRatio", "options", "collisions", "mode"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/interaction-particles-collisions/browser/ResolveCollision.js"], "sourcesContent": ["import { CollisionMode } from \"@tsparticles/engine\";\nimport { absorb } from \"./Absorb.js\";\nimport { bounce } from \"./Bounce.js\";\nimport { destroy } from \"./Destroy.js\";\nexport function resolveCollision(p1, p2, delta, pixelRatio) {\n    switch (p1.options.collisions.mode) {\n        case CollisionMode.absorb: {\n            absorb(p1, p2, delta, pixelRatio);\n            break;\n        }\n        case CollisionMode.bounce: {\n            bounce(p1, p2);\n            break;\n        }\n        case CollisionMode.destroy: {\n            destroy(p1, p2);\n            break;\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,qBAAqB;AACnD,SAASC,MAAM,QAAQ,aAAa;AACpC,SAASC,MAAM,QAAQ,aAAa;AACpC,SAASC,OAAO,QAAQ,cAAc;AACtC,OAAO,SAASC,gBAAgBA,CAACC,EAAE,EAAEC,EAAE,EAAEC,KAAK,EAAEC,UAAU,EAAE;EACxD,QAAQH,EAAE,CAACI,OAAO,CAACC,UAAU,CAACC,IAAI;IAC9B,KAAKX,aAAa,CAACC,MAAM;MAAE;QACvBA,MAAM,CAACI,EAAE,EAAEC,EAAE,EAAEC,KAAK,EAAEC,UAAU,CAAC;QACjC;MACJ;IACA,KAAKR,aAAa,CAACE,MAAM;MAAE;QACvBA,MAAM,CAACG,EAAE,EAAEC,EAAE,CAAC;QACd;MACJ;IACA,KAAKN,aAAa,CAACG,OAAO;MAAE;QACxBA,OAAO,CAACE,EAAE,EAAEC,EAAE,CAAC;QACf;MACJ;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
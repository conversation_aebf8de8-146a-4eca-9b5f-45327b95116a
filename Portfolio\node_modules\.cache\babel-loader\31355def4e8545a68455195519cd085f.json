{"ast": null, "code": "import { LinkInstance } from \"./LinkInstance\";\n\nclass LinksPlugin {\n  constructor() {\n    this.id = \"links\";\n  }\n\n  getPlugin(container) {\n    return new LinkInstance(container);\n  }\n\n  needsPlugin() {\n    return true;\n  }\n\n  loadOptions() {}\n\n}\n\nexport async function loadPlugin(engine) {\n  const plugin = new LinksPlugin();\n  await engine.addPlugin(plugin);\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Interactions/Particles/Links/plugin.js"], "names": ["LinkInstance", "LinksPlugin", "constructor", "id", "getPlugin", "container", "needsPlugin", "loadOptions", "loadPlugin", "engine", "plugin", "addPlugin"], "mappings": "AAAA,SAASA,YAAT,QAA6B,gBAA7B;;AACA,MAAMC,WAAN,CAAkB;AACdC,EAAAA,WAAW,GAAG;AACV,SAAKC,EAAL,GAAU,OAAV;AACH;;AACDC,EAAAA,SAAS,CAACC,SAAD,EAAY;AACjB,WAAO,IAAIL,YAAJ,CAAiBK,SAAjB,CAAP;AACH;;AACDC,EAAAA,WAAW,GAAG;AACV,WAAO,IAAP;AACH;;AACDC,EAAAA,WAAW,GAAG,CACb;;AAXa;;AAalB,OAAO,eAAeC,UAAf,CAA0BC,MAA1B,EAAkC;AACrC,QAAMC,MAAM,GAAG,IAAIT,WAAJ,EAAf;AACA,QAAMQ,MAAM,CAACE,SAAP,CAAiBD,MAAjB,CAAN;AACH", "sourcesContent": ["import { LinkInstance } from \"./LinkInstance\";\nclass LinksPlugin {\n    constructor() {\n        this.id = \"links\";\n    }\n    getPlugin(container) {\n        return new LinkInstance(container);\n    }\n    needsPlugin() {\n        return true;\n    }\n    loadOptions() {\n    }\n}\nexport async function loadPlugin(engine) {\n    const plugin = new LinksPlugin();\n    await engine.addPlugin(plugin);\n}\n"]}, "metadata": {}, "sourceType": "module"}
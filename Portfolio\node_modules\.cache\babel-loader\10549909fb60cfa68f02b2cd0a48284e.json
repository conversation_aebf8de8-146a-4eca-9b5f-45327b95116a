{"ast": null, "code": "import { deepExtend } from \"../../../../Utils\";\nexport class Shape {\n  constructor() {\n    this.options = {};\n    this.type = \"circle\";\n  }\n\n  get image() {\n    var _a;\n\n    return (_a = this.options[\"image\"]) !== null && _a !== void 0 ? _a : this.options[\"images\"];\n  }\n\n  set image(value) {\n    this.options[\"image\"] = value;\n    this.options[\"images\"] = value;\n  }\n\n  get custom() {\n    return this.options;\n  }\n\n  set custom(value) {\n    this.options = value;\n  }\n\n  get images() {\n    return this.image;\n  }\n\n  set images(value) {\n    this.image = value;\n  }\n\n  get stroke() {\n    return [];\n  }\n\n  set stroke(_value) {}\n\n  get character() {\n    var _a;\n\n    return (_a = this.options[\"character\"]) !== null && _a !== void 0 ? _a : this.options[\"char\"];\n  }\n\n  set character(value) {\n    this.options[\"character\"] = value;\n    this.options[\"char\"] = value;\n  }\n\n  get polygon() {\n    var _a;\n\n    return (_a = this.options[\"polygon\"]) !== null && _a !== void 0 ? _a : this.options[\"star\"];\n  }\n\n  set polygon(value) {\n    this.options[\"polygon\"] = value;\n    this.options[\"star\"] = value;\n  }\n\n  load(data) {\n    var _a, _b, _c;\n\n    if (data === undefined) {\n      return;\n    }\n\n    const options = (_a = data.options) !== null && _a !== void 0 ? _a : data.custom;\n\n    if (options !== undefined) {\n      for (const shape in options) {\n        const item = options[shape];\n\n        if (item !== undefined) {\n          this.options[shape] = deepExtend((_b = this.options[shape]) !== null && _b !== void 0 ? _b : {}, item);\n        }\n      }\n    }\n\n    this.loadShape(data.character, \"character\", \"char\", true);\n    this.loadShape(data.polygon, \"polygon\", \"star\", false);\n    this.loadShape((_c = data.image) !== null && _c !== void 0 ? _c : data.images, \"image\", \"images\", true);\n\n    if (data.type !== undefined) {\n      this.type = data.type;\n    }\n  }\n\n  loadShape(item, mainKey, altKey, altOverride) {\n    var _a, _b, _c, _d;\n\n    if (item === undefined) {\n      return;\n    }\n\n    if (item instanceof Array) {\n      if (!(this.options[mainKey] instanceof Array)) {\n        this.options[mainKey] = [];\n\n        if (!this.options[altKey] || altOverride) {\n          this.options[altKey] = [];\n        }\n      }\n\n      this.options[mainKey] = deepExtend((_a = this.options[mainKey]) !== null && _a !== void 0 ? _a : [], item);\n\n      if (!this.options[altKey] || altOverride) {\n        this.options[altKey] = deepExtend((_b = this.options[altKey]) !== null && _b !== void 0 ? _b : [], item);\n      }\n    } else {\n      if (this.options[mainKey] instanceof Array) {\n        this.options[mainKey] = {};\n\n        if (!this.options[altKey] || altOverride) {\n          this.options[altKey] = {};\n        }\n      }\n\n      this.options[mainKey] = deepExtend((_c = this.options[mainKey]) !== null && _c !== void 0 ? _c : {}, item);\n\n      if (!this.options[altKey] || altOverride) {\n        this.options[altKey] = deepExtend((_d = this.options[altKey]) !== null && _d !== void 0 ? _d : {}, item);\n      }\n    }\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Options/Classes/Particles/Shape/Shape.js"], "names": ["deepExtend", "<PERSON><PERSON><PERSON>", "constructor", "options", "type", "image", "_a", "value", "custom", "images", "stroke", "_value", "character", "polygon", "load", "data", "_b", "_c", "undefined", "shape", "item", "loadShape", "main<PERSON>ey", "altKey", "altOverride", "_d", "Array"], "mappings": "AAAA,SAASA,UAAT,QAA2B,mBAA3B;AACA,OAAO,MAAMC,KAAN,CAAY;AACfC,EAAAA,WAAW,GAAG;AACV,SAAKC,OAAL,GAAe,EAAf;AACA,SAAKC,IAAL,GAAY,QAAZ;AACH;;AACQ,MAALC,KAAK,GAAG;AACR,QAAIC,EAAJ;;AACA,WAAQ,CAACA,EAAE,GAAG,KAAKH,OAAL,CAAa,OAAb,CAAN,MAAiC,IAAjC,IAAyCG,EAAE,KAAK,KAAK,CAArD,GAAyDA,EAAzD,GAA8D,KAAKH,OAAL,CAAa,QAAb,CAAtE;AACH;;AACQ,MAALE,KAAK,CAACE,KAAD,EAAQ;AACb,SAAKJ,OAAL,CAAa,OAAb,IAAwBI,KAAxB;AACA,SAAKJ,OAAL,CAAa,QAAb,IAAyBI,KAAzB;AACH;;AACS,MAANC,MAAM,GAAG;AACT,WAAO,KAAKL,OAAZ;AACH;;AACS,MAANK,MAAM,CAACD,KAAD,EAAQ;AACd,SAAKJ,OAAL,GAAeI,KAAf;AACH;;AACS,MAANE,MAAM,GAAG;AACT,WAAO,KAAKJ,KAAZ;AACH;;AACS,MAANI,MAAM,CAACF,KAAD,EAAQ;AACd,SAAKF,KAAL,GAAaE,KAAb;AACH;;AACS,MAANG,MAAM,GAAG;AACT,WAAO,EAAP;AACH;;AACS,MAANA,MAAM,CAACC,MAAD,EAAS,CAClB;;AACY,MAATC,SAAS,GAAG;AACZ,QAAIN,EAAJ;;AACA,WAAQ,CAACA,EAAE,GAAG,KAAKH,OAAL,CAAa,WAAb,CAAN,MAAqC,IAArC,IAA6CG,EAAE,KAAK,KAAK,CAAzD,GAA6DA,EAA7D,GAAkE,KAAKH,OAAL,CAAa,MAAb,CAA1E;AACH;;AACY,MAATS,SAAS,CAACL,KAAD,EAAQ;AACjB,SAAKJ,OAAL,CAAa,WAAb,IAA4BI,KAA5B;AACA,SAAKJ,OAAL,CAAa,MAAb,IAAuBI,KAAvB;AACH;;AACU,MAAPM,OAAO,GAAG;AACV,QAAIP,EAAJ;;AACA,WAAQ,CAACA,EAAE,GAAG,KAAKH,OAAL,CAAa,SAAb,CAAN,MAAmC,IAAnC,IAA2CG,EAAE,KAAK,KAAK,CAAvD,GAA2DA,EAA3D,GAAgE,KAAKH,OAAL,CAAa,MAAb,CAAxE;AACH;;AACU,MAAPU,OAAO,CAACN,KAAD,EAAQ;AACf,SAAKJ,OAAL,CAAa,SAAb,IAA0BI,KAA1B;AACA,SAAKJ,OAAL,CAAa,MAAb,IAAuBI,KAAvB;AACH;;AACDO,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,QAAIT,EAAJ,EAAQU,EAAR,EAAYC,EAAZ;;AACA,QAAIF,IAAI,KAAKG,SAAb,EAAwB;AACpB;AACH;;AACD,UAAMf,OAAO,GAAG,CAACG,EAAE,GAAGS,IAAI,CAACZ,OAAX,MAAwB,IAAxB,IAAgCG,EAAE,KAAK,KAAK,CAA5C,GAAgDA,EAAhD,GAAqDS,IAAI,CAACP,MAA1E;;AACA,QAAIL,OAAO,KAAKe,SAAhB,EAA2B;AACvB,WAAK,MAAMC,KAAX,IAAoBhB,OAApB,EAA6B;AACzB,cAAMiB,IAAI,GAAGjB,OAAO,CAACgB,KAAD,CAApB;;AACA,YAAIC,IAAI,KAAKF,SAAb,EAAwB;AACpB,eAAKf,OAAL,CAAagB,KAAb,IAAsBnB,UAAU,CAAC,CAACgB,EAAE,GAAG,KAAKb,OAAL,CAAagB,KAAb,CAAN,MAA+B,IAA/B,IAAuCH,EAAE,KAAK,KAAK,CAAnD,GAAuDA,EAAvD,GAA4D,EAA7D,EAAiEI,IAAjE,CAAhC;AACH;AACJ;AACJ;;AACD,SAAKC,SAAL,CAAeN,IAAI,CAACH,SAApB,EAA+B,WAA/B,EAA4C,MAA5C,EAAoD,IAApD;AACA,SAAKS,SAAL,CAAeN,IAAI,CAACF,OAApB,EAA6B,SAA7B,EAAwC,MAAxC,EAAgD,KAAhD;AACA,SAAKQ,SAAL,CAAe,CAACJ,EAAE,GAAGF,IAAI,CAACV,KAAX,MAAsB,IAAtB,IAA8BY,EAAE,KAAK,KAAK,CAA1C,GAA8CA,EAA9C,GAAmDF,IAAI,CAACN,MAAvE,EAA+E,OAA/E,EAAwF,QAAxF,EAAkG,IAAlG;;AACA,QAAIM,IAAI,CAACX,IAAL,KAAcc,SAAlB,EAA6B;AACzB,WAAKd,IAAL,GAAYW,IAAI,CAACX,IAAjB;AACH;AACJ;;AACDiB,EAAAA,SAAS,CAACD,IAAD,EAAOE,OAAP,EAAgBC,MAAhB,EAAwBC,WAAxB,EAAqC;AAC1C,QAAIlB,EAAJ,EAAQU,EAAR,EAAYC,EAAZ,EAAgBQ,EAAhB;;AACA,QAAIL,IAAI,KAAKF,SAAb,EAAwB;AACpB;AACH;;AACD,QAAIE,IAAI,YAAYM,KAApB,EAA2B;AACvB,UAAI,EAAE,KAAKvB,OAAL,CAAamB,OAAb,aAAiCI,KAAnC,CAAJ,EAA+C;AAC3C,aAAKvB,OAAL,CAAamB,OAAb,IAAwB,EAAxB;;AACA,YAAI,CAAC,KAAKnB,OAAL,CAAaoB,MAAb,CAAD,IAAyBC,WAA7B,EAA0C;AACtC,eAAKrB,OAAL,CAAaoB,MAAb,IAAuB,EAAvB;AACH;AACJ;;AACD,WAAKpB,OAAL,CAAamB,OAAb,IAAwBtB,UAAU,CAAC,CAACM,EAAE,GAAG,KAAKH,OAAL,CAAamB,OAAb,CAAN,MAAiC,IAAjC,IAAyChB,EAAE,KAAK,KAAK,CAArD,GAAyDA,EAAzD,GAA8D,EAA/D,EAAmEc,IAAnE,CAAlC;;AACA,UAAI,CAAC,KAAKjB,OAAL,CAAaoB,MAAb,CAAD,IAAyBC,WAA7B,EAA0C;AACtC,aAAKrB,OAAL,CAAaoB,MAAb,IAAuBvB,UAAU,CAAC,CAACgB,EAAE,GAAG,KAAKb,OAAL,CAAaoB,MAAb,CAAN,MAAgC,IAAhC,IAAwCP,EAAE,KAAK,KAAK,CAApD,GAAwDA,EAAxD,GAA6D,EAA9D,EAAkEI,IAAlE,CAAjC;AACH;AACJ,KAXD,MAYK;AACD,UAAI,KAAKjB,OAAL,CAAamB,OAAb,aAAiCI,KAArC,EAA4C;AACxC,aAAKvB,OAAL,CAAamB,OAAb,IAAwB,EAAxB;;AACA,YAAI,CAAC,KAAKnB,OAAL,CAAaoB,MAAb,CAAD,IAAyBC,WAA7B,EAA0C;AACtC,eAAKrB,OAAL,CAAaoB,MAAb,IAAuB,EAAvB;AACH;AACJ;;AACD,WAAKpB,OAAL,CAAamB,OAAb,IAAwBtB,UAAU,CAAC,CAACiB,EAAE,GAAG,KAAKd,OAAL,CAAamB,OAAb,CAAN,MAAiC,IAAjC,IAAyCL,EAAE,KAAK,KAAK,CAArD,GAAyDA,EAAzD,GAA8D,EAA/D,EAAmEG,IAAnE,CAAlC;;AACA,UAAI,CAAC,KAAKjB,OAAL,CAAaoB,MAAb,CAAD,IAAyBC,WAA7B,EAA0C;AACtC,aAAKrB,OAAL,CAAaoB,MAAb,IAAuBvB,UAAU,CAAC,CAACyB,EAAE,GAAG,KAAKtB,OAAL,CAAaoB,MAAb,CAAN,MAAgC,IAAhC,IAAwCE,EAAE,KAAK,KAAK,CAApD,GAAwDA,EAAxD,GAA6D,EAA9D,EAAkEL,IAAlE,CAAjC;AACH;AACJ;AACJ;;AAhGc", "sourcesContent": ["import { deepExtend } from \"../../../../Utils\";\nexport class Shape {\n    constructor() {\n        this.options = {};\n        this.type = \"circle\";\n    }\n    get image() {\n        var _a;\n        return ((_a = this.options[\"image\"]) !== null && _a !== void 0 ? _a : this.options[\"images\"]);\n    }\n    set image(value) {\n        this.options[\"image\"] = value;\n        this.options[\"images\"] = value;\n    }\n    get custom() {\n        return this.options;\n    }\n    set custom(value) {\n        this.options = value;\n    }\n    get images() {\n        return this.image;\n    }\n    set images(value) {\n        this.image = value;\n    }\n    get stroke() {\n        return [];\n    }\n    set stroke(_value) {\n    }\n    get character() {\n        var _a;\n        return ((_a = this.options[\"character\"]) !== null && _a !== void 0 ? _a : this.options[\"char\"]);\n    }\n    set character(value) {\n        this.options[\"character\"] = value;\n        this.options[\"char\"] = value;\n    }\n    get polygon() {\n        var _a;\n        return ((_a = this.options[\"polygon\"]) !== null && _a !== void 0 ? _a : this.options[\"star\"]);\n    }\n    set polygon(value) {\n        this.options[\"polygon\"] = value;\n        this.options[\"star\"] = value;\n    }\n    load(data) {\n        var _a, _b, _c;\n        if (data === undefined) {\n            return;\n        }\n        const options = (_a = data.options) !== null && _a !== void 0 ? _a : data.custom;\n        if (options !== undefined) {\n            for (const shape in options) {\n                const item = options[shape];\n                if (item !== undefined) {\n                    this.options[shape] = deepExtend((_b = this.options[shape]) !== null && _b !== void 0 ? _b : {}, item);\n                }\n            }\n        }\n        this.loadShape(data.character, \"character\", \"char\", true);\n        this.loadShape(data.polygon, \"polygon\", \"star\", false);\n        this.loadShape((_c = data.image) !== null && _c !== void 0 ? _c : data.images, \"image\", \"images\", true);\n        if (data.type !== undefined) {\n            this.type = data.type;\n        }\n    }\n    loadShape(item, mainKey, altKey, altOverride) {\n        var _a, _b, _c, _d;\n        if (item === undefined) {\n            return;\n        }\n        if (item instanceof Array) {\n            if (!(this.options[mainKey] instanceof Array)) {\n                this.options[mainKey] = [];\n                if (!this.options[altKey] || altOverride) {\n                    this.options[altKey] = [];\n                }\n            }\n            this.options[mainKey] = deepExtend((_a = this.options[mainKey]) !== null && _a !== void 0 ? _a : [], item);\n            if (!this.options[altKey] || altOverride) {\n                this.options[altKey] = deepExtend((_b = this.options[altKey]) !== null && _b !== void 0 ? _b : [], item);\n            }\n        }\n        else {\n            if (this.options[mainKey] instanceof Array) {\n                this.options[mainKey] = {};\n                if (!this.options[altKey] || altOverride) {\n                    this.options[altKey] = {};\n                }\n            }\n            this.options[mainKey] = deepExtend((_c = this.options[mainKey]) !== null && _c !== void 0 ? _c : {}, item);\n            if (!this.options[altKey] || altOverride) {\n                this.options[altKey] = deepExtend((_d = this.options[altKey]) !== null && _d !== void 0 ? _d : {}, item);\n            }\n        }\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
{"ast": null, "code": "export var EmitterClickMode;\n(function (EmitterClickMode) {\n  EmitterClickMode[\"emitter\"] = \"emitter\";\n})(EmitterClickMode || (EmitterClickMode = {}));", "map": {"version": 3, "names": ["EmitterClickMode"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/plugin-emitters/browser/Enums/EmitterClickMode.js"], "sourcesContent": ["export var EmitterClickMode;\n(function (EmitterClickMode) {\n    EmitterClickMode[\"emitter\"] = \"emitter\";\n})(EmitterClickMode || (EmitterClickMode = {}));\n"], "mappings": "AAAA,OAAO,IAAIA,gBAAgB;AAC3B,CAAC,UAAUA,gBAAgB,EAAE;EACzBA,gBAAgB,CAAC,SAAS,CAAC,GAAG,SAAS;AAC3C,CAAC,EAAEA,gBAAgB,KAAKA,gBAAgB,GAAG,CAAC,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
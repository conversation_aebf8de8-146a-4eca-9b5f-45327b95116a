{"ast": null, "code": "import { getRangeValue } from \"../../Utils\";\nexport function bounceHorizontal(data) {\n  if (!(data.outMode === \"bounce\" || data.outMode === \"bounce-horizontal\" || data.outMode === \"bounceHorizontal\" || data.outMode === \"split\")) {\n    return;\n  }\n\n  const velocity = data.particle.velocity.x;\n  let bounced = false;\n\n  if (data.direction === \"right\" && data.bounds.right >= data.canvasSize.width && velocity > 0 || data.direction === \"left\" && data.bounds.left <= 0 && velocity < 0) {\n    const newVelocity = getRangeValue(data.particle.options.bounce.horizontal.value);\n    data.particle.velocity.x *= -newVelocity;\n    bounced = true;\n  }\n\n  if (!bounced) {\n    return;\n  }\n\n  const minPos = data.offset.x + data.size;\n\n  if (data.bounds.right >= data.canvasSize.width) {\n    data.particle.position.x = data.canvasSize.width - minPos;\n  } else if (data.bounds.left <= 0) {\n    data.particle.position.x = minPos;\n  }\n\n  if (data.outMode === \"split\") {\n    data.particle.destroy();\n  }\n}\nexport function bounceVertical(data) {\n  if (data.outMode === \"bounce\" || data.outMode === \"bounce-vertical\" || data.outMode === \"bounceVertical\" || data.outMode === \"split\") {\n    const velocity = data.particle.velocity.y;\n    let bounced = false;\n\n    if (data.direction === \"bottom\" && data.bounds.bottom >= data.canvasSize.height && velocity > 0 || data.direction === \"top\" && data.bounds.top <= 0 && velocity < 0) {\n      const newVelocity = getRangeValue(data.particle.options.bounce.vertical.value);\n      data.particle.velocity.y *= -newVelocity;\n      bounced = true;\n    }\n\n    if (!bounced) {\n      return;\n    }\n\n    const minPos = data.offset.y + data.size;\n\n    if (data.bounds.bottom >= data.canvasSize.height) {\n      data.particle.position.y = data.canvasSize.height - minPos;\n    } else if (data.bounds.top <= 0) {\n      data.particle.position.y = minPos;\n    }\n\n    if (data.outMode === \"split\") {\n      data.particle.destroy();\n    }\n  }\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Updaters/OutModes/Utils.js"], "names": ["getRangeValue", "bounceHorizontal", "data", "outMode", "velocity", "particle", "x", "bounced", "direction", "bounds", "right", "canvasSize", "width", "left", "newVelocity", "options", "bounce", "horizontal", "value", "minPos", "offset", "size", "position", "destroy", "bounceVertical", "y", "bottom", "height", "top", "vertical"], "mappings": "AAAA,SAASA,aAAT,QAA8B,aAA9B;AACA,OAAO,SAASC,gBAAT,CAA0BC,IAA1B,EAAgC;AACnC,MAAI,EAAEA,IAAI,CAACC,OAAL,KAAiB,QAAjB,IACFD,IAAI,CAACC,OAAL,KAAiB,mBADf,IAEFD,IAAI,CAACC,OAAL,KAAiB,kBAFf,IAGFD,IAAI,CAACC,OAAL,KAAiB,OAHjB,CAAJ,EAG+B;AAC3B;AACH;;AACD,QAAMC,QAAQ,GAAGF,IAAI,CAACG,QAAL,CAAcD,QAAd,CAAuBE,CAAxC;AACA,MAAIC,OAAO,GAAG,KAAd;;AACA,MAAKL,IAAI,CAACM,SAAL,KAAmB,OAAnB,IAA8BN,IAAI,CAACO,MAAL,CAAYC,KAAZ,IAAqBR,IAAI,CAACS,UAAL,CAAgBC,KAAnE,IAA4ER,QAAQ,GAAG,CAAxF,IACCF,IAAI,CAACM,SAAL,KAAmB,MAAnB,IAA6BN,IAAI,CAACO,MAAL,CAAYI,IAAZ,IAAoB,CAAjD,IAAsDT,QAAQ,GAAG,CADtE,EAC0E;AACtE,UAAMU,WAAW,GAAGd,aAAa,CAACE,IAAI,CAACG,QAAL,CAAcU,OAAd,CAAsBC,MAAtB,CAA6BC,UAA7B,CAAwCC,KAAzC,CAAjC;AACAhB,IAAAA,IAAI,CAACG,QAAL,CAAcD,QAAd,CAAuBE,CAAvB,IAA4B,CAACQ,WAA7B;AACAP,IAAAA,OAAO,GAAG,IAAV;AACH;;AACD,MAAI,CAACA,OAAL,EAAc;AACV;AACH;;AACD,QAAMY,MAAM,GAAGjB,IAAI,CAACkB,MAAL,CAAYd,CAAZ,GAAgBJ,IAAI,CAACmB,IAApC;;AACA,MAAInB,IAAI,CAACO,MAAL,CAAYC,KAAZ,IAAqBR,IAAI,CAACS,UAAL,CAAgBC,KAAzC,EAAgD;AAC5CV,IAAAA,IAAI,CAACG,QAAL,CAAciB,QAAd,CAAuBhB,CAAvB,GAA2BJ,IAAI,CAACS,UAAL,CAAgBC,KAAhB,GAAwBO,MAAnD;AACH,GAFD,MAGK,IAAIjB,IAAI,CAACO,MAAL,CAAYI,IAAZ,IAAoB,CAAxB,EAA2B;AAC5BX,IAAAA,IAAI,CAACG,QAAL,CAAciB,QAAd,CAAuBhB,CAAvB,GAA2Ba,MAA3B;AACH;;AACD,MAAIjB,IAAI,CAACC,OAAL,KAAiB,OAArB,EAA8B;AAC1BD,IAAAA,IAAI,CAACG,QAAL,CAAckB,OAAd;AACH;AACJ;AACD,OAAO,SAASC,cAAT,CAAwBtB,IAAxB,EAA8B;AACjC,MAAIA,IAAI,CAACC,OAAL,KAAiB,QAAjB,IACAD,IAAI,CAACC,OAAL,KAAiB,iBADjB,IAEAD,IAAI,CAACC,OAAL,KAAiB,gBAFjB,IAGAD,IAAI,CAACC,OAAL,KAAiB,OAHrB,EAG8B;AAC1B,UAAMC,QAAQ,GAAGF,IAAI,CAACG,QAAL,CAAcD,QAAd,CAAuBqB,CAAxC;AACA,QAAIlB,OAAO,GAAG,KAAd;;AACA,QAAKL,IAAI,CAACM,SAAL,KAAmB,QAAnB,IACDN,IAAI,CAACO,MAAL,CAAYiB,MAAZ,IAAsBxB,IAAI,CAACS,UAAL,CAAgBgB,MADrC,IAEDvB,QAAQ,GAAG,CAFX,IAGCF,IAAI,CAACM,SAAL,KAAmB,KAAnB,IAA4BN,IAAI,CAACO,MAAL,CAAYmB,GAAZ,IAAmB,CAA/C,IAAoDxB,QAAQ,GAAG,CAHpE,EAGwE;AACpE,YAAMU,WAAW,GAAGd,aAAa,CAACE,IAAI,CAACG,QAAL,CAAcU,OAAd,CAAsBC,MAAtB,CAA6Ba,QAA7B,CAAsCX,KAAvC,CAAjC;AACAhB,MAAAA,IAAI,CAACG,QAAL,CAAcD,QAAd,CAAuBqB,CAAvB,IAA4B,CAACX,WAA7B;AACAP,MAAAA,OAAO,GAAG,IAAV;AACH;;AACD,QAAI,CAACA,OAAL,EAAc;AACV;AACH;;AACD,UAAMY,MAAM,GAAGjB,IAAI,CAACkB,MAAL,CAAYK,CAAZ,GAAgBvB,IAAI,CAACmB,IAApC;;AACA,QAAInB,IAAI,CAACO,MAAL,CAAYiB,MAAZ,IAAsBxB,IAAI,CAACS,UAAL,CAAgBgB,MAA1C,EAAkD;AAC9CzB,MAAAA,IAAI,CAACG,QAAL,CAAciB,QAAd,CAAuBG,CAAvB,GAA2BvB,IAAI,CAACS,UAAL,CAAgBgB,MAAhB,GAAyBR,MAApD;AACH,KAFD,MAGK,IAAIjB,IAAI,CAACO,MAAL,CAAYmB,GAAZ,IAAmB,CAAvB,EAA0B;AAC3B1B,MAAAA,IAAI,CAACG,QAAL,CAAciB,QAAd,CAAuBG,CAAvB,GAA2BN,MAA3B;AACH;;AACD,QAAIjB,IAAI,CAACC,OAAL,KAAiB,OAArB,EAA8B;AAC1BD,MAAAA,IAAI,CAACG,QAAL,CAAckB,OAAd;AACH;AACJ;AACJ", "sourcesContent": ["import { getRangeValue } from \"../../Utils\";\nexport function bounceHorizontal(data) {\n    if (!(data.outMode === \"bounce\" ||\n        data.outMode === \"bounce-horizontal\" ||\n        data.outMode === \"bounceHorizontal\" ||\n        data.outMode === \"split\")) {\n        return;\n    }\n    const velocity = data.particle.velocity.x;\n    let bounced = false;\n    if ((data.direction === \"right\" && data.bounds.right >= data.canvasSize.width && velocity > 0) ||\n        (data.direction === \"left\" && data.bounds.left <= 0 && velocity < 0)) {\n        const newVelocity = getRangeValue(data.particle.options.bounce.horizontal.value);\n        data.particle.velocity.x *= -newVelocity;\n        bounced = true;\n    }\n    if (!bounced) {\n        return;\n    }\n    const minPos = data.offset.x + data.size;\n    if (data.bounds.right >= data.canvasSize.width) {\n        data.particle.position.x = data.canvasSize.width - minPos;\n    }\n    else if (data.bounds.left <= 0) {\n        data.particle.position.x = minPos;\n    }\n    if (data.outMode === \"split\") {\n        data.particle.destroy();\n    }\n}\nexport function bounceVertical(data) {\n    if (data.outMode === \"bounce\" ||\n        data.outMode === \"bounce-vertical\" ||\n        data.outMode === \"bounceVertical\" ||\n        data.outMode === \"split\") {\n        const velocity = data.particle.velocity.y;\n        let bounced = false;\n        if ((data.direction === \"bottom\" &&\n            data.bounds.bottom >= data.canvasSize.height &&\n            velocity > 0) ||\n            (data.direction === \"top\" && data.bounds.top <= 0 && velocity < 0)) {\n            const newVelocity = getRangeValue(data.particle.options.bounce.vertical.value);\n            data.particle.velocity.y *= -newVelocity;\n            bounced = true;\n        }\n        if (!bounced) {\n            return;\n        }\n        const minPos = data.offset.y + data.size;\n        if (data.bounds.bottom >= data.canvasSize.height) {\n            data.particle.position.y = data.canvasSize.height - minPos;\n        }\n        else if (data.bounds.top <= 0) {\n            data.particle.position.y = minPos;\n        }\n        if (data.outMode === \"split\") {\n            data.particle.destroy();\n        }\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
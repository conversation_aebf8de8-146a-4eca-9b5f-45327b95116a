{"ast": null, "code": "import { getHslAnimationFromHsl, getRangeValue, itemFromSingleOrMultiple, rangeColorToHsl, updateColor } from \"@tsparticles/engine\";\nconst defaultOpacity = 1;\nexport class StrokeColorUpdater {\n  constructor(container) {\n    this.container = container;\n  }\n  init(particle) {\n    const container = this.container,\n      options = particle.options;\n    const stroke = itemFromSingleOrMultiple(options.stroke, particle.id, options.reduceDuplicates);\n    particle.strokeWidth = getRangeValue(stroke.width) * container.retina.pixelRatio;\n    particle.strokeOpacity = getRangeValue(stroke.opacity ?? defaultOpacity);\n    particle.strokeAnimation = stroke.color?.animation;\n    const strokeHslColor = rangeColorToHsl(stroke.color) ?? particle.getFillColor();\n    if (strokeHslColor) {\n      particle.strokeColor = getHslAnimationFromHsl(strokeHslColor, particle.strokeAnimation, container.retina.reduceFactor);\n    }\n  }\n  isEnabled(particle) {\n    const color = particle.strokeAnimation,\n      {\n        strokeColor\n      } = particle;\n    return !particle.destroyed && !particle.spawning && !!color && (strokeColor?.h.value !== undefined && strokeColor.h.enable || strokeColor?.s.value !== undefined && strokeColor.s.enable || strokeColor?.l.value !== undefined && strokeColor.l.enable);\n  }\n  update(particle, delta) {\n    if (!this.isEnabled(particle)) {\n      return;\n    }\n    updateColor(particle.strokeColor, delta);\n  }\n}", "map": {"version": 3, "names": ["getHslAnimationFromHsl", "getRangeValue", "itemFromSingleOrMultiple", "rangeColorToHsl", "updateColor", "defaultOpacity", "StrokeColorUpdater", "constructor", "container", "init", "particle", "options", "stroke", "id", "reduceDuplicates", "strokeWidth", "width", "retina", "pixelRatio", "strokeOpacity", "opacity", "strokeAnimation", "color", "animation", "strokeHslColor", "getFillColor", "strokeColor", "reduceFactor", "isEnabled", "destroyed", "spawning", "h", "value", "undefined", "enable", "s", "l", "update", "delta"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/updater-stroke-color/browser/StrokeColorUpdater.js"], "sourcesContent": ["import { getHslAnimationFromHsl, getRangeValue, itemFromSingleOrMultiple, rangeColorToHsl, updateColor, } from \"@tsparticles/engine\";\nconst defaultOpacity = 1;\nexport class StrokeColorUpdater {\n    constructor(container) {\n        this.container = container;\n    }\n    init(particle) {\n        const container = this.container, options = particle.options;\n        const stroke = itemFromSingleOrMultiple(options.stroke, particle.id, options.reduceDuplicates);\n        particle.strokeWidth = getRangeValue(stroke.width) * container.retina.pixelRatio;\n        particle.strokeOpacity = getRangeValue(stroke.opacity ?? defaultOpacity);\n        particle.strokeAnimation = stroke.color?.animation;\n        const strokeHslColor = rangeColorToHsl(stroke.color) ?? particle.getFillColor();\n        if (strokeHslColor) {\n            particle.strokeColor = getHslAnimationFromHsl(strokeHslColor, particle.strokeAnimation, container.retina.reduceFactor);\n        }\n    }\n    isEnabled(particle) {\n        const color = particle.strokeAnimation, { strokeColor } = particle;\n        return (!particle.destroyed &&\n            !particle.spawning &&\n            !!color &&\n            ((strokeColor?.h.value !== undefined && strokeColor.h.enable) ||\n                (strokeColor?.s.value !== undefined && strokeColor.s.enable) ||\n                (strokeColor?.l.value !== undefined && strokeColor.l.enable)));\n    }\n    update(particle, delta) {\n        if (!this.isEnabled(particle)) {\n            return;\n        }\n        updateColor(particle.strokeColor, delta);\n    }\n}\n"], "mappings": "AAAA,SAASA,sBAAsB,EAAEC,aAAa,EAAEC,wBAAwB,EAAEC,eAAe,EAAEC,WAAW,QAAS,qBAAqB;AACpI,MAAMC,cAAc,GAAG,CAAC;AACxB,OAAO,MAAMC,kBAAkB,CAAC;EAC5BC,WAAWA,CAACC,SAAS,EAAE;IACnB,IAAI,CAACA,SAAS,GAAGA,SAAS;EAC9B;EACAC,IAAIA,CAACC,QAAQ,EAAE;IACX,MAAMF,SAAS,GAAG,IAAI,CAACA,SAAS;MAAEG,OAAO,GAAGD,QAAQ,CAACC,OAAO;IAC5D,MAAMC,MAAM,GAAGV,wBAAwB,CAACS,OAAO,CAACC,MAAM,EAAEF,QAAQ,CAACG,EAAE,EAAEF,OAAO,CAACG,gBAAgB,CAAC;IAC9FJ,QAAQ,CAACK,WAAW,GAAGd,aAAa,CAACW,MAAM,CAACI,KAAK,CAAC,GAAGR,SAAS,CAACS,MAAM,CAACC,UAAU;IAChFR,QAAQ,CAACS,aAAa,GAAGlB,aAAa,CAACW,MAAM,CAACQ,OAAO,IAAIf,cAAc,CAAC;IACxEK,QAAQ,CAACW,eAAe,GAAGT,MAAM,CAACU,KAAK,EAAEC,SAAS;IAClD,MAAMC,cAAc,GAAGrB,eAAe,CAACS,MAAM,CAACU,KAAK,CAAC,IAAIZ,QAAQ,CAACe,YAAY,CAAC,CAAC;IAC/E,IAAID,cAAc,EAAE;MAChBd,QAAQ,CAACgB,WAAW,GAAG1B,sBAAsB,CAACwB,cAAc,EAAEd,QAAQ,CAACW,eAAe,EAAEb,SAAS,CAACS,MAAM,CAACU,YAAY,CAAC;IAC1H;EACJ;EACAC,SAASA,CAAClB,QAAQ,EAAE;IAChB,MAAMY,KAAK,GAAGZ,QAAQ,CAACW,eAAe;MAAE;QAAEK;MAAY,CAAC,GAAGhB,QAAQ;IAClE,OAAQ,CAACA,QAAQ,CAACmB,SAAS,IACvB,CAACnB,QAAQ,CAACoB,QAAQ,IAClB,CAAC,CAACR,KAAK,KACLI,WAAW,EAAEK,CAAC,CAACC,KAAK,KAAKC,SAAS,IAAIP,WAAW,CAACK,CAAC,CAACG,MAAM,IACvDR,WAAW,EAAES,CAAC,CAACH,KAAK,KAAKC,SAAS,IAAIP,WAAW,CAACS,CAAC,CAACD,MAAO,IAC3DR,WAAW,EAAEU,CAAC,CAACJ,KAAK,KAAKC,SAAS,IAAIP,WAAW,CAACU,CAAC,CAACF,MAAO,CAAC;EACzE;EACAG,MAAMA,CAAC3B,QAAQ,EAAE4B,KAAK,EAAE;IACpB,IAAI,CAAC,IAAI,CAACV,SAAS,CAAClB,QAAQ,CAAC,EAAE;MAC3B;IACJ;IACAN,WAAW,CAACM,QAAQ,CAACgB,WAAW,EAAEY,KAAK,CAAC;EAC5C;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { OptionsColor } from \"../OptionsColor.js\";\nexport class Background {\n  constructor() {\n    this.color = new OptionsColor();\n    this.color.value = \"\";\n    this.image = \"\";\n    this.position = \"\";\n    this.repeat = \"\";\n    this.size = \"\";\n    this.opacity = 1;\n  }\n  load(data) {\n    if (!data) {\n      return;\n    }\n    if (data.color !== undefined) {\n      this.color = OptionsColor.create(this.color, data.color);\n    }\n    if (data.image !== undefined) {\n      this.image = data.image;\n    }\n    if (data.position !== undefined) {\n      this.position = data.position;\n    }\n    if (data.repeat !== undefined) {\n      this.repeat = data.repeat;\n    }\n    if (data.size !== undefined) {\n      this.size = data.size;\n    }\n    if (data.opacity !== undefined) {\n      this.opacity = data.opacity;\n    }\n  }\n}", "map": {"version": 3, "names": ["OptionsColor", "Background", "constructor", "color", "value", "image", "position", "repeat", "size", "opacity", "load", "data", "undefined", "create"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/engine/browser/Options/Classes/Background/Background.js"], "sourcesContent": ["import { OptionsColor } from \"../OptionsColor.js\";\nexport class Background {\n    constructor() {\n        this.color = new OptionsColor();\n        this.color.value = \"\";\n        this.image = \"\";\n        this.position = \"\";\n        this.repeat = \"\";\n        this.size = \"\";\n        this.opacity = 1;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.color !== undefined) {\n            this.color = OptionsColor.create(this.color, data.color);\n        }\n        if (data.image !== undefined) {\n            this.image = data.image;\n        }\n        if (data.position !== undefined) {\n            this.position = data.position;\n        }\n        if (data.repeat !== undefined) {\n            this.repeat = data.repeat;\n        }\n        if (data.size !== undefined) {\n            this.size = data.size;\n        }\n        if (data.opacity !== undefined) {\n            this.opacity = data.opacity;\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,oBAAoB;AACjD,OAAO,MAAMC,UAAU,CAAC;EACpBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,KAAK,GAAG,IAAIH,YAAY,CAAC,CAAC;IAC/B,IAAI,CAACG,KAAK,CAACC,KAAK,GAAG,EAAE;IACrB,IAAI,CAACC,KAAK,GAAG,EAAE;IACf,IAAI,CAACC,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACC,MAAM,GAAG,EAAE;IAChB,IAAI,CAACC,IAAI,GAAG,EAAE;IACd,IAAI,CAACC,OAAO,GAAG,CAAC;EACpB;EACAC,IAAIA,CAACC,IAAI,EAAE;IACP,IAAI,CAACA,IAAI,EAAE;MACP;IACJ;IACA,IAAIA,IAAI,CAACR,KAAK,KAAKS,SAAS,EAAE;MAC1B,IAAI,CAACT,KAAK,GAAGH,YAAY,CAACa,MAAM,CAAC,IAAI,CAACV,KAAK,EAAEQ,IAAI,CAACR,KAAK,CAAC;IAC5D;IACA,IAAIQ,IAAI,CAACN,KAAK,KAAKO,SAAS,EAAE;MAC1B,IAAI,CAACP,KAAK,GAAGM,IAAI,CAACN,KAAK;IAC3B;IACA,IAAIM,IAAI,CAACL,QAAQ,KAAKM,SAAS,EAAE;MAC7B,IAAI,CAACN,QAAQ,GAAGK,IAAI,CAACL,QAAQ;IACjC;IACA,IAAIK,IAAI,CAACJ,MAAM,KAAKK,SAAS,EAAE;MAC3B,IAAI,CAACL,MAAM,GAAGI,IAAI,CAACJ,MAAM;IAC7B;IACA,IAAII,IAAI,CAACH,IAAI,KAAKI,SAAS,EAAE;MACzB,IAAI,CAACJ,IAAI,GAAGG,IAAI,CAACH,IAAI;IACzB;IACA,IAAIG,IAAI,CAACF,OAAO,KAAKG,SAAS,EAAE;MAC5B,IAAI,CAACH,OAAO,GAAGE,IAAI,CAACF,OAAO;IAC/B;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
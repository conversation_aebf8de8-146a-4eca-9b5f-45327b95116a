{"ast": null, "code": "import { Circle, Constants, ExternalInteractorBase, Vector } from \"../../../Core\";\nimport { calcEasing, clamp, getDistances, isInArray } from \"../../../Utils\";\nexport class Attractor extends ExternalInteractorBase {\n  constructor(container) {\n    super(container);\n  }\n\n  isEnabled() {\n    const container = this.container,\n          options = container.actualOptions,\n          mouse = container.interactivity.mouse,\n          events = options.interactivity.events;\n\n    if ((!mouse.position || !events.onHover.enable) && (!mouse.clickPosition || !events.onClick.enable)) {\n      return false;\n    }\n\n    const hoverMode = events.onHover.mode,\n          clickMode = events.onClick.mode;\n    return isInArray(\"attract\", hoverMode) || isInArray(\"attract\", clickMode);\n  }\n\n  reset() {}\n\n  async interact() {\n    const container = this.container,\n          options = container.actualOptions,\n          mouseMoveStatus = container.interactivity.status === Constants.mouseMoveEvent,\n          events = options.interactivity.events,\n          hoverEnabled = events.onHover.enable,\n          hoverMode = events.onHover.mode,\n          clickEnabled = events.onClick.enable,\n          clickMode = events.onClick.mode;\n\n    if (mouseMoveStatus && hoverEnabled && isInArray(\"attract\", hoverMode)) {\n      this.hoverAttract();\n    } else if (clickEnabled && isInArray(\"attract\", clickMode)) {\n      this.clickAttract();\n    }\n  }\n\n  hoverAttract() {\n    const container = this.container;\n    const mousePos = container.interactivity.mouse.position;\n\n    if (!mousePos) {\n      return;\n    }\n\n    const attractRadius = container.retina.attractModeDistance;\n    this.processAttract(mousePos, attractRadius, new Circle(mousePos.x, mousePos.y, attractRadius));\n  }\n\n  processAttract(position, attractRadius, area) {\n    const container = this.container;\n    const attractOptions = container.actualOptions.interactivity.modes.attract;\n    const query = container.particles.quadTree.query(area);\n\n    for (const particle of query) {\n      const {\n        dx,\n        dy,\n        distance\n      } = getDistances(particle.position, position);\n      const velocity = attractOptions.speed * attractOptions.factor;\n      const attractFactor = clamp(calcEasing(1 - distance / attractRadius, attractOptions.easing) * velocity, 0, attractOptions.maxSpeed);\n      const normVec = Vector.create(distance === 0 ? velocity : dx / distance * attractFactor, distance === 0 ? velocity : dy / distance * attractFactor);\n      particle.position.subFrom(normVec);\n    }\n  }\n\n  clickAttract() {\n    const container = this.container;\n\n    if (!container.attract.finish) {\n      if (!container.attract.count) {\n        container.attract.count = 0;\n      }\n\n      container.attract.count++;\n\n      if (container.attract.count === container.particles.count) {\n        container.attract.finish = true;\n      }\n    }\n\n    if (container.attract.clicking) {\n      const mousePos = container.interactivity.mouse.clickPosition;\n\n      if (!mousePos) {\n        return;\n      }\n\n      const attractRadius = container.retina.attractModeDistance;\n      this.processAttract(mousePos, attractRadius, new Circle(mousePos.x, mousePos.y, attractRadius));\n    } else if (container.attract.clicking === false) {\n      container.attract.particles = [];\n    }\n\n    return;\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Interactions/External/Attract/Attractor.js"], "names": ["Circle", "Constants", "ExternalInteractorBase", "Vector", "calcEasing", "clamp", "getDistances", "isInArray", "Attractor", "constructor", "container", "isEnabled", "options", "actualOptions", "mouse", "interactivity", "events", "position", "onHover", "enable", "clickPosition", "onClick", "hoverMode", "mode", "clickMode", "reset", "interact", "mouseMoveStatus", "status", "mouseMoveEvent", "hoverEnabled", "clickEnabled", "hoverAttract", "clickAttract", "mousePos", "attractRadius", "retina", "attractModeDistance", "processAttract", "x", "y", "area", "attractOptions", "modes", "attract", "query", "particles", "quadTree", "particle", "dx", "dy", "distance", "velocity", "speed", "factor", "attractFactor", "easing", "maxSpeed", "normVec", "create", "subFrom", "finish", "count", "clicking"], "mappings": "AAAA,SAASA,MAAT,EAAiBC,SAAjB,EAA4BC,sBAA5B,EAAoDC,MAApD,QAAkE,eAAlE;AACA,SAASC,UAAT,EAAqBC,KAArB,EAA4BC,YAA5B,EAA0CC,SAA1C,QAA2D,gBAA3D;AACA,OAAO,MAAMC,SAAN,SAAwBN,sBAAxB,CAA+C;AAClDO,EAAAA,WAAW,CAACC,SAAD,EAAY;AACnB,UAAMA,SAAN;AACH;;AACDC,EAAAA,SAAS,GAAG;AACR,UAAMD,SAAS,GAAG,KAAKA,SAAvB;AAAA,UAAkCE,OAAO,GAAGF,SAAS,CAACG,aAAtD;AAAA,UAAqEC,KAAK,GAAGJ,SAAS,CAACK,aAAV,CAAwBD,KAArG;AAAA,UAA4GE,MAAM,GAAGJ,OAAO,CAACG,aAAR,CAAsBC,MAA3I;;AACA,QAAI,CAAC,CAACF,KAAK,CAACG,QAAP,IAAmB,CAACD,MAAM,CAACE,OAAP,CAAeC,MAApC,MAAgD,CAACL,KAAK,CAACM,aAAP,IAAwB,CAACJ,MAAM,CAACK,OAAP,CAAeF,MAAxF,CAAJ,EAAqG;AACjG,aAAO,KAAP;AACH;;AACD,UAAMG,SAAS,GAAGN,MAAM,CAACE,OAAP,CAAeK,IAAjC;AAAA,UAAuCC,SAAS,GAAGR,MAAM,CAACK,OAAP,CAAeE,IAAlE;AACA,WAAOhB,SAAS,CAAC,SAAD,EAAYe,SAAZ,CAAT,IAAmCf,SAAS,CAAC,SAAD,EAAYiB,SAAZ,CAAnD;AACH;;AACDC,EAAAA,KAAK,GAAG,CACP;;AACa,QAARC,QAAQ,GAAG;AACb,UAAMhB,SAAS,GAAG,KAAKA,SAAvB;AAAA,UAAkCE,OAAO,GAAGF,SAAS,CAACG,aAAtD;AAAA,UAAqEc,eAAe,GAAGjB,SAAS,CAACK,aAAV,CAAwBa,MAAxB,KAAmC3B,SAAS,CAAC4B,cAApI;AAAA,UAAoJb,MAAM,GAAGJ,OAAO,CAACG,aAAR,CAAsBC,MAAnL;AAAA,UAA2Lc,YAAY,GAAGd,MAAM,CAACE,OAAP,CAAeC,MAAzN;AAAA,UAAiOG,SAAS,GAAGN,MAAM,CAACE,OAAP,CAAeK,IAA5P;AAAA,UAAkQQ,YAAY,GAAGf,MAAM,CAACK,OAAP,CAAeF,MAAhS;AAAA,UAAwSK,SAAS,GAAGR,MAAM,CAACK,OAAP,CAAeE,IAAnU;;AACA,QAAII,eAAe,IAAIG,YAAnB,IAAmCvB,SAAS,CAAC,SAAD,EAAYe,SAAZ,CAAhD,EAAwE;AACpE,WAAKU,YAAL;AACH,KAFD,MAGK,IAAID,YAAY,IAAIxB,SAAS,CAAC,SAAD,EAAYiB,SAAZ,CAA7B,EAAqD;AACtD,WAAKS,YAAL;AACH;AACJ;;AACDD,EAAAA,YAAY,GAAG;AACX,UAAMtB,SAAS,GAAG,KAAKA,SAAvB;AACA,UAAMwB,QAAQ,GAAGxB,SAAS,CAACK,aAAV,CAAwBD,KAAxB,CAA8BG,QAA/C;;AACA,QAAI,CAACiB,QAAL,EAAe;AACX;AACH;;AACD,UAAMC,aAAa,GAAGzB,SAAS,CAAC0B,MAAV,CAAiBC,mBAAvC;AACA,SAAKC,cAAL,CAAoBJ,QAApB,EAA8BC,aAA9B,EAA6C,IAAInC,MAAJ,CAAWkC,QAAQ,CAACK,CAApB,EAAuBL,QAAQ,CAACM,CAAhC,EAAmCL,aAAnC,CAA7C;AACH;;AACDG,EAAAA,cAAc,CAACrB,QAAD,EAAWkB,aAAX,EAA0BM,IAA1B,EAAgC;AAC1C,UAAM/B,SAAS,GAAG,KAAKA,SAAvB;AACA,UAAMgC,cAAc,GAAGhC,SAAS,CAACG,aAAV,CAAwBE,aAAxB,CAAsC4B,KAAtC,CAA4CC,OAAnE;AACA,UAAMC,KAAK,GAAGnC,SAAS,CAACoC,SAAV,CAAoBC,QAApB,CAA6BF,KAA7B,CAAmCJ,IAAnC,CAAd;;AACA,SAAK,MAAMO,QAAX,IAAuBH,KAAvB,EAA8B;AAC1B,YAAM;AAAEI,QAAAA,EAAF;AAAMC,QAAAA,EAAN;AAAUC,QAAAA;AAAV,UAAuB7C,YAAY,CAAC0C,QAAQ,CAAC/B,QAAV,EAAoBA,QAApB,CAAzC;AACA,YAAMmC,QAAQ,GAAGV,cAAc,CAACW,KAAf,GAAuBX,cAAc,CAACY,MAAvD;AACA,YAAMC,aAAa,GAAGlD,KAAK,CAACD,UAAU,CAAC,IAAI+C,QAAQ,GAAGhB,aAAhB,EAA+BO,cAAc,CAACc,MAA9C,CAAV,GAAkEJ,QAAnE,EAA6E,CAA7E,EAAgFV,cAAc,CAACe,QAA/F,CAA3B;AACA,YAAMC,OAAO,GAAGvD,MAAM,CAACwD,MAAP,CAAcR,QAAQ,KAAK,CAAb,GAAiBC,QAAjB,GAA6BH,EAAE,GAAGE,QAAN,GAAkBI,aAA5D,EAA2EJ,QAAQ,KAAK,CAAb,GAAiBC,QAAjB,GAA6BF,EAAE,GAAGC,QAAN,GAAkBI,aAAzH,CAAhB;AACAP,MAAAA,QAAQ,CAAC/B,QAAT,CAAkB2C,OAAlB,CAA0BF,OAA1B;AACH;AACJ;;AACDzB,EAAAA,YAAY,GAAG;AACX,UAAMvB,SAAS,GAAG,KAAKA,SAAvB;;AACA,QAAI,CAACA,SAAS,CAACkC,OAAV,CAAkBiB,MAAvB,EAA+B;AAC3B,UAAI,CAACnD,SAAS,CAACkC,OAAV,CAAkBkB,KAAvB,EAA8B;AAC1BpD,QAAAA,SAAS,CAACkC,OAAV,CAAkBkB,KAAlB,GAA0B,CAA1B;AACH;;AACDpD,MAAAA,SAAS,CAACkC,OAAV,CAAkBkB,KAAlB;;AACA,UAAIpD,SAAS,CAACkC,OAAV,CAAkBkB,KAAlB,KAA4BpD,SAAS,CAACoC,SAAV,CAAoBgB,KAApD,EAA2D;AACvDpD,QAAAA,SAAS,CAACkC,OAAV,CAAkBiB,MAAlB,GAA2B,IAA3B;AACH;AACJ;;AACD,QAAInD,SAAS,CAACkC,OAAV,CAAkBmB,QAAtB,EAAgC;AAC5B,YAAM7B,QAAQ,GAAGxB,SAAS,CAACK,aAAV,CAAwBD,KAAxB,CAA8BM,aAA/C;;AACA,UAAI,CAACc,QAAL,EAAe;AACX;AACH;;AACD,YAAMC,aAAa,GAAGzB,SAAS,CAAC0B,MAAV,CAAiBC,mBAAvC;AACA,WAAKC,cAAL,CAAoBJ,QAApB,EAA8BC,aAA9B,EAA6C,IAAInC,MAAJ,CAAWkC,QAAQ,CAACK,CAApB,EAAuBL,QAAQ,CAACM,CAAhC,EAAmCL,aAAnC,CAA7C;AACH,KAPD,MAQK,IAAIzB,SAAS,CAACkC,OAAV,CAAkBmB,QAAlB,KAA+B,KAAnC,EAA0C;AAC3CrD,MAAAA,SAAS,CAACkC,OAAV,CAAkBE,SAAlB,GAA8B,EAA9B;AACH;;AACD;AACH;;AAnEiD", "sourcesContent": ["import { Circle, Constants, ExternalInteractorBase, Vector } from \"../../../Core\";\nimport { calcEasing, clamp, getDistances, isInArray } from \"../../../Utils\";\nexport class Attractor extends ExternalInteractorBase {\n    constructor(container) {\n        super(container);\n    }\n    isEnabled() {\n        const container = this.container, options = container.actualOptions, mouse = container.interactivity.mouse, events = options.interactivity.events;\n        if ((!mouse.position || !events.onHover.enable) && (!mouse.clickPosition || !events.onClick.enable)) {\n            return false;\n        }\n        const hoverMode = events.onHover.mode, clickMode = events.onClick.mode;\n        return isInArray(\"attract\", hoverMode) || isInArray(\"attract\", clickMode);\n    }\n    reset() {\n    }\n    async interact() {\n        const container = this.container, options = container.actualOptions, mouseMoveStatus = container.interactivity.status === Constants.mouseMoveEvent, events = options.interactivity.events, hoverEnabled = events.onHover.enable, hoverMode = events.onHover.mode, clickEnabled = events.onClick.enable, clickMode = events.onClick.mode;\n        if (mouseMoveStatus && hoverEnabled && isInArray(\"attract\", hoverMode)) {\n            this.hoverAttract();\n        }\n        else if (clickEnabled && isInArray(\"attract\", clickMode)) {\n            this.clickAttract();\n        }\n    }\n    hoverAttract() {\n        const container = this.container;\n        const mousePos = container.interactivity.mouse.position;\n        if (!mousePos) {\n            return;\n        }\n        const attractRadius = container.retina.attractModeDistance;\n        this.processAttract(mousePos, attractRadius, new Circle(mousePos.x, mousePos.y, attractRadius));\n    }\n    processAttract(position, attractRadius, area) {\n        const container = this.container;\n        const attractOptions = container.actualOptions.interactivity.modes.attract;\n        const query = container.particles.quadTree.query(area);\n        for (const particle of query) {\n            const { dx, dy, distance } = getDistances(particle.position, position);\n            const velocity = attractOptions.speed * attractOptions.factor;\n            const attractFactor = clamp(calcEasing(1 - distance / attractRadius, attractOptions.easing) * velocity, 0, attractOptions.maxSpeed);\n            const normVec = Vector.create(distance === 0 ? velocity : (dx / distance) * attractFactor, distance === 0 ? velocity : (dy / distance) * attractFactor);\n            particle.position.subFrom(normVec);\n        }\n    }\n    clickAttract() {\n        const container = this.container;\n        if (!container.attract.finish) {\n            if (!container.attract.count) {\n                container.attract.count = 0;\n            }\n            container.attract.count++;\n            if (container.attract.count === container.particles.count) {\n                container.attract.finish = true;\n            }\n        }\n        if (container.attract.clicking) {\n            const mousePos = container.interactivity.mouse.clickPosition;\n            if (!mousePos) {\n                return;\n            }\n            const attractRadius = container.retina.attractModeDistance;\n            this.processAttract(mousePos, attractRadius, new Circle(mousePos.x, mousePos.y, attractRadius));\n        }\n        else if (container.attract.clicking === false) {\n            container.attract.particles = [];\n        }\n        return;\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
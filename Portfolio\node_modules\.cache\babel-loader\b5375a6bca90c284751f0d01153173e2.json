{"ast": null, "code": "import { TwinkleValues } from \"./TwinkleValues\";\nexport class Twinkle {\n  constructor() {\n    this.lines = new TwinkleValues();\n    this.particles = new TwinkleValues();\n  }\n\n  load(data) {\n    if (data === undefined) {\n      return;\n    }\n\n    this.lines.load(data.lines);\n    this.particles.load(data.particles);\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Options/Classes/Particles/Twinkle/Twinkle.js"], "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "Twinkle", "constructor", "lines", "particles", "load", "data", "undefined"], "mappings": "AAAA,SAASA,aAAT,QAA8B,iBAA9B;AACA,OAAO,MAAMC,OAAN,CAAc;AACjBC,EAAAA,WAAW,GAAG;AACV,SAAKC,KAAL,GAAa,IAAIH,aAAJ,EAAb;AACA,SAAKI,SAAL,GAAiB,IAAIJ,aAAJ,EAAjB;AACH;;AACDK,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,QAAIA,IAAI,KAAKC,SAAb,EAAwB;AACpB;AACH;;AACD,SAAKJ,KAAL,CAAWE,IAAX,CAAgBC,IAAI,CAACH,KAArB;AACA,SAAKC,SAAL,CAAeC,IAAf,CAAoBC,IAAI,CAACF,SAAzB;AACH;;AAXgB", "sourcesContent": ["import { TwinkleValues } from \"./TwinkleValues\";\nexport class Twinkle {\n    constructor() {\n        this.lines = new TwinkleValues();\n        this.particles = new TwinkleValues();\n    }\n    load(data) {\n        if (data === undefined) {\n            return;\n        }\n        this.lines.load(data.lines);\n        this.particles.load(data.particles);\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
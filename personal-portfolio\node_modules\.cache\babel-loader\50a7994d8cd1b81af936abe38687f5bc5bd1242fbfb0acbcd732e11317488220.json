{"ast": null, "code": "import { executeOnSingleOrMultiple, isNumber, itemFromSingleOrMultiple } from \"@tsparticles/engine\";\nimport { AbsorberClickMode } from \"./Enums/AbsorberClickMode.js\";\nimport { AbsorberInstance } from \"./AbsorberInstance.js\";\nconst defaultIndex = 0;\nexport class Absorbers {\n  constructor(container) {\n    this.container = container;\n    this.array = [];\n    this.absorbers = [];\n    this.interactivityAbsorbers = [];\n    container.getAbsorber = idxOrName => idxOrName === undefined || isNumber(idxOrName) ? this.array[idxOrName ?? defaultIndex] : this.array.find(t => t.name === idxOrName);\n    container.addAbsorber = async (options, position) => this.addAbsorber(options, position);\n  }\n  async addAbsorber(options, position) {\n    const absorber = new AbsorberInstance(this, this.container, options, position);\n    this.array.push(absorber);\n    return Promise.resolve(absorber);\n  }\n  draw(context) {\n    for (const absorber of this.array) {\n      absorber.draw(context);\n    }\n  }\n  handleClickMode(mode) {\n    const absorberOptions = this.absorbers,\n      modeAbsorbers = this.interactivityAbsorbers;\n    if (mode === AbsorberClickMode.absorber) {\n      const absorbersModeOptions = itemFromSingleOrMultiple(modeAbsorbers),\n        absorbersOptions = absorbersModeOptions ?? itemFromSingleOrMultiple(absorberOptions),\n        aPosition = this.container.interactivity.mouse.clickPosition;\n      void this.addAbsorber(absorbersOptions, aPosition);\n    }\n  }\n  async init() {\n    this.absorbers = this.container.actualOptions.absorbers;\n    this.interactivityAbsorbers = this.container.actualOptions.interactivity.modes.absorbers;\n    const promises = executeOnSingleOrMultiple(this.absorbers, async absorber => {\n      await this.addAbsorber(absorber);\n    });\n    if (promises instanceof Array) {\n      await Promise.all(promises);\n    } else {\n      await promises;\n    }\n  }\n  particleUpdate(particle) {\n    for (const absorber of this.array) {\n      absorber.attract(particle);\n      if (particle.destroyed) {\n        break;\n      }\n    }\n  }\n  removeAbsorber(absorber) {\n    const index = this.array.indexOf(absorber),\n      deleteCount = 1;\n    if (index >= defaultIndex) {\n      this.array.splice(index, deleteCount);\n    }\n  }\n  resize() {\n    for (const absorber of this.array) {\n      absorber.resize();\n    }\n  }\n  stop() {\n    this.array = [];\n  }\n}", "map": {"version": 3, "names": ["executeOnSingleOrMultiple", "isNumber", "itemFromSingleOrMultiple", "AbsorberClickMode", "AbsorberInstance", "defaultIndex", "Absorbers", "constructor", "container", "array", "absorbers", "interactivityAbsorbers", "getAbsorber", "idxOrName", "undefined", "find", "t", "name", "addAbsorber", "options", "position", "absorber", "push", "Promise", "resolve", "draw", "context", "handleClickMode", "mode", "absorberOptions", "modeAbsorbers", "absorbersModeOptions", "absorbersOptions", "aPosition", "interactivity", "mouse", "clickPosition", "init", "actualOptions", "modes", "promises", "Array", "all", "particleUpdate", "particle", "attract", "destroyed", "removeAbsorber", "index", "indexOf", "deleteCount", "splice", "resize", "stop"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/plugin-absorbers/browser/Absorbers.js"], "sourcesContent": ["import { executeOnSingleOrMultiple, isNumber, itemFromSingleOrMultiple, } from \"@tsparticles/engine\";\nimport { AbsorberClickMode } from \"./Enums/AbsorberClickMode.js\";\nimport { AbsorberInstance } from \"./AbsorberInstance.js\";\nconst defaultIndex = 0;\nexport class Absorbers {\n    constructor(container) {\n        this.container = container;\n        this.array = [];\n        this.absorbers = [];\n        this.interactivityAbsorbers = [];\n        container.getAbsorber = (idxOrName) => idxOrName === undefined || isNumber(idxOrName)\n            ? this.array[idxOrName ?? defaultIndex]\n            : this.array.find(t => t.name === idxOrName);\n        container.addAbsorber = async (options, position) => this.addAbsorber(options, position);\n    }\n    async addAbsorber(options, position) {\n        const absorber = new AbsorberInstance(this, this.container, options, position);\n        this.array.push(absorber);\n        return Promise.resolve(absorber);\n    }\n    draw(context) {\n        for (const absorber of this.array) {\n            absorber.draw(context);\n        }\n    }\n    handleClickMode(mode) {\n        const absorberOptions = this.absorbers, modeAbsorbers = this.interactivityAbsorbers;\n        if (mode === AbsorberClickMode.absorber) {\n            const absorbersModeOptions = itemFromSingleOrMultiple(modeAbsorbers), absorbersOptions = absorbersModeOptions ?? itemFromSingleOrMultiple(absorberOptions), aPosition = this.container.interactivity.mouse.clickPosition;\n            void this.addAbsorber(absorbersOptions, aPosition);\n        }\n    }\n    async init() {\n        this.absorbers = this.container.actualOptions.absorbers;\n        this.interactivityAbsorbers = this.container.actualOptions.interactivity.modes.absorbers;\n        const promises = executeOnSingleOrMultiple(this.absorbers, async (absorber) => {\n            await this.addAbsorber(absorber);\n        });\n        if (promises instanceof Array) {\n            await Promise.all(promises);\n        }\n        else {\n            await promises;\n        }\n    }\n    particleUpdate(particle) {\n        for (const absorber of this.array) {\n            absorber.attract(particle);\n            if (particle.destroyed) {\n                break;\n            }\n        }\n    }\n    removeAbsorber(absorber) {\n        const index = this.array.indexOf(absorber), deleteCount = 1;\n        if (index >= defaultIndex) {\n            this.array.splice(index, deleteCount);\n        }\n    }\n    resize() {\n        for (const absorber of this.array) {\n            absorber.resize();\n        }\n    }\n    stop() {\n        this.array = [];\n    }\n}\n"], "mappings": "AAAA,SAASA,yBAAyB,EAAEC,QAAQ,EAAEC,wBAAwB,QAAS,qBAAqB;AACpG,SAASC,iBAAiB,QAAQ,8BAA8B;AAChE,SAASC,gBAAgB,QAAQ,uBAAuB;AACxD,MAAMC,YAAY,GAAG,CAAC;AACtB,OAAO,MAAMC,SAAS,CAAC;EACnBC,WAAWA,CAACC,SAAS,EAAE;IACnB,IAAI,CAACA,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,KAAK,GAAG,EAAE;IACf,IAAI,CAACC,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,sBAAsB,GAAG,EAAE;IAChCH,SAAS,CAACI,WAAW,GAAIC,SAAS,IAAKA,SAAS,KAAKC,SAAS,IAAIb,QAAQ,CAACY,SAAS,CAAC,GAC/E,IAAI,CAACJ,KAAK,CAACI,SAAS,IAAIR,YAAY,CAAC,GACrC,IAAI,CAACI,KAAK,CAACM,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,KAAKJ,SAAS,CAAC;IAChDL,SAAS,CAACU,WAAW,GAAG,OAAOC,OAAO,EAAEC,QAAQ,KAAK,IAAI,CAACF,WAAW,CAACC,OAAO,EAAEC,QAAQ,CAAC;EAC5F;EACA,MAAMF,WAAWA,CAACC,OAAO,EAAEC,QAAQ,EAAE;IACjC,MAAMC,QAAQ,GAAG,IAAIjB,gBAAgB,CAAC,IAAI,EAAE,IAAI,CAACI,SAAS,EAAEW,OAAO,EAAEC,QAAQ,CAAC;IAC9E,IAAI,CAACX,KAAK,CAACa,IAAI,CAACD,QAAQ,CAAC;IACzB,OAAOE,OAAO,CAACC,OAAO,CAACH,QAAQ,CAAC;EACpC;EACAI,IAAIA,CAACC,OAAO,EAAE;IACV,KAAK,MAAML,QAAQ,IAAI,IAAI,CAACZ,KAAK,EAAE;MAC/BY,QAAQ,CAACI,IAAI,CAACC,OAAO,CAAC;IAC1B;EACJ;EACAC,eAAeA,CAACC,IAAI,EAAE;IAClB,MAAMC,eAAe,GAAG,IAAI,CAACnB,SAAS;MAAEoB,aAAa,GAAG,IAAI,CAACnB,sBAAsB;IACnF,IAAIiB,IAAI,KAAKzB,iBAAiB,CAACkB,QAAQ,EAAE;MACrC,MAAMU,oBAAoB,GAAG7B,wBAAwB,CAAC4B,aAAa,CAAC;QAAEE,gBAAgB,GAAGD,oBAAoB,IAAI7B,wBAAwB,CAAC2B,eAAe,CAAC;QAAEI,SAAS,GAAG,IAAI,CAACzB,SAAS,CAAC0B,aAAa,CAACC,KAAK,CAACC,aAAa;MACxN,KAAK,IAAI,CAAClB,WAAW,CAACc,gBAAgB,EAAEC,SAAS,CAAC;IACtD;EACJ;EACA,MAAMI,IAAIA,CAAA,EAAG;IACT,IAAI,CAAC3B,SAAS,GAAG,IAAI,CAACF,SAAS,CAAC8B,aAAa,CAAC5B,SAAS;IACvD,IAAI,CAACC,sBAAsB,GAAG,IAAI,CAACH,SAAS,CAAC8B,aAAa,CAACJ,aAAa,CAACK,KAAK,CAAC7B,SAAS;IACxF,MAAM8B,QAAQ,GAAGxC,yBAAyB,CAAC,IAAI,CAACU,SAAS,EAAE,MAAOW,QAAQ,IAAK;MAC3E,MAAM,IAAI,CAACH,WAAW,CAACG,QAAQ,CAAC;IACpC,CAAC,CAAC;IACF,IAAImB,QAAQ,YAAYC,KAAK,EAAE;MAC3B,MAAMlB,OAAO,CAACmB,GAAG,CAACF,QAAQ,CAAC;IAC/B,CAAC,MACI;MACD,MAAMA,QAAQ;IAClB;EACJ;EACAG,cAAcA,CAACC,QAAQ,EAAE;IACrB,KAAK,MAAMvB,QAAQ,IAAI,IAAI,CAACZ,KAAK,EAAE;MAC/BY,QAAQ,CAACwB,OAAO,CAACD,QAAQ,CAAC;MAC1B,IAAIA,QAAQ,CAACE,SAAS,EAAE;QACpB;MACJ;IACJ;EACJ;EACAC,cAAcA,CAAC1B,QAAQ,EAAE;IACrB,MAAM2B,KAAK,GAAG,IAAI,CAACvC,KAAK,CAACwC,OAAO,CAAC5B,QAAQ,CAAC;MAAE6B,WAAW,GAAG,CAAC;IAC3D,IAAIF,KAAK,IAAI3C,YAAY,EAAE;MACvB,IAAI,CAACI,KAAK,CAAC0C,MAAM,CAACH,KAAK,EAAEE,WAAW,CAAC;IACzC;EACJ;EACAE,MAAMA,CAAA,EAAG;IACL,KAAK,MAAM/B,QAAQ,IAAI,IAAI,CAACZ,KAAK,EAAE;MAC/BY,QAAQ,CAAC+B,MAAM,CAAC,CAAC;IACrB;EACJ;EACAC,IAAIA,CAAA,EAAG;IACH,IAAI,CAAC5C,KAAK,GAAG,EAAE;EACnB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { RotateDirection, clamp, getDistance, getDistances, getRandom, getRangeValue } from \"@tsparticles/engine\";\nconst half = 0.5,\n  minVelocity = 0,\n  identity = 1,\n  moveSpeedFactor = 60,\n  minSpinRadius = 0,\n  spinFactor = 0.01;\nexport function applyDistance(particle) {\n  const initialPosition = particle.initialPosition,\n    {\n      dx,\n      dy\n    } = getDistances(initialPosition, particle.position),\n    dxFixed = Math.abs(dx),\n    dyFixed = Math.abs(dy),\n    {\n      maxDistance\n    } = particle.retina,\n    hDistance = maxDistance.horizontal,\n    vDistance = maxDistance.vertical;\n  if (!hDistance && !vDistance) {\n    return;\n  }\n  const hasHDistance = (hDistance && dxFixed >= hDistance) ?? false,\n    hasVDistance = (vDistance && dyFixed >= vDistance) ?? false;\n  if ((hasHDistance || hasVDistance) && !particle.misplaced) {\n    particle.misplaced = !!hDistance && dxFixed > hDistance || !!vDistance && dyFixed > vDistance;\n    if (hDistance) {\n      particle.velocity.x = particle.velocity.y * half - particle.velocity.x;\n    }\n    if (vDistance) {\n      particle.velocity.y = particle.velocity.x * half - particle.velocity.y;\n    }\n  } else if ((!hDistance || dxFixed < hDistance) && (!vDistance || dyFixed < vDistance) && particle.misplaced) {\n    particle.misplaced = false;\n  } else if (particle.misplaced) {\n    const pos = particle.position,\n      vel = particle.velocity;\n    if (hDistance && (pos.x < initialPosition.x && vel.x < minVelocity || pos.x > initialPosition.x && vel.x > minVelocity)) {\n      vel.x *= -getRandom();\n    }\n    if (vDistance && (pos.y < initialPosition.y && vel.y < minVelocity || pos.y > initialPosition.y && vel.y > minVelocity)) {\n      vel.y *= -getRandom();\n    }\n  }\n}\nexport function move(particle, moveOptions, moveSpeed, maxSpeed, moveDrift, delta) {\n  applyPath(particle, delta);\n  const gravityOptions = particle.gravity,\n    gravityFactor = gravityOptions?.enable && gravityOptions.inverse ? -identity : identity;\n  if (moveDrift && moveSpeed) {\n    particle.velocity.x += moveDrift * delta.factor / (moveSpeedFactor * moveSpeed);\n  }\n  if (gravityOptions?.enable && moveSpeed) {\n    particle.velocity.y += gravityFactor * (gravityOptions.acceleration * delta.factor) / (moveSpeedFactor * moveSpeed);\n  }\n  const decay = particle.moveDecay;\n  particle.velocity.multTo(decay);\n  const velocity = particle.velocity.mult(moveSpeed);\n  if (gravityOptions?.enable && maxSpeed > minVelocity && (!gravityOptions.inverse && velocity.y >= minVelocity && velocity.y >= maxSpeed || gravityOptions.inverse && velocity.y <= minVelocity && velocity.y <= -maxSpeed)) {\n    velocity.y = gravityFactor * maxSpeed;\n    if (moveSpeed) {\n      particle.velocity.y = velocity.y / moveSpeed;\n    }\n  }\n  const zIndexOptions = particle.options.zIndex,\n    zVelocityFactor = (identity - particle.zIndexFactor) ** zIndexOptions.velocityRate;\n  velocity.multTo(zVelocityFactor);\n  const {\n    position\n  } = particle;\n  position.addTo(velocity);\n  if (moveOptions.vibrate) {\n    position.x += Math.sin(position.x * Math.cos(position.y));\n    position.y += Math.cos(position.y * Math.sin(position.x));\n  }\n}\nexport function spin(particle, moveSpeed) {\n  const container = particle.container;\n  if (!particle.spin) {\n    return;\n  }\n  const updateFunc = {\n    x: particle.spin.direction === RotateDirection.clockwise ? Math.cos : Math.sin,\n    y: particle.spin.direction === RotateDirection.clockwise ? Math.sin : Math.cos\n  };\n  particle.position.x = particle.spin.center.x + particle.spin.radius * updateFunc.x(particle.spin.angle);\n  particle.position.y = particle.spin.center.y + particle.spin.radius * updateFunc.y(particle.spin.angle);\n  particle.spin.radius += particle.spin.acceleration;\n  const maxCanvasSize = Math.max(container.canvas.size.width, container.canvas.size.height),\n    halfMaxSize = maxCanvasSize * half;\n  if (particle.spin.radius > halfMaxSize) {\n    particle.spin.radius = halfMaxSize;\n    particle.spin.acceleration *= -identity;\n  } else if (particle.spin.radius < minSpinRadius) {\n    particle.spin.radius = minSpinRadius;\n    particle.spin.acceleration *= -identity;\n  }\n  particle.spin.angle += moveSpeed * spinFactor * (identity - particle.spin.radius / maxCanvasSize);\n}\nexport function applyPath(particle, delta) {\n  const particlesOptions = particle.options,\n    pathOptions = particlesOptions.move.path,\n    pathEnabled = pathOptions.enable;\n  if (!pathEnabled) {\n    return;\n  }\n  if (particle.lastPathTime <= particle.pathDelay) {\n    particle.lastPathTime += delta.value;\n    return;\n  }\n  const path = particle.pathGenerator?.generate(particle, delta);\n  if (path) {\n    particle.velocity.addTo(path);\n  }\n  if (pathOptions.clamp) {\n    particle.velocity.x = clamp(particle.velocity.x, -identity, identity);\n    particle.velocity.y = clamp(particle.velocity.y, -identity, identity);\n  }\n  particle.lastPathTime -= particle.pathDelay;\n}\nexport function getProximitySpeedFactor(particle) {\n  return particle.slow.inRange ? particle.slow.factor : identity;\n}\nexport function initSpin(particle) {\n  const container = particle.container,\n    options = particle.options,\n    spinOptions = options.move.spin;\n  if (!spinOptions.enable) {\n    return;\n  }\n  const spinPos = spinOptions.position ?? {\n      x: 50,\n      y: 50\n    },\n    spinFactor = 0.01,\n    spinCenter = {\n      x: spinPos.x * spinFactor * container.canvas.size.width,\n      y: spinPos.y * spinFactor * container.canvas.size.height\n    },\n    pos = particle.getPosition(),\n    distance = getDistance(pos, spinCenter),\n    spinAcceleration = getRangeValue(spinOptions.acceleration);\n  particle.retina.spinAcceleration = spinAcceleration * container.retina.pixelRatio;\n  const minVelocity = 0;\n  particle.spin = {\n    center: spinCenter,\n    direction: particle.velocity.x >= minVelocity ? RotateDirection.clockwise : RotateDirection.counterClockwise,\n    angle: particle.velocity.angle,\n    radius: distance,\n    acceleration: particle.retina.spinAcceleration\n  };\n}", "map": {"version": 3, "names": ["RotateDirection", "clamp", "getDistance", "getDistances", "getRandom", "getRangeValue", "half", "minVelocity", "identity", "moveSpeedFactor", "minSpinRadius", "spinFactor", "applyDistance", "particle", "initialPosition", "dx", "dy", "position", "dxFixed", "Math", "abs", "dyFixed", "maxDistance", "retina", "hDistance", "horizontal", "vDistance", "vertical", "hasHDistance", "hasVDistance", "misplaced", "velocity", "x", "y", "pos", "vel", "move", "moveOptions", "moveSpeed", "maxSpeed", "moveDrift", "delta", "applyPath", "gravityOptions", "gravity", "gravityFactor", "enable", "inverse", "factor", "acceleration", "decay", "moveDecay", "multTo", "mult", "zIndexOptions", "options", "zIndex", "zVelocityFactor", "zIndexFactor", "velocityRate", "addTo", "vibrate", "sin", "cos", "spin", "container", "updateFunc", "direction", "clockwise", "center", "radius", "angle", "maxCanvasSize", "max", "canvas", "size", "width", "height", "halfMaxSize", "particlesOptions", "pathOptions", "path", "pathEnabled", "lastPathTime", "pathDelay", "value", "pathGenerator", "generate", "getProximitySpeedFactor", "slow", "inRange", "initSpin", "spinOptions", "spinPos", "spinCenter", "getPosition", "distance", "spinAcceleration", "pixelRatio", "counterClockwise"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/move-base/browser/Utils.js"], "sourcesContent": ["import { RotateDirection, clamp, getDistance, getDistances, getRandom, getRangeValue, } from \"@tsparticles/engine\";\nconst half = 0.5, minVelocity = 0, identity = 1, moveSpeedFactor = 60, minSpinRadius = 0, spinFactor = 0.01;\nexport function applyDistance(particle) {\n    const initialPosition = particle.initialPosition, { dx, dy } = getDistances(initialPosition, particle.position), dxFixed = Math.abs(dx), dyFixed = Math.abs(dy), { maxDistance } = particle.retina, hDistance = maxDistance.horizontal, vDistance = maxDistance.vertical;\n    if (!hDistance && !vDistance) {\n        return;\n    }\n    const hasHDistance = (hDistance && dxFixed >= hDistance) ?? false, hasVDistance = (vDistance && dyFixed >= vDistance) ?? false;\n    if ((hasHDistance || hasVDistance) && !particle.misplaced) {\n        particle.misplaced = (!!hDistance && dxFixed > hDistance) || (!!vDistance && dyFixed > vDistance);\n        if (hDistance) {\n            particle.velocity.x = particle.velocity.y * half - particle.velocity.x;\n        }\n        if (vDistance) {\n            particle.velocity.y = particle.velocity.x * half - particle.velocity.y;\n        }\n    }\n    else if ((!hDistance || dxFixed < hDistance) && (!vDistance || dyFixed < vDistance) && particle.misplaced) {\n        particle.misplaced = false;\n    }\n    else if (particle.misplaced) {\n        const pos = particle.position, vel = particle.velocity;\n        if (hDistance &&\n            ((pos.x < initialPosition.x && vel.x < minVelocity) || (pos.x > initialPosition.x && vel.x > minVelocity))) {\n            vel.x *= -getRandom();\n        }\n        if (vDistance &&\n            ((pos.y < initialPosition.y && vel.y < minVelocity) || (pos.y > initialPosition.y && vel.y > minVelocity))) {\n            vel.y *= -getRandom();\n        }\n    }\n}\nexport function move(particle, moveOptions, moveSpeed, maxSpeed, moveDrift, delta) {\n    applyPath(particle, delta);\n    const gravityOptions = particle.gravity, gravityFactor = gravityOptions?.enable && gravityOptions.inverse ? -identity : identity;\n    if (moveDrift && moveSpeed) {\n        particle.velocity.x += (moveDrift * delta.factor) / (moveSpeedFactor * moveSpeed);\n    }\n    if (gravityOptions?.enable && moveSpeed) {\n        particle.velocity.y +=\n            (gravityFactor * (gravityOptions.acceleration * delta.factor)) / (moveSpeedFactor * moveSpeed);\n    }\n    const decay = particle.moveDecay;\n    particle.velocity.multTo(decay);\n    const velocity = particle.velocity.mult(moveSpeed);\n    if (gravityOptions?.enable &&\n        maxSpeed > minVelocity &&\n        ((!gravityOptions.inverse && velocity.y >= minVelocity && velocity.y >= maxSpeed) ||\n            (gravityOptions.inverse && velocity.y <= minVelocity && velocity.y <= -maxSpeed))) {\n        velocity.y = gravityFactor * maxSpeed;\n        if (moveSpeed) {\n            particle.velocity.y = velocity.y / moveSpeed;\n        }\n    }\n    const zIndexOptions = particle.options.zIndex, zVelocityFactor = (identity - particle.zIndexFactor) ** zIndexOptions.velocityRate;\n    velocity.multTo(zVelocityFactor);\n    const { position } = particle;\n    position.addTo(velocity);\n    if (moveOptions.vibrate) {\n        position.x += Math.sin(position.x * Math.cos(position.y));\n        position.y += Math.cos(position.y * Math.sin(position.x));\n    }\n}\nexport function spin(particle, moveSpeed) {\n    const container = particle.container;\n    if (!particle.spin) {\n        return;\n    }\n    const updateFunc = {\n        x: particle.spin.direction === RotateDirection.clockwise ? Math.cos : Math.sin,\n        y: particle.spin.direction === RotateDirection.clockwise ? Math.sin : Math.cos,\n    };\n    particle.position.x = particle.spin.center.x + particle.spin.radius * updateFunc.x(particle.spin.angle);\n    particle.position.y = particle.spin.center.y + particle.spin.radius * updateFunc.y(particle.spin.angle);\n    particle.spin.radius += particle.spin.acceleration;\n    const maxCanvasSize = Math.max(container.canvas.size.width, container.canvas.size.height), halfMaxSize = maxCanvasSize * half;\n    if (particle.spin.radius > halfMaxSize) {\n        particle.spin.radius = halfMaxSize;\n        particle.spin.acceleration *= -identity;\n    }\n    else if (particle.spin.radius < minSpinRadius) {\n        particle.spin.radius = minSpinRadius;\n        particle.spin.acceleration *= -identity;\n    }\n    particle.spin.angle += moveSpeed * spinFactor * (identity - particle.spin.radius / maxCanvasSize);\n}\nexport function applyPath(particle, delta) {\n    const particlesOptions = particle.options, pathOptions = particlesOptions.move.path, pathEnabled = pathOptions.enable;\n    if (!pathEnabled) {\n        return;\n    }\n    if (particle.lastPathTime <= particle.pathDelay) {\n        particle.lastPathTime += delta.value;\n        return;\n    }\n    const path = particle.pathGenerator?.generate(particle, delta);\n    if (path) {\n        particle.velocity.addTo(path);\n    }\n    if (pathOptions.clamp) {\n        particle.velocity.x = clamp(particle.velocity.x, -identity, identity);\n        particle.velocity.y = clamp(particle.velocity.y, -identity, identity);\n    }\n    particle.lastPathTime -= particle.pathDelay;\n}\nexport function getProximitySpeedFactor(particle) {\n    return particle.slow.inRange ? particle.slow.factor : identity;\n}\nexport function initSpin(particle) {\n    const container = particle.container, options = particle.options, spinOptions = options.move.spin;\n    if (!spinOptions.enable) {\n        return;\n    }\n    const spinPos = spinOptions.position ?? { x: 50, y: 50 }, spinFactor = 0.01, spinCenter = {\n        x: spinPos.x * spinFactor * container.canvas.size.width,\n        y: spinPos.y * spinFactor * container.canvas.size.height,\n    }, pos = particle.getPosition(), distance = getDistance(pos, spinCenter), spinAcceleration = getRangeValue(spinOptions.acceleration);\n    particle.retina.spinAcceleration = spinAcceleration * container.retina.pixelRatio;\n    const minVelocity = 0;\n    particle.spin = {\n        center: spinCenter,\n        direction: particle.velocity.x >= minVelocity ? RotateDirection.clockwise : RotateDirection.counterClockwise,\n        angle: particle.velocity.angle,\n        radius: distance,\n        acceleration: particle.retina.spinAcceleration,\n    };\n}\n"], "mappings": "AAAA,SAASA,eAAe,EAAEC,KAAK,EAAEC,WAAW,EAAEC,YAAY,EAAEC,SAAS,EAAEC,aAAa,QAAS,qBAAqB;AAClH,MAAMC,IAAI,GAAG,GAAG;EAAEC,WAAW,GAAG,CAAC;EAAEC,QAAQ,GAAG,CAAC;EAAEC,eAAe,GAAG,EAAE;EAAEC,aAAa,GAAG,CAAC;EAAEC,UAAU,GAAG,IAAI;AAC3G,OAAO,SAASC,aAAaA,CAACC,QAAQ,EAAE;EACpC,MAAMC,eAAe,GAAGD,QAAQ,CAACC,eAAe;IAAE;MAAEC,EAAE;MAAEC;IAAG,CAAC,GAAGb,YAAY,CAACW,eAAe,EAAED,QAAQ,CAACI,QAAQ,CAAC;IAAEC,OAAO,GAAGC,IAAI,CAACC,GAAG,CAACL,EAAE,CAAC;IAAEM,OAAO,GAAGF,IAAI,CAACC,GAAG,CAACJ,EAAE,CAAC;IAAE;MAAEM;IAAY,CAAC,GAAGT,QAAQ,CAACU,MAAM;IAAEC,SAAS,GAAGF,WAAW,CAACG,UAAU;IAAEC,SAAS,GAAGJ,WAAW,CAACK,QAAQ;EACxQ,IAAI,CAACH,SAAS,IAAI,CAACE,SAAS,EAAE;IAC1B;EACJ;EACA,MAAME,YAAY,GAAG,CAACJ,SAAS,IAAIN,OAAO,IAAIM,SAAS,KAAK,KAAK;IAAEK,YAAY,GAAG,CAACH,SAAS,IAAIL,OAAO,IAAIK,SAAS,KAAK,KAAK;EAC9H,IAAI,CAACE,YAAY,IAAIC,YAAY,KAAK,CAAChB,QAAQ,CAACiB,SAAS,EAAE;IACvDjB,QAAQ,CAACiB,SAAS,GAAI,CAAC,CAACN,SAAS,IAAIN,OAAO,GAAGM,SAAS,IAAM,CAAC,CAACE,SAAS,IAAIL,OAAO,GAAGK,SAAU;IACjG,IAAIF,SAAS,EAAE;MACXX,QAAQ,CAACkB,QAAQ,CAACC,CAAC,GAAGnB,QAAQ,CAACkB,QAAQ,CAACE,CAAC,GAAG3B,IAAI,GAAGO,QAAQ,CAACkB,QAAQ,CAACC,CAAC;IAC1E;IACA,IAAIN,SAAS,EAAE;MACXb,QAAQ,CAACkB,QAAQ,CAACE,CAAC,GAAGpB,QAAQ,CAACkB,QAAQ,CAACC,CAAC,GAAG1B,IAAI,GAAGO,QAAQ,CAACkB,QAAQ,CAACE,CAAC;IAC1E;EACJ,CAAC,MACI,IAAI,CAAC,CAACT,SAAS,IAAIN,OAAO,GAAGM,SAAS,MAAM,CAACE,SAAS,IAAIL,OAAO,GAAGK,SAAS,CAAC,IAAIb,QAAQ,CAACiB,SAAS,EAAE;IACvGjB,QAAQ,CAACiB,SAAS,GAAG,KAAK;EAC9B,CAAC,MACI,IAAIjB,QAAQ,CAACiB,SAAS,EAAE;IACzB,MAAMI,GAAG,GAAGrB,QAAQ,CAACI,QAAQ;MAAEkB,GAAG,GAAGtB,QAAQ,CAACkB,QAAQ;IACtD,IAAIP,SAAS,KACPU,GAAG,CAACF,CAAC,GAAGlB,eAAe,CAACkB,CAAC,IAAIG,GAAG,CAACH,CAAC,GAAGzB,WAAW,IAAM2B,GAAG,CAACF,CAAC,GAAGlB,eAAe,CAACkB,CAAC,IAAIG,GAAG,CAACH,CAAC,GAAGzB,WAAY,CAAC,EAAE;MAC5G4B,GAAG,CAACH,CAAC,IAAI,CAAC5B,SAAS,CAAC,CAAC;IACzB;IACA,IAAIsB,SAAS,KACPQ,GAAG,CAACD,CAAC,GAAGnB,eAAe,CAACmB,CAAC,IAAIE,GAAG,CAACF,CAAC,GAAG1B,WAAW,IAAM2B,GAAG,CAACD,CAAC,GAAGnB,eAAe,CAACmB,CAAC,IAAIE,GAAG,CAACF,CAAC,GAAG1B,WAAY,CAAC,EAAE;MAC5G4B,GAAG,CAACF,CAAC,IAAI,CAAC7B,SAAS,CAAC,CAAC;IACzB;EACJ;AACJ;AACA,OAAO,SAASgC,IAAIA,CAACvB,QAAQ,EAAEwB,WAAW,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,KAAK,EAAE;EAC/EC,SAAS,CAAC7B,QAAQ,EAAE4B,KAAK,CAAC;EAC1B,MAAME,cAAc,GAAG9B,QAAQ,CAAC+B,OAAO;IAAEC,aAAa,GAAGF,cAAc,EAAEG,MAAM,IAAIH,cAAc,CAACI,OAAO,GAAG,CAACvC,QAAQ,GAAGA,QAAQ;EAChI,IAAIgC,SAAS,IAAIF,SAAS,EAAE;IACxBzB,QAAQ,CAACkB,QAAQ,CAACC,CAAC,IAAKQ,SAAS,GAAGC,KAAK,CAACO,MAAM,IAAKvC,eAAe,GAAG6B,SAAS,CAAC;EACrF;EACA,IAAIK,cAAc,EAAEG,MAAM,IAAIR,SAAS,EAAE;IACrCzB,QAAQ,CAACkB,QAAQ,CAACE,CAAC,IACdY,aAAa,IAAIF,cAAc,CAACM,YAAY,GAAGR,KAAK,CAACO,MAAM,CAAC,IAAKvC,eAAe,GAAG6B,SAAS,CAAC;EACtG;EACA,MAAMY,KAAK,GAAGrC,QAAQ,CAACsC,SAAS;EAChCtC,QAAQ,CAACkB,QAAQ,CAACqB,MAAM,CAACF,KAAK,CAAC;EAC/B,MAAMnB,QAAQ,GAAGlB,QAAQ,CAACkB,QAAQ,CAACsB,IAAI,CAACf,SAAS,CAAC;EAClD,IAAIK,cAAc,EAAEG,MAAM,IACtBP,QAAQ,GAAGhC,WAAW,KACpB,CAACoC,cAAc,CAACI,OAAO,IAAIhB,QAAQ,CAACE,CAAC,IAAI1B,WAAW,IAAIwB,QAAQ,CAACE,CAAC,IAAIM,QAAQ,IAC3EI,cAAc,CAACI,OAAO,IAAIhB,QAAQ,CAACE,CAAC,IAAI1B,WAAW,IAAIwB,QAAQ,CAACE,CAAC,IAAI,CAACM,QAAS,CAAC,EAAE;IACvFR,QAAQ,CAACE,CAAC,GAAGY,aAAa,GAAGN,QAAQ;IACrC,IAAID,SAAS,EAAE;MACXzB,QAAQ,CAACkB,QAAQ,CAACE,CAAC,GAAGF,QAAQ,CAACE,CAAC,GAAGK,SAAS;IAChD;EACJ;EACA,MAAMgB,aAAa,GAAGzC,QAAQ,CAAC0C,OAAO,CAACC,MAAM;IAAEC,eAAe,GAAG,CAACjD,QAAQ,GAAGK,QAAQ,CAAC6C,YAAY,KAAKJ,aAAa,CAACK,YAAY;EACjI5B,QAAQ,CAACqB,MAAM,CAACK,eAAe,CAAC;EAChC,MAAM;IAAExC;EAAS,CAAC,GAAGJ,QAAQ;EAC7BI,QAAQ,CAAC2C,KAAK,CAAC7B,QAAQ,CAAC;EACxB,IAAIM,WAAW,CAACwB,OAAO,EAAE;IACrB5C,QAAQ,CAACe,CAAC,IAAIb,IAAI,CAAC2C,GAAG,CAAC7C,QAAQ,CAACe,CAAC,GAAGb,IAAI,CAAC4C,GAAG,CAAC9C,QAAQ,CAACgB,CAAC,CAAC,CAAC;IACzDhB,QAAQ,CAACgB,CAAC,IAAId,IAAI,CAAC4C,GAAG,CAAC9C,QAAQ,CAACgB,CAAC,GAAGd,IAAI,CAAC2C,GAAG,CAAC7C,QAAQ,CAACe,CAAC,CAAC,CAAC;EAC7D;AACJ;AACA,OAAO,SAASgC,IAAIA,CAACnD,QAAQ,EAAEyB,SAAS,EAAE;EACtC,MAAM2B,SAAS,GAAGpD,QAAQ,CAACoD,SAAS;EACpC,IAAI,CAACpD,QAAQ,CAACmD,IAAI,EAAE;IAChB;EACJ;EACA,MAAME,UAAU,GAAG;IACflC,CAAC,EAAEnB,QAAQ,CAACmD,IAAI,CAACG,SAAS,KAAKnE,eAAe,CAACoE,SAAS,GAAGjD,IAAI,CAAC4C,GAAG,GAAG5C,IAAI,CAAC2C,GAAG;IAC9E7B,CAAC,EAAEpB,QAAQ,CAACmD,IAAI,CAACG,SAAS,KAAKnE,eAAe,CAACoE,SAAS,GAAGjD,IAAI,CAAC2C,GAAG,GAAG3C,IAAI,CAAC4C;EAC/E,CAAC;EACDlD,QAAQ,CAACI,QAAQ,CAACe,CAAC,GAAGnB,QAAQ,CAACmD,IAAI,CAACK,MAAM,CAACrC,CAAC,GAAGnB,QAAQ,CAACmD,IAAI,CAACM,MAAM,GAAGJ,UAAU,CAAClC,CAAC,CAACnB,QAAQ,CAACmD,IAAI,CAACO,KAAK,CAAC;EACvG1D,QAAQ,CAACI,QAAQ,CAACgB,CAAC,GAAGpB,QAAQ,CAACmD,IAAI,CAACK,MAAM,CAACpC,CAAC,GAAGpB,QAAQ,CAACmD,IAAI,CAACM,MAAM,GAAGJ,UAAU,CAACjC,CAAC,CAACpB,QAAQ,CAACmD,IAAI,CAACO,KAAK,CAAC;EACvG1D,QAAQ,CAACmD,IAAI,CAACM,MAAM,IAAIzD,QAAQ,CAACmD,IAAI,CAACf,YAAY;EAClD,MAAMuB,aAAa,GAAGrD,IAAI,CAACsD,GAAG,CAACR,SAAS,CAACS,MAAM,CAACC,IAAI,CAACC,KAAK,EAAEX,SAAS,CAACS,MAAM,CAACC,IAAI,CAACE,MAAM,CAAC;IAAEC,WAAW,GAAGN,aAAa,GAAGlE,IAAI;EAC7H,IAAIO,QAAQ,CAACmD,IAAI,CAACM,MAAM,GAAGQ,WAAW,EAAE;IACpCjE,QAAQ,CAACmD,IAAI,CAACM,MAAM,GAAGQ,WAAW;IAClCjE,QAAQ,CAACmD,IAAI,CAACf,YAAY,IAAI,CAACzC,QAAQ;EAC3C,CAAC,MACI,IAAIK,QAAQ,CAACmD,IAAI,CAACM,MAAM,GAAG5D,aAAa,EAAE;IAC3CG,QAAQ,CAACmD,IAAI,CAACM,MAAM,GAAG5D,aAAa;IACpCG,QAAQ,CAACmD,IAAI,CAACf,YAAY,IAAI,CAACzC,QAAQ;EAC3C;EACAK,QAAQ,CAACmD,IAAI,CAACO,KAAK,IAAIjC,SAAS,GAAG3B,UAAU,IAAIH,QAAQ,GAAGK,QAAQ,CAACmD,IAAI,CAACM,MAAM,GAAGE,aAAa,CAAC;AACrG;AACA,OAAO,SAAS9B,SAASA,CAAC7B,QAAQ,EAAE4B,KAAK,EAAE;EACvC,MAAMsC,gBAAgB,GAAGlE,QAAQ,CAAC0C,OAAO;IAAEyB,WAAW,GAAGD,gBAAgB,CAAC3C,IAAI,CAAC6C,IAAI;IAAEC,WAAW,GAAGF,WAAW,CAAClC,MAAM;EACrH,IAAI,CAACoC,WAAW,EAAE;IACd;EACJ;EACA,IAAIrE,QAAQ,CAACsE,YAAY,IAAItE,QAAQ,CAACuE,SAAS,EAAE;IAC7CvE,QAAQ,CAACsE,YAAY,IAAI1C,KAAK,CAAC4C,KAAK;IACpC;EACJ;EACA,MAAMJ,IAAI,GAAGpE,QAAQ,CAACyE,aAAa,EAAEC,QAAQ,CAAC1E,QAAQ,EAAE4B,KAAK,CAAC;EAC9D,IAAIwC,IAAI,EAAE;IACNpE,QAAQ,CAACkB,QAAQ,CAAC6B,KAAK,CAACqB,IAAI,CAAC;EACjC;EACA,IAAID,WAAW,CAAC/E,KAAK,EAAE;IACnBY,QAAQ,CAACkB,QAAQ,CAACC,CAAC,GAAG/B,KAAK,CAACY,QAAQ,CAACkB,QAAQ,CAACC,CAAC,EAAE,CAACxB,QAAQ,EAAEA,QAAQ,CAAC;IACrEK,QAAQ,CAACkB,QAAQ,CAACE,CAAC,GAAGhC,KAAK,CAACY,QAAQ,CAACkB,QAAQ,CAACE,CAAC,EAAE,CAACzB,QAAQ,EAAEA,QAAQ,CAAC;EACzE;EACAK,QAAQ,CAACsE,YAAY,IAAItE,QAAQ,CAACuE,SAAS;AAC/C;AACA,OAAO,SAASI,uBAAuBA,CAAC3E,QAAQ,EAAE;EAC9C,OAAOA,QAAQ,CAAC4E,IAAI,CAACC,OAAO,GAAG7E,QAAQ,CAAC4E,IAAI,CAACzC,MAAM,GAAGxC,QAAQ;AAClE;AACA,OAAO,SAASmF,QAAQA,CAAC9E,QAAQ,EAAE;EAC/B,MAAMoD,SAAS,GAAGpD,QAAQ,CAACoD,SAAS;IAAEV,OAAO,GAAG1C,QAAQ,CAAC0C,OAAO;IAAEqC,WAAW,GAAGrC,OAAO,CAACnB,IAAI,CAAC4B,IAAI;EACjG,IAAI,CAAC4B,WAAW,CAAC9C,MAAM,EAAE;IACrB;EACJ;EACA,MAAM+C,OAAO,GAAGD,WAAW,CAAC3E,QAAQ,IAAI;MAAEe,CAAC,EAAE,EAAE;MAAEC,CAAC,EAAE;IAAG,CAAC;IAAEtB,UAAU,GAAG,IAAI;IAAEmF,UAAU,GAAG;MACtF9D,CAAC,EAAE6D,OAAO,CAAC7D,CAAC,GAAGrB,UAAU,GAAGsD,SAAS,CAACS,MAAM,CAACC,IAAI,CAACC,KAAK;MACvD3C,CAAC,EAAE4D,OAAO,CAAC5D,CAAC,GAAGtB,UAAU,GAAGsD,SAAS,CAACS,MAAM,CAACC,IAAI,CAACE;IACtD,CAAC;IAAE3C,GAAG,GAAGrB,QAAQ,CAACkF,WAAW,CAAC,CAAC;IAAEC,QAAQ,GAAG9F,WAAW,CAACgC,GAAG,EAAE4D,UAAU,CAAC;IAAEG,gBAAgB,GAAG5F,aAAa,CAACuF,WAAW,CAAC3C,YAAY,CAAC;EACpIpC,QAAQ,CAACU,MAAM,CAAC0E,gBAAgB,GAAGA,gBAAgB,GAAGhC,SAAS,CAAC1C,MAAM,CAAC2E,UAAU;EACjF,MAAM3F,WAAW,GAAG,CAAC;EACrBM,QAAQ,CAACmD,IAAI,GAAG;IACZK,MAAM,EAAEyB,UAAU;IAClB3B,SAAS,EAAEtD,QAAQ,CAACkB,QAAQ,CAACC,CAAC,IAAIzB,WAAW,GAAGP,eAAe,CAACoE,SAAS,GAAGpE,eAAe,CAACmG,gBAAgB;IAC5G5B,KAAK,EAAE1D,QAAQ,CAACkB,QAAQ,CAACwC,KAAK;IAC9BD,MAAM,EAAE0B,QAAQ;IAChB/C,YAAY,EAAEpC,QAAQ,CAACU,MAAM,CAAC0E;EAClC,CAAC;AACL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { Attractor } from \"./Attractor\";\nexport async function loadExternalAttractInteraction(engine) {\n  await engine.addInteractor(\"externalAttract\", container => new Attractor(container));\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Interactions/External/Attract/index.js"], "names": ["Attractor", "loadExternalAttractInteraction", "engine", "addInteractor", "container"], "mappings": "AAAA,SAASA,SAAT,QAA0B,aAA1B;AACA,OAAO,eAAeC,8BAAf,CAA8CC,MAA9C,EAAsD;AACzD,QAAMA,MAAM,CAACC,aAAP,CAAqB,iBAArB,EAAyCC,SAAD,IAAe,IAAIJ,SAAJ,CAAcI,SAAd,CAAvD,CAAN;AACH", "sourcesContent": ["import { Attractor } from \"./Attractor\";\nexport async function loadExternalAttractInteraction(engine) {\n    await engine.addInteractor(\"externalAttract\", (container) => new Attractor(container));\n}\n"]}, "metadata": {}, "sourceType": "module"}
{"ast": null, "code": "import { getRandom, getRangeValue } from \"tsparticles-engine\";\nimport { Rotate } from \"./Options/Classes/Rotate\";\nfunction updateRotate(particle, delta) {\n  const rotate = particle.rotate,\n    rotateOptions = particle.options.rotate;\n  if (!rotate || !rotateOptions) {\n    return;\n  }\n  const rotateAnimation = rotateOptions.animation,\n    speed = (rotate.velocity ?? 0) * delta.factor,\n    max = 2 * Math.PI,\n    decay = rotate.decay ?? 1;\n  if (!rotateAnimation.enable) {\n    return;\n  }\n  switch (rotate.status) {\n    case \"increasing\":\n      rotate.value += speed;\n      if (rotate.value > max) {\n        rotate.value -= max;\n      }\n      break;\n    case \"decreasing\":\n    default:\n      rotate.value -= speed;\n      if (rotate.value < 0) {\n        rotate.value += max;\n      }\n      break;\n  }\n  if (rotate.velocity && decay !== 1) {\n    rotate.velocity *= decay;\n  }\n}\nexport class RotateUpdater {\n  constructor(container) {\n    this.container = container;\n  }\n  init(particle) {\n    const rotateOptions = particle.options.rotate;\n    if (!rotateOptions) {\n      return;\n    }\n    particle.rotate = {\n      enable: rotateOptions.animation.enable,\n      value: getRangeValue(rotateOptions.value) * Math.PI / 180\n    };\n    particle.pathRotation = rotateOptions.path;\n    let rotateDirection = rotateOptions.direction;\n    if (rotateDirection === \"random\") {\n      const index = Math.floor(getRandom() * 2);\n      rotateDirection = index > 0 ? \"counter-clockwise\" : \"clockwise\";\n    }\n    switch (rotateDirection) {\n      case \"counter-clockwise\":\n      case \"counterClockwise\":\n        particle.rotate.status = \"decreasing\";\n        break;\n      case \"clockwise\":\n        particle.rotate.status = \"increasing\";\n        break;\n    }\n    const rotateAnimation = rotateOptions.animation;\n    if (rotateAnimation.enable) {\n      particle.rotate.decay = 1 - getRangeValue(rotateAnimation.decay);\n      particle.rotate.velocity = getRangeValue(rotateAnimation.speed) / 360 * this.container.retina.reduceFactor;\n      if (!rotateAnimation.sync) {\n        particle.rotate.velocity *= getRandom();\n      }\n    }\n    particle.rotation = particle.rotate.value;\n  }\n  isEnabled(particle) {\n    const rotate = particle.options.rotate;\n    if (!rotate) {\n      return false;\n    }\n    return !particle.destroyed && !particle.spawning && rotate.animation.enable && !rotate.path;\n  }\n  loadOptions(options, ...sources) {\n    if (!options.rotate) {\n      options.rotate = new Rotate();\n    }\n    for (const source of sources) {\n      options.rotate.load(source?.rotate);\n    }\n  }\n  update(particle, delta) {\n    if (!this.isEnabled(particle)) {\n      return;\n    }\n    updateRotate(particle, delta);\n    particle.rotation = particle.rotate?.value ?? 0;\n  }\n}", "map": {"version": 3, "names": ["getRandom", "getRangeValue", "Rotate", "updateRotate", "particle", "delta", "rotate", "rotateOptions", "options", "rotateAnimation", "animation", "speed", "velocity", "factor", "max", "Math", "PI", "decay", "enable", "status", "value", "RotateUpdater", "constructor", "container", "init", "pathRotation", "path", "rotateDirection", "direction", "index", "floor", "retina", "reduceFactor", "sync", "rotation", "isEnabled", "destroyed", "spawning", "loadOptions", "sources", "source", "load", "update"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/tsparticles-updater-rotate/esm/RotateUpdater.js"], "sourcesContent": ["import { getRandom, getRangeValue, } from \"tsparticles-engine\";\nimport { Rotate } from \"./Options/Classes/Rotate\";\nfunction updateRotate(particle, delta) {\n    const rotate = particle.rotate, rotateOptions = particle.options.rotate;\n    if (!rotate || !rotateOptions) {\n        return;\n    }\n    const rotateAnimation = rotateOptions.animation, speed = (rotate.velocity ?? 0) * delta.factor, max = 2 * Math.PI, decay = rotate.decay ?? 1;\n    if (!rotateAnimation.enable) {\n        return;\n    }\n    switch (rotate.status) {\n        case \"increasing\":\n            rotate.value += speed;\n            if (rotate.value > max) {\n                rotate.value -= max;\n            }\n            break;\n        case \"decreasing\":\n        default:\n            rotate.value -= speed;\n            if (rotate.value < 0) {\n                rotate.value += max;\n            }\n            break;\n    }\n    if (rotate.velocity && decay !== 1) {\n        rotate.velocity *= decay;\n    }\n}\nexport class RotateUpdater {\n    constructor(container) {\n        this.container = container;\n    }\n    init(particle) {\n        const rotateOptions = particle.options.rotate;\n        if (!rotateOptions) {\n            return;\n        }\n        particle.rotate = {\n            enable: rotateOptions.animation.enable,\n            value: (getRangeValue(rotateOptions.value) * Math.PI) / 180,\n        };\n        particle.pathRotation = rotateOptions.path;\n        let rotateDirection = rotateOptions.direction;\n        if (rotateDirection === \"random\") {\n            const index = Math.floor(getRandom() * 2);\n            rotateDirection = index > 0 ? \"counter-clockwise\" : \"clockwise\";\n        }\n        switch (rotateDirection) {\n            case \"counter-clockwise\":\n            case \"counterClockwise\":\n                particle.rotate.status = \"decreasing\";\n                break;\n            case \"clockwise\":\n                particle.rotate.status = \"increasing\";\n                break;\n        }\n        const rotateAnimation = rotateOptions.animation;\n        if (rotateAnimation.enable) {\n            particle.rotate.decay = 1 - getRangeValue(rotateAnimation.decay);\n            particle.rotate.velocity =\n                (getRangeValue(rotateAnimation.speed) / 360) * this.container.retina.reduceFactor;\n            if (!rotateAnimation.sync) {\n                particle.rotate.velocity *= getRandom();\n            }\n        }\n        particle.rotation = particle.rotate.value;\n    }\n    isEnabled(particle) {\n        const rotate = particle.options.rotate;\n        if (!rotate) {\n            return false;\n        }\n        return !particle.destroyed && !particle.spawning && rotate.animation.enable && !rotate.path;\n    }\n    loadOptions(options, ...sources) {\n        if (!options.rotate) {\n            options.rotate = new Rotate();\n        }\n        for (const source of sources) {\n            options.rotate.load(source?.rotate);\n        }\n    }\n    update(particle, delta) {\n        if (!this.isEnabled(particle)) {\n            return;\n        }\n        updateRotate(particle, delta);\n        particle.rotation = particle.rotate?.value ?? 0;\n    }\n}\n"], "mappings": "AAAA,SAASA,SAAS,EAAEC,aAAa,QAAS,oBAAoB;AAC9D,SAASC,MAAM,QAAQ,0BAA0B;AACjD,SAASC,YAAYA,CAACC,QAAQ,EAAEC,KAAK,EAAE;EACnC,MAAMC,MAAM,GAAGF,QAAQ,CAACE,MAAM;IAAEC,aAAa,GAAGH,QAAQ,CAACI,OAAO,CAACF,MAAM;EACvE,IAAI,CAACA,MAAM,IAAI,CAACC,aAAa,EAAE;IAC3B;EACJ;EACA,MAAME,eAAe,GAAGF,aAAa,CAACG,SAAS;IAAEC,KAAK,GAAG,CAACL,MAAM,CAACM,QAAQ,IAAI,CAAC,IAAIP,KAAK,CAACQ,MAAM;IAAEC,GAAG,GAAG,CAAC,GAAGC,IAAI,CAACC,EAAE;IAAEC,KAAK,GAAGX,MAAM,CAACW,KAAK,IAAI,CAAC;EAC5I,IAAI,CAACR,eAAe,CAACS,MAAM,EAAE;IACzB;EACJ;EACA,QAAQZ,MAAM,CAACa,MAAM;IACjB,KAAK,YAAY;MACbb,MAAM,CAACc,KAAK,IAAIT,KAAK;MACrB,IAAIL,MAAM,CAACc,KAAK,GAAGN,GAAG,EAAE;QACpBR,MAAM,CAACc,KAAK,IAAIN,GAAG;MACvB;MACA;IACJ,KAAK,YAAY;IACjB;MACIR,MAAM,CAACc,KAAK,IAAIT,KAAK;MACrB,IAAIL,MAAM,CAACc,KAAK,GAAG,CAAC,EAAE;QAClBd,MAAM,CAACc,KAAK,IAAIN,GAAG;MACvB;MACA;EACR;EACA,IAAIR,MAAM,CAACM,QAAQ,IAAIK,KAAK,KAAK,CAAC,EAAE;IAChCX,MAAM,CAACM,QAAQ,IAAIK,KAAK;EAC5B;AACJ;AACA,OAAO,MAAMI,aAAa,CAAC;EACvBC,WAAWA,CAACC,SAAS,EAAE;IACnB,IAAI,CAACA,SAAS,GAAGA,SAAS;EAC9B;EACAC,IAAIA,CAACpB,QAAQ,EAAE;IACX,MAAMG,aAAa,GAAGH,QAAQ,CAACI,OAAO,CAACF,MAAM;IAC7C,IAAI,CAACC,aAAa,EAAE;MAChB;IACJ;IACAH,QAAQ,CAACE,MAAM,GAAG;MACdY,MAAM,EAAEX,aAAa,CAACG,SAAS,CAACQ,MAAM;MACtCE,KAAK,EAAGnB,aAAa,CAACM,aAAa,CAACa,KAAK,CAAC,GAAGL,IAAI,CAACC,EAAE,GAAI;IAC5D,CAAC;IACDZ,QAAQ,CAACqB,YAAY,GAAGlB,aAAa,CAACmB,IAAI;IAC1C,IAAIC,eAAe,GAAGpB,aAAa,CAACqB,SAAS;IAC7C,IAAID,eAAe,KAAK,QAAQ,EAAE;MAC9B,MAAME,KAAK,GAAGd,IAAI,CAACe,KAAK,CAAC9B,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC;MACzC2B,eAAe,GAAGE,KAAK,GAAG,CAAC,GAAG,mBAAmB,GAAG,WAAW;IACnE;IACA,QAAQF,eAAe;MACnB,KAAK,mBAAmB;MACxB,KAAK,kBAAkB;QACnBvB,QAAQ,CAACE,MAAM,CAACa,MAAM,GAAG,YAAY;QACrC;MACJ,KAAK,WAAW;QACZf,QAAQ,CAACE,MAAM,CAACa,MAAM,GAAG,YAAY;QACrC;IACR;IACA,MAAMV,eAAe,GAAGF,aAAa,CAACG,SAAS;IAC/C,IAAID,eAAe,CAACS,MAAM,EAAE;MACxBd,QAAQ,CAACE,MAAM,CAACW,KAAK,GAAG,CAAC,GAAGhB,aAAa,CAACQ,eAAe,CAACQ,KAAK,CAAC;MAChEb,QAAQ,CAACE,MAAM,CAACM,QAAQ,GACnBX,aAAa,CAACQ,eAAe,CAACE,KAAK,CAAC,GAAG,GAAG,GAAI,IAAI,CAACY,SAAS,CAACQ,MAAM,CAACC,YAAY;MACrF,IAAI,CAACvB,eAAe,CAACwB,IAAI,EAAE;QACvB7B,QAAQ,CAACE,MAAM,CAACM,QAAQ,IAAIZ,SAAS,CAAC,CAAC;MAC3C;IACJ;IACAI,QAAQ,CAAC8B,QAAQ,GAAG9B,QAAQ,CAACE,MAAM,CAACc,KAAK;EAC7C;EACAe,SAASA,CAAC/B,QAAQ,EAAE;IAChB,MAAME,MAAM,GAAGF,QAAQ,CAACI,OAAO,CAACF,MAAM;IACtC,IAAI,CAACA,MAAM,EAAE;MACT,OAAO,KAAK;IAChB;IACA,OAAO,CAACF,QAAQ,CAACgC,SAAS,IAAI,CAAChC,QAAQ,CAACiC,QAAQ,IAAI/B,MAAM,CAACI,SAAS,CAACQ,MAAM,IAAI,CAACZ,MAAM,CAACoB,IAAI;EAC/F;EACAY,WAAWA,CAAC9B,OAAO,EAAE,GAAG+B,OAAO,EAAE;IAC7B,IAAI,CAAC/B,OAAO,CAACF,MAAM,EAAE;MACjBE,OAAO,CAACF,MAAM,GAAG,IAAIJ,MAAM,CAAC,CAAC;IACjC;IACA,KAAK,MAAMsC,MAAM,IAAID,OAAO,EAAE;MAC1B/B,OAAO,CAACF,MAAM,CAACmC,IAAI,CAACD,MAAM,EAAElC,MAAM,CAAC;IACvC;EACJ;EACAoC,MAAMA,CAACtC,QAAQ,EAAEC,KAAK,EAAE;IACpB,IAAI,CAAC,IAAI,CAAC8B,SAAS,CAAC/B,QAAQ,CAAC,EAAE;MAC3B;IACJ;IACAD,YAAY,CAACC,QAAQ,EAAEC,KAAK,CAAC;IAC7BD,QAAQ,CAAC8B,QAAQ,GAAG9B,QAAQ,CAACE,MAAM,EAAEc,KAAK,IAAI,CAAC;EACnD;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
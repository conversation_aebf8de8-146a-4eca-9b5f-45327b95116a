{"ast": null, "code": "import { Range } from \"./Range\";\nimport { getDistance } from \"../../Utils\";\nexport class Circle extends Range {\n  constructor(x, y, radius) {\n    super(x, y);\n    this.radius = radius;\n  }\n\n  contains(point) {\n    return getDistance(point, this.position) <= this.radius;\n  }\n\n  intersects(range) {\n    const rect = range;\n    const circle = range;\n    const pos1 = this.position;\n    const pos2 = range.position;\n    const xDist = Math.abs(pos2.x - pos1.x);\n    const yDist = Math.abs(pos2.y - pos1.y);\n    const r = this.radius;\n\n    if (circle.radius !== undefined) {\n      const rSum = r + circle.radius;\n      const dist = Math.sqrt(xDist * xDist + yDist + yDist);\n      return rSum > dist;\n    } else if (rect.size !== undefined) {\n      const w = rect.size.width;\n      const h = rect.size.height;\n      const edges = Math.pow(xDist - w, 2) + Math.pow(yDist - h, 2);\n\n      if (xDist > r + w || yDist > r + h) {\n        return false;\n      }\n\n      if (xDist <= w || yDist <= h) {\n        return true;\n      }\n\n      return edges <= r * r;\n    }\n\n    return false;\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Core/Utils/Circle.js"], "names": ["Range", "getDistance", "Circle", "constructor", "x", "y", "radius", "contains", "point", "position", "intersects", "range", "rect", "circle", "pos1", "pos2", "xDist", "Math", "abs", "yDist", "r", "undefined", "rSum", "dist", "sqrt", "size", "w", "width", "h", "height", "edges", "pow"], "mappings": "AAAA,SAASA,KAAT,QAAsB,SAAtB;AACA,SAASC,WAAT,QAA4B,aAA5B;AACA,OAAO,MAAMC,MAAN,SAAqBF,KAArB,CAA2B;AAC9BG,EAAAA,WAAW,CAACC,CAAD,EAAIC,CAAJ,EAAOC,MAAP,EAAe;AACtB,UAAMF,CAAN,EAASC,CAAT;AACA,SAAKC,MAAL,GAAcA,MAAd;AACH;;AACDC,EAAAA,QAAQ,CAACC,KAAD,EAAQ;AACZ,WAAOP,WAAW,CAACO,KAAD,EAAQ,KAAKC,QAAb,CAAX,IAAqC,KAAKH,MAAjD;AACH;;AACDI,EAAAA,UAAU,CAACC,KAAD,EAAQ;AACd,UAAMC,IAAI,GAAGD,KAAb;AACA,UAAME,MAAM,GAAGF,KAAf;AACA,UAAMG,IAAI,GAAG,KAAKL,QAAlB;AACA,UAAMM,IAAI,GAAGJ,KAAK,CAACF,QAAnB;AACA,UAAMO,KAAK,GAAGC,IAAI,CAACC,GAAL,CAASH,IAAI,CAACX,CAAL,GAASU,IAAI,CAACV,CAAvB,CAAd;AACA,UAAMe,KAAK,GAAGF,IAAI,CAACC,GAAL,CAASH,IAAI,CAACV,CAAL,GAASS,IAAI,CAACT,CAAvB,CAAd;AACA,UAAMe,CAAC,GAAG,KAAKd,MAAf;;AACA,QAAIO,MAAM,CAACP,MAAP,KAAkBe,SAAtB,EAAiC;AAC7B,YAAMC,IAAI,GAAGF,CAAC,GAAGP,MAAM,CAACP,MAAxB;AACA,YAAMiB,IAAI,GAAGN,IAAI,CAACO,IAAL,CAAUR,KAAK,GAAGA,KAAR,GAAgBG,KAAhB,GAAwBA,KAAlC,CAAb;AACA,aAAOG,IAAI,GAAGC,IAAd;AACH,KAJD,MAKK,IAAIX,IAAI,CAACa,IAAL,KAAcJ,SAAlB,EAA6B;AAC9B,YAAMK,CAAC,GAAGd,IAAI,CAACa,IAAL,CAAUE,KAApB;AACA,YAAMC,CAAC,GAAGhB,IAAI,CAACa,IAAL,CAAUI,MAApB;AACA,YAAMC,KAAK,GAAGb,IAAI,CAACc,GAAL,CAASf,KAAK,GAAGU,CAAjB,EAAoB,CAApB,IAAyBT,IAAI,CAACc,GAAL,CAASZ,KAAK,GAAGS,CAAjB,EAAoB,CAApB,CAAvC;;AACA,UAAIZ,KAAK,GAAGI,CAAC,GAAGM,CAAZ,IAAiBP,KAAK,GAAGC,CAAC,GAAGQ,CAAjC,EAAoC;AAChC,eAAO,KAAP;AACH;;AACD,UAAIZ,KAAK,IAAIU,CAAT,IAAcP,KAAK,IAAIS,CAA3B,EAA8B;AAC1B,eAAO,IAAP;AACH;;AACD,aAAOE,KAAK,IAAIV,CAAC,GAAGA,CAApB;AACH;;AACD,WAAO,KAAP;AACH;;AAlC6B", "sourcesContent": ["import { Range } from \"./Range\";\nimport { getDistance } from \"../../Utils\";\nexport class Circle extends Range {\n    constructor(x, y, radius) {\n        super(x, y);\n        this.radius = radius;\n    }\n    contains(point) {\n        return getDistance(point, this.position) <= this.radius;\n    }\n    intersects(range) {\n        const rect = range;\n        const circle = range;\n        const pos1 = this.position;\n        const pos2 = range.position;\n        const xDist = Math.abs(pos2.x - pos1.x);\n        const yDist = Math.abs(pos2.y - pos1.y);\n        const r = this.radius;\n        if (circle.radius !== undefined) {\n            const rSum = r + circle.radius;\n            const dist = Math.sqrt(xDist * xDist + yDist + yDist);\n            return rSum > dist;\n        }\n        else if (rect.size !== undefined) {\n            const w = rect.size.width;\n            const h = rect.size.height;\n            const edges = Math.pow(xDist - w, 2) + Math.pow(yDist - h, 2);\n            if (xDist > r + w || yDist > r + h) {\n                return false;\n            }\n            if (xDist <= w || yDist <= h) {\n                return true;\n            }\n            return edges <= r * r;\n        }\n        return false;\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
{"ast": null, "code": "import { ExternalInteractorBase, isDivModeEnabled, isInArray, mouseMoveEvent } from \"@tsparticles/engine\";\nimport { divBounce, mouseBounce } from \"./Utils.js\";\nimport { Bounce } from \"./Options/Classes/Bounce.js\";\nconst bounceMode = \"bounce\";\nexport class <PERSON><PERSON><PERSON> extends ExternalInteractorBase {\n  constructor(container) {\n    super(container);\n  }\n  clear() {}\n  init() {\n    const container = this.container,\n      bounce = container.actualOptions.interactivity.modes.bounce;\n    if (!bounce) {\n      return;\n    }\n    container.retina.bounceModeDistance = bounce.distance * container.retina.pixelRatio;\n  }\n  interact() {\n    const container = this.container,\n      options = container.actualOptions,\n      events = options.interactivity.events,\n      mouseMoveStatus = container.interactivity.status === mouseMoveEvent,\n      hoverEnabled = events.onHover.enable,\n      hoverMode = events.onHover.mode,\n      divs = events.onDiv;\n    if (mouseMoveStatus && hoverEnabled && isInArray(bounceMode, hoverMode)) {\n      mouseBounce(this.container, p => this.isEnabled(p));\n    } else {\n      divBounce(this.container, divs, bounceMode, p => this.isEnabled(p));\n    }\n  }\n  isEnabled(particle) {\n    const container = this.container,\n      options = container.actualOptions,\n      mouse = container.interactivity.mouse,\n      events = (particle?.interactivity ?? options.interactivity).events,\n      divs = events.onDiv;\n    return !!mouse.position && events.onHover.enable && isInArray(bounceMode, events.onHover.mode) || isDivModeEnabled(bounceMode, divs);\n  }\n  loadModeOptions(options, ...sources) {\n    if (!options.bounce) {\n      options.bounce = new Bounce();\n    }\n    for (const source of sources) {\n      options.bounce.load(source?.bounce);\n    }\n  }\n  reset() {}\n}", "map": {"version": 3, "names": ["ExternalInteractorBase", "isDivModeEnabled", "isInArray", "mouseMoveEvent", "divBounce", "mouseBounce", "<PERSON><PERSON><PERSON>", "bounceMode", "<PERSON><PERSON><PERSON>", "constructor", "container", "clear", "init", "bounce", "actualOptions", "interactivity", "modes", "retina", "bounceModeDistance", "distance", "pixelRatio", "interact", "options", "events", "mouseMoveStatus", "status", "hoverEnabled", "onHover", "enable", "hoverMode", "mode", "divs", "onDiv", "p", "isEnabled", "particle", "mouse", "position", "loadModeOptions", "sources", "source", "load", "reset"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/interaction-external-bounce/browser/Bouncer.js"], "sourcesContent": ["import { ExternalInteractorBase, isDivModeEnabled, isInArray, mouseMoveEvent, } from \"@tsparticles/engine\";\nimport { divBounce, mouseBounce } from \"./Utils.js\";\nimport { Bounce } from \"./Options/Classes/Bounce.js\";\nconst bounceMode = \"bounce\";\nexport class <PERSON><PERSON><PERSON> extends ExternalInteractorBase {\n    constructor(container) {\n        super(container);\n    }\n    clear() {\n    }\n    init() {\n        const container = this.container, bounce = container.actualOptions.interactivity.modes.bounce;\n        if (!bounce) {\n            return;\n        }\n        container.retina.bounceModeDistance = bounce.distance * container.retina.pixelRatio;\n    }\n    interact() {\n        const container = this.container, options = container.actualOptions, events = options.interactivity.events, mouseMoveStatus = container.interactivity.status === mouseMoveEvent, hoverEnabled = events.onHover.enable, hoverMode = events.onHover.mode, divs = events.onDiv;\n        if (mouseMoveStatus && hoverEnabled && isInArray(bounceMode, hoverMode)) {\n            mouseBounce(this.container, p => this.isEnabled(p));\n        }\n        else {\n            divBounce(this.container, divs, bounceMode, p => this.isEnabled(p));\n        }\n    }\n    isEnabled(particle) {\n        const container = this.container, options = container.actualOptions, mouse = container.interactivity.mouse, events = (particle?.interactivity ?? options.interactivity).events, divs = events.onDiv;\n        return ((!!mouse.position && events.onHover.enable && isInArray(bounceMode, events.onHover.mode)) ||\n            isDivModeEnabled(bounceMode, divs));\n    }\n    loadModeOptions(options, ...sources) {\n        if (!options.bounce) {\n            options.bounce = new Bounce();\n        }\n        for (const source of sources) {\n            options.bounce.load(source?.bounce);\n        }\n    }\n    reset() {\n    }\n}\n"], "mappings": "AAAA,SAASA,sBAAsB,EAAEC,gBAAgB,EAAEC,SAAS,EAAEC,cAAc,QAAS,qBAAqB;AAC1G,SAASC,SAAS,EAAEC,WAAW,QAAQ,YAAY;AACnD,SAASC,MAAM,QAAQ,6BAA6B;AACpD,MAAMC,UAAU,GAAG,QAAQ;AAC3B,OAAO,MAAMC,OAAO,SAASR,sBAAsB,CAAC;EAChDS,WAAWA,CAACC,SAAS,EAAE;IACnB,KAAK,CAACA,SAAS,CAAC;EACpB;EACAC,KAAKA,CAAA,EAAG,CACR;EACAC,IAAIA,CAAA,EAAG;IACH,MAAMF,SAAS,GAAG,IAAI,CAACA,SAAS;MAAEG,MAAM,GAAGH,SAAS,CAACI,aAAa,CAACC,aAAa,CAACC,KAAK,CAACH,MAAM;IAC7F,IAAI,CAACA,MAAM,EAAE;MACT;IACJ;IACAH,SAAS,CAACO,MAAM,CAACC,kBAAkB,GAAGL,MAAM,CAACM,QAAQ,GAAGT,SAAS,CAACO,MAAM,CAACG,UAAU;EACvF;EACAC,QAAQA,CAAA,EAAG;IACP,MAAMX,SAAS,GAAG,IAAI,CAACA,SAAS;MAAEY,OAAO,GAAGZ,SAAS,CAACI,aAAa;MAAES,MAAM,GAAGD,OAAO,CAACP,aAAa,CAACQ,MAAM;MAAEC,eAAe,GAAGd,SAAS,CAACK,aAAa,CAACU,MAAM,KAAKtB,cAAc;MAAEuB,YAAY,GAAGH,MAAM,CAACI,OAAO,CAACC,MAAM;MAAEC,SAAS,GAAGN,MAAM,CAACI,OAAO,CAACG,IAAI;MAAEC,IAAI,GAAGR,MAAM,CAACS,KAAK;IAC3Q,IAAIR,eAAe,IAAIE,YAAY,IAAIxB,SAAS,CAACK,UAAU,EAAEsB,SAAS,CAAC,EAAE;MACrExB,WAAW,CAAC,IAAI,CAACK,SAAS,EAAEuB,CAAC,IAAI,IAAI,CAACC,SAAS,CAACD,CAAC,CAAC,CAAC;IACvD,CAAC,MACI;MACD7B,SAAS,CAAC,IAAI,CAACM,SAAS,EAAEqB,IAAI,EAAExB,UAAU,EAAE0B,CAAC,IAAI,IAAI,CAACC,SAAS,CAACD,CAAC,CAAC,CAAC;IACvE;EACJ;EACAC,SAASA,CAACC,QAAQ,EAAE;IAChB,MAAMzB,SAAS,GAAG,IAAI,CAACA,SAAS;MAAEY,OAAO,GAAGZ,SAAS,CAACI,aAAa;MAAEsB,KAAK,GAAG1B,SAAS,CAACK,aAAa,CAACqB,KAAK;MAAEb,MAAM,GAAG,CAACY,QAAQ,EAAEpB,aAAa,IAAIO,OAAO,CAACP,aAAa,EAAEQ,MAAM;MAAEQ,IAAI,GAAGR,MAAM,CAACS,KAAK;IACnM,OAAS,CAAC,CAACI,KAAK,CAACC,QAAQ,IAAId,MAAM,CAACI,OAAO,CAACC,MAAM,IAAI1B,SAAS,CAACK,UAAU,EAAEgB,MAAM,CAACI,OAAO,CAACG,IAAI,CAAC,IAC5F7B,gBAAgB,CAACM,UAAU,EAAEwB,IAAI,CAAC;EAC1C;EACAO,eAAeA,CAAChB,OAAO,EAAE,GAAGiB,OAAO,EAAE;IACjC,IAAI,CAACjB,OAAO,CAACT,MAAM,EAAE;MACjBS,OAAO,CAACT,MAAM,GAAG,IAAIP,MAAM,CAAC,CAAC;IACjC;IACA,KAAK,MAAMkC,MAAM,IAAID,OAAO,EAAE;MAC1BjB,OAAO,CAACT,MAAM,CAAC4B,IAAI,CAACD,MAAM,EAAE3B,MAAM,CAAC;IACvC;EACJ;EACA6B,KAAKA,CAAA,EAAG,CACR;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { deepExtend } from \"../../Utils\";\nexport class ManualParticle {\n  load(data) {\n    var _a, _b;\n\n    if (!data) {\n      return;\n    }\n\n    if (data.position !== undefined) {\n      this.position = {\n        x: (_a = data.position.x) !== null && _a !== void 0 ? _a : 50,\n        y: (_b = data.position.y) !== null && _b !== void 0 ? _b : 50\n      };\n    }\n\n    if (data.options !== undefined) {\n      this.options = deepExtend({}, data.options);\n    }\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Options/Classes/ManualParticle.js"], "names": ["deepExtend", "ManualParticle", "load", "data", "_a", "_b", "position", "undefined", "x", "y", "options"], "mappings": "AAAA,SAASA,UAAT,QAA2B,aAA3B;AACA,OAAO,MAAMC,cAAN,CAAqB;AACxBC,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,QAAIC,EAAJ,EAAQC,EAAR;;AACA,QAAI,CAACF,IAAL,EAAW;AACP;AACH;;AACD,QAAIA,IAAI,CAACG,QAAL,KAAkBC,SAAtB,EAAiC;AAC7B,WAAKD,QAAL,GAAgB;AACZE,QAAAA,CAAC,EAAE,CAACJ,EAAE,GAAGD,IAAI,CAACG,QAAL,CAAcE,CAApB,MAA2B,IAA3B,IAAmCJ,EAAE,KAAK,KAAK,CAA/C,GAAmDA,EAAnD,GAAwD,EAD/C;AAEZK,QAAAA,CAAC,EAAE,CAACJ,EAAE,GAAGF,IAAI,CAACG,QAAL,CAAcG,CAApB,MAA2B,IAA3B,IAAmCJ,EAAE,KAAK,KAAK,CAA/C,GAAmDA,EAAnD,GAAwD;AAF/C,OAAhB;AAIH;;AACD,QAAIF,IAAI,CAACO,OAAL,KAAiBH,SAArB,EAAgC;AAC5B,WAAKG,OAAL,GAAeV,UAAU,CAAC,EAAD,EAAKG,IAAI,CAACO,OAAV,CAAzB;AACH;AACJ;;AAfuB", "sourcesContent": ["import { deepExtend } from \"../../Utils\";\nexport class ManualParticle {\n    load(data) {\n        var _a, _b;\n        if (!data) {\n            return;\n        }\n        if (data.position !== undefined) {\n            this.position = {\n                x: (_a = data.position.x) !== null && _a !== void 0 ? _a : 50,\n                y: (_b = data.position.y) !== null && _b !== void 0 ? _b : 50,\n            };\n        }\n        if (data.options !== undefined) {\n            this.options = deepExtend({}, data.options);\n        }\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
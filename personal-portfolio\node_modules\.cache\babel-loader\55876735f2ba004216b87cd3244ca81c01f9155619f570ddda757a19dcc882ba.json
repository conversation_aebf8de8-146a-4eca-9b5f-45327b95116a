{"ast": null, "code": "export var AlterType;\n(function (AlterType) {\n  AlterType[\"darken\"] = \"darken\";\n  AlterType[\"enlighten\"] = \"enlighten\";\n})(AlterType || (AlterType = {}));", "map": {"version": 3, "names": ["AlterType"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/engine/browser/Enums/Types/AlterType.js"], "sourcesContent": ["export var AlterType;\n(function (AlterType) {\n    AlterType[\"darken\"] = \"darken\";\n    AlterType[\"enlighten\"] = \"enlighten\";\n})(AlterType || (AlterType = {}));\n"], "mappings": "AAAA,OAAO,IAAIA,SAAS;AACpB,CAAC,UAAUA,SAAS,EAAE;EAClBA,SAAS,CAAC,QAAQ,CAAC,GAAG,QAAQ;EAC9BA,SAAS,CAAC,WAAW,CAAC,GAAG,WAAW;AACxC,CAAC,EAAEA,SAAS,KAAKA,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { clamp, colorMix, drawLine, getStyleFromHsl, getStyleFromRgb } from \"@tsparticles/engine\";\nconst gradientMin = 0,\n  gradientMax = 1,\n  defaultLinksWidth = 0;\nexport function gradient(context, p1, p2, opacity) {\n  const gradStop = Math.floor(p2.getRadius() / p1.getRadius()),\n    color1 = p1.getFillColor(),\n    color2 = p2.getFillColor();\n  if (!color1 || !color2) {\n    return;\n  }\n  const sourcePos = p1.getPosition(),\n    destPos = p2.getPosition(),\n    midRgb = colorMix(color1, color2, p1.getRadius(), p2.getRadius()),\n    grad = context.createLinearGradient(sourcePos.x, sourcePos.y, destPos.x, destPos.y);\n  grad.addColorStop(gradientMin, getStyleFromHsl(color1, opacity));\n  grad.addColorStop(clamp(gradStop, gradientMin, gradientMax), getStyleFromRgb(midRgb, opacity));\n  grad.addColorStop(gradientMax, getStyleFromHsl(color2, opacity));\n  return grad;\n}\nexport function drawConnectLine(context, width, lineStyle, begin, end) {\n  drawLine(context, begin, end);\n  context.lineWidth = width;\n  context.strokeStyle = lineStyle;\n  context.stroke();\n}\nexport function lineStyle(container, ctx, p1, p2) {\n  const options = container.actualOptions,\n    connectOptions = options.interactivity.modes.connect;\n  if (!connectOptions) {\n    return;\n  }\n  return gradient(ctx, p1, p2, connectOptions.links.opacity);\n}\nexport function drawConnection(container, p1, p2) {\n  container.canvas.draw(ctx => {\n    const ls = lineStyle(container, ctx, p1, p2);\n    if (!ls) {\n      return;\n    }\n    const pos1 = p1.getPosition(),\n      pos2 = p2.getPosition();\n    drawConnectLine(ctx, p1.retina.linksWidth ?? defaultLinksWidth, ls, pos1, pos2);\n  });\n}", "map": {"version": 3, "names": ["clamp", "colorMix", "drawLine", "getStyleFromHsl", "getStyleFromRgb", "gradientMin", "gradientMax", "defaultLinksWidth", "gradient", "context", "p1", "p2", "opacity", "gradStop", "Math", "floor", "getRadius", "color1", "getFillColor", "color2", "sourcePos", "getPosition", "destPos", "midRgb", "grad", "createLinearGradient", "x", "y", "addColorStop", "drawConnectLine", "width", "lineStyle", "begin", "end", "lineWidth", "strokeStyle", "stroke", "container", "ctx", "options", "actualOptions", "connectOptions", "interactivity", "modes", "connect", "links", "drawConnection", "canvas", "draw", "ls", "pos1", "pos2", "retina", "linksWidth"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/interaction-external-connect/browser/Utils.js"], "sourcesContent": ["import { clamp, colorMix, drawLine, getStyleFromHsl, getStyleFromRgb, } from \"@tsparticles/engine\";\nconst gradientMin = 0, gradientMax = 1, defaultLinksWidth = 0;\nexport function gradient(context, p1, p2, opacity) {\n    const gradStop = Math.floor(p2.getRadius() / p1.getRadius()), color1 = p1.getFillColor(), color2 = p2.getFillColor();\n    if (!color1 || !color2) {\n        return;\n    }\n    const sourcePos = p1.getPosition(), destPos = p2.getPosition(), midRgb = colorMix(color1, color2, p1.getRadius(), p2.getRadius()), grad = context.createLinearGradient(sourcePos.x, sourcePos.y, destPos.x, destPos.y);\n    grad.addColorStop(gradientMin, getStyleFromHsl(color1, opacity));\n    grad.addColorStop(clamp(gradStop, gradientMin, gradientMax), getStyleFromRgb(midRgb, opacity));\n    grad.addColorStop(gradientMax, getStyleFromHsl(color2, opacity));\n    return grad;\n}\nexport function drawConnectLine(context, width, lineStyle, begin, end) {\n    drawLine(context, begin, end);\n    context.lineWidth = width;\n    context.strokeStyle = lineStyle;\n    context.stroke();\n}\nexport function lineStyle(container, ctx, p1, p2) {\n    const options = container.actualOptions, connectOptions = options.interactivity.modes.connect;\n    if (!connectOptions) {\n        return;\n    }\n    return gradient(ctx, p1, p2, connectOptions.links.opacity);\n}\nexport function drawConnection(container, p1, p2) {\n    container.canvas.draw(ctx => {\n        const ls = lineStyle(container, ctx, p1, p2);\n        if (!ls) {\n            return;\n        }\n        const pos1 = p1.getPosition(), pos2 = p2.getPosition();\n        drawConnectLine(ctx, p1.retina.linksWidth ?? defaultLinksWidth, ls, pos1, pos2);\n    });\n}\n"], "mappings": "AAAA,SAASA,KAAK,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,eAAe,EAAEC,eAAe,QAAS,qBAAqB;AAClG,MAAMC,WAAW,GAAG,CAAC;EAAEC,WAAW,GAAG,CAAC;EAAEC,iBAAiB,GAAG,CAAC;AAC7D,OAAO,SAASC,QAAQA,CAACC,OAAO,EAAEC,EAAE,EAAEC,EAAE,EAAEC,OAAO,EAAE;EAC/C,MAAMC,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACJ,EAAE,CAACK,SAAS,CAAC,CAAC,GAAGN,EAAE,CAACM,SAAS,CAAC,CAAC,CAAC;IAAEC,MAAM,GAAGP,EAAE,CAACQ,YAAY,CAAC,CAAC;IAAEC,MAAM,GAAGR,EAAE,CAACO,YAAY,CAAC,CAAC;EACpH,IAAI,CAACD,MAAM,IAAI,CAACE,MAAM,EAAE;IACpB;EACJ;EACA,MAAMC,SAAS,GAAGV,EAAE,CAACW,WAAW,CAAC,CAAC;IAAEC,OAAO,GAAGX,EAAE,CAACU,WAAW,CAAC,CAAC;IAAEE,MAAM,GAAGtB,QAAQ,CAACgB,MAAM,EAAEE,MAAM,EAAET,EAAE,CAACM,SAAS,CAAC,CAAC,EAAEL,EAAE,CAACK,SAAS,CAAC,CAAC,CAAC;IAAEQ,IAAI,GAAGf,OAAO,CAACgB,oBAAoB,CAACL,SAAS,CAACM,CAAC,EAAEN,SAAS,CAACO,CAAC,EAAEL,OAAO,CAACI,CAAC,EAAEJ,OAAO,CAACK,CAAC,CAAC;EACtNH,IAAI,CAACI,YAAY,CAACvB,WAAW,EAAEF,eAAe,CAACc,MAAM,EAAEL,OAAO,CAAC,CAAC;EAChEY,IAAI,CAACI,YAAY,CAAC5B,KAAK,CAACa,QAAQ,EAAER,WAAW,EAAEC,WAAW,CAAC,EAAEF,eAAe,CAACmB,MAAM,EAAEX,OAAO,CAAC,CAAC;EAC9FY,IAAI,CAACI,YAAY,CAACtB,WAAW,EAAEH,eAAe,CAACgB,MAAM,EAAEP,OAAO,CAAC,CAAC;EAChE,OAAOY,IAAI;AACf;AACA,OAAO,SAASK,eAAeA,CAACpB,OAAO,EAAEqB,KAAK,EAAEC,SAAS,EAAEC,KAAK,EAAEC,GAAG,EAAE;EACnE/B,QAAQ,CAACO,OAAO,EAAEuB,KAAK,EAAEC,GAAG,CAAC;EAC7BxB,OAAO,CAACyB,SAAS,GAAGJ,KAAK;EACzBrB,OAAO,CAAC0B,WAAW,GAAGJ,SAAS;EAC/BtB,OAAO,CAAC2B,MAAM,CAAC,CAAC;AACpB;AACA,OAAO,SAASL,SAASA,CAACM,SAAS,EAAEC,GAAG,EAAE5B,EAAE,EAAEC,EAAE,EAAE;EAC9C,MAAM4B,OAAO,GAAGF,SAAS,CAACG,aAAa;IAAEC,cAAc,GAAGF,OAAO,CAACG,aAAa,CAACC,KAAK,CAACC,OAAO;EAC7F,IAAI,CAACH,cAAc,EAAE;IACjB;EACJ;EACA,OAAOjC,QAAQ,CAAC8B,GAAG,EAAE5B,EAAE,EAAEC,EAAE,EAAE8B,cAAc,CAACI,KAAK,CAACjC,OAAO,CAAC;AAC9D;AACA,OAAO,SAASkC,cAAcA,CAACT,SAAS,EAAE3B,EAAE,EAAEC,EAAE,EAAE;EAC9C0B,SAAS,CAACU,MAAM,CAACC,IAAI,CAACV,GAAG,IAAI;IACzB,MAAMW,EAAE,GAAGlB,SAAS,CAACM,SAAS,EAAEC,GAAG,EAAE5B,EAAE,EAAEC,EAAE,CAAC;IAC5C,IAAI,CAACsC,EAAE,EAAE;MACL;IACJ;IACA,MAAMC,IAAI,GAAGxC,EAAE,CAACW,WAAW,CAAC,CAAC;MAAE8B,IAAI,GAAGxC,EAAE,CAACU,WAAW,CAAC,CAAC;IACtDQ,eAAe,CAACS,GAAG,EAAE5B,EAAE,CAAC0C,MAAM,CAACC,UAAU,IAAI9C,iBAAiB,EAAE0C,EAAE,EAAEC,IAAI,EAAEC,IAAI,CAAC;EACnF,CAAC,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
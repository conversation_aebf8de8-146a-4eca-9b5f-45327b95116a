{"ast": null, "code": "import { Parallax } from \"./Parallax\";\nexport class HoverEvent {\n  constructor() {\n    this.enable = false;\n    this.mode = [];\n    this.parallax = new Parallax();\n  }\n\n  load(data) {\n    if (data === undefined) {\n      return;\n    }\n\n    if (data.enable !== undefined) {\n      this.enable = data.enable;\n    }\n\n    if (data.mode !== undefined) {\n      this.mode = data.mode;\n    }\n\n    this.parallax.load(data.parallax);\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Options/Classes/Interactivity/Events/HoverEvent.js"], "names": ["Parallax", "HoverEvent", "constructor", "enable", "mode", "parallax", "load", "data", "undefined"], "mappings": "AAAA,SAASA,QAAT,QAAyB,YAAzB;AACA,OAAO,MAAMC,UAAN,CAAiB;AACpBC,EAAAA,WAAW,GAAG;AACV,SAAKC,MAAL,GAAc,KAAd;AACA,SAAKC,IAAL,GAAY,EAAZ;AACA,SAAKC,QAAL,GAAgB,IAAIL,QAAJ,EAAhB;AACH;;AACDM,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,QAAIA,IAAI,KAAKC,SAAb,EAAwB;AACpB;AACH;;AACD,QAAID,IAAI,CAACJ,MAAL,KAAgBK,SAApB,EAA+B;AAC3B,WAAKL,MAAL,GAAcI,IAAI,CAACJ,MAAnB;AACH;;AACD,QAAII,IAAI,CAACH,IAAL,KAAcI,SAAlB,EAA6B;AACzB,WAAKJ,IAAL,GAAYG,IAAI,CAACH,IAAjB;AACH;;AACD,SAAKC,QAAL,CAAcC,IAAd,CAAmBC,IAAI,CAACF,QAAxB;AACH;;AAjBmB", "sourcesContent": ["import { Parallax } from \"./Parallax\";\nexport class HoverEvent {\n    constructor() {\n        this.enable = false;\n        this.mode = [];\n        this.parallax = new Parallax();\n    }\n    load(data) {\n        if (data === undefined) {\n            return;\n        }\n        if (data.enable !== undefined) {\n            this.enable = data.enable;\n        }\n        if (data.mode !== undefined) {\n            this.mode = data.mode;\n        }\n        this.parallax.load(data.parallax);\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
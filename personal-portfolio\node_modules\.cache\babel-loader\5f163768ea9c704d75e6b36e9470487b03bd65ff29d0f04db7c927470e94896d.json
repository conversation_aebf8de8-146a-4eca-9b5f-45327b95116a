{"ast": null, "code": "import { ResponsiveMode } from \"../../Enums/Modes/ResponsiveMode.js\";\nimport { deepExtend } from \"../../Utils/Utils.js\";\nexport class Responsive {\n  constructor() {\n    this.maxWidth = Infinity;\n    this.options = {};\n    this.mode = ResponsiveMode.canvas;\n  }\n  load(data) {\n    if (!data) {\n      return;\n    }\n    if (data.maxWidth !== undefined) {\n      this.maxWidth = data.maxWidth;\n    }\n    if (data.mode !== undefined) {\n      if (data.mode === ResponsiveMode.screen) {\n        this.mode = ResponsiveMode.screen;\n      } else {\n        this.mode = ResponsiveMode.canvas;\n      }\n    }\n    if (data.options !== undefined) {\n      this.options = deepExtend({}, data.options);\n    }\n  }\n}", "map": {"version": 3, "names": ["ResponsiveMode", "deepExtend", "Responsive", "constructor", "max<PERSON><PERSON><PERSON>", "Infinity", "options", "mode", "canvas", "load", "data", "undefined", "screen"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/engine/browser/Options/Classes/Responsive.js"], "sourcesContent": ["import { ResponsiveMode } from \"../../Enums/Modes/ResponsiveMode.js\";\nimport { deepExtend } from \"../../Utils/Utils.js\";\nexport class Responsive {\n    constructor() {\n        this.maxWidth = Infinity;\n        this.options = {};\n        this.mode = ResponsiveMode.canvas;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.maxWidth !== undefined) {\n            this.maxWidth = data.maxWidth;\n        }\n        if (data.mode !== undefined) {\n            if (data.mode === ResponsiveMode.screen) {\n                this.mode = ResponsiveMode.screen;\n            }\n            else {\n                this.mode = ResponsiveMode.canvas;\n            }\n        }\n        if (data.options !== undefined) {\n            this.options = deepExtend({}, data.options);\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,qCAAqC;AACpE,SAASC,UAAU,QAAQ,sBAAsB;AACjD,OAAO,MAAMC,UAAU,CAAC;EACpBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,QAAQ,GAAGC,QAAQ;IACxB,IAAI,CAACC,OAAO,GAAG,CAAC,CAAC;IACjB,IAAI,CAACC,IAAI,GAAGP,cAAc,CAACQ,MAAM;EACrC;EACAC,IAAIA,CAACC,IAAI,EAAE;IACP,IAAI,CAACA,IAAI,EAAE;MACP;IACJ;IACA,IAAIA,IAAI,CAACN,QAAQ,KAAKO,SAAS,EAAE;MAC7B,IAAI,CAACP,QAAQ,GAAGM,IAAI,CAACN,QAAQ;IACjC;IACA,IAAIM,IAAI,CAACH,IAAI,KAAKI,SAAS,EAAE;MACzB,IAAID,IAAI,CAACH,IAAI,KAAKP,cAAc,CAACY,MAAM,EAAE;QACrC,IAAI,CAACL,IAAI,GAAGP,cAAc,CAACY,MAAM;MACrC,CAAC,MACI;QACD,IAAI,CAACL,IAAI,GAAGP,cAAc,CAACQ,MAAM;MACrC;IACJ;IACA,IAAIE,IAAI,CAACJ,OAAO,KAAKK,SAAS,EAAE;MAC5B,IAAI,CAACL,OAAO,GAAGL,UAAU,CAAC,CAAC,CAAC,EAAES,IAAI,CAACJ,OAAO,CAAC;IAC/C;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { executeOnSingleOrMultiple, isInArray, itemFromSingleOrMultiple, loadFont } from \"@tsparticles/engine\";\nimport { drawText } from \"./Utils.js\";\nexport class TextDrawer {\n  constructor() {\n    this.validTypes = [\"text\", \"character\", \"char\", \"multiline-text\"];\n  }\n  draw(data) {\n    drawText(data);\n  }\n  async init(container) {\n    const options = container.actualOptions,\n      {\n        validTypes\n      } = this;\n    if (validTypes.find(t => isInArray(t, options.particles.shape.type))) {\n      const shapeOptions = validTypes.map(t => options.particles.shape.options[t]).find(t => !!t),\n        promises = [];\n      executeOnSingleOrMultiple(shapeOptions, shape => {\n        promises.push(loadFont(shape.font, shape.weight));\n      });\n      await Promise.all(promises);\n    }\n  }\n  particleInit(container, particle) {\n    if (!particle.shape || !this.validTypes.includes(particle.shape)) {\n      return;\n    }\n    const character = particle.shapeData;\n    if (character === undefined) {\n      return;\n    }\n    const textData = character.value;\n    if (textData === undefined) {\n      return;\n    }\n    particle.text = itemFromSingleOrMultiple(textData, particle.randomIndexData);\n  }\n}", "map": {"version": 3, "names": ["executeOnSingleOrMultiple", "isInArray", "itemFromSingleOrMultiple", "loadFont", "drawText", "TextDrawer", "constructor", "validTypes", "draw", "data", "init", "container", "options", "actualOptions", "find", "t", "particles", "shape", "type", "shapeOptions", "map", "promises", "push", "font", "weight", "Promise", "all", "particleInit", "particle", "includes", "character", "shapeData", "undefined", "textData", "value", "text", "randomIndexData"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/shape-text/browser/TextDrawer.js"], "sourcesContent": ["import { executeOnSingleOrMultiple, isInArray, itemFromSingleOrMultiple, loadFont, } from \"@tsparticles/engine\";\nimport { drawText } from \"./Utils.js\";\nexport class TextDrawer {\n    constructor() {\n        this.validTypes = [\"text\", \"character\", \"char\", \"multiline-text\"];\n    }\n    draw(data) {\n        drawText(data);\n    }\n    async init(container) {\n        const options = container.actualOptions, { validTypes } = this;\n        if (validTypes.find(t => isInArray(t, options.particles.shape.type))) {\n            const shapeOptions = validTypes\n                .map(t => options.particles.shape.options[t])\n                .find(t => !!t), promises = [];\n            executeOnSingleOrMultiple(shapeOptions, shape => {\n                promises.push(loadFont(shape.font, shape.weight));\n            });\n            await Promise.all(promises);\n        }\n    }\n    particleInit(container, particle) {\n        if (!particle.shape || !this.validTypes.includes(particle.shape)) {\n            return;\n        }\n        const character = particle.shapeData;\n        if (character === undefined) {\n            return;\n        }\n        const textData = character.value;\n        if (textData === undefined) {\n            return;\n        }\n        particle.text = itemFromSingleOrMultiple(textData, particle.randomIndexData);\n    }\n}\n"], "mappings": "AAAA,SAASA,yBAAyB,EAAEC,SAAS,EAAEC,wBAAwB,EAAEC,QAAQ,QAAS,qBAAqB;AAC/G,SAASC,QAAQ,QAAQ,YAAY;AACrC,OAAO,MAAMC,UAAU,CAAC;EACpBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,UAAU,GAAG,CAAC,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,gBAAgB,CAAC;EACrE;EACAC,IAAIA,CAACC,IAAI,EAAE;IACPL,QAAQ,CAACK,IAAI,CAAC;EAClB;EACA,MAAMC,IAAIA,CAACC,SAAS,EAAE;IAClB,MAAMC,OAAO,GAAGD,SAAS,CAACE,aAAa;MAAE;QAAEN;MAAW,CAAC,GAAG,IAAI;IAC9D,IAAIA,UAAU,CAACO,IAAI,CAACC,CAAC,IAAId,SAAS,CAACc,CAAC,EAAEH,OAAO,CAACI,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC,CAAC,EAAE;MAClE,MAAMC,YAAY,GAAGZ,UAAU,CAC1Ba,GAAG,CAACL,CAAC,IAAIH,OAAO,CAACI,SAAS,CAACC,KAAK,CAACL,OAAO,CAACG,CAAC,CAAC,CAAC,CAC5CD,IAAI,CAACC,CAAC,IAAI,CAAC,CAACA,CAAC,CAAC;QAAEM,QAAQ,GAAG,EAAE;MAClCrB,yBAAyB,CAACmB,YAAY,EAAEF,KAAK,IAAI;QAC7CI,QAAQ,CAACC,IAAI,CAACnB,QAAQ,CAACc,KAAK,CAACM,IAAI,EAAEN,KAAK,CAACO,MAAM,CAAC,CAAC;MACrD,CAAC,CAAC;MACF,MAAMC,OAAO,CAACC,GAAG,CAACL,QAAQ,CAAC;IAC/B;EACJ;EACAM,YAAYA,CAAChB,SAAS,EAAEiB,QAAQ,EAAE;IAC9B,IAAI,CAACA,QAAQ,CAACX,KAAK,IAAI,CAAC,IAAI,CAACV,UAAU,CAACsB,QAAQ,CAACD,QAAQ,CAACX,KAAK,CAAC,EAAE;MAC9D;IACJ;IACA,MAAMa,SAAS,GAAGF,QAAQ,CAACG,SAAS;IACpC,IAAID,SAAS,KAAKE,SAAS,EAAE;MACzB;IACJ;IACA,MAAMC,QAAQ,GAAGH,SAAS,CAACI,KAAK;IAChC,IAAID,QAAQ,KAAKD,SAAS,EAAE;MACxB;IACJ;IACAJ,QAAQ,CAACO,IAAI,GAAGjC,wBAAwB,CAAC+B,QAAQ,EAAEL,QAAQ,CAACQ,eAAe,CAAC;EAChF;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
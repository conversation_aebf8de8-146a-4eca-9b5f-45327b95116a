{"ast": null, "code": "export var CollisionMode;\n(function (CollisionMode) {\n  CollisionMode[\"absorb\"] = \"absorb\";\n  CollisionMode[\"bounce\"] = \"bounce\";\n  CollisionMode[\"destroy\"] = \"destroy\";\n})(CollisionMode || (CollisionMode = {}));", "map": {"version": 3, "names": ["CollisionMode"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/engine/browser/Enums/Modes/CollisionMode.js"], "sourcesContent": ["export var CollisionMode;\n(function (CollisionMode) {\n    CollisionMode[\"absorb\"] = \"absorb\";\n    CollisionMode[\"bounce\"] = \"bounce\";\n    CollisionMode[\"destroy\"] = \"destroy\";\n})(CollisionMode || (CollisionMode = {}));\n"], "mappings": "AAAA,OAAO,IAAIA,aAAa;AACxB,CAAC,UAAUA,aAAa,EAAE;EACtBA,aAAa,CAAC,QAAQ,CAAC,GAAG,QAAQ;EAClCA,aAAa,CAAC,QAAQ,CAAC,GAAG,QAAQ;EAClCA,aAAa,CAAC,SAAS,CAAC,GAAG,SAAS;AACxC,CAAC,EAAEA,aAAa,KAAKA,aAAa,GAAG,CAAC,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
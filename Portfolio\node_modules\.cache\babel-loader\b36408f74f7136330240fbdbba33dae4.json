{"ast": null, "code": "var __classPrivateFieldSet = this && this.__classPrivateFieldSet || function (receiver, state, value, kind, f) {\n  if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n  return kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value), value;\n};\n\nvar __classPrivateFieldGet = this && this.__classPrivateFieldGet || function (receiver, state, kind, f) {\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n  return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n};\n\nvar _Emitters_engine;\n\nimport { deepExtend, itemFromArray } from \"../../Utils\";\nimport { Emitter } from \"./Options/Classes/Emitter\";\nimport { EmitterInstance } from \"./EmitterInstance\";\nexport class Emitters {\n  constructor(engine, container) {\n    this.container = container;\n\n    _Emitters_engine.set(this, void 0);\n\n    __classPrivateFieldSet(this, _Emitters_engine, engine, \"f\");\n\n    this.array = [];\n    this.emitters = [];\n    this.interactivityEmitters = [];\n    const overridableContainer = container;\n\n    overridableContainer.getEmitter = idxOrName => idxOrName === undefined || typeof idxOrName === \"number\" ? this.array[idxOrName || 0] : this.array.find(t => t.name === idxOrName);\n\n    overridableContainer.addEmitter = (options, position) => this.addEmitter(options, position);\n\n    overridableContainer.removeEmitter = idxOrName => {\n      const emitter = overridableContainer.getEmitter(idxOrName);\n\n      if (emitter) {\n        this.removeEmitter(emitter);\n      }\n    };\n\n    overridableContainer.playEmitter = idxOrName => {\n      const emitter = overridableContainer.getEmitter(idxOrName);\n\n      if (emitter) {\n        emitter.externalPlay();\n      }\n    };\n\n    overridableContainer.pauseEmitter = idxOrName => {\n      const emitter = overridableContainer.getEmitter(idxOrName);\n\n      if (emitter) {\n        emitter.externalPause();\n      }\n    };\n  }\n\n  init(options) {\n    var _a, _b;\n\n    if (!options) {\n      return;\n    }\n\n    if (options.emitters) {\n      if (options.emitters instanceof Array) {\n        this.emitters = options.emitters.map(s => {\n          const tmp = new Emitter();\n          tmp.load(s);\n          return tmp;\n        });\n      } else {\n        if (this.emitters instanceof Array) {\n          this.emitters = new Emitter();\n        }\n\n        this.emitters.load(options.emitters);\n      }\n    }\n\n    const interactivityEmitters = (_b = (_a = options.interactivity) === null || _a === void 0 ? void 0 : _a.modes) === null || _b === void 0 ? void 0 : _b.emitters;\n\n    if (interactivityEmitters) {\n      if (interactivityEmitters instanceof Array) {\n        this.interactivityEmitters = interactivityEmitters.map(s => {\n          const tmp = new Emitter();\n          tmp.load(s);\n          return tmp;\n        });\n      } else {\n        if (this.interactivityEmitters instanceof Array) {\n          this.interactivityEmitters = new Emitter();\n        }\n\n        this.interactivityEmitters.load(interactivityEmitters);\n      }\n    }\n\n    if (this.emitters instanceof Array) {\n      for (const emitterOptions of this.emitters) {\n        this.addEmitter(emitterOptions);\n      }\n    } else {\n      this.addEmitter(this.emitters);\n    }\n  }\n\n  play() {\n    for (const emitter of this.array) {\n      emitter.play();\n    }\n  }\n\n  pause() {\n    for (const emitter of this.array) {\n      emitter.pause();\n    }\n  }\n\n  stop() {\n    this.array = [];\n  }\n\n  update(delta) {\n    for (const emitter of this.array) {\n      emitter.update(delta);\n    }\n  }\n\n  handleClickMode(mode) {\n    const container = this.container;\n    const emitterOptions = this.emitters;\n    const modeEmitters = this.interactivityEmitters;\n\n    if (mode === \"emitter\") {\n      let emitterModeOptions;\n\n      if (modeEmitters instanceof Array) {\n        if (modeEmitters.length > 0) {\n          emitterModeOptions = itemFromArray(modeEmitters);\n        }\n      } else {\n        emitterModeOptions = modeEmitters;\n      }\n\n      const emittersOptions = emitterModeOptions !== null && emitterModeOptions !== void 0 ? emitterModeOptions : emitterOptions instanceof Array ? itemFromArray(emitterOptions) : emitterOptions;\n      const ePosition = container.interactivity.mouse.clickPosition;\n      this.addEmitter(deepExtend({}, emittersOptions), ePosition);\n    }\n  }\n\n  resize() {\n    for (const emitter of this.array) {\n      emitter.resize();\n    }\n  }\n\n  addEmitter(options, position) {\n    const emitterOptions = new Emitter();\n    emitterOptions.load(options);\n    const emitter = new EmitterInstance(__classPrivateFieldGet(this, _Emitters_engine, \"f\"), this, this.container, emitterOptions, position);\n    this.array.push(emitter);\n    return emitter;\n  }\n\n  removeEmitter(emitter) {\n    const index = this.array.indexOf(emitter);\n\n    if (index >= 0) {\n      this.array.splice(index, 1);\n    }\n  }\n\n}\n_Emitters_engine = new WeakMap();", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Plugins/Emitters/Emitters.js"], "names": ["__classPrivateFieldSet", "receiver", "state", "value", "kind", "f", "TypeError", "has", "call", "set", "__classPrivateFieldGet", "get", "_Emitters_engine", "deepExtend", "itemFromArray", "Emitter", "EmitterInstance", "Emitters", "constructor", "engine", "container", "array", "emitters", "interactivityEmitters", "overridableContainer", "getEmitter", "idxOrName", "undefined", "find", "t", "name", "addEmitter", "options", "position", "removeEmitter", "emitter", "playEmitter", "externalPlay", "pauseEmitter", "externalPause", "init", "_a", "_b", "Array", "map", "s", "tmp", "load", "interactivity", "modes", "emitterOptions", "play", "pause", "stop", "update", "delta", "handleClickMode", "mode", "modeEmitters", "emitterModeOptions", "length", "emittersOptions", "ePosition", "mouse", "clickPosition", "resize", "push", "index", "indexOf", "splice", "WeakMap"], "mappings": "AAAA,IAAIA,sBAAsB,GAAI,QAAQ,KAAKA,sBAAd,IAAyC,UAAUC,QAAV,EAAoBC,KAApB,EAA2BC,KAA3B,EAAkCC,IAAlC,EAAwCC,CAAxC,EAA2C;AAC7G,MAAID,IAAI,KAAK,GAAb,EAAkB,MAAM,IAAIE,SAAJ,CAAc,gCAAd,CAAN;AAClB,MAAIF,IAAI,KAAK,GAAT,IAAgB,CAACC,CAArB,EAAwB,MAAM,IAAIC,SAAJ,CAAc,+CAAd,CAAN;AACxB,MAAI,OAAOJ,KAAP,KAAiB,UAAjB,GAA8BD,QAAQ,KAAKC,KAAb,IAAsB,CAACG,CAArD,GAAyD,CAACH,KAAK,CAACK,GAAN,CAAUN,QAAV,CAA9D,EAAmF,MAAM,IAAIK,SAAJ,CAAc,yEAAd,CAAN;AACnF,SAAQF,IAAI,KAAK,GAAT,GAAeC,CAAC,CAACG,IAAF,CAAOP,QAAP,EAAiBE,KAAjB,CAAf,GAAyCE,CAAC,GAAGA,CAAC,CAACF,KAAF,GAAUA,KAAb,GAAqBD,KAAK,CAACO,GAAN,CAAUR,QAAV,EAAoBE,KAApB,CAAhE,EAA6FA,KAApG;AACH,CALD;;AAMA,IAAIO,sBAAsB,GAAI,QAAQ,KAAKA,sBAAd,IAAyC,UAAUT,QAAV,EAAoBC,KAApB,EAA2BE,IAA3B,EAAiCC,CAAjC,EAAoC;AACtG,MAAID,IAAI,KAAK,GAAT,IAAgB,CAACC,CAArB,EAAwB,MAAM,IAAIC,SAAJ,CAAc,+CAAd,CAAN;AACxB,MAAI,OAAOJ,KAAP,KAAiB,UAAjB,GAA8BD,QAAQ,KAAKC,KAAb,IAAsB,CAACG,CAArD,GAAyD,CAACH,KAAK,CAACK,GAAN,CAAUN,QAAV,CAA9D,EAAmF,MAAM,IAAIK,SAAJ,CAAc,0EAAd,CAAN;AACnF,SAAOF,IAAI,KAAK,GAAT,GAAeC,CAAf,GAAmBD,IAAI,KAAK,GAAT,GAAeC,CAAC,CAACG,IAAF,CAAOP,QAAP,CAAf,GAAkCI,CAAC,GAAGA,CAAC,CAACF,KAAL,GAAaD,KAAK,CAACS,GAAN,CAAUV,QAAV,CAA1E;AACH,CAJD;;AAKA,IAAIW,gBAAJ;;AACA,SAASC,UAAT,EAAqBC,aAArB,QAA0C,aAA1C;AACA,SAASC,OAAT,QAAwB,2BAAxB;AACA,SAASC,eAAT,QAAgC,mBAAhC;AACA,OAAO,MAAMC,QAAN,CAAe;AAClBC,EAAAA,WAAW,CAACC,MAAD,EAASC,SAAT,EAAoB;AAC3B,SAAKA,SAAL,GAAiBA,SAAjB;;AACAR,IAAAA,gBAAgB,CAACH,GAAjB,CAAqB,IAArB,EAA2B,KAAK,CAAhC;;AACAT,IAAAA,sBAAsB,CAAC,IAAD,EAAOY,gBAAP,EAAyBO,MAAzB,EAAiC,GAAjC,CAAtB;;AACA,SAAKE,KAAL,GAAa,EAAb;AACA,SAAKC,QAAL,GAAgB,EAAhB;AACA,SAAKC,qBAAL,GAA6B,EAA7B;AACA,UAAMC,oBAAoB,GAAGJ,SAA7B;;AACAI,IAAAA,oBAAoB,CAACC,UAArB,GAAmCC,SAAD,IAAeA,SAAS,KAAKC,SAAd,IAA2B,OAAOD,SAAP,KAAqB,QAAhD,GAC3C,KAAKL,KAAL,CAAWK,SAAS,IAAI,CAAxB,CAD2C,GAE3C,KAAKL,KAAL,CAAWO,IAAX,CAAiBC,CAAD,IAAOA,CAAC,CAACC,IAAF,KAAWJ,SAAlC,CAFN;;AAGAF,IAAAA,oBAAoB,CAACO,UAArB,GAAkC,CAACC,OAAD,EAAUC,QAAV,KAAuB,KAAKF,UAAL,CAAgBC,OAAhB,EAAyBC,QAAzB,CAAzD;;AACAT,IAAAA,oBAAoB,CAACU,aAArB,GAAsCR,SAAD,IAAe;AAChD,YAAMS,OAAO,GAAGX,oBAAoB,CAACC,UAArB,CAAgCC,SAAhC,CAAhB;;AACA,UAAIS,OAAJ,EAAa;AACT,aAAKD,aAAL,CAAmBC,OAAnB;AACH;AACJ,KALD;;AAMAX,IAAAA,oBAAoB,CAACY,WAArB,GAAoCV,SAAD,IAAe;AAC9C,YAAMS,OAAO,GAAGX,oBAAoB,CAACC,UAArB,CAAgCC,SAAhC,CAAhB;;AACA,UAAIS,OAAJ,EAAa;AACTA,QAAAA,OAAO,CAACE,YAAR;AACH;AACJ,KALD;;AAMAb,IAAAA,oBAAoB,CAACc,YAArB,GAAqCZ,SAAD,IAAe;AAC/C,YAAMS,OAAO,GAAGX,oBAAoB,CAACC,UAArB,CAAgCC,SAAhC,CAAhB;;AACA,UAAIS,OAAJ,EAAa;AACTA,QAAAA,OAAO,CAACI,aAAR;AACH;AACJ,KALD;AAMH;;AACDC,EAAAA,IAAI,CAACR,OAAD,EAAU;AACV,QAAIS,EAAJ,EAAQC,EAAR;;AACA,QAAI,CAACV,OAAL,EAAc;AACV;AACH;;AACD,QAAIA,OAAO,CAACV,QAAZ,EAAsB;AAClB,UAAIU,OAAO,CAACV,QAAR,YAA4BqB,KAAhC,EAAuC;AACnC,aAAKrB,QAAL,GAAgBU,OAAO,CAACV,QAAR,CAAiBsB,GAAjB,CAAsBC,CAAD,IAAO;AACxC,gBAAMC,GAAG,GAAG,IAAI/B,OAAJ,EAAZ;AACA+B,UAAAA,GAAG,CAACC,IAAJ,CAASF,CAAT;AACA,iBAAOC,GAAP;AACH,SAJe,CAAhB;AAKH,OAND,MAOK;AACD,YAAI,KAAKxB,QAAL,YAAyBqB,KAA7B,EAAoC;AAChC,eAAKrB,QAAL,GAAgB,IAAIP,OAAJ,EAAhB;AACH;;AACD,aAAKO,QAAL,CAAcyB,IAAd,CAAmBf,OAAO,CAACV,QAA3B;AACH;AACJ;;AACD,UAAMC,qBAAqB,GAAG,CAACmB,EAAE,GAAG,CAACD,EAAE,GAAGT,OAAO,CAACgB,aAAd,MAAiC,IAAjC,IAAyCP,EAAE,KAAK,KAAK,CAArD,GAAyD,KAAK,CAA9D,GAAkEA,EAAE,CAACQ,KAA3E,MAAsF,IAAtF,IAA8FP,EAAE,KAAK,KAAK,CAA1G,GAA8G,KAAK,CAAnH,GAAuHA,EAAE,CAACpB,QAAxJ;;AACA,QAAIC,qBAAJ,EAA2B;AACvB,UAAIA,qBAAqB,YAAYoB,KAArC,EAA4C;AACxC,aAAKpB,qBAAL,GAA6BA,qBAAqB,CAACqB,GAAtB,CAA2BC,CAAD,IAAO;AAC1D,gBAAMC,GAAG,GAAG,IAAI/B,OAAJ,EAAZ;AACA+B,UAAAA,GAAG,CAACC,IAAJ,CAASF,CAAT;AACA,iBAAOC,GAAP;AACH,SAJ4B,CAA7B;AAKH,OAND,MAOK;AACD,YAAI,KAAKvB,qBAAL,YAAsCoB,KAA1C,EAAiD;AAC7C,eAAKpB,qBAAL,GAA6B,IAAIR,OAAJ,EAA7B;AACH;;AACD,aAAKQ,qBAAL,CAA2BwB,IAA3B,CAAgCxB,qBAAhC;AACH;AACJ;;AACD,QAAI,KAAKD,QAAL,YAAyBqB,KAA7B,EAAoC;AAChC,WAAK,MAAMO,cAAX,IAA6B,KAAK5B,QAAlC,EAA4C;AACxC,aAAKS,UAAL,CAAgBmB,cAAhB;AACH;AACJ,KAJD,MAKK;AACD,WAAKnB,UAAL,CAAgB,KAAKT,QAArB;AACH;AACJ;;AACD6B,EAAAA,IAAI,GAAG;AACH,SAAK,MAAMhB,OAAX,IAAsB,KAAKd,KAA3B,EAAkC;AAC9Bc,MAAAA,OAAO,CAACgB,IAAR;AACH;AACJ;;AACDC,EAAAA,KAAK,GAAG;AACJ,SAAK,MAAMjB,OAAX,IAAsB,KAAKd,KAA3B,EAAkC;AAC9Bc,MAAAA,OAAO,CAACiB,KAAR;AACH;AACJ;;AACDC,EAAAA,IAAI,GAAG;AACH,SAAKhC,KAAL,GAAa,EAAb;AACH;;AACDiC,EAAAA,MAAM,CAACC,KAAD,EAAQ;AACV,SAAK,MAAMpB,OAAX,IAAsB,KAAKd,KAA3B,EAAkC;AAC9Bc,MAAAA,OAAO,CAACmB,MAAR,CAAeC,KAAf;AACH;AACJ;;AACDC,EAAAA,eAAe,CAACC,IAAD,EAAO;AAClB,UAAMrC,SAAS,GAAG,KAAKA,SAAvB;AACA,UAAM8B,cAAc,GAAG,KAAK5B,QAA5B;AACA,UAAMoC,YAAY,GAAG,KAAKnC,qBAA1B;;AACA,QAAIkC,IAAI,KAAK,SAAb,EAAwB;AACpB,UAAIE,kBAAJ;;AACA,UAAID,YAAY,YAAYf,KAA5B,EAAmC;AAC/B,YAAIe,YAAY,CAACE,MAAb,GAAsB,CAA1B,EAA6B;AACzBD,UAAAA,kBAAkB,GAAG7C,aAAa,CAAC4C,YAAD,CAAlC;AACH;AACJ,OAJD,MAKK;AACDC,QAAAA,kBAAkB,GAAGD,YAArB;AACH;;AACD,YAAMG,eAAe,GAAGF,kBAAkB,KAAK,IAAvB,IAA+BA,kBAAkB,KAAK,KAAK,CAA3D,GAA+DA,kBAA/D,GAAqFT,cAAc,YAAYP,KAA1B,GAAkC7B,aAAa,CAACoC,cAAD,CAA/C,GAAkEA,cAA/K;AACA,YAAMY,SAAS,GAAG1C,SAAS,CAAC4B,aAAV,CAAwBe,KAAxB,CAA8BC,aAAhD;AACA,WAAKjC,UAAL,CAAgBlB,UAAU,CAAC,EAAD,EAAKgD,eAAL,CAA1B,EAAiDC,SAAjD;AACH;AACJ;;AACDG,EAAAA,MAAM,GAAG;AACL,SAAK,MAAM9B,OAAX,IAAsB,KAAKd,KAA3B,EAAkC;AAC9Bc,MAAAA,OAAO,CAAC8B,MAAR;AACH;AACJ;;AACDlC,EAAAA,UAAU,CAACC,OAAD,EAAUC,QAAV,EAAoB;AAC1B,UAAMiB,cAAc,GAAG,IAAInC,OAAJ,EAAvB;AACAmC,IAAAA,cAAc,CAACH,IAAf,CAAoBf,OAApB;AACA,UAAMG,OAAO,GAAG,IAAInB,eAAJ,CAAoBN,sBAAsB,CAAC,IAAD,EAAOE,gBAAP,EAAyB,GAAzB,CAA1C,EAAyE,IAAzE,EAA+E,KAAKQ,SAApF,EAA+F8B,cAA/F,EAA+GjB,QAA/G,CAAhB;AACA,SAAKZ,KAAL,CAAW6C,IAAX,CAAgB/B,OAAhB;AACA,WAAOA,OAAP;AACH;;AACDD,EAAAA,aAAa,CAACC,OAAD,EAAU;AACnB,UAAMgC,KAAK,GAAG,KAAK9C,KAAL,CAAW+C,OAAX,CAAmBjC,OAAnB,CAAd;;AACA,QAAIgC,KAAK,IAAI,CAAb,EAAgB;AACZ,WAAK9C,KAAL,CAAWgD,MAAX,CAAkBF,KAAlB,EAAyB,CAAzB;AACH;AACJ;;AAnIiB;AAqItBvD,gBAAgB,GAAG,IAAI0D,OAAJ,EAAnB", "sourcesContent": ["var __classPrivateFieldSet = (this && this.__classPrivateFieldSet) || function (receiver, state, value, kind, f) {\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\n};\nvar __classPrivateFieldGet = (this && this.__classPrivateFieldGet) || function (receiver, state, kind, f) {\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n};\nvar _Emitters_engine;\nimport { deepExtend, itemFromArray } from \"../../Utils\";\nimport { Emitter } from \"./Options/Classes/Emitter\";\nimport { EmitterInstance } from \"./EmitterInstance\";\nexport class Emitters {\n    constructor(engine, container) {\n        this.container = container;\n        _Emitters_engine.set(this, void 0);\n        __classPrivateFieldSet(this, _Emitters_engine, engine, \"f\");\n        this.array = [];\n        this.emitters = [];\n        this.interactivityEmitters = [];\n        const overridableContainer = container;\n        overridableContainer.getEmitter = (idxOrName) => idxOrName === undefined || typeof idxOrName === \"number\"\n            ? this.array[idxOrName || 0]\n            : this.array.find((t) => t.name === idxOrName);\n        overridableContainer.addEmitter = (options, position) => this.addEmitter(options, position);\n        overridableContainer.removeEmitter = (idxOrName) => {\n            const emitter = overridableContainer.getEmitter(idxOrName);\n            if (emitter) {\n                this.removeEmitter(emitter);\n            }\n        };\n        overridableContainer.playEmitter = (idxOrName) => {\n            const emitter = overridableContainer.getEmitter(idxOrName);\n            if (emitter) {\n                emitter.externalPlay();\n            }\n        };\n        overridableContainer.pauseEmitter = (idxOrName) => {\n            const emitter = overridableContainer.getEmitter(idxOrName);\n            if (emitter) {\n                emitter.externalPause();\n            }\n        };\n    }\n    init(options) {\n        var _a, _b;\n        if (!options) {\n            return;\n        }\n        if (options.emitters) {\n            if (options.emitters instanceof Array) {\n                this.emitters = options.emitters.map((s) => {\n                    const tmp = new Emitter();\n                    tmp.load(s);\n                    return tmp;\n                });\n            }\n            else {\n                if (this.emitters instanceof Array) {\n                    this.emitters = new Emitter();\n                }\n                this.emitters.load(options.emitters);\n            }\n        }\n        const interactivityEmitters = (_b = (_a = options.interactivity) === null || _a === void 0 ? void 0 : _a.modes) === null || _b === void 0 ? void 0 : _b.emitters;\n        if (interactivityEmitters) {\n            if (interactivityEmitters instanceof Array) {\n                this.interactivityEmitters = interactivityEmitters.map((s) => {\n                    const tmp = new Emitter();\n                    tmp.load(s);\n                    return tmp;\n                });\n            }\n            else {\n                if (this.interactivityEmitters instanceof Array) {\n                    this.interactivityEmitters = new Emitter();\n                }\n                this.interactivityEmitters.load(interactivityEmitters);\n            }\n        }\n        if (this.emitters instanceof Array) {\n            for (const emitterOptions of this.emitters) {\n                this.addEmitter(emitterOptions);\n            }\n        }\n        else {\n            this.addEmitter(this.emitters);\n        }\n    }\n    play() {\n        for (const emitter of this.array) {\n            emitter.play();\n        }\n    }\n    pause() {\n        for (const emitter of this.array) {\n            emitter.pause();\n        }\n    }\n    stop() {\n        this.array = [];\n    }\n    update(delta) {\n        for (const emitter of this.array) {\n            emitter.update(delta);\n        }\n    }\n    handleClickMode(mode) {\n        const container = this.container;\n        const emitterOptions = this.emitters;\n        const modeEmitters = this.interactivityEmitters;\n        if (mode === \"emitter\") {\n            let emitterModeOptions;\n            if (modeEmitters instanceof Array) {\n                if (modeEmitters.length > 0) {\n                    emitterModeOptions = itemFromArray(modeEmitters);\n                }\n            }\n            else {\n                emitterModeOptions = modeEmitters;\n            }\n            const emittersOptions = emitterModeOptions !== null && emitterModeOptions !== void 0 ? emitterModeOptions : (emitterOptions instanceof Array ? itemFromArray(emitterOptions) : emitterOptions);\n            const ePosition = container.interactivity.mouse.clickPosition;\n            this.addEmitter(deepExtend({}, emittersOptions), ePosition);\n        }\n    }\n    resize() {\n        for (const emitter of this.array) {\n            emitter.resize();\n        }\n    }\n    addEmitter(options, position) {\n        const emitterOptions = new Emitter();\n        emitterOptions.load(options);\n        const emitter = new EmitterInstance(__classPrivateFieldGet(this, _Emitters_engine, \"f\"), this, this.container, emitterOptions, position);\n        this.array.push(emitter);\n        return emitter;\n    }\n    removeEmitter(emitter) {\n        const index = this.array.indexOf(emitter);\n        if (index >= 0) {\n            this.array.splice(index, 1);\n        }\n    }\n}\n_Emitters_engine = new WeakMap();\n"]}, "metadata": {}, "sourceType": "module"}
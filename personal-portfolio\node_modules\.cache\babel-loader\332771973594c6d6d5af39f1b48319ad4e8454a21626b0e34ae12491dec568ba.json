{"ast": null, "code": "export class ConnectLinks {\n  constructor() {\n    this.opacity = 0.5;\n  }\n  load(data) {\n    if (!data) {\n      return;\n    }\n    if (data.opacity !== undefined) {\n      this.opacity = data.opacity;\n    }\n  }\n}", "map": {"version": 3, "names": ["ConnectLinks", "constructor", "opacity", "load", "data", "undefined"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/interaction-external-connect/browser/Options/Classes/ConnectLinks.js"], "sourcesContent": ["export class ConnectLinks {\n    constructor() {\n        this.opacity = 0.5;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.opacity !== undefined) {\n            this.opacity = data.opacity;\n        }\n    }\n}\n"], "mappings": "AAAA,OAAO,MAAMA,YAAY,CAAC;EACtBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,OAAO,GAAG,GAAG;EACtB;EACAC,IAAIA,CAACC,IAAI,EAAE;IACP,IAAI,CAACA,IAAI,EAAE;MACP;IACJ;IACA,IAAIA,IAAI,CAACF,OAAO,KAAKG,SAAS,EAAE;MAC5B,IAAI,CAACH,OAAO,GAAGE,IAAI,CAACF,OAAO;IAC/B;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
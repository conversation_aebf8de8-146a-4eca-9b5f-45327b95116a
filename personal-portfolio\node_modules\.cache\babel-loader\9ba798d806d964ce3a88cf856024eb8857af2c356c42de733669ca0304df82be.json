{"ast": null, "code": "import { EmitterShapeBase } from \"@tsparticles/plugin-emitters\";\nimport { getRandom } from \"@tsparticles/engine\";\nconst quarter = 0.25,\n  double = 2,\n  doublePI = Math.PI * double,\n  squareExp = 2,\n  half = 0.5;\nexport class EmittersCircleShape extends EmitterShapeBase {\n  constructor(position, size, fill, options) {\n    super(position, size, fill, options);\n  }\n  async init() {}\n  randomPosition() {\n    const size = this.size,\n      fill = this.fill,\n      position = this.position,\n      generateTheta = (x, y) => {\n        const u = getRandom() * quarter,\n          theta = Math.atan(y / x * Math.tan(doublePI * u)),\n          v = getRandom();\n        if (v < quarter) {\n          return theta;\n        } else if (v < double * quarter) {\n          return Math.PI - theta;\n        } else if (v < double * quarter + quarter) {\n          return Math.PI + theta;\n        } else {\n          return -theta;\n        }\n      },\n      radius = (x, y, theta) => x * y / Math.sqrt((y * Math.cos(theta)) ** squareExp + (x * Math.sin(theta)) ** squareExp),\n      [a, b] = [size.width * half, size.height * half],\n      randomTheta = generateTheta(a, b),\n      maxRadius = radius(a, b, randomTheta),\n      randomRadius = fill ? maxRadius * Math.sqrt(getRandom()) : maxRadius;\n    return {\n      position: {\n        x: position.x + randomRadius * Math.cos(randomTheta),\n        y: position.y + randomRadius * Math.sin(randomTheta)\n      }\n    };\n  }\n}", "map": {"version": 3, "names": ["EmitterShapeBase", "getRandom", "quarter", "double", "doublePI", "Math", "PI", "squareExp", "half", "EmittersCircleShape", "constructor", "position", "size", "fill", "options", "init", "randomPosition", "generateTheta", "x", "y", "u", "theta", "atan", "tan", "v", "radius", "sqrt", "cos", "sin", "a", "b", "width", "height", "randomTheta", "maxRadius", "randomRadius"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/plugin-emitters-shape-circle/browser/EmittersCircleShape.js"], "sourcesContent": ["import { EmitterShapeBase } from \"@tsparticles/plugin-emitters\";\nimport { getRandom } from \"@tsparticles/engine\";\nconst quarter = 0.25, double = 2, doublePI = Math.PI * double, squareExp = 2, half = 0.5;\nexport class EmittersCircleShape extends EmitterShapeBase {\n    constructor(position, size, fill, options) {\n        super(position, size, fill, options);\n    }\n    async init() {\n    }\n    randomPosition() {\n        const size = this.size, fill = this.fill, position = this.position, generateTheta = (x, y) => {\n            const u = getRandom() * quarter, theta = Math.atan((y / x) * Math.tan(doublePI * u)), v = getRandom();\n            if (v < quarter) {\n                return theta;\n            }\n            else if (v < double * quarter) {\n                return Math.PI - theta;\n            }\n            else if (v < double * quarter + quarter) {\n                return Math.PI + theta;\n            }\n            else {\n                return -theta;\n            }\n        }, radius = (x, y, theta) => (x * y) / Math.sqrt((y * Math.cos(theta)) ** squareExp + (x * Math.sin(theta)) ** squareExp), [a, b] = [size.width * half, size.height * half], randomTheta = generateTheta(a, b), maxRadius = radius(a, b, randomTheta), randomRadius = fill ? maxRadius * Math.sqrt(getRandom()) : maxRadius;\n        return {\n            position: {\n                x: position.x + randomRadius * Math.cos(randomTheta),\n                y: position.y + randomRadius * Math.sin(randomTheta),\n            },\n        };\n    }\n}\n"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,8BAA8B;AAC/D,SAASC,SAAS,QAAQ,qBAAqB;AAC/C,MAAMC,OAAO,GAAG,IAAI;EAAEC,MAAM,GAAG,CAAC;EAAEC,QAAQ,GAAGC,IAAI,CAACC,EAAE,GAAGH,MAAM;EAAEI,SAAS,GAAG,CAAC;EAAEC,IAAI,GAAG,GAAG;AACxF,OAAO,MAAMC,mBAAmB,SAAST,gBAAgB,CAAC;EACtDU,WAAWA,CAACC,QAAQ,EAAEC,IAAI,EAAEC,IAAI,EAAEC,OAAO,EAAE;IACvC,KAAK,CAACH,QAAQ,EAAEC,IAAI,EAAEC,IAAI,EAAEC,OAAO,CAAC;EACxC;EACA,MAAMC,IAAIA,CAAA,EAAG,CACb;EACAC,cAAcA,CAAA,EAAG;IACb,MAAMJ,IAAI,GAAG,IAAI,CAACA,IAAI;MAAEC,IAAI,GAAG,IAAI,CAACA,IAAI;MAAEF,QAAQ,GAAG,IAAI,CAACA,QAAQ;MAAEM,aAAa,GAAGA,CAACC,CAAC,EAAEC,CAAC,KAAK;QAC1F,MAAMC,CAAC,GAAGnB,SAAS,CAAC,CAAC,GAAGC,OAAO;UAAEmB,KAAK,GAAGhB,IAAI,CAACiB,IAAI,CAAEH,CAAC,GAAGD,CAAC,GAAIb,IAAI,CAACkB,GAAG,CAACnB,QAAQ,GAAGgB,CAAC,CAAC,CAAC;UAAEI,CAAC,GAAGvB,SAAS,CAAC,CAAC;QACrG,IAAIuB,CAAC,GAAGtB,OAAO,EAAE;UACb,OAAOmB,KAAK;QAChB,CAAC,MACI,IAAIG,CAAC,GAAGrB,MAAM,GAAGD,OAAO,EAAE;UAC3B,OAAOG,IAAI,CAACC,EAAE,GAAGe,KAAK;QAC1B,CAAC,MACI,IAAIG,CAAC,GAAGrB,MAAM,GAAGD,OAAO,GAAGA,OAAO,EAAE;UACrC,OAAOG,IAAI,CAACC,EAAE,GAAGe,KAAK;QAC1B,CAAC,MACI;UACD,OAAO,CAACA,KAAK;QACjB;MACJ,CAAC;MAAEI,MAAM,GAAGA,CAACP,CAAC,EAAEC,CAAC,EAAEE,KAAK,KAAMH,CAAC,GAAGC,CAAC,GAAId,IAAI,CAACqB,IAAI,CAAC,CAACP,CAAC,GAAGd,IAAI,CAACsB,GAAG,CAACN,KAAK,CAAC,KAAKd,SAAS,GAAG,CAACW,CAAC,GAAGb,IAAI,CAACuB,GAAG,CAACP,KAAK,CAAC,KAAKd,SAAS,CAAC;MAAE,CAACsB,CAAC,EAAEC,CAAC,CAAC,GAAG,CAAClB,IAAI,CAACmB,KAAK,GAAGvB,IAAI,EAAEI,IAAI,CAACoB,MAAM,GAAGxB,IAAI,CAAC;MAAEyB,WAAW,GAAGhB,aAAa,CAACY,CAAC,EAAEC,CAAC,CAAC;MAAEI,SAAS,GAAGT,MAAM,CAACI,CAAC,EAAEC,CAAC,EAAEG,WAAW,CAAC;MAAEE,YAAY,GAAGtB,IAAI,GAAGqB,SAAS,GAAG7B,IAAI,CAACqB,IAAI,CAACzB,SAAS,CAAC,CAAC,CAAC,GAAGiC,SAAS;IAC3T,OAAO;MACHvB,QAAQ,EAAE;QACNO,CAAC,EAAEP,QAAQ,CAACO,CAAC,GAAGiB,YAAY,GAAG9B,IAAI,CAACsB,GAAG,CAACM,WAAW,CAAC;QACpDd,CAAC,EAAER,QAAQ,CAACQ,CAAC,GAAGgB,YAAY,GAAG9B,IAAI,CAACuB,GAAG,CAACK,WAAW;MACvD;IACJ,CAAC;EACL;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { setRangeValue } from \"@tsparticles/engine\";\nexport class Remove {\n  constructor() {\n    this.quantity = 2;\n  }\n  load(data) {\n    if (!data) {\n      return;\n    }\n    const quantity = data.quantity;\n    if (quantity !== undefined) {\n      this.quantity = setRangeValue(quantity);\n    }\n  }\n}", "map": {"version": 3, "names": ["setRangeValue", "Remove", "constructor", "quantity", "load", "data", "undefined"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/interaction-external-remove/browser/Options/Classes/Remove.js"], "sourcesContent": ["import { setRangeValue } from \"@tsparticles/engine\";\nexport class Remove {\n    constructor() {\n        this.quantity = 2;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        const quantity = data.quantity;\n        if (quantity !== undefined) {\n            this.quantity = setRangeValue(quantity);\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,qBAAqB;AACnD,OAAO,MAAMC,MAAM,CAAC;EAChBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,QAAQ,GAAG,CAAC;EACrB;EACAC,IAAIA,CAACC,IAAI,EAAE;IACP,IAAI,CAACA,IAAI,EAAE;MACP;IACJ;IACA,MAAMF,QAAQ,GAAGE,IAAI,CAACF,QAAQ;IAC9B,IAAIA,QAAQ,KAAKG,SAAS,EAAE;MACxB,IAAI,CAACH,QAAQ,GAAGH,aAAa,CAACG,QAAQ,CAAC;IAC3C;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { TextDrawer, validTypes } from \"./TextDrawer\";\nexport async function loadTextShape(engine, refresh = true) {\n  await engine.addShape(validTypes, new TextDrawer(), refresh);\n}", "map": {"version": 3, "names": ["TextDrawer", "validTypes", "loadTextShape", "engine", "refresh", "addShape"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/tsparticles-shape-text/esm/index.js"], "sourcesContent": ["import { TextDrawer, validTypes } from \"./TextDrawer\";\nexport async function loadTextShape(engine, refresh = true) {\n    await engine.addShape(validTypes, new TextDrawer(), refresh);\n}\n"], "mappings": "AAAA,SAASA,UAAU,EAAEC,UAAU,QAAQ,cAAc;AACrD,OAAO,eAAeC,aAAaA,CAACC,MAAM,EAAEC,OAAO,GAAG,IAAI,EAAE;EACxD,MAAMD,MAAM,CAACE,QAAQ,CAACJ,UAAU,EAAE,IAAID,UAAU,CAAC,CAAC,EAAEI,OAAO,CAAC;AAChE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
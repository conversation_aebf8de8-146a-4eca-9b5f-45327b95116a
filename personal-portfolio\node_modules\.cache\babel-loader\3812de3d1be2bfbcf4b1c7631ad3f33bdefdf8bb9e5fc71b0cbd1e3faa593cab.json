{"ast": null, "code": "import { setRangeValue } from \"../../../../Utils/NumberUtils.js\";\nexport class MoveAttract {\n  constructor() {\n    this.distance = 200;\n    this.enable = false;\n    this.rotate = {\n      x: 3000,\n      y: 3000\n    };\n  }\n  load(data) {\n    if (!data) {\n      return;\n    }\n    if (data.distance !== undefined) {\n      this.distance = setRangeValue(data.distance);\n    }\n    if (data.enable !== undefined) {\n      this.enable = data.enable;\n    }\n    if (data.rotate) {\n      const rotateX = data.rotate.x;\n      if (rotateX !== undefined) {\n        this.rotate.x = rotateX;\n      }\n      const rotateY = data.rotate.y;\n      if (rotateY !== undefined) {\n        this.rotate.y = rotateY;\n      }\n    }\n  }\n}", "map": {"version": 3, "names": ["setRangeValue", "MoveAttract", "constructor", "distance", "enable", "rotate", "x", "y", "load", "data", "undefined", "rotateX", "rotateY"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/engine/browser/Options/Classes/Particles/Move/MoveAttract.js"], "sourcesContent": ["import { setRangeValue } from \"../../../../Utils/NumberUtils.js\";\nexport class MoveAttract {\n    constructor() {\n        this.distance = 200;\n        this.enable = false;\n        this.rotate = {\n            x: 3000,\n            y: 3000,\n        };\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.distance !== undefined) {\n            this.distance = setRangeValue(data.distance);\n        }\n        if (data.enable !== undefined) {\n            this.enable = data.enable;\n        }\n        if (data.rotate) {\n            const rotateX = data.rotate.x;\n            if (rotateX !== undefined) {\n                this.rotate.x = rotateX;\n            }\n            const rotateY = data.rotate.y;\n            if (rotateY !== undefined) {\n                this.rotate.y = rotateY;\n            }\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,kCAAkC;AAChE,OAAO,MAAMC,WAAW,CAAC;EACrBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,QAAQ,GAAG,GAAG;IACnB,IAAI,CAACC,MAAM,GAAG,KAAK;IACnB,IAAI,CAACC,MAAM,GAAG;MACVC,CAAC,EAAE,IAAI;MACPC,CAAC,EAAE;IACP,CAAC;EACL;EACAC,IAAIA,CAACC,IAAI,EAAE;IACP,IAAI,CAACA,IAAI,EAAE;MACP;IACJ;IACA,IAAIA,IAAI,CAACN,QAAQ,KAAKO,SAAS,EAAE;MAC7B,IAAI,CAACP,QAAQ,GAAGH,aAAa,CAACS,IAAI,CAACN,QAAQ,CAAC;IAChD;IACA,IAAIM,IAAI,CAACL,MAAM,KAAKM,SAAS,EAAE;MAC3B,IAAI,CAACN,MAAM,GAAGK,IAAI,CAACL,MAAM;IAC7B;IACA,IAAIK,IAAI,CAACJ,MAAM,EAAE;MACb,MAAMM,OAAO,GAAGF,IAAI,CAACJ,MAAM,CAACC,CAAC;MAC7B,IAAIK,OAAO,KAAKD,SAAS,EAAE;QACvB,IAAI,CAACL,MAAM,CAACC,CAAC,GAAGK,OAAO;MAC3B;MACA,MAAMC,OAAO,GAAGH,IAAI,CAACJ,MAAM,CAACE,CAAC;MAC7B,IAAIK,OAAO,KAAKF,SAAS,EAAE;QACvB,IAAI,CAACL,MAAM,CAACE,CAAC,GAAGK,OAAO;MAC3B;IACJ;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "var __classPrivateFieldSet = this && this.__classPrivateFieldSet || function (receiver, state, value, kind, f) {\n  if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n  return kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value), value;\n};\n\nvar _Plugins_engine;\n\nexport class Plugins {\n  constructor(engine) {\n    _Plugins_engine.set(this, void 0);\n\n    __classPrivateFieldSet(this, _Plugins_engine, engine, \"f\");\n\n    this.plugins = [];\n    this.interactorsInitializers = new Map();\n    this.updatersInitializers = new Map();\n    this.interactors = new Map();\n    this.updaters = new Map();\n    this.presets = new Map();\n    this.drawers = new Map();\n    this.pathGenerators = new Map();\n  }\n\n  getPlugin(plugin) {\n    return this.plugins.find(t => t.id === plugin);\n  }\n\n  addPlugin(plugin) {\n    if (!this.getPlugin(plugin.id)) {\n      this.plugins.push(plugin);\n    }\n  }\n\n  getAvailablePlugins(container) {\n    const res = new Map();\n\n    for (const plugin of this.plugins) {\n      if (!plugin.needsPlugin(container.actualOptions)) {\n        continue;\n      }\n\n      res.set(plugin.id, plugin.getPlugin(container));\n    }\n\n    return res;\n  }\n\n  loadOptions(options, sourceOptions) {\n    for (const plugin of this.plugins) {\n      plugin.loadOptions(options, sourceOptions);\n    }\n  }\n\n  getPreset(preset) {\n    return this.presets.get(preset);\n  }\n\n  addPreset(presetKey, options) {\n    let override = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n\n    if (override || !this.getPreset(presetKey)) {\n      this.presets.set(presetKey, options);\n    }\n  }\n\n  addShapeDrawer(type, drawer) {\n    if (!this.getShapeDrawer(type)) {\n      this.drawers.set(type, drawer);\n    }\n  }\n\n  getShapeDrawer(type) {\n    return this.drawers.get(type);\n  }\n\n  getSupportedShapes() {\n    return this.drawers.keys();\n  }\n\n  getPathGenerator(type) {\n    return this.pathGenerators.get(type);\n  }\n\n  addPathGenerator(type, pathGenerator) {\n    if (!this.getPathGenerator(type)) {\n      this.pathGenerators.set(type, pathGenerator);\n    }\n  }\n\n  getInteractors(container) {\n    let force = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    let res = this.interactors.get(container);\n\n    if (!res || force) {\n      res = [...this.interactorsInitializers.values()].map(t => t(container));\n      this.interactors.set(container, res);\n    }\n\n    return res;\n  }\n\n  addInteractor(name, initInteractor) {\n    this.interactorsInitializers.set(name, initInteractor);\n  }\n\n  getUpdaters(container) {\n    let force = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    let res = this.updaters.get(container);\n\n    if (!res || force) {\n      res = [...this.updatersInitializers.values()].map(t => t(container));\n      this.updaters.set(container, res);\n    }\n\n    return res;\n  }\n\n  addParticleUpdater(name, initUpdater) {\n    this.updatersInitializers.set(name, initUpdater);\n  }\n\n}\n_Plugins_engine = new WeakMap();", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Core/Utils/Plugins.js"], "names": ["__classPrivateFieldSet", "receiver", "state", "value", "kind", "f", "TypeError", "has", "call", "set", "_Plugins_engine", "Plugins", "constructor", "engine", "plugins", "interactorsInitializers", "Map", "updatersInitializers", "interactors", "updaters", "presets", "drawers", "pathGenerators", "getPlugin", "plugin", "find", "t", "id", "addPlugin", "push", "getAvailablePlugins", "container", "res", "needsPlugin", "actualOptions", "loadOptions", "options", "sourceOptions", "getPreset", "preset", "get", "addPreset", "preset<PERSON>ey", "override", "addShapeDrawer", "type", "drawer", "getShapeDrawer", "getSupportedShapes", "keys", "getPathGenerator", "addPathGenerator", "pathGenerator", "getInteractors", "force", "values", "map", "addInteractor", "name", "initInteractor", "getUpdaters", "addParticleUpdater", "initUpdater", "WeakMap"], "mappings": "AAAA,IAAIA,sBAAsB,GAAI,QAAQ,KAAKA,sBAAd,IAAyC,UAAUC,QAAV,EAAoBC,KAApB,EAA2BC,KAA3B,EAAkCC,IAAlC,EAAwCC,CAAxC,EAA2C;AAC7G,MAAID,IAAI,KAAK,GAAb,EAAkB,MAAM,IAAIE,SAAJ,CAAc,gCAAd,CAAN;AAClB,MAAIF,IAAI,KAAK,GAAT,IAAgB,CAACC,CAArB,EAAwB,MAAM,IAAIC,SAAJ,CAAc,+CAAd,CAAN;AACxB,MAAI,OAAOJ,KAAP,KAAiB,UAAjB,GAA8BD,QAAQ,KAAKC,KAAb,IAAsB,CAACG,CAArD,GAAyD,CAACH,KAAK,CAACK,GAAN,CAAUN,QAAV,CAA9D,EAAmF,MAAM,IAAIK,SAAJ,CAAc,yEAAd,CAAN;AACnF,SAAQF,IAAI,KAAK,GAAT,GAAeC,CAAC,CAACG,IAAF,CAAOP,QAAP,EAAiBE,KAAjB,CAAf,GAAyCE,CAAC,GAAGA,CAAC,CAACF,KAAF,GAAUA,KAAb,GAAqBD,KAAK,CAACO,GAAN,CAAUR,QAAV,EAAoBE,KAApB,CAAhE,EAA6FA,KAApG;AACH,CALD;;AAMA,IAAIO,eAAJ;;AACA,OAAO,MAAMC,OAAN,CAAc;AACjBC,EAAAA,WAAW,CAACC,MAAD,EAAS;AAChBH,IAAAA,eAAe,CAACD,GAAhB,CAAoB,IAApB,EAA0B,KAAK,CAA/B;;AACAT,IAAAA,sBAAsB,CAAC,IAAD,EAAOU,eAAP,EAAwBG,MAAxB,EAAgC,GAAhC,CAAtB;;AACA,SAAKC,OAAL,GAAe,EAAf;AACA,SAAKC,uBAAL,GAA+B,IAAIC,GAAJ,EAA/B;AACA,SAAKC,oBAAL,GAA4B,IAAID,GAAJ,EAA5B;AACA,SAAKE,WAAL,GAAmB,IAAIF,GAAJ,EAAnB;AACA,SAAKG,QAAL,GAAgB,IAAIH,GAAJ,EAAhB;AACA,SAAKI,OAAL,GAAe,IAAIJ,GAAJ,EAAf;AACA,SAAKK,OAAL,GAAe,IAAIL,GAAJ,EAAf;AACA,SAAKM,cAAL,GAAsB,IAAIN,GAAJ,EAAtB;AACH;;AACDO,EAAAA,SAAS,CAACC,MAAD,EAAS;AACd,WAAO,KAAKV,OAAL,CAAaW,IAAb,CAAmBC,CAAD,IAAOA,CAAC,CAACC,EAAF,KAASH,MAAlC,CAAP;AACH;;AACDI,EAAAA,SAAS,CAACJ,MAAD,EAAS;AACd,QAAI,CAAC,KAAKD,SAAL,CAAeC,MAAM,CAACG,EAAtB,CAAL,EAAgC;AAC5B,WAAKb,OAAL,CAAae,IAAb,CAAkBL,MAAlB;AACH;AACJ;;AACDM,EAAAA,mBAAmB,CAACC,SAAD,EAAY;AAC3B,UAAMC,GAAG,GAAG,IAAIhB,GAAJ,EAAZ;;AACA,SAAK,MAAMQ,MAAX,IAAqB,KAAKV,OAA1B,EAAmC;AAC/B,UAAI,CAACU,MAAM,CAACS,WAAP,CAAmBF,SAAS,CAACG,aAA7B,CAAL,EAAkD;AAC9C;AACH;;AACDF,MAAAA,GAAG,CAACvB,GAAJ,CAAQe,MAAM,CAACG,EAAf,EAAmBH,MAAM,CAACD,SAAP,CAAiBQ,SAAjB,CAAnB;AACH;;AACD,WAAOC,GAAP;AACH;;AACDG,EAAAA,WAAW,CAACC,OAAD,EAAUC,aAAV,EAAyB;AAChC,SAAK,MAAMb,MAAX,IAAqB,KAAKV,OAA1B,EAAmC;AAC/BU,MAAAA,MAAM,CAACW,WAAP,CAAmBC,OAAnB,EAA4BC,aAA5B;AACH;AACJ;;AACDC,EAAAA,SAAS,CAACC,MAAD,EAAS;AACd,WAAO,KAAKnB,OAAL,CAAaoB,GAAb,CAAiBD,MAAjB,CAAP;AACH;;AACDE,EAAAA,SAAS,CAACC,SAAD,EAAYN,OAAZ,EAAuC;AAAA,QAAlBO,QAAkB,uEAAP,KAAO;;AAC5C,QAAIA,QAAQ,IAAI,CAAC,KAAKL,SAAL,CAAeI,SAAf,CAAjB,EAA4C;AACxC,WAAKtB,OAAL,CAAaX,GAAb,CAAiBiC,SAAjB,EAA4BN,OAA5B;AACH;AACJ;;AACDQ,EAAAA,cAAc,CAACC,IAAD,EAAOC,MAAP,EAAe;AACzB,QAAI,CAAC,KAAKC,cAAL,CAAoBF,IAApB,CAAL,EAAgC;AAC5B,WAAKxB,OAAL,CAAaZ,GAAb,CAAiBoC,IAAjB,EAAuBC,MAAvB;AACH;AACJ;;AACDC,EAAAA,cAAc,CAACF,IAAD,EAAO;AACjB,WAAO,KAAKxB,OAAL,CAAamB,GAAb,CAAiBK,IAAjB,CAAP;AACH;;AACDG,EAAAA,kBAAkB,GAAG;AACjB,WAAO,KAAK3B,OAAL,CAAa4B,IAAb,EAAP;AACH;;AACDC,EAAAA,gBAAgB,CAACL,IAAD,EAAO;AACnB,WAAO,KAAKvB,cAAL,CAAoBkB,GAApB,CAAwBK,IAAxB,CAAP;AACH;;AACDM,EAAAA,gBAAgB,CAACN,IAAD,EAAOO,aAAP,EAAsB;AAClC,QAAI,CAAC,KAAKF,gBAAL,CAAsBL,IAAtB,CAAL,EAAkC;AAC9B,WAAKvB,cAAL,CAAoBb,GAApB,CAAwBoC,IAAxB,EAA8BO,aAA9B;AACH;AACJ;;AACDC,EAAAA,cAAc,CAACtB,SAAD,EAA2B;AAAA,QAAfuB,KAAe,uEAAP,KAAO;AACrC,QAAItB,GAAG,GAAG,KAAKd,WAAL,CAAiBsB,GAAjB,CAAqBT,SAArB,CAAV;;AACA,QAAI,CAACC,GAAD,IAAQsB,KAAZ,EAAmB;AACftB,MAAAA,GAAG,GAAG,CAAC,GAAG,KAAKjB,uBAAL,CAA6BwC,MAA7B,EAAJ,EAA2CC,GAA3C,CAAgD9B,CAAD,IAAOA,CAAC,CAACK,SAAD,CAAvD,CAAN;AACA,WAAKb,WAAL,CAAiBT,GAAjB,CAAqBsB,SAArB,EAAgCC,GAAhC;AACH;;AACD,WAAOA,GAAP;AACH;;AACDyB,EAAAA,aAAa,CAACC,IAAD,EAAOC,cAAP,EAAuB;AAChC,SAAK5C,uBAAL,CAA6BN,GAA7B,CAAiCiD,IAAjC,EAAuCC,cAAvC;AACH;;AACDC,EAAAA,WAAW,CAAC7B,SAAD,EAA2B;AAAA,QAAfuB,KAAe,uEAAP,KAAO;AAClC,QAAItB,GAAG,GAAG,KAAKb,QAAL,CAAcqB,GAAd,CAAkBT,SAAlB,CAAV;;AACA,QAAI,CAACC,GAAD,IAAQsB,KAAZ,EAAmB;AACftB,MAAAA,GAAG,GAAG,CAAC,GAAG,KAAKf,oBAAL,CAA0BsC,MAA1B,EAAJ,EAAwCC,GAAxC,CAA6C9B,CAAD,IAAOA,CAAC,CAACK,SAAD,CAApD,CAAN;AACA,WAAKZ,QAAL,CAAcV,GAAd,CAAkBsB,SAAlB,EAA6BC,GAA7B;AACH;;AACD,WAAOA,GAAP;AACH;;AACD6B,EAAAA,kBAAkB,CAACH,IAAD,EAAOI,WAAP,EAAoB;AAClC,SAAK7C,oBAAL,CAA0BR,GAA1B,CAA8BiD,IAA9B,EAAoCI,WAApC;AACH;;AApFgB;AAsFrBpD,eAAe,GAAG,IAAIqD,OAAJ,EAAlB", "sourcesContent": ["var __classPrivateFieldSet = (this && this.__classPrivateFieldSet) || function (receiver, state, value, kind, f) {\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\n};\nvar _Plugins_engine;\nexport class Plugins {\n    constructor(engine) {\n        _Plugins_engine.set(this, void 0);\n        __classPrivateFieldSet(this, _Plugins_engine, engine, \"f\");\n        this.plugins = [];\n        this.interactorsInitializers = new Map();\n        this.updatersInitializers = new Map();\n        this.interactors = new Map();\n        this.updaters = new Map();\n        this.presets = new Map();\n        this.drawers = new Map();\n        this.pathGenerators = new Map();\n    }\n    getPlugin(plugin) {\n        return this.plugins.find((t) => t.id === plugin);\n    }\n    addPlugin(plugin) {\n        if (!this.getPlugin(plugin.id)) {\n            this.plugins.push(plugin);\n        }\n    }\n    getAvailablePlugins(container) {\n        const res = new Map();\n        for (const plugin of this.plugins) {\n            if (!plugin.needsPlugin(container.actualOptions)) {\n                continue;\n            }\n            res.set(plugin.id, plugin.getPlugin(container));\n        }\n        return res;\n    }\n    loadOptions(options, sourceOptions) {\n        for (const plugin of this.plugins) {\n            plugin.loadOptions(options, sourceOptions);\n        }\n    }\n    getPreset(preset) {\n        return this.presets.get(preset);\n    }\n    addPreset(presetKey, options, override = false) {\n        if (override || !this.getPreset(presetKey)) {\n            this.presets.set(presetKey, options);\n        }\n    }\n    addShapeDrawer(type, drawer) {\n        if (!this.getShapeDrawer(type)) {\n            this.drawers.set(type, drawer);\n        }\n    }\n    getShapeDrawer(type) {\n        return this.drawers.get(type);\n    }\n    getSupportedShapes() {\n        return this.drawers.keys();\n    }\n    getPathGenerator(type) {\n        return this.pathGenerators.get(type);\n    }\n    addPathGenerator(type, pathGenerator) {\n        if (!this.getPathGenerator(type)) {\n            this.pathGenerators.set(type, pathGenerator);\n        }\n    }\n    getInteractors(container, force = false) {\n        let res = this.interactors.get(container);\n        if (!res || force) {\n            res = [...this.interactorsInitializers.values()].map((t) => t(container));\n            this.interactors.set(container, res);\n        }\n        return res;\n    }\n    addInteractor(name, initInteractor) {\n        this.interactorsInitializers.set(name, initInteractor);\n    }\n    getUpdaters(container, force = false) {\n        let res = this.updaters.get(container);\n        if (!res || force) {\n            res = [...this.updatersInitializers.values()].map((t) => t(container));\n            this.updaters.set(container, res);\n        }\n        return res;\n    }\n    addParticleUpdater(name, initUpdater) {\n        this.updatersInitializers.set(name, initUpdater);\n    }\n}\n_Plugins_engine = new WeakMap();\n"]}, "metadata": {}, "sourceType": "module"}
{"ast": null, "code": "import { OptionsColor } from \"../../OptionsColor\";\nexport class LightGradient {\n  constructor() {\n    this.start = new OptionsColor();\n    this.stop = new OptionsColor();\n    this.start.value = \"#ffffff\";\n    this.stop.value = \"#000000\";\n  }\n\n  load(data) {\n    if (data === undefined) {\n      return;\n    }\n\n    this.start = OptionsColor.create(this.start, data.start);\n    this.stop = OptionsColor.create(this.stop, data.stop);\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Options/Classes/Interactivity/Modes/LightGradient.js"], "names": ["OptionsColor", "LightGradient", "constructor", "start", "stop", "value", "load", "data", "undefined", "create"], "mappings": "AAAA,SAASA,YAAT,QAA6B,oBAA7B;AACA,OAAO,MAAMC,aAAN,CAAoB;AACvBC,EAAAA,WAAW,GAAG;AACV,SAAKC,KAAL,GAAa,IAAIH,YAAJ,EAAb;AACA,SAAKI,IAAL,GAAY,IAAIJ,YAAJ,EAAZ;AACA,SAAKG,KAAL,CAAWE,KAAX,GAAmB,SAAnB;AACA,SAAKD,IAAL,CAAUC,KAAV,GAAkB,SAAlB;AACH;;AACDC,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,QAAIA,IAAI,KAAKC,SAAb,EAAwB;AACpB;AACH;;AACD,SAAKL,KAAL,GAAaH,YAAY,CAACS,MAAb,CAAoB,KAAKN,KAAzB,EAAgCI,IAAI,CAACJ,KAArC,CAAb;AACA,SAAKC,IAAL,GAAYJ,YAAY,CAACS,MAAb,CAAoB,KAAKL,IAAzB,EAA+BG,IAAI,CAACH,IAApC,CAAZ;AACH;;AAbsB", "sourcesContent": ["import { OptionsColor } from \"../../OptionsColor\";\nexport class LightGradient {\n    constructor() {\n        this.start = new OptionsColor();\n        this.stop = new OptionsColor();\n        this.start.value = \"#ffffff\";\n        this.stop.value = \"#000000\";\n    }\n    load(data) {\n        if (data === undefined) {\n            return;\n        }\n        this.start = OptionsColor.create(this.start, data.start);\n        this.stop = OptionsColor.create(this.stop, data.stop);\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
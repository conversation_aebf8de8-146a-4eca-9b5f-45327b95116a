{"ast": null, "code": "import { Collider } from \"./Collider\";\nexport async function loadParticlesCollisionsInteraction(engine) {\n  await engine.addInteractor(\"particlesCollisions\", container => new Collider(container));\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Interactions/Particles/Collisions/index.js"], "names": ["Collider", "loadParticlesCollisionsInteraction", "engine", "addInteractor", "container"], "mappings": "AAAA,SAASA,QAAT,QAAyB,YAAzB;AACA,OAAO,eAAeC,kCAAf,CAAkDC,MAAlD,EAA0D;AAC7D,QAAMA,MAAM,CAACC,aAAP,CAAqB,qBAArB,EAA6CC,SAAD,IAAe,IAAIJ,QAAJ,CAAaI,SAAb,CAA3D,CAAN;AACH", "sourcesContent": ["import { Collider } from \"./Collider\";\nexport async function loadParticlesCollisionsInteraction(engine) {\n    await engine.addInteractor(\"particlesCollisions\", (container) => new Collider(container));\n}\n"]}, "metadata": {}, "sourceType": "module"}
import { type Container, type IDelta, OutMode, type OutModeAlt, OutModeDirection, type Particle } from "tsparticles-engine";
import type { IOutModeManager } from "./IOutModeManager";
export declare class OutOutMode implements IOutModeManager {
    private readonly container;
    modes: (OutMode | OutModeAlt | keyof typeof OutMode)[];
    constructor(container: Container);
    update(particle: Particle, direction: OutModeDirection, delta: IDelta, outMode: OutMode | OutModeAlt | keyof typeof OutMode): void;
}

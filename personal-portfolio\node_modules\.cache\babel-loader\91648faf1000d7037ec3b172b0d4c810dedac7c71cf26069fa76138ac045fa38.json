{"ast": null, "code": "import { ValueWithRandom } from \"@tsparticles/engine\";\nexport class SplitRate extends ValueWithRandom {\n  constructor() {\n    super();\n    this.value = {\n      min: 4,\n      max: 9\n    };\n  }\n}", "map": {"version": 3, "names": ["ValueWithRandom", "SplitRate", "constructor", "value", "min", "max"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/updater-destroy/browser/Options/Classes/SplitRate.js"], "sourcesContent": ["import { ValueWithRandom } from \"@tsparticles/engine\";\nexport class SplitRate extends ValueWithRandom {\n    constructor() {\n        super();\n        this.value = { min: 4, max: 9 };\n    }\n}\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,qBAAqB;AACrD,OAAO,MAAMC,SAAS,SAASD,eAAe,CAAC;EAC3CE,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,KAAK,GAAG;MAAEC,GAAG,EAAE,CAAC;MAAEC,GAAG,EAAE;IAAE,CAAC;EACnC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { Events } from \"./Events/Events\";\nimport { Modes } from \"./Modes/Modes\";\nexport class Interactivity {\n  constructor() {\n    this.detectsOn = \"window\";\n    this.events = new Events();\n    this.modes = new Modes();\n  }\n\n  get detect_on() {\n    return this.detectsOn;\n  }\n\n  set detect_on(value) {\n    this.detectsOn = value;\n  }\n\n  load(data) {\n    var _a, _b, _c;\n\n    if (data === undefined) {\n      return;\n    }\n\n    const detectsOn = (_a = data.detectsOn) !== null && _a !== void 0 ? _a : data.detect_on;\n\n    if (detectsOn !== undefined) {\n      this.detectsOn = detectsOn;\n    }\n\n    this.events.load(data.events);\n    this.modes.load(data.modes);\n\n    if (((_c = (_b = data.modes) === null || _b === void 0 ? void 0 : _b.slow) === null || _c === void 0 ? void 0 : _c.active) === true) {\n      if (this.events.onHover.mode instanceof Array) {\n        if (this.events.onHover.mode.indexOf(\"slow\") < 0) {\n          this.events.onHover.mode.push(\"slow\");\n        }\n      } else if (this.events.onHover.mode !== \"slow\") {\n        this.events.onHover.mode = [this.events.onHover.mode, \"slow\"];\n      }\n    }\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Options/Classes/Interactivity/Interactivity.js"], "names": ["Events", "Modes", "Interactivity", "constructor", "detectsOn", "events", "modes", "detect_on", "value", "load", "data", "_a", "_b", "_c", "undefined", "slow", "active", "onHover", "mode", "Array", "indexOf", "push"], "mappings": "AAAA,SAASA,MAAT,QAAuB,iBAAvB;AACA,SAASC,KAAT,QAAsB,eAAtB;AACA,OAAO,MAAMC,aAAN,CAAoB;AACvBC,EAAAA,WAAW,GAAG;AACV,SAAKC,SAAL,GAAiB,QAAjB;AACA,SAAKC,MAAL,GAAc,IAAIL,MAAJ,EAAd;AACA,SAAKM,KAAL,GAAa,IAAIL,KAAJ,EAAb;AACH;;AACY,MAATM,SAAS,GAAG;AACZ,WAAO,KAAKH,SAAZ;AACH;;AACY,MAATG,SAAS,CAACC,KAAD,EAAQ;AACjB,SAAKJ,SAAL,GAAiBI,KAAjB;AACH;;AACDC,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,QAAIC,EAAJ,EAAQC,EAAR,EAAYC,EAAZ;;AACA,QAAIH,IAAI,KAAKI,SAAb,EAAwB;AACpB;AACH;;AACD,UAAMV,SAAS,GAAG,CAACO,EAAE,GAAGD,IAAI,CAACN,SAAX,MAA0B,IAA1B,IAAkCO,EAAE,KAAK,KAAK,CAA9C,GAAkDA,EAAlD,GAAuDD,IAAI,CAACH,SAA9E;;AACA,QAAIH,SAAS,KAAKU,SAAlB,EAA6B;AACzB,WAAKV,SAAL,GAAiBA,SAAjB;AACH;;AACD,SAAKC,MAAL,CAAYI,IAAZ,CAAiBC,IAAI,CAACL,MAAtB;AACA,SAAKC,KAAL,CAAWG,IAAX,CAAgBC,IAAI,CAACJ,KAArB;;AACA,QAAI,CAAC,CAACO,EAAE,GAAG,CAACD,EAAE,GAAGF,IAAI,CAACJ,KAAX,MAAsB,IAAtB,IAA8BM,EAAE,KAAK,KAAK,CAA1C,GAA8C,KAAK,CAAnD,GAAuDA,EAAE,CAACG,IAAhE,MAA0E,IAA1E,IAAkFF,EAAE,KAAK,KAAK,CAA9F,GAAkG,KAAK,CAAvG,GAA2GA,EAAE,CAACG,MAA/G,MAA2H,IAA/H,EAAqI;AACjI,UAAI,KAAKX,MAAL,CAAYY,OAAZ,CAAoBC,IAApB,YAAoCC,KAAxC,EAA+C;AAC3C,YAAI,KAAKd,MAAL,CAAYY,OAAZ,CAAoBC,IAApB,CAAyBE,OAAzB,CAAiC,MAAjC,IAA2C,CAA/C,EAAkD;AAC9C,eAAKf,MAAL,CAAYY,OAAZ,CAAoBC,IAApB,CAAyBG,IAAzB,CAA8B,MAA9B;AACH;AACJ,OAJD,MAKK,IAAI,KAAKhB,MAAL,CAAYY,OAAZ,CAAoBC,IAApB,KAA6B,MAAjC,EAAyC;AAC1C,aAAKb,MAAL,CAAYY,OAAZ,CAAoBC,IAApB,GAA2B,CAAC,KAAKb,MAAL,CAAYY,OAAZ,CAAoBC,IAArB,EAA2B,MAA3B,CAA3B;AACH;AACJ;AACJ;;AAjCsB", "sourcesContent": ["import { Events } from \"./Events/Events\";\nimport { Modes } from \"./Modes/Modes\";\nexport class Interactivity {\n    constructor() {\n        this.detectsOn = \"window\";\n        this.events = new Events();\n        this.modes = new Modes();\n    }\n    get detect_on() {\n        return this.detectsOn;\n    }\n    set detect_on(value) {\n        this.detectsOn = value;\n    }\n    load(data) {\n        var _a, _b, _c;\n        if (data === undefined) {\n            return;\n        }\n        const detectsOn = (_a = data.detectsOn) !== null && _a !== void 0 ? _a : data.detect_on;\n        if (detectsOn !== undefined) {\n            this.detectsOn = detectsOn;\n        }\n        this.events.load(data.events);\n        this.modes.load(data.modes);\n        if (((_c = (_b = data.modes) === null || _b === void 0 ? void 0 : _b.slow) === null || _c === void 0 ? void 0 : _c.active) === true) {\n            if (this.events.onHover.mode instanceof Array) {\n                if (this.events.onHover.mode.indexOf(\"slow\") < 0) {\n                    this.events.onHover.mode.push(\"slow\");\n                }\n            }\n            else if (this.events.onHover.mode !== \"slow\") {\n                this.events.onHover.mode = [this.events.onHover.mode, \"slow\"];\n            }\n        }\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
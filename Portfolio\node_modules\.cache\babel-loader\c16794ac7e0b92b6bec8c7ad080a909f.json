{"ast": null, "code": "import getWindow from \"./getWindow.js\";\nexport default function getWindowScroll(node) {\n  var win = getWindow(node);\n  var scrollLeft = win.pageXOffset;\n  var scrollTop = win.pageYOffset;\n  return {\n    scrollLeft: scrollLeft,\n    scrollTop: scrollTop\n  };\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/@popperjs/core/lib/dom-utils/getWindowScroll.js"], "names": ["getWindow", "getWindowScroll", "node", "win", "scrollLeft", "pageXOffset", "scrollTop", "pageYOffset"], "mappings": "AAAA,OAAOA,SAAP,MAAsB,gBAAtB;AACA,eAAe,SAASC,eAAT,CAAyBC,IAAzB,EAA+B;AAC5C,MAAIC,GAAG,GAAGH,SAAS,CAACE,IAAD,CAAnB;AACA,MAAIE,UAAU,GAAGD,GAAG,CAACE,WAArB;AACA,MAAIC,SAAS,GAAGH,GAAG,CAACI,WAApB;AACA,SAAO;AACLH,IAAAA,UAAU,EAAEA,UADP;AAELE,IAAAA,SAAS,EAAEA;AAFN,GAAP;AAID", "sourcesContent": ["import getWindow from \"./getWindow.js\";\nexport default function getWindowScroll(node) {\n  var win = getWindow(node);\n  var scrollLeft = win.pageXOffset;\n  var scrollTop = win.pageYOffset;\n  return {\n    scrollLeft: scrollLeft,\n    scrollTop: scrollTop\n  };\n}"]}, "metadata": {}, "sourceType": "module"}
{"ast": null, "code": "import { getRandom, getRangeValue, millisecondsToSeconds } from \"@tsparticles/engine\";\nimport { Life } from \"./Options/Classes/Life.js\";\nimport { updateLife } from \"./Utils.js\";\nconst noTime = 0,\n  identity = 1,\n  infiniteValue = -1;\nexport class LifeUpdater {\n  constructor(container) {\n    this.container = container;\n  }\n  init(particle) {\n    const container = this.container,\n      particlesOptions = particle.options,\n      lifeOptions = particlesOptions.life;\n    if (!lifeOptions) {\n      return;\n    }\n    particle.life = {\n      delay: container.retina.reduceFactor ? getRangeValue(lifeOptions.delay.value) * (lifeOptions.delay.sync ? identity : getRandom()) / container.retina.reduceFactor * millisecondsToSeconds : noTime,\n      delayTime: noTime,\n      duration: container.retina.reduceFactor ? getRangeValue(lifeOptions.duration.value) * (lifeOptions.duration.sync ? identity : getRandom()) / container.retina.reduceFactor * millisecondsToSeconds : noTime,\n      time: noTime,\n      count: lifeOptions.count\n    };\n    if (particle.life.duration <= noTime) {\n      particle.life.duration = infiniteValue;\n    }\n    if (particle.life.count <= noTime) {\n      particle.life.count = infiniteValue;\n    }\n    if (particle.life) {\n      particle.spawning = particle.life.delay > noTime;\n    }\n  }\n  isEnabled(particle) {\n    return !particle.destroyed;\n  }\n  loadOptions(options, ...sources) {\n    if (!options.life) {\n      options.life = new Life();\n    }\n    for (const source of sources) {\n      options.life.load(source?.life);\n    }\n  }\n  update(particle, delta) {\n    if (!this.isEnabled(particle) || !particle.life) {\n      return;\n    }\n    updateLife(particle, delta, this.container.canvas.size);\n  }\n}", "map": {"version": 3, "names": ["getRandom", "getRangeValue", "millisecondsToSeconds", "Life", "updateLife", "noTime", "identity", "infiniteValue", "LifeUpdater", "constructor", "container", "init", "particle", "particlesOptions", "options", "lifeOptions", "life", "delay", "retina", "reduceFactor", "value", "sync", "delayTime", "duration", "time", "count", "spawning", "isEnabled", "destroyed", "loadOptions", "sources", "source", "load", "update", "delta", "canvas", "size"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/updater-life/browser/LifeUpdater.js"], "sourcesContent": ["import { getRandom, getRangeValue, millisecondsToSeconds, } from \"@tsparticles/engine\";\nimport { Life } from \"./Options/Classes/Life.js\";\nimport { updateLife } from \"./Utils.js\";\nconst noTime = 0, identity = 1, infiniteValue = -1;\nexport class LifeUpdater {\n    constructor(container) {\n        this.container = container;\n    }\n    init(particle) {\n        const container = this.container, particlesOptions = particle.options, lifeOptions = particlesOptions.life;\n        if (!lifeOptions) {\n            return;\n        }\n        particle.life = {\n            delay: container.retina.reduceFactor\n                ? ((getRangeValue(lifeOptions.delay.value) * (lifeOptions.delay.sync ? identity : getRandom())) /\n                    container.retina.reduceFactor) *\n                    millisecondsToSeconds\n                : noTime,\n            delayTime: noTime,\n            duration: container.retina.reduceFactor\n                ? ((getRangeValue(lifeOptions.duration.value) * (lifeOptions.duration.sync ? identity : getRandom())) /\n                    container.retina.reduceFactor) *\n                    millisecondsToSeconds\n                : noTime,\n            time: noTime,\n            count: lifeOptions.count,\n        };\n        if (particle.life.duration <= noTime) {\n            particle.life.duration = infiniteValue;\n        }\n        if (particle.life.count <= noTime) {\n            particle.life.count = infiniteValue;\n        }\n        if (particle.life) {\n            particle.spawning = particle.life.delay > noTime;\n        }\n    }\n    isEnabled(particle) {\n        return !particle.destroyed;\n    }\n    loadOptions(options, ...sources) {\n        if (!options.life) {\n            options.life = new Life();\n        }\n        for (const source of sources) {\n            options.life.load(source?.life);\n        }\n    }\n    update(particle, delta) {\n        if (!this.isEnabled(particle) || !particle.life) {\n            return;\n        }\n        updateLife(particle, delta, this.container.canvas.size);\n    }\n}\n"], "mappings": "AAAA,SAASA,SAAS,EAAEC,aAAa,EAAEC,qBAAqB,QAAS,qBAAqB;AACtF,SAASC,IAAI,QAAQ,2BAA2B;AAChD,SAASC,UAAU,QAAQ,YAAY;AACvC,MAAMC,MAAM,GAAG,CAAC;EAAEC,QAAQ,GAAG,CAAC;EAAEC,aAAa,GAAG,CAAC,CAAC;AAClD,OAAO,MAAMC,WAAW,CAAC;EACrBC,WAAWA,CAACC,SAAS,EAAE;IACnB,IAAI,CAACA,SAAS,GAAGA,SAAS;EAC9B;EACAC,IAAIA,CAACC,QAAQ,EAAE;IACX,MAAMF,SAAS,GAAG,IAAI,CAACA,SAAS;MAAEG,gBAAgB,GAAGD,QAAQ,CAACE,OAAO;MAAEC,WAAW,GAAGF,gBAAgB,CAACG,IAAI;IAC1G,IAAI,CAACD,WAAW,EAAE;MACd;IACJ;IACAH,QAAQ,CAACI,IAAI,GAAG;MACZC,KAAK,EAAEP,SAAS,CAACQ,MAAM,CAACC,YAAY,GAC5BlB,aAAa,CAACc,WAAW,CAACE,KAAK,CAACG,KAAK,CAAC,IAAIL,WAAW,CAACE,KAAK,CAACI,IAAI,GAAGf,QAAQ,GAAGN,SAAS,CAAC,CAAC,CAAC,GAC1FU,SAAS,CAACQ,MAAM,CAACC,YAAY,GAC7BjB,qBAAqB,GACvBG,MAAM;MACZiB,SAAS,EAAEjB,MAAM;MACjBkB,QAAQ,EAAEb,SAAS,CAACQ,MAAM,CAACC,YAAY,GAC/BlB,aAAa,CAACc,WAAW,CAACQ,QAAQ,CAACH,KAAK,CAAC,IAAIL,WAAW,CAACQ,QAAQ,CAACF,IAAI,GAAGf,QAAQ,GAAGN,SAAS,CAAC,CAAC,CAAC,GAChGU,SAAS,CAACQ,MAAM,CAACC,YAAY,GAC7BjB,qBAAqB,GACvBG,MAAM;MACZmB,IAAI,EAAEnB,MAAM;MACZoB,KAAK,EAAEV,WAAW,CAACU;IACvB,CAAC;IACD,IAAIb,QAAQ,CAACI,IAAI,CAACO,QAAQ,IAAIlB,MAAM,EAAE;MAClCO,QAAQ,CAACI,IAAI,CAACO,QAAQ,GAAGhB,aAAa;IAC1C;IACA,IAAIK,QAAQ,CAACI,IAAI,CAACS,KAAK,IAAIpB,MAAM,EAAE;MAC/BO,QAAQ,CAACI,IAAI,CAACS,KAAK,GAAGlB,aAAa;IACvC;IACA,IAAIK,QAAQ,CAACI,IAAI,EAAE;MACfJ,QAAQ,CAACc,QAAQ,GAAGd,QAAQ,CAACI,IAAI,CAACC,KAAK,GAAGZ,MAAM;IACpD;EACJ;EACAsB,SAASA,CAACf,QAAQ,EAAE;IAChB,OAAO,CAACA,QAAQ,CAACgB,SAAS;EAC9B;EACAC,WAAWA,CAACf,OAAO,EAAE,GAAGgB,OAAO,EAAE;IAC7B,IAAI,CAAChB,OAAO,CAACE,IAAI,EAAE;MACfF,OAAO,CAACE,IAAI,GAAG,IAAIb,IAAI,CAAC,CAAC;IAC7B;IACA,KAAK,MAAM4B,MAAM,IAAID,OAAO,EAAE;MAC1BhB,OAAO,CAACE,IAAI,CAACgB,IAAI,CAACD,MAAM,EAAEf,IAAI,CAAC;IACnC;EACJ;EACAiB,MAAMA,CAACrB,QAAQ,EAAEsB,KAAK,EAAE;IACpB,IAAI,CAAC,IAAI,CAACP,SAAS,CAACf,QAAQ,CAAC,IAAI,CAACA,QAAQ,CAACI,IAAI,EAAE;MAC7C;IACJ;IACAZ,UAAU,CAACQ,QAAQ,EAAEsB,KAAK,EAAE,IAAI,CAACxB,SAAS,CAACyB,MAAM,CAACC,IAAI,CAAC;EAC3D;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
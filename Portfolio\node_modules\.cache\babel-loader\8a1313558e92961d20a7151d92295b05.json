{"ast": null, "code": "export default function buildMatchFn(args) {\n  return function (string) {\n    var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    var width = options.width;\n    var matchPattern = width && args.matchPatterns[width] || args.matchPatterns[args.defaultMatchWidth];\n    var matchResult = string.match(matchPattern);\n\n    if (!matchResult) {\n      return null;\n    }\n\n    var matchedString = matchResult[0];\n    var parsePatterns = width && args.parsePatterns[width] || args.parsePatterns[args.defaultParseWidth];\n    var key = Array.isArray(parsePatterns) ? findIndex(parsePatterns, function (pattern) {\n      return pattern.test(matchedString);\n    }) : findKey(parsePatterns, function (pattern) {\n      return pattern.test(matchedString);\n    });\n    var value;\n    value = args.valueCallback ? args.valueCallback(key) : key;\n    value = options.valueCallback ? options.valueCallback(value) : value;\n    var rest = string.slice(matchedString.length);\n    return {\n      value: value,\n      rest: rest\n    };\n  };\n}\n\nfunction findKey(object, predicate) {\n  for (var key in object) {\n    if (object.hasOwnProperty(key) && predicate(object[key])) {\n      return key;\n    }\n  }\n\n  return undefined;\n}\n\nfunction findIndex(array, predicate) {\n  for (var key = 0; key < array.length; key++) {\n    if (predicate(array[key])) {\n      return key;\n    }\n  }\n\n  return undefined;\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/date-fns/esm/locale/_lib/buildMatchFn/index.js"], "names": ["buildMatchFn", "args", "string", "options", "arguments", "length", "undefined", "width", "matchPattern", "matchPatterns", "defaultMatchWidth", "matchResult", "match", "matchedString", "parsePatterns", "defaultParseWidth", "key", "Array", "isArray", "findIndex", "pattern", "test", "<PERSON><PERSON><PERSON>", "value", "valueCallback", "rest", "slice", "object", "predicate", "hasOwnProperty", "array"], "mappings": "AAAA,eAAe,SAASA,YAAT,CAAsBC,IAAtB,EAA4B;AACzC,SAAO,UAAUC,MAAV,EAAkB;AACvB,QAAIC,OAAO,GAAGC,SAAS,CAACC,MAAV,GAAmB,CAAnB,IAAwBD,SAAS,CAAC,CAAD,CAAT,KAAiBE,SAAzC,GAAqDF,SAAS,CAAC,CAAD,CAA9D,GAAoE,EAAlF;AACA,QAAIG,KAAK,GAAGJ,OAAO,CAACI,KAApB;AACA,QAAIC,YAAY,GAAGD,KAAK,IAAIN,IAAI,CAACQ,aAAL,CAAmBF,KAAnB,CAAT,IAAsCN,IAAI,CAACQ,aAAL,CAAmBR,IAAI,CAACS,iBAAxB,CAAzD;AACA,QAAIC,WAAW,GAAGT,MAAM,CAACU,KAAP,CAAaJ,YAAb,CAAlB;;AAEA,QAAI,CAACG,WAAL,EAAkB;AAChB,aAAO,IAAP;AACD;;AAED,QAAIE,aAAa,GAAGF,WAAW,CAAC,CAAD,CAA/B;AACA,QAAIG,aAAa,GAAGP,KAAK,IAAIN,IAAI,CAACa,aAAL,CAAmBP,KAAnB,CAAT,IAAsCN,IAAI,CAACa,aAAL,CAAmBb,IAAI,CAACc,iBAAxB,CAA1D;AACA,QAAIC,GAAG,GAAGC,KAAK,CAACC,OAAN,CAAcJ,aAAd,IAA+BK,SAAS,CAACL,aAAD,EAAgB,UAAUM,OAAV,EAAmB;AACnF,aAAOA,OAAO,CAACC,IAAR,CAAaR,aAAb,CAAP;AACD,KAFiD,CAAxC,GAELS,OAAO,CAACR,aAAD,EAAgB,UAAUM,OAAV,EAAmB;AAC7C,aAAOA,OAAO,CAACC,IAAR,CAAaR,aAAb,CAAP;AACD,KAFW,CAFZ;AAKA,QAAIU,KAAJ;AACAA,IAAAA,KAAK,GAAGtB,IAAI,CAACuB,aAAL,GAAqBvB,IAAI,CAACuB,aAAL,CAAmBR,GAAnB,CAArB,GAA+CA,GAAvD;AACAO,IAAAA,KAAK,GAAGpB,OAAO,CAACqB,aAAR,GAAwBrB,OAAO,CAACqB,aAAR,CAAsBD,KAAtB,CAAxB,GAAuDA,KAA/D;AACA,QAAIE,IAAI,GAAGvB,MAAM,CAACwB,KAAP,CAAab,aAAa,CAACR,MAA3B,CAAX;AACA,WAAO;AACLkB,MAAAA,KAAK,EAAEA,KADF;AAELE,MAAAA,IAAI,EAAEA;AAFD,KAAP;AAID,GAzBD;AA0BD;;AAED,SAASH,OAAT,CAAiBK,MAAjB,EAAyBC,SAAzB,EAAoC;AAClC,OAAK,IAAIZ,GAAT,IAAgBW,MAAhB,EAAwB;AACtB,QAAIA,MAAM,CAACE,cAAP,CAAsBb,GAAtB,KAA8BY,SAAS,CAACD,MAAM,CAACX,GAAD,CAAP,CAA3C,EAA0D;AACxD,aAAOA,GAAP;AACD;AACF;;AAED,SAAOV,SAAP;AACD;;AAED,SAASa,SAAT,CAAmBW,KAAnB,EAA0BF,SAA1B,EAAqC;AACnC,OAAK,IAAIZ,GAAG,GAAG,CAAf,EAAkBA,GAAG,GAAGc,KAAK,CAACzB,MAA9B,EAAsCW,GAAG,EAAzC,EAA6C;AAC3C,QAAIY,SAAS,CAACE,KAAK,CAACd,GAAD,CAAN,CAAb,EAA2B;AACzB,aAAOA,GAAP;AACD;AACF;;AAED,SAAOV,SAAP;AACD", "sourcesContent": ["export default function buildMatchFn(args) {\n  return function (string) {\n    var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    var width = options.width;\n    var matchPattern = width && args.matchPatterns[width] || args.matchPatterns[args.defaultMatchWidth];\n    var matchResult = string.match(matchPattern);\n\n    if (!matchResult) {\n      return null;\n    }\n\n    var matchedString = matchResult[0];\n    var parsePatterns = width && args.parsePatterns[width] || args.parsePatterns[args.defaultParseWidth];\n    var key = Array.isArray(parsePatterns) ? findIndex(parsePatterns, function (pattern) {\n      return pattern.test(matchedString);\n    }) : findKey(parsePatterns, function (pattern) {\n      return pattern.test(matchedString);\n    });\n    var value;\n    value = args.valueCallback ? args.valueCallback(key) : key;\n    value = options.valueCallback ? options.valueCallback(value) : value;\n    var rest = string.slice(matchedString.length);\n    return {\n      value: value,\n      rest: rest\n    };\n  };\n}\n\nfunction findKey(object, predicate) {\n  for (var key in object) {\n    if (object.hasOwnProperty(key) && predicate(object[key])) {\n      return key;\n    }\n  }\n\n  return undefined;\n}\n\nfunction findIndex(array, predicate) {\n  for (var key = 0; key < array.length; key++) {\n    if (predicate(array[key])) {\n      return key;\n    }\n  }\n\n  return undefined;\n}"]}, "metadata": {}, "sourceType": "module"}
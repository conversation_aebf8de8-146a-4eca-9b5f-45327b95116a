{"ast": null, "code": "export default function mergeByName(modifiers) {\n  var merged = modifiers.reduce(function (merged, current) {\n    var existing = merged[current.name];\n    merged[current.name] = existing ? Object.assign({}, existing, current, {\n      options: Object.assign({}, existing.options, current.options),\n      data: Object.assign({}, existing.data, current.data)\n    }) : current;\n    return merged;\n  }, {}); // IE11 does not support Object.values\n\n  return Object.keys(merged).map(function (key) {\n    return merged[key];\n  });\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/@popperjs/core/lib/utils/mergeByName.js"], "names": ["mergeByName", "modifiers", "merged", "reduce", "current", "existing", "name", "Object", "assign", "options", "data", "keys", "map", "key"], "mappings": "AAAA,eAAe,SAASA,WAAT,CAAqBC,SAArB,EAAgC;AAC7C,MAAIC,MAAM,GAAGD,SAAS,CAACE,MAAV,CAAiB,UAAUD,MAAV,EAAkBE,OAAlB,EAA2B;AACvD,QAAIC,QAAQ,GAAGH,MAAM,CAACE,OAAO,CAACE,IAAT,CAArB;AACAJ,IAAAA,MAAM,CAACE,OAAO,CAACE,IAAT,CAAN,GAAuBD,QAAQ,GAAGE,MAAM,CAACC,MAAP,CAAc,EAAd,EAAkBH,QAAlB,EAA4BD,OAA5B,EAAqC;AACrEK,MAAAA,OAAO,EAAEF,MAAM,CAACC,MAAP,CAAc,EAAd,EAAkBH,QAAQ,CAACI,OAA3B,EAAoCL,OAAO,CAACK,OAA5C,CAD4D;AAErEC,MAAAA,IAAI,EAAEH,MAAM,CAACC,MAAP,CAAc,EAAd,EAAkBH,QAAQ,CAACK,IAA3B,EAAiCN,OAAO,CAACM,IAAzC;AAF+D,KAArC,CAAH,GAG1BN,OAHL;AAIA,WAAOF,MAAP;AACD,GAPY,EAOV,EAPU,CAAb,CAD6C,CAQrC;;AAER,SAAOK,MAAM,CAACI,IAAP,CAAYT,MAAZ,EAAoBU,GAApB,CAAwB,UAAUC,GAAV,EAAe;AAC5C,WAAOX,MAAM,CAACW,GAAD,CAAb;AACD,GAFM,CAAP;AAGD", "sourcesContent": ["export default function mergeByName(modifiers) {\n  var merged = modifiers.reduce(function (merged, current) {\n    var existing = merged[current.name];\n    merged[current.name] = existing ? Object.assign({}, existing, current, {\n      options: Object.assign({}, existing.options, current.options),\n      data: Object.assign({}, existing.data, current.data)\n    }) : current;\n    return merged;\n  }, {}); // IE11 does not support Object.values\n\n  return Object.keys(merged).map(function (key) {\n    return merged[key];\n  });\n}"]}, "metadata": {}, "sourceType": "module"}
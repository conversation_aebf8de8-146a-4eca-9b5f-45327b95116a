{"ast": null, "code": "import { OptionsColor } from \"@tsparticles/engine\";\nexport class LinksTriangle {\n  constructor() {\n    this.enable = false;\n    this.frequency = 1;\n  }\n  load(data) {\n    if (!data) {\n      return;\n    }\n    if (data.color !== undefined) {\n      this.color = OptionsColor.create(this.color, data.color);\n    }\n    if (data.enable !== undefined) {\n      this.enable = data.enable;\n    }\n    if (data.frequency !== undefined) {\n      this.frequency = data.frequency;\n    }\n    if (data.opacity !== undefined) {\n      this.opacity = data.opacity;\n    }\n  }\n}", "map": {"version": 3, "names": ["OptionsColor", "LinksTriangle", "constructor", "enable", "frequency", "load", "data", "color", "undefined", "create", "opacity"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/interaction-particles-links/browser/Options/Classes/LinksTriangle.js"], "sourcesContent": ["import { OptionsColor } from \"@tsparticles/engine\";\nexport class LinksTriangle {\n    constructor() {\n        this.enable = false;\n        this.frequency = 1;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.color !== undefined) {\n            this.color = OptionsColor.create(this.color, data.color);\n        }\n        if (data.enable !== undefined) {\n            this.enable = data.enable;\n        }\n        if (data.frequency !== undefined) {\n            this.frequency = data.frequency;\n        }\n        if (data.opacity !== undefined) {\n            this.opacity = data.opacity;\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,qBAAqB;AAClD,OAAO,MAAMC,aAAa,CAAC;EACvBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,MAAM,GAAG,KAAK;IACnB,IAAI,CAACC,SAAS,GAAG,CAAC;EACtB;EACAC,IAAIA,CAACC,IAAI,EAAE;IACP,IAAI,CAACA,IAAI,EAAE;MACP;IACJ;IACA,IAAIA,IAAI,CAACC,KAAK,KAAKC,SAAS,EAAE;MAC1B,IAAI,CAACD,KAAK,GAAGP,YAAY,CAACS,MAAM,CAAC,IAAI,CAACF,KAAK,EAAED,IAAI,CAACC,KAAK,CAAC;IAC5D;IACA,IAAID,IAAI,CAACH,MAAM,KAAKK,SAAS,EAAE;MAC3B,IAAI,CAACL,MAAM,GAAGG,IAAI,CAACH,MAAM;IAC7B;IACA,IAAIG,IAAI,CAACF,SAAS,KAAKI,SAAS,EAAE;MAC9B,IAAI,CAACJ,SAAS,GAAGE,IAAI,CAACF,SAAS;IACnC;IACA,IAAIE,IAAI,CAACI,OAAO,KAAKF,SAAS,EAAE;MAC5B,IAAI,CAACE,OAAO,GAAGJ,IAAI,CAACI,OAAO;IAC/B;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
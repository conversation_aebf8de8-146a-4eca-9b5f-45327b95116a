{"ast": null, "code": "import { CircleDrawer } from \"./CircleDrawer\";\nexport async function loadCircleShape(engine) {\n  await engine.addShape(\"circle\", new CircleDrawer());\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Shapes/Circle/index.js"], "names": ["CircleDrawer", "loadCircleShape", "engine", "addShape"], "mappings": "AAAA,SAASA,YAAT,QAA6B,gBAA7B;AACA,OAAO,eAAeC,eAAf,CAA+BC,MAA/B,EAAuC;AAC1C,QAAMA,MAAM,CAACC,QAAP,CAAgB,QAAhB,EAA0B,IAAIH,YAAJ,EAA1B,CAAN;AACH", "sourcesContent": ["import { CircleDrawer } from \"./CircleDrawer\";\nexport async function loadCircleShape(engine) {\n    await engine.addShape(\"circle\", new CircleDrawer());\n}\n"]}, "metadata": {}, "sourceType": "module"}
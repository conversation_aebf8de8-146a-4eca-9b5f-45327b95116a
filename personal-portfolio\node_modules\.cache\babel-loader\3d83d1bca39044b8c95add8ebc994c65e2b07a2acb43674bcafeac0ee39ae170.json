{"ast": null, "code": "import { getLogger, getPosition } from \"../Utils/Utils.js\";\nimport { EventType } from \"../Enums/Types/EventType.js\";\nimport { InteractionManager } from \"./Utils/InteractionManager.js\";\nimport { LimitMode } from \"../Enums/Modes/LimitMode.js\";\nimport { Particle } from \"./Particle.js\";\nimport { Point } from \"./Utils/Point.js\";\nimport { QuadTree } from \"./Utils/QuadTree.js\";\nimport { Rectangle } from \"./Utils/Ranges.js\";\nimport { errorPrefix } from \"./Utils/Constants.js\";\nconst qTreeCapacity = 4,\n  squareExp = 2,\n  defaultRemoveQuantity = 1;\nconst qTreeRectangle = canvasSize => {\n  const {\n      height,\n      width\n    } = canvasSize,\n    posOffset = -0.25,\n    sizeFactor = 1.5;\n  return new Rectangle(posOffset * width, posOffset * height, sizeFactor * width, sizeFactor * height);\n};\nexport class Particles {\n  constructor(engine, container) {\n    this._addToPool = (...particles) => {\n      this._pool.push(...particles);\n    };\n    this._applyDensity = (options, manualCount, group) => {\n      const numberOptions = options.number;\n      if (!options.number.density?.enable) {\n        if (group === undefined) {\n          this._limit = numberOptions.limit.value;\n        } else if (numberOptions.limit) {\n          this._groupLimits.set(group, numberOptions.limit.value);\n        }\n        return;\n      }\n      const densityFactor = this._initDensityFactor(numberOptions.density),\n        optParticlesNumber = numberOptions.value,\n        minLimit = 0,\n        optParticlesLimit = numberOptions.limit.value > minLimit ? numberOptions.limit.value : optParticlesNumber,\n        particlesNumber = Math.min(optParticlesNumber, optParticlesLimit) * densityFactor + manualCount,\n        particlesCount = Math.min(this.count, this.filter(t => t.group === group).length);\n      if (group === undefined) {\n        this._limit = numberOptions.limit.value * densityFactor;\n      } else {\n        this._groupLimits.set(group, numberOptions.limit.value * densityFactor);\n      }\n      if (particlesCount < particlesNumber) {\n        this.push(Math.abs(particlesNumber - particlesCount), undefined, options, group);\n      } else if (particlesCount > particlesNumber) {\n        this.removeQuantity(particlesCount - particlesNumber, group);\n      }\n    };\n    this._initDensityFactor = densityOptions => {\n      const container = this._container,\n        defaultFactor = 1;\n      if (!container.canvas.element || !densityOptions.enable) {\n        return defaultFactor;\n      }\n      const canvas = container.canvas.element,\n        pxRatio = container.retina.pixelRatio;\n      return canvas.width * canvas.height / (densityOptions.height * densityOptions.width * pxRatio ** squareExp);\n    };\n    this._pushParticle = (position, overrideOptions, group, initializer) => {\n      try {\n        let particle = this._pool.pop();\n        if (!particle) {\n          particle = new Particle(this._engine, this._container);\n        }\n        particle.init(this._nextId, position, overrideOptions, group);\n        let canAdd = true;\n        if (initializer) {\n          canAdd = initializer(particle);\n        }\n        if (!canAdd) {\n          return;\n        }\n        this._array.push(particle);\n        this._zArray.push(particle);\n        this._nextId++;\n        this._engine.dispatchEvent(EventType.particleAdded, {\n          container: this._container,\n          data: {\n            particle\n          }\n        });\n        return particle;\n      } catch (e) {\n        getLogger().warning(`${errorPrefix} adding particle: ${e}`);\n      }\n    };\n    this._removeParticle = (index, group, override) => {\n      const particle = this._array[index];\n      if (!particle || particle.group !== group) {\n        return false;\n      }\n      const zIdx = this._zArray.indexOf(particle),\n        deleteCount = 1;\n      this._array.splice(index, deleteCount);\n      this._zArray.splice(zIdx, deleteCount);\n      particle.destroy(override);\n      this._engine.dispatchEvent(EventType.particleRemoved, {\n        container: this._container,\n        data: {\n          particle\n        }\n      });\n      this._addToPool(particle);\n      return true;\n    };\n    this._engine = engine;\n    this._container = container;\n    this._nextId = 0;\n    this._array = [];\n    this._zArray = [];\n    this._pool = [];\n    this._limit = 0;\n    this._groupLimits = new Map();\n    this._needsSort = false;\n    this._lastZIndex = 0;\n    this._interactionManager = new InteractionManager(engine, container);\n    this._pluginsInitialized = false;\n    const canvasSize = container.canvas.size;\n    this.quadTree = new QuadTree(qTreeRectangle(canvasSize), qTreeCapacity);\n    this.movers = [];\n    this.updaters = [];\n  }\n  get count() {\n    return this._array.length;\n  }\n  addManualParticles() {\n    const container = this._container,\n      options = container.actualOptions;\n    options.manualParticles.forEach(p => this.addParticle(p.position ? getPosition(p.position, container.canvas.size) : undefined, p.options));\n  }\n  addParticle(position, overrideOptions, group, initializer) {\n    const limitMode = this._container.actualOptions.particles.number.limit.mode,\n      limit = group === undefined ? this._limit : this._groupLimits.get(group) ?? this._limit,\n      currentCount = this.count,\n      minLimit = 0;\n    if (limit > minLimit) {\n      switch (limitMode) {\n        case LimitMode.delete:\n          {\n            const countOffset = 1,\n              minCount = 0,\n              countToRemove = currentCount + countOffset - limit;\n            if (countToRemove > minCount) {\n              this.removeQuantity(countToRemove);\n            }\n            break;\n          }\n        case LimitMode.wait:\n          if (currentCount >= limit) {\n            return;\n          }\n          break;\n      }\n    }\n    return this._pushParticle(position, overrideOptions, group, initializer);\n  }\n  clear() {\n    this._array = [];\n    this._zArray = [];\n    this._pluginsInitialized = false;\n  }\n  destroy() {\n    this._array = [];\n    this._zArray = [];\n    this.movers = [];\n    this.updaters = [];\n  }\n  draw(delta) {\n    const container = this._container,\n      canvas = container.canvas;\n    canvas.clear();\n    this.update(delta);\n    for (const [, plugin] of container.plugins) {\n      canvas.drawPlugin(plugin, delta);\n    }\n    for (const p of this._zArray) {\n      p.draw(delta);\n    }\n  }\n  filter(condition) {\n    return this._array.filter(condition);\n  }\n  find(condition) {\n    return this._array.find(condition);\n  }\n  get(index) {\n    return this._array[index];\n  }\n  handleClickMode(mode) {\n    this._interactionManager.handleClickMode(mode);\n  }\n  async init() {\n    const container = this._container,\n      options = container.actualOptions;\n    this._lastZIndex = 0;\n    this._needsSort = false;\n    await this.initPlugins();\n    let handled = false;\n    for (const [, plugin] of container.plugins) {\n      handled = plugin.particlesInitialization?.() ?? handled;\n      if (handled) {\n        break;\n      }\n    }\n    this.addManualParticles();\n    if (!handled) {\n      const particlesOptions = options.particles,\n        groups = particlesOptions.groups;\n      for (const group in groups) {\n        const groupOptions = groups[group];\n        for (let i = this.count, j = 0; j < groupOptions.number?.value && i < particlesOptions.number.value; i++, j++) {\n          this.addParticle(undefined, groupOptions, group);\n        }\n      }\n      for (let i = this.count; i < particlesOptions.number.value; i++) {\n        this.addParticle();\n      }\n    }\n  }\n  async initPlugins() {\n    if (this._pluginsInitialized) {\n      return;\n    }\n    const container = this._container;\n    this.movers = await this._engine.getMovers(container, true);\n    this.updaters = await this._engine.getUpdaters(container, true);\n    await this._interactionManager.init();\n    for (const [, pathGenerator] of container.pathGenerators) {\n      pathGenerator.init(container);\n    }\n  }\n  push(nb, mouse, overrideOptions, group) {\n    for (let i = 0; i < nb; i++) {\n      this.addParticle(mouse?.position, overrideOptions, group);\n    }\n  }\n  async redraw() {\n    this.clear();\n    await this.init();\n    this.draw({\n      value: 0,\n      factor: 0\n    });\n  }\n  remove(particle, group, override) {\n    this.removeAt(this._array.indexOf(particle), undefined, group, override);\n  }\n  removeAt(index, quantity = defaultRemoveQuantity, group, override) {\n    const minIndex = 0;\n    if (index < minIndex || index > this.count) {\n      return;\n    }\n    let deleted = 0;\n    for (let i = index; deleted < quantity && i < this.count; i++) {\n      if (this._removeParticle(i--, group, override)) {\n        deleted++;\n      }\n    }\n  }\n  removeQuantity(quantity, group) {\n    const defaultIndex = 0;\n    this.removeAt(defaultIndex, quantity, group);\n  }\n  setDensity() {\n    const options = this._container.actualOptions,\n      groups = options.particles.groups,\n      manualCount = 0;\n    for (const group in groups) {\n      this._applyDensity(groups[group], manualCount, group);\n    }\n    this._applyDensity(options.particles, options.manualParticles.length);\n  }\n  setLastZIndex(zIndex) {\n    this._lastZIndex = zIndex;\n    this._needsSort = this._needsSort || this._lastZIndex < zIndex;\n  }\n  setResizeFactor(factor) {\n    this._resizeFactor = factor;\n  }\n  update(delta) {\n    const container = this._container,\n      particlesToDelete = new Set();\n    this.quadTree = new QuadTree(qTreeRectangle(container.canvas.size), qTreeCapacity);\n    for (const [, pathGenerator] of container.pathGenerators) {\n      pathGenerator.update();\n    }\n    for (const [, plugin] of container.plugins) {\n      plugin.update?.(delta);\n    }\n    const resizeFactor = this._resizeFactor;\n    for (const particle of this._array) {\n      if (resizeFactor && !particle.ignoresResizeRatio) {\n        particle.position.x *= resizeFactor.width;\n        particle.position.y *= resizeFactor.height;\n        particle.initialPosition.x *= resizeFactor.width;\n        particle.initialPosition.y *= resizeFactor.height;\n      }\n      particle.ignoresResizeRatio = false;\n      this._interactionManager.reset(particle);\n      for (const [, plugin] of this._container.plugins) {\n        if (particle.destroyed) {\n          break;\n        }\n        plugin.particleUpdate?.(particle, delta);\n      }\n      for (const mover of this.movers) {\n        if (mover.isEnabled(particle)) {\n          mover.move(particle, delta);\n        }\n      }\n      if (particle.destroyed) {\n        particlesToDelete.add(particle);\n        continue;\n      }\n      this.quadTree.insert(new Point(particle.getPosition(), particle));\n    }\n    if (particlesToDelete.size) {\n      const checkDelete = p => !particlesToDelete.has(p);\n      this._array = this.filter(checkDelete);\n      this._zArray = this._zArray.filter(checkDelete);\n      for (const particle of particlesToDelete) {\n        this._engine.dispatchEvent(EventType.particleRemoved, {\n          container: this._container,\n          data: {\n            particle\n          }\n        });\n      }\n      this._addToPool(...particlesToDelete);\n    }\n    this._interactionManager.externalInteract(delta);\n    for (const particle of this._array) {\n      for (const updater of this.updaters) {\n        updater.update(particle, delta);\n      }\n      if (!particle.destroyed && !particle.spawning) {\n        this._interactionManager.particlesInteract(particle, delta);\n      }\n    }\n    delete this._resizeFactor;\n    if (this._needsSort) {\n      const zArray = this._zArray;\n      zArray.sort((a, b) => b.position.z - a.position.z || a.id - b.id);\n      const lengthOffset = 1;\n      this._lastZIndex = zArray[zArray.length - lengthOffset].position.z;\n      this._needsSort = false;\n    }\n  }\n}", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "getPosition", "EventType", "InteractionManager", "LimitMode", "Particle", "Point", "QuadTree", "Rectangle", "errorPrefix", "qTreeCapacity", "squareExp", "defaultRemoveQuantity", "qTreeRectangle", "canvasSize", "height", "width", "posOffset", "sizeFactor", "Particles", "constructor", "engine", "container", "_addToPool", "particles", "_pool", "push", "_applyDensity", "options", "manualCount", "group", "numberOptions", "number", "density", "enable", "undefined", "_limit", "limit", "value", "_groupLimits", "set", "densityFactor", "_initDensityFactor", "optParticlesNumber", "minLimit", "optParticlesLimit", "particlesNumber", "Math", "min", "particlesCount", "count", "filter", "t", "length", "abs", "removeQuantity", "densityOptions", "_container", "defaultFactor", "canvas", "element", "pxRatio", "retina", "pixelRatio", "_pushParticle", "position", "overrideOptions", "initializer", "particle", "pop", "_engine", "init", "_nextId", "canAdd", "_array", "_zArray", "dispatchEvent", "particleAdded", "data", "e", "warning", "_removeParticle", "index", "override", "zIdx", "indexOf", "deleteCount", "splice", "destroy", "particleRemoved", "Map", "_needsSort", "_lastZIndex", "_interactionManager", "_pluginsInitialized", "size", "quadTree", "movers", "updaters", "addManualParticles", "actualOptions", "manualParticles", "for<PERSON>ach", "p", "addParticle", "limitMode", "mode", "get", "currentCount", "delete", "countOffset", "minCount", "count<PERSON><PERSON><PERSON><PERSON>ove", "wait", "clear", "draw", "delta", "update", "plugin", "plugins", "drawPlugin", "condition", "find", "handleClickMode", "initPlugins", "handled", "particlesInitialization", "particlesOptions", "groups", "groupOptions", "i", "j", "getMovers", "getUpdaters", "pathGenerator", "pathGenerators", "nb", "mouse", "redraw", "factor", "remove", "removeAt", "quantity", "minIndex", "deleted", "defaultIndex", "setDensity", "setLastZIndex", "zIndex", "setResizeFactor", "_resizeFactor", "particlesToDelete", "Set", "resizeFactor", "ignoresResizeRatio", "x", "y", "initialPosition", "reset", "destroyed", "particleUpdate", "mover", "isEnabled", "move", "add", "insert", "checkDelete", "has", "externalInteract", "updater", "spawning", "particlesInteract", "zArray", "sort", "a", "b", "z", "id", "lengthOffset"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/engine/browser/Core/Particles.js"], "sourcesContent": ["import { getLogger, getPosition } from \"../Utils/Utils.js\";\nimport { EventType } from \"../Enums/Types/EventType.js\";\nimport { InteractionManager } from \"./Utils/InteractionManager.js\";\nimport { LimitMode } from \"../Enums/Modes/LimitMode.js\";\nimport { Particle } from \"./Particle.js\";\nimport { Point } from \"./Utils/Point.js\";\nimport { QuadTree } from \"./Utils/QuadTree.js\";\nimport { Rectangle } from \"./Utils/Ranges.js\";\nimport { errorPrefix } from \"./Utils/Constants.js\";\nconst qTreeCapacity = 4, squareExp = 2, defaultRemoveQuantity = 1;\nconst qTreeRectangle = (canvasSize) => {\n    const { height, width } = canvasSize, posOffset = -0.25, sizeFactor = 1.5;\n    return new Rectangle(posOffset * width, posOffset * height, sizeFactor * width, sizeFactor * height);\n};\nexport class Particles {\n    constructor(engine, container) {\n        this._addToPool = (...particles) => {\n            this._pool.push(...particles);\n        };\n        this._applyDensity = (options, manualCount, group) => {\n            const numberOptions = options.number;\n            if (!options.number.density?.enable) {\n                if (group === undefined) {\n                    this._limit = numberOptions.limit.value;\n                }\n                else if (numberOptions.limit) {\n                    this._groupLimits.set(group, numberOptions.limit.value);\n                }\n                return;\n            }\n            const densityFactor = this._initDensityFactor(numberOptions.density), optParticlesNumber = numberOptions.value, minLimit = 0, optParticlesLimit = numberOptions.limit.value > minLimit ? numberOptions.limit.value : optParticlesNumber, particlesNumber = Math.min(optParticlesNumber, optParticlesLimit) * densityFactor + manualCount, particlesCount = Math.min(this.count, this.filter(t => t.group === group).length);\n            if (group === undefined) {\n                this._limit = numberOptions.limit.value * densityFactor;\n            }\n            else {\n                this._groupLimits.set(group, numberOptions.limit.value * densityFactor);\n            }\n            if (particlesCount < particlesNumber) {\n                this.push(Math.abs(particlesNumber - particlesCount), undefined, options, group);\n            }\n            else if (particlesCount > particlesNumber) {\n                this.removeQuantity(particlesCount - particlesNumber, group);\n            }\n        };\n        this._initDensityFactor = densityOptions => {\n            const container = this._container, defaultFactor = 1;\n            if (!container.canvas.element || !densityOptions.enable) {\n                return defaultFactor;\n            }\n            const canvas = container.canvas.element, pxRatio = container.retina.pixelRatio;\n            return (canvas.width * canvas.height) / (densityOptions.height * densityOptions.width * pxRatio ** squareExp);\n        };\n        this._pushParticle = (position, overrideOptions, group, initializer) => {\n            try {\n                let particle = this._pool.pop();\n                if (!particle) {\n                    particle = new Particle(this._engine, this._container);\n                }\n                particle.init(this._nextId, position, overrideOptions, group);\n                let canAdd = true;\n                if (initializer) {\n                    canAdd = initializer(particle);\n                }\n                if (!canAdd) {\n                    return;\n                }\n                this._array.push(particle);\n                this._zArray.push(particle);\n                this._nextId++;\n                this._engine.dispatchEvent(EventType.particleAdded, {\n                    container: this._container,\n                    data: {\n                        particle,\n                    },\n                });\n                return particle;\n            }\n            catch (e) {\n                getLogger().warning(`${errorPrefix} adding particle: ${e}`);\n            }\n        };\n        this._removeParticle = (index, group, override) => {\n            const particle = this._array[index];\n            if (!particle || particle.group !== group) {\n                return false;\n            }\n            const zIdx = this._zArray.indexOf(particle), deleteCount = 1;\n            this._array.splice(index, deleteCount);\n            this._zArray.splice(zIdx, deleteCount);\n            particle.destroy(override);\n            this._engine.dispatchEvent(EventType.particleRemoved, {\n                container: this._container,\n                data: {\n                    particle,\n                },\n            });\n            this._addToPool(particle);\n            return true;\n        };\n        this._engine = engine;\n        this._container = container;\n        this._nextId = 0;\n        this._array = [];\n        this._zArray = [];\n        this._pool = [];\n        this._limit = 0;\n        this._groupLimits = new Map();\n        this._needsSort = false;\n        this._lastZIndex = 0;\n        this._interactionManager = new InteractionManager(engine, container);\n        this._pluginsInitialized = false;\n        const canvasSize = container.canvas.size;\n        this.quadTree = new QuadTree(qTreeRectangle(canvasSize), qTreeCapacity);\n        this.movers = [];\n        this.updaters = [];\n    }\n    get count() {\n        return this._array.length;\n    }\n    addManualParticles() {\n        const container = this._container, options = container.actualOptions;\n        options.manualParticles.forEach(p => this.addParticle(p.position ? getPosition(p.position, container.canvas.size) : undefined, p.options));\n    }\n    addParticle(position, overrideOptions, group, initializer) {\n        const limitMode = this._container.actualOptions.particles.number.limit.mode, limit = group === undefined ? this._limit : this._groupLimits.get(group) ?? this._limit, currentCount = this.count, minLimit = 0;\n        if (limit > minLimit) {\n            switch (limitMode) {\n                case LimitMode.delete: {\n                    const countOffset = 1, minCount = 0, countToRemove = currentCount + countOffset - limit;\n                    if (countToRemove > minCount) {\n                        this.removeQuantity(countToRemove);\n                    }\n                    break;\n                }\n                case LimitMode.wait:\n                    if (currentCount >= limit) {\n                        return;\n                    }\n                    break;\n            }\n        }\n        return this._pushParticle(position, overrideOptions, group, initializer);\n    }\n    clear() {\n        this._array = [];\n        this._zArray = [];\n        this._pluginsInitialized = false;\n    }\n    destroy() {\n        this._array = [];\n        this._zArray = [];\n        this.movers = [];\n        this.updaters = [];\n    }\n    draw(delta) {\n        const container = this._container, canvas = container.canvas;\n        canvas.clear();\n        this.update(delta);\n        for (const [, plugin] of container.plugins) {\n            canvas.drawPlugin(plugin, delta);\n        }\n        for (const p of this._zArray) {\n            p.draw(delta);\n        }\n    }\n    filter(condition) {\n        return this._array.filter(condition);\n    }\n    find(condition) {\n        return this._array.find(condition);\n    }\n    get(index) {\n        return this._array[index];\n    }\n    handleClickMode(mode) {\n        this._interactionManager.handleClickMode(mode);\n    }\n    async init() {\n        const container = this._container, options = container.actualOptions;\n        this._lastZIndex = 0;\n        this._needsSort = false;\n        await this.initPlugins();\n        let handled = false;\n        for (const [, plugin] of container.plugins) {\n            handled = plugin.particlesInitialization?.() ?? handled;\n            if (handled) {\n                break;\n            }\n        }\n        this.addManualParticles();\n        if (!handled) {\n            const particlesOptions = options.particles, groups = particlesOptions.groups;\n            for (const group in groups) {\n                const groupOptions = groups[group];\n                for (let i = this.count, j = 0; j < groupOptions.number?.value && i < particlesOptions.number.value; i++, j++) {\n                    this.addParticle(undefined, groupOptions, group);\n                }\n            }\n            for (let i = this.count; i < particlesOptions.number.value; i++) {\n                this.addParticle();\n            }\n        }\n    }\n    async initPlugins() {\n        if (this._pluginsInitialized) {\n            return;\n        }\n        const container = this._container;\n        this.movers = await this._engine.getMovers(container, true);\n        this.updaters = await this._engine.getUpdaters(container, true);\n        await this._interactionManager.init();\n        for (const [, pathGenerator] of container.pathGenerators) {\n            pathGenerator.init(container);\n        }\n    }\n    push(nb, mouse, overrideOptions, group) {\n        for (let i = 0; i < nb; i++) {\n            this.addParticle(mouse?.position, overrideOptions, group);\n        }\n    }\n    async redraw() {\n        this.clear();\n        await this.init();\n        this.draw({ value: 0, factor: 0 });\n    }\n    remove(particle, group, override) {\n        this.removeAt(this._array.indexOf(particle), undefined, group, override);\n    }\n    removeAt(index, quantity = defaultRemoveQuantity, group, override) {\n        const minIndex = 0;\n        if (index < minIndex || index > this.count) {\n            return;\n        }\n        let deleted = 0;\n        for (let i = index; deleted < quantity && i < this.count; i++) {\n            if (this._removeParticle(i--, group, override)) {\n                deleted++;\n            }\n        }\n    }\n    removeQuantity(quantity, group) {\n        const defaultIndex = 0;\n        this.removeAt(defaultIndex, quantity, group);\n    }\n    setDensity() {\n        const options = this._container.actualOptions, groups = options.particles.groups, manualCount = 0;\n        for (const group in groups) {\n            this._applyDensity(groups[group], manualCount, group);\n        }\n        this._applyDensity(options.particles, options.manualParticles.length);\n    }\n    setLastZIndex(zIndex) {\n        this._lastZIndex = zIndex;\n        this._needsSort = this._needsSort || this._lastZIndex < zIndex;\n    }\n    setResizeFactor(factor) {\n        this._resizeFactor = factor;\n    }\n    update(delta) {\n        const container = this._container, particlesToDelete = new Set();\n        this.quadTree = new QuadTree(qTreeRectangle(container.canvas.size), qTreeCapacity);\n        for (const [, pathGenerator] of container.pathGenerators) {\n            pathGenerator.update();\n        }\n        for (const [, plugin] of container.plugins) {\n            plugin.update?.(delta);\n        }\n        const resizeFactor = this._resizeFactor;\n        for (const particle of this._array) {\n            if (resizeFactor && !particle.ignoresResizeRatio) {\n                particle.position.x *= resizeFactor.width;\n                particle.position.y *= resizeFactor.height;\n                particle.initialPosition.x *= resizeFactor.width;\n                particle.initialPosition.y *= resizeFactor.height;\n            }\n            particle.ignoresResizeRatio = false;\n            this._interactionManager.reset(particle);\n            for (const [, plugin] of this._container.plugins) {\n                if (particle.destroyed) {\n                    break;\n                }\n                plugin.particleUpdate?.(particle, delta);\n            }\n            for (const mover of this.movers) {\n                if (mover.isEnabled(particle)) {\n                    mover.move(particle, delta);\n                }\n            }\n            if (particle.destroyed) {\n                particlesToDelete.add(particle);\n                continue;\n            }\n            this.quadTree.insert(new Point(particle.getPosition(), particle));\n        }\n        if (particlesToDelete.size) {\n            const checkDelete = (p) => !particlesToDelete.has(p);\n            this._array = this.filter(checkDelete);\n            this._zArray = this._zArray.filter(checkDelete);\n            for (const particle of particlesToDelete) {\n                this._engine.dispatchEvent(EventType.particleRemoved, {\n                    container: this._container,\n                    data: {\n                        particle,\n                    },\n                });\n            }\n            this._addToPool(...particlesToDelete);\n        }\n        this._interactionManager.externalInteract(delta);\n        for (const particle of this._array) {\n            for (const updater of this.updaters) {\n                updater.update(particle, delta);\n            }\n            if (!particle.destroyed && !particle.spawning) {\n                this._interactionManager.particlesInteract(particle, delta);\n            }\n        }\n        delete this._resizeFactor;\n        if (this._needsSort) {\n            const zArray = this._zArray;\n            zArray.sort((a, b) => b.position.z - a.position.z || a.id - b.id);\n            const lengthOffset = 1;\n            this._lastZIndex = zArray[zArray.length - lengthOffset].position.z;\n            this._needsSort = false;\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,SAAS,EAAEC,WAAW,QAAQ,mBAAmB;AAC1D,SAASC,SAAS,QAAQ,6BAA6B;AACvD,SAASC,kBAAkB,QAAQ,+BAA+B;AAClE,SAASC,SAAS,QAAQ,6BAA6B;AACvD,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,KAAK,QAAQ,kBAAkB;AACxC,SAASC,QAAQ,QAAQ,qBAAqB;AAC9C,SAASC,SAAS,QAAQ,mBAAmB;AAC7C,SAASC,WAAW,QAAQ,sBAAsB;AAClD,MAAMC,aAAa,GAAG,CAAC;EAAEC,SAAS,GAAG,CAAC;EAAEC,qBAAqB,GAAG,CAAC;AACjE,MAAMC,cAAc,GAAIC,UAAU,IAAK;EACnC,MAAM;MAAEC,MAAM;MAAEC;IAAM,CAAC,GAAGF,UAAU;IAAEG,SAAS,GAAG,CAAC,IAAI;IAAEC,UAAU,GAAG,GAAG;EACzE,OAAO,IAAIV,SAAS,CAACS,SAAS,GAAGD,KAAK,EAAEC,SAAS,GAAGF,MAAM,EAAEG,UAAU,GAAGF,KAAK,EAAEE,UAAU,GAAGH,MAAM,CAAC;AACxG,CAAC;AACD,OAAO,MAAMI,SAAS,CAAC;EACnBC,WAAWA,CAACC,MAAM,EAAEC,SAAS,EAAE;IAC3B,IAAI,CAACC,UAAU,GAAG,CAAC,GAAGC,SAAS,KAAK;MAChC,IAAI,CAACC,KAAK,CAACC,IAAI,CAAC,GAAGF,SAAS,CAAC;IACjC,CAAC;IACD,IAAI,CAACG,aAAa,GAAG,CAACC,OAAO,EAAEC,WAAW,EAAEC,KAAK,KAAK;MAClD,MAAMC,aAAa,GAAGH,OAAO,CAACI,MAAM;MACpC,IAAI,CAACJ,OAAO,CAACI,MAAM,CAACC,OAAO,EAAEC,MAAM,EAAE;QACjC,IAAIJ,KAAK,KAAKK,SAAS,EAAE;UACrB,IAAI,CAACC,MAAM,GAAGL,aAAa,CAACM,KAAK,CAACC,KAAK;QAC3C,CAAC,MACI,IAAIP,aAAa,CAACM,KAAK,EAAE;UAC1B,IAAI,CAACE,YAAY,CAACC,GAAG,CAACV,KAAK,EAAEC,aAAa,CAACM,KAAK,CAACC,KAAK,CAAC;QAC3D;QACA;MACJ;MACA,MAAMG,aAAa,GAAG,IAAI,CAACC,kBAAkB,CAACX,aAAa,CAACE,OAAO,CAAC;QAAEU,kBAAkB,GAAGZ,aAAa,CAACO,KAAK;QAAEM,QAAQ,GAAG,CAAC;QAAEC,iBAAiB,GAAGd,aAAa,CAACM,KAAK,CAACC,KAAK,GAAGM,QAAQ,GAAGb,aAAa,CAACM,KAAK,CAACC,KAAK,GAAGK,kBAAkB;QAAEG,eAAe,GAAGC,IAAI,CAACC,GAAG,CAACL,kBAAkB,EAAEE,iBAAiB,CAAC,GAAGJ,aAAa,GAAGZ,WAAW;QAAEoB,cAAc,GAAGF,IAAI,CAACC,GAAG,CAAC,IAAI,CAACE,KAAK,EAAE,IAAI,CAACC,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACtB,KAAK,KAAKA,KAAK,CAAC,CAACuB,MAAM,CAAC;MAC3Z,IAAIvB,KAAK,KAAKK,SAAS,EAAE;QACrB,IAAI,CAACC,MAAM,GAAGL,aAAa,CAACM,KAAK,CAACC,KAAK,GAAGG,aAAa;MAC3D,CAAC,MACI;QACD,IAAI,CAACF,YAAY,CAACC,GAAG,CAACV,KAAK,EAAEC,aAAa,CAACM,KAAK,CAACC,KAAK,GAAGG,aAAa,CAAC;MAC3E;MACA,IAAIQ,cAAc,GAAGH,eAAe,EAAE;QAClC,IAAI,CAACpB,IAAI,CAACqB,IAAI,CAACO,GAAG,CAACR,eAAe,GAAGG,cAAc,CAAC,EAAEd,SAAS,EAAEP,OAAO,EAAEE,KAAK,CAAC;MACpF,CAAC,MACI,IAAImB,cAAc,GAAGH,eAAe,EAAE;QACvC,IAAI,CAACS,cAAc,CAACN,cAAc,GAAGH,eAAe,EAAEhB,KAAK,CAAC;MAChE;IACJ,CAAC;IACD,IAAI,CAACY,kBAAkB,GAAGc,cAAc,IAAI;MACxC,MAAMlC,SAAS,GAAG,IAAI,CAACmC,UAAU;QAAEC,aAAa,GAAG,CAAC;MACpD,IAAI,CAACpC,SAAS,CAACqC,MAAM,CAACC,OAAO,IAAI,CAACJ,cAAc,CAACtB,MAAM,EAAE;QACrD,OAAOwB,aAAa;MACxB;MACA,MAAMC,MAAM,GAAGrC,SAAS,CAACqC,MAAM,CAACC,OAAO;QAAEC,OAAO,GAAGvC,SAAS,CAACwC,MAAM,CAACC,UAAU;MAC9E,OAAQJ,MAAM,CAAC3C,KAAK,GAAG2C,MAAM,CAAC5C,MAAM,IAAKyC,cAAc,CAACzC,MAAM,GAAGyC,cAAc,CAACxC,KAAK,GAAG6C,OAAO,IAAIlD,SAAS,CAAC;IACjH,CAAC;IACD,IAAI,CAACqD,aAAa,GAAG,CAACC,QAAQ,EAAEC,eAAe,EAAEpC,KAAK,EAAEqC,WAAW,KAAK;MACpE,IAAI;QACA,IAAIC,QAAQ,GAAG,IAAI,CAAC3C,KAAK,CAAC4C,GAAG,CAAC,CAAC;QAC/B,IAAI,CAACD,QAAQ,EAAE;UACXA,QAAQ,GAAG,IAAI/D,QAAQ,CAAC,IAAI,CAACiE,OAAO,EAAE,IAAI,CAACb,UAAU,CAAC;QAC1D;QACAW,QAAQ,CAACG,IAAI,CAAC,IAAI,CAACC,OAAO,EAAEP,QAAQ,EAAEC,eAAe,EAAEpC,KAAK,CAAC;QAC7D,IAAI2C,MAAM,GAAG,IAAI;QACjB,IAAIN,WAAW,EAAE;UACbM,MAAM,GAAGN,WAAW,CAACC,QAAQ,CAAC;QAClC;QACA,IAAI,CAACK,MAAM,EAAE;UACT;QACJ;QACA,IAAI,CAACC,MAAM,CAAChD,IAAI,CAAC0C,QAAQ,CAAC;QAC1B,IAAI,CAACO,OAAO,CAACjD,IAAI,CAAC0C,QAAQ,CAAC;QAC3B,IAAI,CAACI,OAAO,EAAE;QACd,IAAI,CAACF,OAAO,CAACM,aAAa,CAAC1E,SAAS,CAAC2E,aAAa,EAAE;UAChDvD,SAAS,EAAE,IAAI,CAACmC,UAAU;UAC1BqB,IAAI,EAAE;YACFV;UACJ;QACJ,CAAC,CAAC;QACF,OAAOA,QAAQ;MACnB,CAAC,CACD,OAAOW,CAAC,EAAE;QACN/E,SAAS,CAAC,CAAC,CAACgF,OAAO,CAAC,GAAGvE,WAAW,qBAAqBsE,CAAC,EAAE,CAAC;MAC/D;IACJ,CAAC;IACD,IAAI,CAACE,eAAe,GAAG,CAACC,KAAK,EAAEpD,KAAK,EAAEqD,QAAQ,KAAK;MAC/C,MAAMf,QAAQ,GAAG,IAAI,CAACM,MAAM,CAACQ,KAAK,CAAC;MACnC,IAAI,CAACd,QAAQ,IAAIA,QAAQ,CAACtC,KAAK,KAAKA,KAAK,EAAE;QACvC,OAAO,KAAK;MAChB;MACA,MAAMsD,IAAI,GAAG,IAAI,CAACT,OAAO,CAACU,OAAO,CAACjB,QAAQ,CAAC;QAAEkB,WAAW,GAAG,CAAC;MAC5D,IAAI,CAACZ,MAAM,CAACa,MAAM,CAACL,KAAK,EAAEI,WAAW,CAAC;MACtC,IAAI,CAACX,OAAO,CAACY,MAAM,CAACH,IAAI,EAAEE,WAAW,CAAC;MACtClB,QAAQ,CAACoB,OAAO,CAACL,QAAQ,CAAC;MAC1B,IAAI,CAACb,OAAO,CAACM,aAAa,CAAC1E,SAAS,CAACuF,eAAe,EAAE;QAClDnE,SAAS,EAAE,IAAI,CAACmC,UAAU;QAC1BqB,IAAI,EAAE;UACFV;QACJ;MACJ,CAAC,CAAC;MACF,IAAI,CAAC7C,UAAU,CAAC6C,QAAQ,CAAC;MACzB,OAAO,IAAI;IACf,CAAC;IACD,IAAI,CAACE,OAAO,GAAGjD,MAAM;IACrB,IAAI,CAACoC,UAAU,GAAGnC,SAAS;IAC3B,IAAI,CAACkD,OAAO,GAAG,CAAC;IAChB,IAAI,CAACE,MAAM,GAAG,EAAE;IAChB,IAAI,CAACC,OAAO,GAAG,EAAE;IACjB,IAAI,CAAClD,KAAK,GAAG,EAAE;IACf,IAAI,CAACW,MAAM,GAAG,CAAC;IACf,IAAI,CAACG,YAAY,GAAG,IAAImD,GAAG,CAAC,CAAC;IAC7B,IAAI,CAACC,UAAU,GAAG,KAAK;IACvB,IAAI,CAACC,WAAW,GAAG,CAAC;IACpB,IAAI,CAACC,mBAAmB,GAAG,IAAI1F,kBAAkB,CAACkB,MAAM,EAAEC,SAAS,CAAC;IACpE,IAAI,CAACwE,mBAAmB,GAAG,KAAK;IAChC,MAAMhF,UAAU,GAAGQ,SAAS,CAACqC,MAAM,CAACoC,IAAI;IACxC,IAAI,CAACC,QAAQ,GAAG,IAAIzF,QAAQ,CAACM,cAAc,CAACC,UAAU,CAAC,EAAEJ,aAAa,CAAC;IACvE,IAAI,CAACuF,MAAM,GAAG,EAAE;IAChB,IAAI,CAACC,QAAQ,GAAG,EAAE;EACtB;EACA,IAAIhD,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACwB,MAAM,CAACrB,MAAM;EAC7B;EACA8C,kBAAkBA,CAAA,EAAG;IACjB,MAAM7E,SAAS,GAAG,IAAI,CAACmC,UAAU;MAAE7B,OAAO,GAAGN,SAAS,CAAC8E,aAAa;IACpExE,OAAO,CAACyE,eAAe,CAACC,OAAO,CAACC,CAAC,IAAI,IAAI,CAACC,WAAW,CAACD,CAAC,CAACtC,QAAQ,GAAGhE,WAAW,CAACsG,CAAC,CAACtC,QAAQ,EAAE3C,SAAS,CAACqC,MAAM,CAACoC,IAAI,CAAC,GAAG5D,SAAS,EAAEoE,CAAC,CAAC3E,OAAO,CAAC,CAAC;EAC9I;EACA4E,WAAWA,CAACvC,QAAQ,EAAEC,eAAe,EAAEpC,KAAK,EAAEqC,WAAW,EAAE;IACvD,MAAMsC,SAAS,GAAG,IAAI,CAAChD,UAAU,CAAC2C,aAAa,CAAC5E,SAAS,CAACQ,MAAM,CAACK,KAAK,CAACqE,IAAI;MAAErE,KAAK,GAAGP,KAAK,KAAKK,SAAS,GAAG,IAAI,CAACC,MAAM,GAAG,IAAI,CAACG,YAAY,CAACoE,GAAG,CAAC7E,KAAK,CAAC,IAAI,IAAI,CAACM,MAAM;MAAEwE,YAAY,GAAG,IAAI,CAAC1D,KAAK;MAAEN,QAAQ,GAAG,CAAC;IAC7M,IAAIP,KAAK,GAAGO,QAAQ,EAAE;MAClB,QAAQ6D,SAAS;QACb,KAAKrG,SAAS,CAACyG,MAAM;UAAE;YACnB,MAAMC,WAAW,GAAG,CAAC;cAAEC,QAAQ,GAAG,CAAC;cAAEC,aAAa,GAAGJ,YAAY,GAAGE,WAAW,GAAGzE,KAAK;YACvF,IAAI2E,aAAa,GAAGD,QAAQ,EAAE;cAC1B,IAAI,CAACxD,cAAc,CAACyD,aAAa,CAAC;YACtC;YACA;UACJ;QACA,KAAK5G,SAAS,CAAC6G,IAAI;UACf,IAAIL,YAAY,IAAIvE,KAAK,EAAE;YACvB;UACJ;UACA;MACR;IACJ;IACA,OAAO,IAAI,CAAC2B,aAAa,CAACC,QAAQ,EAAEC,eAAe,EAAEpC,KAAK,EAAEqC,WAAW,CAAC;EAC5E;EACA+C,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACxC,MAAM,GAAG,EAAE;IAChB,IAAI,CAACC,OAAO,GAAG,EAAE;IACjB,IAAI,CAACmB,mBAAmB,GAAG,KAAK;EACpC;EACAN,OAAOA,CAAA,EAAG;IACN,IAAI,CAACd,MAAM,GAAG,EAAE;IAChB,IAAI,CAACC,OAAO,GAAG,EAAE;IACjB,IAAI,CAACsB,MAAM,GAAG,EAAE;IAChB,IAAI,CAACC,QAAQ,GAAG,EAAE;EACtB;EACAiB,IAAIA,CAACC,KAAK,EAAE;IACR,MAAM9F,SAAS,GAAG,IAAI,CAACmC,UAAU;MAAEE,MAAM,GAAGrC,SAAS,CAACqC,MAAM;IAC5DA,MAAM,CAACuD,KAAK,CAAC,CAAC;IACd,IAAI,CAACG,MAAM,CAACD,KAAK,CAAC;IAClB,KAAK,MAAM,GAAGE,MAAM,CAAC,IAAIhG,SAAS,CAACiG,OAAO,EAAE;MACxC5D,MAAM,CAAC6D,UAAU,CAACF,MAAM,EAAEF,KAAK,CAAC;IACpC;IACA,KAAK,MAAMb,CAAC,IAAI,IAAI,CAAC5B,OAAO,EAAE;MAC1B4B,CAAC,CAACY,IAAI,CAACC,KAAK,CAAC;IACjB;EACJ;EACAjE,MAAMA,CAACsE,SAAS,EAAE;IACd,OAAO,IAAI,CAAC/C,MAAM,CAACvB,MAAM,CAACsE,SAAS,CAAC;EACxC;EACAC,IAAIA,CAACD,SAAS,EAAE;IACZ,OAAO,IAAI,CAAC/C,MAAM,CAACgD,IAAI,CAACD,SAAS,CAAC;EACtC;EACAd,GAAGA,CAACzB,KAAK,EAAE;IACP,OAAO,IAAI,CAACR,MAAM,CAACQ,KAAK,CAAC;EAC7B;EACAyC,eAAeA,CAACjB,IAAI,EAAE;IAClB,IAAI,CAACb,mBAAmB,CAAC8B,eAAe,CAACjB,IAAI,CAAC;EAClD;EACA,MAAMnC,IAAIA,CAAA,EAAG;IACT,MAAMjD,SAAS,GAAG,IAAI,CAACmC,UAAU;MAAE7B,OAAO,GAAGN,SAAS,CAAC8E,aAAa;IACpE,IAAI,CAACR,WAAW,GAAG,CAAC;IACpB,IAAI,CAACD,UAAU,GAAG,KAAK;IACvB,MAAM,IAAI,CAACiC,WAAW,CAAC,CAAC;IACxB,IAAIC,OAAO,GAAG,KAAK;IACnB,KAAK,MAAM,GAAGP,MAAM,CAAC,IAAIhG,SAAS,CAACiG,OAAO,EAAE;MACxCM,OAAO,GAAGP,MAAM,CAACQ,uBAAuB,GAAG,CAAC,IAAID,OAAO;MACvD,IAAIA,OAAO,EAAE;QACT;MACJ;IACJ;IACA,IAAI,CAAC1B,kBAAkB,CAAC,CAAC;IACzB,IAAI,CAAC0B,OAAO,EAAE;MACV,MAAME,gBAAgB,GAAGnG,OAAO,CAACJ,SAAS;QAAEwG,MAAM,GAAGD,gBAAgB,CAACC,MAAM;MAC5E,KAAK,MAAMlG,KAAK,IAAIkG,MAAM,EAAE;QACxB,MAAMC,YAAY,GAAGD,MAAM,CAAClG,KAAK,CAAC;QAClC,KAAK,IAAIoG,CAAC,GAAG,IAAI,CAAChF,KAAK,EAAEiF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,YAAY,CAACjG,MAAM,EAAEM,KAAK,IAAI4F,CAAC,GAAGH,gBAAgB,CAAC/F,MAAM,CAACM,KAAK,EAAE4F,CAAC,EAAE,EAAEC,CAAC,EAAE,EAAE;UAC3G,IAAI,CAAC3B,WAAW,CAACrE,SAAS,EAAE8F,YAAY,EAAEnG,KAAK,CAAC;QACpD;MACJ;MACA,KAAK,IAAIoG,CAAC,GAAG,IAAI,CAAChF,KAAK,EAAEgF,CAAC,GAAGH,gBAAgB,CAAC/F,MAAM,CAACM,KAAK,EAAE4F,CAAC,EAAE,EAAE;QAC7D,IAAI,CAAC1B,WAAW,CAAC,CAAC;MACtB;IACJ;EACJ;EACA,MAAMoB,WAAWA,CAAA,EAAG;IAChB,IAAI,IAAI,CAAC9B,mBAAmB,EAAE;MAC1B;IACJ;IACA,MAAMxE,SAAS,GAAG,IAAI,CAACmC,UAAU;IACjC,IAAI,CAACwC,MAAM,GAAG,MAAM,IAAI,CAAC3B,OAAO,CAAC8D,SAAS,CAAC9G,SAAS,EAAE,IAAI,CAAC;IAC3D,IAAI,CAAC4E,QAAQ,GAAG,MAAM,IAAI,CAAC5B,OAAO,CAAC+D,WAAW,CAAC/G,SAAS,EAAE,IAAI,CAAC;IAC/D,MAAM,IAAI,CAACuE,mBAAmB,CAACtB,IAAI,CAAC,CAAC;IACrC,KAAK,MAAM,GAAG+D,aAAa,CAAC,IAAIhH,SAAS,CAACiH,cAAc,EAAE;MACtDD,aAAa,CAAC/D,IAAI,CAACjD,SAAS,CAAC;IACjC;EACJ;EACAI,IAAIA,CAAC8G,EAAE,EAAEC,KAAK,EAAEvE,eAAe,EAAEpC,KAAK,EAAE;IACpC,KAAK,IAAIoG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGM,EAAE,EAAEN,CAAC,EAAE,EAAE;MACzB,IAAI,CAAC1B,WAAW,CAACiC,KAAK,EAAExE,QAAQ,EAAEC,eAAe,EAAEpC,KAAK,CAAC;IAC7D;EACJ;EACA,MAAM4G,MAAMA,CAAA,EAAG;IACX,IAAI,CAACxB,KAAK,CAAC,CAAC;IACZ,MAAM,IAAI,CAAC3C,IAAI,CAAC,CAAC;IACjB,IAAI,CAAC4C,IAAI,CAAC;MAAE7E,KAAK,EAAE,CAAC;MAAEqG,MAAM,EAAE;IAAE,CAAC,CAAC;EACtC;EACAC,MAAMA,CAACxE,QAAQ,EAAEtC,KAAK,EAAEqD,QAAQ,EAAE;IAC9B,IAAI,CAAC0D,QAAQ,CAAC,IAAI,CAACnE,MAAM,CAACW,OAAO,CAACjB,QAAQ,CAAC,EAAEjC,SAAS,EAAEL,KAAK,EAAEqD,QAAQ,CAAC;EAC5E;EACA0D,QAAQA,CAAC3D,KAAK,EAAE4D,QAAQ,GAAGlI,qBAAqB,EAAEkB,KAAK,EAAEqD,QAAQ,EAAE;IAC/D,MAAM4D,QAAQ,GAAG,CAAC;IAClB,IAAI7D,KAAK,GAAG6D,QAAQ,IAAI7D,KAAK,GAAG,IAAI,CAAChC,KAAK,EAAE;MACxC;IACJ;IACA,IAAI8F,OAAO,GAAG,CAAC;IACf,KAAK,IAAId,CAAC,GAAGhD,KAAK,EAAE8D,OAAO,GAAGF,QAAQ,IAAIZ,CAAC,GAAG,IAAI,CAAChF,KAAK,EAAEgF,CAAC,EAAE,EAAE;MAC3D,IAAI,IAAI,CAACjD,eAAe,CAACiD,CAAC,EAAE,EAAEpG,KAAK,EAAEqD,QAAQ,CAAC,EAAE;QAC5C6D,OAAO,EAAE;MACb;IACJ;EACJ;EACAzF,cAAcA,CAACuF,QAAQ,EAAEhH,KAAK,EAAE;IAC5B,MAAMmH,YAAY,GAAG,CAAC;IACtB,IAAI,CAACJ,QAAQ,CAACI,YAAY,EAAEH,QAAQ,EAAEhH,KAAK,CAAC;EAChD;EACAoH,UAAUA,CAAA,EAAG;IACT,MAAMtH,OAAO,GAAG,IAAI,CAAC6B,UAAU,CAAC2C,aAAa;MAAE4B,MAAM,GAAGpG,OAAO,CAACJ,SAAS,CAACwG,MAAM;MAAEnG,WAAW,GAAG,CAAC;IACjG,KAAK,MAAMC,KAAK,IAAIkG,MAAM,EAAE;MACxB,IAAI,CAACrG,aAAa,CAACqG,MAAM,CAAClG,KAAK,CAAC,EAAED,WAAW,EAAEC,KAAK,CAAC;IACzD;IACA,IAAI,CAACH,aAAa,CAACC,OAAO,CAACJ,SAAS,EAAEI,OAAO,CAACyE,eAAe,CAAChD,MAAM,CAAC;EACzE;EACA8F,aAAaA,CAACC,MAAM,EAAE;IAClB,IAAI,CAACxD,WAAW,GAAGwD,MAAM;IACzB,IAAI,CAACzD,UAAU,GAAG,IAAI,CAACA,UAAU,IAAI,IAAI,CAACC,WAAW,GAAGwD,MAAM;EAClE;EACAC,eAAeA,CAACV,MAAM,EAAE;IACpB,IAAI,CAACW,aAAa,GAAGX,MAAM;EAC/B;EACAtB,MAAMA,CAACD,KAAK,EAAE;IACV,MAAM9F,SAAS,GAAG,IAAI,CAACmC,UAAU;MAAE8F,iBAAiB,GAAG,IAAIC,GAAG,CAAC,CAAC;IAChE,IAAI,CAACxD,QAAQ,GAAG,IAAIzF,QAAQ,CAACM,cAAc,CAACS,SAAS,CAACqC,MAAM,CAACoC,IAAI,CAAC,EAAErF,aAAa,CAAC;IAClF,KAAK,MAAM,GAAG4H,aAAa,CAAC,IAAIhH,SAAS,CAACiH,cAAc,EAAE;MACtDD,aAAa,CAACjB,MAAM,CAAC,CAAC;IAC1B;IACA,KAAK,MAAM,GAAGC,MAAM,CAAC,IAAIhG,SAAS,CAACiG,OAAO,EAAE;MACxCD,MAAM,CAACD,MAAM,GAAGD,KAAK,CAAC;IAC1B;IACA,MAAMqC,YAAY,GAAG,IAAI,CAACH,aAAa;IACvC,KAAK,MAAMlF,QAAQ,IAAI,IAAI,CAACM,MAAM,EAAE;MAChC,IAAI+E,YAAY,IAAI,CAACrF,QAAQ,CAACsF,kBAAkB,EAAE;QAC9CtF,QAAQ,CAACH,QAAQ,CAAC0F,CAAC,IAAIF,YAAY,CAACzI,KAAK;QACzCoD,QAAQ,CAACH,QAAQ,CAAC2F,CAAC,IAAIH,YAAY,CAAC1I,MAAM;QAC1CqD,QAAQ,CAACyF,eAAe,CAACF,CAAC,IAAIF,YAAY,CAACzI,KAAK;QAChDoD,QAAQ,CAACyF,eAAe,CAACD,CAAC,IAAIH,YAAY,CAAC1I,MAAM;MACrD;MACAqD,QAAQ,CAACsF,kBAAkB,GAAG,KAAK;MACnC,IAAI,CAAC7D,mBAAmB,CAACiE,KAAK,CAAC1F,QAAQ,CAAC;MACxC,KAAK,MAAM,GAAGkD,MAAM,CAAC,IAAI,IAAI,CAAC7D,UAAU,CAAC8D,OAAO,EAAE;QAC9C,IAAInD,QAAQ,CAAC2F,SAAS,EAAE;UACpB;QACJ;QACAzC,MAAM,CAAC0C,cAAc,GAAG5F,QAAQ,EAAEgD,KAAK,CAAC;MAC5C;MACA,KAAK,MAAM6C,KAAK,IAAI,IAAI,CAAChE,MAAM,EAAE;QAC7B,IAAIgE,KAAK,CAACC,SAAS,CAAC9F,QAAQ,CAAC,EAAE;UAC3B6F,KAAK,CAACE,IAAI,CAAC/F,QAAQ,EAAEgD,KAAK,CAAC;QAC/B;MACJ;MACA,IAAIhD,QAAQ,CAAC2F,SAAS,EAAE;QACpBR,iBAAiB,CAACa,GAAG,CAAChG,QAAQ,CAAC;QAC/B;MACJ;MACA,IAAI,CAAC4B,QAAQ,CAACqE,MAAM,CAAC,IAAI/J,KAAK,CAAC8D,QAAQ,CAACnE,WAAW,CAAC,CAAC,EAAEmE,QAAQ,CAAC,CAAC;IACrE;IACA,IAAImF,iBAAiB,CAACxD,IAAI,EAAE;MACxB,MAAMuE,WAAW,GAAI/D,CAAC,IAAK,CAACgD,iBAAiB,CAACgB,GAAG,CAAChE,CAAC,CAAC;MACpD,IAAI,CAAC7B,MAAM,GAAG,IAAI,CAACvB,MAAM,CAACmH,WAAW,CAAC;MACtC,IAAI,CAAC3F,OAAO,GAAG,IAAI,CAACA,OAAO,CAACxB,MAAM,CAACmH,WAAW,CAAC;MAC/C,KAAK,MAAMlG,QAAQ,IAAImF,iBAAiB,EAAE;QACtC,IAAI,CAACjF,OAAO,CAACM,aAAa,CAAC1E,SAAS,CAACuF,eAAe,EAAE;UAClDnE,SAAS,EAAE,IAAI,CAACmC,UAAU;UAC1BqB,IAAI,EAAE;YACFV;UACJ;QACJ,CAAC,CAAC;MACN;MACA,IAAI,CAAC7C,UAAU,CAAC,GAAGgI,iBAAiB,CAAC;IACzC;IACA,IAAI,CAAC1D,mBAAmB,CAAC2E,gBAAgB,CAACpD,KAAK,CAAC;IAChD,KAAK,MAAMhD,QAAQ,IAAI,IAAI,CAACM,MAAM,EAAE;MAChC,KAAK,MAAM+F,OAAO,IAAI,IAAI,CAACvE,QAAQ,EAAE;QACjCuE,OAAO,CAACpD,MAAM,CAACjD,QAAQ,EAAEgD,KAAK,CAAC;MACnC;MACA,IAAI,CAAChD,QAAQ,CAAC2F,SAAS,IAAI,CAAC3F,QAAQ,CAACsG,QAAQ,EAAE;QAC3C,IAAI,CAAC7E,mBAAmB,CAAC8E,iBAAiB,CAACvG,QAAQ,EAAEgD,KAAK,CAAC;MAC/D;IACJ;IACA,OAAO,IAAI,CAACkC,aAAa;IACzB,IAAI,IAAI,CAAC3D,UAAU,EAAE;MACjB,MAAMiF,MAAM,GAAG,IAAI,CAACjG,OAAO;MAC3BiG,MAAM,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAAC9G,QAAQ,CAAC+G,CAAC,GAAGF,CAAC,CAAC7G,QAAQ,CAAC+G,CAAC,IAAIF,CAAC,CAACG,EAAE,GAAGF,CAAC,CAACE,EAAE,CAAC;MACjE,MAAMC,YAAY,GAAG,CAAC;MACtB,IAAI,CAACtF,WAAW,GAAGgF,MAAM,CAACA,MAAM,CAACvH,MAAM,GAAG6H,YAAY,CAAC,CAACjH,QAAQ,CAAC+G,CAAC;MAClE,IAAI,CAACrF,UAAU,GAAG,KAAK;IAC3B;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { OptionsColor } from \"../../OptionsColor\";\nexport class Trail {\n  constructor() {\n    this.enable = false;\n    this.length = 10;\n    this.fillColor = new OptionsColor();\n    this.fillColor.value = \"#000000\";\n  }\n\n  load(data) {\n    if (data === undefined) {\n      return;\n    }\n\n    if (data.enable !== undefined) {\n      this.enable = data.enable;\n    }\n\n    this.fillColor = OptionsColor.create(this.fillColor, data.fillColor);\n\n    if (data.length !== undefined) {\n      this.length = data.length;\n    }\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Options/Classes/Particles/Move/Trail.js"], "names": ["OptionsColor", "Trail", "constructor", "enable", "length", "fillColor", "value", "load", "data", "undefined", "create"], "mappings": "AAAA,SAASA,YAAT,QAA6B,oBAA7B;AACA,OAAO,MAAMC,KAAN,CAAY;AACfC,EAAAA,WAAW,GAAG;AACV,SAAKC,MAAL,GAAc,KAAd;AACA,SAAKC,MAAL,GAAc,EAAd;AACA,SAAKC,SAAL,GAAiB,IAAIL,YAAJ,EAAjB;AACA,SAAKK,SAAL,CAAeC,KAAf,GAAuB,SAAvB;AACH;;AACDC,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,QAAIA,IAAI,KAAKC,SAAb,EAAwB;AACpB;AACH;;AACD,QAAID,IAAI,CAACL,MAAL,KAAgBM,SAApB,EAA+B;AAC3B,WAAKN,MAAL,GAAcK,IAAI,CAACL,MAAnB;AACH;;AACD,SAAKE,SAAL,GAAiBL,YAAY,CAACU,MAAb,CAAoB,KAAKL,SAAzB,EAAoCG,IAAI,CAACH,SAAzC,CAAjB;;AACA,QAAIG,IAAI,CAACJ,MAAL,KAAgBK,SAApB,EAA+B;AAC3B,WAAKL,MAAL,GAAcI,IAAI,CAACJ,MAAnB;AACH;AACJ;;AAlBc", "sourcesContent": ["import { OptionsColor } from \"../../OptionsColor\";\nexport class Trail {\n    constructor() {\n        this.enable = false;\n        this.length = 10;\n        this.fillColor = new OptionsColor();\n        this.fillColor.value = \"#000000\";\n    }\n    load(data) {\n        if (data === undefined) {\n            return;\n        }\n        if (data.enable !== undefined) {\n            this.enable = data.enable;\n        }\n        this.fillColor = OptionsColor.create(this.fillColor, data.fillColor);\n        if (data.length !== undefined) {\n            this.length = data.length;\n        }\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
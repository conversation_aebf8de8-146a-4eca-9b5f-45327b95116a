{"ast": null, "code": "import { drawSquare } from \"./Utils.js\";\nconst sides = 4;\nexport class SquareDrawer {\n  constructor() {\n    this.validTypes = [\"edge\", \"square\"];\n  }\n  draw(data) {\n    drawSquare(data);\n  }\n  getSidesCount() {\n    return sides;\n  }\n}", "map": {"version": 3, "names": ["drawSquare", "sides", "SquareDrawer", "constructor", "validTypes", "draw", "data", "getSidesCount"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/shape-square/browser/SquareDrawer.js"], "sourcesContent": ["import { drawSquare } from \"./Utils.js\";\nconst sides = 4;\nexport class SquareDrawer {\n    constructor() {\n        this.validTypes = [\"edge\", \"square\"];\n    }\n    draw(data) {\n        drawSquare(data);\n    }\n    getSidesCount() {\n        return sides;\n    }\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,YAAY;AACvC,MAAMC,KAAK,GAAG,CAAC;AACf,OAAO,MAAMC,YAAY,CAAC;EACtBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,UAAU,GAAG,CAAC,MAAM,EAAE,QAAQ,CAAC;EACxC;EACAC,IAAIA,CAACC,IAAI,EAAE;IACPN,UAAU,CAACM,IAAI,CAAC;EACpB;EACAC,aAAaA,CAAA,EAAG;IACZ,OAAON,KAAK;EAChB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
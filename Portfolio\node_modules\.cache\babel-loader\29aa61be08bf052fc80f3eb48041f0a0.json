{"ast": null, "code": "import { OptionsColor } from \"../../../../Options/Classes/OptionsColor\";\nimport { stringToAlpha } from \"../../../../Utils\";\nexport class PolygonMaskDrawStroke {\n  constructor() {\n    this.color = new OptionsColor();\n    this.width = 0.5;\n    this.opacity = 1;\n  }\n\n  load(data) {\n    var _a;\n\n    if (!data) {\n      return;\n    }\n\n    this.color = OptionsColor.create(this.color, data.color);\n\n    if (typeof this.color.value === \"string\") {\n      this.opacity = (_a = stringToAlpha(this.color.value)) !== null && _a !== void 0 ? _a : this.opacity;\n    }\n\n    if (data.opacity !== undefined) {\n      this.opacity = data.opacity;\n    }\n\n    if (data.width !== undefined) {\n      this.width = data.width;\n    }\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Plugins/PolygonMask/Options/Classes/PolygonMaskDrawStroke.js"], "names": ["OptionsColor", "stringToAlpha", "PolygonMaskDrawStroke", "constructor", "color", "width", "opacity", "load", "data", "_a", "create", "value", "undefined"], "mappings": "AAAA,SAASA,YAAT,QAA6B,0CAA7B;AACA,SAASC,aAAT,QAA8B,mBAA9B;AACA,OAAO,MAAMC,qBAAN,CAA4B;AAC/BC,EAAAA,WAAW,GAAG;AACV,SAAKC,KAAL,GAAa,IAAIJ,YAAJ,EAAb;AACA,SAAKK,KAAL,GAAa,GAAb;AACA,SAAKC,OAAL,GAAe,CAAf;AACH;;AACDC,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,QAAIC,EAAJ;;AACA,QAAI,CAACD,IAAL,EAAW;AACP;AACH;;AACD,SAAKJ,KAAL,GAAaJ,YAAY,CAACU,MAAb,CAAoB,KAAKN,KAAzB,EAAgCI,IAAI,CAACJ,KAArC,CAAb;;AACA,QAAI,OAAO,KAAKA,KAAL,CAAWO,KAAlB,KAA4B,QAAhC,EAA0C;AACtC,WAAKL,OAAL,GAAe,CAACG,EAAE,GAAGR,aAAa,CAAC,KAAKG,KAAL,CAAWO,KAAZ,CAAnB,MAA2C,IAA3C,IAAmDF,EAAE,KAAK,KAAK,CAA/D,GAAmEA,EAAnE,GAAwE,KAAKH,OAA5F;AACH;;AACD,QAAIE,IAAI,CAACF,OAAL,KAAiBM,SAArB,EAAgC;AAC5B,WAAKN,OAAL,GAAeE,IAAI,CAACF,OAApB;AACH;;AACD,QAAIE,IAAI,CAACH,KAAL,KAAeO,SAAnB,EAA8B;AAC1B,WAAKP,KAAL,GAAaG,IAAI,CAACH,KAAlB;AACH;AACJ;;AArB8B", "sourcesContent": ["import { OptionsColor } from \"../../../../Options/Classes/OptionsColor\";\nimport { stringToAlpha } from \"../../../../Utils\";\nexport class PolygonMaskDrawStroke {\n    constructor() {\n        this.color = new OptionsColor();\n        this.width = 0.5;\n        this.opacity = 1;\n    }\n    load(data) {\n        var _a;\n        if (!data) {\n            return;\n        }\n        this.color = OptionsColor.create(this.color, data.color);\n        if (typeof this.color.value === \"string\") {\n            this.opacity = (_a = stringToAlpha(this.color.value)) !== null && _a !== void 0 ? _a : this.opacity;\n        }\n        if (data.opacity !== undefined) {\n            this.opacity = data.opacity;\n        }\n        if (data.width !== undefined) {\n            this.width = data.width;\n        }\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
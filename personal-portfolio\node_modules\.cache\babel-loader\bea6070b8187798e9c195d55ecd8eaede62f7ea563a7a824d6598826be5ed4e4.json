{"ast": null, "code": "import { EmitterShapeBase } from \"@tsparticles/plugin-emitters\";\nimport { getRandom, halfRandom } from \"@tsparticles/engine\";\nconst half = 0.5,\n  sides = 4,\n  double = 2;\nvar Sides;\n(function (Sides) {\n  Sides[Sides[\"TopLeft\"] = 0] = \"TopLeft\";\n  Sides[Sides[\"TopRight\"] = 1] = \"TopRight\";\n  Sides[Sides[\"BottomRight\"] = 2] = \"BottomRight\";\n  Sides[Sides[\"BottomLeft\"] = 3] = \"BottomLeft\";\n})(Sides || (Sides = {}));\nfunction randomSquareCoordinate(position, offset) {\n  return position + offset * (getRandom() - halfRandom);\n}\nexport class EmittersSquareShape extends EmitterShapeBase {\n  constructor(position, size, fill, options) {\n    super(position, size, fill, options);\n  }\n  async init() {}\n  randomPosition() {\n    const fill = this.fill,\n      position = this.position,\n      size = this.size;\n    if (fill) {\n      return {\n        position: {\n          x: randomSquareCoordinate(position.x, size.width),\n          y: randomSquareCoordinate(position.y, size.height)\n        }\n      };\n    } else {\n      const halfW = size.width * half,\n        halfH = size.height * half,\n        side = Math.floor(getRandom() * sides),\n        v = (getRandom() - halfRandom) * double;\n      switch (side) {\n        case Sides.TopLeft:\n          return {\n            position: {\n              x: position.x + v * halfW,\n              y: position.y - halfH\n            }\n          };\n        case Sides.TopRight:\n          return {\n            position: {\n              x: position.x - halfW,\n              y: position.y + v * halfH\n            }\n          };\n        case Sides.BottomRight:\n          return {\n            position: {\n              x: position.x + v * halfW,\n              y: position.y + halfH\n            }\n          };\n        case Sides.BottomLeft:\n        default:\n          return {\n            position: {\n              x: position.x + halfW,\n              y: position.y + v * halfH\n            }\n          };\n      }\n    }\n  }\n}", "map": {"version": 3, "names": ["EmitterShapeBase", "getRandom", "halfRandom", "half", "sides", "double", "Sides", "randomSquareCoordinate", "position", "offset", "EmittersSquareShape", "constructor", "size", "fill", "options", "init", "randomPosition", "x", "width", "y", "height", "halfW", "halfH", "side", "Math", "floor", "v", "TopLeft", "TopRight", "BottomRight", "BottomLeft"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/plugin-emitters-shape-square/browser/EmittersSquareShape.js"], "sourcesContent": ["import { EmitterShapeBase } from \"@tsparticles/plugin-emitters\";\nimport { getRandom, halfRandom } from \"@tsparticles/engine\";\nconst half = 0.5, sides = 4, double = 2;\nvar Sides;\n(function (Sides) {\n    Sides[Sides[\"TopLeft\"] = 0] = \"TopLeft\";\n    Sides[Sides[\"TopRight\"] = 1] = \"TopRight\";\n    Sides[Sides[\"BottomRight\"] = 2] = \"BottomRight\";\n    Sides[Sides[\"BottomLeft\"] = 3] = \"BottomLeft\";\n})(Sides || (Sides = {}));\nfunction randomSquareCoordinate(position, offset) {\n    return position + offset * (getRandom() - halfRandom);\n}\nexport class EmittersSquareShape extends EmitterShapeBase {\n    constructor(position, size, fill, options) {\n        super(position, size, fill, options);\n    }\n    async init() {\n    }\n    randomPosition() {\n        const fill = this.fill, position = this.position, size = this.size;\n        if (fill) {\n            return {\n                position: {\n                    x: randomSquareCoordinate(position.x, size.width),\n                    y: randomSquareCoordinate(position.y, size.height),\n                },\n            };\n        }\n        else {\n            const halfW = size.width * half, halfH = size.height * half, side = Math.floor(getRandom() * sides), v = (getRandom() - halfRandom) * double;\n            switch (side) {\n                case Sides.TopLeft:\n                    return {\n                        position: {\n                            x: position.x + v * halfW,\n                            y: position.y - halfH,\n                        },\n                    };\n                case Sides.TopRight:\n                    return {\n                        position: {\n                            x: position.x - halfW,\n                            y: position.y + v * halfH,\n                        },\n                    };\n                case Sides.BottomRight:\n                    return {\n                        position: {\n                            x: position.x + v * halfW,\n                            y: position.y + halfH,\n                        },\n                    };\n                case Sides.BottomLeft:\n                default:\n                    return {\n                        position: {\n                            x: position.x + halfW,\n                            y: position.y + v * halfH,\n                        },\n                    };\n            }\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,8BAA8B;AAC/D,SAASC,SAAS,EAAEC,UAAU,QAAQ,qBAAqB;AAC3D,MAAMC,IAAI,GAAG,GAAG;EAAEC,KAAK,GAAG,CAAC;EAAEC,MAAM,GAAG,CAAC;AACvC,IAAIC,KAAK;AACT,CAAC,UAAUA,KAAK,EAAE;EACdA,KAAK,CAACA,KAAK,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS;EACvCA,KAAK,CAACA,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,GAAG,UAAU;EACzCA,KAAK,CAACA,KAAK,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,GAAG,aAAa;EAC/CA,KAAK,CAACA,KAAK,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,GAAG,YAAY;AACjD,CAAC,EAAEA,KAAK,KAAKA,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC;AACzB,SAASC,sBAAsBA,CAACC,QAAQ,EAAEC,MAAM,EAAE;EAC9C,OAAOD,QAAQ,GAAGC,MAAM,IAAIR,SAAS,CAAC,CAAC,GAAGC,UAAU,CAAC;AACzD;AACA,OAAO,MAAMQ,mBAAmB,SAASV,gBAAgB,CAAC;EACtDW,WAAWA,CAACH,QAAQ,EAAEI,IAAI,EAAEC,IAAI,EAAEC,OAAO,EAAE;IACvC,KAAK,CAACN,QAAQ,EAAEI,IAAI,EAAEC,IAAI,EAAEC,OAAO,CAAC;EACxC;EACA,MAAMC,IAAIA,CAAA,EAAG,CACb;EACAC,cAAcA,CAAA,EAAG;IACb,MAAMH,IAAI,GAAG,IAAI,CAACA,IAAI;MAAEL,QAAQ,GAAG,IAAI,CAACA,QAAQ;MAAEI,IAAI,GAAG,IAAI,CAACA,IAAI;IAClE,IAAIC,IAAI,EAAE;MACN,OAAO;QACHL,QAAQ,EAAE;UACNS,CAAC,EAAEV,sBAAsB,CAACC,QAAQ,CAACS,CAAC,EAAEL,IAAI,CAACM,KAAK,CAAC;UACjDC,CAAC,EAAEZ,sBAAsB,CAACC,QAAQ,CAACW,CAAC,EAAEP,IAAI,CAACQ,MAAM;QACrD;MACJ,CAAC;IACL,CAAC,MACI;MACD,MAAMC,KAAK,GAAGT,IAAI,CAACM,KAAK,GAAGf,IAAI;QAAEmB,KAAK,GAAGV,IAAI,CAACQ,MAAM,GAAGjB,IAAI;QAAEoB,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACxB,SAAS,CAAC,CAAC,GAAGG,KAAK,CAAC;QAAEsB,CAAC,GAAG,CAACzB,SAAS,CAAC,CAAC,GAAGC,UAAU,IAAIG,MAAM;MAC5I,QAAQkB,IAAI;QACR,KAAKjB,KAAK,CAACqB,OAAO;UACd,OAAO;YACHnB,QAAQ,EAAE;cACNS,CAAC,EAAET,QAAQ,CAACS,CAAC,GAAGS,CAAC,GAAGL,KAAK;cACzBF,CAAC,EAAEX,QAAQ,CAACW,CAAC,GAAGG;YACpB;UACJ,CAAC;QACL,KAAKhB,KAAK,CAACsB,QAAQ;UACf,OAAO;YACHpB,QAAQ,EAAE;cACNS,CAAC,EAAET,QAAQ,CAACS,CAAC,GAAGI,KAAK;cACrBF,CAAC,EAAEX,QAAQ,CAACW,CAAC,GAAGO,CAAC,GAAGJ;YACxB;UACJ,CAAC;QACL,KAAKhB,KAAK,CAACuB,WAAW;UAClB,OAAO;YACHrB,QAAQ,EAAE;cACNS,CAAC,EAAET,QAAQ,CAACS,CAAC,GAAGS,CAAC,GAAGL,KAAK;cACzBF,CAAC,EAAEX,QAAQ,CAACW,CAAC,GAAGG;YACpB;UACJ,CAAC;QACL,KAAKhB,KAAK,CAACwB,UAAU;QACrB;UACI,OAAO;YACHtB,QAAQ,EAAE;cACNS,CAAC,EAAET,QAAQ,CAACS,CAAC,GAAGI,KAAK;cACrBF,CAAC,EAAEX,QAAQ,CAACW,CAAC,GAAGO,CAAC,GAAGJ;YACxB;UACJ,CAAC;MACT;IACJ;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
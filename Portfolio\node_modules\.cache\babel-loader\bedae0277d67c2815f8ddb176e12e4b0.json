{"ast": null, "code": "import { AnimationOptions } from \"../../AnimationOptions\";\nimport { OptionsColor } from \"../../OptionsColor\";\nimport { OrbitRotation } from \"./OrbitRotation\";\nimport { setRangeValue } from \"../../../../Utils\";\nexport class Orbit {\n  constructor() {\n    this.animation = new AnimationOptions();\n    this.enable = false;\n    this.opacity = 1;\n    this.rotation = new OrbitRotation();\n    this.width = 1;\n  }\n\n  load(data) {\n    if (data === undefined) {\n      return;\n    }\n\n    this.animation.load(data.animation);\n    this.rotation.load(data.rotation);\n\n    if (data.enable !== undefined) {\n      this.enable = data.enable;\n    }\n\n    if (data.opacity !== undefined) {\n      this.opacity = setRangeValue(data.opacity);\n    }\n\n    if (data.width !== undefined) {\n      this.width = setRangeValue(data.width);\n    }\n\n    if (data.radius !== undefined) {\n      this.radius = setRangeValue(data.radius);\n    }\n\n    if (data.color !== undefined) {\n      this.color = OptionsColor.create(this.color, data.color);\n    }\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Options/Classes/Particles/Orbit/Orbit.js"], "names": ["AnimationOptions", "OptionsColor", "OrbitRotation", "setRangeValue", "Orbit", "constructor", "animation", "enable", "opacity", "rotation", "width", "load", "data", "undefined", "radius", "color", "create"], "mappings": "AAAA,SAASA,gBAAT,QAAiC,wBAAjC;AACA,SAASC,YAAT,QAA6B,oBAA7B;AACA,SAASC,aAAT,QAA8B,iBAA9B;AACA,SAASC,aAAT,QAA8B,mBAA9B;AACA,OAAO,MAAMC,KAAN,CAAY;AACfC,EAAAA,WAAW,GAAG;AACV,SAAKC,SAAL,GAAiB,IAAIN,gBAAJ,EAAjB;AACA,SAAKO,MAAL,GAAc,KAAd;AACA,SAAKC,OAAL,GAAe,CAAf;AACA,SAAKC,QAAL,GAAgB,IAAIP,aAAJ,EAAhB;AACA,SAAKQ,KAAL,GAAa,CAAb;AACH;;AACDC,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,QAAIA,IAAI,KAAKC,SAAb,EAAwB;AACpB;AACH;;AACD,SAAKP,SAAL,CAAeK,IAAf,CAAoBC,IAAI,CAACN,SAAzB;AACA,SAAKG,QAAL,CAAcE,IAAd,CAAmBC,IAAI,CAACH,QAAxB;;AACA,QAAIG,IAAI,CAACL,MAAL,KAAgBM,SAApB,EAA+B;AAC3B,WAAKN,MAAL,GAAcK,IAAI,CAACL,MAAnB;AACH;;AACD,QAAIK,IAAI,CAACJ,OAAL,KAAiBK,SAArB,EAAgC;AAC5B,WAAKL,OAAL,GAAeL,aAAa,CAACS,IAAI,CAACJ,OAAN,CAA5B;AACH;;AACD,QAAII,IAAI,CAACF,KAAL,KAAeG,SAAnB,EAA8B;AAC1B,WAAKH,KAAL,GAAaP,aAAa,CAACS,IAAI,CAACF,KAAN,CAA1B;AACH;;AACD,QAAIE,IAAI,CAACE,MAAL,KAAgBD,SAApB,EAA+B;AAC3B,WAAKC,MAAL,GAAcX,aAAa,CAACS,IAAI,CAACE,MAAN,CAA3B;AACH;;AACD,QAAIF,IAAI,CAACG,KAAL,KAAeF,SAAnB,EAA8B;AAC1B,WAAKE,KAAL,GAAad,YAAY,CAACe,MAAb,CAAoB,KAAKD,KAAzB,EAAgCH,IAAI,CAACG,KAArC,CAAb;AACH;AACJ;;AA7Bc", "sourcesContent": ["import { AnimationOptions } from \"../../AnimationOptions\";\nimport { OptionsColor } from \"../../OptionsColor\";\nimport { OrbitRotation } from \"./OrbitRotation\";\nimport { setRangeValue } from \"../../../../Utils\";\nexport class Orbit {\n    constructor() {\n        this.animation = new AnimationOptions();\n        this.enable = false;\n        this.opacity = 1;\n        this.rotation = new OrbitRotation();\n        this.width = 1;\n    }\n    load(data) {\n        if (data === undefined) {\n            return;\n        }\n        this.animation.load(data.animation);\n        this.rotation.load(data.rotation);\n        if (data.enable !== undefined) {\n            this.enable = data.enable;\n        }\n        if (data.opacity !== undefined) {\n            this.opacity = setRangeValue(data.opacity);\n        }\n        if (data.width !== undefined) {\n            this.width = setRangeValue(data.width);\n        }\n        if (data.radius !== undefined) {\n            this.radius = setRangeValue(data.radius);\n        }\n        if (data.color !== undefined) {\n            this.color = OptionsColor.create(this.color, data.color);\n        }\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
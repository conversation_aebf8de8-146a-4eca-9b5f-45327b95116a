{"ast": null, "code": "import { ValueWithRandom } from \"../../ValueWithRandom\";\nimport { setRangeValue } from \"../../../../Utils\";\nexport class Repulse extends ValueWithRandom {\n  constructor() {\n    super();\n    this.enabled = false;\n    this.distance = 1;\n    this.duration = 1;\n    this.factor = 1;\n    this.speed = 1;\n  }\n\n  load(data) {\n    super.load(data);\n\n    if (!data) {\n      return;\n    }\n\n    if (data.enabled !== undefined) {\n      this.enabled = data.enabled;\n    }\n\n    if (data.distance !== undefined) {\n      this.distance = setRangeValue(data.distance);\n    }\n\n    if (data.duration !== undefined) {\n      this.duration = setRangeValue(data.duration);\n    }\n\n    if (data.factor !== undefined) {\n      this.factor = setRangeValue(data.factor);\n    }\n\n    if (data.speed !== undefined) {\n      this.speed = setRangeValue(data.speed);\n    }\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Options/Classes/Particles/Repulse/Repulse.js"], "names": ["ValueWithRandom", "setRangeValue", "Repulse", "constructor", "enabled", "distance", "duration", "factor", "speed", "load", "data", "undefined"], "mappings": "AAAA,SAASA,eAAT,QAAgC,uBAAhC;AACA,SAASC,aAAT,QAA8B,mBAA9B;AACA,OAAO,MAAMC,OAAN,SAAsBF,eAAtB,CAAsC;AACzCG,EAAAA,WAAW,GAAG;AACV;AACA,SAAKC,OAAL,GAAe,KAAf;AACA,SAAKC,QAAL,GAAgB,CAAhB;AACA,SAAKC,QAAL,GAAgB,CAAhB;AACA,SAAKC,MAAL,GAAc,CAAd;AACA,SAAKC,KAAL,GAAa,CAAb;AACH;;AACDC,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,UAAMD,IAAN,CAAWC,IAAX;;AACA,QAAI,CAACA,IAAL,EAAW;AACP;AACH;;AACD,QAAIA,IAAI,CAACN,OAAL,KAAiBO,SAArB,EAAgC;AAC5B,WAAKP,OAAL,GAAeM,IAAI,CAACN,OAApB;AACH;;AACD,QAAIM,IAAI,CAACL,QAAL,KAAkBM,SAAtB,EAAiC;AAC7B,WAAKN,QAAL,GAAgBJ,aAAa,CAACS,IAAI,CAACL,QAAN,CAA7B;AACH;;AACD,QAAIK,IAAI,CAACJ,QAAL,KAAkBK,SAAtB,EAAiC;AAC7B,WAAKL,QAAL,GAAgBL,aAAa,CAACS,IAAI,CAACJ,QAAN,CAA7B;AACH;;AACD,QAAII,IAAI,CAACH,MAAL,KAAgBI,SAApB,EAA+B;AAC3B,WAAKJ,MAAL,GAAcN,aAAa,CAACS,IAAI,CAACH,MAAN,CAA3B;AACH;;AACD,QAAIG,IAAI,CAACF,KAAL,KAAeG,SAAnB,EAA8B;AAC1B,WAAKH,KAAL,GAAaP,aAAa,CAACS,IAAI,CAACF,KAAN,CAA1B;AACH;AACJ;;AA7BwC", "sourcesContent": ["import { ValueWithRandom } from \"../../ValueWithRandom\";\nimport { setRangeValue } from \"../../../../Utils\";\nexport class Repulse extends ValueWithRandom {\n    constructor() {\n        super();\n        this.enabled = false;\n        this.distance = 1;\n        this.duration = 1;\n        this.factor = 1;\n        this.speed = 1;\n    }\n    load(data) {\n        super.load(data);\n        if (!data) {\n            return;\n        }\n        if (data.enabled !== undefined) {\n            this.enabled = data.enabled;\n        }\n        if (data.distance !== undefined) {\n            this.distance = setRangeValue(data.distance);\n        }\n        if (data.duration !== undefined) {\n            this.duration = setRangeValue(data.duration);\n        }\n        if (data.factor !== undefined) {\n            this.factor = setRangeValue(data.factor);\n        }\n        if (data.speed !== undefined) {\n            this.speed = setRangeValue(data.speed);\n        }\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
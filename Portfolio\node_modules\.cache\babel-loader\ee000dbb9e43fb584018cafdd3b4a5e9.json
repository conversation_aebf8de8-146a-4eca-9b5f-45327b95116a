{"ast": null, "code": "import { ValueWithRandom } from \"../../ValueWithRandom\";\nexport class LifeDuration extends ValueWithRandom {\n  constructor() {\n    super();\n    this.random.minimumValue = 0.0001;\n    this.sync = false;\n  }\n\n  load(data) {\n    if (data === undefined) {\n      return;\n    }\n\n    super.load(data);\n\n    if (data.sync !== undefined) {\n      this.sync = data.sync;\n    }\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Options/Classes/Particles/Life/LifeDuration.js"], "names": ["ValueWithRandom", "LifeDuration", "constructor", "random", "minimumValue", "sync", "load", "data", "undefined"], "mappings": "AAAA,SAASA,eAAT,QAAgC,uBAAhC;AACA,OAAO,MAAMC,YAAN,SAA2BD,eAA3B,CAA2C;AAC9CE,EAAAA,WAAW,GAAG;AACV;AACA,SAAKC,MAAL,CAAYC,YAAZ,GAA2B,MAA3B;AACA,SAAKC,IAAL,GAAY,KAAZ;AACH;;AACDC,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,QAAIA,IAAI,KAAKC,SAAb,EAAwB;AACpB;AACH;;AACD,UAAMF,IAAN,CAAWC,IAAX;;AACA,QAAIA,IAAI,CAACF,IAAL,KAAcG,SAAlB,EAA6B;AACzB,WAAKH,IAAL,GAAYE,IAAI,CAACF,IAAjB;AACH;AACJ;;AAd6C", "sourcesContent": ["import { ValueWithRandom } from \"../../ValueWithRandom\";\nexport class LifeDuration extends ValueWithRandom {\n    constructor() {\n        super();\n        this.random.minimumValue = 0.0001;\n        this.sync = false;\n    }\n    load(data) {\n        if (data === undefined) {\n            return;\n        }\n        super.load(data);\n        if (data.sync !== undefined) {\n            this.sync = data.sync;\n        }\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
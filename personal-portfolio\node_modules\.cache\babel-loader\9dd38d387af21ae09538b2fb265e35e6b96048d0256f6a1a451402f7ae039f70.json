{"ast": null, "code": "const defaultInset = 2,\n  origin = {\n    x: 0,\n    y: 0\n  };\nexport function drawStar(data) {\n  const {\n      context,\n      particle,\n      radius\n    } = data,\n    sides = particle.sides,\n    inset = particle.starInset ?? defaultInset;\n  context.moveTo(origin.x, origin.y - radius);\n  for (let i = 0; i < sides; i++) {\n    context.rotate(Math.PI / sides);\n    context.lineTo(origin.x, origin.y - radius * inset);\n    context.rotate(Math.PI / sides);\n    context.lineTo(origin.x, origin.y - radius);\n  }\n}", "map": {"version": 3, "names": ["defaultInset", "origin", "x", "y", "drawStar", "data", "context", "particle", "radius", "sides", "inset", "starInset", "moveTo", "i", "rotate", "Math", "PI", "lineTo"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/shape-star/browser/Utils.js"], "sourcesContent": ["const defaultInset = 2, origin = { x: 0, y: 0 };\nexport function drawStar(data) {\n    const { context, particle, radius } = data, sides = particle.sides, inset = particle.starInset ?? defaultInset;\n    context.moveTo(origin.x, origin.y - radius);\n    for (let i = 0; i < sides; i++) {\n        context.rotate(Math.PI / sides);\n        context.lineTo(origin.x, origin.y - radius * inset);\n        context.rotate(Math.PI / sides);\n        context.lineTo(origin.x, origin.y - radius);\n    }\n}\n"], "mappings": "AAAA,MAAMA,YAAY,GAAG,CAAC;EAAEC,MAAM,GAAG;IAAEC,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE;EAAE,CAAC;AAC/C,OAAO,SAASC,QAAQA,CAACC,IAAI,EAAE;EAC3B,MAAM;MAAEC,OAAO;MAAEC,QAAQ;MAAEC;IAAO,CAAC,GAAGH,IAAI;IAAEI,KAAK,GAAGF,QAAQ,CAACE,KAAK;IAAEC,KAAK,GAAGH,QAAQ,CAACI,SAAS,IAAIX,YAAY;EAC9GM,OAAO,CAACM,MAAM,CAACX,MAAM,CAACC,CAAC,EAAED,MAAM,CAACE,CAAC,GAAGK,MAAM,CAAC;EAC3C,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,KAAK,EAAEI,CAAC,EAAE,EAAE;IAC5BP,OAAO,CAACQ,MAAM,CAACC,IAAI,CAACC,EAAE,GAAGP,KAAK,CAAC;IAC/BH,OAAO,CAACW,MAAM,CAAChB,MAAM,CAACC,CAAC,EAAED,MAAM,CAACE,CAAC,GAAGK,MAAM,GAAGE,KAAK,CAAC;IACnDJ,OAAO,CAACQ,MAAM,CAACC,IAAI,CAACC,EAAE,GAAGP,KAAK,CAAC;IAC/BH,OAAO,CAACW,MAAM,CAAChB,MAAM,CAACC,CAAC,EAAED,MAAM,CAACE,CAAC,GAAGK,MAAM,CAAC;EAC/C;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
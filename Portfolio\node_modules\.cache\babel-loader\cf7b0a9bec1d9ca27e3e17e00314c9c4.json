{"ast": null, "code": "export class Range {\n  constructor(x, y) {\n    this.position = {\n      x: x,\n      y: y\n    };\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Core/Utils/Range.js"], "names": ["Range", "constructor", "x", "y", "position"], "mappings": "AAAA,OAAO,MAAMA,KAAN,CAAY;AACfC,EAAAA,WAAW,CAACC,CAAD,EAAIC,CAAJ,EAAO;AACd,SAAKC,QAAL,GAAgB;AACZF,MAAAA,CAAC,EAAEA,CADS;AAEZC,MAAAA,CAAC,EAAEA;AAFS,KAAhB;AAIH;;AANc", "sourcesContent": ["export class Range {\n    constructor(x, y) {\n        this.position = {\n            x: x,\n            y: y,\n        };\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
{"ast": null, "code": "import { Circle, ParticlesInteractorBase, getDistances, getLinkRandomColor } from \"@tsparticles/engine\";\nimport { CircleWarp } from \"./CircleWarp.js\";\nimport { Links } from \"./Options/Classes/Links.js\";\nconst squarePower = 2,\n  opacityOffset = 1,\n  origin = {\n    x: 0,\n    y: 0\n  },\n  minDistance = 0;\nfunction getLinkDistance(pos1, pos2, optDistance, canvasSize, warp) {\n  const {\n    dx,\n    dy,\n    distance\n  } = getDistances(pos1, pos2);\n  if (!warp || distance <= optDistance) {\n    return distance;\n  }\n  const absDiffs = {\n      x: Math.abs(dx),\n      y: Math.abs(dy)\n    },\n    warpDistances = {\n      x: Math.min(absDiffs.x, canvasSize.width - absDiffs.x),\n      y: Math.min(absDiffs.y, canvasSize.height - absDiffs.y)\n    };\n  return Math.sqrt(warpDistances.x ** squarePower + warpDistances.y ** squarePower);\n}\nexport class Linker extends ParticlesInteractorBase {\n  constructor(container) {\n    super(container);\n    this._setColor = p1 => {\n      if (!p1.options.links) {\n        return;\n      }\n      const container = this.linkContainer,\n        linksOptions = p1.options.links;\n      let linkColor = linksOptions.id === undefined ? container.particles.linksColor : container.particles.linksColors.get(linksOptions.id);\n      if (linkColor) {\n        return;\n      }\n      const optColor = linksOptions.color;\n      linkColor = getLinkRandomColor(optColor, linksOptions.blink, linksOptions.consent);\n      if (linksOptions.id === undefined) {\n        container.particles.linksColor = linkColor;\n      } else {\n        container.particles.linksColors.set(linksOptions.id, linkColor);\n      }\n    };\n    this.linkContainer = container;\n  }\n  clear() {}\n  init() {\n    this.linkContainer.particles.linksColor = undefined;\n    this.linkContainer.particles.linksColors = new Map();\n  }\n  interact(p1) {\n    if (!p1.options.links) {\n      return;\n    }\n    p1.links = [];\n    const pos1 = p1.getPosition(),\n      container = this.container,\n      canvasSize = container.canvas.size;\n    if (pos1.x < origin.x || pos1.y < origin.y || pos1.x > canvasSize.width || pos1.y > canvasSize.height) {\n      return;\n    }\n    const linkOpt1 = p1.options.links,\n      optOpacity = linkOpt1.opacity,\n      optDistance = p1.retina.linksDistance ?? minDistance,\n      warp = linkOpt1.warp;\n    let range;\n    if (warp) {\n      range = new CircleWarp(pos1.x, pos1.y, optDistance, canvasSize);\n    } else {\n      range = new Circle(pos1.x, pos1.y, optDistance);\n    }\n    const query = container.particles.quadTree.query(range);\n    for (const p2 of query) {\n      const linkOpt2 = p2.options.links;\n      if (p1 === p2 || !linkOpt2?.enable || linkOpt1.id !== linkOpt2.id || p2.spawning || p2.destroyed || !p2.links || p1.links.some(t => t.destination === p2) || p2.links.some(t => t.destination === p1)) {\n        continue;\n      }\n      const pos2 = p2.getPosition();\n      if (pos2.x < origin.x || pos2.y < origin.y || pos2.x > canvasSize.width || pos2.y > canvasSize.height) {\n        continue;\n      }\n      const distance = getLinkDistance(pos1, pos2, optDistance, canvasSize, warp && linkOpt2.warp);\n      if (distance > optDistance) {\n        continue;\n      }\n      const opacityLine = (opacityOffset - distance / optDistance) * optOpacity;\n      this._setColor(p1);\n      p1.links.push({\n        destination: p2,\n        opacity: opacityLine\n      });\n    }\n  }\n  isEnabled(particle) {\n    return !!particle.options.links?.enable;\n  }\n  loadParticlesOptions(options, ...sources) {\n    if (!options.links) {\n      options.links = new Links();\n    }\n    for (const source of sources) {\n      options.links.load(source?.links);\n    }\n  }\n  reset() {}\n}", "map": {"version": 3, "names": ["Circle", "ParticlesInteractorBase", "getDistances", "getLinkRandomColor", "CircleWarp", "Links", "squarePower", "opacityOffset", "origin", "x", "y", "minDistance", "getLinkDistance", "pos1", "pos2", "optDistance", "canvasSize", "warp", "dx", "dy", "distance", "absDiffs", "Math", "abs", "warpDistances", "min", "width", "height", "sqrt", "<PERSON><PERSON>", "constructor", "container", "_setColor", "p1", "options", "links", "linkContainer", "linksOptions", "linkColor", "id", "undefined", "particles", "linksColor", "linksColors", "get", "optColor", "color", "blink", "consent", "set", "clear", "init", "Map", "interact", "getPosition", "canvas", "size", "linkOpt1", "optOpacity", "opacity", "retina", "linksDistance", "range", "query", "quadTree", "p2", "linkOpt2", "enable", "spawning", "destroyed", "some", "t", "destination", "opacityLine", "push", "isEnabled", "particle", "loadParticlesOptions", "sources", "source", "load", "reset"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/interaction-particles-links/browser/Linker.js"], "sourcesContent": ["import { Circle, ParticlesInteractorBase, getDistances, getLinkRandomColor, } from \"@tsparticles/engine\";\nimport { CircleWarp } from \"./CircleWarp.js\";\nimport { Links } from \"./Options/Classes/Links.js\";\nconst squarePower = 2, opacityOffset = 1, origin = {\n    x: 0,\n    y: 0,\n}, minDistance = 0;\nfunction getLinkDistance(pos1, pos2, optDistance, canvasSize, warp) {\n    const { dx, dy, distance } = getDistances(pos1, pos2);\n    if (!warp || distance <= optDistance) {\n        return distance;\n    }\n    const absDiffs = {\n        x: Math.abs(dx),\n        y: Math.abs(dy),\n    }, warpDistances = {\n        x: Math.min(absDiffs.x, canvasSize.width - absDiffs.x),\n        y: Math.min(absDiffs.y, canvasSize.height - absDiffs.y),\n    };\n    return Math.sqrt(warpDistances.x ** squarePower + warpDistances.y ** squarePower);\n}\nexport class Linker extends ParticlesInteractorBase {\n    constructor(container) {\n        super(container);\n        this._setColor = p1 => {\n            if (!p1.options.links) {\n                return;\n            }\n            const container = this.linkContainer, linksOptions = p1.options.links;\n            let linkColor = linksOptions.id === undefined\n                ? container.particles.linksColor\n                : container.particles.linksColors.get(linksOptions.id);\n            if (linkColor) {\n                return;\n            }\n            const optColor = linksOptions.color;\n            linkColor = getLinkRandomColor(optColor, linksOptions.blink, linksOptions.consent);\n            if (linksOptions.id === undefined) {\n                container.particles.linksColor = linkColor;\n            }\n            else {\n                container.particles.linksColors.set(linksOptions.id, linkColor);\n            }\n        };\n        this.linkContainer = container;\n    }\n    clear() {\n    }\n    init() {\n        this.linkContainer.particles.linksColor = undefined;\n        this.linkContainer.particles.linksColors = new Map();\n    }\n    interact(p1) {\n        if (!p1.options.links) {\n            return;\n        }\n        p1.links = [];\n        const pos1 = p1.getPosition(), container = this.container, canvasSize = container.canvas.size;\n        if (pos1.x < origin.x || pos1.y < origin.y || pos1.x > canvasSize.width || pos1.y > canvasSize.height) {\n            return;\n        }\n        const linkOpt1 = p1.options.links, optOpacity = linkOpt1.opacity, optDistance = p1.retina.linksDistance ?? minDistance, warp = linkOpt1.warp;\n        let range;\n        if (warp) {\n            range = new CircleWarp(pos1.x, pos1.y, optDistance, canvasSize);\n        }\n        else {\n            range = new Circle(pos1.x, pos1.y, optDistance);\n        }\n        const query = container.particles.quadTree.query(range);\n        for (const p2 of query) {\n            const linkOpt2 = p2.options.links;\n            if (p1 === p2 ||\n                !linkOpt2?.enable ||\n                linkOpt1.id !== linkOpt2.id ||\n                p2.spawning ||\n                p2.destroyed ||\n                !p2.links ||\n                p1.links.some(t => t.destination === p2) ||\n                p2.links.some(t => t.destination === p1)) {\n                continue;\n            }\n            const pos2 = p2.getPosition();\n            if (pos2.x < origin.x || pos2.y < origin.y || pos2.x > canvasSize.width || pos2.y > canvasSize.height) {\n                continue;\n            }\n            const distance = getLinkDistance(pos1, pos2, optDistance, canvasSize, warp && linkOpt2.warp);\n            if (distance > optDistance) {\n                continue;\n            }\n            const opacityLine = (opacityOffset - distance / optDistance) * optOpacity;\n            this._setColor(p1);\n            p1.links.push({\n                destination: p2,\n                opacity: opacityLine,\n            });\n        }\n    }\n    isEnabled(particle) {\n        return !!particle.options.links?.enable;\n    }\n    loadParticlesOptions(options, ...sources) {\n        if (!options.links) {\n            options.links = new Links();\n        }\n        for (const source of sources) {\n            options.links.load(source?.links);\n        }\n    }\n    reset() {\n    }\n}\n"], "mappings": "AAAA,SAASA,MAAM,EAAEC,uBAAuB,EAAEC,YAAY,EAAEC,kBAAkB,QAAS,qBAAqB;AACxG,SAASC,UAAU,QAAQ,iBAAiB;AAC5C,SAASC,KAAK,QAAQ,4BAA4B;AAClD,MAAMC,WAAW,GAAG,CAAC;EAAEC,aAAa,GAAG,CAAC;EAAEC,MAAM,GAAG;IAC/CC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE;EACP,CAAC;EAAEC,WAAW,GAAG,CAAC;AAClB,SAASC,eAAeA,CAACC,IAAI,EAAEC,IAAI,EAAEC,WAAW,EAAEC,UAAU,EAAEC,IAAI,EAAE;EAChE,MAAM;IAAEC,EAAE;IAAEC,EAAE;IAAEC;EAAS,CAAC,GAAGlB,YAAY,CAACW,IAAI,EAAEC,IAAI,CAAC;EACrD,IAAI,CAACG,IAAI,IAAIG,QAAQ,IAAIL,WAAW,EAAE;IAClC,OAAOK,QAAQ;EACnB;EACA,MAAMC,QAAQ,GAAG;MACbZ,CAAC,EAAEa,IAAI,CAACC,GAAG,CAACL,EAAE,CAAC;MACfR,CAAC,EAAEY,IAAI,CAACC,GAAG,CAACJ,EAAE;IAClB,CAAC;IAAEK,aAAa,GAAG;MACff,CAAC,EAAEa,IAAI,CAACG,GAAG,CAACJ,QAAQ,CAACZ,CAAC,EAAEO,UAAU,CAACU,KAAK,GAAGL,QAAQ,CAACZ,CAAC,CAAC;MACtDC,CAAC,EAAEY,IAAI,CAACG,GAAG,CAACJ,QAAQ,CAACX,CAAC,EAAEM,UAAU,CAACW,MAAM,GAAGN,QAAQ,CAACX,CAAC;IAC1D,CAAC;EACD,OAAOY,IAAI,CAACM,IAAI,CAACJ,aAAa,CAACf,CAAC,IAAIH,WAAW,GAAGkB,aAAa,CAACd,CAAC,IAAIJ,WAAW,CAAC;AACrF;AACA,OAAO,MAAMuB,MAAM,SAAS5B,uBAAuB,CAAC;EAChD6B,WAAWA,CAACC,SAAS,EAAE;IACnB,KAAK,CAACA,SAAS,CAAC;IAChB,IAAI,CAACC,SAAS,GAAGC,EAAE,IAAI;MACnB,IAAI,CAACA,EAAE,CAACC,OAAO,CAACC,KAAK,EAAE;QACnB;MACJ;MACA,MAAMJ,SAAS,GAAG,IAAI,CAACK,aAAa;QAAEC,YAAY,GAAGJ,EAAE,CAACC,OAAO,CAACC,KAAK;MACrE,IAAIG,SAAS,GAAGD,YAAY,CAACE,EAAE,KAAKC,SAAS,GACvCT,SAAS,CAACU,SAAS,CAACC,UAAU,GAC9BX,SAAS,CAACU,SAAS,CAACE,WAAW,CAACC,GAAG,CAACP,YAAY,CAACE,EAAE,CAAC;MAC1D,IAAID,SAAS,EAAE;QACX;MACJ;MACA,MAAMO,QAAQ,GAAGR,YAAY,CAACS,KAAK;MACnCR,SAAS,GAAGnC,kBAAkB,CAAC0C,QAAQ,EAAER,YAAY,CAACU,KAAK,EAAEV,YAAY,CAACW,OAAO,CAAC;MAClF,IAAIX,YAAY,CAACE,EAAE,KAAKC,SAAS,EAAE;QAC/BT,SAAS,CAACU,SAAS,CAACC,UAAU,GAAGJ,SAAS;MAC9C,CAAC,MACI;QACDP,SAAS,CAACU,SAAS,CAACE,WAAW,CAACM,GAAG,CAACZ,YAAY,CAACE,EAAE,EAAED,SAAS,CAAC;MACnE;IACJ,CAAC;IACD,IAAI,CAACF,aAAa,GAAGL,SAAS;EAClC;EACAmB,KAAKA,CAAA,EAAG,CACR;EACAC,IAAIA,CAAA,EAAG;IACH,IAAI,CAACf,aAAa,CAACK,SAAS,CAACC,UAAU,GAAGF,SAAS;IACnD,IAAI,CAACJ,aAAa,CAACK,SAAS,CAACE,WAAW,GAAG,IAAIS,GAAG,CAAC,CAAC;EACxD;EACAC,QAAQA,CAACpB,EAAE,EAAE;IACT,IAAI,CAACA,EAAE,CAACC,OAAO,CAACC,KAAK,EAAE;MACnB;IACJ;IACAF,EAAE,CAACE,KAAK,GAAG,EAAE;IACb,MAAMtB,IAAI,GAAGoB,EAAE,CAACqB,WAAW,CAAC,CAAC;MAAEvB,SAAS,GAAG,IAAI,CAACA,SAAS;MAAEf,UAAU,GAAGe,SAAS,CAACwB,MAAM,CAACC,IAAI;IAC7F,IAAI3C,IAAI,CAACJ,CAAC,GAAGD,MAAM,CAACC,CAAC,IAAII,IAAI,CAACH,CAAC,GAAGF,MAAM,CAACE,CAAC,IAAIG,IAAI,CAACJ,CAAC,GAAGO,UAAU,CAACU,KAAK,IAAIb,IAAI,CAACH,CAAC,GAAGM,UAAU,CAACW,MAAM,EAAE;MACnG;IACJ;IACA,MAAM8B,QAAQ,GAAGxB,EAAE,CAACC,OAAO,CAACC,KAAK;MAAEuB,UAAU,GAAGD,QAAQ,CAACE,OAAO;MAAE5C,WAAW,GAAGkB,EAAE,CAAC2B,MAAM,CAACC,aAAa,IAAIlD,WAAW;MAAEM,IAAI,GAAGwC,QAAQ,CAACxC,IAAI;IAC5I,IAAI6C,KAAK;IACT,IAAI7C,IAAI,EAAE;MACN6C,KAAK,GAAG,IAAI1D,UAAU,CAACS,IAAI,CAACJ,CAAC,EAAEI,IAAI,CAACH,CAAC,EAAEK,WAAW,EAAEC,UAAU,CAAC;IACnE,CAAC,MACI;MACD8C,KAAK,GAAG,IAAI9D,MAAM,CAACa,IAAI,CAACJ,CAAC,EAAEI,IAAI,CAACH,CAAC,EAAEK,WAAW,CAAC;IACnD;IACA,MAAMgD,KAAK,GAAGhC,SAAS,CAACU,SAAS,CAACuB,QAAQ,CAACD,KAAK,CAACD,KAAK,CAAC;IACvD,KAAK,MAAMG,EAAE,IAAIF,KAAK,EAAE;MACpB,MAAMG,QAAQ,GAAGD,EAAE,CAAC/B,OAAO,CAACC,KAAK;MACjC,IAAIF,EAAE,KAAKgC,EAAE,IACT,CAACC,QAAQ,EAAEC,MAAM,IACjBV,QAAQ,CAAClB,EAAE,KAAK2B,QAAQ,CAAC3B,EAAE,IAC3B0B,EAAE,CAACG,QAAQ,IACXH,EAAE,CAACI,SAAS,IACZ,CAACJ,EAAE,CAAC9B,KAAK,IACTF,EAAE,CAACE,KAAK,CAACmC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,WAAW,KAAKP,EAAE,CAAC,IACxCA,EAAE,CAAC9B,KAAK,CAACmC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,WAAW,KAAKvC,EAAE,CAAC,EAAE;QAC1C;MACJ;MACA,MAAMnB,IAAI,GAAGmD,EAAE,CAACX,WAAW,CAAC,CAAC;MAC7B,IAAIxC,IAAI,CAACL,CAAC,GAAGD,MAAM,CAACC,CAAC,IAAIK,IAAI,CAACJ,CAAC,GAAGF,MAAM,CAACE,CAAC,IAAII,IAAI,CAACL,CAAC,GAAGO,UAAU,CAACU,KAAK,IAAIZ,IAAI,CAACJ,CAAC,GAAGM,UAAU,CAACW,MAAM,EAAE;QACnG;MACJ;MACA,MAAMP,QAAQ,GAAGR,eAAe,CAACC,IAAI,EAAEC,IAAI,EAAEC,WAAW,EAAEC,UAAU,EAAEC,IAAI,IAAIiD,QAAQ,CAACjD,IAAI,CAAC;MAC5F,IAAIG,QAAQ,GAAGL,WAAW,EAAE;QACxB;MACJ;MACA,MAAM0D,WAAW,GAAG,CAAClE,aAAa,GAAGa,QAAQ,GAAGL,WAAW,IAAI2C,UAAU;MACzE,IAAI,CAAC1B,SAAS,CAACC,EAAE,CAAC;MAClBA,EAAE,CAACE,KAAK,CAACuC,IAAI,CAAC;QACVF,WAAW,EAAEP,EAAE;QACfN,OAAO,EAAEc;MACb,CAAC,CAAC;IACN;EACJ;EACAE,SAASA,CAACC,QAAQ,EAAE;IAChB,OAAO,CAAC,CAACA,QAAQ,CAAC1C,OAAO,CAACC,KAAK,EAAEgC,MAAM;EAC3C;EACAU,oBAAoBA,CAAC3C,OAAO,EAAE,GAAG4C,OAAO,EAAE;IACtC,IAAI,CAAC5C,OAAO,CAACC,KAAK,EAAE;MAChBD,OAAO,CAACC,KAAK,GAAG,IAAI9B,KAAK,CAAC,CAAC;IAC/B;IACA,KAAK,MAAM0E,MAAM,IAAID,OAAO,EAAE;MAC1B5C,OAAO,CAACC,KAAK,CAAC6C,IAAI,CAACD,MAAM,EAAE5C,KAAK,CAAC;IACrC;EACJ;EACA8C,KAAKA,CAAA,EAAG,CACR;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
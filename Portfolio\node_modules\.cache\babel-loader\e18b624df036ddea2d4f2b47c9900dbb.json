{"ast": null, "code": "import { ParticlesInteractorBase } from \"../../../Core\";\nimport { getDistances } from \"../../../Utils\";\nexport class Attractor extends ParticlesInteractorBase {\n  constructor(container) {\n    super(container);\n  }\n\n  async interact(p1) {\n    var _a;\n\n    const container = this.container,\n          distance = (_a = p1.retina.attractDistance) !== null && _a !== void 0 ? _a : container.retina.attractDistance,\n          pos1 = p1.getPosition(),\n          query = container.particles.quadTree.queryCircle(pos1, distance);\n\n    for (const p2 of query) {\n      if (p1 === p2 || !p2.options.move.attract.enable || p2.destroyed || p2.spawning) {\n        continue;\n      }\n\n      const pos2 = p2.getPosition(),\n            {\n        dx,\n        dy\n      } = getDistances(pos1, pos2),\n            rotate = p1.options.move.attract.rotate,\n            ax = dx / (rotate.x * 1000),\n            ay = dy / (rotate.y * 1000),\n            p1Factor = p2.size.value / p1.size.value,\n            p2Factor = 1 / p1Factor;\n      p1.velocity.x -= ax * p1Factor;\n      p1.velocity.y -= ay * p1Factor;\n      p2.velocity.x += ax * p2Factor;\n      p2.velocity.y += ay * p2Factor;\n    }\n  }\n\n  isEnabled(particle) {\n    return particle.options.move.attract.enable;\n  }\n\n  reset() {}\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Interactions/Particles/Attract/Attractor.js"], "names": ["ParticlesInteractorBase", "getDistances", "Attractor", "constructor", "container", "interact", "p1", "_a", "distance", "retina", "attractDistance", "pos1", "getPosition", "query", "particles", "quadTree", "queryCircle", "p2", "options", "move", "attract", "enable", "destroyed", "spawning", "pos2", "dx", "dy", "rotate", "ax", "x", "ay", "y", "p1Factor", "size", "value", "p2Factor", "velocity", "isEnabled", "particle", "reset"], "mappings": "AAAA,SAASA,uBAAT,QAAwC,eAAxC;AACA,SAASC,YAAT,QAA6B,gBAA7B;AACA,OAAO,MAAMC,SAAN,SAAwBF,uBAAxB,CAAgD;AACnDG,EAAAA,WAAW,CAACC,SAAD,EAAY;AACnB,UAAMA,SAAN;AACH;;AACa,QAARC,QAAQ,CAACC,EAAD,EAAK;AACf,QAAIC,EAAJ;;AACA,UAAMH,SAAS,GAAG,KAAKA,SAAvB;AAAA,UAAkCI,QAAQ,GAAG,CAACD,EAAE,GAAGD,EAAE,CAACG,MAAH,CAAUC,eAAhB,MAAqC,IAArC,IAA6CH,EAAE,KAAK,KAAK,CAAzD,GAA6DA,EAA7D,GAAkEH,SAAS,CAACK,MAAV,CAAiBC,eAAhI;AAAA,UAAiJC,IAAI,GAAGL,EAAE,CAACM,WAAH,EAAxJ;AAAA,UAA0KC,KAAK,GAAGT,SAAS,CAACU,SAAV,CAAoBC,QAApB,CAA6BC,WAA7B,CAAyCL,IAAzC,EAA+CH,QAA/C,CAAlL;;AACA,SAAK,MAAMS,EAAX,IAAiBJ,KAAjB,EAAwB;AACpB,UAAIP,EAAE,KAAKW,EAAP,IAAa,CAACA,EAAE,CAACC,OAAH,CAAWC,IAAX,CAAgBC,OAAhB,CAAwBC,MAAtC,IAAgDJ,EAAE,CAACK,SAAnD,IAAgEL,EAAE,CAACM,QAAvE,EAAiF;AAC7E;AACH;;AACD,YAAMC,IAAI,GAAGP,EAAE,CAACL,WAAH,EAAb;AAAA,YAA+B;AAAEa,QAAAA,EAAF;AAAMC,QAAAA;AAAN,UAAazB,YAAY,CAACU,IAAD,EAAOa,IAAP,CAAxD;AAAA,YAAsEG,MAAM,GAAGrB,EAAE,CAACY,OAAH,CAAWC,IAAX,CAAgBC,OAAhB,CAAwBO,MAAvG;AAAA,YAA+GC,EAAE,GAAGH,EAAE,IAAIE,MAAM,CAACE,CAAP,GAAW,IAAf,CAAtH;AAAA,YAA4IC,EAAE,GAAGJ,EAAE,IAAIC,MAAM,CAACI,CAAP,GAAW,IAAf,CAAnJ;AAAA,YAAyKC,QAAQ,GAAGf,EAAE,CAACgB,IAAH,CAAQC,KAAR,GAAgB5B,EAAE,CAAC2B,IAAH,CAAQC,KAA5M;AAAA,YAAmNC,QAAQ,GAAG,IAAIH,QAAlO;AACA1B,MAAAA,EAAE,CAAC8B,QAAH,CAAYP,CAAZ,IAAiBD,EAAE,GAAGI,QAAtB;AACA1B,MAAAA,EAAE,CAAC8B,QAAH,CAAYL,CAAZ,IAAiBD,EAAE,GAAGE,QAAtB;AACAf,MAAAA,EAAE,CAACmB,QAAH,CAAYP,CAAZ,IAAiBD,EAAE,GAAGO,QAAtB;AACAlB,MAAAA,EAAE,CAACmB,QAAH,CAAYL,CAAZ,IAAiBD,EAAE,GAAGK,QAAtB;AACH;AACJ;;AACDE,EAAAA,SAAS,CAACC,QAAD,EAAW;AAChB,WAAOA,QAAQ,CAACpB,OAAT,CAAiBC,IAAjB,CAAsBC,OAAtB,CAA8BC,MAArC;AACH;;AACDkB,EAAAA,KAAK,GAAG,CACP;;AAtBkD", "sourcesContent": ["import { ParticlesInteractorBase } from \"../../../Core\";\nimport { getDistances } from \"../../../Utils\";\nexport class Attractor extends ParticlesInteractorBase {\n    constructor(container) {\n        super(container);\n    }\n    async interact(p1) {\n        var _a;\n        const container = this.container, distance = (_a = p1.retina.attractDistance) !== null && _a !== void 0 ? _a : container.retina.attractDistance, pos1 = p1.getPosition(), query = container.particles.quadTree.queryCircle(pos1, distance);\n        for (const p2 of query) {\n            if (p1 === p2 || !p2.options.move.attract.enable || p2.destroyed || p2.spawning) {\n                continue;\n            }\n            const pos2 = p2.getPosition(), { dx, dy } = getDistances(pos1, pos2), rotate = p1.options.move.attract.rotate, ax = dx / (rotate.x * 1000), ay = dy / (rotate.y * 1000), p1Factor = p2.size.value / p1.size.value, p2Factor = 1 / p1Factor;\n            p1.velocity.x -= ax * p1Factor;\n            p1.velocity.y -= ay * p1Factor;\n            p2.velocity.x += ax * p2Factor;\n            p2.velocity.y += ay * p2Factor;\n        }\n    }\n    isEnabled(particle) {\n        return particle.options.move.attract.enable;\n    }\n    reset() {\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
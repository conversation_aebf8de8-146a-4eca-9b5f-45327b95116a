{"ast": null, "code": "export default function requiredArgs(required, args) {\n  if (args.length < required) {\n    throw new TypeError(required + ' argument' + (required > 1 ? 's' : '') + ' required, but only ' + args.length + ' present');\n  }\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/date-fns/esm/_lib/requiredArgs/index.js"], "names": ["requiredArgs", "required", "args", "length", "TypeError"], "mappings": "AAAA,eAAe,SAASA,YAAT,CAAsBC,QAAtB,EAAgCC,IAAhC,EAAsC;AACnD,MAAIA,IAAI,CAACC,MAAL,GAAcF,QAAlB,EAA4B;AAC1B,UAAM,IAAIG,SAAJ,CAAcH,QAAQ,GAAG,WAAX,IAA0BA,QAAQ,GAAG,CAAX,GAAe,GAAf,GAAqB,EAA/C,IAAqD,sBAArD,GAA8EC,IAAI,CAACC,MAAnF,GAA4F,UAA1G,CAAN;AACD;AACF", "sourcesContent": ["export default function requiredArgs(required, args) {\n  if (args.length < required) {\n    throw new TypeError(required + ' argument' + (required > 1 ? 's' : '') + ' required, but only ' + args.length + ' present');\n  }\n}"]}, "metadata": {}, "sourceType": "module"}
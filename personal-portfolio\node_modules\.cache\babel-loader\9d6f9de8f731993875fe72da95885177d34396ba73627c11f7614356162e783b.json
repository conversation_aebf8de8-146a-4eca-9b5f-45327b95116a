{"ast": null, "code": "import { StarDrawer } from \"./StarDrawer.js\";\nexport async function loadStarShape(engine, refresh = true) {\n  await engine.addShape(new StarDrawer(), refresh);\n}", "map": {"version": 3, "names": ["StarDrawer", "loadStarShape", "engine", "refresh", "addShape"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/shape-star/browser/index.js"], "sourcesContent": ["import { StarDrawer } from \"./StarDrawer.js\";\nexport async function loadStarShape(engine, refresh = true) {\n    await engine.addShape(new StarDrawer(), refresh);\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,iBAAiB;AAC5C,OAAO,eAAeC,aAAaA,CAACC,MAAM,EAAEC,OAAO,GAAG,IAAI,EAAE;EACxD,MAAMD,MAAM,CAACE,QAAQ,CAAC,IAAIJ,UAAU,CAAC,CAAC,EAAEG,OAAO,CAAC;AACpD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
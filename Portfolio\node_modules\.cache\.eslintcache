[{"D:\\Audio Songs\\React-portfolio\\Portfolio\\src\\index.js": "1", "D:\\Audio Songs\\React-portfolio\\Portfolio\\src\\App.js": "2", "D:\\Audio Songs\\React-portfolio\\Portfolio\\src\\reportWebVitals.js": "3", "D:\\Audio Songs\\React-portfolio\\Portfolio\\src\\components\\Footer.js": "4", "D:\\Audio Songs\\React-portfolio\\Portfolio\\src\\components\\ScrollToTop.js": "5", "D:\\Audio Songs\\React-portfolio\\Portfolio\\src\\components\\Pre.js": "6", "D:\\Audio Songs\\React-portfolio\\Portfolio\\src\\components\\Navbar.js": "7", "D:\\Audio Songs\\React-portfolio\\Portfolio\\src\\components\\Home\\Home.js": "8", "D:\\Audio Songs\\React-portfolio\\Portfolio\\src\\components\\Projects\\Projects.js": "9", "D:\\Audio Songs\\React-portfolio\\Portfolio\\src\\components\\About\\About.js": "10", "D:\\Audio Songs\\React-portfolio\\Portfolio\\src\\components\\Resume\\ResumeNew.js": "11", "D:\\Audio Songs\\React-portfolio\\Portfolio\\src\\components\\Particle.js": "12", "D:\\Audio Songs\\React-portfolio\\Portfolio\\src\\components\\Home\\Home2.js": "13", "D:\\Audio Songs\\React-portfolio\\Portfolio\\src\\components\\Home\\Type.js": "14", "D:\\Audio Songs\\React-portfolio\\Portfolio\\src\\components\\Projects\\ProjectCards.js": "15", "D:\\Audio Songs\\React-portfolio\\Portfolio\\src\\components\\About\\Github.js": "16", "D:\\Audio Songs\\React-portfolio\\Portfolio\\src\\components\\About\\Techstack.js": "17", "D:\\Audio Songs\\React-portfolio\\Portfolio\\src\\components\\About\\Toolstack.js": "18", "D:\\Audio Songs\\React-portfolio\\Portfolio\\src\\components\\About\\AboutCard.js": "19"}, {"size": 517, "mtime": 1751900147246, "results": "20", "hashOfConfig": "21"}, {"size": 1421, "mtime": 1751900147210, "results": "22", "hashOfConfig": "21"}, {"size": 375, "mtime": 1751900147246, "results": "23", "hashOfConfig": "21"}, {"size": 2146, "mtime": 1751900147242, "results": "24", "hashOfConfig": "21"}, {"size": 270, "mtime": 1751900147245, "results": "25", "hashOfConfig": "21"}, {"size": 149, "mtime": 1751900147243, "results": "26", "hashOfConfig": "21"}, {"size": 3673, "mtime": 1751900147243, "results": "27", "hashOfConfig": "21"}, {"size": 1415, "mtime": 1751900147242, "results": "28", "hashOfConfig": "21"}, {"size": 5015, "mtime": 1751900147244, "results": "29", "hashOfConfig": "21"}, {"size": 1551, "mtime": 1751900147241, "results": "30", "hashOfConfig": "21"}, {"size": 1739, "mtime": 1751900147245, "results": "31", "hashOfConfig": "21"}, {"size": 1126, "mtime": 1751900147243, "results": "32", "hashOfConfig": "21"}, {"size": 3919, "mtime": 1751900147242, "results": "33", "hashOfConfig": "21"}, {"size": 428, "mtime": 1751900147243, "results": "34", "hashOfConfig": "21"}, {"size": 1237, "mtime": 1751900147244, "results": "35", "hashOfConfig": "21"}, {"size": 582, "mtime": 1751900147241, "results": "36", "hashOfConfig": "21"}, {"size": 1788, "mtime": 1751900147241, "results": "37", "hashOfConfig": "21"}, {"size": 781, "mtime": 1751900147241, "results": "38", "hashOfConfig": "21"}, {"size": 1473, "mtime": 1751900147241, "results": "39", "hashOfConfig": "21"}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "acx6i0", {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\Audio Songs\\React-portfolio\\Portfolio\\src\\index.js", [], [], "D:\\Audio Songs\\React-portfolio\\Portfolio\\src\\App.js", [], [], "D:\\Audio Songs\\React-portfolio\\Portfolio\\src\\reportWebVitals.js", [], [], "D:\\Audio Songs\\React-portfolio\\Portfolio\\src\\components\\Footer.js", [], [], "D:\\Audio Songs\\React-portfolio\\Portfolio\\src\\components\\ScrollToTop.js", [], [], "D:\\Audio Songs\\React-portfolio\\Portfolio\\src\\components\\Pre.js", [], [], "D:\\Audio Songs\\React-portfolio\\Portfolio\\src\\components\\Navbar.js", [], [], "D:\\Audio Songs\\React-portfolio\\Portfolio\\src\\components\\Home\\Home.js", [], [], "D:\\Audio Songs\\React-portfolio\\Portfolio\\src\\components\\Projects\\Projects.js", [], [], "D:\\Audio Songs\\React-portfolio\\Portfolio\\src\\components\\About\\About.js", [], [], "D:\\Audio Songs\\React-portfolio\\Portfolio\\src\\components\\Resume\\ResumeNew.js", [], [], "D:\\Audio Songs\\React-portfolio\\Portfolio\\src\\components\\Particle.js", [], [], "D:\\Audio Songs\\React-portfolio\\Portfolio\\src\\components\\Home\\Home2.js", [], [], "D:\\Audio Songs\\React-portfolio\\Portfolio\\src\\components\\Home\\Type.js", [], [], "D:\\Audio Songs\\React-portfolio\\Portfolio\\src\\components\\Projects\\ProjectCards.js", [], [], "D:\\Audio Songs\\React-portfolio\\Portfolio\\src\\components\\About\\Github.js", [], [], "D:\\Audio Songs\\React-portfolio\\Portfolio\\src\\components\\About\\Techstack.js", [], [], "D:\\Audio Songs\\React-portfolio\\Portfolio\\src\\components\\About\\Toolstack.js", [], [], "D:\\Audio Songs\\React-portfolio\\Portfolio\\src\\components\\About\\AboutCard.js", [], []]
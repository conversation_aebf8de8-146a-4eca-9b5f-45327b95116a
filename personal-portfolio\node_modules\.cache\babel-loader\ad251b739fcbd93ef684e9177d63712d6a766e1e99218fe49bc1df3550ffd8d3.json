{"ast": null, "code": "import { OutMode } from \"../../../../Enums/Modes/OutMode.js\";\nexport class OutModes {\n  constructor() {\n    this.default = OutMode.out;\n  }\n  load(data) {\n    if (!data) {\n      return;\n    }\n    if (data.default !== undefined) {\n      this.default = data.default;\n    }\n    this.bottom = data.bottom ?? data.default;\n    this.left = data.left ?? data.default;\n    this.right = data.right ?? data.default;\n    this.top = data.top ?? data.default;\n  }\n}", "map": {"version": 3, "names": ["OutMode", "OutModes", "constructor", "default", "out", "load", "data", "undefined", "bottom", "left", "right", "top"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/engine/browser/Options/Classes/Particles/Move/OutModes.js"], "sourcesContent": ["import { OutMode } from \"../../../../Enums/Modes/OutMode.js\";\nexport class OutModes {\n    constructor() {\n        this.default = OutMode.out;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.default !== undefined) {\n            this.default = data.default;\n        }\n        this.bottom = data.bottom ?? data.default;\n        this.left = data.left ?? data.default;\n        this.right = data.right ?? data.default;\n        this.top = data.top ?? data.default;\n    }\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,oCAAoC;AAC5D,OAAO,MAAMC,QAAQ,CAAC;EAClBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,OAAO,GAAGH,OAAO,CAACI,GAAG;EAC9B;EACAC,IAAIA,CAACC,IAAI,EAAE;IACP,IAAI,CAACA,IAAI,EAAE;MACP;IACJ;IACA,IAAIA,IAAI,CAACH,OAAO,KAAKI,SAAS,EAAE;MAC5B,IAAI,CAACJ,OAAO,GAAGG,IAAI,CAACH,OAAO;IAC/B;IACA,IAAI,CAACK,MAAM,GAAGF,IAAI,CAACE,MAAM,IAAIF,IAAI,CAACH,OAAO;IACzC,IAAI,CAACM,IAAI,GAAGH,IAAI,CAACG,IAAI,IAAIH,IAAI,CAACH,OAAO;IACrC,IAAI,CAACO,KAAK,GAAGJ,IAAI,CAACI,KAAK,IAAIJ,IAAI,CAACH,OAAO;IACvC,IAAI,CAACQ,GAAG,GAAGL,IAAI,CAACK,GAAG,IAAIL,IAAI,CAACH,OAAO;EACvC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
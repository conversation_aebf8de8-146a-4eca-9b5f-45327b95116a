{"ast": null, "code": "export class CircleDrawer {\n  getSidesCount() {\n    return 12;\n  }\n\n  draw(context, particle, radius) {\n    context.arc(0, 0, radius, 0, Math.PI * 2, false);\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Shapes/Circle/CircleDrawer.js"], "names": ["CircleDrawer", "getSidesCount", "draw", "context", "particle", "radius", "arc", "Math", "PI"], "mappings": "AAAA,OAAO,MAAMA,YAAN,CAAmB;AACtBC,EAAAA,aAAa,GAAG;AACZ,WAAO,EAAP;AACH;;AACDC,EAAAA,IAAI,CAACC,OAAD,EAAUC,QAAV,EAAoBC,MAApB,EAA4B;AAC5BF,IAAAA,OAAO,CAACG,GAAR,CAAY,CAAZ,EAAe,CAAf,EAAkBD,MAAlB,EAA0B,CAA1B,EAA6BE,IAAI,CAACC,EAAL,GAAU,CAAvC,EAA0C,KAA1C;AACH;;AANqB", "sourcesContent": ["export class CircleDrawer {\n    getSidesCount() {\n        return 12;\n    }\n    draw(context, particle, radius) {\n        context.arc(0, 0, radius, 0, Math.PI * 2, false);\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
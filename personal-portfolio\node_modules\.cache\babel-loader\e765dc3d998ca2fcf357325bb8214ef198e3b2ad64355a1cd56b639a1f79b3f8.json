{"ast": null, "code": "import { OptionsColor, setRangeValue } from \"@tsparticles/engine\";\nimport { AbsorberSize } from \"./AbsorberSize.js\";\nexport class Absorber {\n  constructor() {\n    this.color = new OptionsColor();\n    this.color.value = \"#000000\";\n    this.draggable = false;\n    this.opacity = 1;\n    this.destroy = true;\n    this.orbits = false;\n    this.size = new AbsorberSize();\n  }\n  load(data) {\n    if (data === undefined) {\n      return;\n    }\n    if (data.color !== undefined) {\n      this.color = OptionsColor.create(this.color, data.color);\n    }\n    if (data.draggable !== undefined) {\n      this.draggable = data.draggable;\n    }\n    this.name = data.name;\n    if (data.opacity !== undefined) {\n      this.opacity = data.opacity;\n    }\n    if (data.position !== undefined) {\n      this.position = {};\n      if (data.position.x !== undefined) {\n        this.position.x = setRangeValue(data.position.x);\n      }\n      if (data.position.y !== undefined) {\n        this.position.y = setRangeValue(data.position.y);\n      }\n    }\n    if (data.size !== undefined) {\n      this.size.load(data.size);\n    }\n    if (data.destroy !== undefined) {\n      this.destroy = data.destroy;\n    }\n    if (data.orbits !== undefined) {\n      this.orbits = data.orbits;\n    }\n  }\n}", "map": {"version": 3, "names": ["OptionsColor", "setRangeValue", "AbsorberSize", "Absorber", "constructor", "color", "value", "draggable", "opacity", "destroy", "orbits", "size", "load", "data", "undefined", "create", "name", "position", "x", "y"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/plugin-absorbers/browser/Options/Classes/Absorber.js"], "sourcesContent": ["import { OptionsColor, setRangeValue, } from \"@tsparticles/engine\";\nimport { AbsorberSize } from \"./AbsorberSize.js\";\nexport class Absorber {\n    constructor() {\n        this.color = new OptionsColor();\n        this.color.value = \"#000000\";\n        this.draggable = false;\n        this.opacity = 1;\n        this.destroy = true;\n        this.orbits = false;\n        this.size = new AbsorberSize();\n    }\n    load(data) {\n        if (data === undefined) {\n            return;\n        }\n        if (data.color !== undefined) {\n            this.color = OptionsColor.create(this.color, data.color);\n        }\n        if (data.draggable !== undefined) {\n            this.draggable = data.draggable;\n        }\n        this.name = data.name;\n        if (data.opacity !== undefined) {\n            this.opacity = data.opacity;\n        }\n        if (data.position !== undefined) {\n            this.position = {};\n            if (data.position.x !== undefined) {\n                this.position.x = setRangeValue(data.position.x);\n            }\n            if (data.position.y !== undefined) {\n                this.position.y = setRangeValue(data.position.y);\n            }\n        }\n        if (data.size !== undefined) {\n            this.size.load(data.size);\n        }\n        if (data.destroy !== undefined) {\n            this.destroy = data.destroy;\n        }\n        if (data.orbits !== undefined) {\n            this.orbits = data.orbits;\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,YAAY,EAAEC,aAAa,QAAS,qBAAqB;AAClE,SAASC,YAAY,QAAQ,mBAAmB;AAChD,OAAO,MAAMC,QAAQ,CAAC;EAClBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,KAAK,GAAG,IAAIL,YAAY,CAAC,CAAC;IAC/B,IAAI,CAACK,KAAK,CAACC,KAAK,GAAG,SAAS;IAC5B,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,OAAO,GAAG,CAAC;IAChB,IAAI,CAACC,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,MAAM,GAAG,KAAK;IACnB,IAAI,CAACC,IAAI,GAAG,IAAIT,YAAY,CAAC,CAAC;EAClC;EACAU,IAAIA,CAACC,IAAI,EAAE;IACP,IAAIA,IAAI,KAAKC,SAAS,EAAE;MACpB;IACJ;IACA,IAAID,IAAI,CAACR,KAAK,KAAKS,SAAS,EAAE;MAC1B,IAAI,CAACT,KAAK,GAAGL,YAAY,CAACe,MAAM,CAAC,IAAI,CAACV,KAAK,EAAEQ,IAAI,CAACR,KAAK,CAAC;IAC5D;IACA,IAAIQ,IAAI,CAACN,SAAS,KAAKO,SAAS,EAAE;MAC9B,IAAI,CAACP,SAAS,GAAGM,IAAI,CAACN,SAAS;IACnC;IACA,IAAI,CAACS,IAAI,GAAGH,IAAI,CAACG,IAAI;IACrB,IAAIH,IAAI,CAACL,OAAO,KAAKM,SAAS,EAAE;MAC5B,IAAI,CAACN,OAAO,GAAGK,IAAI,CAACL,OAAO;IAC/B;IACA,IAAIK,IAAI,CAACI,QAAQ,KAAKH,SAAS,EAAE;MAC7B,IAAI,CAACG,QAAQ,GAAG,CAAC,CAAC;MAClB,IAAIJ,IAAI,CAACI,QAAQ,CAACC,CAAC,KAAKJ,SAAS,EAAE;QAC/B,IAAI,CAACG,QAAQ,CAACC,CAAC,GAAGjB,aAAa,CAACY,IAAI,CAACI,QAAQ,CAACC,CAAC,CAAC;MACpD;MACA,IAAIL,IAAI,CAACI,QAAQ,CAACE,CAAC,KAAKL,SAAS,EAAE;QAC/B,IAAI,CAACG,QAAQ,CAACE,CAAC,GAAGlB,aAAa,CAACY,IAAI,CAACI,QAAQ,CAACE,CAAC,CAAC;MACpD;IACJ;IACA,IAAIN,IAAI,CAACF,IAAI,KAAKG,SAAS,EAAE;MACzB,IAAI,CAACH,IAAI,CAACC,IAAI,CAACC,IAAI,CAACF,IAAI,CAAC;IAC7B;IACA,IAAIE,IAAI,CAACJ,OAAO,KAAKK,SAAS,EAAE;MAC5B,IAAI,CAACL,OAAO,GAAGI,IAAI,CAACJ,OAAO;IAC/B;IACA,IAAII,IAAI,CAACH,MAAM,KAAKI,SAAS,EAAE;MAC3B,IAAI,CAACJ,MAAM,GAAGG,IAAI,CAACH,MAAM;IAC7B;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
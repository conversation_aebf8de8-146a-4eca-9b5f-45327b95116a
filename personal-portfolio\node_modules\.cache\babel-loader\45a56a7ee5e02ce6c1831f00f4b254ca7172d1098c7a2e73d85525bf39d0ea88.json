{"ast": null, "code": "import { AnimationStatus, DestroyType, RotateDirection, degToRad, getRandom, getRangeValue, updateAnimation } from \"@tsparticles/engine\";\nimport { Rotate } from \"./Options/Classes/Rotate.js\";\nconst double = 2,\n  doublePI = Math.PI * double,\n  identity = 1,\n  doublePIDeg = 360;\nexport class RotateUpdater {\n  constructor(container) {\n    this.container = container;\n  }\n  init(particle) {\n    const rotateOptions = particle.options.rotate;\n    if (!rotateOptions) {\n      return;\n    }\n    particle.rotate = {\n      enable: rotateOptions.animation.enable,\n      value: degToRad(getRangeValue(rotateOptions.value)),\n      min: 0,\n      max: doublePI\n    };\n    particle.pathRotation = rotateOptions.path;\n    let rotateDirection = rotateOptions.direction;\n    if (rotateDirection === RotateDirection.random) {\n      const index = Math.floor(getRandom() * double),\n        minIndex = 0;\n      rotateDirection = index > minIndex ? RotateDirection.counterClockwise : RotateDirection.clockwise;\n    }\n    switch (rotateDirection) {\n      case RotateDirection.counterClockwise:\n      case \"counterClockwise\":\n        particle.rotate.status = AnimationStatus.decreasing;\n        break;\n      case RotateDirection.clockwise:\n        particle.rotate.status = AnimationStatus.increasing;\n        break;\n    }\n    const rotateAnimation = rotateOptions.animation;\n    if (rotateAnimation.enable) {\n      particle.rotate.decay = identity - getRangeValue(rotateAnimation.decay);\n      particle.rotate.velocity = getRangeValue(rotateAnimation.speed) / doublePIDeg * this.container.retina.reduceFactor;\n      if (!rotateAnimation.sync) {\n        particle.rotate.velocity *= getRandom();\n      }\n    }\n    particle.rotation = particle.rotate.value;\n  }\n  isEnabled(particle) {\n    const rotate = particle.options.rotate;\n    if (!rotate) {\n      return false;\n    }\n    return !particle.destroyed && !particle.spawning && (!!rotate.value || rotate.animation.enable || rotate.path);\n  }\n  loadOptions(options, ...sources) {\n    if (!options.rotate) {\n      options.rotate = new Rotate();\n    }\n    for (const source of sources) {\n      options.rotate.load(source?.rotate);\n    }\n  }\n  update(particle, delta) {\n    if (!this.isEnabled(particle)) {\n      return;\n    }\n    particle.isRotating = !!particle.rotate;\n    if (!particle.rotate) {\n      return;\n    }\n    updateAnimation(particle, particle.rotate, false, DestroyType.none, delta);\n    particle.rotation = particle.rotate.value;\n  }\n}", "map": {"version": 3, "names": ["AnimationStatus", "DestroyType", "RotateDirection", "degToRad", "getRandom", "getRangeValue", "updateAnimation", "Rotate", "double", "doublePI", "Math", "PI", "identity", "doublePIDeg", "RotateUpdater", "constructor", "container", "init", "particle", "rotateOptions", "options", "rotate", "enable", "animation", "value", "min", "max", "pathRotation", "path", "rotateDirection", "direction", "random", "index", "floor", "minIndex", "counterClockwise", "clockwise", "status", "decreasing", "increasing", "rotateAnimation", "decay", "velocity", "speed", "retina", "reduceFactor", "sync", "rotation", "isEnabled", "destroyed", "spawning", "loadOptions", "sources", "source", "load", "update", "delta", "isRotating", "none"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/updater-rotate/browser/RotateUpdater.js"], "sourcesContent": ["import { AnimationStatus, DestroyType, RotateDirection, degToRad, getRandom, getRangeValue, updateAnimation, } from \"@tsparticles/engine\";\nimport { Rotate } from \"./Options/Classes/Rotate.js\";\nconst double = 2, doublePI = Math.PI * double, identity = 1, doublePIDeg = 360;\nexport class RotateUpdater {\n    constructor(container) {\n        this.container = container;\n    }\n    init(particle) {\n        const rotateOptions = particle.options.rotate;\n        if (!rotateOptions) {\n            return;\n        }\n        particle.rotate = {\n            enable: rotateOptions.animation.enable,\n            value: degToRad(getRangeValue(rotateOptions.value)),\n            min: 0,\n            max: doublePI,\n        };\n        particle.pathRotation = rotateOptions.path;\n        let rotateDirection = rotateOptions.direction;\n        if (rotateDirection === RotateDirection.random) {\n            const index = Math.floor(getRandom() * double), minIndex = 0;\n            rotateDirection = index > minIndex ? RotateDirection.counterClockwise : RotateDirection.clockwise;\n        }\n        switch (rotateDirection) {\n            case RotateDirection.counterClockwise:\n            case \"counterClockwise\":\n                particle.rotate.status = AnimationStatus.decreasing;\n                break;\n            case RotateDirection.clockwise:\n                particle.rotate.status = AnimationStatus.increasing;\n                break;\n        }\n        const rotateAnimation = rotateOptions.animation;\n        if (rotateAnimation.enable) {\n            particle.rotate.decay = identity - getRangeValue(rotateAnimation.decay);\n            particle.rotate.velocity =\n                (getRangeValue(rotateAnimation.speed) / doublePIDeg) * this.container.retina.reduceFactor;\n            if (!rotateAnimation.sync) {\n                particle.rotate.velocity *= getRandom();\n            }\n        }\n        particle.rotation = particle.rotate.value;\n    }\n    isEnabled(particle) {\n        const rotate = particle.options.rotate;\n        if (!rotate) {\n            return false;\n        }\n        return !particle.destroyed && !particle.spawning && (!!rotate.value || rotate.animation.enable || rotate.path);\n    }\n    loadOptions(options, ...sources) {\n        if (!options.rotate) {\n            options.rotate = new Rotate();\n        }\n        for (const source of sources) {\n            options.rotate.load(source?.rotate);\n        }\n    }\n    update(particle, delta) {\n        if (!this.isEnabled(particle)) {\n            return;\n        }\n        particle.isRotating = !!particle.rotate;\n        if (!particle.rotate) {\n            return;\n        }\n        updateAnimation(particle, particle.rotate, false, DestroyType.none, delta);\n        particle.rotation = particle.rotate.value;\n    }\n}\n"], "mappings": "AAAA,SAASA,eAAe,EAAEC,WAAW,EAAEC,eAAe,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,aAAa,EAAEC,eAAe,QAAS,qBAAqB;AACzI,SAASC,MAAM,QAAQ,6BAA6B;AACpD,MAAMC,MAAM,GAAG,CAAC;EAAEC,QAAQ,GAAGC,IAAI,CAACC,EAAE,GAAGH,MAAM;EAAEI,QAAQ,GAAG,CAAC;EAAEC,WAAW,GAAG,GAAG;AAC9E,OAAO,MAAMC,aAAa,CAAC;EACvBC,WAAWA,CAACC,SAAS,EAAE;IACnB,IAAI,CAACA,SAAS,GAAGA,SAAS;EAC9B;EACAC,IAAIA,CAACC,QAAQ,EAAE;IACX,MAAMC,aAAa,GAAGD,QAAQ,CAACE,OAAO,CAACC,MAAM;IAC7C,IAAI,CAACF,aAAa,EAAE;MAChB;IACJ;IACAD,QAAQ,CAACG,MAAM,GAAG;MACdC,MAAM,EAAEH,aAAa,CAACI,SAAS,CAACD,MAAM;MACtCE,KAAK,EAAErB,QAAQ,CAACE,aAAa,CAACc,aAAa,CAACK,KAAK,CAAC,CAAC;MACnDC,GAAG,EAAE,CAAC;MACNC,GAAG,EAAEjB;IACT,CAAC;IACDS,QAAQ,CAACS,YAAY,GAAGR,aAAa,CAACS,IAAI;IAC1C,IAAIC,eAAe,GAAGV,aAAa,CAACW,SAAS;IAC7C,IAAID,eAAe,KAAK3B,eAAe,CAAC6B,MAAM,EAAE;MAC5C,MAAMC,KAAK,GAAGtB,IAAI,CAACuB,KAAK,CAAC7B,SAAS,CAAC,CAAC,GAAGI,MAAM,CAAC;QAAE0B,QAAQ,GAAG,CAAC;MAC5DL,eAAe,GAAGG,KAAK,GAAGE,QAAQ,GAAGhC,eAAe,CAACiC,gBAAgB,GAAGjC,eAAe,CAACkC,SAAS;IACrG;IACA,QAAQP,eAAe;MACnB,KAAK3B,eAAe,CAACiC,gBAAgB;MACrC,KAAK,kBAAkB;QACnBjB,QAAQ,CAACG,MAAM,CAACgB,MAAM,GAAGrC,eAAe,CAACsC,UAAU;QACnD;MACJ,KAAKpC,eAAe,CAACkC,SAAS;QAC1BlB,QAAQ,CAACG,MAAM,CAACgB,MAAM,GAAGrC,eAAe,CAACuC,UAAU;QACnD;IACR;IACA,MAAMC,eAAe,GAAGrB,aAAa,CAACI,SAAS;IAC/C,IAAIiB,eAAe,CAAClB,MAAM,EAAE;MACxBJ,QAAQ,CAACG,MAAM,CAACoB,KAAK,GAAG7B,QAAQ,GAAGP,aAAa,CAACmC,eAAe,CAACC,KAAK,CAAC;MACvEvB,QAAQ,CAACG,MAAM,CAACqB,QAAQ,GACnBrC,aAAa,CAACmC,eAAe,CAACG,KAAK,CAAC,GAAG9B,WAAW,GAAI,IAAI,CAACG,SAAS,CAAC4B,MAAM,CAACC,YAAY;MAC7F,IAAI,CAACL,eAAe,CAACM,IAAI,EAAE;QACvB5B,QAAQ,CAACG,MAAM,CAACqB,QAAQ,IAAItC,SAAS,CAAC,CAAC;MAC3C;IACJ;IACAc,QAAQ,CAAC6B,QAAQ,GAAG7B,QAAQ,CAACG,MAAM,CAACG,KAAK;EAC7C;EACAwB,SAASA,CAAC9B,QAAQ,EAAE;IAChB,MAAMG,MAAM,GAAGH,QAAQ,CAACE,OAAO,CAACC,MAAM;IACtC,IAAI,CAACA,MAAM,EAAE;MACT,OAAO,KAAK;IAChB;IACA,OAAO,CAACH,QAAQ,CAAC+B,SAAS,IAAI,CAAC/B,QAAQ,CAACgC,QAAQ,KAAK,CAAC,CAAC7B,MAAM,CAACG,KAAK,IAAIH,MAAM,CAACE,SAAS,CAACD,MAAM,IAAID,MAAM,CAACO,IAAI,CAAC;EAClH;EACAuB,WAAWA,CAAC/B,OAAO,EAAE,GAAGgC,OAAO,EAAE;IAC7B,IAAI,CAAChC,OAAO,CAACC,MAAM,EAAE;MACjBD,OAAO,CAACC,MAAM,GAAG,IAAId,MAAM,CAAC,CAAC;IACjC;IACA,KAAK,MAAM8C,MAAM,IAAID,OAAO,EAAE;MAC1BhC,OAAO,CAACC,MAAM,CAACiC,IAAI,CAACD,MAAM,EAAEhC,MAAM,CAAC;IACvC;EACJ;EACAkC,MAAMA,CAACrC,QAAQ,EAAEsC,KAAK,EAAE;IACpB,IAAI,CAAC,IAAI,CAACR,SAAS,CAAC9B,QAAQ,CAAC,EAAE;MAC3B;IACJ;IACAA,QAAQ,CAACuC,UAAU,GAAG,CAAC,CAACvC,QAAQ,CAACG,MAAM;IACvC,IAAI,CAACH,QAAQ,CAACG,MAAM,EAAE;MAClB;IACJ;IACAf,eAAe,CAACY,QAAQ,EAAEA,QAAQ,CAACG,MAAM,EAAE,KAAK,EAAEpB,WAAW,CAACyD,IAAI,EAAEF,KAAK,CAAC;IAC1EtC,QAAQ,CAAC6B,QAAQ,GAAG7B,QAAQ,CAACG,MAAM,CAACG,KAAK;EAC7C;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "export class EmitterShapeBase {\n  constructor(position, size, fill, options) {\n    this.position = position;\n    this.size = size;\n    this.fill = fill;\n    this.options = options;\n  }\n  resize(position, size) {\n    this.position = position;\n    this.size = size;\n  }\n}", "map": {"version": 3, "names": ["EmitterShapeBase", "constructor", "position", "size", "fill", "options", "resize"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/plugin-emitters/browser/EmitterShapeBase.js"], "sourcesContent": ["export class EmitterShapeBase {\n    constructor(position, size, fill, options) {\n        this.position = position;\n        this.size = size;\n        this.fill = fill;\n        this.options = options;\n    }\n    resize(position, size) {\n        this.position = position;\n        this.size = size;\n    }\n}\n"], "mappings": "AAAA,OAAO,MAAMA,gBAAgB,CAAC;EAC1BC,WAAWA,CAACC,QAAQ,EAAEC,IAAI,EAAEC,IAAI,EAAEC,OAAO,EAAE;IACvC,IAAI,CAACH,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,OAAO,GAAGA,OAAO;EAC1B;EACAC,MAAMA,CAACJ,QAAQ,EAAEC,IAAI,EAAE;IACnB,IAAI,CAACD,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,IAAI,GAAGA,IAAI;EACpB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
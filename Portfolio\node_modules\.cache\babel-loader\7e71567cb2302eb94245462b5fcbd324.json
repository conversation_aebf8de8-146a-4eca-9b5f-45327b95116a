{"ast": null, "code": "import { PathDelay } from \"./PathDelay\";\nimport { deepExtend } from \"../../../../../Utils\";\nexport class Path {\n  constructor() {\n    this.clamp = true;\n    this.delay = new PathDelay();\n    this.enable = false;\n    this.options = {};\n  }\n\n  load(data) {\n    if (data === undefined) {\n      return;\n    }\n\n    if (data.clamp !== undefined) {\n      this.clamp = data.clamp;\n    }\n\n    this.delay.load(data.delay);\n\n    if (data.enable !== undefined) {\n      this.enable = data.enable;\n    }\n\n    this.generator = data.generator;\n\n    if (data.options) {\n      this.options = deepExtend(this.options, data.options);\n    }\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Options/Classes/Particles/Move/Path/Path.js"], "names": ["PathDelay", "deepExtend", "Path", "constructor", "clamp", "delay", "enable", "options", "load", "data", "undefined", "generator"], "mappings": "AAAA,SAASA,SAAT,QAA0B,aAA1B;AACA,SAASC,UAAT,QAA2B,sBAA3B;AACA,OAAO,MAAMC,IAAN,CAAW;AACdC,EAAAA,WAAW,GAAG;AACV,SAAKC,KAAL,GAAa,IAAb;AACA,SAAKC,KAAL,GAAa,IAAIL,SAAJ,EAAb;AACA,SAAKM,MAAL,GAAc,KAAd;AACA,SAAKC,OAAL,GAAe,EAAf;AACH;;AACDC,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,QAAIA,IAAI,KAAKC,SAAb,EAAwB;AACpB;AACH;;AACD,QAAID,IAAI,CAACL,KAAL,KAAeM,SAAnB,EAA8B;AAC1B,WAAKN,KAAL,GAAaK,IAAI,CAACL,KAAlB;AACH;;AACD,SAAKC,KAAL,CAAWG,IAAX,CAAgBC,IAAI,CAACJ,KAArB;;AACA,QAAII,IAAI,CAACH,MAAL,KAAgBI,SAApB,EAA+B;AAC3B,WAAKJ,MAAL,GAAcG,IAAI,CAACH,MAAnB;AACH;;AACD,SAAKK,SAAL,GAAiBF,IAAI,CAACE,SAAtB;;AACA,QAAIF,IAAI,CAACF,OAAT,EAAkB;AACd,WAAKA,OAAL,GAAeN,UAAU,CAAC,KAAKM,OAAN,EAAeE,IAAI,CAACF,OAApB,CAAzB;AACH;AACJ;;AAtBa", "sourcesContent": ["import { PathDelay } from \"./PathDelay\";\nimport { deepExtend } from \"../../../../../Utils\";\nexport class Path {\n    constructor() {\n        this.clamp = true;\n        this.delay = new PathDelay();\n        this.enable = false;\n        this.options = {};\n    }\n    load(data) {\n        if (data === undefined) {\n            return;\n        }\n        if (data.clamp !== undefined) {\n            this.clamp = data.clamp;\n        }\n        this.delay.load(data.delay);\n        if (data.enable !== undefined) {\n            this.enable = data.enable;\n        }\n        this.generator = data.generator;\n        if (data.options) {\n            this.options = deepExtend(this.options, data.options);\n        }\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
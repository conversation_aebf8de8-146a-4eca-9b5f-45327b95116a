{"ast": null, "code": "export class Modes {\n  constructor(engine, container) {\n    this._engine = engine;\n    this._container = container;\n  }\n  load(data) {\n    if (!data) {\n      return;\n    }\n    if (!this._container) {\n      return;\n    }\n    const interactors = this._engine.interactors.get(this._container);\n    if (!interactors) {\n      return;\n    }\n    for (const interactor of interactors) {\n      if (!interactor.loadModeOptions) {\n        continue;\n      }\n      interactor.loadModeOptions(this, data);\n    }\n  }\n}", "map": {"version": 3, "names": ["Modes", "constructor", "engine", "container", "_engine", "_container", "load", "data", "interactors", "get", "interactor", "loadModeOptions"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/engine/browser/Options/Classes/Interactivity/Modes/Modes.js"], "sourcesContent": ["export class Modes {\n    constructor(engine, container) {\n        this._engine = engine;\n        this._container = container;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (!this._container) {\n            return;\n        }\n        const interactors = this._engine.interactors.get(this._container);\n        if (!interactors) {\n            return;\n        }\n        for (const interactor of interactors) {\n            if (!interactor.loadModeOptions) {\n                continue;\n            }\n            interactor.loadModeOptions(this, data);\n        }\n    }\n}\n"], "mappings": "AAAA,OAAO,MAAMA,KAAK,CAAC;EACfC,WAAWA,CAACC,MAAM,EAAEC,SAAS,EAAE;IAC3B,IAAI,CAACC,OAAO,GAAGF,MAAM;IACrB,IAAI,CAACG,UAAU,GAAGF,SAAS;EAC/B;EACAG,IAAIA,CAACC,IAAI,EAAE;IACP,IAAI,CAACA,IAAI,EAAE;MACP;IACJ;IACA,IAAI,CAAC,IAAI,CAACF,UAAU,EAAE;MAClB;IACJ;IACA,MAAMG,WAAW,GAAG,IAAI,CAACJ,OAAO,CAACI,WAAW,CAACC,GAAG,CAAC,IAAI,CAACJ,UAAU,CAAC;IACjE,IAAI,CAACG,WAAW,EAAE;MACd;IACJ;IACA,KAAK,MAAME,UAAU,IAAIF,WAAW,EAAE;MAClC,IAAI,CAACE,UAAU,CAACC,eAAe,EAAE;QAC7B;MACJ;MACAD,UAAU,CAACC,eAAe,CAAC,IAAI,EAAEJ,IAAI,CAAC;IAC1C;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
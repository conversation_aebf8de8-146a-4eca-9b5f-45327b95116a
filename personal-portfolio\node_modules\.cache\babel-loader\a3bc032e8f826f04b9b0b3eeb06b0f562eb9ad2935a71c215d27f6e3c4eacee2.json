{"ast": null, "code": "import { setRangeValue } from \"@tsparticles/engine\";\nexport class RollLight {\n  constructor() {\n    this.enable = false;\n    this.value = 0;\n  }\n  load(data) {\n    if (!data) {\n      return;\n    }\n    if (data.enable !== undefined) {\n      this.enable = data.enable;\n    }\n    if (data.value !== undefined) {\n      this.value = setRangeValue(data.value);\n    }\n  }\n}", "map": {"version": 3, "names": ["setRangeValue", "RollLight", "constructor", "enable", "value", "load", "data", "undefined"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/updater-roll/browser/Options/Classes/RollLight.js"], "sourcesContent": ["import { setRangeValue } from \"@tsparticles/engine\";\nexport class RollLight {\n    constructor() {\n        this.enable = false;\n        this.value = 0;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.enable !== undefined) {\n            this.enable = data.enable;\n        }\n        if (data.value !== undefined) {\n            this.value = setRangeValue(data.value);\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,qBAAqB;AACnD,OAAO,MAAMC,SAAS,CAAC;EACnBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,MAAM,GAAG,KAAK;IACnB,IAAI,CAACC,KAAK,GAAG,CAAC;EAClB;EACAC,IAAIA,CAACC,IAAI,EAAE;IACP,IAAI,CAACA,IAAI,EAAE;MACP;IACJ;IACA,IAAIA,IAAI,CAACH,MAAM,KAAKI,SAAS,EAAE;MAC3B,IAAI,CAACJ,MAAM,GAAGG,IAAI,CAACH,MAAM;IAC7B;IACA,IAAIG,IAAI,CAACF,KAAK,KAAKG,SAAS,EAAE;MAC1B,IAAI,CAACH,KAAK,GAAGJ,aAAa,CAACM,IAAI,CAACF,KAAK,CAAC;IAC1C;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
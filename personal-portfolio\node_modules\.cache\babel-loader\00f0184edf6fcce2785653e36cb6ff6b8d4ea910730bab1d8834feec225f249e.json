{"ast": null, "code": "import { circleBounce, circleBounceDataFromParticle, getRangeValue } from \"@tsparticles/engine\";\nconst fixBounceSpeed = p => {\n  if (p.collisionMaxSpeed === undefined) {\n    p.collisionMaxSpeed = getRangeValue(p.options.collisions.maxSpeed);\n  }\n  if (p.velocity.length > p.collisionMaxSpeed) {\n    p.velocity.length = p.collisionMaxSpeed;\n  }\n};\nexport function bounce(p1, p2) {\n  circleBounce(circleBounceDataFromParticle(p1), circleBounceDataFromParticle(p2));\n  fixBounceSpeed(p1);\n  fixBounceSpeed(p2);\n}", "map": {"version": 3, "names": ["circleBounce", "circleBounceDataFromParticle", "getRangeValue", "fixBounceSpeed", "p", "collisionMaxSpeed", "undefined", "options", "collisions", "maxSpeed", "velocity", "length", "bounce", "p1", "p2"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/interaction-particles-collisions/browser/Bounce.js"], "sourcesContent": ["import { circleBounce, circleBounceDataFromParticle, getRangeValue } from \"@tsparticles/engine\";\nconst fixBounceSpeed = (p) => {\n    if (p.collisionMaxSpeed === undefined) {\n        p.collisionMaxSpeed = getRangeValue(p.options.collisions.maxSpeed);\n    }\n    if (p.velocity.length > p.collisionMaxSpeed) {\n        p.velocity.length = p.collisionMaxSpeed;\n    }\n};\nexport function bounce(p1, p2) {\n    circleBounce(circleBounceDataFromParticle(p1), circleBounceDataFromParticle(p2));\n    fixBounceSpeed(p1);\n    fixBounceSpeed(p2);\n}\n"], "mappings": "AAAA,SAASA,YAAY,EAAEC,4BAA4B,EAAEC,aAAa,QAAQ,qBAAqB;AAC/F,MAAMC,cAAc,GAAIC,CAAC,IAAK;EAC1B,IAAIA,CAAC,CAACC,iBAAiB,KAAKC,SAAS,EAAE;IACnCF,CAAC,CAACC,iBAAiB,GAAGH,aAAa,CAACE,CAAC,CAACG,OAAO,CAACC,UAAU,CAACC,QAAQ,CAAC;EACtE;EACA,IAAIL,CAAC,CAACM,QAAQ,CAACC,MAAM,GAAGP,CAAC,CAACC,iBAAiB,EAAE;IACzCD,CAAC,CAACM,QAAQ,CAACC,MAAM,GAAGP,CAAC,CAACC,iBAAiB;EAC3C;AACJ,CAAC;AACD,OAAO,SAASO,MAAMA,CAACC,EAAE,EAAEC,EAAE,EAAE;EAC3Bd,YAAY,CAACC,4BAA4B,CAACY,EAAE,CAAC,EAAEZ,4BAA4B,CAACa,EAAE,CAAC,CAAC;EAChFX,cAAc,CAACU,EAAE,CAAC;EAClBV,cAAc,CAACW,EAAE,CAAC;AACtB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
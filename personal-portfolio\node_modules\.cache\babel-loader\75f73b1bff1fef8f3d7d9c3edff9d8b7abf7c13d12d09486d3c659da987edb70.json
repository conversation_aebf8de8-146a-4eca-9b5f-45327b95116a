{"ast": null, "code": "import { OptionsColor, setRangeValue } from \"@tsparticles/engine\";\nimport { RollLight } from \"./RollLight.js\";\nimport { RollMode } from \"../../RollMode.js\";\nexport class Roll {\n  constructor() {\n    this.darken = new RollLight();\n    this.enable = false;\n    this.enlighten = new RollLight();\n    this.mode = RollMode.vertical;\n    this.speed = 25;\n  }\n  load(data) {\n    if (!data) {\n      return;\n    }\n    if (data.backColor !== undefined) {\n      this.backColor = OptionsColor.create(this.backColor, data.backColor);\n    }\n    this.darken.load(data.darken);\n    if (data.enable !== undefined) {\n      this.enable = data.enable;\n    }\n    this.enlighten.load(data.enlighten);\n    if (data.mode !== undefined) {\n      this.mode = data.mode;\n    }\n    if (data.speed !== undefined) {\n      this.speed = setRangeValue(data.speed);\n    }\n  }\n}", "map": {"version": 3, "names": ["OptionsColor", "setRangeValue", "RollLight", "RollMode", "Roll", "constructor", "darken", "enable", "enlighten", "mode", "vertical", "speed", "load", "data", "backColor", "undefined", "create"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/updater-roll/browser/Options/Classes/Roll.js"], "sourcesContent": ["import { OptionsColor, setRangeValue, } from \"@tsparticles/engine\";\nimport { RollLight } from \"./RollLight.js\";\nimport { RollMode } from \"../../RollMode.js\";\nexport class Roll {\n    constructor() {\n        this.darken = new RollLight();\n        this.enable = false;\n        this.enlighten = new RollLight();\n        this.mode = RollMode.vertical;\n        this.speed = 25;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.backColor !== undefined) {\n            this.backColor = OptionsColor.create(this.backColor, data.backColor);\n        }\n        this.darken.load(data.darken);\n        if (data.enable !== undefined) {\n            this.enable = data.enable;\n        }\n        this.enlighten.load(data.enlighten);\n        if (data.mode !== undefined) {\n            this.mode = data.mode;\n        }\n        if (data.speed !== undefined) {\n            this.speed = setRangeValue(data.speed);\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,YAAY,EAAEC,aAAa,QAAS,qBAAqB;AAClE,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,QAAQ,QAAQ,mBAAmB;AAC5C,OAAO,MAAMC,IAAI,CAAC;EACdC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,MAAM,GAAG,IAAIJ,SAAS,CAAC,CAAC;IAC7B,IAAI,CAACK,MAAM,GAAG,KAAK;IACnB,IAAI,CAACC,SAAS,GAAG,IAAIN,SAAS,CAAC,CAAC;IAChC,IAAI,CAACO,IAAI,GAAGN,QAAQ,CAACO,QAAQ;IAC7B,IAAI,CAACC,KAAK,GAAG,EAAE;EACnB;EACAC,IAAIA,CAACC,IAAI,EAAE;IACP,IAAI,CAACA,IAAI,EAAE;MACP;IACJ;IACA,IAAIA,IAAI,CAACC,SAAS,KAAKC,SAAS,EAAE;MAC9B,IAAI,CAACD,SAAS,GAAGd,YAAY,CAACgB,MAAM,CAAC,IAAI,CAACF,SAAS,EAAED,IAAI,CAACC,SAAS,CAAC;IACxE;IACA,IAAI,CAACR,MAAM,CAACM,IAAI,CAACC,IAAI,CAACP,MAAM,CAAC;IAC7B,IAAIO,IAAI,CAACN,MAAM,KAAKQ,SAAS,EAAE;MAC3B,IAAI,CAACR,MAAM,GAAGM,IAAI,CAACN,MAAM;IAC7B;IACA,IAAI,CAACC,SAAS,CAACI,IAAI,CAACC,IAAI,CAACL,SAAS,CAAC;IACnC,IAAIK,IAAI,CAACJ,IAAI,KAAKM,SAAS,EAAE;MACzB,IAAI,CAACN,IAAI,GAAGI,IAAI,CAACJ,IAAI;IACzB;IACA,IAAII,IAAI,CAACF,KAAK,KAAKI,SAAS,EAAE;MAC1B,IAAI,CAACJ,KAAK,GAAGV,aAAa,CAACY,IAAI,CAACF,KAAK,CAAC;IAC1C;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
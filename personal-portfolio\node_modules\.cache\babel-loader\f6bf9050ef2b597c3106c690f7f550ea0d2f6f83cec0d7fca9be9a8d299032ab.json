{"ast": null, "code": "import { TwinkleUpdater } from \"./TwinkleUpdater.js\";\nexport async function loadTwinkleUpdater(engine, refresh = true) {\n  await engine.addParticleUpdater(\"twinkle\", () => {\n    return Promise.resolve(new TwinkleUpdater());\n  }, refresh);\n}", "map": {"version": 3, "names": ["TwinkleUpdater", "loadTwinkleUpdater", "engine", "refresh", "addParticleUpdater", "Promise", "resolve"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/updater-twinkle/browser/index.js"], "sourcesContent": ["import { TwinkleUpdater } from \"./TwinkleUpdater.js\";\nexport async function loadTwinkleUpdater(engine, refresh = true) {\n    await engine.addParticleUpdater(\"twinkle\", () => {\n        return Promise.resolve(new TwinkleUpdater());\n    }, refresh);\n}\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,qBAAqB;AACpD,OAAO,eAAeC,kBAAkBA,CAACC,MAAM,EAAEC,OAAO,GAAG,IAAI,EAAE;EAC7D,MAAMD,MAAM,CAACE,kBAAkB,CAAC,SAAS,EAAE,MAAM;IAC7C,OAAOC,OAAO,CAACC,OAAO,CAAC,IAAIN,cAAc,CAAC,CAAC,CAAC;EAChD,CAAC,EAAEG,OAAO,CAAC;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
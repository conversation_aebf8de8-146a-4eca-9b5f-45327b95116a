{"ast": null, "code": "import { StarDrawer } from \"./StarDrawer\";\nexport async function loadStarShape(engine, refresh = true) {\n  await engine.addShape(\"star\", new StarDrawer(), refresh);\n}", "map": {"version": 3, "names": ["StarDrawer", "loadStarShape", "engine", "refresh", "addShape"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/tsparticles-shape-star/esm/index.js"], "sourcesContent": ["import { StarDrawer } from \"./StarDrawer\";\nexport async function loadStarShape(engine, refresh = true) {\n    await engine.addShape(\"star\", new StarDrawer(), refresh);\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,cAAc;AACzC,OAAO,eAAeC,aAAaA,CAACC,MAAM,EAAEC,OAAO,GAAG,IAAI,EAAE;EACxD,MAAMD,MAAM,CAACE,QAAQ,CAAC,MAAM,EAAE,IAAIJ,UAAU,CAAC,CAAC,EAAEG,OAAO,CAAC;AAC5D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
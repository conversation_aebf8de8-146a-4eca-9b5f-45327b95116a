{"ast": null, "code": "import { colorToRgb, getDistance, getDistances, getRangeValue, getStyleFromRgb, isPointInside } from \"../../Utils\";\nimport { Absorber } from \"./Options/Classes/Absorber\";\nimport { Vector } from \"../../Core\";\nexport class AbsorberInstance {\n  constructor(absorbers, container, options, position) {\n    var _a, _b, _c;\n\n    this.absorbers = absorbers;\n    this.container = container;\n    this.initialPosition = position ? Vector.create(position.x, position.y) : undefined;\n\n    if (options instanceof Absorber) {\n      this.options = options;\n    } else {\n      this.options = new Absorber();\n      this.options.load(options);\n    }\n\n    this.dragging = false;\n    this.name = this.options.name;\n    this.opacity = this.options.opacity;\n    this.size = getRangeValue(this.options.size.value) * container.retina.pixelRatio;\n    this.mass = this.size * this.options.size.density * container.retina.reduceFactor;\n    const limit = this.options.size.limit;\n    this.limit = {\n      radius: limit.radius * container.retina.pixelRatio * container.retina.reduceFactor,\n      mass: limit.mass\n    };\n    this.color = (_a = colorToRgb(this.options.color)) !== null && _a !== void 0 ? _a : {\n      b: 0,\n      g: 0,\n      r: 0\n    };\n    this.position = (_c = (_b = this.initialPosition) === null || _b === void 0 ? void 0 : _b.copy()) !== null && _c !== void 0 ? _c : this.calcPosition();\n  }\n\n  attract(particle) {\n    const container = this.container;\n    const options = this.options;\n\n    if (options.draggable) {\n      const mouse = container.interactivity.mouse;\n\n      if (mouse.clicking && mouse.downPosition) {\n        const mouseDist = getDistance(this.position, mouse.downPosition);\n\n        if (mouseDist <= this.size) {\n          this.dragging = true;\n        }\n      } else {\n        this.dragging = false;\n      }\n\n      if (this.dragging && mouse.position) {\n        this.position.x = mouse.position.x;\n        this.position.y = mouse.position.y;\n      }\n    }\n\n    const pos = particle.getPosition();\n    const {\n      dx,\n      dy,\n      distance\n    } = getDistances(this.position, pos);\n    const v = Vector.create(dx, dy);\n    v.length = this.mass / Math.pow(distance, 2) * container.retina.reduceFactor;\n\n    if (distance < this.size + particle.getRadius()) {\n      const sizeFactor = particle.getRadius() * 0.033 * container.retina.pixelRatio;\n\n      if (this.size > particle.getRadius() && distance < this.size - particle.getRadius() || particle.absorberOrbit !== undefined && particle.absorberOrbit.length < 0) {\n        if (options.destroy) {\n          particle.destroy();\n        } else {\n          particle.needsNewPosition = true;\n          this.updateParticlePosition(particle, v);\n        }\n      } else {\n        if (options.destroy) {\n          particle.size.value -= sizeFactor;\n        }\n\n        this.updateParticlePosition(particle, v);\n      }\n\n      if (this.limit.radius <= 0 || this.size < this.limit.radius) {\n        this.size += sizeFactor;\n      }\n\n      if (this.limit.mass <= 0 || this.mass < this.limit.mass) {\n        this.mass += sizeFactor * this.options.size.density * container.retina.reduceFactor;\n      }\n    } else {\n      this.updateParticlePosition(particle, v);\n    }\n  }\n\n  resize() {\n    const initialPosition = this.initialPosition;\n    this.position = initialPosition && isPointInside(initialPosition, this.container.canvas.size) ? initialPosition : this.calcPosition();\n  }\n\n  draw(context) {\n    context.translate(this.position.x, this.position.y);\n    context.beginPath();\n    context.arc(0, 0, this.size, 0, Math.PI * 2, false);\n    context.closePath();\n    context.fillStyle = getStyleFromRgb(this.color, this.opacity);\n    context.fill();\n  }\n\n  calcPosition() {\n    var _a, _b;\n\n    const container = this.container;\n    const percentPosition = this.options.position;\n    return Vector.create(getRangeValue((_a = percentPosition === null || percentPosition === void 0 ? void 0 : percentPosition.x) !== null && _a !== void 0 ? _a : Math.random() * 100) / 100 * container.canvas.size.width, getRangeValue((_b = percentPosition === null || percentPosition === void 0 ? void 0 : percentPosition.y) !== null && _b !== void 0 ? _b : Math.random() * 100) / 100 * container.canvas.size.height);\n  }\n\n  updateParticlePosition(particle, v) {\n    var _a;\n\n    if (particle.destroyed) {\n      return;\n    }\n\n    const container = this.container;\n    const canvasSize = container.canvas.size;\n\n    if (particle.needsNewPosition) {\n      particle.position.x = Math.floor(Math.random() * canvasSize.width);\n      particle.position.y = Math.floor(Math.random() * canvasSize.height);\n      particle.velocity.setTo(particle.initialVelocity);\n      particle.absorberOrbit = undefined;\n      particle.needsNewPosition = false;\n    }\n\n    if (this.options.orbits) {\n      if (particle.absorberOrbit === undefined) {\n        particle.absorberOrbit = Vector.create(0, 0);\n        particle.absorberOrbit.length = getDistance(particle.getPosition(), this.position);\n        particle.absorberOrbit.angle = Math.random() * Math.PI * 2;\n      }\n\n      if (particle.absorberOrbit.length <= this.size && !this.options.destroy) {\n        const minSize = Math.min(canvasSize.width, canvasSize.height);\n        particle.absorberOrbit.length = minSize * (1 + (Math.random() * 0.2 - 0.1));\n      }\n\n      if (particle.absorberOrbitDirection === undefined) {\n        particle.absorberOrbitDirection = particle.velocity.x >= 0 ? \"clockwise\" : \"counter-clockwise\";\n      }\n\n      const orbitRadius = particle.absorberOrbit.length;\n      const orbitAngle = particle.absorberOrbit.angle;\n      const orbitDirection = particle.absorberOrbitDirection;\n      particle.velocity.x = 0;\n      particle.velocity.y = 0;\n      const updateFunc = {\n        x: orbitDirection === \"clockwise\" ? Math.cos : Math.sin,\n        y: orbitDirection === \"clockwise\" ? Math.sin : Math.cos\n      };\n      particle.position.x = this.position.x + orbitRadius * updateFunc.x(orbitAngle);\n      particle.position.y = this.position.y + orbitRadius * updateFunc.y(orbitAngle);\n      particle.absorberOrbit.length -= v.length;\n      particle.absorberOrbit.angle += ((_a = particle.retina.moveSpeed) !== null && _a !== void 0 ? _a : 0) * container.retina.pixelRatio / 100 * container.retina.reduceFactor;\n    } else {\n      const addV = Vector.origin;\n      addV.length = v.length;\n      addV.angle = v.angle;\n      particle.velocity.addTo(addV);\n    }\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Plugins/Absorbers/AbsorberInstance.js"], "names": ["colorToRgb", "getDistance", "getDistances", "getRangeValue", "getStyleFromRgb", "isPointInside", "Absorber", "Vector", "AbsorberInstance", "constructor", "absorbers", "container", "options", "position", "_a", "_b", "_c", "initialPosition", "create", "x", "y", "undefined", "load", "dragging", "name", "opacity", "size", "value", "retina", "pixelRatio", "mass", "density", "reduceFactor", "limit", "radius", "color", "b", "g", "r", "copy", "calcPosition", "attract", "particle", "draggable", "mouse", "interactivity", "clicking", "downPosition", "mouseDist", "pos", "getPosition", "dx", "dy", "distance", "v", "length", "Math", "pow", "getRadius", "sizeFactor", "absorberOrbit", "destroy", "needsNewPosition", "updateParticlePosition", "resize", "canvas", "draw", "context", "translate", "beginPath", "arc", "PI", "closePath", "fillStyle", "fill", "percentPosition", "random", "width", "height", "destroyed", "canvasSize", "floor", "velocity", "setTo", "initialVelocity", "orbits", "angle", "minSize", "min", "absorberOrbitDirection", "orbitRadius", "orbitAngle", "orbitDirection", "updateFunc", "cos", "sin", "moveSpeed", "addV", "origin", "addTo"], "mappings": "AAAA,SAASA,UAAT,EAAqBC,WAArB,EAAkCC,YAAlC,EAAgDC,aAAhD,EAA+DC,eAA/D,EAAgFC,aAAhF,QAAqG,aAArG;AACA,SAASC,QAAT,QAAyB,4BAAzB;AACA,SAASC,MAAT,QAAuB,YAAvB;AACA,OAAO,MAAMC,gBAAN,CAAuB;AAC1BC,EAAAA,WAAW,CAACC,SAAD,EAAYC,SAAZ,EAAuBC,OAAvB,EAAgCC,QAAhC,EAA0C;AACjD,QAAIC,EAAJ,EAAQC,EAAR,EAAYC,EAAZ;;AACA,SAAKN,SAAL,GAAiBA,SAAjB;AACA,SAAKC,SAAL,GAAiBA,SAAjB;AACA,SAAKM,eAAL,GAAuBJ,QAAQ,GAAGN,MAAM,CAACW,MAAP,CAAcL,QAAQ,CAACM,CAAvB,EAA0BN,QAAQ,CAACO,CAAnC,CAAH,GAA2CC,SAA1E;;AACA,QAAIT,OAAO,YAAYN,QAAvB,EAAiC;AAC7B,WAAKM,OAAL,GAAeA,OAAf;AACH,KAFD,MAGK;AACD,WAAKA,OAAL,GAAe,IAAIN,QAAJ,EAAf;AACA,WAAKM,OAAL,CAAaU,IAAb,CAAkBV,OAAlB;AACH;;AACD,SAAKW,QAAL,GAAgB,KAAhB;AACA,SAAKC,IAAL,GAAY,KAAKZ,OAAL,CAAaY,IAAzB;AACA,SAAKC,OAAL,GAAe,KAAKb,OAAL,CAAaa,OAA5B;AACA,SAAKC,IAAL,GAAYvB,aAAa,CAAC,KAAKS,OAAL,CAAac,IAAb,CAAkBC,KAAnB,CAAb,GAAyChB,SAAS,CAACiB,MAAV,CAAiBC,UAAtE;AACA,SAAKC,IAAL,GAAY,KAAKJ,IAAL,GAAY,KAAKd,OAAL,CAAac,IAAb,CAAkBK,OAA9B,GAAwCpB,SAAS,CAACiB,MAAV,CAAiBI,YAArE;AACA,UAAMC,KAAK,GAAG,KAAKrB,OAAL,CAAac,IAAb,CAAkBO,KAAhC;AACA,SAAKA,KAAL,GAAa;AACTC,MAAAA,MAAM,EAAED,KAAK,CAACC,MAAN,GAAevB,SAAS,CAACiB,MAAV,CAAiBC,UAAhC,GAA6ClB,SAAS,CAACiB,MAAV,CAAiBI,YAD7D;AAETF,MAAAA,IAAI,EAAEG,KAAK,CAACH;AAFH,KAAb;AAIA,SAAKK,KAAL,GAAa,CAACrB,EAAE,GAAGd,UAAU,CAAC,KAAKY,OAAL,CAAauB,KAAd,CAAhB,MAA0C,IAA1C,IAAkDrB,EAAE,KAAK,KAAK,CAA9D,GAAkEA,EAAlE,GAAuE;AAChFsB,MAAAA,CAAC,EAAE,CAD6E;AAEhFC,MAAAA,CAAC,EAAE,CAF6E;AAGhFC,MAAAA,CAAC,EAAE;AAH6E,KAApF;AAKA,SAAKzB,QAAL,GAAgB,CAACG,EAAE,GAAG,CAACD,EAAE,GAAG,KAAKE,eAAX,MAAgC,IAAhC,IAAwCF,EAAE,KAAK,KAAK,CAApD,GAAwD,KAAK,CAA7D,GAAiEA,EAAE,CAACwB,IAAH,EAAvE,MAAsF,IAAtF,IAA8FvB,EAAE,KAAK,KAAK,CAA1G,GAA8GA,EAA9G,GAAmH,KAAKwB,YAAL,EAAnI;AACH;;AACDC,EAAAA,OAAO,CAACC,QAAD,EAAW;AACd,UAAM/B,SAAS,GAAG,KAAKA,SAAvB;AACA,UAAMC,OAAO,GAAG,KAAKA,OAArB;;AACA,QAAIA,OAAO,CAAC+B,SAAZ,EAAuB;AACnB,YAAMC,KAAK,GAAGjC,SAAS,CAACkC,aAAV,CAAwBD,KAAtC;;AACA,UAAIA,KAAK,CAACE,QAAN,IAAkBF,KAAK,CAACG,YAA5B,EAA0C;AACtC,cAAMC,SAAS,GAAG/C,WAAW,CAAC,KAAKY,QAAN,EAAgB+B,KAAK,CAACG,YAAtB,CAA7B;;AACA,YAAIC,SAAS,IAAI,KAAKtB,IAAtB,EAA4B;AACxB,eAAKH,QAAL,GAAgB,IAAhB;AACH;AACJ,OALD,MAMK;AACD,aAAKA,QAAL,GAAgB,KAAhB;AACH;;AACD,UAAI,KAAKA,QAAL,IAAiBqB,KAAK,CAAC/B,QAA3B,EAAqC;AACjC,aAAKA,QAAL,CAAcM,CAAd,GAAkByB,KAAK,CAAC/B,QAAN,CAAeM,CAAjC;AACA,aAAKN,QAAL,CAAcO,CAAd,GAAkBwB,KAAK,CAAC/B,QAAN,CAAeO,CAAjC;AACH;AACJ;;AACD,UAAM6B,GAAG,GAAGP,QAAQ,CAACQ,WAAT,EAAZ;AACA,UAAM;AAAEC,MAAAA,EAAF;AAAMC,MAAAA,EAAN;AAAUC,MAAAA;AAAV,QAAuBnD,YAAY,CAAC,KAAKW,QAAN,EAAgBoC,GAAhB,CAAzC;AACA,UAAMK,CAAC,GAAG/C,MAAM,CAACW,MAAP,CAAciC,EAAd,EAAkBC,EAAlB,CAAV;AACAE,IAAAA,CAAC,CAACC,MAAF,GAAY,KAAKzB,IAAL,GAAY0B,IAAI,CAACC,GAAL,CAASJ,QAAT,EAAmB,CAAnB,CAAb,GAAsC1C,SAAS,CAACiB,MAAV,CAAiBI,YAAlE;;AACA,QAAIqB,QAAQ,GAAG,KAAK3B,IAAL,GAAYgB,QAAQ,CAACgB,SAAT,EAA3B,EAAiD;AAC7C,YAAMC,UAAU,GAAGjB,QAAQ,CAACgB,SAAT,KAAuB,KAAvB,GAA+B/C,SAAS,CAACiB,MAAV,CAAiBC,UAAnE;;AACA,UAAK,KAAKH,IAAL,GAAYgB,QAAQ,CAACgB,SAAT,EAAZ,IAAoCL,QAAQ,GAAG,KAAK3B,IAAL,GAAYgB,QAAQ,CAACgB,SAAT,EAA5D,IACChB,QAAQ,CAACkB,aAAT,KAA2BvC,SAA3B,IAAwCqB,QAAQ,CAACkB,aAAT,CAAuBL,MAAvB,GAAgC,CAD7E,EACiF;AAC7E,YAAI3C,OAAO,CAACiD,OAAZ,EAAqB;AACjBnB,UAAAA,QAAQ,CAACmB,OAAT;AACH,SAFD,MAGK;AACDnB,UAAAA,QAAQ,CAACoB,gBAAT,GAA4B,IAA5B;AACA,eAAKC,sBAAL,CAA4BrB,QAA5B,EAAsCY,CAAtC;AACH;AACJ,OATD,MAUK;AACD,YAAI1C,OAAO,CAACiD,OAAZ,EAAqB;AACjBnB,UAAAA,QAAQ,CAAChB,IAAT,CAAcC,KAAd,IAAuBgC,UAAvB;AACH;;AACD,aAAKI,sBAAL,CAA4BrB,QAA5B,EAAsCY,CAAtC;AACH;;AACD,UAAI,KAAKrB,KAAL,CAAWC,MAAX,IAAqB,CAArB,IAA0B,KAAKR,IAAL,GAAY,KAAKO,KAAL,CAAWC,MAArD,EAA6D;AACzD,aAAKR,IAAL,IAAaiC,UAAb;AACH;;AACD,UAAI,KAAK1B,KAAL,CAAWH,IAAX,IAAmB,CAAnB,IAAwB,KAAKA,IAAL,GAAY,KAAKG,KAAL,CAAWH,IAAnD,EAAyD;AACrD,aAAKA,IAAL,IAAa6B,UAAU,GAAG,KAAK/C,OAAL,CAAac,IAAb,CAAkBK,OAA/B,GAAyCpB,SAAS,CAACiB,MAAV,CAAiBI,YAAvE;AACH;AACJ,KAxBD,MAyBK;AACD,WAAK+B,sBAAL,CAA4BrB,QAA5B,EAAsCY,CAAtC;AACH;AACJ;;AACDU,EAAAA,MAAM,GAAG;AACL,UAAM/C,eAAe,GAAG,KAAKA,eAA7B;AACA,SAAKJ,QAAL,GACII,eAAe,IAAIZ,aAAa,CAACY,eAAD,EAAkB,KAAKN,SAAL,CAAesD,MAAf,CAAsBvC,IAAxC,CAAhC,GACMT,eADN,GAEM,KAAKuB,YAAL,EAHV;AAIH;;AACD0B,EAAAA,IAAI,CAACC,OAAD,EAAU;AACVA,IAAAA,OAAO,CAACC,SAAR,CAAkB,KAAKvD,QAAL,CAAcM,CAAhC,EAAmC,KAAKN,QAAL,CAAcO,CAAjD;AACA+C,IAAAA,OAAO,CAACE,SAAR;AACAF,IAAAA,OAAO,CAACG,GAAR,CAAY,CAAZ,EAAe,CAAf,EAAkB,KAAK5C,IAAvB,EAA6B,CAA7B,EAAgC8B,IAAI,CAACe,EAAL,GAAU,CAA1C,EAA6C,KAA7C;AACAJ,IAAAA,OAAO,CAACK,SAAR;AACAL,IAAAA,OAAO,CAACM,SAAR,GAAoBrE,eAAe,CAAC,KAAK+B,KAAN,EAAa,KAAKV,OAAlB,CAAnC;AACA0C,IAAAA,OAAO,CAACO,IAAR;AACH;;AACDlC,EAAAA,YAAY,GAAG;AACX,QAAI1B,EAAJ,EAAQC,EAAR;;AACA,UAAMJ,SAAS,GAAG,KAAKA,SAAvB;AACA,UAAMgE,eAAe,GAAG,KAAK/D,OAAL,CAAaC,QAArC;AACA,WAAON,MAAM,CAACW,MAAP,CAAef,aAAa,CAAC,CAACW,EAAE,GAAG6D,eAAe,KAAK,IAApB,IAA4BA,eAAe,KAAK,KAAK,CAArD,GAAyD,KAAK,CAA9D,GAAkEA,eAAe,CAACxD,CAAxF,MAA+F,IAA/F,IAAuGL,EAAE,KAAK,KAAK,CAAnH,GAAuHA,EAAvH,GAA4H0C,IAAI,CAACoB,MAAL,KAAgB,GAA7I,CAAb,GAAiK,GAAlK,GAAyKjE,SAAS,CAACsD,MAAV,CAAiBvC,IAAjB,CAAsBmD,KAA7M,EAAqN1E,aAAa,CAAC,CAACY,EAAE,GAAG4D,eAAe,KAAK,IAApB,IAA4BA,eAAe,KAAK,KAAK,CAArD,GAAyD,KAAK,CAA9D,GAAkEA,eAAe,CAACvD,CAAxF,MAA+F,IAA/F,IAAuGL,EAAE,KAAK,KAAK,CAAnH,GAAuHA,EAAvH,GAA4HyC,IAAI,CAACoB,MAAL,KAAgB,GAA7I,CAAb,GAAiK,GAAlK,GAAyKjE,SAAS,CAACsD,MAAV,CAAiBvC,IAAjB,CAAsBoD,MAAnZ,CAAP;AACH;;AACDf,EAAAA,sBAAsB,CAACrB,QAAD,EAAWY,CAAX,EAAc;AAChC,QAAIxC,EAAJ;;AACA,QAAI4B,QAAQ,CAACqC,SAAb,EAAwB;AACpB;AACH;;AACD,UAAMpE,SAAS,GAAG,KAAKA,SAAvB;AACA,UAAMqE,UAAU,GAAGrE,SAAS,CAACsD,MAAV,CAAiBvC,IAApC;;AACA,QAAIgB,QAAQ,CAACoB,gBAAb,EAA+B;AAC3BpB,MAAAA,QAAQ,CAAC7B,QAAT,CAAkBM,CAAlB,GAAsBqC,IAAI,CAACyB,KAAL,CAAWzB,IAAI,CAACoB,MAAL,KAAgBI,UAAU,CAACH,KAAtC,CAAtB;AACAnC,MAAAA,QAAQ,CAAC7B,QAAT,CAAkBO,CAAlB,GAAsBoC,IAAI,CAACyB,KAAL,CAAWzB,IAAI,CAACoB,MAAL,KAAgBI,UAAU,CAACF,MAAtC,CAAtB;AACApC,MAAAA,QAAQ,CAACwC,QAAT,CAAkBC,KAAlB,CAAwBzC,QAAQ,CAAC0C,eAAjC;AACA1C,MAAAA,QAAQ,CAACkB,aAAT,GAAyBvC,SAAzB;AACAqB,MAAAA,QAAQ,CAACoB,gBAAT,GAA4B,KAA5B;AACH;;AACD,QAAI,KAAKlD,OAAL,CAAayE,MAAjB,EAAyB;AACrB,UAAI3C,QAAQ,CAACkB,aAAT,KAA2BvC,SAA/B,EAA0C;AACtCqB,QAAAA,QAAQ,CAACkB,aAAT,GAAyBrD,MAAM,CAACW,MAAP,CAAc,CAAd,EAAiB,CAAjB,CAAzB;AACAwB,QAAAA,QAAQ,CAACkB,aAAT,CAAuBL,MAAvB,GAAgCtD,WAAW,CAACyC,QAAQ,CAACQ,WAAT,EAAD,EAAyB,KAAKrC,QAA9B,CAA3C;AACA6B,QAAAA,QAAQ,CAACkB,aAAT,CAAuB0B,KAAvB,GAA+B9B,IAAI,CAACoB,MAAL,KAAgBpB,IAAI,CAACe,EAArB,GAA0B,CAAzD;AACH;;AACD,UAAI7B,QAAQ,CAACkB,aAAT,CAAuBL,MAAvB,IAAiC,KAAK7B,IAAtC,IAA8C,CAAC,KAAKd,OAAL,CAAaiD,OAAhE,EAAyE;AACrE,cAAM0B,OAAO,GAAG/B,IAAI,CAACgC,GAAL,CAASR,UAAU,CAACH,KAApB,EAA2BG,UAAU,CAACF,MAAtC,CAAhB;AACApC,QAAAA,QAAQ,CAACkB,aAAT,CAAuBL,MAAvB,GAAgCgC,OAAO,IAAI,KAAK/B,IAAI,CAACoB,MAAL,KAAgB,GAAhB,GAAsB,GAA3B,CAAJ,CAAvC;AACH;;AACD,UAAIlC,QAAQ,CAAC+C,sBAAT,KAAoCpE,SAAxC,EAAmD;AAC/CqB,QAAAA,QAAQ,CAAC+C,sBAAT,GACI/C,QAAQ,CAACwC,QAAT,CAAkB/D,CAAlB,IAAuB,CAAvB,GAA2B,WAA3B,GAAyC,mBAD7C;AAEH;;AACD,YAAMuE,WAAW,GAAGhD,QAAQ,CAACkB,aAAT,CAAuBL,MAA3C;AACA,YAAMoC,UAAU,GAAGjD,QAAQ,CAACkB,aAAT,CAAuB0B,KAA1C;AACA,YAAMM,cAAc,GAAGlD,QAAQ,CAAC+C,sBAAhC;AACA/C,MAAAA,QAAQ,CAACwC,QAAT,CAAkB/D,CAAlB,GAAsB,CAAtB;AACAuB,MAAAA,QAAQ,CAACwC,QAAT,CAAkB9D,CAAlB,GAAsB,CAAtB;AACA,YAAMyE,UAAU,GAAG;AACf1E,QAAAA,CAAC,EAAEyE,cAAc,KAAK,WAAnB,GAAiCpC,IAAI,CAACsC,GAAtC,GAA4CtC,IAAI,CAACuC,GADrC;AAEf3E,QAAAA,CAAC,EAAEwE,cAAc,KAAK,WAAnB,GAAiCpC,IAAI,CAACuC,GAAtC,GAA4CvC,IAAI,CAACsC;AAFrC,OAAnB;AAIApD,MAAAA,QAAQ,CAAC7B,QAAT,CAAkBM,CAAlB,GAAsB,KAAKN,QAAL,CAAcM,CAAd,GAAkBuE,WAAW,GAAGG,UAAU,CAAC1E,CAAX,CAAawE,UAAb,CAAtD;AACAjD,MAAAA,QAAQ,CAAC7B,QAAT,CAAkBO,CAAlB,GAAsB,KAAKP,QAAL,CAAcO,CAAd,GAAkBsE,WAAW,GAAGG,UAAU,CAACzE,CAAX,CAAauE,UAAb,CAAtD;AACAjD,MAAAA,QAAQ,CAACkB,aAAT,CAAuBL,MAAvB,IAAiCD,CAAC,CAACC,MAAnC;AACAb,MAAAA,QAAQ,CAACkB,aAAT,CAAuB0B,KAAvB,IACM,CAAC,CAACxE,EAAE,GAAG4B,QAAQ,CAACd,MAAT,CAAgBoE,SAAtB,MAAqC,IAArC,IAA6ClF,EAAE,KAAK,KAAK,CAAzD,GAA6DA,EAA7D,GAAkE,CAAnE,IAAwEH,SAAS,CAACiB,MAAV,CAAiBC,UAA1F,GAAwG,GAAzG,GACIlB,SAAS,CAACiB,MAAV,CAAiBI,YAFzB;AAGH,KA7BD,MA8BK;AACD,YAAMiE,IAAI,GAAG1F,MAAM,CAAC2F,MAApB;AACAD,MAAAA,IAAI,CAAC1C,MAAL,GAAcD,CAAC,CAACC,MAAhB;AACA0C,MAAAA,IAAI,CAACX,KAAL,GAAahC,CAAC,CAACgC,KAAf;AACA5C,MAAAA,QAAQ,CAACwC,QAAT,CAAkBiB,KAAlB,CAAwBF,IAAxB;AACH;AACJ;;AAzJyB", "sourcesContent": ["import { colorToRgb, getDistance, getDistances, getRangeValue, getStyleFromRgb, isPointInside } from \"../../Utils\";\nimport { Absorber } from \"./Options/Classes/Absorber\";\nimport { Vector } from \"../../Core\";\nexport class AbsorberInstance {\n    constructor(absorbers, container, options, position) {\n        var _a, _b, _c;\n        this.absorbers = absorbers;\n        this.container = container;\n        this.initialPosition = position ? Vector.create(position.x, position.y) : undefined;\n        if (options instanceof Absorber) {\n            this.options = options;\n        }\n        else {\n            this.options = new Absorber();\n            this.options.load(options);\n        }\n        this.dragging = false;\n        this.name = this.options.name;\n        this.opacity = this.options.opacity;\n        this.size = getRangeValue(this.options.size.value) * container.retina.pixelRatio;\n        this.mass = this.size * this.options.size.density * container.retina.reduceFactor;\n        const limit = this.options.size.limit;\n        this.limit = {\n            radius: limit.radius * container.retina.pixelRatio * container.retina.reduceFactor,\n            mass: limit.mass,\n        };\n        this.color = (_a = colorToRgb(this.options.color)) !== null && _a !== void 0 ? _a : {\n            b: 0,\n            g: 0,\n            r: 0,\n        };\n        this.position = (_c = (_b = this.initialPosition) === null || _b === void 0 ? void 0 : _b.copy()) !== null && _c !== void 0 ? _c : this.calcPosition();\n    }\n    attract(particle) {\n        const container = this.container;\n        const options = this.options;\n        if (options.draggable) {\n            const mouse = container.interactivity.mouse;\n            if (mouse.clicking && mouse.downPosition) {\n                const mouseDist = getDistance(this.position, mouse.downPosition);\n                if (mouseDist <= this.size) {\n                    this.dragging = true;\n                }\n            }\n            else {\n                this.dragging = false;\n            }\n            if (this.dragging && mouse.position) {\n                this.position.x = mouse.position.x;\n                this.position.y = mouse.position.y;\n            }\n        }\n        const pos = particle.getPosition();\n        const { dx, dy, distance } = getDistances(this.position, pos);\n        const v = Vector.create(dx, dy);\n        v.length = (this.mass / Math.pow(distance, 2)) * container.retina.reduceFactor;\n        if (distance < this.size + particle.getRadius()) {\n            const sizeFactor = particle.getRadius() * 0.033 * container.retina.pixelRatio;\n            if ((this.size > particle.getRadius() && distance < this.size - particle.getRadius()) ||\n                (particle.absorberOrbit !== undefined && particle.absorberOrbit.length < 0)) {\n                if (options.destroy) {\n                    particle.destroy();\n                }\n                else {\n                    particle.needsNewPosition = true;\n                    this.updateParticlePosition(particle, v);\n                }\n            }\n            else {\n                if (options.destroy) {\n                    particle.size.value -= sizeFactor;\n                }\n                this.updateParticlePosition(particle, v);\n            }\n            if (this.limit.radius <= 0 || this.size < this.limit.radius) {\n                this.size += sizeFactor;\n            }\n            if (this.limit.mass <= 0 || this.mass < this.limit.mass) {\n                this.mass += sizeFactor * this.options.size.density * container.retina.reduceFactor;\n            }\n        }\n        else {\n            this.updateParticlePosition(particle, v);\n        }\n    }\n    resize() {\n        const initialPosition = this.initialPosition;\n        this.position =\n            initialPosition && isPointInside(initialPosition, this.container.canvas.size)\n                ? initialPosition\n                : this.calcPosition();\n    }\n    draw(context) {\n        context.translate(this.position.x, this.position.y);\n        context.beginPath();\n        context.arc(0, 0, this.size, 0, Math.PI * 2, false);\n        context.closePath();\n        context.fillStyle = getStyleFromRgb(this.color, this.opacity);\n        context.fill();\n    }\n    calcPosition() {\n        var _a, _b;\n        const container = this.container;\n        const percentPosition = this.options.position;\n        return Vector.create((getRangeValue((_a = percentPosition === null || percentPosition === void 0 ? void 0 : percentPosition.x) !== null && _a !== void 0 ? _a : Math.random() * 100) / 100) * container.canvas.size.width, (getRangeValue((_b = percentPosition === null || percentPosition === void 0 ? void 0 : percentPosition.y) !== null && _b !== void 0 ? _b : Math.random() * 100) / 100) * container.canvas.size.height);\n    }\n    updateParticlePosition(particle, v) {\n        var _a;\n        if (particle.destroyed) {\n            return;\n        }\n        const container = this.container;\n        const canvasSize = container.canvas.size;\n        if (particle.needsNewPosition) {\n            particle.position.x = Math.floor(Math.random() * canvasSize.width);\n            particle.position.y = Math.floor(Math.random() * canvasSize.height);\n            particle.velocity.setTo(particle.initialVelocity);\n            particle.absorberOrbit = undefined;\n            particle.needsNewPosition = false;\n        }\n        if (this.options.orbits) {\n            if (particle.absorberOrbit === undefined) {\n                particle.absorberOrbit = Vector.create(0, 0);\n                particle.absorberOrbit.length = getDistance(particle.getPosition(), this.position);\n                particle.absorberOrbit.angle = Math.random() * Math.PI * 2;\n            }\n            if (particle.absorberOrbit.length <= this.size && !this.options.destroy) {\n                const minSize = Math.min(canvasSize.width, canvasSize.height);\n                particle.absorberOrbit.length = minSize * (1 + (Math.random() * 0.2 - 0.1));\n            }\n            if (particle.absorberOrbitDirection === undefined) {\n                particle.absorberOrbitDirection =\n                    particle.velocity.x >= 0 ? \"clockwise\" : \"counter-clockwise\";\n            }\n            const orbitRadius = particle.absorberOrbit.length;\n            const orbitAngle = particle.absorberOrbit.angle;\n            const orbitDirection = particle.absorberOrbitDirection;\n            particle.velocity.x = 0;\n            particle.velocity.y = 0;\n            const updateFunc = {\n                x: orbitDirection === \"clockwise\" ? Math.cos : Math.sin,\n                y: orbitDirection === \"clockwise\" ? Math.sin : Math.cos,\n            };\n            particle.position.x = this.position.x + orbitRadius * updateFunc.x(orbitAngle);\n            particle.position.y = this.position.y + orbitRadius * updateFunc.y(orbitAngle);\n            particle.absorberOrbit.length -= v.length;\n            particle.absorberOrbit.angle +=\n                ((((_a = particle.retina.moveSpeed) !== null && _a !== void 0 ? _a : 0) * container.retina.pixelRatio) / 100) *\n                    container.retina.reduceFactor;\n        }\n        else {\n            const addV = Vector.origin;\n            addV.length = v.length;\n            addV.angle = v.angle;\n            particle.velocity.addTo(addV);\n        }\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
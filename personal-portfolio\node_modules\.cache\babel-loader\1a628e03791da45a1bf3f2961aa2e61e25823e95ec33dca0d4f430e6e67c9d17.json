{"ast": null, "code": "export var GradientType;\n(function (GradientType) {\n  GradientType[\"linear\"] = \"linear\";\n  GradientType[\"radial\"] = \"radial\";\n  GradientType[\"random\"] = \"random\";\n})(GradientType || (GradientType = {}));", "map": {"version": 3, "names": ["GradientType"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/engine/browser/Enums/Types/GradientType.js"], "sourcesContent": ["export var GradientType;\n(function (GradientType) {\n    GradientType[\"linear\"] = \"linear\";\n    GradientType[\"radial\"] = \"radial\";\n    GradientType[\"random\"] = \"random\";\n})(GradientType || (GradientType = {}));\n"], "mappings": "AAAA,OAAO,IAAIA,YAAY;AACvB,CAAC,UAAUA,YAAY,EAAE;EACrBA,YAAY,CAAC,QAAQ,CAAC,GAAG,QAAQ;EACjCA,YAAY,CAAC,QAAQ,CAAC,GAAG,QAAQ;EACjCA,YAAY,CAAC,QAAQ,CAAC,GAAG,QAAQ;AACrC,CAAC,EAAEA,YAAY,KAAKA,YAAY,GAAG,CAAC,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
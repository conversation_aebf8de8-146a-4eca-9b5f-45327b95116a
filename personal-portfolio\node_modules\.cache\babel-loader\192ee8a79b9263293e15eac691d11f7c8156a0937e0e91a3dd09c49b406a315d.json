{"ast": null, "code": "import { RotateUpdater } from \"./RotateUpdater.js\";\nexport async function loadRotateUpdater(engine, refresh = true) {\n  await engine.addParticleUpdater(\"rotate\", container => {\n    return Promise.resolve(new RotateUpdater(container));\n  }, refresh);\n}", "map": {"version": 3, "names": ["RotateUpdater", "loadRotateUpdater", "engine", "refresh", "addParticleUpdater", "container", "Promise", "resolve"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/updater-rotate/browser/index.js"], "sourcesContent": ["import { RotateUpdater } from \"./RotateUpdater.js\";\nexport async function loadRotateUpdater(engine, refresh = true) {\n    await engine.addParticleUpdater(\"rotate\", container => {\n        return Promise.resolve(new RotateUpdater(container));\n    }, refresh);\n}\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,oBAAoB;AAClD,OAAO,eAAeC,iBAAiBA,CAACC,MAAM,EAAEC,OAAO,GAAG,IAAI,EAAE;EAC5D,MAAMD,MAAM,CAACE,kBAAkB,CAAC,QAAQ,EAAEC,SAAS,IAAI;IACnD,OAAOC,OAAO,CAACC,OAAO,CAAC,IAAIP,aAAa,CAACK,SAAS,CAAC,CAAC;EACxD,CAAC,EAAEF,OAAO,CAAC;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
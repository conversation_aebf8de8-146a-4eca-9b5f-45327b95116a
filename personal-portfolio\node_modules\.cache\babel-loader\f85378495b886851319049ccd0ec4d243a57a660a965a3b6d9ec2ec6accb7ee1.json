{"ast": null, "code": "var _jsxFileName = \"D:\\\\Audio Songs\\\\React-portfolio\\\\personal-portfolio\\\\src\\\\components\\\\ParticleBackground\\\\index.js\",\n  _s = $RefreshSig$();\nimport { useCallback } from 'react';\nimport Particles from 'react-tsparticles';\nimport { loadFull } from 'tsparticles';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ParticleBackground = () => {\n  _s();\n  const particlesInit = useCallback(async engine => {\n    await loadFull(engine);\n  }, []);\n  const particlesLoaded = useCallback(async container => {\n    // Optional callback when particles are loaded\n  }, []);\n  return /*#__PURE__*/_jsxDEV(Particles, {\n    id: \"tsparticles\",\n    init: particlesInit,\n    loaded: particlesLoaded,\n    options: {\n      background: {\n        color: {\n          value: 'transparent'\n        }\n      },\n      fpsLimit: 120,\n      interactivity: {\n        events: {\n          onClick: {\n            enable: true,\n            mode: 'push'\n          },\n          onHover: {\n            enable: true,\n            mode: 'repulse'\n          },\n          resize: true\n        },\n        modes: {\n          push: {\n            quantity: 4\n          },\n          repulse: {\n            distance: 200,\n            duration: 0.4\n          }\n        }\n      },\n      particles: {\n        color: {\n          value: '#ffd700'\n        },\n        links: {\n          color: '#ffd700',\n          distance: 150,\n          enable: true,\n          opacity: 0.5,\n          width: 1\n        },\n        move: {\n          direction: 'none',\n          enable: true,\n          outModes: {\n            default: 'bounce'\n          },\n          random: false,\n          speed: 2,\n          straight: false\n        },\n        number: {\n          density: {\n            enable: true,\n            area: 800\n          },\n          value: 80\n        },\n        opacity: {\n          value: 0.5\n        },\n        shape: {\n          type: 'circle'\n        },\n        size: {\n          value: {\n            min: 1,\n            max: 5\n          }\n        }\n      },\n      detectRetina: true\n    },\n    style: {\n      position: 'fixed',\n      top: 0,\n      left: 0,\n      width: '100%',\n      height: '100%',\n      zIndex: -1\n    }\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 15,\n    columnNumber: 5\n  }, this);\n};\n_s(ParticleBackground, \"PjleD03JXt5mR6AE5+SxrsWvXBc=\");\n_c = ParticleBackground;\nexport default ParticleBackground;\nvar _c;\n$RefreshReg$(_c, \"ParticleBackground\");", "map": {"version": 3, "names": ["useCallback", "Particles", "loadFull", "jsxDEV", "_jsxDEV", "ParticleBackground", "_s", "particlesInit", "engine", "particlesLoaded", "container", "id", "init", "loaded", "options", "background", "color", "value", "fpsLimit", "interactivity", "events", "onClick", "enable", "mode", "onHover", "resize", "modes", "push", "quantity", "repulse", "distance", "duration", "particles", "links", "opacity", "width", "move", "direction", "outModes", "default", "random", "speed", "straight", "number", "density", "area", "shape", "type", "size", "min", "max", "detectRetina", "style", "position", "top", "left", "height", "zIndex", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/src/components/ParticleBackground/index.js"], "sourcesContent": ["import { useCallback } from 'react'\nimport Particles from 'react-tsparticles'\nimport { loadFull } from 'tsparticles'\n\nconst ParticleBackground = () => {\n  const particlesInit = useCallback(async (engine) => {\n    await loadFull(engine)\n  }, [])\n\n  const particlesLoaded = useCallback(async (container) => {\n    // Optional callback when particles are loaded\n  }, [])\n\n  return (\n    <Particles\n      id=\"tsparticles\"\n      init={particlesInit}\n      loaded={particlesLoaded}\n      options={{\n        background: {\n          color: {\n            value: 'transparent',\n          },\n        },\n        fpsLimit: 120,\n        interactivity: {\n          events: {\n            onClick: {\n              enable: true,\n              mode: 'push',\n            },\n            onHover: {\n              enable: true,\n              mode: 'repulse',\n            },\n            resize: true,\n          },\n          modes: {\n            push: {\n              quantity: 4,\n            },\n            repulse: {\n              distance: 200,\n              duration: 0.4,\n            },\n          },\n        },\n        particles: {\n          color: {\n            value: '#ffd700',\n          },\n          links: {\n            color: '#ffd700',\n            distance: 150,\n            enable: true,\n            opacity: 0.5,\n            width: 1,\n          },\n          move: {\n            direction: 'none',\n            enable: true,\n            outModes: {\n              default: 'bounce',\n            },\n            random: false,\n            speed: 2,\n            straight: false,\n          },\n          number: {\n            density: {\n              enable: true,\n              area: 800,\n            },\n            value: 80,\n          },\n          opacity: {\n            value: 0.5,\n          },\n          shape: {\n            type: 'circle',\n          },\n          size: {\n            value: { min: 1, max: 5 },\n          },\n        },\n        detectRetina: true,\n      }}\n      style={{\n        position: 'fixed',\n        top: 0,\n        left: 0,\n        width: '100%',\n        height: '100%',\n        zIndex: -1,\n      }}\n    />\n  )\n}\n\nexport default ParticleBackground\n"], "mappings": ";;AAAA,SAASA,WAAW,QAAQ,OAAO;AACnC,OAAOC,SAAS,MAAM,mBAAmB;AACzC,SAASC,QAAQ,QAAQ,aAAa;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAEtC,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/B,MAAMC,aAAa,GAAGP,WAAW,CAAC,MAAOQ,MAAM,IAAK;IAClD,MAAMN,QAAQ,CAACM,MAAM,CAAC;EACxB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,eAAe,GAAGT,WAAW,CAAC,MAAOU,SAAS,IAAK;IACvD;EAAA,CACD,EAAE,EAAE,CAAC;EAEN,oBACEN,OAAA,CAACH,SAAS;IACRU,EAAE,EAAC,aAAa;IAChBC,IAAI,EAAEL,aAAc;IACpBM,MAAM,EAAEJ,eAAgB;IACxBK,OAAO,EAAE;MACPC,UAAU,EAAE;QACVC,KAAK,EAAE;UACLC,KAAK,EAAE;QACT;MACF,CAAC;MACDC,QAAQ,EAAE,GAAG;MACbC,aAAa,EAAE;QACbC,MAAM,EAAE;UACNC,OAAO,EAAE;YACPC,MAAM,EAAE,IAAI;YACZC,IAAI,EAAE;UACR,CAAC;UACDC,OAAO,EAAE;YACPF,MAAM,EAAE,IAAI;YACZC,IAAI,EAAE;UACR,CAAC;UACDE,MAAM,EAAE;QACV,CAAC;QACDC,KAAK,EAAE;UACLC,IAAI,EAAE;YACJC,QAAQ,EAAE;UACZ,CAAC;UACDC,OAAO,EAAE;YACPC,QAAQ,EAAE,GAAG;YACbC,QAAQ,EAAE;UACZ;QACF;MACF,CAAC;MACDC,SAAS,EAAE;QACThB,KAAK,EAAE;UACLC,KAAK,EAAE;QACT,CAAC;QACDgB,KAAK,EAAE;UACLjB,KAAK,EAAE,SAAS;UAChBc,QAAQ,EAAE,GAAG;UACbR,MAAM,EAAE,IAAI;UACZY,OAAO,EAAE,GAAG;UACZC,KAAK,EAAE;QACT,CAAC;QACDC,IAAI,EAAE;UACJC,SAAS,EAAE,MAAM;UACjBf,MAAM,EAAE,IAAI;UACZgB,QAAQ,EAAE;YACRC,OAAO,EAAE;UACX,CAAC;UACDC,MAAM,EAAE,KAAK;UACbC,KAAK,EAAE,CAAC;UACRC,QAAQ,EAAE;QACZ,CAAC;QACDC,MAAM,EAAE;UACNC,OAAO,EAAE;YACPtB,MAAM,EAAE,IAAI;YACZuB,IAAI,EAAE;UACR,CAAC;UACD5B,KAAK,EAAE;QACT,CAAC;QACDiB,OAAO,EAAE;UACPjB,KAAK,EAAE;QACT,CAAC;QACD6B,KAAK,EAAE;UACLC,IAAI,EAAE;QACR,CAAC;QACDC,IAAI,EAAE;UACJ/B,KAAK,EAAE;YAAEgC,GAAG,EAAE,CAAC;YAAEC,GAAG,EAAE;UAAE;QAC1B;MACF,CAAC;MACDC,YAAY,EAAE;IAChB,CAAE;IACFC,KAAK,EAAE;MACLC,QAAQ,EAAE,OAAO;MACjBC,GAAG,EAAE,CAAC;MACNC,IAAI,EAAE,CAAC;MACPpB,KAAK,EAAE,MAAM;MACbqB,MAAM,EAAE,MAAM;MACdC,MAAM,EAAE,CAAC;IACX;EAAE;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEN,CAAC;AAAAvD,EAAA,CA7FKD,kBAAkB;AAAAyD,EAAA,GAAlBzD,kBAAkB;AA+FxB,eAAeA,kBAAkB;AAAA,IAAAyD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
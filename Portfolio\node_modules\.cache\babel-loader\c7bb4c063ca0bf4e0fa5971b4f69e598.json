{"ast": null, "code": "export class MotionReduce {\n  constructor() {\n    this.factor = 4;\n    this.value = true;\n  }\n\n  load(data) {\n    if (!data) {\n      return;\n    }\n\n    if (data.factor !== undefined) {\n      this.factor = data.factor;\n    }\n\n    if (data.value !== undefined) {\n      this.value = data.value;\n    }\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Options/Classes/Motion/MotionReduce.js"], "names": ["MotionReduce", "constructor", "factor", "value", "load", "data", "undefined"], "mappings": "AAAA,OAAO,MAAMA,YAAN,CAAmB;AACtBC,EAAAA,WAAW,GAAG;AACV,SAAKC,MAAL,GAAc,CAAd;AACA,SAAKC,KAAL,GAAa,IAAb;AACH;;AACDC,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,QAAI,CAACA,IAAL,EAAW;AACP;AACH;;AACD,QAAIA,IAAI,CAACH,MAAL,KAAgBI,SAApB,EAA+B;AAC3B,WAAKJ,MAAL,GAAcG,IAAI,CAACH,MAAnB;AACH;;AACD,QAAIG,IAAI,CAACF,KAAL,KAAeG,SAAnB,EAA8B;AAC1B,WAAKH,KAAL,GAAaE,IAAI,CAACF,KAAlB;AACH;AACJ;;AAfqB", "sourcesContent": ["export class MotionReduce {\n    constructor() {\n        this.factor = 4;\n        this.value = true;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.factor !== undefined) {\n            this.factor = data.factor;\n        }\n        if (data.value !== undefined) {\n            this.value = data.value;\n        }\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
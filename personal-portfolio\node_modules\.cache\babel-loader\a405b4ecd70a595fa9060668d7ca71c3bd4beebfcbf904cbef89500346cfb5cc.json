{"ast": null, "code": "import { getRangeValue } from \"tsparticles-engine\";\nexport class PolygonDrawerBase {\n  draw(context, particle, radius) {\n    const start = this.getCenter(particle, radius),\n      side = this.getSidesData(particle, radius),\n      sideCount = side.count.numerator * side.count.denominator,\n      decimalSides = side.count.numerator / side.count.denominator,\n      interiorAngleDegrees = 180 * (decimalSides - 2) / decimalSides,\n      interiorAngle = Math.PI - Math.PI * interiorAngleDegrees / 180;\n    if (!context) {\n      return;\n    }\n    context.beginPath();\n    context.translate(start.x, start.y);\n    context.moveTo(0, 0);\n    for (let i = 0; i < sideCount; i++) {\n      context.lineTo(side.length, 0);\n      context.translate(side.length, 0);\n      context.rotate(interiorAngle);\n    }\n  }\n  getSidesCount(particle) {\n    const polygon = particle.shapeData;\n    return Math.round(getRangeValue(polygon?.sides ?? polygon?.nb_sides ?? 5));\n  }\n}", "map": {"version": 3, "names": ["getRangeValue", "PolygonDrawerBase", "draw", "context", "particle", "radius", "start", "getCenter", "side", "getSidesData", "sideCount", "count", "numerator", "denominator", "decimalSides", "interiorAngleDegrees", "interiorAngle", "Math", "PI", "beginPath", "translate", "x", "y", "moveTo", "i", "lineTo", "length", "rotate", "getSidesCount", "polygon", "shapeData", "round", "sides", "nb_sides"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/tsparticles-shape-polygon/esm/PolygonDrawerBase.js"], "sourcesContent": ["import { getRangeValue } from \"tsparticles-engine\";\nexport class PolygonDrawerBase {\n    draw(context, particle, radius) {\n        const start = this.getCenter(particle, radius), side = this.getSidesData(particle, radius), sideCount = side.count.numerator * side.count.denominator, decimalSides = side.count.numerator / side.count.denominator, interiorAngleDegrees = (180 * (decimalSides - 2)) / decimalSides, interiorAngle = Math.PI - (Math.PI * interiorAngleDegrees) / 180;\n        if (!context) {\n            return;\n        }\n        context.beginPath();\n        context.translate(start.x, start.y);\n        context.moveTo(0, 0);\n        for (let i = 0; i < sideCount; i++) {\n            context.lineTo(side.length, 0);\n            context.translate(side.length, 0);\n            context.rotate(interiorAngle);\n        }\n    }\n    getSidesCount(particle) {\n        const polygon = particle.shapeData;\n        return Math.round(getRangeValue(polygon?.sides ?? polygon?.nb_sides ?? 5));\n    }\n}\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,oBAAoB;AAClD,OAAO,MAAMC,iBAAiB,CAAC;EAC3BC,IAAIA,CAACC,OAAO,EAAEC,QAAQ,EAAEC,MAAM,EAAE;IAC5B,MAAMC,KAAK,GAAG,IAAI,CAACC,SAAS,CAACH,QAAQ,EAAEC,MAAM,CAAC;MAAEG,IAAI,GAAG,IAAI,CAACC,YAAY,CAACL,QAAQ,EAAEC,MAAM,CAAC;MAAEK,SAAS,GAAGF,IAAI,CAACG,KAAK,CAACC,SAAS,GAAGJ,IAAI,CAACG,KAAK,CAACE,WAAW;MAAEC,YAAY,GAAGN,IAAI,CAACG,KAAK,CAACC,SAAS,GAAGJ,IAAI,CAACG,KAAK,CAACE,WAAW;MAAEE,oBAAoB,GAAI,GAAG,IAAID,YAAY,GAAG,CAAC,CAAC,GAAIA,YAAY;MAAEE,aAAa,GAAGC,IAAI,CAACC,EAAE,GAAID,IAAI,CAACC,EAAE,GAAGH,oBAAoB,GAAI,GAAG;IACvV,IAAI,CAACZ,OAAO,EAAE;MACV;IACJ;IACAA,OAAO,CAACgB,SAAS,CAAC,CAAC;IACnBhB,OAAO,CAACiB,SAAS,CAACd,KAAK,CAACe,CAAC,EAAEf,KAAK,CAACgB,CAAC,CAAC;IACnCnB,OAAO,CAACoB,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;IACpB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGd,SAAS,EAAEc,CAAC,EAAE,EAAE;MAChCrB,OAAO,CAACsB,MAAM,CAACjB,IAAI,CAACkB,MAAM,EAAE,CAAC,CAAC;MAC9BvB,OAAO,CAACiB,SAAS,CAACZ,IAAI,CAACkB,MAAM,EAAE,CAAC,CAAC;MACjCvB,OAAO,CAACwB,MAAM,CAACX,aAAa,CAAC;IACjC;EACJ;EACAY,aAAaA,CAACxB,QAAQ,EAAE;IACpB,MAAMyB,OAAO,GAAGzB,QAAQ,CAAC0B,SAAS;IAClC,OAAOb,IAAI,CAACc,KAAK,CAAC/B,aAAa,CAAC6B,OAAO,EAAEG,KAAK,IAAIH,OAAO,EAAEI,QAAQ,IAAI,CAAC,CAAC,CAAC;EAC9E;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
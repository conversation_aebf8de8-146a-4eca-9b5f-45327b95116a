{"ast": null, "code": "import { EmittersPlugin } from \"./EmittersPlugin.js\";\nimport { ShapeManager } from \"./ShapeManager.js\";\nexport async function loadEmittersPlugin(engine, refresh = true) {\n  if (!engine.emitterShapeManager) {\n    engine.emitterShapeManager = new ShapeManager(engine);\n  }\n  if (!engine.addEmitterShapeGenerator) {\n    engine.addEmitterShapeGenerator = (name, generator) => {\n      engine.emitterShapeManager?.addShapeGenerator(name, generator);\n    };\n  }\n  const plugin = new EmittersPlugin(engine);\n  await engine.addPlugin(plugin, refresh);\n}\nexport * from \"./EmitterContainer.js\";\nexport * from \"./EmitterShapeBase.js\";\nexport * from \"./EmittersEngine.js\";\nexport * from \"./IEmitterShape.js\";\nexport * from \"./IEmitterShapeGenerator.js\";\nexport * from \"./Enums/EmitterClickMode.js\";\nexport * from \"./IRandomPositionData.js\";", "map": {"version": 3, "names": ["EmittersPlugin", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "loadEmittersPlugin", "engine", "refresh", "emitter<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "addEmitterShapeGenerator", "name", "generator", "addShapeGenerator", "plugin", "addPlugin"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/plugin-emitters/browser/index.js"], "sourcesContent": ["import { EmittersPlugin } from \"./EmittersPlugin.js\";\nimport { ShapeManager } from \"./ShapeManager.js\";\nexport async function loadEmittersPlugin(engine, refresh = true) {\n    if (!engine.emitterShapeManager) {\n        engine.emitterShapeManager = new ShapeManager(engine);\n    }\n    if (!engine.addEmitterShapeGenerator) {\n        engine.addEmitterShapeGenerator = (name, generator) => {\n            engine.emitterShapeManager?.addShapeGenerator(name, generator);\n        };\n    }\n    const plugin = new EmittersPlugin(engine);\n    await engine.addPlugin(plugin, refresh);\n}\nexport * from \"./EmitterContainer.js\";\nexport * from \"./EmitterShapeBase.js\";\nexport * from \"./EmittersEngine.js\";\nexport * from \"./IEmitterShape.js\";\nexport * from \"./IEmitterShapeGenerator.js\";\nexport * from \"./Enums/EmitterClickMode.js\";\nexport * from \"./IRandomPositionData.js\";\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,qBAAqB;AACpD,SAASC,YAAY,QAAQ,mBAAmB;AAChD,OAAO,eAAeC,kBAAkBA,CAACC,MAAM,EAAEC,OAAO,GAAG,IAAI,EAAE;EAC7D,IAAI,CAACD,MAAM,CAACE,mBAAmB,EAAE;IAC7BF,MAAM,CAACE,mBAAmB,GAAG,IAAIJ,YAAY,CAACE,MAAM,CAAC;EACzD;EACA,IAAI,CAACA,MAAM,CAACG,wBAAwB,EAAE;IAClCH,MAAM,CAACG,wBAAwB,GAAG,CAACC,IAAI,EAAEC,SAAS,KAAK;MACnDL,MAAM,CAACE,mBAAmB,EAAEI,iBAAiB,CAACF,IAAI,EAAEC,SAAS,CAAC;IAClE,CAAC;EACL;EACA,MAAME,MAAM,GAAG,IAAIV,cAAc,CAACG,MAAM,CAAC;EACzC,MAAMA,MAAM,CAACQ,SAAS,CAACD,MAAM,EAAEN,OAAO,CAAC;AAC3C;AACA,cAAc,uBAAuB;AACrC,cAAc,uBAAuB;AACrC,cAAc,qBAAqB;AACnC,cAAc,oBAAoB;AAClC,cAAc,6BAA6B;AAC3C,cAAc,6BAA6B;AAC3C,cAAc,0BAA0B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
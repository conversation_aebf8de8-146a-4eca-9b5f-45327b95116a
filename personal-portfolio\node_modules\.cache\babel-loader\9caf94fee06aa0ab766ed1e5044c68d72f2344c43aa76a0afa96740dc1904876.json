{"ast": null, "code": "import { ParticlesDensity } from \"./ParticlesDensity.js\";\nimport { ParticlesNumberLimit } from \"./ParticlesNumberLimit.js\";\nexport class ParticlesNumber {\n  constructor() {\n    this.density = new ParticlesDensity();\n    this.limit = new ParticlesNumberLimit();\n    this.value = 0;\n  }\n  load(data) {\n    if (!data) {\n      return;\n    }\n    this.density.load(data.density);\n    this.limit.load(data.limit);\n    if (data.value !== undefined) {\n      this.value = data.value;\n    }\n  }\n}", "map": {"version": 3, "names": ["ParticlesDensity", "ParticlesNumberLimit", "ParticlesNumber", "constructor", "density", "limit", "value", "load", "data", "undefined"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/engine/browser/Options/Classes/Particles/Number/ParticlesNumber.js"], "sourcesContent": ["import { ParticlesDensity } from \"./ParticlesDensity.js\";\nimport { ParticlesNumberLimit } from \"./ParticlesNumberLimit.js\";\nexport class ParticlesNumber {\n    constructor() {\n        this.density = new ParticlesDensity();\n        this.limit = new ParticlesNumberLimit();\n        this.value = 0;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        this.density.load(data.density);\n        this.limit.load(data.limit);\n        if (data.value !== undefined) {\n            this.value = data.value;\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,uBAAuB;AACxD,SAASC,oBAAoB,QAAQ,2BAA2B;AAChE,OAAO,MAAMC,eAAe,CAAC;EACzBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,OAAO,GAAG,IAAIJ,gBAAgB,CAAC,CAAC;IACrC,IAAI,CAACK,KAAK,GAAG,IAAIJ,oBAAoB,CAAC,CAAC;IACvC,IAAI,CAACK,KAAK,GAAG,CAAC;EAClB;EACAC,IAAIA,CAACC,IAAI,EAAE;IACP,IAAI,CAACA,IAAI,EAAE;MACP;IACJ;IACA,IAAI,CAACJ,OAAO,CAACG,IAAI,CAACC,IAAI,CAACJ,OAAO,CAAC;IAC/B,IAAI,CAACC,KAAK,CAACE,IAAI,CAACC,IAAI,CAACH,KAAK,CAAC;IAC3B,IAAIG,IAAI,CAACF,KAAK,KAAKG,SAAS,EAAE;MAC1B,IAAI,CAACH,KAAK,GAAGE,IAAI,CAACF,KAAK;IAC3B;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { AbsorberSize } from \"./AbsorberSize\";\nimport { OptionsColor } from \"../../../../Options/Classes/OptionsColor\";\nimport { setRangeValue } from \"../../../../Utils\";\nexport class Absorber {\n  constructor() {\n    this.color = new OptionsColor();\n    this.color.value = \"#000000\";\n    this.draggable = false;\n    this.opacity = 1;\n    this.destroy = true;\n    this.orbits = false;\n    this.size = new AbsorberSize();\n  }\n\n  load(data) {\n    if (data === undefined) {\n      return;\n    }\n\n    if (data.color !== undefined) {\n      this.color = OptionsColor.create(this.color, data.color);\n    }\n\n    if (data.draggable !== undefined) {\n      this.draggable = data.draggable;\n    }\n\n    this.name = data.name;\n\n    if (data.opacity !== undefined) {\n      this.opacity = data.opacity;\n    }\n\n    if (data.position !== undefined) {\n      this.position = {};\n\n      if (data.position.x !== undefined) {\n        this.position.x = setRangeValue(data.position.x);\n      }\n\n      if (data.position.y !== undefined) {\n        this.position.y = setRangeValue(data.position.y);\n      }\n    }\n\n    if (data.size !== undefined) {\n      this.size.load(data.size);\n    }\n\n    if (data.destroy !== undefined) {\n      this.destroy = data.destroy;\n    }\n\n    if (data.orbits !== undefined) {\n      this.orbits = data.orbits;\n    }\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Plugins/Absorbers/Options/Classes/Absorber.js"], "names": ["AbsorberSize", "OptionsColor", "setRangeValue", "Absorber", "constructor", "color", "value", "draggable", "opacity", "destroy", "orbits", "size", "load", "data", "undefined", "create", "name", "position", "x", "y"], "mappings": "AAAA,SAASA,YAAT,QAA6B,gBAA7B;AACA,SAASC,YAAT,QAA6B,0CAA7B;AACA,SAASC,aAAT,QAA8B,mBAA9B;AACA,OAAO,MAAMC,QAAN,CAAe;AAClBC,EAAAA,WAAW,GAAG;AACV,SAAKC,KAAL,GAAa,IAAIJ,YAAJ,EAAb;AACA,SAAKI,KAAL,CAAWC,KAAX,GAAmB,SAAnB;AACA,SAAKC,SAAL,GAAiB,KAAjB;AACA,SAAKC,OAAL,GAAe,CAAf;AACA,SAAKC,OAAL,GAAe,IAAf;AACA,SAAKC,MAAL,GAAc,KAAd;AACA,SAAKC,IAAL,GAAY,IAAIX,YAAJ,EAAZ;AACH;;AACDY,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,QAAIA,IAAI,KAAKC,SAAb,EAAwB;AACpB;AACH;;AACD,QAAID,IAAI,CAACR,KAAL,KAAeS,SAAnB,EAA8B;AAC1B,WAAKT,KAAL,GAAaJ,YAAY,CAACc,MAAb,CAAoB,KAAKV,KAAzB,EAAgCQ,IAAI,CAACR,KAArC,CAAb;AACH;;AACD,QAAIQ,IAAI,CAACN,SAAL,KAAmBO,SAAvB,EAAkC;AAC9B,WAAKP,SAAL,GAAiBM,IAAI,CAACN,SAAtB;AACH;;AACD,SAAKS,IAAL,GAAYH,IAAI,CAACG,IAAjB;;AACA,QAAIH,IAAI,CAACL,OAAL,KAAiBM,SAArB,EAAgC;AAC5B,WAAKN,OAAL,GAAeK,IAAI,CAACL,OAApB;AACH;;AACD,QAAIK,IAAI,CAACI,QAAL,KAAkBH,SAAtB,EAAiC;AAC7B,WAAKG,QAAL,GAAgB,EAAhB;;AACA,UAAIJ,IAAI,CAACI,QAAL,CAAcC,CAAd,KAAoBJ,SAAxB,EAAmC;AAC/B,aAAKG,QAAL,CAAcC,CAAd,GAAkBhB,aAAa,CAACW,IAAI,CAACI,QAAL,CAAcC,CAAf,CAA/B;AACH;;AACD,UAAIL,IAAI,CAACI,QAAL,CAAcE,CAAd,KAAoBL,SAAxB,EAAmC;AAC/B,aAAKG,QAAL,CAAcE,CAAd,GAAkBjB,aAAa,CAACW,IAAI,CAACI,QAAL,CAAcE,CAAf,CAA/B;AACH;AACJ;;AACD,QAAIN,IAAI,CAACF,IAAL,KAAcG,SAAlB,EAA6B;AACzB,WAAKH,IAAL,CAAUC,IAAV,CAAeC,IAAI,CAACF,IAApB;AACH;;AACD,QAAIE,IAAI,CAACJ,OAAL,KAAiBK,SAArB,EAAgC;AAC5B,WAAKL,OAAL,GAAeI,IAAI,CAACJ,OAApB;AACH;;AACD,QAAII,IAAI,CAACH,MAAL,KAAgBI,SAApB,EAA+B;AAC3B,WAAKJ,MAAL,GAAcG,IAAI,CAACH,MAAnB;AACH;AACJ;;AA1CiB", "sourcesContent": ["import { AbsorberSize } from \"./AbsorberSize\";\nimport { OptionsColor } from \"../../../../Options/Classes/OptionsColor\";\nimport { setRangeValue } from \"../../../../Utils\";\nexport class Absorber {\n    constructor() {\n        this.color = new OptionsColor();\n        this.color.value = \"#000000\";\n        this.draggable = false;\n        this.opacity = 1;\n        this.destroy = true;\n        this.orbits = false;\n        this.size = new AbsorberSize();\n    }\n    load(data) {\n        if (data === undefined) {\n            return;\n        }\n        if (data.color !== undefined) {\n            this.color = OptionsColor.create(this.color, data.color);\n        }\n        if (data.draggable !== undefined) {\n            this.draggable = data.draggable;\n        }\n        this.name = data.name;\n        if (data.opacity !== undefined) {\n            this.opacity = data.opacity;\n        }\n        if (data.position !== undefined) {\n            this.position = {};\n            if (data.position.x !== undefined) {\n                this.position.x = setRangeValue(data.position.x);\n            }\n            if (data.position.y !== undefined) {\n                this.position.y = setRangeValue(data.position.y);\n            }\n        }\n        if (data.size !== undefined) {\n            this.size.load(data.size);\n        }\n        if (data.destroy !== undefined) {\n            this.destroy = data.destroy;\n        }\n        if (data.orbits !== undefined) {\n            this.orbits = data.orbits;\n        }\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
{"ast": null, "code": "import { TextDrawer } from \"./TextDrawer.js\";\nexport async function loadTextShape(engine, refresh = true) {\n  await engine.addShape(new TextDrawer(), refresh);\n}", "map": {"version": 3, "names": ["TextDrawer", "loadTextShape", "engine", "refresh", "addShape"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/shape-text/browser/index.js"], "sourcesContent": ["import { TextDrawer } from \"./TextDrawer.js\";\nexport async function loadTextShape(engine, refresh = true) {\n    await engine.addShape(new TextDrawer(), refresh);\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,iBAAiB;AAC5C,OAAO,eAAeC,aAAaA,CAACC,MAAM,EAAEC,OAAO,GAAG,IAAI,EAAE;EACxD,MAAMD,MAAM,CAACE,QAAQ,CAAC,IAAIJ,UAAU,CAAC,CAAC,EAAEG,OAAO,CAAC;AACpD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
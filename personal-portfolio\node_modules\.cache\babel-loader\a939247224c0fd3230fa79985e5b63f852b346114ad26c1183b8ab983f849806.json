{"ast": null, "code": "import { deepExtend } from \"@tsparticles/engine\";\nimport { EmitterShapeReplace } from \"./EmitterShapeReplace.js\";\nexport class EmitterShape {\n  constructor() {\n    this.options = {};\n    this.replace = new EmitterShapeReplace();\n    this.type = \"square\";\n  }\n  load(data) {\n    if (!data) {\n      return;\n    }\n    if (data.options !== undefined) {\n      this.options = deepExtend({}, data.options ?? {});\n    }\n    this.replace.load(data.replace);\n    if (data.type !== undefined) {\n      this.type = data.type;\n    }\n  }\n}", "map": {"version": 3, "names": ["deepExtend", "Emitter<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EmitterShape", "constructor", "options", "replace", "type", "load", "data", "undefined"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/plugin-emitters/browser/Options/Classes/EmitterShape.js"], "sourcesContent": ["import { deepExtend } from \"@tsparticles/engine\";\nimport { EmitterShapeReplace } from \"./EmitterShapeReplace.js\";\nexport class EmitterShape {\n    constructor() {\n        this.options = {};\n        this.replace = new EmitterShapeReplace();\n        this.type = \"square\";\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.options !== undefined) {\n            this.options = deepExtend({}, data.options ?? {});\n        }\n        this.replace.load(data.replace);\n        if (data.type !== undefined) {\n            this.type = data.type;\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,qBAAqB;AAChD,SAASC,mBAAmB,QAAQ,0BAA0B;AAC9D,OAAO,MAAMC,YAAY,CAAC;EACtBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,OAAO,GAAG,CAAC,CAAC;IACjB,IAAI,CAACC,OAAO,GAAG,IAAIJ,mBAAmB,CAAC,CAAC;IACxC,IAAI,CAACK,IAAI,GAAG,QAAQ;EACxB;EACAC,IAAIA,CAACC,IAAI,EAAE;IACP,IAAI,CAACA,IAAI,EAAE;MACP;IACJ;IACA,IAAIA,IAAI,CAACJ,OAAO,KAAKK,SAAS,EAAE;MAC5B,IAAI,CAACL,OAAO,GAAGJ,UAAU,CAAC,CAAC,CAAC,EAAEQ,IAAI,CAACJ,OAAO,IAAI,CAAC,CAAC,CAAC;IACrD;IACA,IAAI,CAACC,OAAO,CAACE,IAAI,CAACC,IAAI,CAACH,OAAO,CAAC;IAC/B,IAAIG,IAAI,CAACF,IAAI,KAAKG,SAAS,EAAE;MACzB,IAAI,CAACH,IAAI,GAAGE,IAAI,CAACF,IAAI;IACzB;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
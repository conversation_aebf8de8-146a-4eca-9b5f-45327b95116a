{"ast": null, "code": "import { WobbleUpdater } from \"./WobbleUpdater.js\";\nexport async function loadWobbleUpdater(engine, refresh = true) {\n  await engine.addParticleUpdater(\"wobble\", container => {\n    return Promise.resolve(new WobbleUpdater(container));\n  }, refresh);\n}", "map": {"version": 3, "names": ["WobbleUpdater", "loadWobbleUpdater", "engine", "refresh", "addParticleUpdater", "container", "Promise", "resolve"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/updater-wobble/browser/index.js"], "sourcesContent": ["import { WobbleUpdater } from \"./WobbleUpdater.js\";\nexport async function loadWobbleUpdater(engine, refresh = true) {\n    await engine.addParticleUpdater(\"wobble\", container => {\n        return Promise.resolve(new WobbleUpdater(container));\n    }, refresh);\n}\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,oBAAoB;AAClD,OAAO,eAAeC,iBAAiBA,CAACC,MAAM,EAAEC,OAAO,GAAG,IAAI,EAAE;EAC5D,MAAMD,MAAM,CAACE,kBAAkB,CAAC,QAAQ,EAAEC,SAAS,IAAI;IACnD,OAAOC,OAAO,CAACC,OAAO,CAAC,IAAIP,aAAa,CAACK,SAAS,CAAC,CAAC;EACxD,CAAC,EAAEF,OAAO,CAAC;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
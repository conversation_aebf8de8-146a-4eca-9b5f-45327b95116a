{"ast": null, "code": "export var AnimationStatus;\n(function (AnimationStatus) {\n  AnimationStatus[\"increasing\"] = \"increasing\";\n  AnimationStatus[\"decreasing\"] = \"decreasing\";\n})(AnimationStatus || (AnimationStatus = {}));", "map": {"version": 3, "names": ["AnimationStatus"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/engine/browser/Enums/AnimationStatus.js"], "sourcesContent": ["export var AnimationStatus;\n(function (AnimationStatus) {\n    AnimationStatus[\"increasing\"] = \"increasing\";\n    AnimationStatus[\"decreasing\"] = \"decreasing\";\n})(AnimationStatus || (AnimationStatus = {}));\n"], "mappings": "AAAA,OAAO,IAAIA,eAAe;AAC1B,CAAC,UAAUA,eAAe,EAAE;EACxBA,eAAe,CAAC,YAAY,CAAC,GAAG,YAAY;EAC5CA,eAAe,CAAC,YAAY,CAAC,GAAG,YAAY;AAChD,CAAC,EAAEA,eAAe,KAAKA,eAAe,GAAG,CAAC,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
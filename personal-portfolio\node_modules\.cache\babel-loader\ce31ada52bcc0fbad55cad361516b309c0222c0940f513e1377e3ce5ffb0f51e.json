{"ast": null, "code": "import { OptionsColor } from \"../../OptionsColor.js\";\nexport class MoveTrailFill {\n  load(data) {\n    if (!data) {\n      return;\n    }\n    if (data.color !== undefined) {\n      this.color = OptionsColor.create(this.color, data.color);\n    }\n    if (data.image !== undefined) {\n      this.image = data.image;\n    }\n  }\n}", "map": {"version": 3, "names": ["OptionsColor", "MoveTrailFill", "load", "data", "color", "undefined", "create", "image"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/engine/browser/Options/Classes/Particles/Move/MoveTrailFill.js"], "sourcesContent": ["import { OptionsColor } from \"../../OptionsColor.js\";\nexport class MoveTrailFill {\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.color !== undefined) {\n            this.color = OptionsColor.create(this.color, data.color);\n        }\n        if (data.image !== undefined) {\n            this.image = data.image;\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,uBAAuB;AACpD,OAAO,MAAMC,aAAa,CAAC;EACvBC,IAAIA,CAACC,IAAI,EAAE;IACP,IAAI,CAACA,IAAI,EAAE;MACP;IACJ;IACA,IAAIA,IAAI,CAACC,KAAK,KAAKC,SAAS,EAAE;MAC1B,IAAI,CAACD,KAAK,GAAGJ,YAAY,CAACM,MAAM,CAAC,IAAI,CAACF,KAAK,EAAED,IAAI,CAACC,KAAK,CAAC;IAC5D;IACA,IAAID,IAAI,CAACI,KAAK,KAAKF,SAAS,EAAE;MAC1B,IAAI,CAACE,KAAK,GAAGJ,IAAI,CAACI,KAAK;IAC3B;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { clamp, collisionVelocity, getDistances, getRandom, getRangeMax, getRangeMin, getRangeValue, randomInRange } from \"./NumberUtils.js\";\nimport { halfRandom, millisecondsToSeconds, percentDenominator } from \"../Core/Utils/Constants.js\";\nimport { isArray, isObject } from \"./TypeUtils.js\";\nimport { AnimationMode } from \"../Enums/Modes/AnimationMode.js\";\nimport { AnimationStatus } from \"../Enums/AnimationStatus.js\";\nimport { DestroyType } from \"../Enums/Types/DestroyType.js\";\nimport { OutModeDirection } from \"../Enums/Directions/OutModeDirection.js\";\nimport { PixelMode } from \"../Enums/Modes/PixelMode.js\";\nimport { StartValueType } from \"../Enums/Types/StartValueType.js\";\nimport { Vector } from \"../Core/Utils/Vectors.js\";\nconst _logger = {\n  debug: console.debug,\n  error: console.error,\n  info: console.info,\n  log: console.log,\n  verbose: console.log,\n  warning: console.warn\n};\nexport function setLogger(logger) {\n  _logger.debug = logger.debug || _logger.debug;\n  _logger.error = logger.error || _logger.error;\n  _logger.info = logger.info || _logger.info;\n  _logger.log = logger.log || _logger.log;\n  _logger.verbose = logger.verbose || _logger.verbose;\n  _logger.warning = logger.warning || _logger.warning;\n}\nexport function getLogger() {\n  return _logger;\n}\nfunction rectSideBounce(data) {\n  const res = {\n      bounced: false\n    },\n    {\n      pSide,\n      pOtherSide,\n      rectSide,\n      rectOtherSide,\n      velocity,\n      factor\n    } = data,\n    half = 0.5,\n    minVelocity = 0;\n  if (pOtherSide.min < rectOtherSide.min || pOtherSide.min > rectOtherSide.max || pOtherSide.max < rectOtherSide.min || pOtherSide.max > rectOtherSide.max) {\n    return res;\n  }\n  if (pSide.max >= rectSide.min && pSide.max <= (rectSide.max + rectSide.min) * half && velocity > minVelocity || pSide.min <= rectSide.max && pSide.min > (rectSide.max + rectSide.min) * half && velocity < minVelocity) {\n    res.velocity = velocity * -factor;\n    res.bounced = true;\n  }\n  return res;\n}\nfunction checkSelector(element, selectors) {\n  const res = executeOnSingleOrMultiple(selectors, selector => {\n    return element.matches(selector);\n  });\n  return isArray(res) ? res.some(t => t) : res;\n}\nexport function isSsr() {\n  return typeof window === \"undefined\" || !window || typeof window.document === \"undefined\" || !window.document;\n}\nexport function hasMatchMedia() {\n  return !isSsr() && typeof matchMedia !== \"undefined\";\n}\nexport function safeMatchMedia(query) {\n  if (!hasMatchMedia()) {\n    return;\n  }\n  return matchMedia(query);\n}\nexport function safeIntersectionObserver(callback) {\n  if (isSsr() || typeof IntersectionObserver === \"undefined\") {\n    return;\n  }\n  return new IntersectionObserver(callback);\n}\nexport function safeMutationObserver(callback) {\n  if (isSsr() || typeof MutationObserver === \"undefined\") {\n    return;\n  }\n  return new MutationObserver(callback);\n}\nexport function isInArray(value, array) {\n  const invalidIndex = -1;\n  return value === array || isArray(array) && array.indexOf(value) > invalidIndex;\n}\nexport async function loadFont(font, weight) {\n  try {\n    await document.fonts.load(`${weight ?? \"400\"} 36px '${font ?? \"Verdana\"}'`);\n  } catch {}\n}\nexport function arrayRandomIndex(array) {\n  return Math.floor(getRandom() * array.length);\n}\nexport function itemFromArray(array, index, useIndex = true) {\n  return array[index !== undefined && useIndex ? index % array.length : arrayRandomIndex(array)];\n}\nexport function isPointInside(point, size, offset, radius, direction) {\n  const minRadius = 0;\n  return areBoundsInside(calculateBounds(point, radius ?? minRadius), size, offset, direction);\n}\nexport function areBoundsInside(bounds, size, offset, direction) {\n  let inside = true;\n  if (!direction || direction === OutModeDirection.bottom) {\n    inside = bounds.top < size.height + offset.x;\n  }\n  if (inside && (!direction || direction === OutModeDirection.left)) {\n    inside = bounds.right > offset.x;\n  }\n  if (inside && (!direction || direction === OutModeDirection.right)) {\n    inside = bounds.left < size.width + offset.y;\n  }\n  if (inside && (!direction || direction === OutModeDirection.top)) {\n    inside = bounds.bottom > offset.y;\n  }\n  return inside;\n}\nexport function calculateBounds(point, radius) {\n  return {\n    bottom: point.y + radius,\n    left: point.x - radius,\n    right: point.x + radius,\n    top: point.y - radius\n  };\n}\nexport function deepExtend(destination, ...sources) {\n  for (const source of sources) {\n    if (source === undefined || source === null) {\n      continue;\n    }\n    if (!isObject(source)) {\n      destination = source;\n      continue;\n    }\n    const sourceIsArray = Array.isArray(source);\n    if (sourceIsArray && (isObject(destination) || !destination || !Array.isArray(destination))) {\n      destination = [];\n    } else if (!sourceIsArray && (isObject(destination) || !destination || Array.isArray(destination))) {\n      destination = {};\n    }\n    for (const key in source) {\n      if (key === \"__proto__\") {\n        continue;\n      }\n      const sourceDict = source,\n        value = sourceDict[key],\n        destDict = destination;\n      destDict[key] = isObject(value) && Array.isArray(value) ? value.map(v => deepExtend(destDict[key], v)) : deepExtend(destDict[key], value);\n    }\n  }\n  return destination;\n}\nexport function isDivModeEnabled(mode, divs) {\n  return !!findItemFromSingleOrMultiple(divs, t => t.enable && isInArray(mode, t.mode));\n}\nexport function divModeExecute(mode, divs, callback) {\n  executeOnSingleOrMultiple(divs, div => {\n    const divMode = div.mode,\n      divEnabled = div.enable;\n    if (divEnabled && isInArray(mode, divMode)) {\n      singleDivModeExecute(div, callback);\n    }\n  });\n}\nexport function singleDivModeExecute(div, callback) {\n  const selectors = div.selectors;\n  executeOnSingleOrMultiple(selectors, selector => {\n    callback(selector, div);\n  });\n}\nexport function divMode(divs, element) {\n  if (!element || !divs) {\n    return;\n  }\n  return findItemFromSingleOrMultiple(divs, div => {\n    return checkSelector(element, div.selectors);\n  });\n}\nexport function circleBounceDataFromParticle(p) {\n  return {\n    position: p.getPosition(),\n    radius: p.getRadius(),\n    mass: p.getMass(),\n    velocity: p.velocity,\n    factor: Vector.create(getRangeValue(p.options.bounce.horizontal.value), getRangeValue(p.options.bounce.vertical.value))\n  };\n}\nexport function circleBounce(p1, p2) {\n  const {\n      x: xVelocityDiff,\n      y: yVelocityDiff\n    } = p1.velocity.sub(p2.velocity),\n    [pos1, pos2] = [p1.position, p2.position],\n    {\n      dx: xDist,\n      dy: yDist\n    } = getDistances(pos2, pos1),\n    minimumDistance = 0;\n  if (xVelocityDiff * xDist + yVelocityDiff * yDist < minimumDistance) {\n    return;\n  }\n  const angle = -Math.atan2(yDist, xDist),\n    m1 = p1.mass,\n    m2 = p2.mass,\n    u1 = p1.velocity.rotate(angle),\n    u2 = p2.velocity.rotate(angle),\n    v1 = collisionVelocity(u1, u2, m1, m2),\n    v2 = collisionVelocity(u2, u1, m1, m2),\n    vFinal1 = v1.rotate(-angle),\n    vFinal2 = v2.rotate(-angle);\n  p1.velocity.x = vFinal1.x * p1.factor.x;\n  p1.velocity.y = vFinal1.y * p1.factor.y;\n  p2.velocity.x = vFinal2.x * p2.factor.x;\n  p2.velocity.y = vFinal2.y * p2.factor.y;\n}\nexport function rectBounce(particle, divBounds) {\n  const pPos = particle.getPosition(),\n    size = particle.getRadius(),\n    bounds = calculateBounds(pPos, size),\n    bounceOptions = particle.options.bounce,\n    resH = rectSideBounce({\n      pSide: {\n        min: bounds.left,\n        max: bounds.right\n      },\n      pOtherSide: {\n        min: bounds.top,\n        max: bounds.bottom\n      },\n      rectSide: {\n        min: divBounds.left,\n        max: divBounds.right\n      },\n      rectOtherSide: {\n        min: divBounds.top,\n        max: divBounds.bottom\n      },\n      velocity: particle.velocity.x,\n      factor: getRangeValue(bounceOptions.horizontal.value)\n    });\n  if (resH.bounced) {\n    if (resH.velocity !== undefined) {\n      particle.velocity.x = resH.velocity;\n    }\n    if (resH.position !== undefined) {\n      particle.position.x = resH.position;\n    }\n  }\n  const resV = rectSideBounce({\n    pSide: {\n      min: bounds.top,\n      max: bounds.bottom\n    },\n    pOtherSide: {\n      min: bounds.left,\n      max: bounds.right\n    },\n    rectSide: {\n      min: divBounds.top,\n      max: divBounds.bottom\n    },\n    rectOtherSide: {\n      min: divBounds.left,\n      max: divBounds.right\n    },\n    velocity: particle.velocity.y,\n    factor: getRangeValue(bounceOptions.vertical.value)\n  });\n  if (resV.bounced) {\n    if (resV.velocity !== undefined) {\n      particle.velocity.y = resV.velocity;\n    }\n    if (resV.position !== undefined) {\n      particle.position.y = resV.position;\n    }\n  }\n}\nexport function executeOnSingleOrMultiple(obj, callback) {\n  const defaultIndex = 0;\n  return isArray(obj) ? obj.map((item, index) => callback(item, index)) : callback(obj, defaultIndex);\n}\nexport function itemFromSingleOrMultiple(obj, index, useIndex) {\n  return isArray(obj) ? itemFromArray(obj, index, useIndex) : obj;\n}\nexport function findItemFromSingleOrMultiple(obj, callback) {\n  if (isArray(obj)) {\n    return obj.find((t, index) => callback(t, index));\n  }\n  const defaultIndex = 0;\n  return callback(obj, defaultIndex) ? obj : undefined;\n}\nexport function initParticleNumericAnimationValue(options, pxRatio) {\n  const valueRange = options.value,\n    animationOptions = options.animation,\n    res = {\n      delayTime: getRangeValue(animationOptions.delay) * millisecondsToSeconds,\n      enable: animationOptions.enable,\n      value: getRangeValue(options.value) * pxRatio,\n      max: getRangeMax(valueRange) * pxRatio,\n      min: getRangeMin(valueRange) * pxRatio,\n      loops: 0,\n      maxLoops: getRangeValue(animationOptions.count),\n      time: 0\n    },\n    decayOffset = 1;\n  if (animationOptions.enable) {\n    res.decay = decayOffset - getRangeValue(animationOptions.decay);\n    switch (animationOptions.mode) {\n      case AnimationMode.increase:\n        res.status = AnimationStatus.increasing;\n        break;\n      case AnimationMode.decrease:\n        res.status = AnimationStatus.decreasing;\n        break;\n      case AnimationMode.random:\n        res.status = getRandom() >= halfRandom ? AnimationStatus.increasing : AnimationStatus.decreasing;\n        break;\n    }\n    const autoStatus = animationOptions.mode === AnimationMode.auto;\n    switch (animationOptions.startValue) {\n      case StartValueType.min:\n        res.value = res.min;\n        if (autoStatus) {\n          res.status = AnimationStatus.increasing;\n        }\n        break;\n      case StartValueType.max:\n        res.value = res.max;\n        if (autoStatus) {\n          res.status = AnimationStatus.decreasing;\n        }\n        break;\n      case StartValueType.random:\n      default:\n        res.value = randomInRange(res);\n        if (autoStatus) {\n          res.status = getRandom() >= halfRandom ? AnimationStatus.increasing : AnimationStatus.decreasing;\n        }\n        break;\n    }\n  }\n  res.initialValue = res.value;\n  return res;\n}\nfunction getPositionOrSize(positionOrSize, canvasSize) {\n  const isPercent = positionOrSize.mode === PixelMode.percent;\n  if (!isPercent) {\n    const {\n      mode: _,\n      ...rest\n    } = positionOrSize;\n    return rest;\n  }\n  const isPosition = \"x\" in positionOrSize;\n  if (isPosition) {\n    return {\n      x: positionOrSize.x / percentDenominator * canvasSize.width,\n      y: positionOrSize.y / percentDenominator * canvasSize.height\n    };\n  } else {\n    return {\n      width: positionOrSize.width / percentDenominator * canvasSize.width,\n      height: positionOrSize.height / percentDenominator * canvasSize.height\n    };\n  }\n}\nexport function getPosition(position, canvasSize) {\n  return getPositionOrSize(position, canvasSize);\n}\nexport function getSize(size, canvasSize) {\n  return getPositionOrSize(size, canvasSize);\n}\nfunction checkDestroy(particle, destroyType, value, minValue, maxValue) {\n  switch (destroyType) {\n    case DestroyType.max:\n      if (value >= maxValue) {\n        particle.destroy();\n      }\n      break;\n    case DestroyType.min:\n      if (value <= minValue) {\n        particle.destroy();\n      }\n      break;\n  }\n}\nexport function updateAnimation(particle, data, changeDirection, destroyType, delta) {\n  const minLoops = 0,\n    minDelay = 0,\n    identity = 1,\n    minVelocity = 0,\n    minDecay = 1;\n  if (particle.destroyed || !data || !data.enable || (data.maxLoops ?? minLoops) > minLoops && (data.loops ?? minLoops) > (data.maxLoops ?? minLoops)) {\n    return;\n  }\n  const velocity = (data.velocity ?? minVelocity) * delta.factor,\n    minValue = data.min,\n    maxValue = data.max,\n    decay = data.decay ?? minDecay;\n  if (!data.time) {\n    data.time = 0;\n  }\n  if ((data.delayTime ?? minDelay) > minDelay && data.time < (data.delayTime ?? minDelay)) {\n    data.time += delta.value;\n  }\n  if ((data.delayTime ?? minDelay) > minDelay && data.time < (data.delayTime ?? minDelay)) {\n    return;\n  }\n  switch (data.status) {\n    case AnimationStatus.increasing:\n      if (data.value >= maxValue) {\n        if (changeDirection) {\n          data.status = AnimationStatus.decreasing;\n        } else {\n          data.value -= maxValue;\n        }\n        if (!data.loops) {\n          data.loops = minLoops;\n        }\n        data.loops++;\n      } else {\n        data.value += velocity;\n      }\n      break;\n    case AnimationStatus.decreasing:\n      if (data.value <= minValue) {\n        if (changeDirection) {\n          data.status = AnimationStatus.increasing;\n        } else {\n          data.value += maxValue;\n        }\n        if (!data.loops) {\n          data.loops = minLoops;\n        }\n        data.loops++;\n      } else {\n        data.value -= velocity;\n      }\n  }\n  if (data.velocity && decay !== identity) {\n    data.velocity *= decay;\n  }\n  checkDestroy(particle, destroyType, data.value, minValue, maxValue);\n  if (!particle.destroyed) {\n    data.value = clamp(data.value, minValue, maxValue);\n  }\n}", "map": {"version": 3, "names": ["clamp", "collisionVelocity", "getDistances", "getRandom", "getRangeMax", "getRangeMin", "getRangeValue", "randomInRange", "halfRandom", "millisecondsToSeconds", "percentDenominator", "isArray", "isObject", "AnimationMode", "AnimationStatus", "DestroyType", "OutModeDirection", "PixelMode", "StartValueType", "Vector", "_logger", "debug", "console", "error", "info", "log", "verbose", "warning", "warn", "<PERSON><PERSON><PERSON><PERSON>", "logger", "<PERSON><PERSON><PERSON><PERSON>", "rectSideBounce", "data", "res", "bounced", "pSide", "pOtherSide", "rectSide", "rectOtherSide", "velocity", "factor", "half", "minVelocity", "min", "max", "checkSelector", "element", "selectors", "executeOnSingleOrMultiple", "selector", "matches", "some", "t", "isSsr", "window", "document", "hasMatchMedia", "matchMedia", "safeMatchMedia", "query", "safeIntersectionObserver", "callback", "IntersectionObserver", "safeMutationObserver", "MutationObserver", "isInArray", "value", "array", "invalidIndex", "indexOf", "loadFont", "font", "weight", "fonts", "load", "arrayRandomIndex", "Math", "floor", "length", "itemFromArray", "index", "useIndex", "undefined", "isPointInside", "point", "size", "offset", "radius", "direction", "minRadius", "areBoundsInside", "calculateBounds", "bounds", "inside", "bottom", "top", "height", "x", "left", "right", "width", "y", "deepExtend", "destination", "sources", "source", "sourceIsArray", "Array", "key", "sourceDict", "destDict", "map", "v", "isDivModeEnabled", "mode", "divs", "findItemFromSingleOrMultiple", "enable", "divModeExecute", "div", "divMode", "divEnabled", "singleDivModeExecute", "circleBounceDataFromParticle", "p", "position", "getPosition", "getRadius", "mass", "getMass", "create", "options", "bounce", "horizontal", "vertical", "circleBounce", "p1", "p2", "xVelocityDiff", "yVelocityDiff", "sub", "pos1", "pos2", "dx", "xDist", "dy", "yDist", "minimumDistance", "angle", "atan2", "m1", "m2", "u1", "rotate", "u2", "v1", "v2", "vFinal1", "vFinal2", "rectBounce", "particle", "divBounds", "pPos", "bounceOptions", "resH", "resV", "obj", "defaultIndex", "item", "itemFromSingleOrMultiple", "find", "initParticleNumericAnimationValue", "pxRatio", "valueRange", "animationOptions", "animation", "delayTime", "delay", "loops", "max<PERSON><PERSON>s", "count", "time", "decayOffset", "decay", "increase", "status", "increasing", "decrease", "decreasing", "random", "autoStatus", "auto", "startValue", "initialValue", "getPositionOrSize", "positionOrSize", "canvasSize", "isPercent", "percent", "_", "rest", "isPosition", "getSize", "checkDestroy", "destroyType", "minValue", "maxValue", "destroy", "updateAnimation", "changeDirection", "delta", "minLoops", "min<PERSON>elay", "identity", "min<PERSON><PERSON><PERSON>", "destroyed"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/engine/browser/Utils/Utils.js"], "sourcesContent": ["import { clamp, collisionVelocity, getDistances, getRandom, getRangeMax, getRangeMin, getRangeValue, randomInRange, } from \"./NumberUtils.js\";\nimport { halfRandom, millisecondsToSeconds, percentDenominator } from \"../Core/Utils/Constants.js\";\nimport { isArray, isObject } from \"./TypeUtils.js\";\nimport { AnimationMode } from \"../Enums/Modes/AnimationMode.js\";\nimport { AnimationStatus } from \"../Enums/AnimationStatus.js\";\nimport { DestroyType } from \"../Enums/Types/DestroyType.js\";\nimport { OutModeDirection } from \"../Enums/Directions/OutModeDirection.js\";\nimport { PixelMode } from \"../Enums/Modes/PixelMode.js\";\nimport { StartValueType } from \"../Enums/Types/StartValueType.js\";\nimport { Vector } from \"../Core/Utils/Vectors.js\";\nconst _logger = {\n    debug: console.debug,\n    error: console.error,\n    info: console.info,\n    log: console.log,\n    verbose: console.log,\n    warning: console.warn,\n};\nexport function setLogger(logger) {\n    _logger.debug = logger.debug || _logger.debug;\n    _logger.error = logger.error || _logger.error;\n    _logger.info = logger.info || _logger.info;\n    _logger.log = logger.log || _logger.log;\n    _logger.verbose = logger.verbose || _logger.verbose;\n    _logger.warning = logger.warning || _logger.warning;\n}\nexport function getLogger() {\n    return _logger;\n}\nfunction rectSideBounce(data) {\n    const res = { bounced: false }, { pSide, pOtherSide, rectSide, rectOtherSide, velocity, factor } = data, half = 0.5, minVelocity = 0;\n    if (pOtherSide.min < rectOtherSide.min ||\n        pOtherSide.min > rectOtherSide.max ||\n        pOtherSide.max < rectOtherSide.min ||\n        pOtherSide.max > rectOtherSide.max) {\n        return res;\n    }\n    if ((pSide.max >= rectSide.min && pSide.max <= (rectSide.max + rectSide.min) * half && velocity > minVelocity) ||\n        (pSide.min <= rectSide.max && pSide.min > (rectSide.max + rectSide.min) * half && velocity < minVelocity)) {\n        res.velocity = velocity * -factor;\n        res.bounced = true;\n    }\n    return res;\n}\nfunction checkSelector(element, selectors) {\n    const res = executeOnSingleOrMultiple(selectors, selector => {\n        return element.matches(selector);\n    });\n    return isArray(res) ? res.some(t => t) : res;\n}\nexport function isSsr() {\n    return typeof window === \"undefined\" || !window || typeof window.document === \"undefined\" || !window.document;\n}\nexport function hasMatchMedia() {\n    return !isSsr() && typeof matchMedia !== \"undefined\";\n}\nexport function safeMatchMedia(query) {\n    if (!hasMatchMedia()) {\n        return;\n    }\n    return matchMedia(query);\n}\nexport function safeIntersectionObserver(callback) {\n    if (isSsr() || typeof IntersectionObserver === \"undefined\") {\n        return;\n    }\n    return new IntersectionObserver(callback);\n}\nexport function safeMutationObserver(callback) {\n    if (isSsr() || typeof MutationObserver === \"undefined\") {\n        return;\n    }\n    return new MutationObserver(callback);\n}\nexport function isInArray(value, array) {\n    const invalidIndex = -1;\n    return value === array || (isArray(array) && array.indexOf(value) > invalidIndex);\n}\nexport async function loadFont(font, weight) {\n    try {\n        await document.fonts.load(`${weight ?? \"400\"} 36px '${font ?? \"Verdana\"}'`);\n    }\n    catch {\n    }\n}\nexport function arrayRandomIndex(array) {\n    return Math.floor(getRandom() * array.length);\n}\nexport function itemFromArray(array, index, useIndex = true) {\n    return array[index !== undefined && useIndex ? index % array.length : arrayRandomIndex(array)];\n}\nexport function isPointInside(point, size, offset, radius, direction) {\n    const minRadius = 0;\n    return areBoundsInside(calculateBounds(point, radius ?? minRadius), size, offset, direction);\n}\nexport function areBoundsInside(bounds, size, offset, direction) {\n    let inside = true;\n    if (!direction || direction === OutModeDirection.bottom) {\n        inside = bounds.top < size.height + offset.x;\n    }\n    if (inside && (!direction || direction === OutModeDirection.left)) {\n        inside = bounds.right > offset.x;\n    }\n    if (inside && (!direction || direction === OutModeDirection.right)) {\n        inside = bounds.left < size.width + offset.y;\n    }\n    if (inside && (!direction || direction === OutModeDirection.top)) {\n        inside = bounds.bottom > offset.y;\n    }\n    return inside;\n}\nexport function calculateBounds(point, radius) {\n    return {\n        bottom: point.y + radius,\n        left: point.x - radius,\n        right: point.x + radius,\n        top: point.y - radius,\n    };\n}\nexport function deepExtend(destination, ...sources) {\n    for (const source of sources) {\n        if (source === undefined || source === null) {\n            continue;\n        }\n        if (!isObject(source)) {\n            destination = source;\n            continue;\n        }\n        const sourceIsArray = Array.isArray(source);\n        if (sourceIsArray && (isObject(destination) || !destination || !Array.isArray(destination))) {\n            destination = [];\n        }\n        else if (!sourceIsArray && (isObject(destination) || !destination || Array.isArray(destination))) {\n            destination = {};\n        }\n        for (const key in source) {\n            if (key === \"__proto__\") {\n                continue;\n            }\n            const sourceDict = source, value = sourceDict[key], destDict = destination;\n            destDict[key] =\n                isObject(value) && Array.isArray(value)\n                    ? value.map(v => deepExtend(destDict[key], v))\n                    : deepExtend(destDict[key], value);\n        }\n    }\n    return destination;\n}\nexport function isDivModeEnabled(mode, divs) {\n    return !!findItemFromSingleOrMultiple(divs, t => t.enable && isInArray(mode, t.mode));\n}\nexport function divModeExecute(mode, divs, callback) {\n    executeOnSingleOrMultiple(divs, div => {\n        const divMode = div.mode, divEnabled = div.enable;\n        if (divEnabled && isInArray(mode, divMode)) {\n            singleDivModeExecute(div, callback);\n        }\n    });\n}\nexport function singleDivModeExecute(div, callback) {\n    const selectors = div.selectors;\n    executeOnSingleOrMultiple(selectors, selector => {\n        callback(selector, div);\n    });\n}\nexport function divMode(divs, element) {\n    if (!element || !divs) {\n        return;\n    }\n    return findItemFromSingleOrMultiple(divs, div => {\n        return checkSelector(element, div.selectors);\n    });\n}\nexport function circleBounceDataFromParticle(p) {\n    return {\n        position: p.getPosition(),\n        radius: p.getRadius(),\n        mass: p.getMass(),\n        velocity: p.velocity,\n        factor: Vector.create(getRangeValue(p.options.bounce.horizontal.value), getRangeValue(p.options.bounce.vertical.value)),\n    };\n}\nexport function circleBounce(p1, p2) {\n    const { x: xVelocityDiff, y: yVelocityDiff } = p1.velocity.sub(p2.velocity), [pos1, pos2] = [p1.position, p2.position], { dx: xDist, dy: yDist } = getDistances(pos2, pos1), minimumDistance = 0;\n    if (xVelocityDiff * xDist + yVelocityDiff * yDist < minimumDistance) {\n        return;\n    }\n    const angle = -Math.atan2(yDist, xDist), m1 = p1.mass, m2 = p2.mass, u1 = p1.velocity.rotate(angle), u2 = p2.velocity.rotate(angle), v1 = collisionVelocity(u1, u2, m1, m2), v2 = collisionVelocity(u2, u1, m1, m2), vFinal1 = v1.rotate(-angle), vFinal2 = v2.rotate(-angle);\n    p1.velocity.x = vFinal1.x * p1.factor.x;\n    p1.velocity.y = vFinal1.y * p1.factor.y;\n    p2.velocity.x = vFinal2.x * p2.factor.x;\n    p2.velocity.y = vFinal2.y * p2.factor.y;\n}\nexport function rectBounce(particle, divBounds) {\n    const pPos = particle.getPosition(), size = particle.getRadius(), bounds = calculateBounds(pPos, size), bounceOptions = particle.options.bounce, resH = rectSideBounce({\n        pSide: {\n            min: bounds.left,\n            max: bounds.right,\n        },\n        pOtherSide: {\n            min: bounds.top,\n            max: bounds.bottom,\n        },\n        rectSide: {\n            min: divBounds.left,\n            max: divBounds.right,\n        },\n        rectOtherSide: {\n            min: divBounds.top,\n            max: divBounds.bottom,\n        },\n        velocity: particle.velocity.x,\n        factor: getRangeValue(bounceOptions.horizontal.value),\n    });\n    if (resH.bounced) {\n        if (resH.velocity !== undefined) {\n            particle.velocity.x = resH.velocity;\n        }\n        if (resH.position !== undefined) {\n            particle.position.x = resH.position;\n        }\n    }\n    const resV = rectSideBounce({\n        pSide: {\n            min: bounds.top,\n            max: bounds.bottom,\n        },\n        pOtherSide: {\n            min: bounds.left,\n            max: bounds.right,\n        },\n        rectSide: {\n            min: divBounds.top,\n            max: divBounds.bottom,\n        },\n        rectOtherSide: {\n            min: divBounds.left,\n            max: divBounds.right,\n        },\n        velocity: particle.velocity.y,\n        factor: getRangeValue(bounceOptions.vertical.value),\n    });\n    if (resV.bounced) {\n        if (resV.velocity !== undefined) {\n            particle.velocity.y = resV.velocity;\n        }\n        if (resV.position !== undefined) {\n            particle.position.y = resV.position;\n        }\n    }\n}\nexport function executeOnSingleOrMultiple(obj, callback) {\n    const defaultIndex = 0;\n    return isArray(obj) ? obj.map((item, index) => callback(item, index)) : callback(obj, defaultIndex);\n}\nexport function itemFromSingleOrMultiple(obj, index, useIndex) {\n    return isArray(obj) ? itemFromArray(obj, index, useIndex) : obj;\n}\nexport function findItemFromSingleOrMultiple(obj, callback) {\n    if (isArray(obj)) {\n        return obj.find((t, index) => callback(t, index));\n    }\n    const defaultIndex = 0;\n    return callback(obj, defaultIndex) ? obj : undefined;\n}\nexport function initParticleNumericAnimationValue(options, pxRatio) {\n    const valueRange = options.value, animationOptions = options.animation, res = {\n        delayTime: getRangeValue(animationOptions.delay) * millisecondsToSeconds,\n        enable: animationOptions.enable,\n        value: getRangeValue(options.value) * pxRatio,\n        max: getRangeMax(valueRange) * pxRatio,\n        min: getRangeMin(valueRange) * pxRatio,\n        loops: 0,\n        maxLoops: getRangeValue(animationOptions.count),\n        time: 0,\n    }, decayOffset = 1;\n    if (animationOptions.enable) {\n        res.decay = decayOffset - getRangeValue(animationOptions.decay);\n        switch (animationOptions.mode) {\n            case AnimationMode.increase:\n                res.status = AnimationStatus.increasing;\n                break;\n            case AnimationMode.decrease:\n                res.status = AnimationStatus.decreasing;\n                break;\n            case AnimationMode.random:\n                res.status = getRandom() >= halfRandom ? AnimationStatus.increasing : AnimationStatus.decreasing;\n                break;\n        }\n        const autoStatus = animationOptions.mode === AnimationMode.auto;\n        switch (animationOptions.startValue) {\n            case StartValueType.min:\n                res.value = res.min;\n                if (autoStatus) {\n                    res.status = AnimationStatus.increasing;\n                }\n                break;\n            case StartValueType.max:\n                res.value = res.max;\n                if (autoStatus) {\n                    res.status = AnimationStatus.decreasing;\n                }\n                break;\n            case StartValueType.random:\n            default:\n                res.value = randomInRange(res);\n                if (autoStatus) {\n                    res.status = getRandom() >= halfRandom ? AnimationStatus.increasing : AnimationStatus.decreasing;\n                }\n                break;\n        }\n    }\n    res.initialValue = res.value;\n    return res;\n}\nfunction getPositionOrSize(positionOrSize, canvasSize) {\n    const isPercent = positionOrSize.mode === PixelMode.percent;\n    if (!isPercent) {\n        const { mode: _, ...rest } = positionOrSize;\n        return rest;\n    }\n    const isPosition = \"x\" in positionOrSize;\n    if (isPosition) {\n        return {\n            x: (positionOrSize.x / percentDenominator) * canvasSize.width,\n            y: (positionOrSize.y / percentDenominator) * canvasSize.height,\n        };\n    }\n    else {\n        return {\n            width: (positionOrSize.width / percentDenominator) * canvasSize.width,\n            height: (positionOrSize.height / percentDenominator) * canvasSize.height,\n        };\n    }\n}\nexport function getPosition(position, canvasSize) {\n    return getPositionOrSize(position, canvasSize);\n}\nexport function getSize(size, canvasSize) {\n    return getPositionOrSize(size, canvasSize);\n}\nfunction checkDestroy(particle, destroyType, value, minValue, maxValue) {\n    switch (destroyType) {\n        case DestroyType.max:\n            if (value >= maxValue) {\n                particle.destroy();\n            }\n            break;\n        case DestroyType.min:\n            if (value <= minValue) {\n                particle.destroy();\n            }\n            break;\n    }\n}\nexport function updateAnimation(particle, data, changeDirection, destroyType, delta) {\n    const minLoops = 0, minDelay = 0, identity = 1, minVelocity = 0, minDecay = 1;\n    if (particle.destroyed ||\n        !data ||\n        !data.enable ||\n        ((data.maxLoops ?? minLoops) > minLoops && (data.loops ?? minLoops) > (data.maxLoops ?? minLoops))) {\n        return;\n    }\n    const velocity = (data.velocity ?? minVelocity) * delta.factor, minValue = data.min, maxValue = data.max, decay = data.decay ?? minDecay;\n    if (!data.time) {\n        data.time = 0;\n    }\n    if ((data.delayTime ?? minDelay) > minDelay && data.time < (data.delayTime ?? minDelay)) {\n        data.time += delta.value;\n    }\n    if ((data.delayTime ?? minDelay) > minDelay && data.time < (data.delayTime ?? minDelay)) {\n        return;\n    }\n    switch (data.status) {\n        case AnimationStatus.increasing:\n            if (data.value >= maxValue) {\n                if (changeDirection) {\n                    data.status = AnimationStatus.decreasing;\n                }\n                else {\n                    data.value -= maxValue;\n                }\n                if (!data.loops) {\n                    data.loops = minLoops;\n                }\n                data.loops++;\n            }\n            else {\n                data.value += velocity;\n            }\n            break;\n        case AnimationStatus.decreasing:\n            if (data.value <= minValue) {\n                if (changeDirection) {\n                    data.status = AnimationStatus.increasing;\n                }\n                else {\n                    data.value += maxValue;\n                }\n                if (!data.loops) {\n                    data.loops = minLoops;\n                }\n                data.loops++;\n            }\n            else {\n                data.value -= velocity;\n            }\n    }\n    if (data.velocity && decay !== identity) {\n        data.velocity *= decay;\n    }\n    checkDestroy(particle, destroyType, data.value, minValue, maxValue);\n    if (!particle.destroyed) {\n        data.value = clamp(data.value, minValue, maxValue);\n    }\n}\n"], "mappings": "AAAA,SAASA,KAAK,EAAEC,iBAAiB,EAAEC,YAAY,EAAEC,SAAS,EAAEC,WAAW,EAAEC,WAAW,EAAEC,aAAa,EAAEC,aAAa,QAAS,kBAAkB;AAC7I,SAASC,UAAU,EAAEC,qBAAqB,EAAEC,kBAAkB,QAAQ,4BAA4B;AAClG,SAASC,OAAO,EAAEC,QAAQ,QAAQ,gBAAgB;AAClD,SAASC,aAAa,QAAQ,iCAAiC;AAC/D,SAASC,eAAe,QAAQ,6BAA6B;AAC7D,SAASC,WAAW,QAAQ,+BAA+B;AAC3D,SAASC,gBAAgB,QAAQ,yCAAyC;AAC1E,SAASC,SAAS,QAAQ,6BAA6B;AACvD,SAASC,cAAc,QAAQ,kCAAkC;AACjE,SAASC,MAAM,QAAQ,0BAA0B;AACjD,MAAMC,OAAO,GAAG;EACZC,KAAK,EAAEC,OAAO,CAACD,KAAK;EACpBE,KAAK,EAAED,OAAO,CAACC,KAAK;EACpBC,IAAI,EAAEF,OAAO,CAACE,IAAI;EAClBC,GAAG,EAAEH,OAAO,CAACG,GAAG;EAChBC,OAAO,EAAEJ,OAAO,CAACG,GAAG;EACpBE,OAAO,EAAEL,OAAO,CAACM;AACrB,CAAC;AACD,OAAO,SAASC,SAASA,CAACC,MAAM,EAAE;EAC9BV,OAAO,CAACC,KAAK,GAAGS,MAAM,CAACT,KAAK,IAAID,OAAO,CAACC,KAAK;EAC7CD,OAAO,CAACG,KAAK,GAAGO,MAAM,CAACP,KAAK,IAAIH,OAAO,CAACG,KAAK;EAC7CH,OAAO,CAACI,IAAI,GAAGM,MAAM,CAACN,IAAI,IAAIJ,OAAO,CAACI,IAAI;EAC1CJ,OAAO,CAACK,GAAG,GAAGK,MAAM,CAACL,GAAG,IAAIL,OAAO,CAACK,GAAG;EACvCL,OAAO,CAACM,OAAO,GAAGI,MAAM,CAACJ,OAAO,IAAIN,OAAO,CAACM,OAAO;EACnDN,OAAO,CAACO,OAAO,GAAGG,MAAM,CAACH,OAAO,IAAIP,OAAO,CAACO,OAAO;AACvD;AACA,OAAO,SAASI,SAASA,CAAA,EAAG;EACxB,OAAOX,OAAO;AAClB;AACA,SAASY,cAAcA,CAACC,IAAI,EAAE;EAC1B,MAAMC,GAAG,GAAG;MAAEC,OAAO,EAAE;IAAM,CAAC;IAAE;MAAEC,KAAK;MAAEC,UAAU;MAAEC,QAAQ;MAAEC,aAAa;MAAEC,QAAQ;MAAEC;IAAO,CAAC,GAAGR,IAAI;IAAES,IAAI,GAAG,GAAG;IAAEC,WAAW,GAAG,CAAC;EACpI,IAAIN,UAAU,CAACO,GAAG,GAAGL,aAAa,CAACK,GAAG,IAClCP,UAAU,CAACO,GAAG,GAAGL,aAAa,CAACM,GAAG,IAClCR,UAAU,CAACQ,GAAG,GAAGN,aAAa,CAACK,GAAG,IAClCP,UAAU,CAACQ,GAAG,GAAGN,aAAa,CAACM,GAAG,EAAE;IACpC,OAAOX,GAAG;EACd;EACA,IAAKE,KAAK,CAACS,GAAG,IAAIP,QAAQ,CAACM,GAAG,IAAIR,KAAK,CAACS,GAAG,IAAI,CAACP,QAAQ,CAACO,GAAG,GAAGP,QAAQ,CAACM,GAAG,IAAIF,IAAI,IAAIF,QAAQ,GAAGG,WAAW,IACxGP,KAAK,CAACQ,GAAG,IAAIN,QAAQ,CAACO,GAAG,IAAIT,KAAK,CAACQ,GAAG,GAAG,CAACN,QAAQ,CAACO,GAAG,GAAGP,QAAQ,CAACM,GAAG,IAAIF,IAAI,IAAIF,QAAQ,GAAGG,WAAY,EAAE;IAC3GT,GAAG,CAACM,QAAQ,GAAGA,QAAQ,GAAG,CAACC,MAAM;IACjCP,GAAG,CAACC,OAAO,GAAG,IAAI;EACtB;EACA,OAAOD,GAAG;AACd;AACA,SAASY,aAAaA,CAACC,OAAO,EAAEC,SAAS,EAAE;EACvC,MAAMd,GAAG,GAAGe,yBAAyB,CAACD,SAAS,EAAEE,QAAQ,IAAI;IACzD,OAAOH,OAAO,CAACI,OAAO,CAACD,QAAQ,CAAC;EACpC,CAAC,CAAC;EACF,OAAOvC,OAAO,CAACuB,GAAG,CAAC,GAAGA,GAAG,CAACkB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC,GAAGnB,GAAG;AAChD;AACA,OAAO,SAASoB,KAAKA,CAAA,EAAG;EACpB,OAAO,OAAOC,MAAM,KAAK,WAAW,IAAI,CAACA,MAAM,IAAI,OAAOA,MAAM,CAACC,QAAQ,KAAK,WAAW,IAAI,CAACD,MAAM,CAACC,QAAQ;AACjH;AACA,OAAO,SAASC,aAAaA,CAAA,EAAG;EAC5B,OAAO,CAACH,KAAK,CAAC,CAAC,IAAI,OAAOI,UAAU,KAAK,WAAW;AACxD;AACA,OAAO,SAASC,cAAcA,CAACC,KAAK,EAAE;EAClC,IAAI,CAACH,aAAa,CAAC,CAAC,EAAE;IAClB;EACJ;EACA,OAAOC,UAAU,CAACE,KAAK,CAAC;AAC5B;AACA,OAAO,SAASC,wBAAwBA,CAACC,QAAQ,EAAE;EAC/C,IAAIR,KAAK,CAAC,CAAC,IAAI,OAAOS,oBAAoB,KAAK,WAAW,EAAE;IACxD;EACJ;EACA,OAAO,IAAIA,oBAAoB,CAACD,QAAQ,CAAC;AAC7C;AACA,OAAO,SAASE,oBAAoBA,CAACF,QAAQ,EAAE;EAC3C,IAAIR,KAAK,CAAC,CAAC,IAAI,OAAOW,gBAAgB,KAAK,WAAW,EAAE;IACpD;EACJ;EACA,OAAO,IAAIA,gBAAgB,CAACH,QAAQ,CAAC;AACzC;AACA,OAAO,SAASI,SAASA,CAACC,KAAK,EAAEC,KAAK,EAAE;EACpC,MAAMC,YAAY,GAAG,CAAC,CAAC;EACvB,OAAOF,KAAK,KAAKC,KAAK,IAAKzD,OAAO,CAACyD,KAAK,CAAC,IAAIA,KAAK,CAACE,OAAO,CAACH,KAAK,CAAC,GAAGE,YAAa;AACrF;AACA,OAAO,eAAeE,QAAQA,CAACC,IAAI,EAAEC,MAAM,EAAE;EACzC,IAAI;IACA,MAAMjB,QAAQ,CAACkB,KAAK,CAACC,IAAI,CAAC,GAAGF,MAAM,IAAI,KAAK,UAAUD,IAAI,IAAI,SAAS,GAAG,CAAC;EAC/E,CAAC,CACD,MAAM,CACN;AACJ;AACA,OAAO,SAASI,gBAAgBA,CAACR,KAAK,EAAE;EACpC,OAAOS,IAAI,CAACC,KAAK,CAAC3E,SAAS,CAAC,CAAC,GAAGiE,KAAK,CAACW,MAAM,CAAC;AACjD;AACA,OAAO,SAASC,aAAaA,CAACZ,KAAK,EAAEa,KAAK,EAAEC,QAAQ,GAAG,IAAI,EAAE;EACzD,OAAOd,KAAK,CAACa,KAAK,KAAKE,SAAS,IAAID,QAAQ,GAAGD,KAAK,GAAGb,KAAK,CAACW,MAAM,GAAGH,gBAAgB,CAACR,KAAK,CAAC,CAAC;AAClG;AACA,OAAO,SAASgB,aAAaA,CAACC,KAAK,EAAEC,IAAI,EAAEC,MAAM,EAAEC,MAAM,EAAEC,SAAS,EAAE;EAClE,MAAMC,SAAS,GAAG,CAAC;EACnB,OAAOC,eAAe,CAACC,eAAe,CAACP,KAAK,EAAEG,MAAM,IAAIE,SAAS,CAAC,EAAEJ,IAAI,EAAEC,MAAM,EAAEE,SAAS,CAAC;AAChG;AACA,OAAO,SAASE,eAAeA,CAACE,MAAM,EAAEP,IAAI,EAAEC,MAAM,EAAEE,SAAS,EAAE;EAC7D,IAAIK,MAAM,GAAG,IAAI;EACjB,IAAI,CAACL,SAAS,IAAIA,SAAS,KAAKzE,gBAAgB,CAAC+E,MAAM,EAAE;IACrDD,MAAM,GAAGD,MAAM,CAACG,GAAG,GAAGV,IAAI,CAACW,MAAM,GAAGV,MAAM,CAACW,CAAC;EAChD;EACA,IAAIJ,MAAM,KAAK,CAACL,SAAS,IAAIA,SAAS,KAAKzE,gBAAgB,CAACmF,IAAI,CAAC,EAAE;IAC/DL,MAAM,GAAGD,MAAM,CAACO,KAAK,GAAGb,MAAM,CAACW,CAAC;EACpC;EACA,IAAIJ,MAAM,KAAK,CAACL,SAAS,IAAIA,SAAS,KAAKzE,gBAAgB,CAACoF,KAAK,CAAC,EAAE;IAChEN,MAAM,GAAGD,MAAM,CAACM,IAAI,GAAGb,IAAI,CAACe,KAAK,GAAGd,MAAM,CAACe,CAAC;EAChD;EACA,IAAIR,MAAM,KAAK,CAACL,SAAS,IAAIA,SAAS,KAAKzE,gBAAgB,CAACgF,GAAG,CAAC,EAAE;IAC9DF,MAAM,GAAGD,MAAM,CAACE,MAAM,GAAGR,MAAM,CAACe,CAAC;EACrC;EACA,OAAOR,MAAM;AACjB;AACA,OAAO,SAASF,eAAeA,CAACP,KAAK,EAAEG,MAAM,EAAE;EAC3C,OAAO;IACHO,MAAM,EAAEV,KAAK,CAACiB,CAAC,GAAGd,MAAM;IACxBW,IAAI,EAAEd,KAAK,CAACa,CAAC,GAAGV,MAAM;IACtBY,KAAK,EAAEf,KAAK,CAACa,CAAC,GAAGV,MAAM;IACvBQ,GAAG,EAAEX,KAAK,CAACiB,CAAC,GAAGd;EACnB,CAAC;AACL;AACA,OAAO,SAASe,UAAUA,CAACC,WAAW,EAAE,GAAGC,OAAO,EAAE;EAChD,KAAK,MAAMC,MAAM,IAAID,OAAO,EAAE;IAC1B,IAAIC,MAAM,KAAKvB,SAAS,IAAIuB,MAAM,KAAK,IAAI,EAAE;MACzC;IACJ;IACA,IAAI,CAAC9F,QAAQ,CAAC8F,MAAM,CAAC,EAAE;MACnBF,WAAW,GAAGE,MAAM;MACpB;IACJ;IACA,MAAMC,aAAa,GAAGC,KAAK,CAACjG,OAAO,CAAC+F,MAAM,CAAC;IAC3C,IAAIC,aAAa,KAAK/F,QAAQ,CAAC4F,WAAW,CAAC,IAAI,CAACA,WAAW,IAAI,CAACI,KAAK,CAACjG,OAAO,CAAC6F,WAAW,CAAC,CAAC,EAAE;MACzFA,WAAW,GAAG,EAAE;IACpB,CAAC,MACI,IAAI,CAACG,aAAa,KAAK/F,QAAQ,CAAC4F,WAAW,CAAC,IAAI,CAACA,WAAW,IAAII,KAAK,CAACjG,OAAO,CAAC6F,WAAW,CAAC,CAAC,EAAE;MAC9FA,WAAW,GAAG,CAAC,CAAC;IACpB;IACA,KAAK,MAAMK,GAAG,IAAIH,MAAM,EAAE;MACtB,IAAIG,GAAG,KAAK,WAAW,EAAE;QACrB;MACJ;MACA,MAAMC,UAAU,GAAGJ,MAAM;QAAEvC,KAAK,GAAG2C,UAAU,CAACD,GAAG,CAAC;QAAEE,QAAQ,GAAGP,WAAW;MAC1EO,QAAQ,CAACF,GAAG,CAAC,GACTjG,QAAQ,CAACuD,KAAK,CAAC,IAAIyC,KAAK,CAACjG,OAAO,CAACwD,KAAK,CAAC,GACjCA,KAAK,CAAC6C,GAAG,CAACC,CAAC,IAAIV,UAAU,CAACQ,QAAQ,CAACF,GAAG,CAAC,EAAEI,CAAC,CAAC,CAAC,GAC5CV,UAAU,CAACQ,QAAQ,CAACF,GAAG,CAAC,EAAE1C,KAAK,CAAC;IAC9C;EACJ;EACA,OAAOqC,WAAW;AACtB;AACA,OAAO,SAASU,gBAAgBA,CAACC,IAAI,EAAEC,IAAI,EAAE;EACzC,OAAO,CAAC,CAACC,4BAA4B,CAACD,IAAI,EAAE/D,CAAC,IAAIA,CAAC,CAACiE,MAAM,IAAIpD,SAAS,CAACiD,IAAI,EAAE9D,CAAC,CAAC8D,IAAI,CAAC,CAAC;AACzF;AACA,OAAO,SAASI,cAAcA,CAACJ,IAAI,EAAEC,IAAI,EAAEtD,QAAQ,EAAE;EACjDb,yBAAyB,CAACmE,IAAI,EAAEI,GAAG,IAAI;IACnC,MAAMC,OAAO,GAAGD,GAAG,CAACL,IAAI;MAAEO,UAAU,GAAGF,GAAG,CAACF,MAAM;IACjD,IAAII,UAAU,IAAIxD,SAAS,CAACiD,IAAI,EAAEM,OAAO,CAAC,EAAE;MACxCE,oBAAoB,CAACH,GAAG,EAAE1D,QAAQ,CAAC;IACvC;EACJ,CAAC,CAAC;AACN;AACA,OAAO,SAAS6D,oBAAoBA,CAACH,GAAG,EAAE1D,QAAQ,EAAE;EAChD,MAAMd,SAAS,GAAGwE,GAAG,CAACxE,SAAS;EAC/BC,yBAAyB,CAACD,SAAS,EAAEE,QAAQ,IAAI;IAC7CY,QAAQ,CAACZ,QAAQ,EAAEsE,GAAG,CAAC;EAC3B,CAAC,CAAC;AACN;AACA,OAAO,SAASC,OAAOA,CAACL,IAAI,EAAErE,OAAO,EAAE;EACnC,IAAI,CAACA,OAAO,IAAI,CAACqE,IAAI,EAAE;IACnB;EACJ;EACA,OAAOC,4BAA4B,CAACD,IAAI,EAAEI,GAAG,IAAI;IAC7C,OAAO1E,aAAa,CAACC,OAAO,EAAEyE,GAAG,CAACxE,SAAS,CAAC;EAChD,CAAC,CAAC;AACN;AACA,OAAO,SAAS4E,4BAA4BA,CAACC,CAAC,EAAE;EAC5C,OAAO;IACHC,QAAQ,EAAED,CAAC,CAACE,WAAW,CAAC,CAAC;IACzBvC,MAAM,EAAEqC,CAAC,CAACG,SAAS,CAAC,CAAC;IACrBC,IAAI,EAAEJ,CAAC,CAACK,OAAO,CAAC,CAAC;IACjB1F,QAAQ,EAAEqF,CAAC,CAACrF,QAAQ;IACpBC,MAAM,EAAEtB,MAAM,CAACgH,MAAM,CAAC7H,aAAa,CAACuH,CAAC,CAACO,OAAO,CAACC,MAAM,CAACC,UAAU,CAACnE,KAAK,CAAC,EAAE7D,aAAa,CAACuH,CAAC,CAACO,OAAO,CAACC,MAAM,CAACE,QAAQ,CAACpE,KAAK,CAAC;EAC1H,CAAC;AACL;AACA,OAAO,SAASqE,YAAYA,CAACC,EAAE,EAAEC,EAAE,EAAE;EACjC,MAAM;MAAExC,CAAC,EAAEyC,aAAa;MAAErC,CAAC,EAAEsC;IAAc,CAAC,GAAGH,EAAE,CAACjG,QAAQ,CAACqG,GAAG,CAACH,EAAE,CAAClG,QAAQ,CAAC;IAAE,CAACsG,IAAI,EAAEC,IAAI,CAAC,GAAG,CAACN,EAAE,CAACX,QAAQ,EAAEY,EAAE,CAACZ,QAAQ,CAAC;IAAE;MAAEkB,EAAE,EAAEC,KAAK;MAAEC,EAAE,EAAEC;IAAM,CAAC,GAAGjJ,YAAY,CAAC6I,IAAI,EAAED,IAAI,CAAC;IAAEM,eAAe,GAAG,CAAC;EAChM,IAAIT,aAAa,GAAGM,KAAK,GAAGL,aAAa,GAAGO,KAAK,GAAGC,eAAe,EAAE;IACjE;EACJ;EACA,MAAMC,KAAK,GAAG,CAACxE,IAAI,CAACyE,KAAK,CAACH,KAAK,EAAEF,KAAK,CAAC;IAAEM,EAAE,GAAGd,EAAE,CAACR,IAAI;IAAEuB,EAAE,GAAGd,EAAE,CAACT,IAAI;IAAEwB,EAAE,GAAGhB,EAAE,CAACjG,QAAQ,CAACkH,MAAM,CAACL,KAAK,CAAC;IAAEM,EAAE,GAAGjB,EAAE,CAAClG,QAAQ,CAACkH,MAAM,CAACL,KAAK,CAAC;IAAEO,EAAE,GAAG3J,iBAAiB,CAACwJ,EAAE,EAAEE,EAAE,EAAEJ,EAAE,EAAEC,EAAE,CAAC;IAAEK,EAAE,GAAG5J,iBAAiB,CAAC0J,EAAE,EAAEF,EAAE,EAAEF,EAAE,EAAEC,EAAE,CAAC;IAAEM,OAAO,GAAGF,EAAE,CAACF,MAAM,CAAC,CAACL,KAAK,CAAC;IAAEU,OAAO,GAAGF,EAAE,CAACH,MAAM,CAAC,CAACL,KAAK,CAAC;EAC7QZ,EAAE,CAACjG,QAAQ,CAAC0D,CAAC,GAAG4D,OAAO,CAAC5D,CAAC,GAAGuC,EAAE,CAAChG,MAAM,CAACyD,CAAC;EACvCuC,EAAE,CAACjG,QAAQ,CAAC8D,CAAC,GAAGwD,OAAO,CAACxD,CAAC,GAAGmC,EAAE,CAAChG,MAAM,CAAC6D,CAAC;EACvCoC,EAAE,CAAClG,QAAQ,CAAC0D,CAAC,GAAG6D,OAAO,CAAC7D,CAAC,GAAGwC,EAAE,CAACjG,MAAM,CAACyD,CAAC;EACvCwC,EAAE,CAAClG,QAAQ,CAAC8D,CAAC,GAAGyD,OAAO,CAACzD,CAAC,GAAGoC,EAAE,CAACjG,MAAM,CAAC6D,CAAC;AAC3C;AACA,OAAO,SAAS0D,UAAUA,CAACC,QAAQ,EAAEC,SAAS,EAAE;EAC5C,MAAMC,IAAI,GAAGF,QAAQ,CAAClC,WAAW,CAAC,CAAC;IAAEzC,IAAI,GAAG2E,QAAQ,CAACjC,SAAS,CAAC,CAAC;IAAEnC,MAAM,GAAGD,eAAe,CAACuE,IAAI,EAAE7E,IAAI,CAAC;IAAE8E,aAAa,GAAGH,QAAQ,CAAC7B,OAAO,CAACC,MAAM;IAAEgC,IAAI,GAAGrI,cAAc,CAAC;MACnKI,KAAK,EAAE;QACHQ,GAAG,EAAEiD,MAAM,CAACM,IAAI;QAChBtD,GAAG,EAAEgD,MAAM,CAACO;MAChB,CAAC;MACD/D,UAAU,EAAE;QACRO,GAAG,EAAEiD,MAAM,CAACG,GAAG;QACfnD,GAAG,EAAEgD,MAAM,CAACE;MAChB,CAAC;MACDzD,QAAQ,EAAE;QACNM,GAAG,EAAEsH,SAAS,CAAC/D,IAAI;QACnBtD,GAAG,EAAEqH,SAAS,CAAC9D;MACnB,CAAC;MACD7D,aAAa,EAAE;QACXK,GAAG,EAAEsH,SAAS,CAAClE,GAAG;QAClBnD,GAAG,EAAEqH,SAAS,CAACnE;MACnB,CAAC;MACDvD,QAAQ,EAAEyH,QAAQ,CAACzH,QAAQ,CAAC0D,CAAC;MAC7BzD,MAAM,EAAEnC,aAAa,CAAC8J,aAAa,CAAC9B,UAAU,CAACnE,KAAK;IACxD,CAAC,CAAC;EACF,IAAIkG,IAAI,CAAClI,OAAO,EAAE;IACd,IAAIkI,IAAI,CAAC7H,QAAQ,KAAK2C,SAAS,EAAE;MAC7B8E,QAAQ,CAACzH,QAAQ,CAAC0D,CAAC,GAAGmE,IAAI,CAAC7H,QAAQ;IACvC;IACA,IAAI6H,IAAI,CAACvC,QAAQ,KAAK3C,SAAS,EAAE;MAC7B8E,QAAQ,CAACnC,QAAQ,CAAC5B,CAAC,GAAGmE,IAAI,CAACvC,QAAQ;IACvC;EACJ;EACA,MAAMwC,IAAI,GAAGtI,cAAc,CAAC;IACxBI,KAAK,EAAE;MACHQ,GAAG,EAAEiD,MAAM,CAACG,GAAG;MACfnD,GAAG,EAAEgD,MAAM,CAACE;IAChB,CAAC;IACD1D,UAAU,EAAE;MACRO,GAAG,EAAEiD,MAAM,CAACM,IAAI;MAChBtD,GAAG,EAAEgD,MAAM,CAACO;IAChB,CAAC;IACD9D,QAAQ,EAAE;MACNM,GAAG,EAAEsH,SAAS,CAAClE,GAAG;MAClBnD,GAAG,EAAEqH,SAAS,CAACnE;IACnB,CAAC;IACDxD,aAAa,EAAE;MACXK,GAAG,EAAEsH,SAAS,CAAC/D,IAAI;MACnBtD,GAAG,EAAEqH,SAAS,CAAC9D;IACnB,CAAC;IACD5D,QAAQ,EAAEyH,QAAQ,CAACzH,QAAQ,CAAC8D,CAAC;IAC7B7D,MAAM,EAAEnC,aAAa,CAAC8J,aAAa,CAAC7B,QAAQ,CAACpE,KAAK;EACtD,CAAC,CAAC;EACF,IAAImG,IAAI,CAACnI,OAAO,EAAE;IACd,IAAImI,IAAI,CAAC9H,QAAQ,KAAK2C,SAAS,EAAE;MAC7B8E,QAAQ,CAACzH,QAAQ,CAAC8D,CAAC,GAAGgE,IAAI,CAAC9H,QAAQ;IACvC;IACA,IAAI8H,IAAI,CAACxC,QAAQ,KAAK3C,SAAS,EAAE;MAC7B8E,QAAQ,CAACnC,QAAQ,CAACxB,CAAC,GAAGgE,IAAI,CAACxC,QAAQ;IACvC;EACJ;AACJ;AACA,OAAO,SAAS7E,yBAAyBA,CAACsH,GAAG,EAAEzG,QAAQ,EAAE;EACrD,MAAM0G,YAAY,GAAG,CAAC;EACtB,OAAO7J,OAAO,CAAC4J,GAAG,CAAC,GAAGA,GAAG,CAACvD,GAAG,CAAC,CAACyD,IAAI,EAAExF,KAAK,KAAKnB,QAAQ,CAAC2G,IAAI,EAAExF,KAAK,CAAC,CAAC,GAAGnB,QAAQ,CAACyG,GAAG,EAAEC,YAAY,CAAC;AACvG;AACA,OAAO,SAASE,wBAAwBA,CAACH,GAAG,EAAEtF,KAAK,EAAEC,QAAQ,EAAE;EAC3D,OAAOvE,OAAO,CAAC4J,GAAG,CAAC,GAAGvF,aAAa,CAACuF,GAAG,EAAEtF,KAAK,EAAEC,QAAQ,CAAC,GAAGqF,GAAG;AACnE;AACA,OAAO,SAASlD,4BAA4BA,CAACkD,GAAG,EAAEzG,QAAQ,EAAE;EACxD,IAAInD,OAAO,CAAC4J,GAAG,CAAC,EAAE;IACd,OAAOA,GAAG,CAACI,IAAI,CAAC,CAACtH,CAAC,EAAE4B,KAAK,KAAKnB,QAAQ,CAACT,CAAC,EAAE4B,KAAK,CAAC,CAAC;EACrD;EACA,MAAMuF,YAAY,GAAG,CAAC;EACtB,OAAO1G,QAAQ,CAACyG,GAAG,EAAEC,YAAY,CAAC,GAAGD,GAAG,GAAGpF,SAAS;AACxD;AACA,OAAO,SAASyF,iCAAiCA,CAACxC,OAAO,EAAEyC,OAAO,EAAE;EAChE,MAAMC,UAAU,GAAG1C,OAAO,CAACjE,KAAK;IAAE4G,gBAAgB,GAAG3C,OAAO,CAAC4C,SAAS;IAAE9I,GAAG,GAAG;MAC1E+I,SAAS,EAAE3K,aAAa,CAACyK,gBAAgB,CAACG,KAAK,CAAC,GAAGzK,qBAAqB;MACxE6G,MAAM,EAAEyD,gBAAgB,CAACzD,MAAM;MAC/BnD,KAAK,EAAE7D,aAAa,CAAC8H,OAAO,CAACjE,KAAK,CAAC,GAAG0G,OAAO;MAC7ChI,GAAG,EAAEzC,WAAW,CAAC0K,UAAU,CAAC,GAAGD,OAAO;MACtCjI,GAAG,EAAEvC,WAAW,CAACyK,UAAU,CAAC,GAAGD,OAAO;MACtCM,KAAK,EAAE,CAAC;MACRC,QAAQ,EAAE9K,aAAa,CAACyK,gBAAgB,CAACM,KAAK,CAAC;MAC/CC,IAAI,EAAE;IACV,CAAC;IAAEC,WAAW,GAAG,CAAC;EAClB,IAAIR,gBAAgB,CAACzD,MAAM,EAAE;IACzBpF,GAAG,CAACsJ,KAAK,GAAGD,WAAW,GAAGjL,aAAa,CAACyK,gBAAgB,CAACS,KAAK,CAAC;IAC/D,QAAQT,gBAAgB,CAAC5D,IAAI;MACzB,KAAKtG,aAAa,CAAC4K,QAAQ;QACvBvJ,GAAG,CAACwJ,MAAM,GAAG5K,eAAe,CAAC6K,UAAU;QACvC;MACJ,KAAK9K,aAAa,CAAC+K,QAAQ;QACvB1J,GAAG,CAACwJ,MAAM,GAAG5K,eAAe,CAAC+K,UAAU;QACvC;MACJ,KAAKhL,aAAa,CAACiL,MAAM;QACrB5J,GAAG,CAACwJ,MAAM,GAAGvL,SAAS,CAAC,CAAC,IAAIK,UAAU,GAAGM,eAAe,CAAC6K,UAAU,GAAG7K,eAAe,CAAC+K,UAAU;QAChG;IACR;IACA,MAAME,UAAU,GAAGhB,gBAAgB,CAAC5D,IAAI,KAAKtG,aAAa,CAACmL,IAAI;IAC/D,QAAQjB,gBAAgB,CAACkB,UAAU;MAC/B,KAAK/K,cAAc,CAAC0B,GAAG;QACnBV,GAAG,CAACiC,KAAK,GAAGjC,GAAG,CAACU,GAAG;QACnB,IAAImJ,UAAU,EAAE;UACZ7J,GAAG,CAACwJ,MAAM,GAAG5K,eAAe,CAAC6K,UAAU;QAC3C;QACA;MACJ,KAAKzK,cAAc,CAAC2B,GAAG;QACnBX,GAAG,CAACiC,KAAK,GAAGjC,GAAG,CAACW,GAAG;QACnB,IAAIkJ,UAAU,EAAE;UACZ7J,GAAG,CAACwJ,MAAM,GAAG5K,eAAe,CAAC+K,UAAU;QAC3C;QACA;MACJ,KAAK3K,cAAc,CAAC4K,MAAM;MAC1B;QACI5J,GAAG,CAACiC,KAAK,GAAG5D,aAAa,CAAC2B,GAAG,CAAC;QAC9B,IAAI6J,UAAU,EAAE;UACZ7J,GAAG,CAACwJ,MAAM,GAAGvL,SAAS,CAAC,CAAC,IAAIK,UAAU,GAAGM,eAAe,CAAC6K,UAAU,GAAG7K,eAAe,CAAC+K,UAAU;QACpG;QACA;IACR;EACJ;EACA3J,GAAG,CAACgK,YAAY,GAAGhK,GAAG,CAACiC,KAAK;EAC5B,OAAOjC,GAAG;AACd;AACA,SAASiK,iBAAiBA,CAACC,cAAc,EAAEC,UAAU,EAAE;EACnD,MAAMC,SAAS,GAAGF,cAAc,CAACjF,IAAI,KAAKlG,SAAS,CAACsL,OAAO;EAC3D,IAAI,CAACD,SAAS,EAAE;IACZ,MAAM;MAAEnF,IAAI,EAAEqF,CAAC;MAAE,GAAGC;IAAK,CAAC,GAAGL,cAAc;IAC3C,OAAOK,IAAI;EACf;EACA,MAAMC,UAAU,GAAG,GAAG,IAAIN,cAAc;EACxC,IAAIM,UAAU,EAAE;IACZ,OAAO;MACHxG,CAAC,EAAGkG,cAAc,CAAClG,CAAC,GAAGxF,kBAAkB,GAAI2L,UAAU,CAAChG,KAAK;MAC7DC,CAAC,EAAG8F,cAAc,CAAC9F,CAAC,GAAG5F,kBAAkB,GAAI2L,UAAU,CAACpG;IAC5D,CAAC;EACL,CAAC,MACI;IACD,OAAO;MACHI,KAAK,EAAG+F,cAAc,CAAC/F,KAAK,GAAG3F,kBAAkB,GAAI2L,UAAU,CAAChG,KAAK;MACrEJ,MAAM,EAAGmG,cAAc,CAACnG,MAAM,GAAGvF,kBAAkB,GAAI2L,UAAU,CAACpG;IACtE,CAAC;EACL;AACJ;AACA,OAAO,SAAS8B,WAAWA,CAACD,QAAQ,EAAEuE,UAAU,EAAE;EAC9C,OAAOF,iBAAiB,CAACrE,QAAQ,EAAEuE,UAAU,CAAC;AAClD;AACA,OAAO,SAASM,OAAOA,CAACrH,IAAI,EAAE+G,UAAU,EAAE;EACtC,OAAOF,iBAAiB,CAAC7G,IAAI,EAAE+G,UAAU,CAAC;AAC9C;AACA,SAASO,YAAYA,CAAC3C,QAAQ,EAAE4C,WAAW,EAAE1I,KAAK,EAAE2I,QAAQ,EAAEC,QAAQ,EAAE;EACpE,QAAQF,WAAW;IACf,KAAK9L,WAAW,CAAC8B,GAAG;MAChB,IAAIsB,KAAK,IAAI4I,QAAQ,EAAE;QACnB9C,QAAQ,CAAC+C,OAAO,CAAC,CAAC;MACtB;MACA;IACJ,KAAKjM,WAAW,CAAC6B,GAAG;MAChB,IAAIuB,KAAK,IAAI2I,QAAQ,EAAE;QACnB7C,QAAQ,CAAC+C,OAAO,CAAC,CAAC;MACtB;MACA;EACR;AACJ;AACA,OAAO,SAASC,eAAeA,CAAChD,QAAQ,EAAEhI,IAAI,EAAEiL,eAAe,EAAEL,WAAW,EAAEM,KAAK,EAAE;EACjF,MAAMC,QAAQ,GAAG,CAAC;IAAEC,QAAQ,GAAG,CAAC;IAAEC,QAAQ,GAAG,CAAC;IAAE3K,WAAW,GAAG,CAAC;IAAE4K,QAAQ,GAAG,CAAC;EAC7E,IAAItD,QAAQ,CAACuD,SAAS,IAClB,CAACvL,IAAI,IACL,CAACA,IAAI,CAACqF,MAAM,IACX,CAACrF,IAAI,CAACmJ,QAAQ,IAAIgC,QAAQ,IAAIA,QAAQ,IAAI,CAACnL,IAAI,CAACkJ,KAAK,IAAIiC,QAAQ,KAAKnL,IAAI,CAACmJ,QAAQ,IAAIgC,QAAQ,CAAE,EAAE;IACpG;EACJ;EACA,MAAM5K,QAAQ,GAAG,CAACP,IAAI,CAACO,QAAQ,IAAIG,WAAW,IAAIwK,KAAK,CAAC1K,MAAM;IAAEqK,QAAQ,GAAG7K,IAAI,CAACW,GAAG;IAAEmK,QAAQ,GAAG9K,IAAI,CAACY,GAAG;IAAE2I,KAAK,GAAGvJ,IAAI,CAACuJ,KAAK,IAAI+B,QAAQ;EACxI,IAAI,CAACtL,IAAI,CAACqJ,IAAI,EAAE;IACZrJ,IAAI,CAACqJ,IAAI,GAAG,CAAC;EACjB;EACA,IAAI,CAACrJ,IAAI,CAACgJ,SAAS,IAAIoC,QAAQ,IAAIA,QAAQ,IAAIpL,IAAI,CAACqJ,IAAI,IAAIrJ,IAAI,CAACgJ,SAAS,IAAIoC,QAAQ,CAAC,EAAE;IACrFpL,IAAI,CAACqJ,IAAI,IAAI6B,KAAK,CAAChJ,KAAK;EAC5B;EACA,IAAI,CAAClC,IAAI,CAACgJ,SAAS,IAAIoC,QAAQ,IAAIA,QAAQ,IAAIpL,IAAI,CAACqJ,IAAI,IAAIrJ,IAAI,CAACgJ,SAAS,IAAIoC,QAAQ,CAAC,EAAE;IACrF;EACJ;EACA,QAAQpL,IAAI,CAACyJ,MAAM;IACf,KAAK5K,eAAe,CAAC6K,UAAU;MAC3B,IAAI1J,IAAI,CAACkC,KAAK,IAAI4I,QAAQ,EAAE;QACxB,IAAIG,eAAe,EAAE;UACjBjL,IAAI,CAACyJ,MAAM,GAAG5K,eAAe,CAAC+K,UAAU;QAC5C,CAAC,MACI;UACD5J,IAAI,CAACkC,KAAK,IAAI4I,QAAQ;QAC1B;QACA,IAAI,CAAC9K,IAAI,CAACkJ,KAAK,EAAE;UACblJ,IAAI,CAACkJ,KAAK,GAAGiC,QAAQ;QACzB;QACAnL,IAAI,CAACkJ,KAAK,EAAE;MAChB,CAAC,MACI;QACDlJ,IAAI,CAACkC,KAAK,IAAI3B,QAAQ;MAC1B;MACA;IACJ,KAAK1B,eAAe,CAAC+K,UAAU;MAC3B,IAAI5J,IAAI,CAACkC,KAAK,IAAI2I,QAAQ,EAAE;QACxB,IAAII,eAAe,EAAE;UACjBjL,IAAI,CAACyJ,MAAM,GAAG5K,eAAe,CAAC6K,UAAU;QAC5C,CAAC,MACI;UACD1J,IAAI,CAACkC,KAAK,IAAI4I,QAAQ;QAC1B;QACA,IAAI,CAAC9K,IAAI,CAACkJ,KAAK,EAAE;UACblJ,IAAI,CAACkJ,KAAK,GAAGiC,QAAQ;QACzB;QACAnL,IAAI,CAACkJ,KAAK,EAAE;MAChB,CAAC,MACI;QACDlJ,IAAI,CAACkC,KAAK,IAAI3B,QAAQ;MAC1B;EACR;EACA,IAAIP,IAAI,CAACO,QAAQ,IAAIgJ,KAAK,KAAK8B,QAAQ,EAAE;IACrCrL,IAAI,CAACO,QAAQ,IAAIgJ,KAAK;EAC1B;EACAoB,YAAY,CAAC3C,QAAQ,EAAE4C,WAAW,EAAE5K,IAAI,CAACkC,KAAK,EAAE2I,QAAQ,EAAEC,QAAQ,CAAC;EACnE,IAAI,CAAC9C,QAAQ,CAACuD,SAAS,EAAE;IACrBvL,IAAI,CAACkC,KAAK,GAAGnE,KAAK,CAACiC,IAAI,CAACkC,KAAK,EAAE2I,QAAQ,EAAEC,QAAQ,CAAC;EACtD;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
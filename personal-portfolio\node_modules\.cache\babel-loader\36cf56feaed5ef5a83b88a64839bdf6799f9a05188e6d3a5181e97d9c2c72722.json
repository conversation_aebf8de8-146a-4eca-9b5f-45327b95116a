{"ast": null, "code": "import { AnimatableColor, deepExtend, executeOnSingleOrMultiple, setRangeValue } from \"@tsparticles/engine\";\nimport { EmitterLife } from \"./EmitterLife.js\";\nimport { EmitterRate } from \"./EmitterRate.js\";\nimport { EmitterShape } from \"./EmitterShape.js\";\nimport { EmitterSize } from \"./EmitterSize.js\";\nexport class Emitter {\n  constructor() {\n    this.autoPlay = true;\n    this.fill = true;\n    this.life = new EmitterLife();\n    this.rate = new EmitterRate();\n    this.shape = new EmitterShape();\n    this.startCount = 0;\n  }\n  load(data) {\n    if (!data) {\n      return;\n    }\n    if (data.autoPlay !== undefined) {\n      this.autoPlay = data.autoPlay;\n    }\n    if (data.size !== undefined) {\n      if (!this.size) {\n        this.size = new EmitterSize();\n      }\n      this.size.load(data.size);\n    }\n    if (data.direction !== undefined) {\n      this.direction = data.direction;\n    }\n    this.domId = data.domId;\n    if (data.fill !== undefined) {\n      this.fill = data.fill;\n    }\n    this.life.load(data.life);\n    this.name = data.name;\n    this.particles = executeOnSingleOrMultiple(data.particles, particles => {\n      return deepExtend({}, particles);\n    });\n    this.rate.load(data.rate);\n    this.shape.load(data.shape);\n    if (data.position !== undefined) {\n      this.position = {};\n      if (data.position.x !== undefined) {\n        this.position.x = setRangeValue(data.position.x);\n      }\n      if (data.position.y !== undefined) {\n        this.position.y = setRangeValue(data.position.y);\n      }\n    }\n    if (data.spawnColor !== undefined) {\n      if (this.spawnColor === undefined) {\n        this.spawnColor = new AnimatableColor();\n      }\n      this.spawnColor.load(data.spawnColor);\n    }\n    if (data.startCount !== undefined) {\n      this.startCount = data.startCount;\n    }\n  }\n}", "map": {"version": 3, "names": ["AnimatableColor", "deepExtend", "executeOnSingleOrMultiple", "setRangeValue", "EmitterLife", "EmitterRate", "EmitterShape", "EmitterSize", "Emitter", "constructor", "autoPlay", "fill", "life", "rate", "shape", "startCount", "load", "data", "undefined", "size", "direction", "domId", "name", "particles", "position", "x", "y", "spawnColor"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/plugin-emitters/browser/Options/Classes/Emitter.js"], "sourcesContent": ["import { AnimatableColor, deepExtend, executeOnSingleOrMultiple, setRangeValue, } from \"@tsparticles/engine\";\nimport { EmitterLife } from \"./EmitterLife.js\";\nimport { EmitterRate } from \"./EmitterRate.js\";\nimport { EmitterShape } from \"./EmitterShape.js\";\nimport { EmitterSize } from \"./EmitterSize.js\";\nexport class Emitter {\n    constructor() {\n        this.autoPlay = true;\n        this.fill = true;\n        this.life = new EmitterLife();\n        this.rate = new EmitterRate();\n        this.shape = new EmitterShape();\n        this.startCount = 0;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.autoPlay !== undefined) {\n            this.autoPlay = data.autoPlay;\n        }\n        if (data.size !== undefined) {\n            if (!this.size) {\n                this.size = new EmitterSize();\n            }\n            this.size.load(data.size);\n        }\n        if (data.direction !== undefined) {\n            this.direction = data.direction;\n        }\n        this.domId = data.domId;\n        if (data.fill !== undefined) {\n            this.fill = data.fill;\n        }\n        this.life.load(data.life);\n        this.name = data.name;\n        this.particles = executeOnSingleOrMultiple(data.particles, particles => {\n            return deepExtend({}, particles);\n        });\n        this.rate.load(data.rate);\n        this.shape.load(data.shape);\n        if (data.position !== undefined) {\n            this.position = {};\n            if (data.position.x !== undefined) {\n                this.position.x = setRangeValue(data.position.x);\n            }\n            if (data.position.y !== undefined) {\n                this.position.y = setRangeValue(data.position.y);\n            }\n        }\n        if (data.spawnColor !== undefined) {\n            if (this.spawnColor === undefined) {\n                this.spawnColor = new AnimatableColor();\n            }\n            this.spawnColor.load(data.spawnColor);\n        }\n        if (data.startCount !== undefined) {\n            this.startCount = data.startCount;\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,eAAe,EAAEC,UAAU,EAAEC,yBAAyB,EAAEC,aAAa,QAAS,qBAAqB;AAC5G,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,YAAY,QAAQ,mBAAmB;AAChD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAO,MAAMC,OAAO,CAAC;EACjBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,IAAI,GAAG,IAAI;IAChB,IAAI,CAACC,IAAI,GAAG,IAAIR,WAAW,CAAC,CAAC;IAC7B,IAAI,CAACS,IAAI,GAAG,IAAIR,WAAW,CAAC,CAAC;IAC7B,IAAI,CAACS,KAAK,GAAG,IAAIR,YAAY,CAAC,CAAC;IAC/B,IAAI,CAACS,UAAU,GAAG,CAAC;EACvB;EACAC,IAAIA,CAACC,IAAI,EAAE;IACP,IAAI,CAACA,IAAI,EAAE;MACP;IACJ;IACA,IAAIA,IAAI,CAACP,QAAQ,KAAKQ,SAAS,EAAE;MAC7B,IAAI,CAACR,QAAQ,GAAGO,IAAI,CAACP,QAAQ;IACjC;IACA,IAAIO,IAAI,CAACE,IAAI,KAAKD,SAAS,EAAE;MACzB,IAAI,CAAC,IAAI,CAACC,IAAI,EAAE;QACZ,IAAI,CAACA,IAAI,GAAG,IAAIZ,WAAW,CAAC,CAAC;MACjC;MACA,IAAI,CAACY,IAAI,CAACH,IAAI,CAACC,IAAI,CAACE,IAAI,CAAC;IAC7B;IACA,IAAIF,IAAI,CAACG,SAAS,KAAKF,SAAS,EAAE;MAC9B,IAAI,CAACE,SAAS,GAAGH,IAAI,CAACG,SAAS;IACnC;IACA,IAAI,CAACC,KAAK,GAAGJ,IAAI,CAACI,KAAK;IACvB,IAAIJ,IAAI,CAACN,IAAI,KAAKO,SAAS,EAAE;MACzB,IAAI,CAACP,IAAI,GAAGM,IAAI,CAACN,IAAI;IACzB;IACA,IAAI,CAACC,IAAI,CAACI,IAAI,CAACC,IAAI,CAACL,IAAI,CAAC;IACzB,IAAI,CAACU,IAAI,GAAGL,IAAI,CAACK,IAAI;IACrB,IAAI,CAACC,SAAS,GAAGrB,yBAAyB,CAACe,IAAI,CAACM,SAAS,EAAEA,SAAS,IAAI;MACpE,OAAOtB,UAAU,CAAC,CAAC,CAAC,EAAEsB,SAAS,CAAC;IACpC,CAAC,CAAC;IACF,IAAI,CAACV,IAAI,CAACG,IAAI,CAACC,IAAI,CAACJ,IAAI,CAAC;IACzB,IAAI,CAACC,KAAK,CAACE,IAAI,CAACC,IAAI,CAACH,KAAK,CAAC;IAC3B,IAAIG,IAAI,CAACO,QAAQ,KAAKN,SAAS,EAAE;MAC7B,IAAI,CAACM,QAAQ,GAAG,CAAC,CAAC;MAClB,IAAIP,IAAI,CAACO,QAAQ,CAACC,CAAC,KAAKP,SAAS,EAAE;QAC/B,IAAI,CAACM,QAAQ,CAACC,CAAC,GAAGtB,aAAa,CAACc,IAAI,CAACO,QAAQ,CAACC,CAAC,CAAC;MACpD;MACA,IAAIR,IAAI,CAACO,QAAQ,CAACE,CAAC,KAAKR,SAAS,EAAE;QAC/B,IAAI,CAACM,QAAQ,CAACE,CAAC,GAAGvB,aAAa,CAACc,IAAI,CAACO,QAAQ,CAACE,CAAC,CAAC;MACpD;IACJ;IACA,IAAIT,IAAI,CAACU,UAAU,KAAKT,SAAS,EAAE;MAC/B,IAAI,IAAI,CAACS,UAAU,KAAKT,SAAS,EAAE;QAC/B,IAAI,CAACS,UAAU,GAAG,IAAI3B,eAAe,CAAC,CAAC;MAC3C;MACA,IAAI,CAAC2B,UAAU,CAACX,IAAI,CAACC,IAAI,CAACU,UAAU,CAAC;IACzC;IACA,IAAIV,IAAI,CAACF,UAAU,KAAKG,SAAS,EAAE;MAC/B,IAAI,CAACH,UAAU,GAAGE,IAAI,CAACF,UAAU;IACrC;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
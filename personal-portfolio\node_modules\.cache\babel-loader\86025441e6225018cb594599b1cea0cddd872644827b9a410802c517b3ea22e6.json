{"ast": null, "code": "import { ColorUpdater } from \"./ColorUpdater.js\";\nexport async function loadColorUpdater(engine, refresh = true) {\n  await engine.addParticleUpdater(\"color\", container => {\n    return Promise.resolve(new ColorUpdater(container));\n  }, refresh);\n}", "map": {"version": 3, "names": ["ColorUpdater", "loadColorUpdater", "engine", "refresh", "addParticleUpdater", "container", "Promise", "resolve"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/updater-color/browser/index.js"], "sourcesContent": ["import { ColorUpdater } from \"./ColorUpdater.js\";\nexport async function loadColorUpdater(engine, refresh = true) {\n    await engine.addParticleUpdater(\"color\", container => {\n        return Promise.resolve(new ColorUpdater(container));\n    }, refresh);\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,mBAAmB;AAChD,OAAO,eAAeC,gBAAgBA,CAACC,MAAM,EAAEC,OAAO,GAAG,IAAI,EAAE;EAC3D,MAAMD,MAAM,CAACE,kBAAkB,CAAC,OAAO,EAAEC,SAAS,IAAI;IAClD,OAAOC,OAAO,CAACC,OAAO,CAAC,IAAIP,YAAY,CAACK,SAAS,CAAC,CAAC;EACvD,CAAC,EAAEF,OAAO,CAAC;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
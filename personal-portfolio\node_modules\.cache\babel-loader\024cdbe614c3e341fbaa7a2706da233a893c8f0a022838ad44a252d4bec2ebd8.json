{"ast": null, "code": "import { OpacityAnimation } from \"./OpacityAnimation.js\";\nimport { RangedAnimationValueWithRandom } from \"../../ValueWithRandom.js\";\nexport class Opacity extends RangedAnimationValueWithRandom {\n  constructor() {\n    super();\n    this.animation = new OpacityAnimation();\n    this.value = 1;\n  }\n  load(data) {\n    if (!data) {\n      return;\n    }\n    super.load(data);\n    const animation = data.animation;\n    if (animation !== undefined) {\n      this.animation.load(animation);\n    }\n  }\n}", "map": {"version": 3, "names": ["OpacityAnimation", "RangedAnimationValueWithRandom", "Opacity", "constructor", "animation", "value", "load", "data", "undefined"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/engine/browser/Options/Classes/Particles/Opacity/Opacity.js"], "sourcesContent": ["import { OpacityAnimation } from \"./OpacityAnimation.js\";\nimport { RangedAnimationValueWithRandom } from \"../../ValueWithRandom.js\";\nexport class Opacity extends RangedAnimationValueWithRandom {\n    constructor() {\n        super();\n        this.animation = new OpacityAnimation();\n        this.value = 1;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        super.load(data);\n        const animation = data.animation;\n        if (animation !== undefined) {\n            this.animation.load(animation);\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,uBAAuB;AACxD,SAASC,8BAA8B,QAAQ,0BAA0B;AACzE,OAAO,MAAMC,OAAO,SAASD,8BAA8B,CAAC;EACxDE,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,SAAS,GAAG,IAAIJ,gBAAgB,CAAC,CAAC;IACvC,IAAI,CAACK,KAAK,GAAG,CAAC;EAClB;EACAC,IAAIA,CAACC,IAAI,EAAE;IACP,IAAI,CAACA,IAAI,EAAE;MACP;IACJ;IACA,KAAK,CAACD,IAAI,CAACC,IAAI,CAAC;IAChB,MAAMH,SAAS,GAAGG,IAAI,CAACH,SAAS;IAChC,IAAIA,SAAS,KAAKI,SAAS,EAAE;MACzB,IAAI,CAACJ,SAAS,CAACE,IAAI,CAACF,SAAS,CAAC;IAClC;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import toDate from \"../../toDate/index.js\";\nimport requiredArgs from \"../requiredArgs/index.js\"; // This function will be a part of public API when UTC function will be implemented.\n// See issue: https://github.com/date-fns/date-fns/issues/376\n\nexport default function startOfUTCISOWeek(dirtyDate) {\n  requiredArgs(1, arguments);\n  var weekStartsOn = 1;\n  var date = toDate(dirtyDate);\n  var day = date.getUTCDay();\n  var diff = (day < weekStartsOn ? 7 : 0) + day - weekStartsOn;\n  date.setUTCDate(date.getUTCDate() - diff);\n  date.setUTCHours(0, 0, 0, 0);\n  return date;\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/date-fns/esm/_lib/startOfUTCISOWeek/index.js"], "names": ["toDate", "requiredArgs", "startOfUTCISOWeek", "dirtyDate", "arguments", "weekStartsOn", "date", "day", "getUTCDay", "diff", "setUTCDate", "getUTCDate", "setUTCHours"], "mappings": "AAAA,OAAOA,MAAP,MAAmB,uBAAnB;AACA,OAAOC,YAAP,MAAyB,0BAAzB,C,CAAqD;AACrD;;AAEA,eAAe,SAASC,iBAAT,CAA2BC,SAA3B,EAAsC;AACnDF,EAAAA,YAAY,CAAC,CAAD,EAAIG,SAAJ,CAAZ;AACA,MAAIC,YAAY,GAAG,CAAnB;AACA,MAAIC,IAAI,GAAGN,MAAM,CAACG,SAAD,CAAjB;AACA,MAAII,GAAG,GAAGD,IAAI,CAACE,SAAL,EAAV;AACA,MAAIC,IAAI,GAAG,CAACF,GAAG,GAAGF,YAAN,GAAqB,CAArB,GAAyB,CAA1B,IAA+BE,GAA/B,GAAqCF,YAAhD;AACAC,EAAAA,IAAI,CAACI,UAAL,CAAgBJ,IAAI,CAACK,UAAL,KAAoBF,IAApC;AACAH,EAAAA,IAAI,CAACM,WAAL,CAAiB,CAAjB,EAAoB,CAApB,EAAuB,CAAvB,EAA0B,CAA1B;AACA,SAAON,IAAP;AACD", "sourcesContent": ["import toDate from \"../../toDate/index.js\";\nimport requiredArgs from \"../requiredArgs/index.js\"; // This function will be a part of public API when UTC function will be implemented.\n// See issue: https://github.com/date-fns/date-fns/issues/376\n\nexport default function startOfUTCISOWeek(dirtyDate) {\n  requiredArgs(1, arguments);\n  var weekStartsOn = 1;\n  var date = toDate(dirtyDate);\n  var day = date.getUTCDay();\n  var diff = (day < weekStartsOn ? 7 : 0) + day - weekStartsOn;\n  date.setUTCDate(date.getUTCDate() - diff);\n  date.setUTCHours(0, 0, 0, 0);\n  return date;\n}"]}, "metadata": {}, "sourceType": "module"}
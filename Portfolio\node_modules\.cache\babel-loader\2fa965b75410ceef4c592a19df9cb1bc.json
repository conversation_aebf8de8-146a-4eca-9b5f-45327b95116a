{"ast": null, "code": "import { Circle, CircleWarp, ParticlesInteractorBase } from \"../../../Core\";\nimport { getDistance, getLinkRandomColor } from \"../../../Utils\";\n\nfunction getLinkDistance(pos1, pos2, optDistance, canvasSize, warp) {\n  let distance = getDistance(pos1, pos2);\n\n  if (!warp || distance <= optDistance) {\n    return distance;\n  }\n\n  const pos2NE = {\n    x: pos2.x - canvasSize.width,\n    y: pos2.y\n  };\n  distance = getDistance(pos1, pos2NE);\n\n  if (distance <= optDistance) {\n    return distance;\n  }\n\n  const pos2SE = {\n    x: pos2.x - canvasSize.width,\n    y: pos2.y - canvasSize.height\n  };\n  distance = getDistance(pos1, pos2SE);\n\n  if (distance <= optDistance) {\n    return distance;\n  }\n\n  const pos2SW = {\n    x: pos2.x,\n    y: pos2.y - canvasSize.height\n  };\n  distance = getDistance(pos1, pos2SW);\n  return distance;\n}\n\nexport class Linker extends ParticlesInteractorBase {\n  constructor(container) {\n    super(container);\n  }\n\n  isEnabled(particle) {\n    return particle.options.links.enable;\n  }\n\n  reset() {}\n\n  async interact(p1) {\n    var _a;\n\n    p1.links = [];\n    const pos1 = p1.getPosition();\n    const container = this.container;\n    const canvasSize = container.canvas.size;\n\n    if (pos1.x < 0 || pos1.y < 0 || pos1.x > canvasSize.width || pos1.y > canvasSize.height) {\n      return;\n    }\n\n    const linkOpt1 = p1.options.links;\n    const optOpacity = linkOpt1.opacity;\n    const optDistance = (_a = p1.retina.linksDistance) !== null && _a !== void 0 ? _a : container.retina.linksDistance;\n    const warp = linkOpt1.warp;\n    const range = warp ? new CircleWarp(pos1.x, pos1.y, optDistance, canvasSize) : new Circle(pos1.x, pos1.y, optDistance);\n    const query = container.particles.quadTree.query(range);\n\n    for (const p2 of query) {\n      const linkOpt2 = p2.options.links;\n\n      if (p1 === p2 || !linkOpt2.enable || linkOpt1.id !== linkOpt2.id || p2.spawning || p2.destroyed || p1.links.map(t => t.destination).indexOf(p2) !== -1 || p2.links.map(t => t.destination).indexOf(p1) !== -1) {\n        continue;\n      }\n\n      const pos2 = p2.getPosition();\n\n      if (pos2.x < 0 || pos2.y < 0 || pos2.x > canvasSize.width || pos2.y > canvasSize.height) {\n        continue;\n      }\n\n      const distance = getLinkDistance(pos1, pos2, optDistance, canvasSize, warp && linkOpt2.warp);\n\n      if (distance > optDistance) {\n        return;\n      }\n\n      const opacityLine = (1 - distance / optDistance) * optOpacity;\n      this.setColor(p1);\n      p1.links.push({\n        destination: p2,\n        opacity: opacityLine\n      });\n    }\n  }\n\n  setColor(p1) {\n    const container = this.container;\n    const linksOptions = p1.options.links;\n    let linkColor = linksOptions.id === undefined ? container.particles.linksColor : container.particles.linksColors.get(linksOptions.id);\n\n    if (!linkColor) {\n      const optColor = linksOptions.color;\n      linkColor = getLinkRandomColor(optColor, linksOptions.blink, linksOptions.consent);\n\n      if (linksOptions.id === undefined) {\n        container.particles.linksColor = linkColor;\n      } else {\n        container.particles.linksColors.set(linksOptions.id, linkColor);\n      }\n    }\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Interactions/Particles/Links/Linker.js"], "names": ["Circle", "CircleWarp", "ParticlesInteractorBase", "getDistance", "getLinkRandomColor", "getLinkDistance", "pos1", "pos2", "optDistance", "canvasSize", "warp", "distance", "pos2NE", "x", "width", "y", "pos2SE", "height", "pos2SW", "<PERSON><PERSON>", "constructor", "container", "isEnabled", "particle", "options", "links", "enable", "reset", "interact", "p1", "_a", "getPosition", "canvas", "size", "linkOpt1", "optOpacity", "opacity", "retina", "linksDistance", "range", "query", "particles", "quadTree", "p2", "linkOpt2", "id", "spawning", "destroyed", "map", "t", "destination", "indexOf", "opacityLine", "setColor", "push", "linksOptions", "linkColor", "undefined", "linksColor", "linksColors", "get", "optColor", "color", "blink", "consent", "set"], "mappings": "AAAA,SAASA,MAAT,EAAiBC,UAAjB,EAA6BC,uBAA7B,QAA4D,eAA5D;AACA,SAASC,WAAT,EAAsBC,kBAAtB,QAAgD,gBAAhD;;AACA,SAASC,eAAT,CAAyBC,IAAzB,EAA+BC,IAA/B,EAAqCC,WAArC,EAAkDC,UAAlD,EAA8DC,IAA9D,EAAoE;AAChE,MAAIC,QAAQ,GAAGR,WAAW,CAACG,IAAD,EAAOC,IAAP,CAA1B;;AACA,MAAI,CAACG,IAAD,IAASC,QAAQ,IAAIH,WAAzB,EAAsC;AAClC,WAAOG,QAAP;AACH;;AACD,QAAMC,MAAM,GAAG;AACXC,IAAAA,CAAC,EAAEN,IAAI,CAACM,CAAL,GAASJ,UAAU,CAACK,KADZ;AAEXC,IAAAA,CAAC,EAAER,IAAI,CAACQ;AAFG,GAAf;AAIAJ,EAAAA,QAAQ,GAAGR,WAAW,CAACG,IAAD,EAAOM,MAAP,CAAtB;;AACA,MAAID,QAAQ,IAAIH,WAAhB,EAA6B;AACzB,WAAOG,QAAP;AACH;;AACD,QAAMK,MAAM,GAAG;AACXH,IAAAA,CAAC,EAAEN,IAAI,CAACM,CAAL,GAASJ,UAAU,CAACK,KADZ;AAEXC,IAAAA,CAAC,EAAER,IAAI,CAACQ,CAAL,GAASN,UAAU,CAACQ;AAFZ,GAAf;AAIAN,EAAAA,QAAQ,GAAGR,WAAW,CAACG,IAAD,EAAOU,MAAP,CAAtB;;AACA,MAAIL,QAAQ,IAAIH,WAAhB,EAA6B;AACzB,WAAOG,QAAP;AACH;;AACD,QAAMO,MAAM,GAAG;AACXL,IAAAA,CAAC,EAAEN,IAAI,CAACM,CADG;AAEXE,IAAAA,CAAC,EAAER,IAAI,CAACQ,CAAL,GAASN,UAAU,CAACQ;AAFZ,GAAf;AAIAN,EAAAA,QAAQ,GAAGR,WAAW,CAACG,IAAD,EAAOY,MAAP,CAAtB;AACA,SAAOP,QAAP;AACH;;AACD,OAAO,MAAMQ,MAAN,SAAqBjB,uBAArB,CAA6C;AAChDkB,EAAAA,WAAW,CAACC,SAAD,EAAY;AACnB,UAAMA,SAAN;AACH;;AACDC,EAAAA,SAAS,CAACC,QAAD,EAAW;AAChB,WAAOA,QAAQ,CAACC,OAAT,CAAiBC,KAAjB,CAAuBC,MAA9B;AACH;;AACDC,EAAAA,KAAK,GAAG,CACP;;AACa,QAARC,QAAQ,CAACC,EAAD,EAAK;AACf,QAAIC,EAAJ;;AACAD,IAAAA,EAAE,CAACJ,KAAH,GAAW,EAAX;AACA,UAAMnB,IAAI,GAAGuB,EAAE,CAACE,WAAH,EAAb;AACA,UAAMV,SAAS,GAAG,KAAKA,SAAvB;AACA,UAAMZ,UAAU,GAAGY,SAAS,CAACW,MAAV,CAAiBC,IAApC;;AACA,QAAI3B,IAAI,CAACO,CAAL,GAAS,CAAT,IAAcP,IAAI,CAACS,CAAL,GAAS,CAAvB,IAA4BT,IAAI,CAACO,CAAL,GAASJ,UAAU,CAACK,KAAhD,IAAyDR,IAAI,CAACS,CAAL,GAASN,UAAU,CAACQ,MAAjF,EAAyF;AACrF;AACH;;AACD,UAAMiB,QAAQ,GAAGL,EAAE,CAACL,OAAH,CAAWC,KAA5B;AACA,UAAMU,UAAU,GAAGD,QAAQ,CAACE,OAA5B;AACA,UAAM5B,WAAW,GAAG,CAACsB,EAAE,GAAGD,EAAE,CAACQ,MAAH,CAAUC,aAAhB,MAAmC,IAAnC,IAA2CR,EAAE,KAAK,KAAK,CAAvD,GAA2DA,EAA3D,GAAgET,SAAS,CAACgB,MAAV,CAAiBC,aAArG;AACA,UAAM5B,IAAI,GAAGwB,QAAQ,CAACxB,IAAtB;AACA,UAAM6B,KAAK,GAAG7B,IAAI,GACZ,IAAIT,UAAJ,CAAeK,IAAI,CAACO,CAApB,EAAuBP,IAAI,CAACS,CAA5B,EAA+BP,WAA/B,EAA4CC,UAA5C,CADY,GAEZ,IAAIT,MAAJ,CAAWM,IAAI,CAACO,CAAhB,EAAmBP,IAAI,CAACS,CAAxB,EAA2BP,WAA3B,CAFN;AAGA,UAAMgC,KAAK,GAAGnB,SAAS,CAACoB,SAAV,CAAoBC,QAApB,CAA6BF,KAA7B,CAAmCD,KAAnC,CAAd;;AACA,SAAK,MAAMI,EAAX,IAAiBH,KAAjB,EAAwB;AACpB,YAAMI,QAAQ,GAAGD,EAAE,CAACnB,OAAH,CAAWC,KAA5B;;AACA,UAAII,EAAE,KAAKc,EAAP,IACA,CAACC,QAAQ,CAAClB,MADV,IAEAQ,QAAQ,CAACW,EAAT,KAAgBD,QAAQ,CAACC,EAFzB,IAGAF,EAAE,CAACG,QAHH,IAIAH,EAAE,CAACI,SAJH,IAKAlB,EAAE,CAACJ,KAAH,CAASuB,GAAT,CAAcC,CAAD,IAAOA,CAAC,CAACC,WAAtB,EAAmCC,OAAnC,CAA2CR,EAA3C,MAAmD,CAAC,CALpD,IAMAA,EAAE,CAAClB,KAAH,CAASuB,GAAT,CAAcC,CAAD,IAAOA,CAAC,CAACC,WAAtB,EAAmCC,OAAnC,CAA2CtB,EAA3C,MAAmD,CAAC,CANxD,EAM2D;AACvD;AACH;;AACD,YAAMtB,IAAI,GAAGoC,EAAE,CAACZ,WAAH,EAAb;;AACA,UAAIxB,IAAI,CAACM,CAAL,GAAS,CAAT,IAAcN,IAAI,CAACQ,CAAL,GAAS,CAAvB,IAA4BR,IAAI,CAACM,CAAL,GAASJ,UAAU,CAACK,KAAhD,IAAyDP,IAAI,CAACQ,CAAL,GAASN,UAAU,CAACQ,MAAjF,EAAyF;AACrF;AACH;;AACD,YAAMN,QAAQ,GAAGN,eAAe,CAACC,IAAD,EAAOC,IAAP,EAAaC,WAAb,EAA0BC,UAA1B,EAAsCC,IAAI,IAAIkC,QAAQ,CAAClC,IAAvD,CAAhC;;AACA,UAAIC,QAAQ,GAAGH,WAAf,EAA4B;AACxB;AACH;;AACD,YAAM4C,WAAW,GAAG,CAAC,IAAIzC,QAAQ,GAAGH,WAAhB,IAA+B2B,UAAnD;AACA,WAAKkB,QAAL,CAAcxB,EAAd;AACAA,MAAAA,EAAE,CAACJ,KAAH,CAAS6B,IAAT,CAAc;AACVJ,QAAAA,WAAW,EAAEP,EADH;AAEVP,QAAAA,OAAO,EAAEgB;AAFC,OAAd;AAIH;AACJ;;AACDC,EAAAA,QAAQ,CAACxB,EAAD,EAAK;AACT,UAAMR,SAAS,GAAG,KAAKA,SAAvB;AACA,UAAMkC,YAAY,GAAG1B,EAAE,CAACL,OAAH,CAAWC,KAAhC;AACA,QAAI+B,SAAS,GAAGD,YAAY,CAACV,EAAb,KAAoBY,SAApB,GACVpC,SAAS,CAACoB,SAAV,CAAoBiB,UADV,GAEVrC,SAAS,CAACoB,SAAV,CAAoBkB,WAApB,CAAgCC,GAAhC,CAAoCL,YAAY,CAACV,EAAjD,CAFN;;AAGA,QAAI,CAACW,SAAL,EAAgB;AACZ,YAAMK,QAAQ,GAAGN,YAAY,CAACO,KAA9B;AACAN,MAAAA,SAAS,GAAGpD,kBAAkB,CAACyD,QAAD,EAAWN,YAAY,CAACQ,KAAxB,EAA+BR,YAAY,CAACS,OAA5C,CAA9B;;AACA,UAAIT,YAAY,CAACV,EAAb,KAAoBY,SAAxB,EAAmC;AAC/BpC,QAAAA,SAAS,CAACoB,SAAV,CAAoBiB,UAApB,GAAiCF,SAAjC;AACH,OAFD,MAGK;AACDnC,QAAAA,SAAS,CAACoB,SAAV,CAAoBkB,WAApB,CAAgCM,GAAhC,CAAoCV,YAAY,CAACV,EAAjD,EAAqDW,SAArD;AACH;AACJ;AACJ;;AArE+C", "sourcesContent": ["import { Circle, CircleWarp, ParticlesInteractorBase } from \"../../../Core\";\nimport { getDistance, getLinkRandomColor } from \"../../../Utils\";\nfunction getLinkDistance(pos1, pos2, optDistance, canvasSize, warp) {\n    let distance = getDistance(pos1, pos2);\n    if (!warp || distance <= optDistance) {\n        return distance;\n    }\n    const pos2NE = {\n        x: pos2.x - canvasSize.width,\n        y: pos2.y,\n    };\n    distance = getDistance(pos1, pos2NE);\n    if (distance <= optDistance) {\n        return distance;\n    }\n    const pos2SE = {\n        x: pos2.x - canvasSize.width,\n        y: pos2.y - canvasSize.height,\n    };\n    distance = getDistance(pos1, pos2SE);\n    if (distance <= optDistance) {\n        return distance;\n    }\n    const pos2SW = {\n        x: pos2.x,\n        y: pos2.y - canvasSize.height,\n    };\n    distance = getDistance(pos1, pos2SW);\n    return distance;\n}\nexport class Linker extends ParticlesInteractorBase {\n    constructor(container) {\n        super(container);\n    }\n    isEnabled(particle) {\n        return particle.options.links.enable;\n    }\n    reset() {\n    }\n    async interact(p1) {\n        var _a;\n        p1.links = [];\n        const pos1 = p1.getPosition();\n        const container = this.container;\n        const canvasSize = container.canvas.size;\n        if (pos1.x < 0 || pos1.y < 0 || pos1.x > canvasSize.width || pos1.y > canvasSize.height) {\n            return;\n        }\n        const linkOpt1 = p1.options.links;\n        const optOpacity = linkOpt1.opacity;\n        const optDistance = (_a = p1.retina.linksDistance) !== null && _a !== void 0 ? _a : container.retina.linksDistance;\n        const warp = linkOpt1.warp;\n        const range = warp\n            ? new CircleWarp(pos1.x, pos1.y, optDistance, canvasSize)\n            : new Circle(pos1.x, pos1.y, optDistance);\n        const query = container.particles.quadTree.query(range);\n        for (const p2 of query) {\n            const linkOpt2 = p2.options.links;\n            if (p1 === p2 ||\n                !linkOpt2.enable ||\n                linkOpt1.id !== linkOpt2.id ||\n                p2.spawning ||\n                p2.destroyed ||\n                p1.links.map((t) => t.destination).indexOf(p2) !== -1 ||\n                p2.links.map((t) => t.destination).indexOf(p1) !== -1) {\n                continue;\n            }\n            const pos2 = p2.getPosition();\n            if (pos2.x < 0 || pos2.y < 0 || pos2.x > canvasSize.width || pos2.y > canvasSize.height) {\n                continue;\n            }\n            const distance = getLinkDistance(pos1, pos2, optDistance, canvasSize, warp && linkOpt2.warp);\n            if (distance > optDistance) {\n                return;\n            }\n            const opacityLine = (1 - distance / optDistance) * optOpacity;\n            this.setColor(p1);\n            p1.links.push({\n                destination: p2,\n                opacity: opacityLine,\n            });\n        }\n    }\n    setColor(p1) {\n        const container = this.container;\n        const linksOptions = p1.options.links;\n        let linkColor = linksOptions.id === undefined\n            ? container.particles.linksColor\n            : container.particles.linksColors.get(linksOptions.id);\n        if (!linkColor) {\n            const optColor = linksOptions.color;\n            linkColor = getLinkRandomColor(optColor, linksOptions.blink, linksOptions.consent);\n            if (linksOptions.id === undefined) {\n                container.particles.linksColor = linkColor;\n            }\n            else {\n                container.particles.linksColors.set(linksOptions.id, linkColor);\n            }\n        }\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
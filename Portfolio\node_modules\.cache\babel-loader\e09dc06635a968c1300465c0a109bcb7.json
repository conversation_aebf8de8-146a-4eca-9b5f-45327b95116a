{"ast": null, "code": "import toInteger from \"../_lib/toInteger/index.js\";\nimport toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name addMilliseconds\n * @category Millisecond Helpers\n * @summary Add the specified number of milliseconds to the given date.\n *\n * @description\n * Add the specified number of milliseconds to the given date.\n *\n * ### v2.0.0 breaking changes:\n *\n * - [Changes that are common for the whole library](https://github.com/date-fns/date-fns/blob/master/docs/upgradeGuide.md#Common-Changes).\n *\n * @param {Date|Number} date - the date to be changed\n * @param {Number} amount - the amount of milliseconds to be added. Positive decimals will be rounded using `Math.floor`, decimals less than zero will be rounded using `Math.ceil`.\n * @returns {Date} the new date with the milliseconds added\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Add 750 milliseconds to 10 July 2014 12:45:30.000:\n * const result = addMilliseconds(new Date(2014, 6, 10, 12, 45, 30, 0), 750)\n * //=> Thu Jul 10 2014 12:45:30.750\n */\n\nexport default function addMilliseconds(dirtyDate, dirtyAmount) {\n  requiredArgs(2, arguments);\n  var timestamp = toDate(dirtyDate).getTime();\n  var amount = toInteger(dirtyAmount);\n  return new Date(timestamp + amount);\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/date-fns/esm/addMilliseconds/index.js"], "names": ["toInteger", "toDate", "requiredArgs", "addMilliseconds", "dirtyDate", "dirtyAmount", "arguments", "timestamp", "getTime", "amount", "Date"], "mappings": "AAAA,OAAOA,SAAP,MAAsB,4BAAtB;AACA,OAAOC,MAAP,MAAmB,oBAAnB;AACA,OAAOC,YAAP,MAAyB,+BAAzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,eAAe,SAASC,eAAT,CAAyBC,SAAzB,EAAoCC,WAApC,EAAiD;AAC9DH,EAAAA,YAAY,CAAC,CAAD,EAAII,SAAJ,CAAZ;AACA,MAAIC,SAAS,GAAGN,MAAM,CAACG,SAAD,CAAN,CAAkBI,OAAlB,EAAhB;AACA,MAAIC,MAAM,GAAGT,SAAS,CAACK,WAAD,CAAtB;AACA,SAAO,IAAIK,IAAJ,CAASH,SAAS,GAAGE,MAArB,CAAP;AACD", "sourcesContent": ["import toInteger from \"../_lib/toInteger/index.js\";\nimport toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name addMilliseconds\n * @category Millisecond Helpers\n * @summary Add the specified number of milliseconds to the given date.\n *\n * @description\n * Add the specified number of milliseconds to the given date.\n *\n * ### v2.0.0 breaking changes:\n *\n * - [Changes that are common for the whole library](https://github.com/date-fns/date-fns/blob/master/docs/upgradeGuide.md#Common-Changes).\n *\n * @param {Date|Number} date - the date to be changed\n * @param {Number} amount - the amount of milliseconds to be added. Positive decimals will be rounded using `Math.floor`, decimals less than zero will be rounded using `Math.ceil`.\n * @returns {Date} the new date with the milliseconds added\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Add 750 milliseconds to 10 July 2014 12:45:30.000:\n * const result = addMilliseconds(new Date(2014, 6, 10, 12, 45, 30, 0), 750)\n * //=> Thu Jul 10 2014 12:45:30.750\n */\n\nexport default function addMilliseconds(dirtyDate, dirtyAmount) {\n  requiredArgs(2, arguments);\n  var timestamp = toDate(dirtyDate).getTime();\n  var amount = toInteger(dirtyAmount);\n  return new Date(timestamp + amount);\n}"]}, "metadata": {}, "sourceType": "module"}
{"ast": null, "code": "export var DivType;\n(function (DivType) {\n  DivType[\"circle\"] = \"circle\";\n  DivType[\"rectangle\"] = \"rectangle\";\n})(DivType || (DivType = {}));", "map": {"version": 3, "names": ["DivType"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/engine/browser/Enums/Types/DivType.js"], "sourcesContent": ["export var DivType;\n(function (DivType) {\n    DivType[\"circle\"] = \"circle\";\n    DivType[\"rectangle\"] = \"rectangle\";\n})(DivType || (DivType = {}));\n"], "mappings": "AAAA,OAAO,IAAIA,OAAO;AAClB,CAAC,UAAUA,OAAO,EAAE;EAChBA,OAAO,CAAC,QAAQ,CAAC,GAAG,QAAQ;EAC5BA,OAAO,CAAC,WAAW,CAAC,GAAG,WAAW;AACtC,CAAC,EAAEA,OAAO,KAAKA,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
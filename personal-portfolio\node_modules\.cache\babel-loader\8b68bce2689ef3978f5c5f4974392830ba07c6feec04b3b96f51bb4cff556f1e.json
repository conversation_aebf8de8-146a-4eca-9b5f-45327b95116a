{"ast": null, "code": "import { drawLine, getStyleFromRgb } from \"@tsparticles/engine\";\nconst defaultWidth = 0;\nexport function drawGrabLine(context, width, begin, end, colorLine, opacity) {\n  drawLine(context, begin, end);\n  context.strokeStyle = getStyleFromRgb(colorLine, opacity);\n  context.lineWidth = width;\n  context.stroke();\n}\nexport function drawGrab(container, particle, lineColor, opacity, mousePos) {\n  container.canvas.draw(ctx => {\n    const beginPos = particle.getPosition();\n    drawGrabLine(ctx, particle.retina.linksWidth ?? defaultWidth, beginPos, mousePos, lineColor, opacity);\n  });\n}", "map": {"version": 3, "names": ["drawLine", "getStyleFromRgb", "defaultWidth", "drawGrabLine", "context", "width", "begin", "end", "colorLine", "opacity", "strokeStyle", "lineWidth", "stroke", "drawGrab", "container", "particle", "lineColor", "mousePos", "canvas", "draw", "ctx", "beginPos", "getPosition", "retina", "linksWidth"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/interaction-external-grab/browser/Utils.js"], "sourcesContent": ["import { drawLine, getStyleFromRgb } from \"@tsparticles/engine\";\nconst defaultWidth = 0;\nexport function drawGrabLine(context, width, begin, end, colorLine, opacity) {\n    drawLine(context, begin, end);\n    context.strokeStyle = getStyleFromRgb(colorLine, opacity);\n    context.lineWidth = width;\n    context.stroke();\n}\nexport function drawGrab(container, particle, lineColor, opacity, mousePos) {\n    container.canvas.draw(ctx => {\n        const beginPos = particle.getPosition();\n        drawGrabLine(ctx, particle.retina.linksWidth ?? defaultWidth, beginPos, mousePos, lineColor, opacity);\n    });\n}\n"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,eAAe,QAAQ,qBAAqB;AAC/D,MAAMC,YAAY,GAAG,CAAC;AACtB,OAAO,SAASC,YAAYA,CAACC,OAAO,EAAEC,KAAK,EAAEC,KAAK,EAAEC,GAAG,EAAEC,SAAS,EAAEC,OAAO,EAAE;EACzET,QAAQ,CAACI,OAAO,EAAEE,KAAK,EAAEC,GAAG,CAAC;EAC7BH,OAAO,CAACM,WAAW,GAAGT,eAAe,CAACO,SAAS,EAAEC,OAAO,CAAC;EACzDL,OAAO,CAACO,SAAS,GAAGN,KAAK;EACzBD,OAAO,CAACQ,MAAM,CAAC,CAAC;AACpB;AACA,OAAO,SAASC,QAAQA,CAACC,SAAS,EAAEC,QAAQ,EAAEC,SAAS,EAAEP,OAAO,EAAEQ,QAAQ,EAAE;EACxEH,SAAS,CAACI,MAAM,CAACC,IAAI,CAACC,GAAG,IAAI;IACzB,MAAMC,QAAQ,GAAGN,QAAQ,CAACO,WAAW,CAAC,CAAC;IACvCnB,YAAY,CAACiB,GAAG,EAAEL,QAAQ,CAACQ,MAAM,CAACC,UAAU,IAAItB,YAAY,EAAEmB,QAAQ,EAAEJ,QAAQ,EAAED,SAAS,EAAEP,OAAO,CAAC;EACzG,CAAC,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { LifeDelay } from \"./LifeDelay\";\nimport { LifeDuration } from \"./LifeDuration\";\nexport class Life {\n  constructor() {\n    this.count = 0;\n    this.delay = new LifeDelay();\n    this.duration = new LifeDuration();\n  }\n\n  load(data) {\n    if (data === undefined) {\n      return;\n    }\n\n    if (data.count !== undefined) {\n      this.count = data.count;\n    }\n\n    this.delay.load(data.delay);\n    this.duration.load(data.duration);\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Options/Classes/Particles/Life/Life.js"], "names": ["LifeDelay", "LifeDuration", "Life", "constructor", "count", "delay", "duration", "load", "data", "undefined"], "mappings": "AAAA,SAASA,SAAT,QAA0B,aAA1B;AACA,SAASC,YAAT,QAA6B,gBAA7B;AACA,OAAO,MAAMC,IAAN,CAAW;AACdC,EAAAA,WAAW,GAAG;AACV,SAAKC,KAAL,GAAa,CAAb;AACA,SAAKC,KAAL,GAAa,IAAIL,SAAJ,EAAb;AACA,SAAKM,QAAL,GAAgB,IAAIL,YAAJ,EAAhB;AACH;;AACDM,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,QAAIA,IAAI,KAAKC,SAAb,EAAwB;AACpB;AACH;;AACD,QAAID,IAAI,CAACJ,KAAL,KAAeK,SAAnB,EAA8B;AAC1B,WAAKL,KAAL,GAAaI,IAAI,CAACJ,KAAlB;AACH;;AACD,SAAKC,KAAL,CAAWE,IAAX,CAAgBC,IAAI,CAACH,KAArB;AACA,SAAKC,QAAL,CAAcC,IAAd,CAAmBC,IAAI,CAACF,QAAxB;AACH;;AAfa", "sourcesContent": ["import { LifeDelay } from \"./LifeDelay\";\nimport { LifeDuration } from \"./LifeDuration\";\nexport class Life {\n    constructor() {\n        this.count = 0;\n        this.delay = new LifeDelay();\n        this.duration = new LifeDuration();\n    }\n    load(data) {\n        if (data === undefined) {\n            return;\n        }\n        if (data.count !== undefined) {\n            this.count = data.count;\n        }\n        this.delay.load(data.delay);\n        this.duration.load(data.duration);\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
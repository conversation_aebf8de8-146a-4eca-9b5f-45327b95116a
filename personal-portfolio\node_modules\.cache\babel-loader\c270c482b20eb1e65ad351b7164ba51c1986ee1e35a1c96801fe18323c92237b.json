{"ast": null, "code": "import { PolygonDrawer } from \"./PolygonDrawer.js\";\nimport { TriangleDrawer } from \"./TriangleDrawer.js\";\nexport async function loadGenericPolygonShape(engine, refresh = true) {\n  await engine.addShape(new PolygonDrawer(), refresh);\n}\nexport async function loadTriangleShape(engine, refresh = true) {\n  await engine.addShape(new TriangleDrawer(), refresh);\n}\nexport async function loadPolygonShape(engine, refresh = true) {\n  await loadGenericPolygonShape(engine, refresh);\n  await loadTriangleShape(engine, refresh);\n}", "map": {"version": 3, "names": ["PolygonDrawer", "TriangleDrawer", "loadGenericPolygonShape", "engine", "refresh", "addShape", "loadTriangleShape", "loadPolygonShape"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/shape-polygon/browser/index.js"], "sourcesContent": ["import { PolygonDrawer } from \"./PolygonDrawer.js\";\nimport { TriangleDrawer } from \"./TriangleDrawer.js\";\nexport async function loadGenericPolygonShape(engine, refresh = true) {\n    await engine.addShape(new PolygonDrawer(), refresh);\n}\nexport async function loadTriangleShape(engine, refresh = true) {\n    await engine.addShape(new TriangleDrawer(), refresh);\n}\nexport async function loadPolygonShape(engine, refresh = true) {\n    await loadGenericPolygonShape(engine, refresh);\n    await loadTriangleShape(engine, refresh);\n}\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,oBAAoB;AAClD,SAASC,cAAc,QAAQ,qBAAqB;AACpD,OAAO,eAAeC,uBAAuBA,CAACC,MAAM,EAAEC,OAAO,GAAG,IAAI,EAAE;EAClE,MAAMD,MAAM,CAACE,QAAQ,CAAC,IAAIL,aAAa,CAAC,CAAC,EAAEI,OAAO,CAAC;AACvD;AACA,OAAO,eAAeE,iBAAiBA,CAACH,MAAM,EAAEC,OAAO,GAAG,IAAI,EAAE;EAC5D,MAAMD,MAAM,CAACE,QAAQ,CAAC,IAAIJ,cAAc,CAAC,CAAC,EAAEG,OAAO,CAAC;AACxD;AACA,OAAO,eAAeG,gBAAgBA,CAACJ,MAAM,EAAEC,OAAO,GAAG,IAAI,EAAE;EAC3D,MAAMF,uBAAuB,CAACC,MAAM,EAAEC,OAAO,CAAC;EAC9C,MAAME,iBAAiB,CAACH,MAAM,EAAEC,OAAO,CAAC;AAC5C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
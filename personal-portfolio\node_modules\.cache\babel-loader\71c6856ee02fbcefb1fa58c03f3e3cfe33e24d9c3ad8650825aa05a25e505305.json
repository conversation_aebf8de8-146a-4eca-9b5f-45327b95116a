{"ast": null, "code": "const fixFactorSquared = 2,\n  fixFactor = Math.sqrt(fixFactorSquared),\n  double = 2;\nexport function drawSquare(data) {\n  const {\n      context,\n      radius\n    } = data,\n    fixedRadius = radius / fixFactor,\n    fixedDiameter = fixedRadius * double;\n  context.rect(-fixedRadius, -fixedRadius, fixedDiameter, fixedDiameter);\n}", "map": {"version": 3, "names": ["fixFactorSquared", "fixFactor", "Math", "sqrt", "double", "drawSquare", "data", "context", "radius", "fixedRadius", "fixedDiameter", "rect"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/shape-square/browser/Utils.js"], "sourcesContent": ["const fixFactorSquared = 2, fixFactor = Math.sqrt(fixFactorSquared), double = 2;\nexport function drawSquare(data) {\n    const { context, radius } = data, fixedRadius = radius / fixFactor, fixedDiameter = fixedRadius * double;\n    context.rect(-fixedRadius, -fixedRadius, fixedDiameter, fixedDiameter);\n}\n"], "mappings": "AAAA,MAAMA,gBAAgB,GAAG,CAAC;EAAEC,SAAS,GAAGC,IAAI,CAACC,IAAI,CAACH,gBAAgB,CAAC;EAAEI,MAAM,GAAG,CAAC;AAC/E,OAAO,SAASC,UAAUA,CAACC,IAAI,EAAE;EAC7B,MAAM;MAAEC,OAAO;MAAEC;IAAO,CAAC,GAAGF,IAAI;IAAEG,WAAW,GAAGD,MAAM,GAAGP,SAAS;IAAES,aAAa,GAAGD,WAAW,GAAGL,MAAM;EACxGG,OAAO,CAACI,IAAI,CAAC,CAACF,WAAW,EAAE,CAACA,WAAW,EAAEC,aAAa,EAAEA,aAAa,CAAC;AAC1E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
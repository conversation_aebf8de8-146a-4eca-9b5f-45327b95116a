{"ast": null, "code": "import { InterlaceOffsets, InterlaceSteps } from \"./Constants\";\nimport { ByteStream } from \"./ByteStream\";\nfunction parseColorTable(byteStream, count) {\n  const colors = [];\n  for (let i = 0; i < count; i++) {\n    colors.push({\n      r: byteStream.data[byteStream.pos],\n      g: byteStream.data[byteStream.pos + 1],\n      b: byteStream.data[byteStream.pos + 2]\n    });\n    byteStream.pos += 3;\n  }\n  return colors;\n}\nasync function parseExtensionBlock(byteStream, gif, getFrameIndex, getTransparencyIndex) {\n  switch (byteStream.nextByte()) {\n    case 249:\n      {\n        const frame = gif.frames[getFrameIndex(false)];\n        byteStream.pos++;\n        const packedByte = byteStream.nextByte();\n        frame.GCreserved = (packedByte & 0xe0) >>> 5;\n        frame.disposalMethod = (packedByte & 0x1c) >>> 2;\n        frame.userInputDelayFlag = (packedByte & 2) === 2;\n        const transparencyFlag = (packedByte & 1) === 1;\n        frame.delayTime = byteStream.nextTwoBytes() * 0xa;\n        const transparencyIndex = byteStream.nextByte();\n        if (transparencyFlag) {\n          getTransparencyIndex(transparencyIndex);\n        }\n        byteStream.pos++;\n        break;\n      }\n    case 255:\n      {\n        byteStream.pos++;\n        const applicationExtension = {\n          identifier: byteStream.getString(8),\n          authenticationCode: byteStream.getString(3),\n          data: byteStream.readSubBlocksBin()\n        };\n        gif.applicationExtensions.push(applicationExtension);\n        break;\n      }\n    case 254:\n      {\n        gif.comments.push([getFrameIndex(false), byteStream.readSubBlocks()]);\n        break;\n      }\n    case 1:\n      {\n        if (gif.globalColorTable.length === 0) {\n          throw new EvalError(\"plain text extension without global color table\");\n        }\n        byteStream.pos++;\n        gif.frames[getFrameIndex(false)].plainTextData = {\n          left: byteStream.nextTwoBytes(),\n          top: byteStream.nextTwoBytes(),\n          width: byteStream.nextTwoBytes(),\n          height: byteStream.nextTwoBytes(),\n          charSize: {\n            width: byteStream.nextTwoBytes(),\n            height: byteStream.nextTwoBytes()\n          },\n          foregroundColor: byteStream.nextByte(),\n          backgroundColor: byteStream.nextByte(),\n          text: byteStream.readSubBlocks()\n        };\n        break;\n      }\n    default:\n      byteStream.skipSubBlocks();\n      break;\n  }\n}\nasync function parseImageBlock(byteStream, gif, avgAlpha, getFrameIndex, getTransparencyIndex, progressCallback) {\n  const frame = gif.frames[getFrameIndex(true)];\n  frame.left = byteStream.nextTwoBytes();\n  frame.top = byteStream.nextTwoBytes();\n  frame.width = byteStream.nextTwoBytes();\n  frame.height = byteStream.nextTwoBytes();\n  const packedByte = byteStream.nextByte(),\n    localColorTableFlag = (packedByte & 0x80) === 0x80,\n    interlacedFlag = (packedByte & 0x40) === 0x40;\n  frame.sortFlag = (packedByte & 0x20) === 0x20;\n  frame.reserved = (packedByte & 0x18) >>> 3;\n  const localColorCount = 1 << (packedByte & 7) + 1;\n  if (localColorTableFlag) {\n    frame.localColorTable = parseColorTable(byteStream, localColorCount);\n  }\n  const getColor = index => {\n    const {\n      r,\n      g,\n      b\n    } = (localColorTableFlag ? frame.localColorTable : gif.globalColorTable)[index];\n    return {\n      r,\n      g,\n      b,\n      a: index === getTransparencyIndex(null) ? avgAlpha ? ~~((r + g + b) / 3) : 0 : 255\n    };\n  };\n  const image = (() => {\n    try {\n      return new ImageData(frame.width, frame.height, {\n        colorSpace: \"srgb\"\n      });\n    } catch (error) {\n      if (error instanceof DOMException && error.name === \"IndexSizeError\") {\n        return null;\n      }\n      throw error;\n    }\n  })();\n  if (image == null) {\n    throw new EvalError(\"GIF frame size is to large\");\n  }\n  const minCodeSize = byteStream.nextByte(),\n    imageData = byteStream.readSubBlocksBin(),\n    clearCode = 1 << minCodeSize;\n  const readBits = (pos, len) => {\n    const bytePos = pos >>> 3,\n      bitPos = pos & 7;\n    return (imageData[bytePos] + (imageData[bytePos + 1] << 8) + (imageData[bytePos + 2] << 16) & (1 << len) - 1 << bitPos) >>> bitPos;\n  };\n  if (interlacedFlag) {\n    for (let code = 0, size = minCodeSize + 1, pos = 0, dic = [[0]], pass = 0; pass < 4; pass++) {\n      if (InterlaceOffsets[pass] < frame.height) {\n        for (let pixelPos = 0, lineIndex = 0;;) {\n          const last = code;\n          code = readBits(pos, size);\n          pos += size + 1;\n          if (code === clearCode) {\n            size = minCodeSize + 1;\n            dic.length = clearCode + 2;\n            for (let i = 0; i < dic.length; i++) {\n              dic[i] = i < clearCode ? [i] : [];\n            }\n          } else {\n            if (code >= dic.length) {\n              dic.push(dic[last].concat(dic[last][0]));\n            } else if (last !== clearCode) {\n              dic.push(dic[last].concat(dic[code][0]));\n            }\n            for (let i = 0; i < dic[code].length; i++) {\n              const {\n                r,\n                g,\n                b,\n                a\n              } = getColor(dic[code][i]);\n              image.data.set([r, g, b, a], InterlaceOffsets[pass] * frame.width + InterlaceSteps[pass] * lineIndex + pixelPos % (frame.width * 4));\n              pixelPos += 4;\n            }\n            if (dic.length === 1 << size && size < 0xc) {\n              size++;\n            }\n          }\n          if (pixelPos === frame.width * 4 * (lineIndex + 1)) {\n            lineIndex++;\n            if (InterlaceOffsets[pass] + InterlaceSteps[pass] * lineIndex >= frame.height) {\n              break;\n            }\n          }\n        }\n      }\n      progressCallback?.(byteStream.pos / (byteStream.data.length - 1), getFrameIndex(false) + 1, image, {\n        x: frame.left,\n        y: frame.top\n      }, {\n        width: gif.width,\n        height: gif.height\n      });\n    }\n    frame.image = image;\n    frame.bitmap = await createImageBitmap(image);\n  } else {\n    for (let code = 0, size = minCodeSize + 1, pos = 0, dic = [[0]], pixelPos = -4;;) {\n      const last = code;\n      code = readBits(pos, size);\n      pos += size;\n      if (code === clearCode) {\n        size = minCodeSize + 1;\n        dic.length = clearCode + 2;\n        for (let i = 0; i < dic.length; i++) {\n          dic[i] = i < clearCode ? [i] : [];\n        }\n      } else {\n        if (code === clearCode + 1) {\n          break;\n        }\n        if (code >= dic.length) {\n          dic.push(dic[last].concat(dic[last][0]));\n        } else if (last !== clearCode) {\n          dic.push(dic[last].concat(dic[code][0]));\n        }\n        for (let i = 0; i < dic[code].length; i++) {\n          const {\n            r,\n            g,\n            b,\n            a\n          } = getColor(dic[code][i]);\n          image.data.set([r, g, b, a], pixelPos += 4);\n        }\n        if (dic.length >= 1 << size && size < 0xc) {\n          size++;\n        }\n      }\n    }\n    frame.image = image;\n    frame.bitmap = await createImageBitmap(image);\n    progressCallback?.((byteStream.pos + 1) / byteStream.data.length, getFrameIndex(false) + 1, frame.image, {\n      x: frame.left,\n      y: frame.top\n    }, {\n      width: gif.width,\n      height: gif.height\n    });\n  }\n}\nasync function parseBlock(byteStream, gif, avgAlpha, getFrameIndex, getTransparencyIndex, progressCallback) {\n  switch (byteStream.nextByte()) {\n    case 59:\n      return true;\n    case 44:\n      await parseImageBlock(byteStream, gif, avgAlpha, getFrameIndex, getTransparencyIndex, progressCallback);\n      break;\n    case 33:\n      await parseExtensionBlock(byteStream, gif, getFrameIndex, getTransparencyIndex);\n      break;\n    default:\n      throw new EvalError(\"undefined block found\");\n  }\n  return false;\n}\nexport function getGIFLoopAmount(gif) {\n  for (const extension of gif.applicationExtensions) {\n    if (extension.identifier + extension.authenticationCode !== \"NETSCAPE2.0\") {\n      continue;\n    }\n    return extension.data[1] + (extension.data[2] << 8);\n  }\n  return NaN;\n}\nexport async function decodeGIF(gifURL, progressCallback, avgAlpha) {\n  if (!avgAlpha) avgAlpha = false;\n  const res = await fetch(gifURL);\n  if (!res.ok && res.status === 404) {\n    throw new EvalError(\"file not found\");\n  }\n  const buffer = await res.arrayBuffer();\n  const gif = {\n      width: 0,\n      height: 0,\n      totalTime: 0,\n      colorRes: 0,\n      pixelAspectRatio: 0,\n      frames: [],\n      sortFlag: false,\n      globalColorTable: [],\n      backgroundImage: new ImageData(1, 1, {\n        colorSpace: \"srgb\"\n      }),\n      comments: [],\n      applicationExtensions: []\n    },\n    byteStream = new ByteStream(new Uint8ClampedArray(buffer));\n  if (byteStream.getString(6) !== \"GIF89a\") {\n    throw new Error(\"not a supported GIF file\");\n  }\n  gif.width = byteStream.nextTwoBytes();\n  gif.height = byteStream.nextTwoBytes();\n  const packedByte = byteStream.nextByte(),\n    globalColorTableFlag = (packedByte & 0x80) === 0x80;\n  gif.colorRes = (packedByte & 0x70) >>> 4;\n  gif.sortFlag = (packedByte & 8) === 8;\n  const globalColorCount = 1 << (packedByte & 7) + 1,\n    backgroundColorIndex = byteStream.nextByte();\n  gif.pixelAspectRatio = byteStream.nextByte();\n  if (gif.pixelAspectRatio !== 0) {\n    gif.pixelAspectRatio = (gif.pixelAspectRatio + 0xf) / 0x40;\n  }\n  if (globalColorTableFlag) {\n    gif.globalColorTable = parseColorTable(byteStream, globalColorCount);\n  }\n  const backgroundImage = (() => {\n    try {\n      return new ImageData(gif.width, gif.height, {\n        colorSpace: \"srgb\"\n      });\n    } catch (error) {\n      if (error instanceof DOMException && error.name === \"IndexSizeError\") {\n        return null;\n      }\n      throw error;\n    }\n  })();\n  if (backgroundImage == null) {\n    throw new Error(\"GIF frame size is to large\");\n  }\n  const {\n    r,\n    g,\n    b\n  } = gif.globalColorTable[backgroundColorIndex];\n  backgroundImage.data.set(globalColorTableFlag ? [r, g, b, 255] : [0, 0, 0, 0]);\n  for (let i = 4; i < backgroundImage.data.length; i *= 2) {\n    backgroundImage.data.copyWithin(i, 0, i);\n  }\n  gif.backgroundImage = backgroundImage;\n  let frameIndex = -1,\n    incrementFrameIndex = true,\n    transparencyIndex = -1;\n  const getframeIndex = increment => {\n    if (increment) {\n      incrementFrameIndex = true;\n    }\n    return frameIndex;\n  };\n  const getTransparencyIndex = newValue => {\n    if (newValue != null) {\n      transparencyIndex = newValue;\n    }\n    return transparencyIndex;\n  };\n  try {\n    do {\n      if (incrementFrameIndex) {\n        gif.frames.push({\n          left: 0,\n          top: 0,\n          width: 0,\n          height: 0,\n          disposalMethod: 0,\n          image: new ImageData(1, 1, {\n            colorSpace: \"srgb\"\n          }),\n          plainTextData: null,\n          userInputDelayFlag: false,\n          delayTime: 0,\n          sortFlag: false,\n          localColorTable: [],\n          reserved: 0,\n          GCreserved: 0\n        });\n        frameIndex++;\n        transparencyIndex = -1;\n        incrementFrameIndex = false;\n      }\n    } while (!(await parseBlock(byteStream, gif, avgAlpha, getframeIndex, getTransparencyIndex, progressCallback)));\n    gif.frames.length--;\n    for (const frame of gif.frames) {\n      if (frame.userInputDelayFlag && frame.delayTime === 0) {\n        gif.totalTime = Infinity;\n        break;\n      }\n      gif.totalTime += frame.delayTime;\n    }\n    return gif;\n  } catch (error) {\n    if (error instanceof EvalError) {\n      throw new Error(`error while parsing frame ${frameIndex} \"${error.message}\"`);\n    }\n    throw error;\n  }\n}", "map": {"version": 3, "names": ["InterlaceOffsets", "InterlaceSteps", "ByteStream", "parseColorTable", "byteStream", "count", "colors", "i", "push", "r", "data", "pos", "g", "b", "parseExtensionBlock", "gif", "getFrameIndex", "getTransparencyIndex", "nextByte", "frame", "frames", "packedByte", "GCreserved", "disposalMethod", "userInputDelayFlag", "transparencyFlag", "delayTime", "nextTwoBytes", "transparencyIndex", "applicationExtension", "identifier", "getString", "authenticationCode", "readSubBlocksBin", "applicationExtensions", "comments", "readSubBlocks", "globalColorTable", "length", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "plainTextData", "left", "top", "width", "height", "charSize", "foregroundColor", "backgroundColor", "text", "skip<PERSON><PERSON><PERSON><PERSON>s", "parseImageBlock", "avgAlpha", "progressCallback", "localColorTableFlag", "interlacedFlag", "sortFlag", "reserved", "localColorCount", "localColorTable", "getColor", "index", "a", "image", "ImageData", "colorSpace", "error", "DOMException", "name", "minCodeSize", "imageData", "clearCode", "readBits", "len", "bytePos", "bitPos", "code", "size", "dic", "pass", "pixelPos", "lineIndex", "last", "concat", "set", "x", "y", "bitmap", "createImageBitmap", "parseBlock", "getGIFLoopAmount", "extension", "NaN", "decodeGIF", "gifURL", "res", "fetch", "ok", "status", "buffer", "arrayBuffer", "totalTime", "colorRes", "pixelAspectRatio", "backgroundImage", "Uint8ClampedArray", "Error", "globalColorTableFlag", "globalColorCount", "backgroundColorIndex", "copyWithin", "frameIndex", "incrementFrameIndex", "getframeIndex", "increment", "newValue", "Infinity", "message"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/tsparticles-shape-image/esm/GifUtils/Utils.js"], "sourcesContent": ["import { InterlaceOffsets, InterlaceSteps } from \"./Constants\";\nimport { ByteStream } from \"./ByteStream\";\nfunction parseColorTable(byteStream, count) {\n    const colors = [];\n    for (let i = 0; i < count; i++) {\n        colors.push({\n            r: byteStream.data[byteStream.pos],\n            g: byteStream.data[byteStream.pos + 1],\n            b: byteStream.data[byteStream.pos + 2],\n        });\n        byteStream.pos += 3;\n    }\n    return colors;\n}\nasync function parseExtensionBlock(byteStream, gif, getFrameIndex, getTransparencyIndex) {\n    switch (byteStream.nextByte()) {\n        case 249: {\n            const frame = gif.frames[getFrameIndex(false)];\n            byteStream.pos++;\n            const packedByte = byteStream.nextByte();\n            frame.GCreserved = (packedByte & 0xe0) >>> 5;\n            frame.disposalMethod = (packedByte & 0x1c) >>> 2;\n            frame.userInputDelayFlag = (packedByte & 2) === 2;\n            const transparencyFlag = (packedByte & 1) === 1;\n            frame.delayTime = byteStream.nextTwoBytes() * 0xa;\n            const transparencyIndex = byteStream.nextByte();\n            if (transparencyFlag) {\n                getTransparencyIndex(transparencyIndex);\n            }\n            byteStream.pos++;\n            break;\n        }\n        case 255: {\n            byteStream.pos++;\n            const applicationExtension = {\n                identifier: byteStream.getString(8),\n                authenticationCode: byteStream.getString(3),\n                data: byteStream.readSubBlocksBin(),\n            };\n            gif.applicationExtensions.push(applicationExtension);\n            break;\n        }\n        case 254: {\n            gif.comments.push([getFrameIndex(false), byteStream.readSubBlocks()]);\n            break;\n        }\n        case 1: {\n            if (gif.globalColorTable.length === 0) {\n                throw new EvalError(\"plain text extension without global color table\");\n            }\n            byteStream.pos++;\n            gif.frames[getFrameIndex(false)].plainTextData = {\n                left: byteStream.nextTwoBytes(),\n                top: byteStream.nextTwoBytes(),\n                width: byteStream.nextTwoBytes(),\n                height: byteStream.nextTwoBytes(),\n                charSize: {\n                    width: byteStream.nextTwoBytes(),\n                    height: byteStream.nextTwoBytes(),\n                },\n                foregroundColor: byteStream.nextByte(),\n                backgroundColor: byteStream.nextByte(),\n                text: byteStream.readSubBlocks(),\n            };\n            break;\n        }\n        default:\n            byteStream.skipSubBlocks();\n            break;\n    }\n}\nasync function parseImageBlock(byteStream, gif, avgAlpha, getFrameIndex, getTransparencyIndex, progressCallback) {\n    const frame = gif.frames[getFrameIndex(true)];\n    frame.left = byteStream.nextTwoBytes();\n    frame.top = byteStream.nextTwoBytes();\n    frame.width = byteStream.nextTwoBytes();\n    frame.height = byteStream.nextTwoBytes();\n    const packedByte = byteStream.nextByte(), localColorTableFlag = (packedByte & 0x80) === 0x80, interlacedFlag = (packedByte & 0x40) === 0x40;\n    frame.sortFlag = (packedByte & 0x20) === 0x20;\n    frame.reserved = (packedByte & 0x18) >>> 3;\n    const localColorCount = 1 << ((packedByte & 7) + 1);\n    if (localColorTableFlag) {\n        frame.localColorTable = parseColorTable(byteStream, localColorCount);\n    }\n    const getColor = (index) => {\n        const { r, g, b } = (localColorTableFlag ? frame.localColorTable : gif.globalColorTable)[index];\n        return { r, g, b, a: index === getTransparencyIndex(null) ? (avgAlpha ? ~~((r + g + b) / 3) : 0) : 255 };\n    };\n    const image = (() => {\n        try {\n            return new ImageData(frame.width, frame.height, { colorSpace: \"srgb\" });\n        }\n        catch (error) {\n            if (error instanceof DOMException && error.name === \"IndexSizeError\") {\n                return null;\n            }\n            throw error;\n        }\n    })();\n    if (image == null) {\n        throw new EvalError(\"GIF frame size is to large\");\n    }\n    const minCodeSize = byteStream.nextByte(), imageData = byteStream.readSubBlocksBin(), clearCode = 1 << minCodeSize;\n    const readBits = (pos, len) => {\n        const bytePos = pos >>> 3, bitPos = pos & 7;\n        return (((imageData[bytePos] + (imageData[bytePos + 1] << 8) + (imageData[bytePos + 2] << 16)) &\n            (((1 << len) - 1) << bitPos)) >>>\n            bitPos);\n    };\n    if (interlacedFlag) {\n        for (let code = 0, size = minCodeSize + 1, pos = 0, dic = [[0]], pass = 0; pass < 4; pass++) {\n            if (InterlaceOffsets[pass] < frame.height) {\n                for (let pixelPos = 0, lineIndex = 0;;) {\n                    const last = code;\n                    code = readBits(pos, size);\n                    pos += size + 1;\n                    if (code === clearCode) {\n                        size = minCodeSize + 1;\n                        dic.length = clearCode + 2;\n                        for (let i = 0; i < dic.length; i++) {\n                            dic[i] = i < clearCode ? [i] : [];\n                        }\n                    }\n                    else {\n                        if (code >= dic.length) {\n                            dic.push(dic[last].concat(dic[last][0]));\n                        }\n                        else if (last !== clearCode) {\n                            dic.push(dic[last].concat(dic[code][0]));\n                        }\n                        for (let i = 0; i < dic[code].length; i++) {\n                            const { r, g, b, a } = getColor(dic[code][i]);\n                            image.data.set([r, g, b, a], InterlaceOffsets[pass] * frame.width +\n                                InterlaceSteps[pass] * lineIndex +\n                                (pixelPos % (frame.width * 4)));\n                            pixelPos += 4;\n                        }\n                        if (dic.length === 1 << size && size < 0xc) {\n                            size++;\n                        }\n                    }\n                    if (pixelPos === frame.width * 4 * (lineIndex + 1)) {\n                        lineIndex++;\n                        if (InterlaceOffsets[pass] + InterlaceSteps[pass] * lineIndex >= frame.height) {\n                            break;\n                        }\n                    }\n                }\n            }\n            progressCallback?.(byteStream.pos / (byteStream.data.length - 1), getFrameIndex(false) + 1, image, { x: frame.left, y: frame.top }, { width: gif.width, height: gif.height });\n        }\n        frame.image = image;\n        frame.bitmap = await createImageBitmap(image);\n    }\n    else {\n        for (let code = 0, size = minCodeSize + 1, pos = 0, dic = [[0]], pixelPos = -4;;) {\n            const last = code;\n            code = readBits(pos, size);\n            pos += size;\n            if (code === clearCode) {\n                size = minCodeSize + 1;\n                dic.length = clearCode + 2;\n                for (let i = 0; i < dic.length; i++) {\n                    dic[i] = i < clearCode ? [i] : [];\n                }\n            }\n            else {\n                if (code === clearCode + 1) {\n                    break;\n                }\n                if (code >= dic.length) {\n                    dic.push(dic[last].concat(dic[last][0]));\n                }\n                else if (last !== clearCode) {\n                    dic.push(dic[last].concat(dic[code][0]));\n                }\n                for (let i = 0; i < dic[code].length; i++) {\n                    const { r, g, b, a } = getColor(dic[code][i]);\n                    image.data.set([r, g, b, a], (pixelPos += 4));\n                }\n                if (dic.length >= 1 << size && size < 0xc) {\n                    size++;\n                }\n            }\n        }\n        frame.image = image;\n        frame.bitmap = await createImageBitmap(image);\n        progressCallback?.((byteStream.pos + 1) / byteStream.data.length, getFrameIndex(false) + 1, frame.image, { x: frame.left, y: frame.top }, { width: gif.width, height: gif.height });\n    }\n}\nasync function parseBlock(byteStream, gif, avgAlpha, getFrameIndex, getTransparencyIndex, progressCallback) {\n    switch (byteStream.nextByte()) {\n        case 59:\n            return true;\n        case 44:\n            await parseImageBlock(byteStream, gif, avgAlpha, getFrameIndex, getTransparencyIndex, progressCallback);\n            break;\n        case 33:\n            await parseExtensionBlock(byteStream, gif, getFrameIndex, getTransparencyIndex);\n            break;\n        default:\n            throw new EvalError(\"undefined block found\");\n    }\n    return false;\n}\nexport function getGIFLoopAmount(gif) {\n    for (const extension of gif.applicationExtensions) {\n        if (extension.identifier + extension.authenticationCode !== \"NETSCAPE2.0\") {\n            continue;\n        }\n        return extension.data[1] + (extension.data[2] << 8);\n    }\n    return NaN;\n}\nexport async function decodeGIF(gifURL, progressCallback, avgAlpha) {\n    if (!avgAlpha)\n        avgAlpha = false;\n    const res = await fetch(gifURL);\n    if (!res.ok && res.status === 404) {\n        throw new EvalError(\"file not found\");\n    }\n    const buffer = await res.arrayBuffer();\n    const gif = {\n        width: 0,\n        height: 0,\n        totalTime: 0,\n        colorRes: 0,\n        pixelAspectRatio: 0,\n        frames: [],\n        sortFlag: false,\n        globalColorTable: [],\n        backgroundImage: new ImageData(1, 1, { colorSpace: \"srgb\" }),\n        comments: [],\n        applicationExtensions: [],\n    }, byteStream = new ByteStream(new Uint8ClampedArray(buffer));\n    if (byteStream.getString(6) !== \"GIF89a\") {\n        throw new Error(\"not a supported GIF file\");\n    }\n    gif.width = byteStream.nextTwoBytes();\n    gif.height = byteStream.nextTwoBytes();\n    const packedByte = byteStream.nextByte(), globalColorTableFlag = (packedByte & 0x80) === 0x80;\n    gif.colorRes = (packedByte & 0x70) >>> 4;\n    gif.sortFlag = (packedByte & 8) === 8;\n    const globalColorCount = 1 << ((packedByte & 7) + 1), backgroundColorIndex = byteStream.nextByte();\n    gif.pixelAspectRatio = byteStream.nextByte();\n    if (gif.pixelAspectRatio !== 0) {\n        gif.pixelAspectRatio = (gif.pixelAspectRatio + 0xf) / 0x40;\n    }\n    if (globalColorTableFlag) {\n        gif.globalColorTable = parseColorTable(byteStream, globalColorCount);\n    }\n    const backgroundImage = (() => {\n        try {\n            return new ImageData(gif.width, gif.height, { colorSpace: \"srgb\" });\n        }\n        catch (error) {\n            if (error instanceof DOMException && error.name === \"IndexSizeError\") {\n                return null;\n            }\n            throw error;\n        }\n    })();\n    if (backgroundImage == null) {\n        throw new Error(\"GIF frame size is to large\");\n    }\n    const { r, g, b } = gif.globalColorTable[backgroundColorIndex];\n    backgroundImage.data.set(globalColorTableFlag ? [r, g, b, 255] : [0, 0, 0, 0]);\n    for (let i = 4; i < backgroundImage.data.length; i *= 2) {\n        backgroundImage.data.copyWithin(i, 0, i);\n    }\n    gif.backgroundImage = backgroundImage;\n    let frameIndex = -1, incrementFrameIndex = true, transparencyIndex = -1;\n    const getframeIndex = (increment) => {\n        if (increment) {\n            incrementFrameIndex = true;\n        }\n        return frameIndex;\n    };\n    const getTransparencyIndex = (newValue) => {\n        if (newValue != null) {\n            transparencyIndex = newValue;\n        }\n        return transparencyIndex;\n    };\n    try {\n        do {\n            if (incrementFrameIndex) {\n                gif.frames.push({\n                    left: 0,\n                    top: 0,\n                    width: 0,\n                    height: 0,\n                    disposalMethod: 0,\n                    image: new ImageData(1, 1, { colorSpace: \"srgb\" }),\n                    plainTextData: null,\n                    userInputDelayFlag: false,\n                    delayTime: 0,\n                    sortFlag: false,\n                    localColorTable: [],\n                    reserved: 0,\n                    GCreserved: 0,\n                });\n                frameIndex++;\n                transparencyIndex = -1;\n                incrementFrameIndex = false;\n            }\n        } while (!(await parseBlock(byteStream, gif, avgAlpha, getframeIndex, getTransparencyIndex, progressCallback)));\n        gif.frames.length--;\n        for (const frame of gif.frames) {\n            if (frame.userInputDelayFlag && frame.delayTime === 0) {\n                gif.totalTime = Infinity;\n                break;\n            }\n            gif.totalTime += frame.delayTime;\n        }\n        return gif;\n    }\n    catch (error) {\n        if (error instanceof EvalError) {\n            throw new Error(`error while parsing frame ${frameIndex} \"${error.message}\"`);\n        }\n        throw error;\n    }\n}\n"], "mappings": "AAAA,SAASA,gBAAgB,EAAEC,cAAc,QAAQ,aAAa;AAC9D,SAASC,UAAU,QAAQ,cAAc;AACzC,SAASC,eAAeA,CAACC,UAAU,EAAEC,KAAK,EAAE;EACxC,MAAMC,MAAM,GAAG,EAAE;EACjB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,KAAK,EAAEE,CAAC,EAAE,EAAE;IAC5BD,MAAM,CAACE,IAAI,CAAC;MACRC,CAAC,EAAEL,UAAU,CAACM,IAAI,CAACN,UAAU,CAACO,GAAG,CAAC;MAClCC,CAAC,EAAER,UAAU,CAACM,IAAI,CAACN,UAAU,CAACO,GAAG,GAAG,CAAC,CAAC;MACtCE,CAAC,EAAET,UAAU,CAACM,IAAI,CAACN,UAAU,CAACO,GAAG,GAAG,CAAC;IACzC,CAAC,CAAC;IACFP,UAAU,CAACO,GAAG,IAAI,CAAC;EACvB;EACA,OAAOL,MAAM;AACjB;AACA,eAAeQ,mBAAmBA,CAACV,UAAU,EAAEW,GAAG,EAAEC,aAAa,EAAEC,oBAAoB,EAAE;EACrF,QAAQb,UAAU,CAACc,QAAQ,CAAC,CAAC;IACzB,KAAK,GAAG;MAAE;QACN,MAAMC,KAAK,GAAGJ,GAAG,CAACK,MAAM,CAACJ,aAAa,CAAC,KAAK,CAAC,CAAC;QAC9CZ,UAAU,CAACO,GAAG,EAAE;QAChB,MAAMU,UAAU,GAAGjB,UAAU,CAACc,QAAQ,CAAC,CAAC;QACxCC,KAAK,CAACG,UAAU,GAAG,CAACD,UAAU,GAAG,IAAI,MAAM,CAAC;QAC5CF,KAAK,CAACI,cAAc,GAAG,CAACF,UAAU,GAAG,IAAI,MAAM,CAAC;QAChDF,KAAK,CAACK,kBAAkB,GAAG,CAACH,UAAU,GAAG,CAAC,MAAM,CAAC;QACjD,MAAMI,gBAAgB,GAAG,CAACJ,UAAU,GAAG,CAAC,MAAM,CAAC;QAC/CF,KAAK,CAACO,SAAS,GAAGtB,UAAU,CAACuB,YAAY,CAAC,CAAC,GAAG,GAAG;QACjD,MAAMC,iBAAiB,GAAGxB,UAAU,CAACc,QAAQ,CAAC,CAAC;QAC/C,IAAIO,gBAAgB,EAAE;UAClBR,oBAAoB,CAACW,iBAAiB,CAAC;QAC3C;QACAxB,UAAU,CAACO,GAAG,EAAE;QAChB;MACJ;IACA,KAAK,GAAG;MAAE;QACNP,UAAU,CAACO,GAAG,EAAE;QAChB,MAAMkB,oBAAoB,GAAG;UACzBC,UAAU,EAAE1B,UAAU,CAAC2B,SAAS,CAAC,CAAC,CAAC;UACnCC,kBAAkB,EAAE5B,UAAU,CAAC2B,SAAS,CAAC,CAAC,CAAC;UAC3CrB,IAAI,EAAEN,UAAU,CAAC6B,gBAAgB,CAAC;QACtC,CAAC;QACDlB,GAAG,CAACmB,qBAAqB,CAAC1B,IAAI,CAACqB,oBAAoB,CAAC;QACpD;MACJ;IACA,KAAK,GAAG;MAAE;QACNd,GAAG,CAACoB,QAAQ,CAAC3B,IAAI,CAAC,CAACQ,aAAa,CAAC,KAAK,CAAC,EAAEZ,UAAU,CAACgC,aAAa,CAAC,CAAC,CAAC,CAAC;QACrE;MACJ;IACA,KAAK,CAAC;MAAE;QACJ,IAAIrB,GAAG,CAACsB,gBAAgB,CAACC,MAAM,KAAK,CAAC,EAAE;UACnC,MAAM,IAAIC,SAAS,CAAC,iDAAiD,CAAC;QAC1E;QACAnC,UAAU,CAACO,GAAG,EAAE;QAChBI,GAAG,CAACK,MAAM,CAACJ,aAAa,CAAC,KAAK,CAAC,CAAC,CAACwB,aAAa,GAAG;UAC7CC,IAAI,EAAErC,UAAU,CAACuB,YAAY,CAAC,CAAC;UAC/Be,GAAG,EAAEtC,UAAU,CAACuB,YAAY,CAAC,CAAC;UAC9BgB,KAAK,EAAEvC,UAAU,CAACuB,YAAY,CAAC,CAAC;UAChCiB,MAAM,EAAExC,UAAU,CAACuB,YAAY,CAAC,CAAC;UACjCkB,QAAQ,EAAE;YACNF,KAAK,EAAEvC,UAAU,CAACuB,YAAY,CAAC,CAAC;YAChCiB,MAAM,EAAExC,UAAU,CAACuB,YAAY,CAAC;UACpC,CAAC;UACDmB,eAAe,EAAE1C,UAAU,CAACc,QAAQ,CAAC,CAAC;UACtC6B,eAAe,EAAE3C,UAAU,CAACc,QAAQ,CAAC,CAAC;UACtC8B,IAAI,EAAE5C,UAAU,CAACgC,aAAa,CAAC;QACnC,CAAC;QACD;MACJ;IACA;MACIhC,UAAU,CAAC6C,aAAa,CAAC,CAAC;MAC1B;EACR;AACJ;AACA,eAAeC,eAAeA,CAAC9C,UAAU,EAAEW,GAAG,EAAEoC,QAAQ,EAAEnC,aAAa,EAAEC,oBAAoB,EAAEmC,gBAAgB,EAAE;EAC7G,MAAMjC,KAAK,GAAGJ,GAAG,CAACK,MAAM,CAACJ,aAAa,CAAC,IAAI,CAAC,CAAC;EAC7CG,KAAK,CAACsB,IAAI,GAAGrC,UAAU,CAACuB,YAAY,CAAC,CAAC;EACtCR,KAAK,CAACuB,GAAG,GAAGtC,UAAU,CAACuB,YAAY,CAAC,CAAC;EACrCR,KAAK,CAACwB,KAAK,GAAGvC,UAAU,CAACuB,YAAY,CAAC,CAAC;EACvCR,KAAK,CAACyB,MAAM,GAAGxC,UAAU,CAACuB,YAAY,CAAC,CAAC;EACxC,MAAMN,UAAU,GAAGjB,UAAU,CAACc,QAAQ,CAAC,CAAC;IAAEmC,mBAAmB,GAAG,CAAChC,UAAU,GAAG,IAAI,MAAM,IAAI;IAAEiC,cAAc,GAAG,CAACjC,UAAU,GAAG,IAAI,MAAM,IAAI;EAC3IF,KAAK,CAACoC,QAAQ,GAAG,CAAClC,UAAU,GAAG,IAAI,MAAM,IAAI;EAC7CF,KAAK,CAACqC,QAAQ,GAAG,CAACnC,UAAU,GAAG,IAAI,MAAM,CAAC;EAC1C,MAAMoC,eAAe,GAAG,CAAC,IAAK,CAACpC,UAAU,GAAG,CAAC,IAAI,CAAE;EACnD,IAAIgC,mBAAmB,EAAE;IACrBlC,KAAK,CAACuC,eAAe,GAAGvD,eAAe,CAACC,UAAU,EAAEqD,eAAe,CAAC;EACxE;EACA,MAAME,QAAQ,GAAIC,KAAK,IAAK;IACxB,MAAM;MAAEnD,CAAC;MAAEG,CAAC;MAAEC;IAAE,CAAC,GAAG,CAACwC,mBAAmB,GAAGlC,KAAK,CAACuC,eAAe,GAAG3C,GAAG,CAACsB,gBAAgB,EAAEuB,KAAK,CAAC;IAC/F,OAAO;MAAEnD,CAAC;MAAEG,CAAC;MAAEC,CAAC;MAAEgD,CAAC,EAAED,KAAK,KAAK3C,oBAAoB,CAAC,IAAI,CAAC,GAAIkC,QAAQ,GAAG,CAAC,EAAE,CAAC1C,CAAC,GAAGG,CAAC,GAAGC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,GAAI;IAAI,CAAC;EAC5G,CAAC;EACD,MAAMiD,KAAK,GAAG,CAAC,MAAM;IACjB,IAAI;MACA,OAAO,IAAIC,SAAS,CAAC5C,KAAK,CAACwB,KAAK,EAAExB,KAAK,CAACyB,MAAM,EAAE;QAAEoB,UAAU,EAAE;MAAO,CAAC,CAAC;IAC3E,CAAC,CACD,OAAOC,KAAK,EAAE;MACV,IAAIA,KAAK,YAAYC,YAAY,IAAID,KAAK,CAACE,IAAI,KAAK,gBAAgB,EAAE;QAClE,OAAO,IAAI;MACf;MACA,MAAMF,KAAK;IACf;EACJ,CAAC,EAAE,CAAC;EACJ,IAAIH,KAAK,IAAI,IAAI,EAAE;IACf,MAAM,IAAIvB,SAAS,CAAC,4BAA4B,CAAC;EACrD;EACA,MAAM6B,WAAW,GAAGhE,UAAU,CAACc,QAAQ,CAAC,CAAC;IAAEmD,SAAS,GAAGjE,UAAU,CAAC6B,gBAAgB,CAAC,CAAC;IAAEqC,SAAS,GAAG,CAAC,IAAIF,WAAW;EAClH,MAAMG,QAAQ,GAAGA,CAAC5D,GAAG,EAAE6D,GAAG,KAAK;IAC3B,MAAMC,OAAO,GAAG9D,GAAG,KAAK,CAAC;MAAE+D,MAAM,GAAG/D,GAAG,GAAG,CAAC;IAC3C,OAAQ,CAAE0D,SAAS,CAACI,OAAO,CAAC,IAAIJ,SAAS,CAACI,OAAO,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,IAAIJ,SAAS,CAACI,OAAO,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,GACvF,CAAC,CAAC,IAAID,GAAG,IAAI,CAAC,IAAKE,MAAO,MAC5BA,MAAM;EACd,CAAC;EACD,IAAIpB,cAAc,EAAE;IAChB,KAAK,IAAIqB,IAAI,GAAG,CAAC,EAAEC,IAAI,GAAGR,WAAW,GAAG,CAAC,EAAEzD,GAAG,GAAG,CAAC,EAAEkE,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEC,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAG,CAAC,EAAEA,IAAI,EAAE,EAAE;MACzF,IAAI9E,gBAAgB,CAAC8E,IAAI,CAAC,GAAG3D,KAAK,CAACyB,MAAM,EAAE;QACvC,KAAK,IAAImC,QAAQ,GAAG,CAAC,EAAEC,SAAS,GAAG,CAAC,IAAI;UACpC,MAAMC,IAAI,GAAGN,IAAI;UACjBA,IAAI,GAAGJ,QAAQ,CAAC5D,GAAG,EAAEiE,IAAI,CAAC;UAC1BjE,GAAG,IAAIiE,IAAI,GAAG,CAAC;UACf,IAAID,IAAI,KAAKL,SAAS,EAAE;YACpBM,IAAI,GAAGR,WAAW,GAAG,CAAC;YACtBS,GAAG,CAACvC,MAAM,GAAGgC,SAAS,GAAG,CAAC;YAC1B,KAAK,IAAI/D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsE,GAAG,CAACvC,MAAM,EAAE/B,CAAC,EAAE,EAAE;cACjCsE,GAAG,CAACtE,CAAC,CAAC,GAAGA,CAAC,GAAG+D,SAAS,GAAG,CAAC/D,CAAC,CAAC,GAAG,EAAE;YACrC;UACJ,CAAC,MACI;YACD,IAAIoE,IAAI,IAAIE,GAAG,CAACvC,MAAM,EAAE;cACpBuC,GAAG,CAACrE,IAAI,CAACqE,GAAG,CAACI,IAAI,CAAC,CAACC,MAAM,CAACL,GAAG,CAACI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5C,CAAC,MACI,IAAIA,IAAI,KAAKX,SAAS,EAAE;cACzBO,GAAG,CAACrE,IAAI,CAACqE,GAAG,CAACI,IAAI,CAAC,CAACC,MAAM,CAACL,GAAG,CAACF,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5C;YACA,KAAK,IAAIpE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsE,GAAG,CAACF,IAAI,CAAC,CAACrC,MAAM,EAAE/B,CAAC,EAAE,EAAE;cACvC,MAAM;gBAAEE,CAAC;gBAAEG,CAAC;gBAAEC,CAAC;gBAAEgD;cAAE,CAAC,GAAGF,QAAQ,CAACkB,GAAG,CAACF,IAAI,CAAC,CAACpE,CAAC,CAAC,CAAC;cAC7CuD,KAAK,CAACpD,IAAI,CAACyE,GAAG,CAAC,CAAC1E,CAAC,EAAEG,CAAC,EAAEC,CAAC,EAAEgD,CAAC,CAAC,EAAE7D,gBAAgB,CAAC8E,IAAI,CAAC,GAAG3D,KAAK,CAACwB,KAAK,GAC7D1C,cAAc,CAAC6E,IAAI,CAAC,GAAGE,SAAS,GAC/BD,QAAQ,IAAI5D,KAAK,CAACwB,KAAK,GAAG,CAAC,CAAE,CAAC;cACnCoC,QAAQ,IAAI,CAAC;YACjB;YACA,IAAIF,GAAG,CAACvC,MAAM,KAAK,CAAC,IAAIsC,IAAI,IAAIA,IAAI,GAAG,GAAG,EAAE;cACxCA,IAAI,EAAE;YACV;UACJ;UACA,IAAIG,QAAQ,KAAK5D,KAAK,CAACwB,KAAK,GAAG,CAAC,IAAIqC,SAAS,GAAG,CAAC,CAAC,EAAE;YAChDA,SAAS,EAAE;YACX,IAAIhF,gBAAgB,CAAC8E,IAAI,CAAC,GAAG7E,cAAc,CAAC6E,IAAI,CAAC,GAAGE,SAAS,IAAI7D,KAAK,CAACyB,MAAM,EAAE;cAC3E;YACJ;UACJ;QACJ;MACJ;MACAQ,gBAAgB,GAAGhD,UAAU,CAACO,GAAG,IAAIP,UAAU,CAACM,IAAI,CAAC4B,MAAM,GAAG,CAAC,CAAC,EAAEtB,aAAa,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE8C,KAAK,EAAE;QAAEsB,CAAC,EAAEjE,KAAK,CAACsB,IAAI;QAAE4C,CAAC,EAAElE,KAAK,CAACuB;MAAI,CAAC,EAAE;QAAEC,KAAK,EAAE5B,GAAG,CAAC4B,KAAK;QAAEC,MAAM,EAAE7B,GAAG,CAAC6B;MAAO,CAAC,CAAC;IACjL;IACAzB,KAAK,CAAC2C,KAAK,GAAGA,KAAK;IACnB3C,KAAK,CAACmE,MAAM,GAAG,MAAMC,iBAAiB,CAACzB,KAAK,CAAC;EACjD,CAAC,MACI;IACD,KAAK,IAAIa,IAAI,GAAG,CAAC,EAAEC,IAAI,GAAGR,WAAW,GAAG,CAAC,EAAEzD,GAAG,GAAG,CAAC,EAAEkE,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEE,QAAQ,GAAG,CAAC,CAAC,IAAI;MAC9E,MAAME,IAAI,GAAGN,IAAI;MACjBA,IAAI,GAAGJ,QAAQ,CAAC5D,GAAG,EAAEiE,IAAI,CAAC;MAC1BjE,GAAG,IAAIiE,IAAI;MACX,IAAID,IAAI,KAAKL,SAAS,EAAE;QACpBM,IAAI,GAAGR,WAAW,GAAG,CAAC;QACtBS,GAAG,CAACvC,MAAM,GAAGgC,SAAS,GAAG,CAAC;QAC1B,KAAK,IAAI/D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsE,GAAG,CAACvC,MAAM,EAAE/B,CAAC,EAAE,EAAE;UACjCsE,GAAG,CAACtE,CAAC,CAAC,GAAGA,CAAC,GAAG+D,SAAS,GAAG,CAAC/D,CAAC,CAAC,GAAG,EAAE;QACrC;MACJ,CAAC,MACI;QACD,IAAIoE,IAAI,KAAKL,SAAS,GAAG,CAAC,EAAE;UACxB;QACJ;QACA,IAAIK,IAAI,IAAIE,GAAG,CAACvC,MAAM,EAAE;UACpBuC,GAAG,CAACrE,IAAI,CAACqE,GAAG,CAACI,IAAI,CAAC,CAACC,MAAM,CAACL,GAAG,CAACI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5C,CAAC,MACI,IAAIA,IAAI,KAAKX,SAAS,EAAE;UACzBO,GAAG,CAACrE,IAAI,CAACqE,GAAG,CAACI,IAAI,CAAC,CAACC,MAAM,CAACL,GAAG,CAACF,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5C;QACA,KAAK,IAAIpE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsE,GAAG,CAACF,IAAI,CAAC,CAACrC,MAAM,EAAE/B,CAAC,EAAE,EAAE;UACvC,MAAM;YAAEE,CAAC;YAAEG,CAAC;YAAEC,CAAC;YAAEgD;UAAE,CAAC,GAAGF,QAAQ,CAACkB,GAAG,CAACF,IAAI,CAAC,CAACpE,CAAC,CAAC,CAAC;UAC7CuD,KAAK,CAACpD,IAAI,CAACyE,GAAG,CAAC,CAAC1E,CAAC,EAAEG,CAAC,EAAEC,CAAC,EAAEgD,CAAC,CAAC,EAAGkB,QAAQ,IAAI,CAAE,CAAC;QACjD;QACA,IAAIF,GAAG,CAACvC,MAAM,IAAI,CAAC,IAAIsC,IAAI,IAAIA,IAAI,GAAG,GAAG,EAAE;UACvCA,IAAI,EAAE;QACV;MACJ;IACJ;IACAzD,KAAK,CAAC2C,KAAK,GAAGA,KAAK;IACnB3C,KAAK,CAACmE,MAAM,GAAG,MAAMC,iBAAiB,CAACzB,KAAK,CAAC;IAC7CV,gBAAgB,GAAG,CAAChD,UAAU,CAACO,GAAG,GAAG,CAAC,IAAIP,UAAU,CAACM,IAAI,CAAC4B,MAAM,EAAEtB,aAAa,CAAC,KAAK,CAAC,GAAG,CAAC,EAAEG,KAAK,CAAC2C,KAAK,EAAE;MAAEsB,CAAC,EAAEjE,KAAK,CAACsB,IAAI;MAAE4C,CAAC,EAAElE,KAAK,CAACuB;IAAI,CAAC,EAAE;MAAEC,KAAK,EAAE5B,GAAG,CAAC4B,KAAK;MAAEC,MAAM,EAAE7B,GAAG,CAAC6B;IAAO,CAAC,CAAC;EACvL;AACJ;AACA,eAAe4C,UAAUA,CAACpF,UAAU,EAAEW,GAAG,EAAEoC,QAAQ,EAAEnC,aAAa,EAAEC,oBAAoB,EAAEmC,gBAAgB,EAAE;EACxG,QAAQhD,UAAU,CAACc,QAAQ,CAAC,CAAC;IACzB,KAAK,EAAE;MACH,OAAO,IAAI;IACf,KAAK,EAAE;MACH,MAAMgC,eAAe,CAAC9C,UAAU,EAAEW,GAAG,EAAEoC,QAAQ,EAAEnC,aAAa,EAAEC,oBAAoB,EAAEmC,gBAAgB,CAAC;MACvG;IACJ,KAAK,EAAE;MACH,MAAMtC,mBAAmB,CAACV,UAAU,EAAEW,GAAG,EAAEC,aAAa,EAAEC,oBAAoB,CAAC;MAC/E;IACJ;MACI,MAAM,IAAIsB,SAAS,CAAC,uBAAuB,CAAC;EACpD;EACA,OAAO,KAAK;AAChB;AACA,OAAO,SAASkD,gBAAgBA,CAAC1E,GAAG,EAAE;EAClC,KAAK,MAAM2E,SAAS,IAAI3E,GAAG,CAACmB,qBAAqB,EAAE;IAC/C,IAAIwD,SAAS,CAAC5D,UAAU,GAAG4D,SAAS,CAAC1D,kBAAkB,KAAK,aAAa,EAAE;MACvE;IACJ;IACA,OAAO0D,SAAS,CAAChF,IAAI,CAAC,CAAC,CAAC,IAAIgF,SAAS,CAAChF,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;EACvD;EACA,OAAOiF,GAAG;AACd;AACA,OAAO,eAAeC,SAASA,CAACC,MAAM,EAAEzC,gBAAgB,EAAED,QAAQ,EAAE;EAChE,IAAI,CAACA,QAAQ,EACTA,QAAQ,GAAG,KAAK;EACpB,MAAM2C,GAAG,GAAG,MAAMC,KAAK,CAACF,MAAM,CAAC;EAC/B,IAAI,CAACC,GAAG,CAACE,EAAE,IAAIF,GAAG,CAACG,MAAM,KAAK,GAAG,EAAE;IAC/B,MAAM,IAAI1D,SAAS,CAAC,gBAAgB,CAAC;EACzC;EACA,MAAM2D,MAAM,GAAG,MAAMJ,GAAG,CAACK,WAAW,CAAC,CAAC;EACtC,MAAMpF,GAAG,GAAG;MACR4B,KAAK,EAAE,CAAC;MACRC,MAAM,EAAE,CAAC;MACTwD,SAAS,EAAE,CAAC;MACZC,QAAQ,EAAE,CAAC;MACXC,gBAAgB,EAAE,CAAC;MACnBlF,MAAM,EAAE,EAAE;MACVmC,QAAQ,EAAE,KAAK;MACflB,gBAAgB,EAAE,EAAE;MACpBkE,eAAe,EAAE,IAAIxC,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE;QAAEC,UAAU,EAAE;MAAO,CAAC,CAAC;MAC5D7B,QAAQ,EAAE,EAAE;MACZD,qBAAqB,EAAE;IAC3B,CAAC;IAAE9B,UAAU,GAAG,IAAIF,UAAU,CAAC,IAAIsG,iBAAiB,CAACN,MAAM,CAAC,CAAC;EAC7D,IAAI9F,UAAU,CAAC2B,SAAS,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;IACtC,MAAM,IAAI0E,KAAK,CAAC,0BAA0B,CAAC;EAC/C;EACA1F,GAAG,CAAC4B,KAAK,GAAGvC,UAAU,CAACuB,YAAY,CAAC,CAAC;EACrCZ,GAAG,CAAC6B,MAAM,GAAGxC,UAAU,CAACuB,YAAY,CAAC,CAAC;EACtC,MAAMN,UAAU,GAAGjB,UAAU,CAACc,QAAQ,CAAC,CAAC;IAAEwF,oBAAoB,GAAG,CAACrF,UAAU,GAAG,IAAI,MAAM,IAAI;EAC7FN,GAAG,CAACsF,QAAQ,GAAG,CAAChF,UAAU,GAAG,IAAI,MAAM,CAAC;EACxCN,GAAG,CAACwC,QAAQ,GAAG,CAAClC,UAAU,GAAG,CAAC,MAAM,CAAC;EACrC,MAAMsF,gBAAgB,GAAG,CAAC,IAAK,CAACtF,UAAU,GAAG,CAAC,IAAI,CAAE;IAAEuF,oBAAoB,GAAGxG,UAAU,CAACc,QAAQ,CAAC,CAAC;EAClGH,GAAG,CAACuF,gBAAgB,GAAGlG,UAAU,CAACc,QAAQ,CAAC,CAAC;EAC5C,IAAIH,GAAG,CAACuF,gBAAgB,KAAK,CAAC,EAAE;IAC5BvF,GAAG,CAACuF,gBAAgB,GAAG,CAACvF,GAAG,CAACuF,gBAAgB,GAAG,GAAG,IAAI,IAAI;EAC9D;EACA,IAAII,oBAAoB,EAAE;IACtB3F,GAAG,CAACsB,gBAAgB,GAAGlC,eAAe,CAACC,UAAU,EAAEuG,gBAAgB,CAAC;EACxE;EACA,MAAMJ,eAAe,GAAG,CAAC,MAAM;IAC3B,IAAI;MACA,OAAO,IAAIxC,SAAS,CAAChD,GAAG,CAAC4B,KAAK,EAAE5B,GAAG,CAAC6B,MAAM,EAAE;QAAEoB,UAAU,EAAE;MAAO,CAAC,CAAC;IACvE,CAAC,CACD,OAAOC,KAAK,EAAE;MACV,IAAIA,KAAK,YAAYC,YAAY,IAAID,KAAK,CAACE,IAAI,KAAK,gBAAgB,EAAE;QAClE,OAAO,IAAI;MACf;MACA,MAAMF,KAAK;IACf;EACJ,CAAC,EAAE,CAAC;EACJ,IAAIsC,eAAe,IAAI,IAAI,EAAE;IACzB,MAAM,IAAIE,KAAK,CAAC,4BAA4B,CAAC;EACjD;EACA,MAAM;IAAEhG,CAAC;IAAEG,CAAC;IAAEC;EAAE,CAAC,GAAGE,GAAG,CAACsB,gBAAgB,CAACuE,oBAAoB,CAAC;EAC9DL,eAAe,CAAC7F,IAAI,CAACyE,GAAG,CAACuB,oBAAoB,GAAG,CAACjG,CAAC,EAAEG,CAAC,EAAEC,CAAC,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;EAC9E,KAAK,IAAIN,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgG,eAAe,CAAC7F,IAAI,CAAC4B,MAAM,EAAE/B,CAAC,IAAI,CAAC,EAAE;IACrDgG,eAAe,CAAC7F,IAAI,CAACmG,UAAU,CAACtG,CAAC,EAAE,CAAC,EAAEA,CAAC,CAAC;EAC5C;EACAQ,GAAG,CAACwF,eAAe,GAAGA,eAAe;EACrC,IAAIO,UAAU,GAAG,CAAC,CAAC;IAAEC,mBAAmB,GAAG,IAAI;IAAEnF,iBAAiB,GAAG,CAAC,CAAC;EACvE,MAAMoF,aAAa,GAAIC,SAAS,IAAK;IACjC,IAAIA,SAAS,EAAE;MACXF,mBAAmB,GAAG,IAAI;IAC9B;IACA,OAAOD,UAAU;EACrB,CAAC;EACD,MAAM7F,oBAAoB,GAAIiG,QAAQ,IAAK;IACvC,IAAIA,QAAQ,IAAI,IAAI,EAAE;MAClBtF,iBAAiB,GAAGsF,QAAQ;IAChC;IACA,OAAOtF,iBAAiB;EAC5B,CAAC;EACD,IAAI;IACA,GAAG;MACC,IAAImF,mBAAmB,EAAE;QACrBhG,GAAG,CAACK,MAAM,CAACZ,IAAI,CAAC;UACZiC,IAAI,EAAE,CAAC;UACPC,GAAG,EAAE,CAAC;UACNC,KAAK,EAAE,CAAC;UACRC,MAAM,EAAE,CAAC;UACTrB,cAAc,EAAE,CAAC;UACjBuC,KAAK,EAAE,IAAIC,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE;YAAEC,UAAU,EAAE;UAAO,CAAC,CAAC;UAClDxB,aAAa,EAAE,IAAI;UACnBhB,kBAAkB,EAAE,KAAK;UACzBE,SAAS,EAAE,CAAC;UACZ6B,QAAQ,EAAE,KAAK;UACfG,eAAe,EAAE,EAAE;UACnBF,QAAQ,EAAE,CAAC;UACXlC,UAAU,EAAE;QAChB,CAAC,CAAC;QACFwF,UAAU,EAAE;QACZlF,iBAAiB,GAAG,CAAC,CAAC;QACtBmF,mBAAmB,GAAG,KAAK;MAC/B;IACJ,CAAC,QAAQ,EAAE,MAAMvB,UAAU,CAACpF,UAAU,EAAEW,GAAG,EAAEoC,QAAQ,EAAE6D,aAAa,EAAE/F,oBAAoB,EAAEmC,gBAAgB,CAAC,CAAC;IAC9GrC,GAAG,CAACK,MAAM,CAACkB,MAAM,EAAE;IACnB,KAAK,MAAMnB,KAAK,IAAIJ,GAAG,CAACK,MAAM,EAAE;MAC5B,IAAID,KAAK,CAACK,kBAAkB,IAAIL,KAAK,CAACO,SAAS,KAAK,CAAC,EAAE;QACnDX,GAAG,CAACqF,SAAS,GAAGe,QAAQ;QACxB;MACJ;MACApG,GAAG,CAACqF,SAAS,IAAIjF,KAAK,CAACO,SAAS;IACpC;IACA,OAAOX,GAAG;EACd,CAAC,CACD,OAAOkD,KAAK,EAAE;IACV,IAAIA,KAAK,YAAY1B,SAAS,EAAE;MAC5B,MAAM,IAAIkE,KAAK,CAAC,6BAA6BK,UAAU,KAAK7C,KAAK,CAACmD,OAAO,GAAG,CAAC;IACjF;IACA,MAAMnD,KAAK;EACf;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
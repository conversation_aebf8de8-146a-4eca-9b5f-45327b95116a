{"ast": null, "code": "var _jsxFileName = \"D:\\\\Audio Songs\\\\React-portfolio\\\\personal-portfolio\\\\src\\\\components\\\\Home\\\\index.js\",\n  _s = $RefreshSig$();\nimport { useEffect, useState } from 'react';\nimport Loader from 'react-loaders';\nimport { Link } from 'react-router-dom';\nimport Logo from './Logo';\nimport AnimatedLetters from '../AnimatedLetters';\nimport ParticleBackground from '../ParticleBackground';\nimport './index.scss';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Home = () => {\n  _s();\n  const [letterClass, setLetterClass] = useState('text-animate');\n  const nameArray = 'RRizwan'.split('');\n  const jobArray = 'Frontend Developer'.split('');\n  const interestArray = 'React | Angular'.split('');\n  useEffect(() => {\n    const timer = setTimeout(() => {\n      setLetterClass('text-animate-hover');\n    }, 4000);\n    return () => clearTimeout(timer);\n  }, []);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(ParticleBackground, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container home-page\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-zone\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: letterClass,\n            children: \"H\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 31,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: `${letterClass} _12`,\n            children: \"i,\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 32,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 33,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: `${letterClass} _13`,\n            children: \"I\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 34,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: `${letterClass} _14`,\n            children: \"'m\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 35,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(AnimatedLetters, {\n            letterClass: letterClass,\n            strArray: nameArray,\n            idx: 15\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 36,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 41,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(AnimatedLetters, {\n            letterClass: letterClass,\n            strArray: jobArray,\n            idx: 22\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 42,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 47,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(AnimatedLetters, {\n            letterClass: letterClass,\n            strArray: interestArray,\n            idx: 22\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 48,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Building Modern Web Applications | Creating Interactive User Interfaces | Developing Responsive Frontend Solutions\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/contact\",\n          className: \"flat-button\",\n          children: \"CONTACT ME\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Logo, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Loader, {\n      type: \"pacman\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 65,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(Home, \"YlPNN7oJ+6mzs0fohZppsFcng1g=\");\n_c = Home;\nexport default Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");", "map": {"version": 3, "names": ["useEffect", "useState", "Loader", "Link", "Logo", "AnimatedLetters", "ParticleBackground", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Home", "_s", "letterClass", "setLetterClass", "nameArray", "split", "<PERSON><PERSON><PERSON><PERSON>", "interestArray", "timer", "setTimeout", "clearTimeout", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "strArray", "idx", "to", "type", "_c", "$RefreshReg$"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/src/components/Home/index.js"], "sourcesContent": ["import { useEffect, useState } from 'react'\r\n\r\nimport Loader from 'react-loaders'\r\nimport { Link } from 'react-router-dom'\r\n\r\nimport Logo from './Logo'\r\nimport AnimatedLetters from '../AnimatedLetters'\r\nimport ParticleBackground from '../ParticleBackground'\r\nimport './index.scss'\r\n\r\nconst Home = () => {\r\n  const [letterClass, setLetterClass] = useState('text-animate')\r\n\r\n  const nameArray = 'RRizwan'.split('')\r\n  const jobArray = 'Frontend Developer'.split('')\r\n  const interestArray = 'React | Angular'.split('')\r\n\r\n  useEffect(() => {\r\n    const timer = setTimeout(() => {\r\n      setLetterClass('text-animate-hover')\r\n    }, 4000)\r\n    return () => clearTimeout(timer)\r\n  }, [])\r\n\r\n  return (\r\n    <>\r\n      <ParticleBackground />\r\n      <div className=\"container home-page\">\r\n        <div className=\"text-zone\">\r\n          <h1>\r\n            <span className={letterClass}>H</span>\r\n            <span className={`${letterClass} _12`}>i,</span>\r\n            <br />\r\n            <span className={`${letterClass} _13`}>I</span>\r\n            <span className={`${letterClass} _14`}>'m</span>\r\n            <AnimatedLetters\r\n              letterClass={letterClass}\r\n              strArray={nameArray}\r\n              idx={15}\r\n            />\r\n            <br />\r\n            <AnimatedLetters\r\n              letterClass={letterClass}\r\n              strArray={jobArray}\r\n              idx={22}\r\n            />\r\n            <br />\r\n            <AnimatedLetters\r\n              letterClass={letterClass}\r\n              strArray={interestArray}\r\n              idx={22}\r\n            />\r\n          </h1>\r\n          <h2>\r\n            Building Modern Web Applications | Creating Interactive User Interfaces |\r\n            Developing Responsive Frontend Solutions\r\n          </h2>\r\n          <Link to=\"/contact\" className=\"flat-button\">\r\n            CONTACT ME\r\n          </Link>\r\n        </div>\r\n        <Logo />\r\n      </div>\r\n\r\n      <Loader type=\"pacman\" />\r\n    </>\r\n  )\r\n}\r\n\r\nexport default Home\r\n"], "mappings": ";;AAAA,SAASA,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAE3C,OAAOC,MAAM,MAAM,eAAe;AAClC,SAASC,IAAI,QAAQ,kBAAkB;AAEvC,OAAOC,IAAI,MAAM,QAAQ;AACzB,OAAOC,eAAe,MAAM,oBAAoB;AAChD,OAAOC,kBAAkB,MAAM,uBAAuB;AACtD,OAAO,cAAc;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAErB,MAAMC,IAAI,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjB,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGb,QAAQ,CAAC,cAAc,CAAC;EAE9D,MAAMc,SAAS,GAAG,SAAS,CAACC,KAAK,CAAC,EAAE,CAAC;EACrC,MAAMC,QAAQ,GAAG,oBAAoB,CAACD,KAAK,CAAC,EAAE,CAAC;EAC/C,MAAME,aAAa,GAAG,iBAAiB,CAACF,KAAK,CAAC,EAAE,CAAC;EAEjDhB,SAAS,CAAC,MAAM;IACd,MAAMmB,KAAK,GAAGC,UAAU,CAAC,MAAM;MAC7BN,cAAc,CAAC,oBAAoB,CAAC;IACtC,CAAC,EAAE,IAAI,CAAC;IACR,OAAO,MAAMO,YAAY,CAACF,KAAK,CAAC;EAClC,CAAC,EAAE,EAAE,CAAC;EAEN,oBACEX,OAAA,CAAAE,SAAA;IAAAY,QAAA,gBACEd,OAAA,CAACF,kBAAkB;MAAAiB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACtBlB,OAAA;MAAKmB,SAAS,EAAC,qBAAqB;MAAAL,QAAA,gBAClCd,OAAA;QAAKmB,SAAS,EAAC,WAAW;QAAAL,QAAA,gBACxBd,OAAA;UAAAc,QAAA,gBACEd,OAAA;YAAMmB,SAAS,EAAEd,WAAY;YAAAS,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACtClB,OAAA;YAAMmB,SAAS,EAAE,GAAGd,WAAW,MAAO;YAAAS,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAChDlB,OAAA;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNlB,OAAA;YAAMmB,SAAS,EAAE,GAAGd,WAAW,MAAO;YAAAS,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC/ClB,OAAA;YAAMmB,SAAS,EAAE,GAAGd,WAAW,MAAO;YAAAS,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAChDlB,OAAA,CAACH,eAAe;YACdQ,WAAW,EAAEA,WAAY;YACzBe,QAAQ,EAAEb,SAAU;YACpBc,GAAG,EAAE;UAAG;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACFlB,OAAA;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNlB,OAAA,CAACH,eAAe;YACdQ,WAAW,EAAEA,WAAY;YACzBe,QAAQ,EAAEX,QAAS;YACnBY,GAAG,EAAE;UAAG;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACFlB,OAAA;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNlB,OAAA,CAACH,eAAe;YACdQ,WAAW,EAAEA,WAAY;YACzBe,QAAQ,EAAEV,aAAc;YACxBW,GAAG,EAAE;UAAG;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACLlB,OAAA;UAAAc,QAAA,EAAI;QAGJ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLlB,OAAA,CAACL,IAAI;UAAC2B,EAAE,EAAC,UAAU;UAACH,SAAS,EAAC,aAAa;UAAAL,QAAA,EAAC;QAE5C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNlB,OAAA,CAACJ,IAAI;QAAAmB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAENlB,OAAA,CAACN,MAAM;MAAC6B,IAAI,EAAC;IAAQ;MAAAR,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA,eACxB,CAAC;AAEP,CAAC;AAAAd,EAAA,CAzDKD,IAAI;AAAAqB,EAAA,GAAJrB,IAAI;AA2DV,eAAeA,IAAI;AAAA,IAAAqB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
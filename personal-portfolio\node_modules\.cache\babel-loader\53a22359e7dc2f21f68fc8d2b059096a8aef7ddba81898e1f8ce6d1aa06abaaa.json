{"ast": null, "code": "export class EventDispatcher {\n  constructor() {\n    this._listeners = new Map();\n  }\n  addEventListener(type, listener) {\n    this.removeEventListener(type, listener);\n    let arr = this._listeners.get(type);\n    if (!arr) {\n      arr = [];\n      this._listeners.set(type, arr);\n    }\n    arr.push(listener);\n  }\n  dispatchEvent(type, args) {\n    const listeners = this._listeners.get(type);\n    listeners?.forEach(handler => handler(args));\n  }\n  hasEventListener(type) {\n    return !!this._listeners.get(type);\n  }\n  removeAllEventListeners(type) {\n    if (!type) {\n      this._listeners = new Map();\n    } else {\n      this._listeners.delete(type);\n    }\n  }\n  removeEventListener(type, listener) {\n    const arr = this._listeners.get(type);\n    if (!arr) {\n      return;\n    }\n    const length = arr.length,\n      idx = arr.indexOf(listener),\n      minIndex = 0;\n    if (idx < minIndex) {\n      return;\n    }\n    const deleteCount = 1;\n    if (length === deleteCount) {\n      this._listeners.delete(type);\n    } else {\n      arr.splice(idx, deleteCount);\n    }\n  }\n}", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "constructor", "_listeners", "Map", "addEventListener", "type", "listener", "removeEventListener", "arr", "get", "set", "push", "dispatchEvent", "args", "listeners", "for<PERSON>ach", "handler", "hasEventListener", "removeAllEventListeners", "delete", "length", "idx", "indexOf", "minIndex", "deleteCount", "splice"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/engine/browser/Utils/EventDispatcher.js"], "sourcesContent": ["export class EventDispatcher {\n    constructor() {\n        this._listeners = new Map();\n    }\n    addEventListener(type, listener) {\n        this.removeEventListener(type, listener);\n        let arr = this._listeners.get(type);\n        if (!arr) {\n            arr = [];\n            this._listeners.set(type, arr);\n        }\n        arr.push(listener);\n    }\n    dispatchEvent(type, args) {\n        const listeners = this._listeners.get(type);\n        listeners?.forEach(handler => handler(args));\n    }\n    hasEventListener(type) {\n        return !!this._listeners.get(type);\n    }\n    removeAllEventListeners(type) {\n        if (!type) {\n            this._listeners = new Map();\n        }\n        else {\n            this._listeners.delete(type);\n        }\n    }\n    removeEventListener(type, listener) {\n        const arr = this._listeners.get(type);\n        if (!arr) {\n            return;\n        }\n        const length = arr.length, idx = arr.indexOf(listener), minIndex = 0;\n        if (idx < minIndex) {\n            return;\n        }\n        const deleteCount = 1;\n        if (length === deleteCount) {\n            this._listeners.delete(type);\n        }\n        else {\n            arr.splice(idx, deleteCount);\n        }\n    }\n}\n"], "mappings": "AAAA,OAAO,MAAMA,eAAe,CAAC;EACzBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,UAAU,GAAG,IAAIC,GAAG,CAAC,CAAC;EAC/B;EACAC,gBAAgBA,CAACC,IAAI,EAAEC,QAAQ,EAAE;IAC7B,IAAI,CAACC,mBAAmB,CAACF,IAAI,EAAEC,QAAQ,CAAC;IACxC,IAAIE,GAAG,GAAG,IAAI,CAACN,UAAU,CAACO,GAAG,CAACJ,IAAI,CAAC;IACnC,IAAI,CAACG,GAAG,EAAE;MACNA,GAAG,GAAG,EAAE;MACR,IAAI,CAACN,UAAU,CAACQ,GAAG,CAACL,IAAI,EAAEG,GAAG,CAAC;IAClC;IACAA,GAAG,CAACG,IAAI,CAACL,QAAQ,CAAC;EACtB;EACAM,aAAaA,CAACP,IAAI,EAAEQ,IAAI,EAAE;IACtB,MAAMC,SAAS,GAAG,IAAI,CAACZ,UAAU,CAACO,GAAG,CAACJ,IAAI,CAAC;IAC3CS,SAAS,EAAEC,OAAO,CAACC,OAAO,IAAIA,OAAO,CAACH,IAAI,CAAC,CAAC;EAChD;EACAI,gBAAgBA,CAACZ,IAAI,EAAE;IACnB,OAAO,CAAC,CAAC,IAAI,CAACH,UAAU,CAACO,GAAG,CAACJ,IAAI,CAAC;EACtC;EACAa,uBAAuBA,CAACb,IAAI,EAAE;IAC1B,IAAI,CAACA,IAAI,EAAE;MACP,IAAI,CAACH,UAAU,GAAG,IAAIC,GAAG,CAAC,CAAC;IAC/B,CAAC,MACI;MACD,IAAI,CAACD,UAAU,CAACiB,MAAM,CAACd,IAAI,CAAC;IAChC;EACJ;EACAE,mBAAmBA,CAACF,IAAI,EAAEC,QAAQ,EAAE;IAChC,MAAME,GAAG,GAAG,IAAI,CAACN,UAAU,CAACO,GAAG,CAACJ,IAAI,CAAC;IACrC,IAAI,CAACG,GAAG,EAAE;MACN;IACJ;IACA,MAAMY,MAAM,GAAGZ,GAAG,CAACY,MAAM;MAAEC,GAAG,GAAGb,GAAG,CAACc,OAAO,CAAChB,QAAQ,CAAC;MAAEiB,QAAQ,GAAG,CAAC;IACpE,IAAIF,GAAG,GAAGE,QAAQ,EAAE;MAChB;IACJ;IACA,MAAMC,WAAW,GAAG,CAAC;IACrB,IAAIJ,MAAM,KAAKI,WAAW,EAAE;MACxB,IAAI,CAACtB,UAAU,CAACiB,MAAM,CAACd,IAAI,CAAC;IAChC,CAAC,MACI;MACDG,GAAG,CAACiB,MAAM,CAACJ,GAAG,EAAEG,WAAW,CAAC;IAChC;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { ConnectLinks } from \"./ConnectLinks\";\nexport class Connect {\n  constructor() {\n    this.distance = 80;\n    this.links = new ConnectLinks();\n    this.radius = 60;\n  }\n\n  get line_linked() {\n    return this.links;\n  }\n\n  set line_linked(value) {\n    this.links = value;\n  }\n\n  get lineLinked() {\n    return this.links;\n  }\n\n  set lineLinked(value) {\n    this.links = value;\n  }\n\n  load(data) {\n    var _a, _b;\n\n    if (data === undefined) {\n      return;\n    }\n\n    if (data.distance !== undefined) {\n      this.distance = data.distance;\n    }\n\n    this.links.load((_b = (_a = data.links) !== null && _a !== void 0 ? _a : data.lineLinked) !== null && _b !== void 0 ? _b : data.line_linked);\n\n    if (data.radius !== undefined) {\n      this.radius = data.radius;\n    }\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Options/Classes/Interactivity/Modes/Connect.js"], "names": ["ConnectLinks", "Connect", "constructor", "distance", "links", "radius", "line_linked", "value", "lineLinked", "load", "data", "_a", "_b", "undefined"], "mappings": "AAAA,SAASA,YAAT,QAA6B,gBAA7B;AACA,OAAO,MAAMC,OAAN,CAAc;AACjBC,EAAAA,WAAW,GAAG;AACV,SAAKC,QAAL,GAAgB,EAAhB;AACA,SAAKC,KAAL,GAAa,IAAIJ,YAAJ,EAAb;AACA,SAAKK,MAAL,GAAc,EAAd;AACH;;AACc,MAAXC,WAAW,GAAG;AACd,WAAO,KAAKF,KAAZ;AACH;;AACc,MAAXE,WAAW,CAACC,KAAD,EAAQ;AACnB,SAAKH,KAAL,GAAaG,KAAb;AACH;;AACa,MAAVC,UAAU,GAAG;AACb,WAAO,KAAKJ,KAAZ;AACH;;AACa,MAAVI,UAAU,CAACD,KAAD,EAAQ;AAClB,SAAKH,KAAL,GAAaG,KAAb;AACH;;AACDE,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,QAAIC,EAAJ,EAAQC,EAAR;;AACA,QAAIF,IAAI,KAAKG,SAAb,EAAwB;AACpB;AACH;;AACD,QAAIH,IAAI,CAACP,QAAL,KAAkBU,SAAtB,EAAiC;AAC7B,WAAKV,QAAL,GAAgBO,IAAI,CAACP,QAArB;AACH;;AACD,SAAKC,KAAL,CAAWK,IAAX,CAAgB,CAACG,EAAE,GAAG,CAACD,EAAE,GAAGD,IAAI,CAACN,KAAX,MAAsB,IAAtB,IAA8BO,EAAE,KAAK,KAAK,CAA1C,GAA8CA,EAA9C,GAAmDD,IAAI,CAACF,UAA9D,MAA8E,IAA9E,IAAsFI,EAAE,KAAK,KAAK,CAAlG,GAAsGA,EAAtG,GAA2GF,IAAI,CAACJ,WAAhI;;AACA,QAAII,IAAI,CAACL,MAAL,KAAgBQ,SAApB,EAA+B;AAC3B,WAAKR,MAAL,GAAcK,IAAI,CAACL,MAAnB;AACH;AACJ;;AA9BgB", "sourcesContent": ["import { ConnectLinks } from \"./ConnectLinks\";\nexport class Connect {\n    constructor() {\n        this.distance = 80;\n        this.links = new ConnectLinks();\n        this.radius = 60;\n    }\n    get line_linked() {\n        return this.links;\n    }\n    set line_linked(value) {\n        this.links = value;\n    }\n    get lineLinked() {\n        return this.links;\n    }\n    set lineLinked(value) {\n        this.links = value;\n    }\n    load(data) {\n        var _a, _b;\n        if (data === undefined) {\n            return;\n        }\n        if (data.distance !== undefined) {\n            this.distance = data.distance;\n        }\n        this.links.load((_b = (_a = data.links) !== null && _a !== void 0 ? _a : data.lineLinked) !== null && _b !== void 0 ? _b : data.line_linked);\n        if (data.radius !== undefined) {\n            this.radius = data.radius;\n        }\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
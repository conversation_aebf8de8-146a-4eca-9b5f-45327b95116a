{"ast": null, "code": "export class StarDrawer {\n  getSidesCount(particle) {\n    var _a, _b;\n\n    const star = particle.shapeData;\n    return (_b = (_a = star === null || star === void 0 ? void 0 : star.sides) !== null && _a !== void 0 ? _a : star === null || star === void 0 ? void 0 : star.nb_sides) !== null && _b !== void 0 ? _b : 5;\n  }\n\n  draw(context, particle, radius) {\n    var _a;\n\n    const star = particle.shapeData;\n    const sides = this.getSidesCount(particle);\n    const inset = (_a = star === null || star === void 0 ? void 0 : star.inset) !== null && _a !== void 0 ? _a : 2;\n    context.moveTo(0, 0 - radius);\n\n    for (let i = 0; i < sides; i++) {\n      context.rotate(Math.PI / sides);\n      context.lineTo(0, 0 - radius * inset);\n      context.rotate(Math.PI / sides);\n      context.lineTo(0, 0 - radius);\n    }\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Shapes/Star/StarDrawer.js"], "names": ["StarDrawer", "getSidesCount", "particle", "_a", "_b", "star", "shapeData", "sides", "nb_sides", "draw", "context", "radius", "inset", "moveTo", "i", "rotate", "Math", "PI", "lineTo"], "mappings": "AAAA,OAAO,MAAMA,UAAN,CAAiB;AACpBC,EAAAA,aAAa,CAACC,QAAD,EAAW;AACpB,QAAIC,EAAJ,EAAQC,EAAR;;AACA,UAAMC,IAAI,GAAGH,QAAQ,CAACI,SAAtB;AACA,WAAO,CAACF,EAAE,GAAG,CAACD,EAAE,GAAGE,IAAI,KAAK,IAAT,IAAiBA,IAAI,KAAK,KAAK,CAA/B,GAAmC,KAAK,CAAxC,GAA4CA,IAAI,CAACE,KAAvD,MAAkE,IAAlE,IAA0EJ,EAAE,KAAK,KAAK,CAAtF,GAA0FA,EAA1F,GAA+FE,IAAI,KAAK,IAAT,IAAiBA,IAAI,KAAK,KAAK,CAA/B,GAAmC,KAAK,CAAxC,GAA4CA,IAAI,CAACG,QAAtJ,MAAoK,IAApK,IAA4KJ,EAAE,KAAK,KAAK,CAAxL,GAA4LA,EAA5L,GAAiM,CAAxM;AACH;;AACDK,EAAAA,IAAI,CAACC,OAAD,EAAUR,QAAV,EAAoBS,MAApB,EAA4B;AAC5B,QAAIR,EAAJ;;AACA,UAAME,IAAI,GAAGH,QAAQ,CAACI,SAAtB;AACA,UAAMC,KAAK,GAAG,KAAKN,aAAL,CAAmBC,QAAnB,CAAd;AACA,UAAMU,KAAK,GAAG,CAACT,EAAE,GAAGE,IAAI,KAAK,IAAT,IAAiBA,IAAI,KAAK,KAAK,CAA/B,GAAmC,KAAK,CAAxC,GAA4CA,IAAI,CAACO,KAAvD,MAAkE,IAAlE,IAA0ET,EAAE,KAAK,KAAK,CAAtF,GAA0FA,EAA1F,GAA+F,CAA7G;AACAO,IAAAA,OAAO,CAACG,MAAR,CAAe,CAAf,EAAkB,IAAIF,MAAtB;;AACA,SAAK,IAAIG,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGP,KAApB,EAA2BO,CAAC,EAA5B,EAAgC;AAC5BJ,MAAAA,OAAO,CAACK,MAAR,CAAeC,IAAI,CAACC,EAAL,GAAUV,KAAzB;AACAG,MAAAA,OAAO,CAACQ,MAAR,CAAe,CAAf,EAAkB,IAAIP,MAAM,GAAGC,KAA/B;AACAF,MAAAA,OAAO,CAACK,MAAR,CAAeC,IAAI,CAACC,EAAL,GAAUV,KAAzB;AACAG,MAAAA,OAAO,CAACQ,MAAR,CAAe,CAAf,EAAkB,IAAIP,MAAtB;AACH;AACJ;;AAlBmB", "sourcesContent": ["export class StarDrawer {\n    getSidesCount(particle) {\n        var _a, _b;\n        const star = particle.shapeData;\n        return (_b = (_a = star === null || star === void 0 ? void 0 : star.sides) !== null && _a !== void 0 ? _a : star === null || star === void 0 ? void 0 : star.nb_sides) !== null && _b !== void 0 ? _b : 5;\n    }\n    draw(context, particle, radius) {\n        var _a;\n        const star = particle.shapeData;\n        const sides = this.getSidesCount(particle);\n        const inset = (_a = star === null || star === void 0 ? void 0 : star.inset) !== null && _a !== void 0 ? _a : 2;\n        context.moveTo(0, 0 - radius);\n        for (let i = 0; i < sides; i++) {\n            context.rotate(Math.PI / sides);\n            context.lineTo(0, 0 - radius * inset);\n            context.rotate(Math.PI / sides);\n            context.lineTo(0, 0 - radius);\n        }\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
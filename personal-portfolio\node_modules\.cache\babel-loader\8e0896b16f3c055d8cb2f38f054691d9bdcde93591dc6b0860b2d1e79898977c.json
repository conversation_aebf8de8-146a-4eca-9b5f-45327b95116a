{"ast": null, "code": "import { degToRad, isObject } from \"@tsparticles/engine\";\nimport { drawCircle } from \"./Utils.js\";\nconst sides = 12,\n  maxAngle = 360,\n  minAngle = 0;\nexport class CircleDrawer {\n  constructor() {\n    this.validTypes = [\"circle\"];\n  }\n  draw(data) {\n    drawCircle(data);\n  }\n  getSidesCount() {\n    return sides;\n  }\n  particleInit(container, particle) {\n    const shapeData = particle.shapeData,\n      angle = shapeData?.angle ?? {\n        max: maxAngle,\n        min: minAngle\n      };\n    particle.circleRange = !isObject(angle) ? {\n      min: minAngle,\n      max: degToRad(angle)\n    } : {\n      min: degToRad(angle.min),\n      max: degToRad(angle.max)\n    };\n  }\n}", "map": {"version": 3, "names": ["degToRad", "isObject", "drawCircle", "sides", "maxAngle", "minAngle", "CircleDrawer", "constructor", "validTypes", "draw", "data", "getSidesCount", "particleInit", "container", "particle", "shapeData", "angle", "max", "min", "circleRange"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/shape-circle/browser/CircleDrawer.js"], "sourcesContent": ["import { degToRad, isObject } from \"@tsparticles/engine\";\nimport { drawCircle } from \"./Utils.js\";\nconst sides = 12, maxAngle = 360, minAngle = 0;\nexport class CircleDrawer {\n    constructor() {\n        this.validTypes = [\"circle\"];\n    }\n    draw(data) {\n        drawCircle(data);\n    }\n    getSidesCount() {\n        return sides;\n    }\n    particleInit(container, particle) {\n        const shapeData = particle.shapeData, angle = shapeData?.angle ?? {\n            max: maxAngle,\n            min: minAngle,\n        };\n        particle.circleRange = !isObject(angle)\n            ? {\n                min: minAngle,\n                max: degToRad(angle),\n            }\n            : { min: degToRad(angle.min), max: degToRad(angle.max) };\n    }\n}\n"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,QAAQ,QAAQ,qBAAqB;AACxD,SAASC,UAAU,QAAQ,YAAY;AACvC,MAAMC,KAAK,GAAG,EAAE;EAAEC,QAAQ,GAAG,GAAG;EAAEC,QAAQ,GAAG,CAAC;AAC9C,OAAO,MAAMC,YAAY,CAAC;EACtBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,UAAU,GAAG,CAAC,QAAQ,CAAC;EAChC;EACAC,IAAIA,CAACC,IAAI,EAAE;IACPR,UAAU,CAACQ,IAAI,CAAC;EACpB;EACAC,aAAaA,CAAA,EAAG;IACZ,OAAOR,KAAK;EAChB;EACAS,YAAYA,CAACC,SAAS,EAAEC,QAAQ,EAAE;IAC9B,MAAMC,SAAS,GAAGD,QAAQ,CAACC,SAAS;MAAEC,KAAK,GAAGD,SAAS,EAAEC,KAAK,IAAI;QAC9DC,GAAG,EAAEb,QAAQ;QACbc,GAAG,EAAEb;MACT,CAAC;IACDS,QAAQ,CAACK,WAAW,GAAG,CAAClB,QAAQ,CAACe,KAAK,CAAC,GACjC;MACEE,GAAG,EAAEb,QAAQ;MACbY,GAAG,EAAEjB,QAAQ,CAACgB,KAAK;IACvB,CAAC,GACC;MAAEE,GAAG,EAAElB,QAAQ,CAACgB,KAAK,CAACE,GAAG,CAAC;MAAED,GAAG,EAAEjB,QAAQ,CAACgB,KAAK,CAACC,GAAG;IAAE,CAAC;EAChE;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
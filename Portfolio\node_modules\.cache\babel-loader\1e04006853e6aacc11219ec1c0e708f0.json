{"ast": null, "code": "var __classPrivateFieldSet = this && this.__classPrivateFieldSet || function (receiver, state, value, kind, f) {\n  if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n  return kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value), value;\n};\n\nvar __classPrivateFieldGet = this && this.__classPrivateFieldGet || function (receiver, state, kind, f) {\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n  return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n};\n\nvar _InteractionManager_engine;\n\nexport class InteractionManager {\n  constructor(engine, container) {\n    this.container = container;\n\n    _InteractionManager_engine.set(this, void 0);\n\n    __classPrivateFieldSet(this, _InteractionManager_engine, engine, \"f\");\n\n    this.externalInteractors = [];\n    this.particleInteractors = [];\n    this.init();\n  }\n\n  init() {\n    const interactors = __classPrivateFieldGet(this, _InteractionManager_engine, \"f\").plugins.getInteractors(this.container, true);\n\n    this.externalInteractors = [];\n    this.particleInteractors = [];\n\n    for (const interactor of interactors) {\n      switch (interactor.type) {\n        case 0:\n          this.externalInteractors.push(interactor);\n          break;\n\n        case 1:\n          this.particleInteractors.push(interactor);\n          break;\n      }\n    }\n  }\n\n  async externalInteract(delta) {\n    for (const interactor of this.externalInteractors) {\n      if (interactor.isEnabled()) {\n        await interactor.interact(delta);\n      }\n    }\n  }\n\n  async particlesInteract(particle, delta) {\n    for (const interactor of this.externalInteractors) {\n      interactor.reset(particle);\n    }\n\n    for (const interactor of this.particleInteractors) {\n      if (interactor.isEnabled(particle)) {\n        await interactor.interact(particle, delta);\n      }\n    }\n  }\n\n}\n_InteractionManager_engine = new WeakMap();", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Core/Utils/InteractionManager.js"], "names": ["__classPrivateFieldSet", "receiver", "state", "value", "kind", "f", "TypeError", "has", "call", "set", "__classPrivateFieldGet", "get", "_InteractionManager_engine", "InteractionManager", "constructor", "engine", "container", "externalInteractors", "particleInteractors", "init", "interactors", "plugins", "getInteractors", "interactor", "type", "push", "externalInteract", "delta", "isEnabled", "interact", "particlesInteract", "particle", "reset", "WeakMap"], "mappings": "AAAA,IAAIA,sBAAsB,GAAI,QAAQ,KAAKA,sBAAd,IAAyC,UAAUC,QAAV,EAAoBC,KAApB,EAA2BC,KAA3B,EAAkCC,IAAlC,EAAwCC,CAAxC,EAA2C;AAC7G,MAAID,IAAI,KAAK,GAAb,EAAkB,MAAM,IAAIE,SAAJ,CAAc,gCAAd,CAAN;AAClB,MAAIF,IAAI,KAAK,GAAT,IAAgB,CAACC,CAArB,EAAwB,MAAM,IAAIC,SAAJ,CAAc,+CAAd,CAAN;AACxB,MAAI,OAAOJ,KAAP,KAAiB,UAAjB,GAA8BD,QAAQ,KAAKC,KAAb,IAAsB,CAACG,CAArD,GAAyD,CAACH,KAAK,CAACK,GAAN,CAAUN,QAAV,CAA9D,EAAmF,MAAM,IAAIK,SAAJ,CAAc,yEAAd,CAAN;AACnF,SAAQF,IAAI,KAAK,GAAT,GAAeC,CAAC,CAACG,IAAF,CAAOP,QAAP,EAAiBE,KAAjB,CAAf,GAAyCE,CAAC,GAAGA,CAAC,CAACF,KAAF,GAAUA,KAAb,GAAqBD,KAAK,CAACO,GAAN,CAAUR,QAAV,EAAoBE,KAApB,CAAhE,EAA6FA,KAApG;AACH,CALD;;AAMA,IAAIO,sBAAsB,GAAI,QAAQ,KAAKA,sBAAd,IAAyC,UAAUT,QAAV,EAAoBC,KAApB,EAA2BE,IAA3B,EAAiCC,CAAjC,EAAoC;AACtG,MAAID,IAAI,KAAK,GAAT,IAAgB,CAACC,CAArB,EAAwB,MAAM,IAAIC,SAAJ,CAAc,+CAAd,CAAN;AACxB,MAAI,OAAOJ,KAAP,KAAiB,UAAjB,GAA8BD,QAAQ,KAAKC,KAAb,IAAsB,CAACG,CAArD,GAAyD,CAACH,KAAK,CAACK,GAAN,CAAUN,QAAV,CAA9D,EAAmF,MAAM,IAAIK,SAAJ,CAAc,0EAAd,CAAN;AACnF,SAAOF,IAAI,KAAK,GAAT,GAAeC,CAAf,GAAmBD,IAAI,KAAK,GAAT,GAAeC,CAAC,CAACG,IAAF,CAAOP,QAAP,CAAf,GAAkCI,CAAC,GAAGA,CAAC,CAACF,KAAL,GAAaD,KAAK,CAACS,GAAN,CAAUV,QAAV,CAA1E;AACH,CAJD;;AAKA,IAAIW,0BAAJ;;AACA,OAAO,MAAMC,kBAAN,CAAyB;AAC5BC,EAAAA,WAAW,CAACC,MAAD,EAASC,SAAT,EAAoB;AAC3B,SAAKA,SAAL,GAAiBA,SAAjB;;AACAJ,IAAAA,0BAA0B,CAACH,GAA3B,CAA+B,IAA/B,EAAqC,KAAK,CAA1C;;AACAT,IAAAA,sBAAsB,CAAC,IAAD,EAAOY,0BAAP,EAAmCG,MAAnC,EAA2C,GAA3C,CAAtB;;AACA,SAAKE,mBAAL,GAA2B,EAA3B;AACA,SAAKC,mBAAL,GAA2B,EAA3B;AACA,SAAKC,IAAL;AACH;;AACDA,EAAAA,IAAI,GAAG;AACH,UAAMC,WAAW,GAAGV,sBAAsB,CAAC,IAAD,EAAOE,0BAAP,EAAmC,GAAnC,CAAtB,CAA8DS,OAA9D,CAAsEC,cAAtE,CAAqF,KAAKN,SAA1F,EAAqG,IAArG,CAApB;;AACA,SAAKC,mBAAL,GAA2B,EAA3B;AACA,SAAKC,mBAAL,GAA2B,EAA3B;;AACA,SAAK,MAAMK,UAAX,IAAyBH,WAAzB,EAAsC;AAClC,cAAQG,UAAU,CAACC,IAAnB;AACI,aAAK,CAAL;AACI,eAAKP,mBAAL,CAAyBQ,IAAzB,CAA8BF,UAA9B;AACA;;AACJ,aAAK,CAAL;AACI,eAAKL,mBAAL,CAAyBO,IAAzB,CAA8BF,UAA9B;AACA;AANR;AAQH;AACJ;;AACqB,QAAhBG,gBAAgB,CAACC,KAAD,EAAQ;AAC1B,SAAK,MAAMJ,UAAX,IAAyB,KAAKN,mBAA9B,EAAmD;AAC/C,UAAIM,UAAU,CAACK,SAAX,EAAJ,EAA4B;AACxB,cAAML,UAAU,CAACM,QAAX,CAAoBF,KAApB,CAAN;AACH;AACJ;AACJ;;AACsB,QAAjBG,iBAAiB,CAACC,QAAD,EAAWJ,KAAX,EAAkB;AACrC,SAAK,MAAMJ,UAAX,IAAyB,KAAKN,mBAA9B,EAAmD;AAC/CM,MAAAA,UAAU,CAACS,KAAX,CAAiBD,QAAjB;AACH;;AACD,SAAK,MAAMR,UAAX,IAAyB,KAAKL,mBAA9B,EAAmD;AAC/C,UAAIK,UAAU,CAACK,SAAX,CAAqBG,QAArB,CAAJ,EAAoC;AAChC,cAAMR,UAAU,CAACM,QAAX,CAAoBE,QAApB,EAA8BJ,KAA9B,CAAN;AACH;AACJ;AACJ;;AAxC2B;AA0ChCf,0BAA0B,GAAG,IAAIqB,OAAJ,EAA7B", "sourcesContent": ["var __classPrivateFieldSet = (this && this.__classPrivateFieldSet) || function (receiver, state, value, kind, f) {\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\n};\nvar __classPrivateFieldGet = (this && this.__classPrivateFieldGet) || function (receiver, state, kind, f) {\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n};\nvar _InteractionManager_engine;\nexport class InteractionManager {\n    constructor(engine, container) {\n        this.container = container;\n        _InteractionManager_engine.set(this, void 0);\n        __classPrivateFieldSet(this, _InteractionManager_engine, engine, \"f\");\n        this.externalInteractors = [];\n        this.particleInteractors = [];\n        this.init();\n    }\n    init() {\n        const interactors = __classPrivateFieldGet(this, _InteractionManager_engine, \"f\").plugins.getInteractors(this.container, true);\n        this.externalInteractors = [];\n        this.particleInteractors = [];\n        for (const interactor of interactors) {\n            switch (interactor.type) {\n                case 0:\n                    this.externalInteractors.push(interactor);\n                    break;\n                case 1:\n                    this.particleInteractors.push(interactor);\n                    break;\n            }\n        }\n    }\n    async externalInteract(delta) {\n        for (const interactor of this.externalInteractors) {\n            if (interactor.isEnabled()) {\n                await interactor.interact(delta);\n            }\n        }\n    }\n    async particlesInteract(particle, delta) {\n        for (const interactor of this.externalInteractors) {\n            interactor.reset(particle);\n        }\n        for (const interactor of this.particleInteractors) {\n            if (interactor.isEnabled(particle)) {\n                await interactor.interact(particle, delta);\n            }\n        }\n    }\n}\n_InteractionManager_engine = new WeakMap();\n"]}, "metadata": {}, "sourceType": "module"}
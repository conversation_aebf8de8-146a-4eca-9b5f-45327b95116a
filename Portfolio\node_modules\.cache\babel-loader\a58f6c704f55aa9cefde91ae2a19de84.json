{"ast": null, "code": "import { LifeUpdater } from \"./LifeUpdater\";\nexport async function loadLifeUpdater(engine) {\n  await engine.addParticleUpdater(\"life\", container => new LifeUpdater(container));\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Updaters/Life/index.js"], "names": ["LifeUpdater", "loadLifeUpdater", "engine", "addParticleUpdater", "container"], "mappings": "AAAA,SAASA,WAAT,QAA4B,eAA5B;AACA,OAAO,eAAeC,eAAf,CAA+BC,MAA/B,EAAuC;AAC1C,QAAMA,MAAM,CAACC,kBAAP,CAA0B,MAA1B,EAAmCC,SAAD,IAAe,IAAIJ,WAAJ,CAAgBI,SAAhB,CAAjD,CAAN;AACH", "sourcesContent": ["import { LifeUpdater } from \"./LifeUpdater\";\nexport async function loadLifeUpdater(engine) {\n    await engine.addParticleUpdater(\"life\", (container) => new LifeUpdater(container));\n}\n"]}, "metadata": {}, "sourceType": "module"}
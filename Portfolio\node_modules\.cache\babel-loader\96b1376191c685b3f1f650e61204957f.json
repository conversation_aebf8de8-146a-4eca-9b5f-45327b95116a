{"ast": null, "code": "import { deepExtend, setRangeValue } from \"../../../../Utils\";\nexport class Spin {\n  constructor() {\n    this.acceleration = 0;\n    this.enable = false;\n  }\n\n  load(data) {\n    if (!data) {\n      return;\n    }\n\n    if (data.acceleration !== undefined) {\n      this.acceleration = setRangeValue(data.acceleration);\n    }\n\n    if (data.enable !== undefined) {\n      this.enable = data.enable;\n    }\n\n    this.position = data.position ? deepExtend({}, data.position) : undefined;\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Options/Classes/Particles/Move/Spin.js"], "names": ["deepExtend", "setRangeValue", "Spin", "constructor", "acceleration", "enable", "load", "data", "undefined", "position"], "mappings": "AAAA,SAASA,UAAT,EAAqBC,aAArB,QAA0C,mBAA1C;AACA,OAAO,MAAMC,IAAN,CAAW;AACdC,EAAAA,WAAW,GAAG;AACV,SAAKC,YAAL,GAAoB,CAApB;AACA,SAAKC,MAAL,GAAc,KAAd;AACH;;AACDC,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,QAAI,CAACA,IAAL,EAAW;AACP;AACH;;AACD,QAAIA,IAAI,CAACH,YAAL,KAAsBI,SAA1B,EAAqC;AACjC,WAAKJ,YAAL,GAAoBH,aAAa,CAACM,IAAI,CAACH,YAAN,CAAjC;AACH;;AACD,QAAIG,IAAI,CAACF,MAAL,KAAgBG,SAApB,EAA+B;AAC3B,WAAKH,MAAL,GAAcE,IAAI,CAACF,MAAnB;AACH;;AACD,SAAKI,QAAL,GAAgBF,IAAI,CAACE,QAAL,GAAgBT,UAAU,CAAC,EAAD,EAAKO,IAAI,CAACE,QAAV,CAA1B,GAAgDD,SAAhE;AACH;;AAhBa", "sourcesContent": ["import { deepExtend, setRangeValue } from \"../../../../Utils\";\nexport class Spin {\n    constructor() {\n        this.acceleration = 0;\n        this.enable = false;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.acceleration !== undefined) {\n            this.acceleration = setRangeValue(data.acceleration);\n        }\n        if (data.enable !== undefined) {\n            this.enable = data.enable;\n        }\n        this.position = data.position ? deepExtend({}, data.position) : undefined;\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
{"ast": null, "code": "import { RepulseBase } from \"./RepulseBase\";\nexport class RepulseDiv extends RepulseBase {\n  constructor() {\n    super();\n    this.selectors = [];\n  }\n\n  get ids() {\n    if (this.selectors instanceof Array) {\n      return this.selectors.map(t => t.replace(\"#\", \"\"));\n    } else {\n      return this.selectors.replace(\"#\", \"\");\n    }\n  }\n\n  set ids(value) {\n    if (value instanceof Array) {\n      this.selectors = value.map(() => `#${value}`);\n    } else {\n      this.selectors = `#${value}`;\n    }\n  }\n\n  load(data) {\n    super.load(data);\n\n    if (data === undefined) {\n      return;\n    }\n\n    if (data.ids !== undefined) {\n      this.ids = data.ids;\n    }\n\n    if (data.selectors !== undefined) {\n      this.selectors = data.selectors;\n    }\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Options/Classes/Interactivity/Modes/RepulseDiv.js"], "names": ["RepulseBase", "RepulseDiv", "constructor", "selectors", "ids", "Array", "map", "t", "replace", "value", "load", "data", "undefined"], "mappings": "AAAA,SAASA,WAAT,QAA4B,eAA5B;AACA,OAAO,MAAMC,UAAN,SAAyBD,WAAzB,CAAqC;AACxCE,EAAAA,WAAW,GAAG;AACV;AACA,SAAKC,SAAL,GAAiB,EAAjB;AACH;;AACM,MAAHC,GAAG,GAAG;AACN,QAAI,KAAKD,SAAL,YAA0BE,KAA9B,EAAqC;AACjC,aAAO,KAAKF,SAAL,CAAeG,GAAf,CAAoBC,CAAD,IAAOA,CAAC,CAACC,OAAF,CAAU,GAAV,EAAe,EAAf,CAA1B,CAAP;AACH,KAFD,MAGK;AACD,aAAO,KAAKL,SAAL,CAAeK,OAAf,CAAuB,GAAvB,EAA4B,EAA5B,CAAP;AACH;AACJ;;AACM,MAAHJ,GAAG,CAACK,KAAD,EAAQ;AACX,QAAIA,KAAK,YAAYJ,KAArB,EAA4B;AACxB,WAAKF,SAAL,GAAiBM,KAAK,CAACH,GAAN,CAAU,MAAO,IAAGG,KAAM,EAA1B,CAAjB;AACH,KAFD,MAGK;AACD,WAAKN,SAAL,GAAkB,IAAGM,KAAM,EAA3B;AACH;AACJ;;AACDC,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,UAAMD,IAAN,CAAWC,IAAX;;AACA,QAAIA,IAAI,KAAKC,SAAb,EAAwB;AACpB;AACH;;AACD,QAAID,IAAI,CAACP,GAAL,KAAaQ,SAAjB,EAA4B;AACxB,WAAKR,GAAL,GAAWO,IAAI,CAACP,GAAhB;AACH;;AACD,QAAIO,IAAI,CAACR,SAAL,KAAmBS,SAAvB,EAAkC;AAC9B,WAAKT,SAAL,GAAiBQ,IAAI,CAACR,SAAtB;AACH;AACJ;;AAhCuC", "sourcesContent": ["import { RepulseBase } from \"./RepulseBase\";\nexport class RepulseDiv extends RepulseBase {\n    constructor() {\n        super();\n        this.selectors = [];\n    }\n    get ids() {\n        if (this.selectors instanceof Array) {\n            return this.selectors.map((t) => t.replace(\"#\", \"\"));\n        }\n        else {\n            return this.selectors.replace(\"#\", \"\");\n        }\n    }\n    set ids(value) {\n        if (value instanceof Array) {\n            this.selectors = value.map(() => `#${value}`);\n        }\n        else {\n            this.selectors = `#${value}`;\n        }\n    }\n    load(data) {\n        super.load(data);\n        if (data === undefined) {\n            return;\n        }\n        if (data.ids !== undefined) {\n            this.ids = data.ids;\n        }\n        if (data.selectors !== undefined) {\n            this.selectors = data.selectors;\n        }\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
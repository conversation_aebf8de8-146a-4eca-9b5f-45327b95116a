{"ast": null, "code": "import { BounceFactor } from \"./BounceFactor\";\nexport class Bounce {\n  constructor() {\n    this.horizontal = new BounceFactor();\n    this.vertical = new BounceFactor();\n  }\n\n  load(data) {\n    if (!data) {\n      return;\n    }\n\n    this.horizontal.load(data.horizontal);\n    this.vertical.load(data.vertical);\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Options/Classes/Particles/Bounce/Bounce.js"], "names": ["BounceFactor", "<PERSON><PERSON><PERSON>", "constructor", "horizontal", "vertical", "load", "data"], "mappings": "AAAA,SAASA,YAAT,QAA6B,gBAA7B;AACA,OAAO,MAAMC,MAAN,CAAa;AAChBC,EAAAA,WAAW,GAAG;AACV,SAAKC,UAAL,GAAkB,IAAIH,YAAJ,EAAlB;AACA,SAAKI,QAAL,GAAgB,IAAIJ,YAAJ,EAAhB;AACH;;AACDK,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,QAAI,CAACA,IAAL,EAAW;AACP;AACH;;AACD,SAAKH,UAAL,CAAgBE,IAAhB,CAAqBC,IAAI,CAACH,UAA1B;AACA,SAAKC,QAAL,CAAcC,IAAd,CAAmBC,IAAI,CAACF,QAAxB;AACH;;AAXe", "sourcesContent": ["import { BounceFactor } from \"./BounceFactor\";\nexport class Bounce {\n    constructor() {\n        this.horizontal = new BounceFactor();\n        this.vertical = new BounceFactor();\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        this.horizontal.load(data.horizontal);\n        this.vertical.load(data.vertical);\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
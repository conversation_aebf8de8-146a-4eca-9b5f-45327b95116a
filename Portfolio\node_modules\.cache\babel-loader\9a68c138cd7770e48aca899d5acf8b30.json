{"ast": null, "code": "var __classPrivateFieldSet = this && this.__classPrivateFieldSet || function (receiver, state, value, kind, f) {\n  if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n  return kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value), value;\n};\n\nvar _ShapeManager_engine;\n\nconst shapes = new Map();\nexport class ShapeManager {\n  constructor(engine) {\n    _ShapeManager_engine.set(this, void 0);\n\n    __classPrivateFieldSet(this, _ShapeManager_engine, engine, \"f\");\n  }\n\n  addShape(name, drawer) {\n    if (!this.getShape(name)) {\n      shapes.set(name, drawer);\n    }\n  }\n\n  getShape(name) {\n    return shapes.get(name);\n  }\n\n  getSupportedShapes() {\n    return shapes.keys();\n  }\n\n}\n_ShapeManager_engine = new WeakMap();", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Plugins/Emitters/ShapeManager.js"], "names": ["__classPrivateFieldSet", "receiver", "state", "value", "kind", "f", "TypeError", "has", "call", "set", "_ShapeManager_engine", "shapes", "Map", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "constructor", "engine", "addShape", "name", "drawer", "getShape", "get", "getSupportedShapes", "keys", "WeakMap"], "mappings": "AAAA,IAAIA,sBAAsB,GAAI,QAAQ,KAAKA,sBAAd,IAAyC,UAAUC,QAAV,EAAoBC,KAApB,EAA2BC,KAA3B,EAAkCC,IAAlC,EAAwCC,CAAxC,EAA2C;AAC7G,MAAID,IAAI,KAAK,GAAb,EAAkB,MAAM,IAAIE,SAAJ,CAAc,gCAAd,CAAN;AAClB,MAAIF,IAAI,KAAK,GAAT,IAAgB,CAACC,CAArB,EAAwB,MAAM,IAAIC,SAAJ,CAAc,+CAAd,CAAN;AACxB,MAAI,OAAOJ,KAAP,KAAiB,UAAjB,GAA8BD,QAAQ,KAAKC,KAAb,IAAsB,CAACG,CAArD,GAAyD,CAACH,KAAK,CAACK,GAAN,CAAUN,QAAV,CAA9D,EAAmF,MAAM,IAAIK,SAAJ,CAAc,yEAAd,CAAN;AACnF,SAAQF,IAAI,KAAK,GAAT,GAAeC,CAAC,CAACG,IAAF,CAAOP,QAAP,EAAiBE,KAAjB,CAAf,GAAyCE,CAAC,GAAGA,CAAC,CAACF,KAAF,GAAUA,KAAb,GAAqBD,KAAK,CAACO,GAAN,CAAUR,QAAV,EAAoBE,KAApB,CAAhE,EAA6FA,KAApG;AACH,CALD;;AAMA,IAAIO,oBAAJ;;AACA,MAAMC,MAAM,GAAG,IAAIC,GAAJ,EAAf;AACA,OAAO,MAAMC,YAAN,CAAmB;AACtBC,EAAAA,WAAW,CAACC,MAAD,EAAS;AAChBL,IAAAA,oBAAoB,CAACD,GAArB,CAAyB,IAAzB,EAA+B,KAAK,CAApC;;AACAT,IAAAA,sBAAsB,CAAC,IAAD,EAAOU,oBAAP,EAA6BK,MAA7B,EAAqC,GAArC,CAAtB;AACH;;AACDC,EAAAA,QAAQ,CAACC,IAAD,EAAOC,MAAP,EAAe;AACnB,QAAI,CAAC,KAAKC,QAAL,CAAcF,IAAd,CAAL,EAA0B;AACtBN,MAAAA,MAAM,CAACF,GAAP,CAAWQ,IAAX,EAAiBC,MAAjB;AACH;AACJ;;AACDC,EAAAA,QAAQ,CAACF,IAAD,EAAO;AACX,WAAON,MAAM,CAACS,GAAP,CAAWH,IAAX,CAAP;AACH;;AACDI,EAAAA,kBAAkB,GAAG;AACjB,WAAOV,MAAM,CAACW,IAAP,EAAP;AACH;;AAfqB;AAiB1BZ,oBAAoB,GAAG,IAAIa,OAAJ,EAAvB", "sourcesContent": ["var __classPrivateFieldSet = (this && this.__classPrivateFieldSet) || function (receiver, state, value, kind, f) {\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\n};\nvar _ShapeManager_engine;\nconst shapes = new Map();\nexport class ShapeManager {\n    constructor(engine) {\n        _ShapeManager_engine.set(this, void 0);\n        __classPrivateFieldSet(this, _ShapeManager_engine, engine, \"f\");\n    }\n    addShape(name, drawer) {\n        if (!this.getShape(name)) {\n            shapes.set(name, drawer);\n        }\n    }\n    getShape(name) {\n        return shapes.get(name);\n    }\n    getSupportedShapes() {\n        return shapes.keys();\n    }\n}\n_ShapeManager_engine = new WeakMap();\n"]}, "metadata": {}, "sourceType": "module"}
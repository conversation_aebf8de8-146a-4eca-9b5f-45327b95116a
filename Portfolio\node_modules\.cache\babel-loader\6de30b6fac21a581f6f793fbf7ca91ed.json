{"ast": null, "code": "import { colorToRgb, drawLinkLine, drawLinkTriangle, getDistance, getLinkColor, getRangeValue } from \"../../../Utils\";\nexport class LinkInstance {\n  constructor(container) {\n    this.container = container;\n  }\n\n  particleCreated(particle) {\n    const linkParticle = particle;\n    linkParticle.links = [];\n  }\n\n  particleDestroyed(particle) {\n    const linkParticle = particle;\n    linkParticle.links = [];\n  }\n\n  drawParticle(context, particle) {\n    const linkParticle = particle;\n    const container = this.container;\n    const particles = container.particles;\n    const pOptions = particle.options;\n\n    if (linkParticle.links.length > 0) {\n      context.save();\n      const p1Links = linkParticle.links.filter(l => {\n        const linkFreq = container.particles.getLinkFrequency(linkParticle, l.destination);\n        return linkFreq <= pOptions.links.frequency;\n      });\n\n      for (const link of p1Links) {\n        const p2 = link.destination;\n\n        if (pOptions.links.triangles.enable) {\n          const links = p1Links.map(l => l.destination);\n          const vertices = p2.links.filter(t => {\n            const linkFreq = container.particles.getLinkFrequency(p2, t.destination);\n            return linkFreq <= p2.options.links.frequency && links.indexOf(t.destination) >= 0;\n          });\n\n          if (vertices.length) {\n            for (const vertex of vertices) {\n              const p3 = vertex.destination;\n              const triangleFreq = particles.getTriangleFrequency(linkParticle, p2, p3);\n\n              if (triangleFreq > pOptions.links.triangles.frequency) {\n                continue;\n              }\n\n              this.drawLinkTriangle(linkParticle, link, vertex);\n            }\n          }\n        }\n\n        if (link.opacity > 0 && container.retina.linksWidth > 0) {\n          this.drawLinkLine(linkParticle, link);\n        }\n      }\n\n      context.restore();\n    }\n  }\n\n  drawLinkTriangle(p1, link1, link2) {\n    var _a;\n\n    const container = this.container;\n    const options = container.actualOptions;\n    const p2 = link1.destination;\n    const p3 = link2.destination;\n    const triangleOptions = p1.options.links.triangles;\n    const opacityTriangle = (_a = triangleOptions.opacity) !== null && _a !== void 0 ? _a : (link1.opacity + link2.opacity) / 2;\n\n    if (opacityTriangle <= 0) {\n      return;\n    }\n\n    const pos1 = p1.getPosition();\n    const pos2 = p2.getPosition();\n    const pos3 = p3.getPosition();\n    container.canvas.draw(ctx => {\n      if (getDistance(pos1, pos2) > container.retina.linksDistance || getDistance(pos3, pos2) > container.retina.linksDistance || getDistance(pos3, pos1) > container.retina.linksDistance) {\n        return;\n      }\n\n      let colorTriangle = colorToRgb(triangleOptions.color);\n\n      if (!colorTriangle) {\n        const linksOptions = p1.options.links;\n        const linkColor = linksOptions.id !== undefined ? container.particles.linksColors.get(linksOptions.id) : container.particles.linksColor;\n        colorTriangle = getLinkColor(p1, p2, linkColor);\n      }\n\n      if (!colorTriangle) {\n        return;\n      }\n\n      drawLinkTriangle(ctx, pos1, pos2, pos3, options.backgroundMask.enable, options.backgroundMask.composite, colorTriangle, opacityTriangle);\n    });\n  }\n\n  drawLinkLine(p1, link) {\n    const container = this.container;\n    const options = container.actualOptions;\n    const p2 = link.destination;\n    let opacity = link.opacity;\n    const pos1 = p1.getPosition();\n    const pos2 = p2.getPosition();\n    container.canvas.draw(ctx => {\n      var _a, _b;\n\n      let colorLine;\n      const twinkle = p1.options.twinkle.lines;\n\n      if (twinkle.enable) {\n        const twinkleFreq = twinkle.frequency;\n        const twinkleRgb = colorToRgb(twinkle.color);\n        const twinkling = Math.random() < twinkleFreq;\n\n        if (twinkling && twinkleRgb !== undefined) {\n          colorLine = twinkleRgb;\n          opacity = getRangeValue(twinkle.opacity);\n        }\n      }\n\n      if (!colorLine) {\n        const linksOptions = p1.options.links;\n        const linkColor = linksOptions.id !== undefined ? container.particles.linksColors.get(linksOptions.id) : container.particles.linksColor;\n        colorLine = getLinkColor(p1, p2, linkColor);\n      }\n\n      if (!colorLine) {\n        return;\n      }\n\n      const width = (_a = p1.retina.linksWidth) !== null && _a !== void 0 ? _a : container.retina.linksWidth;\n      const maxDistance = (_b = p1.retina.linksDistance) !== null && _b !== void 0 ? _b : container.retina.linksDistance;\n      drawLinkLine(ctx, width, pos1, pos2, maxDistance, container.canvas.size, p1.options.links.warp, options.backgroundMask.enable, options.backgroundMask.composite, colorLine, opacity, p1.options.links.shadow);\n    });\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Interactions/Particles/Links/LinkInstance.js"], "names": ["colorToRgb", "drawLinkLine", "drawLinkTriangle", "getDistance", "getLinkColor", "getRangeValue", "LinkInstance", "constructor", "container", "particleCreated", "particle", "linkParticle", "links", "particleDestroyed", "drawParticle", "context", "particles", "pOptions", "options", "length", "save", "p1Links", "filter", "l", "linkFreq", "getLinkFrequency", "destination", "frequency", "link", "p2", "triangles", "enable", "map", "vertices", "t", "indexOf", "vertex", "p3", "triangleFreq", "getTriangleFrequency", "opacity", "retina", "linksWidth", "restore", "p1", "link1", "link2", "_a", "actualOptions", "triangleOptions", "opacityTriangle", "pos1", "getPosition", "pos2", "pos3", "canvas", "draw", "ctx", "linksDistance", "colorTriangle", "color", "linksOptions", "linkColor", "id", "undefined", "linksColors", "get", "linksColor", "backgroundMask", "composite", "_b", "colorLine", "twinkle", "lines", "twinkleFreq", "twinkleRgb", "twinkling", "Math", "random", "width", "maxDistance", "size", "warp", "shadow"], "mappings": "AAAA,SAASA,UAAT,EAAqBC,YAArB,EAAmCC,gBAAnC,EAAqDC,WAArD,EAAkEC,YAAlE,EAAgFC,aAAhF,QAAqG,gBAArG;AACA,OAAO,MAAMC,YAAN,CAAmB;AACtBC,EAAAA,WAAW,CAACC,SAAD,EAAY;AACnB,SAAKA,SAAL,GAAiBA,SAAjB;AACH;;AACDC,EAAAA,eAAe,CAACC,QAAD,EAAW;AACtB,UAAMC,YAAY,GAAGD,QAArB;AACAC,IAAAA,YAAY,CAACC,KAAb,GAAqB,EAArB;AACH;;AACDC,EAAAA,iBAAiB,CAACH,QAAD,EAAW;AACxB,UAAMC,YAAY,GAAGD,QAArB;AACAC,IAAAA,YAAY,CAACC,KAAb,GAAqB,EAArB;AACH;;AACDE,EAAAA,YAAY,CAACC,OAAD,EAAUL,QAAV,EAAoB;AAC5B,UAAMC,YAAY,GAAGD,QAArB;AACA,UAAMF,SAAS,GAAG,KAAKA,SAAvB;AACA,UAAMQ,SAAS,GAAGR,SAAS,CAACQ,SAA5B;AACA,UAAMC,QAAQ,GAAGP,QAAQ,CAACQ,OAA1B;;AACA,QAAIP,YAAY,CAACC,KAAb,CAAmBO,MAAnB,GAA4B,CAAhC,EAAmC;AAC/BJ,MAAAA,OAAO,CAACK,IAAR;AACA,YAAMC,OAAO,GAAGV,YAAY,CAACC,KAAb,CAAmBU,MAAnB,CAA2BC,CAAD,IAAO;AAC7C,cAAMC,QAAQ,GAAGhB,SAAS,CAACQ,SAAV,CAAoBS,gBAApB,CAAqCd,YAArC,EAAmDY,CAAC,CAACG,WAArD,CAAjB;AACA,eAAOF,QAAQ,IAAIP,QAAQ,CAACL,KAAT,CAAee,SAAlC;AACH,OAHe,CAAhB;;AAIA,WAAK,MAAMC,IAAX,IAAmBP,OAAnB,EAA4B;AACxB,cAAMQ,EAAE,GAAGD,IAAI,CAACF,WAAhB;;AACA,YAAIT,QAAQ,CAACL,KAAT,CAAekB,SAAf,CAAyBC,MAA7B,EAAqC;AACjC,gBAAMnB,KAAK,GAAGS,OAAO,CAACW,GAAR,CAAaT,CAAD,IAAOA,CAAC,CAACG,WAArB,CAAd;AACA,gBAAMO,QAAQ,GAAGJ,EAAE,CAACjB,KAAH,CAASU,MAAT,CAAiBY,CAAD,IAAO;AACpC,kBAAMV,QAAQ,GAAGhB,SAAS,CAACQ,SAAV,CAAoBS,gBAApB,CAAqCI,EAArC,EAAyCK,CAAC,CAACR,WAA3C,CAAjB;AACA,mBAAOF,QAAQ,IAAIK,EAAE,CAACX,OAAH,CAAWN,KAAX,CAAiBe,SAA7B,IAA0Cf,KAAK,CAACuB,OAAN,CAAcD,CAAC,CAACR,WAAhB,KAAgC,CAAjF;AACH,WAHgB,CAAjB;;AAIA,cAAIO,QAAQ,CAACd,MAAb,EAAqB;AACjB,iBAAK,MAAMiB,MAAX,IAAqBH,QAArB,EAA+B;AAC3B,oBAAMI,EAAE,GAAGD,MAAM,CAACV,WAAlB;AACA,oBAAMY,YAAY,GAAGtB,SAAS,CAACuB,oBAAV,CAA+B5B,YAA/B,EAA6CkB,EAA7C,EAAiDQ,EAAjD,CAArB;;AACA,kBAAIC,YAAY,GAAGrB,QAAQ,CAACL,KAAT,CAAekB,SAAf,CAAyBH,SAA5C,EAAuD;AACnD;AACH;;AACD,mBAAKzB,gBAAL,CAAsBS,YAAtB,EAAoCiB,IAApC,EAA0CQ,MAA1C;AACH;AACJ;AACJ;;AACD,YAAIR,IAAI,CAACY,OAAL,GAAe,CAAf,IAAoBhC,SAAS,CAACiC,MAAV,CAAiBC,UAAjB,GAA8B,CAAtD,EAAyD;AACrD,eAAKzC,YAAL,CAAkBU,YAAlB,EAAgCiB,IAAhC;AACH;AACJ;;AACDb,MAAAA,OAAO,CAAC4B,OAAR;AACH;AACJ;;AACDzC,EAAAA,gBAAgB,CAAC0C,EAAD,EAAKC,KAAL,EAAYC,KAAZ,EAAmB;AAC/B,QAAIC,EAAJ;;AACA,UAAMvC,SAAS,GAAG,KAAKA,SAAvB;AACA,UAAMU,OAAO,GAAGV,SAAS,CAACwC,aAA1B;AACA,UAAMnB,EAAE,GAAGgB,KAAK,CAACnB,WAAjB;AACA,UAAMW,EAAE,GAAGS,KAAK,CAACpB,WAAjB;AACA,UAAMuB,eAAe,GAAGL,EAAE,CAAC1B,OAAH,CAAWN,KAAX,CAAiBkB,SAAzC;AACA,UAAMoB,eAAe,GAAG,CAACH,EAAE,GAAGE,eAAe,CAACT,OAAtB,MAAmC,IAAnC,IAA2CO,EAAE,KAAK,KAAK,CAAvD,GAA2DA,EAA3D,GAAgE,CAACF,KAAK,CAACL,OAAN,GAAgBM,KAAK,CAACN,OAAvB,IAAkC,CAA1H;;AACA,QAAIU,eAAe,IAAI,CAAvB,EAA0B;AACtB;AACH;;AACD,UAAMC,IAAI,GAAGP,EAAE,CAACQ,WAAH,EAAb;AACA,UAAMC,IAAI,GAAGxB,EAAE,CAACuB,WAAH,EAAb;AACA,UAAME,IAAI,GAAGjB,EAAE,CAACe,WAAH,EAAb;AACA5C,IAAAA,SAAS,CAAC+C,MAAV,CAAiBC,IAAjB,CAAuBC,GAAD,IAAS;AAC3B,UAAItD,WAAW,CAACgD,IAAD,EAAOE,IAAP,CAAX,GAA0B7C,SAAS,CAACiC,MAAV,CAAiBiB,aAA3C,IACAvD,WAAW,CAACmD,IAAD,EAAOD,IAAP,CAAX,GAA0B7C,SAAS,CAACiC,MAAV,CAAiBiB,aAD3C,IAEAvD,WAAW,CAACmD,IAAD,EAAOH,IAAP,CAAX,GAA0B3C,SAAS,CAACiC,MAAV,CAAiBiB,aAF/C,EAE8D;AAC1D;AACH;;AACD,UAAIC,aAAa,GAAG3D,UAAU,CAACiD,eAAe,CAACW,KAAjB,CAA9B;;AACA,UAAI,CAACD,aAAL,EAAoB;AAChB,cAAME,YAAY,GAAGjB,EAAE,CAAC1B,OAAH,CAAWN,KAAhC;AACA,cAAMkD,SAAS,GAAGD,YAAY,CAACE,EAAb,KAAoBC,SAApB,GACZxD,SAAS,CAACQ,SAAV,CAAoBiD,WAApB,CAAgCC,GAAhC,CAAoCL,YAAY,CAACE,EAAjD,CADY,GAEZvD,SAAS,CAACQ,SAAV,CAAoBmD,UAF1B;AAGAR,QAAAA,aAAa,GAAGvD,YAAY,CAACwC,EAAD,EAAKf,EAAL,EAASiC,SAAT,CAA5B;AACH;;AACD,UAAI,CAACH,aAAL,EAAoB;AAChB;AACH;;AACDzD,MAAAA,gBAAgB,CAACuD,GAAD,EAAMN,IAAN,EAAYE,IAAZ,EAAkBC,IAAlB,EAAwBpC,OAAO,CAACkD,cAAR,CAAuBrC,MAA/C,EAAuDb,OAAO,CAACkD,cAAR,CAAuBC,SAA9E,EAAyFV,aAAzF,EAAwGT,eAAxG,CAAhB;AACH,KAlBD;AAmBH;;AACDjD,EAAAA,YAAY,CAAC2C,EAAD,EAAKhB,IAAL,EAAW;AACnB,UAAMpB,SAAS,GAAG,KAAKA,SAAvB;AACA,UAAMU,OAAO,GAAGV,SAAS,CAACwC,aAA1B;AACA,UAAMnB,EAAE,GAAGD,IAAI,CAACF,WAAhB;AACA,QAAIc,OAAO,GAAGZ,IAAI,CAACY,OAAnB;AACA,UAAMW,IAAI,GAAGP,EAAE,CAACQ,WAAH,EAAb;AACA,UAAMC,IAAI,GAAGxB,EAAE,CAACuB,WAAH,EAAb;AACA5C,IAAAA,SAAS,CAAC+C,MAAV,CAAiBC,IAAjB,CAAuBC,GAAD,IAAS;AAC3B,UAAIV,EAAJ,EAAQuB,EAAR;;AACA,UAAIC,SAAJ;AACA,YAAMC,OAAO,GAAG5B,EAAE,CAAC1B,OAAH,CAAWsD,OAAX,CAAmBC,KAAnC;;AACA,UAAID,OAAO,CAACzC,MAAZ,EAAoB;AAChB,cAAM2C,WAAW,GAAGF,OAAO,CAAC7C,SAA5B;AACA,cAAMgD,UAAU,GAAG3E,UAAU,CAACwE,OAAO,CAACZ,KAAT,CAA7B;AACA,cAAMgB,SAAS,GAAGC,IAAI,CAACC,MAAL,KAAgBJ,WAAlC;;AACA,YAAIE,SAAS,IAAID,UAAU,KAAKX,SAAhC,EAA2C;AACvCO,UAAAA,SAAS,GAAGI,UAAZ;AACAnC,UAAAA,OAAO,GAAGnC,aAAa,CAACmE,OAAO,CAAChC,OAAT,CAAvB;AACH;AACJ;;AACD,UAAI,CAAC+B,SAAL,EAAgB;AACZ,cAAMV,YAAY,GAAGjB,EAAE,CAAC1B,OAAH,CAAWN,KAAhC;AACA,cAAMkD,SAAS,GAAGD,YAAY,CAACE,EAAb,KAAoBC,SAApB,GACZxD,SAAS,CAACQ,SAAV,CAAoBiD,WAApB,CAAgCC,GAAhC,CAAoCL,YAAY,CAACE,EAAjD,CADY,GAEZvD,SAAS,CAACQ,SAAV,CAAoBmD,UAF1B;AAGAI,QAAAA,SAAS,GAAGnE,YAAY,CAACwC,EAAD,EAAKf,EAAL,EAASiC,SAAT,CAAxB;AACH;;AACD,UAAI,CAACS,SAAL,EAAgB;AACZ;AACH;;AACD,YAAMQ,KAAK,GAAG,CAAChC,EAAE,GAAGH,EAAE,CAACH,MAAH,CAAUC,UAAhB,MAAgC,IAAhC,IAAwCK,EAAE,KAAK,KAAK,CAApD,GAAwDA,EAAxD,GAA6DvC,SAAS,CAACiC,MAAV,CAAiBC,UAA5F;AACA,YAAMsC,WAAW,GAAG,CAACV,EAAE,GAAG1B,EAAE,CAACH,MAAH,CAAUiB,aAAhB,MAAmC,IAAnC,IAA2CY,EAAE,KAAK,KAAK,CAAvD,GAA2DA,EAA3D,GAAgE9D,SAAS,CAACiC,MAAV,CAAiBiB,aAArG;AACAzD,MAAAA,YAAY,CAACwD,GAAD,EAAMsB,KAAN,EAAa5B,IAAb,EAAmBE,IAAnB,EAAyB2B,WAAzB,EAAsCxE,SAAS,CAAC+C,MAAV,CAAiB0B,IAAvD,EAA6DrC,EAAE,CAAC1B,OAAH,CAAWN,KAAX,CAAiBsE,IAA9E,EAAoFhE,OAAO,CAACkD,cAAR,CAAuBrC,MAA3G,EAAmHb,OAAO,CAACkD,cAAR,CAAuBC,SAA1I,EAAqJE,SAArJ,EAAgK/B,OAAhK,EAAyKI,EAAE,CAAC1B,OAAH,CAAWN,KAAX,CAAiBuE,MAA1L,CAAZ;AACH,KA1BD;AA2BH;;AArHqB", "sourcesContent": ["import { colorToRgb, drawLinkLine, drawLinkTriangle, getDistance, getLinkColor, getRangeValue } from \"../../../Utils\";\nexport class LinkInstance {\n    constructor(container) {\n        this.container = container;\n    }\n    particleCreated(particle) {\n        const linkParticle = particle;\n        linkParticle.links = [];\n    }\n    particleDestroyed(particle) {\n        const linkParticle = particle;\n        linkParticle.links = [];\n    }\n    drawParticle(context, particle) {\n        const linkParticle = particle;\n        const container = this.container;\n        const particles = container.particles;\n        const pOptions = particle.options;\n        if (linkParticle.links.length > 0) {\n            context.save();\n            const p1Links = linkParticle.links.filter((l) => {\n                const linkFreq = container.particles.getLinkFrequency(linkParticle, l.destination);\n                return linkFreq <= pOptions.links.frequency;\n            });\n            for (const link of p1Links) {\n                const p2 = link.destination;\n                if (pOptions.links.triangles.enable) {\n                    const links = p1Links.map((l) => l.destination);\n                    const vertices = p2.links.filter((t) => {\n                        const linkFreq = container.particles.getLinkFrequency(p2, t.destination);\n                        return linkFreq <= p2.options.links.frequency && links.indexOf(t.destination) >= 0;\n                    });\n                    if (vertices.length) {\n                        for (const vertex of vertices) {\n                            const p3 = vertex.destination;\n                            const triangleFreq = particles.getTriangleFrequency(linkParticle, p2, p3);\n                            if (triangleFreq > pOptions.links.triangles.frequency) {\n                                continue;\n                            }\n                            this.drawLinkTriangle(linkParticle, link, vertex);\n                        }\n                    }\n                }\n                if (link.opacity > 0 && container.retina.linksWidth > 0) {\n                    this.drawLinkLine(linkParticle, link);\n                }\n            }\n            context.restore();\n        }\n    }\n    drawLinkTriangle(p1, link1, link2) {\n        var _a;\n        const container = this.container;\n        const options = container.actualOptions;\n        const p2 = link1.destination;\n        const p3 = link2.destination;\n        const triangleOptions = p1.options.links.triangles;\n        const opacityTriangle = (_a = triangleOptions.opacity) !== null && _a !== void 0 ? _a : (link1.opacity + link2.opacity) / 2;\n        if (opacityTriangle <= 0) {\n            return;\n        }\n        const pos1 = p1.getPosition();\n        const pos2 = p2.getPosition();\n        const pos3 = p3.getPosition();\n        container.canvas.draw((ctx) => {\n            if (getDistance(pos1, pos2) > container.retina.linksDistance ||\n                getDistance(pos3, pos2) > container.retina.linksDistance ||\n                getDistance(pos3, pos1) > container.retina.linksDistance) {\n                return;\n            }\n            let colorTriangle = colorToRgb(triangleOptions.color);\n            if (!colorTriangle) {\n                const linksOptions = p1.options.links;\n                const linkColor = linksOptions.id !== undefined\n                    ? container.particles.linksColors.get(linksOptions.id)\n                    : container.particles.linksColor;\n                colorTriangle = getLinkColor(p1, p2, linkColor);\n            }\n            if (!colorTriangle) {\n                return;\n            }\n            drawLinkTriangle(ctx, pos1, pos2, pos3, options.backgroundMask.enable, options.backgroundMask.composite, colorTriangle, opacityTriangle);\n        });\n    }\n    drawLinkLine(p1, link) {\n        const container = this.container;\n        const options = container.actualOptions;\n        const p2 = link.destination;\n        let opacity = link.opacity;\n        const pos1 = p1.getPosition();\n        const pos2 = p2.getPosition();\n        container.canvas.draw((ctx) => {\n            var _a, _b;\n            let colorLine;\n            const twinkle = p1.options.twinkle.lines;\n            if (twinkle.enable) {\n                const twinkleFreq = twinkle.frequency;\n                const twinkleRgb = colorToRgb(twinkle.color);\n                const twinkling = Math.random() < twinkleFreq;\n                if (twinkling && twinkleRgb !== undefined) {\n                    colorLine = twinkleRgb;\n                    opacity = getRangeValue(twinkle.opacity);\n                }\n            }\n            if (!colorLine) {\n                const linksOptions = p1.options.links;\n                const linkColor = linksOptions.id !== undefined\n                    ? container.particles.linksColors.get(linksOptions.id)\n                    : container.particles.linksColor;\n                colorLine = getLinkColor(p1, p2, linkColor);\n            }\n            if (!colorLine) {\n                return;\n            }\n            const width = (_a = p1.retina.linksWidth) !== null && _a !== void 0 ? _a : container.retina.linksWidth;\n            const maxDistance = (_b = p1.retina.linksDistance) !== null && _b !== void 0 ? _b : container.retina.linksDistance;\n            drawLinkLine(ctx, width, pos1, pos2, maxDistance, container.canvas.size, p1.options.links.warp, options.backgroundMask.enable, options.backgroundMask.composite, colorLine, opacity, p1.options.links.shadow);\n        });\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
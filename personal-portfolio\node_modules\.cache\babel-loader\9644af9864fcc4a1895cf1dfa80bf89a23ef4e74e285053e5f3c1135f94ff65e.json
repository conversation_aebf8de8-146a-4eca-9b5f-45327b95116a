{"ast": null, "code": "import { OutOfCanvasUpdater } from \"./OutOfCanvasUpdater.js\";\nexport async function loadOutModesUpdater(engine, refresh = true) {\n  await engine.addParticleUpdater(\"outModes\", container => {\n    return Promise.resolve(new OutOfCanvasUpdater(container));\n  }, refresh);\n}", "map": {"version": 3, "names": ["OutOfCanvasUpdater", "loadOutModesUpdater", "engine", "refresh", "addParticleUpdater", "container", "Promise", "resolve"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/updater-out-modes/browser/index.js"], "sourcesContent": ["import { OutOfCanvasUpdater } from \"./OutOfCanvasUpdater.js\";\nexport async function loadOutModesUpdater(engine, refresh = true) {\n    await engine.addParticleUpdater(\"outModes\", container => {\n        return Promise.resolve(new OutOfCanvasUpdater(container));\n    }, refresh);\n}\n"], "mappings": "AAAA,SAASA,kBAAkB,QAAQ,yBAAyB;AAC5D,OAAO,eAAeC,mBAAmBA,CAACC,MAAM,EAAEC,OAAO,GAAG,IAAI,EAAE;EAC9D,MAAMD,MAAM,CAACE,kBAAkB,CAAC,UAAU,EAAEC,SAAS,IAAI;IACrD,OAAOC,OAAO,CAACC,OAAO,CAAC,IAAIP,kBAAkB,CAACK,SAAS,CAAC,CAAC;EAC7D,CAAC,EAAEF,OAAO,CAAC;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
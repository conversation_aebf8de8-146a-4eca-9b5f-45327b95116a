{"ast": null, "code": "var __classPrivateFieldSet = this && this.__classPrivateFieldSet || function (receiver, state, value, kind, f) {\n  if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n  return kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value), value;\n};\n\nvar __classPrivateFieldGet = this && this.__classPrivateFieldGet || function (receiver, state, kind, f) {\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n  return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n};\n\nvar _EmitterInstance_firstSpawn, _EmitterInstance_startParticlesAdded, _EmitterInstance_engine;\n\nimport { colorToHsl, deepExtend, getRangeValue, isPointInside, randomInRange } from \"../../Utils\";\nimport { Emitter } from \"./Options/Classes/Emitter\";\nimport { EmitterSize } from \"./Options/Classes/EmitterSize\";\nexport class EmitterInstance {\n  constructor(engine, emitters, container, options, position) {\n    var _a, _b, _c, _d, _e, _f, _g;\n\n    var _h;\n\n    this.emitters = emitters;\n    this.container = container;\n\n    _EmitterInstance_firstSpawn.set(this, void 0);\n\n    _EmitterInstance_startParticlesAdded.set(this, void 0);\n\n    _EmitterInstance_engine.set(this, void 0);\n\n    __classPrivateFieldSet(this, _EmitterInstance_engine, engine, \"f\");\n\n    this.currentDuration = 0;\n    this.currentEmitDelay = 0;\n    this.currentSpawnDelay = 0;\n    this.initialPosition = position;\n\n    if (options instanceof Emitter) {\n      this.options = options;\n    } else {\n      this.options = new Emitter();\n      this.options.load(options);\n    }\n\n    this.spawnDelay = ((_a = this.options.life.delay) !== null && _a !== void 0 ? _a : 0) * 1000 / this.container.retina.reduceFactor;\n    this.position = (_b = this.initialPosition) !== null && _b !== void 0 ? _b : this.calcPosition();\n    this.name = this.options.name;\n    this.shape = (_c = __classPrivateFieldGet(this, _EmitterInstance_engine, \"f\").emitterShapeManager) === null || _c === void 0 ? void 0 : _c.getShape(this.options.shape);\n    this.fill = this.options.fill;\n\n    __classPrivateFieldSet(this, _EmitterInstance_firstSpawn, !this.options.life.wait, \"f\");\n\n    __classPrivateFieldSet(this, _EmitterInstance_startParticlesAdded, false, \"f\");\n\n    let particlesOptions = deepExtend({}, this.options.particles);\n    particlesOptions !== null && particlesOptions !== void 0 ? particlesOptions : particlesOptions = {};\n    (_d = particlesOptions.move) !== null && _d !== void 0 ? _d : particlesOptions.move = {};\n    (_e = (_h = particlesOptions.move).direction) !== null && _e !== void 0 ? _e : _h.direction = this.options.direction;\n\n    if (this.options.spawnColor) {\n      this.spawnColor = colorToHsl(this.options.spawnColor);\n    }\n\n    this.paused = !this.options.autoPlay;\n    this.particlesOptions = particlesOptions;\n    this.size = (_f = this.options.size) !== null && _f !== void 0 ? _f : (() => {\n      const size = new EmitterSize();\n      size.load({\n        height: 0,\n        mode: \"percent\",\n        width: 0\n      });\n      return size;\n    })();\n    this.lifeCount = (_g = this.options.life.count) !== null && _g !== void 0 ? _g : -1;\n    this.immortal = this.lifeCount <= 0;\n    this.play();\n  }\n\n  externalPlay() {\n    this.paused = false;\n    this.play();\n  }\n\n  externalPause() {\n    this.paused = true;\n    this.pause();\n  }\n\n  play() {\n    var _a;\n\n    if (this.paused) {\n      return;\n    }\n\n    if (this.container.retina.reduceFactor && (this.lifeCount > 0 || this.immortal || !this.options.life.count) && (__classPrivateFieldGet(this, _EmitterInstance_firstSpawn, \"f\") || this.currentSpawnDelay >= ((_a = this.spawnDelay) !== null && _a !== void 0 ? _a : 0))) {\n      if (this.emitDelay === undefined) {\n        const delay = getRangeValue(this.options.rate.delay);\n        this.emitDelay = 1000 * delay / this.container.retina.reduceFactor;\n      }\n\n      if (this.lifeCount > 0 || this.immortal) {\n        this.prepareToDie();\n      }\n    }\n  }\n\n  pause() {\n    if (this.paused) {\n      return;\n    }\n\n    delete this.emitDelay;\n  }\n\n  resize() {\n    const initialPosition = this.initialPosition;\n    this.position = initialPosition && isPointInside(initialPosition, this.container.canvas.size) ? initialPosition : this.calcPosition();\n  }\n\n  update(delta) {\n    var _a, _b, _c;\n\n    if (this.paused) {\n      return;\n    }\n\n    if (__classPrivateFieldGet(this, _EmitterInstance_firstSpawn, \"f\")) {\n      __classPrivateFieldSet(this, _EmitterInstance_firstSpawn, false, \"f\");\n\n      this.currentSpawnDelay = (_a = this.spawnDelay) !== null && _a !== void 0 ? _a : 0;\n      this.currentEmitDelay = (_b = this.emitDelay) !== null && _b !== void 0 ? _b : 0;\n    }\n\n    if (!__classPrivateFieldGet(this, _EmitterInstance_startParticlesAdded, \"f\")) {\n      __classPrivateFieldSet(this, _EmitterInstance_startParticlesAdded, true, \"f\");\n\n      this.emitParticles(this.options.startCount);\n    }\n\n    if (this.duration !== undefined) {\n      this.currentDuration += delta.value;\n\n      if (this.currentDuration >= this.duration) {\n        this.pause();\n\n        if (this.spawnDelay !== undefined) {\n          delete this.spawnDelay;\n        }\n\n        if (!this.immortal) {\n          this.lifeCount--;\n        }\n\n        if (this.lifeCount > 0 || this.immortal) {\n          this.position = this.calcPosition();\n          this.spawnDelay = ((_c = this.options.life.delay) !== null && _c !== void 0 ? _c : 0) * 1000 / this.container.retina.reduceFactor;\n        } else {\n          this.destroy();\n        }\n\n        this.currentDuration -= this.duration;\n        delete this.duration;\n      }\n    }\n\n    if (this.spawnDelay !== undefined) {\n      this.currentSpawnDelay += delta.value;\n\n      if (this.currentSpawnDelay >= this.spawnDelay) {\n        this.play();\n        this.currentSpawnDelay -= this.currentSpawnDelay;\n        delete this.spawnDelay;\n      }\n    }\n\n    if (this.emitDelay !== undefined) {\n      this.currentEmitDelay += delta.value;\n\n      if (this.currentEmitDelay >= this.emitDelay) {\n        this.emit();\n        this.currentEmitDelay -= this.emitDelay;\n      }\n    }\n  }\n\n  getPosition() {\n    if (this.options.domId) {\n      const container = this.container,\n            element = document.getElementById(this.options.domId);\n\n      if (element) {\n        const elRect = element.getBoundingClientRect();\n        return {\n          x: (elRect.x + elRect.width / 2) * container.retina.pixelRatio,\n          y: (elRect.y + elRect.height / 2) * container.retina.pixelRatio\n        };\n      }\n    }\n\n    return this.position;\n  }\n\n  getSize() {\n    const container = this.container;\n\n    if (this.options.domId) {\n      const element = document.getElementById(this.options.domId);\n\n      if (element) {\n        const elRect = element.getBoundingClientRect();\n        return {\n          width: elRect.width * container.retina.pixelRatio,\n          height: elRect.height * container.retina.pixelRatio\n        };\n      }\n    }\n\n    return {\n      width: this.size.mode === \"percent\" ? container.canvas.size.width * this.size.width / 100 : this.size.width,\n      height: this.size.mode === \"percent\" ? container.canvas.size.height * this.size.height / 100 : this.size.height\n    };\n  }\n\n  prepareToDie() {\n    var _a;\n\n    if (this.paused) {\n      return;\n    }\n\n    const duration = (_a = this.options.life) === null || _a === void 0 ? void 0 : _a.duration;\n\n    if (this.container.retina.reduceFactor && (this.lifeCount > 0 || this.immortal) && duration !== undefined && duration > 0) {\n      this.duration = duration * 1000;\n    }\n  }\n\n  destroy() {\n    this.emitters.removeEmitter(this);\n  }\n\n  calcPosition() {\n    var _a, _b;\n\n    const container = this.container;\n    const percentPosition = this.options.position;\n    return {\n      x: getRangeValue((_a = percentPosition === null || percentPosition === void 0 ? void 0 : percentPosition.x) !== null && _a !== void 0 ? _a : Math.random() * 100) / 100 * container.canvas.size.width,\n      y: getRangeValue((_b = percentPosition === null || percentPosition === void 0 ? void 0 : percentPosition.y) !== null && _b !== void 0 ? _b : Math.random() * 100) / 100 * container.canvas.size.height\n    };\n  }\n\n  emit() {\n    if (this.paused) {\n      return;\n    }\n\n    const quantity = getRangeValue(this.options.rate.quantity);\n    this.emitParticles(quantity);\n  }\n\n  emitParticles(quantity) {\n    var _a, _b, _c;\n\n    const container = this.container;\n    const position = this.getPosition();\n    const size = this.getSize();\n\n    for (let i = 0; i < quantity; i++) {\n      const particlesOptions = deepExtend({}, this.particlesOptions);\n\n      if (this.spawnColor) {\n        const hslAnimation = (_a = this.options.spawnColor) === null || _a === void 0 ? void 0 : _a.animation;\n\n        if (hslAnimation) {\n          this.spawnColor.h = this.setColorAnimation(hslAnimation.h, this.spawnColor.h, 360);\n          this.spawnColor.s = this.setColorAnimation(hslAnimation.s, this.spawnColor.s, 100);\n          this.spawnColor.l = this.setColorAnimation(hslAnimation.l, this.spawnColor.l, 100);\n        }\n\n        if (!particlesOptions.color) {\n          particlesOptions.color = {\n            value: this.spawnColor\n          };\n        } else {\n          particlesOptions.color.value = this.spawnColor;\n        }\n      }\n\n      if (!position) {\n        return;\n      }\n\n      const pPosition = (_c = (_b = this.shape) === null || _b === void 0 ? void 0 : _b.randomPosition(position, size, this.fill)) !== null && _c !== void 0 ? _c : position;\n      container.particles.addParticle(pPosition, particlesOptions);\n    }\n  }\n\n  setColorAnimation(animation, initValue, maxValue) {\n    var _a;\n\n    const container = this.container;\n\n    if (!animation.enable) {\n      return initValue;\n    }\n\n    const colorOffset = randomInRange(animation.offset);\n    const delay = getRangeValue(this.options.rate.delay);\n    const emitFactor = 1000 * delay / container.retina.reduceFactor;\n    const colorSpeed = getRangeValue((_a = animation.speed) !== null && _a !== void 0 ? _a : 0);\n    return (initValue + colorSpeed * container.fpsLimit / emitFactor + colorOffset * 3.6) % maxValue;\n  }\n\n}\n_EmitterInstance_firstSpawn = new WeakMap(), _EmitterInstance_startParticlesAdded = new WeakMap(), _EmitterInstance_engine = new WeakMap();", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Plugins/Emitters/EmitterInstance.js"], "names": ["__classPrivateFieldSet", "receiver", "state", "value", "kind", "f", "TypeError", "has", "call", "set", "__classPrivateFieldGet", "get", "_EmitterInstance_firstSpawn", "_EmitterInstance_startParticlesAdded", "_EmitterInstance_engine", "colorToHsl", "deepExtend", "getRangeValue", "isPointInside", "randomInRange", "Emitter", "EmitterSize", "EmitterInstance", "constructor", "engine", "emitters", "container", "options", "position", "_a", "_b", "_c", "_d", "_e", "_f", "_g", "_h", "currentDuration", "currentEmitDelay", "currentSpawnDelay", "initialPosition", "load", "spawnDelay", "life", "delay", "retina", "reduceFactor", "calcPosition", "name", "shape", "emitter<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getShape", "fill", "wait", "particlesOptions", "particles", "move", "direction", "spawnColor", "paused", "autoPlay", "size", "height", "mode", "width", "lifeCount", "count", "immortal", "play", "externalPlay", "externalPause", "pause", "emit<PERSON><PERSON><PERSON>", "undefined", "rate", "prepareTo<PERSON>ie", "resize", "canvas", "update", "delta", "emitParticles", "startCount", "duration", "destroy", "emit", "getPosition", "domId", "element", "document", "getElementById", "elRect", "getBoundingClientRect", "x", "pixelRatio", "y", "getSize", "removeEmitter", "percentPosition", "Math", "random", "quantity", "i", "hslAnimation", "animation", "h", "setColorAnimation", "s", "l", "color", "pPosition", "randomPosition", "addParticle", "initValue", "maxValue", "enable", "colorOffset", "offset", "emitFactor", "colorSpeed", "speed", "fpsLimit", "WeakMap"], "mappings": "AAAA,IAAIA,sBAAsB,GAAI,QAAQ,KAAKA,sBAAd,IAAyC,UAAUC,QAAV,EAAoBC,KAApB,EAA2BC,KAA3B,EAAkCC,IAAlC,EAAwCC,CAAxC,EAA2C;AAC7G,MAAID,IAAI,KAAK,GAAb,EAAkB,MAAM,IAAIE,SAAJ,CAAc,gCAAd,CAAN;AAClB,MAAIF,IAAI,KAAK,GAAT,IAAgB,CAACC,CAArB,EAAwB,MAAM,IAAIC,SAAJ,CAAc,+CAAd,CAAN;AACxB,MAAI,OAAOJ,KAAP,KAAiB,UAAjB,GAA8BD,QAAQ,KAAKC,KAAb,IAAsB,CAACG,CAArD,GAAyD,CAACH,KAAK,CAACK,GAAN,CAAUN,QAAV,CAA9D,EAAmF,MAAM,IAAIK,SAAJ,CAAc,yEAAd,CAAN;AACnF,SAAQF,IAAI,KAAK,GAAT,GAAeC,CAAC,CAACG,IAAF,CAAOP,QAAP,EAAiBE,KAAjB,CAAf,GAAyCE,CAAC,GAAGA,CAAC,CAACF,KAAF,GAAUA,KAAb,GAAqBD,KAAK,CAACO,GAAN,CAAUR,QAAV,EAAoBE,KAApB,CAAhE,EAA6FA,KAApG;AACH,CALD;;AAMA,IAAIO,sBAAsB,GAAI,QAAQ,KAAKA,sBAAd,IAAyC,UAAUT,QAAV,EAAoBC,KAApB,EAA2BE,IAA3B,EAAiCC,CAAjC,EAAoC;AACtG,MAAID,IAAI,KAAK,GAAT,IAAgB,CAACC,CAArB,EAAwB,MAAM,IAAIC,SAAJ,CAAc,+CAAd,CAAN;AACxB,MAAI,OAAOJ,KAAP,KAAiB,UAAjB,GAA8BD,QAAQ,KAAKC,KAAb,IAAsB,CAACG,CAArD,GAAyD,CAACH,KAAK,CAACK,GAAN,CAAUN,QAAV,CAA9D,EAAmF,MAAM,IAAIK,SAAJ,CAAc,0EAAd,CAAN;AACnF,SAAOF,IAAI,KAAK,GAAT,GAAeC,CAAf,GAAmBD,IAAI,KAAK,GAAT,GAAeC,CAAC,CAACG,IAAF,CAAOP,QAAP,CAAf,GAAkCI,CAAC,GAAGA,CAAC,CAACF,KAAL,GAAaD,KAAK,CAACS,GAAN,CAAUV,QAAV,CAA1E;AACH,CAJD;;AAKA,IAAIW,2BAAJ,EAAiCC,oCAAjC,EAAuEC,uBAAvE;;AACA,SAASC,UAAT,EAAqBC,UAArB,EAAiCC,aAAjC,EAAgDC,aAAhD,EAA+DC,aAA/D,QAAoF,aAApF;AACA,SAASC,OAAT,QAAwB,2BAAxB;AACA,SAASC,WAAT,QAA4B,+BAA5B;AACA,OAAO,MAAMC,eAAN,CAAsB;AACzBC,EAAAA,WAAW,CAACC,MAAD,EAASC,QAAT,EAAmBC,SAAnB,EAA8BC,OAA9B,EAAuCC,QAAvC,EAAiD;AACxD,QAAIC,EAAJ,EAAQC,EAAR,EAAYC,EAAZ,EAAgBC,EAAhB,EAAoBC,EAApB,EAAwBC,EAAxB,EAA4BC,EAA5B;;AACA,QAAIC,EAAJ;;AACA,SAAKX,QAAL,GAAgBA,QAAhB;AACA,SAAKC,SAAL,GAAiBA,SAAjB;;AACAd,IAAAA,2BAA2B,CAACH,GAA5B,CAAgC,IAAhC,EAAsC,KAAK,CAA3C;;AACAI,IAAAA,oCAAoC,CAACJ,GAArC,CAAyC,IAAzC,EAA+C,KAAK,CAApD;;AACAK,IAAAA,uBAAuB,CAACL,GAAxB,CAA4B,IAA5B,EAAkC,KAAK,CAAvC;;AACAT,IAAAA,sBAAsB,CAAC,IAAD,EAAOc,uBAAP,EAAgCU,MAAhC,EAAwC,GAAxC,CAAtB;;AACA,SAAKa,eAAL,GAAuB,CAAvB;AACA,SAAKC,gBAAL,GAAwB,CAAxB;AACA,SAAKC,iBAAL,GAAyB,CAAzB;AACA,SAAKC,eAAL,GAAuBZ,QAAvB;;AACA,QAAID,OAAO,YAAYP,OAAvB,EAAgC;AAC5B,WAAKO,OAAL,GAAeA,OAAf;AACH,KAFD,MAGK;AACD,WAAKA,OAAL,GAAe,IAAIP,OAAJ,EAAf;AACA,WAAKO,OAAL,CAAac,IAAb,CAAkBd,OAAlB;AACH;;AACD,SAAKe,UAAL,GAAmB,CAAC,CAACb,EAAE,GAAG,KAAKF,OAAL,CAAagB,IAAb,CAAkBC,KAAxB,MAAmC,IAAnC,IAA2Cf,EAAE,KAAK,KAAK,CAAvD,GAA2DA,EAA3D,GAAgE,CAAjE,IAAsE,IAAvE,GAA+E,KAAKH,SAAL,CAAemB,MAAf,CAAsBC,YAAvH;AACA,SAAKlB,QAAL,GAAgB,CAACE,EAAE,GAAG,KAAKU,eAAX,MAAgC,IAAhC,IAAwCV,EAAE,KAAK,KAAK,CAApD,GAAwDA,EAAxD,GAA6D,KAAKiB,YAAL,EAA7E;AACA,SAAKC,IAAL,GAAY,KAAKrB,OAAL,CAAaqB,IAAzB;AACA,SAAKC,KAAL,GAAa,CAAClB,EAAE,GAAGrB,sBAAsB,CAAC,IAAD,EAAOI,uBAAP,EAAgC,GAAhC,CAAtB,CAA2DoC,mBAAjE,MAA0F,IAA1F,IAAkGnB,EAAE,KAAK,KAAK,CAA9G,GAAkH,KAAK,CAAvH,GAA2HA,EAAE,CAACoB,QAAH,CAAY,KAAKxB,OAAL,CAAasB,KAAzB,CAAxI;AACA,SAAKG,IAAL,GAAY,KAAKzB,OAAL,CAAayB,IAAzB;;AACApD,IAAAA,sBAAsB,CAAC,IAAD,EAAOY,2BAAP,EAAoC,CAAC,KAAKe,OAAL,CAAagB,IAAb,CAAkBU,IAAvD,EAA6D,GAA7D,CAAtB;;AACArD,IAAAA,sBAAsB,CAAC,IAAD,EAAOa,oCAAP,EAA6C,KAA7C,EAAoD,GAApD,CAAtB;;AACA,QAAIyC,gBAAgB,GAAGtC,UAAU,CAAC,EAAD,EAAK,KAAKW,OAAL,CAAa4B,SAAlB,CAAjC;AACAD,IAAAA,gBAAgB,KAAK,IAArB,IAA6BA,gBAAgB,KAAK,KAAK,CAAvD,GAA2DA,gBAA3D,GAA+EA,gBAAgB,GAAG,EAAlG;AACA,KAACtB,EAAE,GAAGsB,gBAAgB,CAACE,IAAvB,MAAiC,IAAjC,IAAyCxB,EAAE,KAAK,KAAK,CAArD,GAAyDA,EAAzD,GAA+DsB,gBAAgB,CAACE,IAAjB,GAAwB,EAAvF;AACA,KAACvB,EAAE,GAAG,CAACG,EAAE,GAAGkB,gBAAgB,CAACE,IAAvB,EAA6BC,SAAnC,MAAkD,IAAlD,IAA0DxB,EAAE,KAAK,KAAK,CAAtE,GAA0EA,EAA1E,GAAgFG,EAAE,CAACqB,SAAH,GAAe,KAAK9B,OAAL,CAAa8B,SAA5G;;AACA,QAAI,KAAK9B,OAAL,CAAa+B,UAAjB,EAA6B;AACzB,WAAKA,UAAL,GAAkB3C,UAAU,CAAC,KAAKY,OAAL,CAAa+B,UAAd,CAA5B;AACH;;AACD,SAAKC,MAAL,GAAc,CAAC,KAAKhC,OAAL,CAAaiC,QAA5B;AACA,SAAKN,gBAAL,GAAwBA,gBAAxB;AACA,SAAKO,IAAL,GACI,CAAC3B,EAAE,GAAG,KAAKP,OAAL,CAAakC,IAAnB,MAA6B,IAA7B,IAAqC3B,EAAE,KAAK,KAAK,CAAjD,GAAqDA,EAArD,GAA0D,CAAC,MAAM;AAC7D,YAAM2B,IAAI,GAAG,IAAIxC,WAAJ,EAAb;AACAwC,MAAAA,IAAI,CAACpB,IAAL,CAAU;AACNqB,QAAAA,MAAM,EAAE,CADF;AAENC,QAAAA,IAAI,EAAE,SAFA;AAGNC,QAAAA,KAAK,EAAE;AAHD,OAAV;AAKA,aAAOH,IAAP;AACH,KARyD,GAD9D;AAUA,SAAKI,SAAL,GAAiB,CAAC9B,EAAE,GAAG,KAAKR,OAAL,CAAagB,IAAb,CAAkBuB,KAAxB,MAAmC,IAAnC,IAA2C/B,EAAE,KAAK,KAAK,CAAvD,GAA2DA,EAA3D,GAAgE,CAAC,CAAlF;AACA,SAAKgC,QAAL,GAAgB,KAAKF,SAAL,IAAkB,CAAlC;AACA,SAAKG,IAAL;AACH;;AACDC,EAAAA,YAAY,GAAG;AACX,SAAKV,MAAL,GAAc,KAAd;AACA,SAAKS,IAAL;AACH;;AACDE,EAAAA,aAAa,GAAG;AACZ,SAAKX,MAAL,GAAc,IAAd;AACA,SAAKY,KAAL;AACH;;AACDH,EAAAA,IAAI,GAAG;AACH,QAAIvC,EAAJ;;AACA,QAAI,KAAK8B,MAAT,EAAiB;AACb;AACH;;AACD,QAAI,KAAKjC,SAAL,CAAemB,MAAf,CAAsBC,YAAtB,KACC,KAAKmB,SAAL,GAAiB,CAAjB,IAAsB,KAAKE,QAA3B,IAAuC,CAAC,KAAKxC,OAAL,CAAagB,IAAb,CAAkBuB,KAD3D,MAECxD,sBAAsB,CAAC,IAAD,EAAOE,2BAAP,EAAoC,GAApC,CAAtB,IAAkE,KAAK2B,iBAAL,KAA2B,CAACV,EAAE,GAAG,KAAKa,UAAX,MAA2B,IAA3B,IAAmCb,EAAE,KAAK,KAAK,CAA/C,GAAmDA,EAAnD,GAAwD,CAAnF,CAFnE,CAAJ,EAE+J;AAC3J,UAAI,KAAK2C,SAAL,KAAmBC,SAAvB,EAAkC;AAC9B,cAAM7B,KAAK,GAAG3B,aAAa,CAAC,KAAKU,OAAL,CAAa+C,IAAb,CAAkB9B,KAAnB,CAA3B;AACA,aAAK4B,SAAL,GAAkB,OAAO5B,KAAR,GAAiB,KAAKlB,SAAL,CAAemB,MAAf,CAAsBC,YAAxD;AACH;;AACD,UAAI,KAAKmB,SAAL,GAAiB,CAAjB,IAAsB,KAAKE,QAA/B,EAAyC;AACrC,aAAKQ,YAAL;AACH;AACJ;AACJ;;AACDJ,EAAAA,KAAK,GAAG;AACJ,QAAI,KAAKZ,MAAT,EAAiB;AACb;AACH;;AACD,WAAO,KAAKa,SAAZ;AACH;;AACDI,EAAAA,MAAM,GAAG;AACL,UAAMpC,eAAe,GAAG,KAAKA,eAA7B;AACA,SAAKZ,QAAL,GACIY,eAAe,IAAItB,aAAa,CAACsB,eAAD,EAAkB,KAAKd,SAAL,CAAemD,MAAf,CAAsBhB,IAAxC,CAAhC,GACMrB,eADN,GAEM,KAAKO,YAAL,EAHV;AAIH;;AACD+B,EAAAA,MAAM,CAACC,KAAD,EAAQ;AACV,QAAIlD,EAAJ,EAAQC,EAAR,EAAYC,EAAZ;;AACA,QAAI,KAAK4B,MAAT,EAAiB;AACb;AACH;;AACD,QAAIjD,sBAAsB,CAAC,IAAD,EAAOE,2BAAP,EAAoC,GAApC,CAA1B,EAAoE;AAChEZ,MAAAA,sBAAsB,CAAC,IAAD,EAAOY,2BAAP,EAAoC,KAApC,EAA2C,GAA3C,CAAtB;;AACA,WAAK2B,iBAAL,GAAyB,CAACV,EAAE,GAAG,KAAKa,UAAX,MAA2B,IAA3B,IAAmCb,EAAE,KAAK,KAAK,CAA/C,GAAmDA,EAAnD,GAAwD,CAAjF;AACA,WAAKS,gBAAL,GAAwB,CAACR,EAAE,GAAG,KAAK0C,SAAX,MAA0B,IAA1B,IAAkC1C,EAAE,KAAK,KAAK,CAA9C,GAAkDA,EAAlD,GAAuD,CAA/E;AACH;;AACD,QAAI,CAACpB,sBAAsB,CAAC,IAAD,EAAOG,oCAAP,EAA6C,GAA7C,CAA3B,EAA8E;AAC1Eb,MAAAA,sBAAsB,CAAC,IAAD,EAAOa,oCAAP,EAA6C,IAA7C,EAAmD,GAAnD,CAAtB;;AACA,WAAKmE,aAAL,CAAmB,KAAKrD,OAAL,CAAasD,UAAhC;AACH;;AACD,QAAI,KAAKC,QAAL,KAAkBT,SAAtB,EAAiC;AAC7B,WAAKpC,eAAL,IAAwB0C,KAAK,CAAC5E,KAA9B;;AACA,UAAI,KAAKkC,eAAL,IAAwB,KAAK6C,QAAjC,EAA2C;AACvC,aAAKX,KAAL;;AACA,YAAI,KAAK7B,UAAL,KAAoB+B,SAAxB,EAAmC;AAC/B,iBAAO,KAAK/B,UAAZ;AACH;;AACD,YAAI,CAAC,KAAKyB,QAAV,EAAoB;AAChB,eAAKF,SAAL;AACH;;AACD,YAAI,KAAKA,SAAL,GAAiB,CAAjB,IAAsB,KAAKE,QAA/B,EAAyC;AACrC,eAAKvC,QAAL,GAAgB,KAAKmB,YAAL,EAAhB;AACA,eAAKL,UAAL,GAAmB,CAAC,CAACX,EAAE,GAAG,KAAKJ,OAAL,CAAagB,IAAb,CAAkBC,KAAxB,MAAmC,IAAnC,IAA2Cb,EAAE,KAAK,KAAK,CAAvD,GAA2DA,EAA3D,GAAgE,CAAjE,IAAsE,IAAvE,GAA+E,KAAKL,SAAL,CAAemB,MAAf,CAAsBC,YAAvH;AACH,SAHD,MAIK;AACD,eAAKqC,OAAL;AACH;;AACD,aAAK9C,eAAL,IAAwB,KAAK6C,QAA7B;AACA,eAAO,KAAKA,QAAZ;AACH;AACJ;;AACD,QAAI,KAAKxC,UAAL,KAAoB+B,SAAxB,EAAmC;AAC/B,WAAKlC,iBAAL,IAA0BwC,KAAK,CAAC5E,KAAhC;;AACA,UAAI,KAAKoC,iBAAL,IAA0B,KAAKG,UAAnC,EAA+C;AAC3C,aAAK0B,IAAL;AACA,aAAK7B,iBAAL,IAA0B,KAAKA,iBAA/B;AACA,eAAO,KAAKG,UAAZ;AACH;AACJ;;AACD,QAAI,KAAK8B,SAAL,KAAmBC,SAAvB,EAAkC;AAC9B,WAAKnC,gBAAL,IAAyByC,KAAK,CAAC5E,KAA/B;;AACA,UAAI,KAAKmC,gBAAL,IAAyB,KAAKkC,SAAlC,EAA6C;AACzC,aAAKY,IAAL;AACA,aAAK9C,gBAAL,IAAyB,KAAKkC,SAA9B;AACH;AACJ;AACJ;;AACDa,EAAAA,WAAW,GAAG;AACV,QAAI,KAAK1D,OAAL,CAAa2D,KAAjB,EAAwB;AACpB,YAAM5D,SAAS,GAAG,KAAKA,SAAvB;AAAA,YAAkC6D,OAAO,GAAGC,QAAQ,CAACC,cAAT,CAAwB,KAAK9D,OAAL,CAAa2D,KAArC,CAA5C;;AACA,UAAIC,OAAJ,EAAa;AACT,cAAMG,MAAM,GAAGH,OAAO,CAACI,qBAAR,EAAf;AACA,eAAO;AACHC,UAAAA,CAAC,EAAE,CAACF,MAAM,CAACE,CAAP,GAAWF,MAAM,CAAC1B,KAAP,GAAe,CAA3B,IAAgCtC,SAAS,CAACmB,MAAV,CAAiBgD,UADjD;AAEHC,UAAAA,CAAC,EAAE,CAACJ,MAAM,CAACI,CAAP,GAAWJ,MAAM,CAAC5B,MAAP,GAAgB,CAA5B,IAAiCpC,SAAS,CAACmB,MAAV,CAAiBgD;AAFlD,SAAP;AAIH;AACJ;;AACD,WAAO,KAAKjE,QAAZ;AACH;;AACDmE,EAAAA,OAAO,GAAG;AACN,UAAMrE,SAAS,GAAG,KAAKA,SAAvB;;AACA,QAAI,KAAKC,OAAL,CAAa2D,KAAjB,EAAwB;AACpB,YAAMC,OAAO,GAAGC,QAAQ,CAACC,cAAT,CAAwB,KAAK9D,OAAL,CAAa2D,KAArC,CAAhB;;AACA,UAAIC,OAAJ,EAAa;AACT,cAAMG,MAAM,GAAGH,OAAO,CAACI,qBAAR,EAAf;AACA,eAAO;AACH3B,UAAAA,KAAK,EAAE0B,MAAM,CAAC1B,KAAP,GAAetC,SAAS,CAACmB,MAAV,CAAiBgD,UADpC;AAEH/B,UAAAA,MAAM,EAAE4B,MAAM,CAAC5B,MAAP,GAAgBpC,SAAS,CAACmB,MAAV,CAAiBgD;AAFtC,SAAP;AAIH;AACJ;;AACD,WAAO;AACH7B,MAAAA,KAAK,EAAE,KAAKH,IAAL,CAAUE,IAAV,KAAmB,SAAnB,GACArC,SAAS,CAACmD,MAAV,CAAiBhB,IAAjB,CAAsBG,KAAtB,GAA8B,KAAKH,IAAL,CAAUG,KAAzC,GAAkD,GADjD,GAED,KAAKH,IAAL,CAAUG,KAHb;AAIHF,MAAAA,MAAM,EAAE,KAAKD,IAAL,CAAUE,IAAV,KAAmB,SAAnB,GACDrC,SAAS,CAACmD,MAAV,CAAiBhB,IAAjB,CAAsBC,MAAtB,GAA+B,KAAKD,IAAL,CAAUC,MAA1C,GAAoD,GADlD,GAEF,KAAKD,IAAL,CAAUC;AANb,KAAP;AAQH;;AACDa,EAAAA,YAAY,GAAG;AACX,QAAI9C,EAAJ;;AACA,QAAI,KAAK8B,MAAT,EAAiB;AACb;AACH;;AACD,UAAMuB,QAAQ,GAAG,CAACrD,EAAE,GAAG,KAAKF,OAAL,CAAagB,IAAnB,MAA6B,IAA7B,IAAqCd,EAAE,KAAK,KAAK,CAAjD,GAAqD,KAAK,CAA1D,GAA8DA,EAAE,CAACqD,QAAlF;;AACA,QAAI,KAAKxD,SAAL,CAAemB,MAAf,CAAsBC,YAAtB,KACC,KAAKmB,SAAL,GAAiB,CAAjB,IAAsB,KAAKE,QAD5B,KAEAe,QAAQ,KAAKT,SAFb,IAGAS,QAAQ,GAAG,CAHf,EAGkB;AACd,WAAKA,QAAL,GAAgBA,QAAQ,GAAG,IAA3B;AACH;AACJ;;AACDC,EAAAA,OAAO,GAAG;AACN,SAAK1D,QAAL,CAAcuE,aAAd,CAA4B,IAA5B;AACH;;AACDjD,EAAAA,YAAY,GAAG;AACX,QAAIlB,EAAJ,EAAQC,EAAR;;AACA,UAAMJ,SAAS,GAAG,KAAKA,SAAvB;AACA,UAAMuE,eAAe,GAAG,KAAKtE,OAAL,CAAaC,QAArC;AACA,WAAO;AACHgE,MAAAA,CAAC,EAAG3E,aAAa,CAAC,CAACY,EAAE,GAAGoE,eAAe,KAAK,IAApB,IAA4BA,eAAe,KAAK,KAAK,CAArD,GAAyD,KAAK,CAA9D,GAAkEA,eAAe,CAACL,CAAxF,MAA+F,IAA/F,IAAuG/D,EAAE,KAAK,KAAK,CAAnH,GAAuHA,EAAvH,GAA4HqE,IAAI,CAACC,MAAL,KAAgB,GAA7I,CAAb,GAAiK,GAAlK,GAAyKzE,SAAS,CAACmD,MAAV,CAAiBhB,IAAjB,CAAsBG,KAD/L;AAEH8B,MAAAA,CAAC,EAAG7E,aAAa,CAAC,CAACa,EAAE,GAAGmE,eAAe,KAAK,IAApB,IAA4BA,eAAe,KAAK,KAAK,CAArD,GAAyD,KAAK,CAA9D,GAAkEA,eAAe,CAACH,CAAxF,MAA+F,IAA/F,IAAuGhE,EAAE,KAAK,KAAK,CAAnH,GAAuHA,EAAvH,GAA4HoE,IAAI,CAACC,MAAL,KAAgB,GAA7I,CAAb,GAAiK,GAAlK,GAAyKzE,SAAS,CAACmD,MAAV,CAAiBhB,IAAjB,CAAsBC;AAF/L,KAAP;AAIH;;AACDsB,EAAAA,IAAI,GAAG;AACH,QAAI,KAAKzB,MAAT,EAAiB;AACb;AACH;;AACD,UAAMyC,QAAQ,GAAGnF,aAAa,CAAC,KAAKU,OAAL,CAAa+C,IAAb,CAAkB0B,QAAnB,CAA9B;AACA,SAAKpB,aAAL,CAAmBoB,QAAnB;AACH;;AACDpB,EAAAA,aAAa,CAACoB,QAAD,EAAW;AACpB,QAAIvE,EAAJ,EAAQC,EAAR,EAAYC,EAAZ;;AACA,UAAML,SAAS,GAAG,KAAKA,SAAvB;AACA,UAAME,QAAQ,GAAG,KAAKyD,WAAL,EAAjB;AACA,UAAMxB,IAAI,GAAG,KAAKkC,OAAL,EAAb;;AACA,SAAK,IAAIM,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGD,QAApB,EAA8BC,CAAC,EAA/B,EAAmC;AAC/B,YAAM/C,gBAAgB,GAAGtC,UAAU,CAAC,EAAD,EAAK,KAAKsC,gBAAV,CAAnC;;AACA,UAAI,KAAKI,UAAT,EAAqB;AACjB,cAAM4C,YAAY,GAAG,CAACzE,EAAE,GAAG,KAAKF,OAAL,CAAa+B,UAAnB,MAAmC,IAAnC,IAA2C7B,EAAE,KAAK,KAAK,CAAvD,GAA2D,KAAK,CAAhE,GAAoEA,EAAE,CAAC0E,SAA5F;;AACA,YAAID,YAAJ,EAAkB;AACd,eAAK5C,UAAL,CAAgB8C,CAAhB,GAAoB,KAAKC,iBAAL,CAAuBH,YAAY,CAACE,CAApC,EAAuC,KAAK9C,UAAL,CAAgB8C,CAAvD,EAA0D,GAA1D,CAApB;AACA,eAAK9C,UAAL,CAAgBgD,CAAhB,GAAoB,KAAKD,iBAAL,CAAuBH,YAAY,CAACI,CAApC,EAAuC,KAAKhD,UAAL,CAAgBgD,CAAvD,EAA0D,GAA1D,CAApB;AACA,eAAKhD,UAAL,CAAgBiD,CAAhB,GAAoB,KAAKF,iBAAL,CAAuBH,YAAY,CAACK,CAApC,EAAuC,KAAKjD,UAAL,CAAgBiD,CAAvD,EAA0D,GAA1D,CAApB;AACH;;AACD,YAAI,CAACrD,gBAAgB,CAACsD,KAAtB,EAA6B;AACzBtD,UAAAA,gBAAgB,CAACsD,KAAjB,GAAyB;AACrBzG,YAAAA,KAAK,EAAE,KAAKuD;AADS,WAAzB;AAGH,SAJD,MAKK;AACDJ,UAAAA,gBAAgB,CAACsD,KAAjB,CAAuBzG,KAAvB,GAA+B,KAAKuD,UAApC;AACH;AACJ;;AACD,UAAI,CAAC9B,QAAL,EAAe;AACX;AACH;;AACD,YAAMiF,SAAS,GAAG,CAAC9E,EAAE,GAAG,CAACD,EAAE,GAAG,KAAKmB,KAAX,MAAsB,IAAtB,IAA8BnB,EAAE,KAAK,KAAK,CAA1C,GAA8C,KAAK,CAAnD,GAAuDA,EAAE,CAACgF,cAAH,CAAkBlF,QAAlB,EAA4BiC,IAA5B,EAAkC,KAAKT,IAAvC,CAA7D,MAA+G,IAA/G,IAAuHrB,EAAE,KAAK,KAAK,CAAnI,GAAuIA,EAAvI,GAA4IH,QAA9J;AACAF,MAAAA,SAAS,CAAC6B,SAAV,CAAoBwD,WAApB,CAAgCF,SAAhC,EAA2CvD,gBAA3C;AACH;AACJ;;AACDmD,EAAAA,iBAAiB,CAACF,SAAD,EAAYS,SAAZ,EAAuBC,QAAvB,EAAiC;AAC9C,QAAIpF,EAAJ;;AACA,UAAMH,SAAS,GAAG,KAAKA,SAAvB;;AACA,QAAI,CAAC6E,SAAS,CAACW,MAAf,EAAuB;AACnB,aAAOF,SAAP;AACH;;AACD,UAAMG,WAAW,GAAGhG,aAAa,CAACoF,SAAS,CAACa,MAAX,CAAjC;AACA,UAAMxE,KAAK,GAAG3B,aAAa,CAAC,KAAKU,OAAL,CAAa+C,IAAb,CAAkB9B,KAAnB,CAA3B;AACA,UAAMyE,UAAU,GAAI,OAAOzE,KAAR,GAAiBlB,SAAS,CAACmB,MAAV,CAAiBC,YAArD;AACA,UAAMwE,UAAU,GAAGrG,aAAa,CAAC,CAACY,EAAE,GAAG0E,SAAS,CAACgB,KAAhB,MAA2B,IAA3B,IAAmC1F,EAAE,KAAK,KAAK,CAA/C,GAAmDA,EAAnD,GAAwD,CAAzD,CAAhC;AACA,WAAO,CAACmF,SAAS,GAAIM,UAAU,GAAG5F,SAAS,CAAC8F,QAAxB,GAAoCH,UAAhD,GAA6DF,WAAW,GAAG,GAA5E,IAAmFF,QAA1F;AACH;;AAvPwB;AAyP7BrG,2BAA2B,GAAG,IAAI6G,OAAJ,EAA9B,EAA6C5G,oCAAoC,GAAG,IAAI4G,OAAJ,EAApF,EAAmG3G,uBAAuB,GAAG,IAAI2G,OAAJ,EAA7H", "sourcesContent": ["var __classPrivateFieldSet = (this && this.__classPrivateFieldSet) || function (receiver, state, value, kind, f) {\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\n};\nvar __classPrivateFieldGet = (this && this.__classPrivateFieldGet) || function (receiver, state, kind, f) {\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n};\nvar _EmitterInstance_firstSpawn, _EmitterInstance_startParticlesAdded, _EmitterInstance_engine;\nimport { colorToHsl, deepExtend, getRangeValue, isPointInside, randomInRange } from \"../../Utils\";\nimport { Emitter } from \"./Options/Classes/Emitter\";\nimport { EmitterSize } from \"./Options/Classes/EmitterSize\";\nexport class EmitterInstance {\n    constructor(engine, emitters, container, options, position) {\n        var _a, _b, _c, _d, _e, _f, _g;\n        var _h;\n        this.emitters = emitters;\n        this.container = container;\n        _EmitterInstance_firstSpawn.set(this, void 0);\n        _EmitterInstance_startParticlesAdded.set(this, void 0);\n        _EmitterInstance_engine.set(this, void 0);\n        __classPrivateFieldSet(this, _EmitterInstance_engine, engine, \"f\");\n        this.currentDuration = 0;\n        this.currentEmitDelay = 0;\n        this.currentSpawnDelay = 0;\n        this.initialPosition = position;\n        if (options instanceof Emitter) {\n            this.options = options;\n        }\n        else {\n            this.options = new Emitter();\n            this.options.load(options);\n        }\n        this.spawnDelay = (((_a = this.options.life.delay) !== null && _a !== void 0 ? _a : 0) * 1000) / this.container.retina.reduceFactor;\n        this.position = (_b = this.initialPosition) !== null && _b !== void 0 ? _b : this.calcPosition();\n        this.name = this.options.name;\n        this.shape = (_c = __classPrivateFieldGet(this, _EmitterInstance_engine, \"f\").emitterShapeManager) === null || _c === void 0 ? void 0 : _c.getShape(this.options.shape);\n        this.fill = this.options.fill;\n        __classPrivateFieldSet(this, _EmitterInstance_firstSpawn, !this.options.life.wait, \"f\");\n        __classPrivateFieldSet(this, _EmitterInstance_startParticlesAdded, false, \"f\");\n        let particlesOptions = deepExtend({}, this.options.particles);\n        particlesOptions !== null && particlesOptions !== void 0 ? particlesOptions : (particlesOptions = {});\n        (_d = particlesOptions.move) !== null && _d !== void 0 ? _d : (particlesOptions.move = {});\n        (_e = (_h = particlesOptions.move).direction) !== null && _e !== void 0 ? _e : (_h.direction = this.options.direction);\n        if (this.options.spawnColor) {\n            this.spawnColor = colorToHsl(this.options.spawnColor);\n        }\n        this.paused = !this.options.autoPlay;\n        this.particlesOptions = particlesOptions;\n        this.size =\n            (_f = this.options.size) !== null && _f !== void 0 ? _f : (() => {\n                const size = new EmitterSize();\n                size.load({\n                    height: 0,\n                    mode: \"percent\",\n                    width: 0,\n                });\n                return size;\n            })();\n        this.lifeCount = (_g = this.options.life.count) !== null && _g !== void 0 ? _g : -1;\n        this.immortal = this.lifeCount <= 0;\n        this.play();\n    }\n    externalPlay() {\n        this.paused = false;\n        this.play();\n    }\n    externalPause() {\n        this.paused = true;\n        this.pause();\n    }\n    play() {\n        var _a;\n        if (this.paused) {\n            return;\n        }\n        if (this.container.retina.reduceFactor &&\n            (this.lifeCount > 0 || this.immortal || !this.options.life.count) &&\n            (__classPrivateFieldGet(this, _EmitterInstance_firstSpawn, \"f\") || this.currentSpawnDelay >= ((_a = this.spawnDelay) !== null && _a !== void 0 ? _a : 0))) {\n            if (this.emitDelay === undefined) {\n                const delay = getRangeValue(this.options.rate.delay);\n                this.emitDelay = (1000 * delay) / this.container.retina.reduceFactor;\n            }\n            if (this.lifeCount > 0 || this.immortal) {\n                this.prepareToDie();\n            }\n        }\n    }\n    pause() {\n        if (this.paused) {\n            return;\n        }\n        delete this.emitDelay;\n    }\n    resize() {\n        const initialPosition = this.initialPosition;\n        this.position =\n            initialPosition && isPointInside(initialPosition, this.container.canvas.size)\n                ? initialPosition\n                : this.calcPosition();\n    }\n    update(delta) {\n        var _a, _b, _c;\n        if (this.paused) {\n            return;\n        }\n        if (__classPrivateFieldGet(this, _EmitterInstance_firstSpawn, \"f\")) {\n            __classPrivateFieldSet(this, _EmitterInstance_firstSpawn, false, \"f\");\n            this.currentSpawnDelay = (_a = this.spawnDelay) !== null && _a !== void 0 ? _a : 0;\n            this.currentEmitDelay = (_b = this.emitDelay) !== null && _b !== void 0 ? _b : 0;\n        }\n        if (!__classPrivateFieldGet(this, _EmitterInstance_startParticlesAdded, \"f\")) {\n            __classPrivateFieldSet(this, _EmitterInstance_startParticlesAdded, true, \"f\");\n            this.emitParticles(this.options.startCount);\n        }\n        if (this.duration !== undefined) {\n            this.currentDuration += delta.value;\n            if (this.currentDuration >= this.duration) {\n                this.pause();\n                if (this.spawnDelay !== undefined) {\n                    delete this.spawnDelay;\n                }\n                if (!this.immortal) {\n                    this.lifeCount--;\n                }\n                if (this.lifeCount > 0 || this.immortal) {\n                    this.position = this.calcPosition();\n                    this.spawnDelay = (((_c = this.options.life.delay) !== null && _c !== void 0 ? _c : 0) * 1000) / this.container.retina.reduceFactor;\n                }\n                else {\n                    this.destroy();\n                }\n                this.currentDuration -= this.duration;\n                delete this.duration;\n            }\n        }\n        if (this.spawnDelay !== undefined) {\n            this.currentSpawnDelay += delta.value;\n            if (this.currentSpawnDelay >= this.spawnDelay) {\n                this.play();\n                this.currentSpawnDelay -= this.currentSpawnDelay;\n                delete this.spawnDelay;\n            }\n        }\n        if (this.emitDelay !== undefined) {\n            this.currentEmitDelay += delta.value;\n            if (this.currentEmitDelay >= this.emitDelay) {\n                this.emit();\n                this.currentEmitDelay -= this.emitDelay;\n            }\n        }\n    }\n    getPosition() {\n        if (this.options.domId) {\n            const container = this.container, element = document.getElementById(this.options.domId);\n            if (element) {\n                const elRect = element.getBoundingClientRect();\n                return {\n                    x: (elRect.x + elRect.width / 2) * container.retina.pixelRatio,\n                    y: (elRect.y + elRect.height / 2) * container.retina.pixelRatio,\n                };\n            }\n        }\n        return this.position;\n    }\n    getSize() {\n        const container = this.container;\n        if (this.options.domId) {\n            const element = document.getElementById(this.options.domId);\n            if (element) {\n                const elRect = element.getBoundingClientRect();\n                return {\n                    width: elRect.width * container.retina.pixelRatio,\n                    height: elRect.height * container.retina.pixelRatio,\n                };\n            }\n        }\n        return {\n            width: this.size.mode === \"percent\"\n                ? (container.canvas.size.width * this.size.width) / 100\n                : this.size.width,\n            height: this.size.mode === \"percent\"\n                ? (container.canvas.size.height * this.size.height) / 100\n                : this.size.height,\n        };\n    }\n    prepareToDie() {\n        var _a;\n        if (this.paused) {\n            return;\n        }\n        const duration = (_a = this.options.life) === null || _a === void 0 ? void 0 : _a.duration;\n        if (this.container.retina.reduceFactor &&\n            (this.lifeCount > 0 || this.immortal) &&\n            duration !== undefined &&\n            duration > 0) {\n            this.duration = duration * 1000;\n        }\n    }\n    destroy() {\n        this.emitters.removeEmitter(this);\n    }\n    calcPosition() {\n        var _a, _b;\n        const container = this.container;\n        const percentPosition = this.options.position;\n        return {\n            x: (getRangeValue((_a = percentPosition === null || percentPosition === void 0 ? void 0 : percentPosition.x) !== null && _a !== void 0 ? _a : Math.random() * 100) / 100) * container.canvas.size.width,\n            y: (getRangeValue((_b = percentPosition === null || percentPosition === void 0 ? void 0 : percentPosition.y) !== null && _b !== void 0 ? _b : Math.random() * 100) / 100) * container.canvas.size.height,\n        };\n    }\n    emit() {\n        if (this.paused) {\n            return;\n        }\n        const quantity = getRangeValue(this.options.rate.quantity);\n        this.emitParticles(quantity);\n    }\n    emitParticles(quantity) {\n        var _a, _b, _c;\n        const container = this.container;\n        const position = this.getPosition();\n        const size = this.getSize();\n        for (let i = 0; i < quantity; i++) {\n            const particlesOptions = deepExtend({}, this.particlesOptions);\n            if (this.spawnColor) {\n                const hslAnimation = (_a = this.options.spawnColor) === null || _a === void 0 ? void 0 : _a.animation;\n                if (hslAnimation) {\n                    this.spawnColor.h = this.setColorAnimation(hslAnimation.h, this.spawnColor.h, 360);\n                    this.spawnColor.s = this.setColorAnimation(hslAnimation.s, this.spawnColor.s, 100);\n                    this.spawnColor.l = this.setColorAnimation(hslAnimation.l, this.spawnColor.l, 100);\n                }\n                if (!particlesOptions.color) {\n                    particlesOptions.color = {\n                        value: this.spawnColor,\n                    };\n                }\n                else {\n                    particlesOptions.color.value = this.spawnColor;\n                }\n            }\n            if (!position) {\n                return;\n            }\n            const pPosition = (_c = (_b = this.shape) === null || _b === void 0 ? void 0 : _b.randomPosition(position, size, this.fill)) !== null && _c !== void 0 ? _c : position;\n            container.particles.addParticle(pPosition, particlesOptions);\n        }\n    }\n    setColorAnimation(animation, initValue, maxValue) {\n        var _a;\n        const container = this.container;\n        if (!animation.enable) {\n            return initValue;\n        }\n        const colorOffset = randomInRange(animation.offset);\n        const delay = getRangeValue(this.options.rate.delay);\n        const emitFactor = (1000 * delay) / container.retina.reduceFactor;\n        const colorSpeed = getRangeValue((_a = animation.speed) !== null && _a !== void 0 ? _a : 0);\n        return (initValue + (colorSpeed * container.fpsLimit) / emitFactor + colorOffset * 3.6) % maxValue;\n    }\n}\n_EmitterInstance_firstSpawn = new WeakMap(), _EmitterInstance_startParticlesAdded = new WeakMap(), _EmitterInstance_engine = new WeakMap();\n"]}, "metadata": {}, "sourceType": "module"}
{"ast": null, "code": "import { LineDrawer } from \"./LineDrawer.js\";\nexport async function loadLineShape(engine, refresh = true) {\n  await engine.addShape(new LineDrawer(), refresh);\n}", "map": {"version": 3, "names": ["LineDrawer", "loadLineShape", "engine", "refresh", "addShape"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/shape-line/browser/index.js"], "sourcesContent": ["import { LineDrawer } from \"./LineDrawer.js\";\nexport async function loadLineShape(engine, refresh = true) {\n    await engine.addShape(new LineDrawer(), refresh);\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,iBAAiB;AAC5C,OAAO,eAAeC,aAAaA,CAACC,MAAM,EAAEC,OAAO,GAAG,IAAI,EAAE;EACxD,MAAMD,MAAM,CAACE,QAAQ,CAAC,IAAIJ,UAAU,CAAC,CAAC,EAAEG,OAAO,CAAC;AACpD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { ExternalInteractorBase, getDistance, getLinkColor, getLinkRandomColor, isInArray, mouseMoveEvent } from \"@tsparticles/engine\";\nimport { Grab } from \"./Options/Classes/Grab.js\";\nimport { drawGrab } from \"./Utils.js\";\nconst grabMode = \"grab\",\n  minDistance = 0,\n  minOpacity = 0;\nexport class <PERSON>rabber extends ExternalInteractorBase {\n  constructor(container) {\n    super(container);\n  }\n  clear() {}\n  init() {\n    const container = this.container,\n      grab = container.actualOptions.interactivity.modes.grab;\n    if (!grab) {\n      return;\n    }\n    container.retina.grabModeDistance = grab.distance * container.retina.pixelRatio;\n  }\n  interact() {\n    const container = this.container,\n      options = container.actualOptions,\n      interactivity = options.interactivity;\n    if (!interactivity.modes.grab || !interactivity.events.onHover.enable || container.interactivity.status !== mouseMoveEvent) {\n      return;\n    }\n    const mousePos = container.interactivity.mouse.position;\n    if (!mousePos) {\n      return;\n    }\n    const distance = container.retina.grabModeDistance;\n    if (!distance || distance < minDistance) {\n      return;\n    }\n    const query = container.particles.quadTree.queryCircle(mousePos, distance, p => this.isEnabled(p));\n    for (const particle of query) {\n      const pos = particle.getPosition(),\n        pointDistance = getDistance(pos, mousePos);\n      if (pointDistance > distance) {\n        continue;\n      }\n      const grabLineOptions = interactivity.modes.grab.links,\n        lineOpacity = grabLineOptions.opacity,\n        opacityLine = lineOpacity - pointDistance * lineOpacity / distance;\n      if (opacityLine <= minOpacity) {\n        continue;\n      }\n      const optColor = grabLineOptions.color ?? particle.options.links?.color;\n      if (!container.particles.grabLineColor && optColor) {\n        const linksOptions = interactivity.modes.grab.links;\n        container.particles.grabLineColor = getLinkRandomColor(optColor, linksOptions.blink, linksOptions.consent);\n      }\n      const colorLine = getLinkColor(particle, undefined, container.particles.grabLineColor);\n      if (!colorLine) {\n        continue;\n      }\n      drawGrab(container, particle, colorLine, opacityLine, mousePos);\n    }\n  }\n  isEnabled(particle) {\n    const container = this.container,\n      mouse = container.interactivity.mouse,\n      events = (particle?.interactivity ?? container.actualOptions.interactivity).events;\n    return events.onHover.enable && !!mouse.position && isInArray(grabMode, events.onHover.mode);\n  }\n  loadModeOptions(options, ...sources) {\n    if (!options.grab) {\n      options.grab = new Grab();\n    }\n    for (const source of sources) {\n      options.grab.load(source?.grab);\n    }\n  }\n  reset() {}\n}", "map": {"version": 3, "names": ["ExternalInteractorBase", "getDistance", "getLinkColor", "getLinkRandomColor", "isInArray", "mouseMoveEvent", "<PERSON>rab", "drawGrab", "grabMode", "minDistance", "minOpacity", "<PERSON><PERSON><PERSON>", "constructor", "container", "clear", "init", "grab", "actualOptions", "interactivity", "modes", "retina", "grabModeDistance", "distance", "pixelRatio", "interact", "options", "events", "onHover", "enable", "status", "mousePos", "mouse", "position", "query", "particles", "quadTree", "queryCircle", "p", "isEnabled", "particle", "pos", "getPosition", "pointDistance", "grabLineOptions", "links", "lineOpacity", "opacity", "opacityLine", "optColor", "color", "grabLineColor", "linksOptions", "blink", "consent", "colorLine", "undefined", "mode", "loadModeOptions", "sources", "source", "load", "reset"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/interaction-external-grab/browser/Grabber.js"], "sourcesContent": ["import { ExternalInteractorBase, getDistance, getLinkColor, getLinkRandomColor, isInArray, mouseMoveEvent, } from \"@tsparticles/engine\";\nimport { Grab } from \"./Options/Classes/Grab.js\";\nimport { drawGrab } from \"./Utils.js\";\nconst grabMode = \"grab\", minDistance = 0, minOpacity = 0;\nexport class <PERSON><PERSON><PERSON> extends ExternalInteractorBase {\n    constructor(container) {\n        super(container);\n    }\n    clear() {\n    }\n    init() {\n        const container = this.container, grab = container.actualOptions.interactivity.modes.grab;\n        if (!grab) {\n            return;\n        }\n        container.retina.grabModeDistance = grab.distance * container.retina.pixelRatio;\n    }\n    interact() {\n        const container = this.container, options = container.actualOptions, interactivity = options.interactivity;\n        if (!interactivity.modes.grab ||\n            !interactivity.events.onHover.enable ||\n            container.interactivity.status !== mouseMoveEvent) {\n            return;\n        }\n        const mousePos = container.interactivity.mouse.position;\n        if (!mousePos) {\n            return;\n        }\n        const distance = container.retina.grabModeDistance;\n        if (!distance || distance < minDistance) {\n            return;\n        }\n        const query = container.particles.quadTree.queryCircle(mousePos, distance, p => this.isEnabled(p));\n        for (const particle of query) {\n            const pos = particle.getPosition(), pointDistance = getDistance(pos, mousePos);\n            if (pointDistance > distance) {\n                continue;\n            }\n            const grabLineOptions = interactivity.modes.grab.links, lineOpacity = grabLineOptions.opacity, opacityLine = lineOpacity - (pointDistance * lineOpacity) / distance;\n            if (opacityLine <= minOpacity) {\n                continue;\n            }\n            const optColor = grabLineOptions.color ?? particle.options.links?.color;\n            if (!container.particles.grabLineColor && optColor) {\n                const linksOptions = interactivity.modes.grab.links;\n                container.particles.grabLineColor = getLinkRandomColor(optColor, linksOptions.blink, linksOptions.consent);\n            }\n            const colorLine = getLinkColor(particle, undefined, container.particles.grabLineColor);\n            if (!colorLine) {\n                continue;\n            }\n            drawGrab(container, particle, colorLine, opacityLine, mousePos);\n        }\n    }\n    isEnabled(particle) {\n        const container = this.container, mouse = container.interactivity.mouse, events = (particle?.interactivity ?? container.actualOptions.interactivity).events;\n        return events.onHover.enable && !!mouse.position && isInArray(grabMode, events.onHover.mode);\n    }\n    loadModeOptions(options, ...sources) {\n        if (!options.grab) {\n            options.grab = new Grab();\n        }\n        for (const source of sources) {\n            options.grab.load(source?.grab);\n        }\n    }\n    reset() {\n    }\n}\n"], "mappings": "AAAA,SAASA,sBAAsB,EAAEC,WAAW,EAAEC,YAAY,EAAEC,kBAAkB,EAAEC,SAAS,EAAEC,cAAc,QAAS,qBAAqB;AACvI,SAASC,IAAI,QAAQ,2BAA2B;AAChD,SAASC,QAAQ,QAAQ,YAAY;AACrC,MAAMC,QAAQ,GAAG,MAAM;EAAEC,WAAW,GAAG,CAAC;EAAEC,UAAU,GAAG,CAAC;AACxD,OAAO,MAAMC,OAAO,SAASX,sBAAsB,CAAC;EAChDY,WAAWA,CAACC,SAAS,EAAE;IACnB,KAAK,CAACA,SAAS,CAAC;EACpB;EACAC,KAAKA,CAAA,EAAG,CACR;EACAC,IAAIA,CAAA,EAAG;IACH,MAAMF,SAAS,GAAG,IAAI,CAACA,SAAS;MAAEG,IAAI,GAAGH,SAAS,CAACI,aAAa,CAACC,aAAa,CAACC,KAAK,CAACH,IAAI;IACzF,IAAI,CAACA,IAAI,EAAE;MACP;IACJ;IACAH,SAAS,CAACO,MAAM,CAACC,gBAAgB,GAAGL,IAAI,CAACM,QAAQ,GAAGT,SAAS,CAACO,MAAM,CAACG,UAAU;EACnF;EACAC,QAAQA,CAAA,EAAG;IACP,MAAMX,SAAS,GAAG,IAAI,CAACA,SAAS;MAAEY,OAAO,GAAGZ,SAAS,CAACI,aAAa;MAAEC,aAAa,GAAGO,OAAO,CAACP,aAAa;IAC1G,IAAI,CAACA,aAAa,CAACC,KAAK,CAACH,IAAI,IACzB,CAACE,aAAa,CAACQ,MAAM,CAACC,OAAO,CAACC,MAAM,IACpCf,SAAS,CAACK,aAAa,CAACW,MAAM,KAAKxB,cAAc,EAAE;MACnD;IACJ;IACA,MAAMyB,QAAQ,GAAGjB,SAAS,CAACK,aAAa,CAACa,KAAK,CAACC,QAAQ;IACvD,IAAI,CAACF,QAAQ,EAAE;MACX;IACJ;IACA,MAAMR,QAAQ,GAAGT,SAAS,CAACO,MAAM,CAACC,gBAAgB;IAClD,IAAI,CAACC,QAAQ,IAAIA,QAAQ,GAAGb,WAAW,EAAE;MACrC;IACJ;IACA,MAAMwB,KAAK,GAAGpB,SAAS,CAACqB,SAAS,CAACC,QAAQ,CAACC,WAAW,CAACN,QAAQ,EAAER,QAAQ,EAAEe,CAAC,IAAI,IAAI,CAACC,SAAS,CAACD,CAAC,CAAC,CAAC;IAClG,KAAK,MAAME,QAAQ,IAAIN,KAAK,EAAE;MAC1B,MAAMO,GAAG,GAAGD,QAAQ,CAACE,WAAW,CAAC,CAAC;QAAEC,aAAa,GAAGzC,WAAW,CAACuC,GAAG,EAAEV,QAAQ,CAAC;MAC9E,IAAIY,aAAa,GAAGpB,QAAQ,EAAE;QAC1B;MACJ;MACA,MAAMqB,eAAe,GAAGzB,aAAa,CAACC,KAAK,CAACH,IAAI,CAAC4B,KAAK;QAAEC,WAAW,GAAGF,eAAe,CAACG,OAAO;QAAEC,WAAW,GAAGF,WAAW,GAAIH,aAAa,GAAGG,WAAW,GAAIvB,QAAQ;MACnK,IAAIyB,WAAW,IAAIrC,UAAU,EAAE;QAC3B;MACJ;MACA,MAAMsC,QAAQ,GAAGL,eAAe,CAACM,KAAK,IAAIV,QAAQ,CAACd,OAAO,CAACmB,KAAK,EAAEK,KAAK;MACvE,IAAI,CAACpC,SAAS,CAACqB,SAAS,CAACgB,aAAa,IAAIF,QAAQ,EAAE;QAChD,MAAMG,YAAY,GAAGjC,aAAa,CAACC,KAAK,CAACH,IAAI,CAAC4B,KAAK;QACnD/B,SAAS,CAACqB,SAAS,CAACgB,aAAa,GAAG/C,kBAAkB,CAAC6C,QAAQ,EAAEG,YAAY,CAACC,KAAK,EAAED,YAAY,CAACE,OAAO,CAAC;MAC9G;MACA,MAAMC,SAAS,GAAGpD,YAAY,CAACqC,QAAQ,EAAEgB,SAAS,EAAE1C,SAAS,CAACqB,SAAS,CAACgB,aAAa,CAAC;MACtF,IAAI,CAACI,SAAS,EAAE;QACZ;MACJ;MACA/C,QAAQ,CAACM,SAAS,EAAE0B,QAAQ,EAAEe,SAAS,EAAEP,WAAW,EAAEjB,QAAQ,CAAC;IACnE;EACJ;EACAQ,SAASA,CAACC,QAAQ,EAAE;IAChB,MAAM1B,SAAS,GAAG,IAAI,CAACA,SAAS;MAAEkB,KAAK,GAAGlB,SAAS,CAACK,aAAa,CAACa,KAAK;MAAEL,MAAM,GAAG,CAACa,QAAQ,EAAErB,aAAa,IAAIL,SAAS,CAACI,aAAa,CAACC,aAAa,EAAEQ,MAAM;IAC3J,OAAOA,MAAM,CAACC,OAAO,CAACC,MAAM,IAAI,CAAC,CAACG,KAAK,CAACC,QAAQ,IAAI5B,SAAS,CAACI,QAAQ,EAAEkB,MAAM,CAACC,OAAO,CAAC6B,IAAI,CAAC;EAChG;EACAC,eAAeA,CAAChC,OAAO,EAAE,GAAGiC,OAAO,EAAE;IACjC,IAAI,CAACjC,OAAO,CAACT,IAAI,EAAE;MACfS,OAAO,CAACT,IAAI,GAAG,IAAIV,IAAI,CAAC,CAAC;IAC7B;IACA,KAAK,MAAMqD,MAAM,IAAID,OAAO,EAAE;MAC1BjC,OAAO,CAACT,IAAI,CAAC4C,IAAI,CAACD,MAAM,EAAE3C,IAAI,CAAC;IACnC;EACJ;EACA6C,KAAKA,CAAA,EAAG,CACR;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
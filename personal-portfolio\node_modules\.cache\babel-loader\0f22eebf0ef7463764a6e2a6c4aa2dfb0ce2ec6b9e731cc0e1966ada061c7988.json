{"ast": null, "code": "export var ProcessBubbleType;\n(function (ProcessBubbleType) {\n  ProcessBubbleType[\"color\"] = \"color\";\n  ProcessBubbleType[\"opacity\"] = \"opacity\";\n  ProcessBubbleType[\"size\"] = \"size\";\n})(ProcessBubbleType || (ProcessBubbleType = {}));", "map": {"version": 3, "names": ["ProcessBubbleType"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/interaction-external-bubble/browser/Enums.js"], "sourcesContent": ["export var ProcessBubbleType;\n(function (ProcessBubbleType) {\n    ProcessBubbleType[\"color\"] = \"color\";\n    ProcessBubbleType[\"opacity\"] = \"opacity\";\n    ProcessBubbleType[\"size\"] = \"size\";\n})(ProcessBubbleType || (ProcessBubbleType = {}));\n"], "mappings": "AAAA,OAAO,IAAIA,iBAAiB;AAC5B,CAAC,UAAUA,iBAAiB,EAAE;EAC1BA,iBAAiB,CAAC,OAAO,CAAC,GAAG,OAAO;EACpCA,iBAAiB,CAAC,SAAS,CAAC,GAAG,SAAS;EACxCA,iBAAiB,CAAC,MAAM,CAAC,GAAG,MAAM;AACtC,CAAC,EAAEA,iBAAiB,KAAKA,iBAAiB,GAAG,CAAC,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
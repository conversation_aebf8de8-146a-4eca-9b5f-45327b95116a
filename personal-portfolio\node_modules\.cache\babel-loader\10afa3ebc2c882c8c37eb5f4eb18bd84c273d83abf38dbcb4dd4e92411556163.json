{"ast": null, "code": "export var RotateDirection;\n(function (RotateDirection) {\n  RotateDirection[\"clockwise\"] = \"clockwise\";\n  RotateDirection[\"counterClockwise\"] = \"counter-clockwise\";\n  RotateDirection[\"random\"] = \"random\";\n})(RotateDirection || (RotateDirection = {}));", "map": {"version": 3, "names": ["RotateDirection"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/engine/browser/Enums/Directions/RotateDirection.js"], "sourcesContent": ["export var RotateDirection;\n(function (RotateDirection) {\n    RotateDirection[\"clockwise\"] = \"clockwise\";\n    RotateDirection[\"counterClockwise\"] = \"counter-clockwise\";\n    RotateDirection[\"random\"] = \"random\";\n})(RotateDirection || (RotateDirection = {}));\n"], "mappings": "AAAA,OAAO,IAAIA,eAAe;AAC1B,CAAC,UAAUA,eAAe,EAAE;EACxBA,eAAe,CAAC,WAAW,CAAC,GAAG,WAAW;EAC1CA,eAAe,CAAC,kBAAkB,CAAC,GAAG,mBAAmB;EACzDA,eAAe,CAAC,QAAQ,CAAC,GAAG,QAAQ;AACxC,CAAC,EAAEA,eAAe,KAAKA,eAAe,GAAG,CAAC,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { ValueWithRandom } from \"../../ValueWithRandom\";\nexport class OrbitRotation extends ValueWithRandom {\n  constructor() {\n    super();\n    this.value = 45;\n    this.random.enable = false;\n    this.random.minimumValue = 0;\n  }\n\n  load(data) {\n    if (data === undefined) {\n      return;\n    }\n\n    super.load(data);\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Options/Classes/Particles/Orbit/OrbitRotation.js"], "names": ["ValueWithRandom", "OrbitRotation", "constructor", "value", "random", "enable", "minimumValue", "load", "data", "undefined"], "mappings": "AAAA,SAASA,eAAT,QAAgC,uBAAhC;AACA,OAAO,MAAMC,aAAN,SAA4BD,eAA5B,CAA4C;AAC/CE,EAAAA,WAAW,GAAG;AACV;AACA,SAAKC,KAAL,GAAa,EAAb;AACA,SAAKC,MAAL,CAAYC,MAAZ,GAAqB,KAArB;AACA,SAAKD,MAAL,CAAYE,YAAZ,GAA2B,CAA3B;AACH;;AACDC,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,QAAIA,IAAI,KAAKC,SAAb,EAAwB;AACpB;AACH;;AACD,UAAMF,IAAN,CAAWC,IAAX;AACH;;AAZ8C", "sourcesContent": ["import { ValueWithRandom } from \"../../ValueWithRandom\";\nexport class OrbitRotation extends ValueWithRandom {\n    constructor() {\n        super();\n        this.value = 45;\n        this.random.enable = false;\n        this.random.minimumValue = 0;\n    }\n    load(data) {\n        if (data === undefined) {\n            return;\n        }\n        super.load(data);\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
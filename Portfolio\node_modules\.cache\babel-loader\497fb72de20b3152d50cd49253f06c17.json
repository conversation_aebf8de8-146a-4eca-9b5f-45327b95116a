{"ast": null, "code": "export class Slow {\n  constructor() {\n    this.factor = 3;\n    this.radius = 200;\n  }\n\n  get active() {\n    return false;\n  }\n\n  set active(_value) {}\n\n  load(data) {\n    if (data === undefined) {\n      return;\n    }\n\n    if (data.factor !== undefined) {\n      this.factor = data.factor;\n    }\n\n    if (data.radius !== undefined) {\n      this.radius = data.radius;\n    }\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Options/Classes/Interactivity/Modes/Slow.js"], "names": ["Slow", "constructor", "factor", "radius", "active", "_value", "load", "data", "undefined"], "mappings": "AAAA,OAAO,MAAMA,IAAN,CAAW;AACdC,EAAAA,WAAW,GAAG;AACV,SAAKC,MAAL,GAAc,CAAd;AACA,SAAKC,MAAL,GAAc,GAAd;AACH;;AACS,MAANC,MAAM,GAAG;AACT,WAAO,KAAP;AACH;;AACS,MAANA,MAAM,CAACC,MAAD,EAAS,CAClB;;AACDC,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,QAAIA,IAAI,KAAKC,SAAb,EAAwB;AACpB;AACH;;AACD,QAAID,IAAI,CAACL,MAAL,KAAgBM,SAApB,EAA+B;AAC3B,WAAKN,MAAL,GAAcK,IAAI,CAACL,MAAnB;AACH;;AACD,QAAIK,IAAI,CAACJ,MAAL,KAAgBK,SAApB,EAA+B;AAC3B,WAAKL,MAAL,GAAcI,IAAI,CAACJ,MAAnB;AACH;AACJ;;AApBa", "sourcesContent": ["export class Slow {\n    constructor() {\n        this.factor = 3;\n        this.radius = 200;\n    }\n    get active() {\n        return false;\n    }\n    set active(_value) {\n    }\n    load(data) {\n        if (data === undefined) {\n            return;\n        }\n        if (data.factor !== undefined) {\n            this.factor = data.factor;\n        }\n        if (data.radius !== undefined) {\n            this.radius = data.radius;\n        }\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
{"ast": null, "code": "import { ValueWithRandom, isNumber } from \"@tsparticles/engine\";\nimport { AbsorberSizeLimit } from \"./AbsorberSizeLimit.js\";\nexport class AbsorberSize extends ValueWithRandom {\n  constructor() {\n    super();\n    this.density = 5;\n    this.value = 50;\n    this.limit = new AbsorberSizeLimit();\n  }\n  load(data) {\n    if (!data) {\n      return;\n    }\n    super.load(data);\n    if (data.density !== undefined) {\n      this.density = data.density;\n    }\n    if (isNumber(data.limit)) {\n      this.limit.radius = data.limit;\n    } else {\n      this.limit.load(data.limit);\n    }\n  }\n}", "map": {"version": 3, "names": ["ValueWithRandom", "isNumber", "AbsorberSizeLimit", "AbsorberSize", "constructor", "density", "value", "limit", "load", "data", "undefined", "radius"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/plugin-absorbers/browser/Options/Classes/AbsorberSize.js"], "sourcesContent": ["import { ValueWithRandom, isNumber } from \"@tsparticles/engine\";\nimport { AbsorberSizeLimit } from \"./AbsorberSizeLimit.js\";\nexport class AbsorberSize extends ValueWithRandom {\n    constructor() {\n        super();\n        this.density = 5;\n        this.value = 50;\n        this.limit = new AbsorberSizeLimit();\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        super.load(data);\n        if (data.density !== undefined) {\n            this.density = data.density;\n        }\n        if (isNumber(data.limit)) {\n            this.limit.radius = data.limit;\n        }\n        else {\n            this.limit.load(data.limit);\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,eAAe,EAAEC,QAAQ,QAAQ,qBAAqB;AAC/D,SAASC,iBAAiB,QAAQ,wBAAwB;AAC1D,OAAO,MAAMC,YAAY,SAASH,eAAe,CAAC;EAC9CI,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,OAAO,GAAG,CAAC;IAChB,IAAI,CAACC,KAAK,GAAG,EAAE;IACf,IAAI,CAACC,KAAK,GAAG,IAAIL,iBAAiB,CAAC,CAAC;EACxC;EACAM,IAAIA,CAACC,IAAI,EAAE;IACP,IAAI,CAACA,IAAI,EAAE;MACP;IACJ;IACA,KAAK,CAACD,IAAI,CAACC,IAAI,CAAC;IAChB,IAAIA,IAAI,CAACJ,OAAO,KAAKK,SAAS,EAAE;MAC5B,IAAI,CAACL,OAAO,GAAGI,IAAI,CAACJ,OAAO;IAC/B;IACA,IAAIJ,QAAQ,CAACQ,IAAI,CAACF,KAAK,CAAC,EAAE;MACtB,IAAI,CAACA,KAAK,CAACI,MAAM,GAAGF,IAAI,CAACF,KAAK;IAClC,CAAC,MACI;MACD,IAAI,CAACA,KAAK,CAACC,IAAI,CAACC,IAAI,CAACF,KAAK,CAAC;IAC/B;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
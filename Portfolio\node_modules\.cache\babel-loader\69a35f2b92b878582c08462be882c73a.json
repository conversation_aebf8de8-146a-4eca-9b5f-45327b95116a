{"ast": null, "code": "import { getStyleFromHsl } from \"../../Utils\";\nconst currentColorRegex = /(#(?:[0-9a-f]{2}){2,4}|(#[0-9a-f]{3})|(rgb|hsl)a?\\((-?\\d+%?[,\\s]+){2,3}\\s*[\\d.]+%?\\))|currentcolor/gi;\n\nfunction replaceColorSvg(imageShape, color, opacity) {\n  const {\n    svgData\n  } = imageShape;\n\n  if (!svgData) {\n    return \"\";\n  }\n\n  const colorStyle = getStyleFromHsl(color, opacity);\n\n  if (svgData.includes(\"fill\")) {\n    return svgData.replace(currentColorRegex, () => colorStyle);\n  }\n\n  const preFillIndex = svgData.indexOf(\">\");\n  return `${svgData.substring(0, preFillIndex)} fill=\"${colorStyle}\"${svgData.substring(preFillIndex)}`;\n}\n\nexport async function loadImage(image) {\n  return new Promise(resolve => {\n    image.loading = true;\n    const img = new Image();\n    img.addEventListener(\"load\", () => {\n      image.element = img;\n      image.loading = false;\n      resolve();\n    });\n    img.addEventListener(\"error\", () => {\n      image.error = true;\n      image.loading = false;\n      console.error(`Error tsParticles - loading image: ${image.source}`);\n      resolve();\n    });\n    img.src = image.source;\n  });\n}\nexport async function downloadSvgImage(image) {\n  if (image.type !== \"svg\") {\n    await loadImage(image);\n    return;\n  }\n\n  image.loading = true;\n  const response = await fetch(image.source);\n  image.loading = false;\n\n  if (!response.ok) {\n    console.error(\"Error tsParticles - Image not found\");\n    image.error = true;\n  }\n\n  if (!image.error) {\n    image.svgData = await response.text();\n  }\n}\nexport function replaceImageColor(image, imageData, color, particle) {\n  var _a, _b, _c;\n\n  const svgColoredData = replaceColorSvg(image, color, (_b = (_a = particle.opacity) === null || _a === void 0 ? void 0 : _a.value) !== null && _b !== void 0 ? _b : 1);\n  const svg = new Blob([svgColoredData], {\n    type: \"image/svg+xml\"\n  });\n  const domUrl = URL || window.URL || window.webkitURL || window;\n  const url = domUrl.createObjectURL(svg);\n  const img = new Image();\n  const imageRes = {\n    data: Object.assign(Object.assign({}, image), {\n      svgData: svgColoredData\n    }),\n    ratio: imageData.width / imageData.height,\n    replaceColor: (_c = imageData.replaceColor) !== null && _c !== void 0 ? _c : imageData.replace_color,\n    source: imageData.src\n  };\n  img.addEventListener(\"load\", () => {\n    const pImage = particle.image;\n\n    if (pImage) {\n      pImage.loaded = true;\n      image.element = img;\n    }\n\n    domUrl.revokeObjectURL(url);\n  });\n  img.addEventListener(\"error\", () => {\n    domUrl.revokeObjectURL(url);\n    const img2 = Object.assign(Object.assign({}, image), {\n      error: false,\n      loading: true\n    });\n    loadImage(img2).then(() => {\n      const pImage = particle.image;\n\n      if (pImage) {\n        image.element = img2.element;\n        pImage.loaded = true;\n      }\n    });\n  });\n  img.src = url;\n  return imageRes;\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Shapes/Image/Utils.js"], "names": ["getStyleFromHsl", "currentColorRegex", "replaceColorSvg", "imageShape", "color", "opacity", "svgData", "colorStyle", "includes", "replace", "preFillIndex", "indexOf", "substring", "loadImage", "image", "Promise", "resolve", "loading", "img", "Image", "addEventListener", "element", "error", "console", "source", "src", "downloadSvgImage", "type", "response", "fetch", "ok", "text", "replaceImageColor", "imageData", "particle", "_a", "_b", "_c", "svgColoredData", "value", "svg", "Blob", "domUrl", "URL", "window", "webkitURL", "url", "createObjectURL", "imageRes", "data", "Object", "assign", "ratio", "width", "height", "replaceColor", "replace_color", "pImage", "loaded", "revokeObjectURL", "img2", "then"], "mappings": "AAAA,SAASA,eAAT,QAAgC,aAAhC;AACA,MAAMC,iBAAiB,GAAG,sGAA1B;;AACA,SAASC,eAAT,CAAyBC,UAAzB,EAAqCC,KAArC,EAA4CC,OAA5C,EAAqD;AACjD,QAAM;AAAEC,IAAAA;AAAF,MAAcH,UAApB;;AACA,MAAI,CAACG,OAAL,EAAc;AACV,WAAO,EAAP;AACH;;AACD,QAAMC,UAAU,GAAGP,eAAe,CAACI,KAAD,EAAQC,OAAR,CAAlC;;AACA,MAAIC,OAAO,CAACE,QAAR,CAAiB,MAAjB,CAAJ,EAA8B;AAC1B,WAAOF,OAAO,CAACG,OAAR,CAAgBR,iBAAhB,EAAmC,MAAMM,UAAzC,CAAP;AACH;;AACD,QAAMG,YAAY,GAAGJ,OAAO,CAACK,OAAR,CAAgB,GAAhB,CAArB;AACA,SAAQ,GAAEL,OAAO,CAACM,SAAR,CAAkB,CAAlB,EAAqBF,YAArB,CAAmC,UAASH,UAAW,IAAGD,OAAO,CAACM,SAAR,CAAkBF,YAAlB,CAAgC,EAApG;AACH;;AACD,OAAO,eAAeG,SAAf,CAAyBC,KAAzB,EAAgC;AACnC,SAAO,IAAIC,OAAJ,CAAaC,OAAD,IAAa;AAC5BF,IAAAA,KAAK,CAACG,OAAN,GAAgB,IAAhB;AACA,UAAMC,GAAG,GAAG,IAAIC,KAAJ,EAAZ;AACAD,IAAAA,GAAG,CAACE,gBAAJ,CAAqB,MAArB,EAA6B,MAAM;AAC/BN,MAAAA,KAAK,CAACO,OAAN,GAAgBH,GAAhB;AACAJ,MAAAA,KAAK,CAACG,OAAN,GAAgB,KAAhB;AACAD,MAAAA,OAAO;AACV,KAJD;AAKAE,IAAAA,GAAG,CAACE,gBAAJ,CAAqB,OAArB,EAA8B,MAAM;AAChCN,MAAAA,KAAK,CAACQ,KAAN,GAAc,IAAd;AACAR,MAAAA,KAAK,CAACG,OAAN,GAAgB,KAAhB;AACAM,MAAAA,OAAO,CAACD,KAAR,CAAe,sCAAqCR,KAAK,CAACU,MAAO,EAAjE;AACAR,MAAAA,OAAO;AACV,KALD;AAMAE,IAAAA,GAAG,CAACO,GAAJ,GAAUX,KAAK,CAACU,MAAhB;AACH,GAfM,CAAP;AAgBH;AACD,OAAO,eAAeE,gBAAf,CAAgCZ,KAAhC,EAAuC;AAC1C,MAAIA,KAAK,CAACa,IAAN,KAAe,KAAnB,EAA0B;AACtB,UAAMd,SAAS,CAACC,KAAD,CAAf;AACA;AACH;;AACDA,EAAAA,KAAK,CAACG,OAAN,GAAgB,IAAhB;AACA,QAAMW,QAAQ,GAAG,MAAMC,KAAK,CAACf,KAAK,CAACU,MAAP,CAA5B;AACAV,EAAAA,KAAK,CAACG,OAAN,GAAgB,KAAhB;;AACA,MAAI,CAACW,QAAQ,CAACE,EAAd,EAAkB;AACdP,IAAAA,OAAO,CAACD,KAAR,CAAc,qCAAd;AACAR,IAAAA,KAAK,CAACQ,KAAN,GAAc,IAAd;AACH;;AACD,MAAI,CAACR,KAAK,CAACQ,KAAX,EAAkB;AACdR,IAAAA,KAAK,CAACR,OAAN,GAAgB,MAAMsB,QAAQ,CAACG,IAAT,EAAtB;AACH;AACJ;AACD,OAAO,SAASC,iBAAT,CAA2BlB,KAA3B,EAAkCmB,SAAlC,EAA6C7B,KAA7C,EAAoD8B,QAApD,EAA8D;AACjE,MAAIC,EAAJ,EAAQC,EAAR,EAAYC,EAAZ;;AACA,QAAMC,cAAc,GAAGpC,eAAe,CAACY,KAAD,EAAQV,KAAR,EAAe,CAACgC,EAAE,GAAG,CAACD,EAAE,GAAGD,QAAQ,CAAC7B,OAAf,MAA4B,IAA5B,IAAoC8B,EAAE,KAAK,KAAK,CAAhD,GAAoD,KAAK,CAAzD,GAA6DA,EAAE,CAACI,KAAtE,MAAiF,IAAjF,IAAyFH,EAAE,KAAK,KAAK,CAArG,GAAyGA,EAAzG,GAA8G,CAA7H,CAAtC;AACA,QAAMI,GAAG,GAAG,IAAIC,IAAJ,CAAS,CAACH,cAAD,CAAT,EAA2B;AAAEX,IAAAA,IAAI,EAAE;AAAR,GAA3B,CAAZ;AACA,QAAMe,MAAM,GAAGC,GAAG,IAAIC,MAAM,CAACD,GAAd,IAAqBC,MAAM,CAACC,SAA5B,IAAyCD,MAAxD;AACA,QAAME,GAAG,GAAGJ,MAAM,CAACK,eAAP,CAAuBP,GAAvB,CAAZ;AACA,QAAMtB,GAAG,GAAG,IAAIC,KAAJ,EAAZ;AACA,QAAM6B,QAAQ,GAAG;AACbC,IAAAA,IAAI,EAAEC,MAAM,CAACC,MAAP,CAAcD,MAAM,CAACC,MAAP,CAAc,EAAd,EAAkBrC,KAAlB,CAAd,EAAwC;AAAER,MAAAA,OAAO,EAAEgC;AAAX,KAAxC,CADO;AAEbc,IAAAA,KAAK,EAAEnB,SAAS,CAACoB,KAAV,GAAkBpB,SAAS,CAACqB,MAFtB;AAGbC,IAAAA,YAAY,EAAE,CAAClB,EAAE,GAAGJ,SAAS,CAACsB,YAAhB,MAAkC,IAAlC,IAA0ClB,EAAE,KAAK,KAAK,CAAtD,GAA0DA,EAA1D,GAA+DJ,SAAS,CAACuB,aAH1E;AAIbhC,IAAAA,MAAM,EAAES,SAAS,CAACR;AAJL,GAAjB;AAMAP,EAAAA,GAAG,CAACE,gBAAJ,CAAqB,MAArB,EAA6B,MAAM;AAC/B,UAAMqC,MAAM,GAAGvB,QAAQ,CAACpB,KAAxB;;AACA,QAAI2C,MAAJ,EAAY;AACRA,MAAAA,MAAM,CAACC,MAAP,GAAgB,IAAhB;AACA5C,MAAAA,KAAK,CAACO,OAAN,GAAgBH,GAAhB;AACH;;AACDwB,IAAAA,MAAM,CAACiB,eAAP,CAAuBb,GAAvB;AACH,GAPD;AAQA5B,EAAAA,GAAG,CAACE,gBAAJ,CAAqB,OAArB,EAA8B,MAAM;AAChCsB,IAAAA,MAAM,CAACiB,eAAP,CAAuBb,GAAvB;AACA,UAAMc,IAAI,GAAGV,MAAM,CAACC,MAAP,CAAcD,MAAM,CAACC,MAAP,CAAc,EAAd,EAAkBrC,KAAlB,CAAd,EAAwC;AAAEQ,MAAAA,KAAK,EAAE,KAAT;AAAgBL,MAAAA,OAAO,EAAE;AAAzB,KAAxC,CAAb;AACAJ,IAAAA,SAAS,CAAC+C,IAAD,CAAT,CAAgBC,IAAhB,CAAqB,MAAM;AACvB,YAAMJ,MAAM,GAAGvB,QAAQ,CAACpB,KAAxB;;AACA,UAAI2C,MAAJ,EAAY;AACR3C,QAAAA,KAAK,CAACO,OAAN,GAAgBuC,IAAI,CAACvC,OAArB;AACAoC,QAAAA,MAAM,CAACC,MAAP,GAAgB,IAAhB;AACH;AACJ,KAND;AAOH,GAVD;AAWAxC,EAAAA,GAAG,CAACO,GAAJ,GAAUqB,GAAV;AACA,SAAOE,QAAP;AACH", "sourcesContent": ["import { getStyleFromHsl } from \"../../Utils\";\nconst currentColorRegex = /(#(?:[0-9a-f]{2}){2,4}|(#[0-9a-f]{3})|(rgb|hsl)a?\\((-?\\d+%?[,\\s]+){2,3}\\s*[\\d.]+%?\\))|currentcolor/gi;\nfunction replaceColorSvg(imageShape, color, opacity) {\n    const { svgData } = imageShape;\n    if (!svgData) {\n        return \"\";\n    }\n    const colorStyle = getStyleFromHsl(color, opacity);\n    if (svgData.includes(\"fill\")) {\n        return svgData.replace(currentColorRegex, () => colorStyle);\n    }\n    const preFillIndex = svgData.indexOf(\">\");\n    return `${svgData.substring(0, preFillIndex)} fill=\"${colorStyle}\"${svgData.substring(preFillIndex)}`;\n}\nexport async function loadImage(image) {\n    return new Promise((resolve) => {\n        image.loading = true;\n        const img = new Image();\n        img.addEventListener(\"load\", () => {\n            image.element = img;\n            image.loading = false;\n            resolve();\n        });\n        img.addEventListener(\"error\", () => {\n            image.error = true;\n            image.loading = false;\n            console.error(`Error tsParticles - loading image: ${image.source}`);\n            resolve();\n        });\n        img.src = image.source;\n    });\n}\nexport async function downloadSvgImage(image) {\n    if (image.type !== \"svg\") {\n        await loadImage(image);\n        return;\n    }\n    image.loading = true;\n    const response = await fetch(image.source);\n    image.loading = false;\n    if (!response.ok) {\n        console.error(\"Error tsParticles - Image not found\");\n        image.error = true;\n    }\n    if (!image.error) {\n        image.svgData = await response.text();\n    }\n}\nexport function replaceImageColor(image, imageData, color, particle) {\n    var _a, _b, _c;\n    const svgColoredData = replaceColorSvg(image, color, (_b = (_a = particle.opacity) === null || _a === void 0 ? void 0 : _a.value) !== null && _b !== void 0 ? _b : 1);\n    const svg = new Blob([svgColoredData], { type: \"image/svg+xml\" });\n    const domUrl = URL || window.URL || window.webkitURL || window;\n    const url = domUrl.createObjectURL(svg);\n    const img = new Image();\n    const imageRes = {\n        data: Object.assign(Object.assign({}, image), { svgData: svgColoredData }),\n        ratio: imageData.width / imageData.height,\n        replaceColor: (_c = imageData.replaceColor) !== null && _c !== void 0 ? _c : imageData.replace_color,\n        source: imageData.src,\n    };\n    img.addEventListener(\"load\", () => {\n        const pImage = particle.image;\n        if (pImage) {\n            pImage.loaded = true;\n            image.element = img;\n        }\n        domUrl.revokeObjectURL(url);\n    });\n    img.addEventListener(\"error\", () => {\n        domUrl.revokeObjectURL(url);\n        const img2 = Object.assign(Object.assign({}, image), { error: false, loading: true });\n        loadImage(img2).then(() => {\n            const pImage = particle.image;\n            if (pImage) {\n                image.element = img2.element;\n                pImage.loaded = true;\n            }\n        });\n    });\n    img.src = url;\n    return imageRes;\n}\n"]}, "metadata": {}, "sourceType": "module"}
{"ast": null, "code": "export class EmitterLife {\n  constructor() {\n    this.wait = false;\n  }\n\n  load(data) {\n    if (data === undefined) {\n      return;\n    }\n\n    if (data.count !== undefined) {\n      this.count = data.count;\n    }\n\n    if (data.delay !== undefined) {\n      this.delay = data.delay;\n    }\n\n    if (data.duration !== undefined) {\n      this.duration = data.duration;\n    }\n\n    if (data.wait !== undefined) {\n      this.wait = data.wait;\n    }\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Plugins/Emitters/Options/Classes/EmitterLife.js"], "names": ["EmitterLife", "constructor", "wait", "load", "data", "undefined", "count", "delay", "duration"], "mappings": "AAAA,OAAO,MAAMA,WAAN,CAAkB;AACrBC,EAAAA,WAAW,GAAG;AACV,SAAKC,IAAL,GAAY,KAAZ;AACH;;AACDC,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,QAAIA,IAAI,KAAKC,SAAb,EAAwB;AACpB;AACH;;AACD,QAAID,IAAI,CAACE,KAAL,KAAeD,SAAnB,EAA8B;AAC1B,WAAKC,KAAL,GAAaF,IAAI,CAACE,KAAlB;AACH;;AACD,QAAIF,IAAI,CAACG,KAAL,KAAeF,SAAnB,EAA8B;AAC1B,WAAKE,KAAL,GAAaH,IAAI,CAACG,KAAlB;AACH;;AACD,QAAIH,IAAI,CAACI,QAAL,KAAkBH,SAAtB,EAAiC;AAC7B,WAAKG,QAAL,GAAgBJ,IAAI,CAACI,QAArB;AACH;;AACD,QAAIJ,IAAI,CAACF,IAAL,KAAcG,SAAlB,EAA6B;AACzB,WAAKH,IAAL,GAAYE,IAAI,CAACF,IAAjB;AACH;AACJ;;AApBoB", "sourcesContent": ["export class EmitterLife {\n    constructor() {\n        this.wait = false;\n    }\n    load(data) {\n        if (data === undefined) {\n            return;\n        }\n        if (data.count !== undefined) {\n            this.count = data.count;\n        }\n        if (data.delay !== undefined) {\n            this.delay = data.delay;\n        }\n        if (data.duration !== undefined) {\n            this.duration = data.duration;\n        }\n        if (data.wait !== undefined) {\n            this.wait = data.wait;\n        }\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
{"ast": null, "code": "import { getRangeValue } from \"../../Utils\";\n\nfunction updateTilt(particle, delta) {\n  var _a;\n\n  if (!particle.tilt) {\n    return;\n  }\n\n  const tilt = particle.options.tilt;\n  const tiltAnimation = tilt.animation;\n  const speed = ((_a = particle.tilt.velocity) !== null && _a !== void 0 ? _a : 0) * delta.factor;\n  const max = 2 * Math.PI;\n\n  if (!tiltAnimation.enable) {\n    return;\n  }\n\n  switch (particle.tilt.status) {\n    case 0:\n      particle.tilt.value += speed;\n\n      if (particle.tilt.value > max) {\n        particle.tilt.value -= max;\n      }\n\n      break;\n\n    case 1:\n    default:\n      particle.tilt.value -= speed;\n\n      if (particle.tilt.value < 0) {\n        particle.tilt.value += max;\n      }\n\n      break;\n  }\n}\n\nexport class TiltUpdater {\n  constructor(container) {\n    this.container = container;\n  }\n\n  init(particle) {\n    const tiltOptions = particle.options.tilt;\n    particle.tilt = {\n      enable: tiltOptions.enable,\n      value: getRangeValue(tiltOptions.value) * Math.PI / 180,\n      sinDirection: Math.random() >= 0.5 ? 1 : -1,\n      cosDirection: Math.random() >= 0.5 ? 1 : -1\n    };\n    let tiltDirection = tiltOptions.direction;\n\n    if (tiltDirection === \"random\") {\n      const index = Math.floor(Math.random() * 2);\n      tiltDirection = index > 0 ? \"counter-clockwise\" : \"clockwise\";\n    }\n\n    switch (tiltDirection) {\n      case \"counter-clockwise\":\n      case \"counterClockwise\":\n        particle.tilt.status = 1;\n        break;\n\n      case \"clockwise\":\n        particle.tilt.status = 0;\n        break;\n    }\n\n    const tiltAnimation = particle.options.tilt.animation;\n\n    if (tiltAnimation.enable) {\n      particle.tilt.velocity = getRangeValue(tiltAnimation.speed) / 360 * this.container.retina.reduceFactor;\n\n      if (!tiltAnimation.sync) {\n        particle.tilt.velocity *= Math.random();\n      }\n    }\n  }\n\n  isEnabled(particle) {\n    const tilt = particle.options.tilt;\n    const tiltAnimation = tilt.animation;\n    return !particle.destroyed && !particle.spawning && tiltAnimation.enable;\n  }\n\n  update(particle, delta) {\n    if (!this.isEnabled(particle)) {\n      return;\n    }\n\n    updateTilt(particle, delta);\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Updaters/Tilt/TiltUpdater.js"], "names": ["getRangeValue", "updateTilt", "particle", "delta", "_a", "tilt", "options", "tiltAnimation", "animation", "speed", "velocity", "factor", "max", "Math", "PI", "enable", "status", "value", "TiltUpdater", "constructor", "container", "init", "tiltOptions", "sinDirection", "random", "cosDirection", "tiltDirection", "direction", "index", "floor", "retina", "reduceFactor", "sync", "isEnabled", "destroyed", "spawning", "update"], "mappings": "AAAA,SAASA,aAAT,QAA8B,aAA9B;;AACA,SAASC,UAAT,CAAoBC,QAApB,EAA8BC,KAA9B,EAAqC;AACjC,MAAIC,EAAJ;;AACA,MAAI,CAACF,QAAQ,CAACG,IAAd,EAAoB;AAChB;AACH;;AACD,QAAMA,IAAI,GAAGH,QAAQ,CAACI,OAAT,CAAiBD,IAA9B;AACA,QAAME,aAAa,GAAGF,IAAI,CAACG,SAA3B;AACA,QAAMC,KAAK,GAAG,CAAC,CAACL,EAAE,GAAGF,QAAQ,CAACG,IAAT,CAAcK,QAApB,MAAkC,IAAlC,IAA0CN,EAAE,KAAK,KAAK,CAAtD,GAA0DA,EAA1D,GAA+D,CAAhE,IAAqED,KAAK,CAACQ,MAAzF;AACA,QAAMC,GAAG,GAAG,IAAIC,IAAI,CAACC,EAArB;;AACA,MAAI,CAACP,aAAa,CAACQ,MAAnB,EAA2B;AACvB;AACH;;AACD,UAAQb,QAAQ,CAACG,IAAT,CAAcW,MAAtB;AACI,SAAK,CAAL;AACId,MAAAA,QAAQ,CAACG,IAAT,CAAcY,KAAd,IAAuBR,KAAvB;;AACA,UAAIP,QAAQ,CAACG,IAAT,CAAcY,KAAd,GAAsBL,GAA1B,EAA+B;AAC3BV,QAAAA,QAAQ,CAACG,IAAT,CAAcY,KAAd,IAAuBL,GAAvB;AACH;;AACD;;AACJ,SAAK,CAAL;AACA;AACIV,MAAAA,QAAQ,CAACG,IAAT,CAAcY,KAAd,IAAuBR,KAAvB;;AACA,UAAIP,QAAQ,CAACG,IAAT,CAAcY,KAAd,GAAsB,CAA1B,EAA6B;AACzBf,QAAAA,QAAQ,CAACG,IAAT,CAAcY,KAAd,IAAuBL,GAAvB;AACH;;AACD;AAbR;AAeH;;AACD,OAAO,MAAMM,WAAN,CAAkB;AACrBC,EAAAA,WAAW,CAACC,SAAD,EAAY;AACnB,SAAKA,SAAL,GAAiBA,SAAjB;AACH;;AACDC,EAAAA,IAAI,CAACnB,QAAD,EAAW;AACX,UAAMoB,WAAW,GAAGpB,QAAQ,CAACI,OAAT,CAAiBD,IAArC;AACAH,IAAAA,QAAQ,CAACG,IAAT,GAAgB;AACZU,MAAAA,MAAM,EAAEO,WAAW,CAACP,MADR;AAEZE,MAAAA,KAAK,EAAGjB,aAAa,CAACsB,WAAW,CAACL,KAAb,CAAb,GAAmCJ,IAAI,CAACC,EAAzC,GAA+C,GAF1C;AAGZS,MAAAA,YAAY,EAAEV,IAAI,CAACW,MAAL,MAAiB,GAAjB,GAAuB,CAAvB,GAA2B,CAAC,CAH9B;AAIZC,MAAAA,YAAY,EAAEZ,IAAI,CAACW,MAAL,MAAiB,GAAjB,GAAuB,CAAvB,GAA2B,CAAC;AAJ9B,KAAhB;AAMA,QAAIE,aAAa,GAAGJ,WAAW,CAACK,SAAhC;;AACA,QAAID,aAAa,KAAK,QAAtB,EAAgC;AAC5B,YAAME,KAAK,GAAGf,IAAI,CAACgB,KAAL,CAAWhB,IAAI,CAACW,MAAL,KAAgB,CAA3B,CAAd;AACAE,MAAAA,aAAa,GAAGE,KAAK,GAAG,CAAR,GAAY,mBAAZ,GAAkC,WAAlD;AACH;;AACD,YAAQF,aAAR;AACI,WAAK,mBAAL;AACA,WAAK,kBAAL;AACIxB,QAAAA,QAAQ,CAACG,IAAT,CAAcW,MAAd,GAAuB,CAAvB;AACA;;AACJ,WAAK,WAAL;AACId,QAAAA,QAAQ,CAACG,IAAT,CAAcW,MAAd,GAAuB,CAAvB;AACA;AAPR;;AASA,UAAMT,aAAa,GAAGL,QAAQ,CAACI,OAAT,CAAiBD,IAAjB,CAAsBG,SAA5C;;AACA,QAAID,aAAa,CAACQ,MAAlB,EAA0B;AACtBb,MAAAA,QAAQ,CAACG,IAAT,CAAcK,QAAd,GAA0BV,aAAa,CAACO,aAAa,CAACE,KAAf,CAAb,GAAqC,GAAtC,GAA6C,KAAKW,SAAL,CAAeU,MAAf,CAAsBC,YAA5F;;AACA,UAAI,CAACxB,aAAa,CAACyB,IAAnB,EAAyB;AACrB9B,QAAAA,QAAQ,CAACG,IAAT,CAAcK,QAAd,IAA0BG,IAAI,CAACW,MAAL,EAA1B;AACH;AACJ;AACJ;;AACDS,EAAAA,SAAS,CAAC/B,QAAD,EAAW;AAChB,UAAMG,IAAI,GAAGH,QAAQ,CAACI,OAAT,CAAiBD,IAA9B;AACA,UAAME,aAAa,GAAGF,IAAI,CAACG,SAA3B;AACA,WAAO,CAACN,QAAQ,CAACgC,SAAV,IAAuB,CAAChC,QAAQ,CAACiC,QAAjC,IAA6C5B,aAAa,CAACQ,MAAlE;AACH;;AACDqB,EAAAA,MAAM,CAAClC,QAAD,EAAWC,KAAX,EAAkB;AACpB,QAAI,CAAC,KAAK8B,SAAL,CAAe/B,QAAf,CAAL,EAA+B;AAC3B;AACH;;AACDD,IAAAA,UAAU,CAACC,QAAD,EAAWC,KAAX,CAAV;AACH;;AA5CoB", "sourcesContent": ["import { getRangeValue } from \"../../Utils\";\nfunction updateTilt(particle, delta) {\n    var _a;\n    if (!particle.tilt) {\n        return;\n    }\n    const tilt = particle.options.tilt;\n    const tiltAnimation = tilt.animation;\n    const speed = ((_a = particle.tilt.velocity) !== null && _a !== void 0 ? _a : 0) * delta.factor;\n    const max = 2 * Math.PI;\n    if (!tiltAnimation.enable) {\n        return;\n    }\n    switch (particle.tilt.status) {\n        case 0:\n            particle.tilt.value += speed;\n            if (particle.tilt.value > max) {\n                particle.tilt.value -= max;\n            }\n            break;\n        case 1:\n        default:\n            particle.tilt.value -= speed;\n            if (particle.tilt.value < 0) {\n                particle.tilt.value += max;\n            }\n            break;\n    }\n}\nexport class TiltUpdater {\n    constructor(container) {\n        this.container = container;\n    }\n    init(particle) {\n        const tiltOptions = particle.options.tilt;\n        particle.tilt = {\n            enable: tiltOptions.enable,\n            value: (getRangeValue(tiltOptions.value) * Math.PI) / 180,\n            sinDirection: Math.random() >= 0.5 ? 1 : -1,\n            cosDirection: Math.random() >= 0.5 ? 1 : -1,\n        };\n        let tiltDirection = tiltOptions.direction;\n        if (tiltDirection === \"random\") {\n            const index = Math.floor(Math.random() * 2);\n            tiltDirection = index > 0 ? \"counter-clockwise\" : \"clockwise\";\n        }\n        switch (tiltDirection) {\n            case \"counter-clockwise\":\n            case \"counterClockwise\":\n                particle.tilt.status = 1;\n                break;\n            case \"clockwise\":\n                particle.tilt.status = 0;\n                break;\n        }\n        const tiltAnimation = particle.options.tilt.animation;\n        if (tiltAnimation.enable) {\n            particle.tilt.velocity = (getRangeValue(tiltAnimation.speed) / 360) * this.container.retina.reduceFactor;\n            if (!tiltAnimation.sync) {\n                particle.tilt.velocity *= Math.random();\n            }\n        }\n    }\n    isEnabled(particle) {\n        const tilt = particle.options.tilt;\n        const tiltAnimation = tilt.animation;\n        return !particle.destroyed && !particle.spawning && tiltAnimation.enable;\n    }\n    update(particle, delta) {\n        if (!this.isEnabled(particle)) {\n            return;\n        }\n        updateTilt(particle, delta);\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
{"ast": null, "code": "export class ConnectLinks {\n  constructor() {\n    this.opacity = 0.5;\n  }\n\n  load(data) {\n    if (!(data !== undefined && data.opacity !== undefined)) {\n      return;\n    }\n\n    this.opacity = data.opacity;\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Options/Classes/Interactivity/Modes/ConnectLinks.js"], "names": ["ConnectLinks", "constructor", "opacity", "load", "data", "undefined"], "mappings": "AAAA,OAAO,MAAMA,YAAN,CAAmB;AACtBC,EAAAA,WAAW,GAAG;AACV,SAAKC,OAAL,GAAe,GAAf;AACH;;AACDC,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,QAAI,EAAEA,IAAI,KAAKC,SAAT,IAAsBD,IAAI,CAACF,OAAL,KAAiBG,SAAzC,CAAJ,EAAyD;AACrD;AACH;;AACD,SAAKH,OAAL,GAAeE,IAAI,CAACF,OAApB;AACH;;AATqB", "sourcesContent": ["export class ConnectLinks {\n    constructor() {\n        this.opacity = 0.5;\n    }\n    load(data) {\n        if (!(data !== undefined && data.opacity !== undefined)) {\n            return;\n        }\n        this.opacity = data.opacity;\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
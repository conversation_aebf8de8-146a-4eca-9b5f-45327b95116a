{"ast": null, "code": "import { setRangeValue } from \"../../../../Utils/NumberUtils.js\";\nexport class MoveAngle {\n  constructor() {\n    this.offset = 0;\n    this.value = 90;\n  }\n  load(data) {\n    if (!data) {\n      return;\n    }\n    if (data.offset !== undefined) {\n      this.offset = setRangeValue(data.offset);\n    }\n    if (data.value !== undefined) {\n      this.value = setRangeValue(data.value);\n    }\n  }\n}", "map": {"version": 3, "names": ["setRangeValue", "MoveAngle", "constructor", "offset", "value", "load", "data", "undefined"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/engine/browser/Options/Classes/Particles/Move/MoveAngle.js"], "sourcesContent": ["import { setRangeValue } from \"../../../../Utils/NumberUtils.js\";\nexport class MoveAngle {\n    constructor() {\n        this.offset = 0;\n        this.value = 90;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.offset !== undefined) {\n            this.offset = setRangeValue(data.offset);\n        }\n        if (data.value !== undefined) {\n            this.value = setRangeValue(data.value);\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,kCAAkC;AAChE,OAAO,MAAMC,SAAS,CAAC;EACnBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,MAAM,GAAG,CAAC;IACf,IAAI,CAACC,KAAK,GAAG,EAAE;EACnB;EACAC,IAAIA,CAACC,IAAI,EAAE;IACP,IAAI,CAACA,IAAI,EAAE;MACP;IACJ;IACA,IAAIA,IAAI,CAACH,MAAM,KAAKI,SAAS,EAAE;MAC3B,IAAI,CAACJ,MAAM,GAAGH,aAAa,CAACM,IAAI,CAACH,MAAM,CAAC;IAC5C;IACA,IAAIG,IAAI,CAACF,KAAK,KAAKG,SAAS,EAAE;MAC1B,IAAI,CAACH,KAAK,GAAGJ,aAAa,CAACM,IAAI,CAACF,KAAK,CAAC;IAC1C;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
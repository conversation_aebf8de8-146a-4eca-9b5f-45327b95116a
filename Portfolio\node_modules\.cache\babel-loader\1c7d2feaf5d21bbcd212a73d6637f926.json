{"ast": null, "code": "import { ValueWithRandom } from \"../../ValueWithRandom\";\nexport class ZIndex extends ValueWithRandom {\n  constructor() {\n    super();\n    this.opacityRate = 1;\n    this.sizeRate = 1;\n    this.velocityRate = 1;\n  }\n\n  load(data) {\n    super.load(data);\n\n    if (!data) {\n      return;\n    }\n\n    if (data.opacityRate !== undefined) {\n      this.opacityRate = data.opacityRate;\n    }\n\n    if (data.sizeRate !== undefined) {\n      this.sizeRate = data.sizeRate;\n    }\n\n    if (data.velocityRate !== undefined) {\n      this.velocityRate = data.velocityRate;\n    }\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Options/Classes/Particles/ZIndex/ZIndex.js"], "names": ["ValueWithRandom", "ZIndex", "constructor", "opacityRate", "sizeRate", "velocityRate", "load", "data", "undefined"], "mappings": "AAAA,SAASA,eAAT,QAAgC,uBAAhC;AACA,OAAO,MAAMC,MAAN,SAAqBD,eAArB,CAAqC;AACxCE,EAAAA,WAAW,GAAG;AACV;AACA,SAAKC,WAAL,GAAmB,CAAnB;AACA,SAAKC,QAAL,GAAgB,CAAhB;AACA,SAAKC,YAAL,GAAoB,CAApB;AACH;;AACDC,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,UAAMD,IAAN,CAAWC,IAAX;;AACA,QAAI,CAACA,IAAL,EAAW;AACP;AACH;;AACD,QAAIA,IAAI,CAACJ,WAAL,KAAqBK,SAAzB,EAAoC;AAChC,WAAKL,WAAL,GAAmBI,IAAI,CAACJ,WAAxB;AACH;;AACD,QAAII,IAAI,CAACH,QAAL,KAAkBI,SAAtB,EAAiC;AAC7B,WAAKJ,QAAL,GAAgBG,IAAI,CAACH,QAArB;AACH;;AACD,QAAIG,IAAI,CAACF,YAAL,KAAsBG,SAA1B,EAAqC;AACjC,WAAKH,YAAL,GAAoBE,IAAI,CAACF,YAAzB;AACH;AACJ;;AArBuC", "sourcesContent": ["import { ValueWithRandom } from \"../../ValueWithRandom\";\nexport class ZIndex extends ValueWithRandom {\n    constructor() {\n        super();\n        this.opacityRate = 1;\n        this.sizeRate = 1;\n        this.velocityRate = 1;\n    }\n    load(data) {\n        super.load(data);\n        if (!data) {\n            return;\n        }\n        if (data.opacityRate !== undefined) {\n            this.opacityRate = data.opacityRate;\n        }\n        if (data.sizeRate !== undefined) {\n            this.sizeRate = data.sizeRate;\n        }\n        if (data.velocityRate !== undefined) {\n            this.velocityRate = data.velocityRate;\n        }\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
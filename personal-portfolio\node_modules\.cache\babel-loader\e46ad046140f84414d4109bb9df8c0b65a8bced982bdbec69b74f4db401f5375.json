{"ast": null, "code": "import { OutMode, OutModeDirection, Vector, isPointInside } from \"@tsparticles/engine\";\nconst minVelocity = 0;\nexport class NoneOutMode {\n  constructor(container) {\n    this.container = container;\n    this.modes = [OutMode.none];\n  }\n  update(particle, direction, delta, outMode) {\n    if (!this.modes.includes(outMode)) {\n      return;\n    }\n    if ((particle.options.move.distance.horizontal && (direction === OutModeDirection.left || direction === OutModeDirection.right)) ?? (particle.options.move.distance.vertical && (direction === OutModeDirection.top || direction === OutModeDirection.bottom))) {\n      return;\n    }\n    const gravityOptions = particle.options.move.gravity,\n      container = this.container,\n      canvasSize = container.canvas.size,\n      pRadius = particle.getRadius();\n    if (!gravityOptions.enable) {\n      if (particle.velocity.y > minVelocity && particle.position.y <= canvasSize.height + pRadius || particle.velocity.y < minVelocity && particle.position.y >= -pRadius || particle.velocity.x > minVelocity && particle.position.x <= canvasSize.width + pRadius || particle.velocity.x < minVelocity && particle.position.x >= -pRadius) {\n        return;\n      }\n      if (!isPointInside(particle.position, container.canvas.size, Vector.origin, pRadius, direction)) {\n        container.particles.remove(particle);\n      }\n    } else {\n      const position = particle.position;\n      if (!gravityOptions.inverse && position.y > canvasSize.height + pRadius && direction === OutModeDirection.bottom || gravityOptions.inverse && position.y < -pRadius && direction === OutModeDirection.top) {\n        container.particles.remove(particle);\n      }\n    }\n  }\n}", "map": {"version": 3, "names": ["OutMode", "OutModeDirection", "Vector", "isPointInside", "minVelocity", "NoneOutMode", "constructor", "container", "modes", "none", "update", "particle", "direction", "delta", "outMode", "includes", "options", "move", "distance", "horizontal", "left", "right", "vertical", "top", "bottom", "gravityOptions", "gravity", "canvasSize", "canvas", "size", "pRadius", "getRadius", "enable", "velocity", "y", "position", "height", "x", "width", "origin", "particles", "remove", "inverse"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/updater-out-modes/browser/NoneOutMode.js"], "sourcesContent": ["import { OutMode, OutModeDirection, Vector, isPointInside, } from \"@tsparticles/engine\";\nconst minVelocity = 0;\nexport class NoneOutMode {\n    constructor(container) {\n        this.container = container;\n        this.modes = [OutMode.none];\n    }\n    update(particle, direction, delta, outMode) {\n        if (!this.modes.includes(outMode)) {\n            return;\n        }\n        if ((particle.options.move.distance.horizontal &&\n            (direction === OutModeDirection.left || direction === OutModeDirection.right)) ??\n            (particle.options.move.distance.vertical &&\n                (direction === OutModeDirection.top || direction === OutModeDirection.bottom))) {\n            return;\n        }\n        const gravityOptions = particle.options.move.gravity, container = this.container, canvasSize = container.canvas.size, pRadius = particle.getRadius();\n        if (!gravityOptions.enable) {\n            if ((particle.velocity.y > minVelocity && particle.position.y <= canvasSize.height + pRadius) ||\n                (particle.velocity.y < minVelocity && particle.position.y >= -pRadius) ||\n                (particle.velocity.x > minVelocity && particle.position.x <= canvasSize.width + pRadius) ||\n                (particle.velocity.x < minVelocity && particle.position.x >= -pRadius)) {\n                return;\n            }\n            if (!isPointInside(particle.position, container.canvas.size, Vector.origin, pRadius, direction)) {\n                container.particles.remove(particle);\n            }\n        }\n        else {\n            const position = particle.position;\n            if ((!gravityOptions.inverse &&\n                position.y > canvasSize.height + pRadius &&\n                direction === OutModeDirection.bottom) ||\n                (gravityOptions.inverse && position.y < -pRadius && direction === OutModeDirection.top)) {\n                container.particles.remove(particle);\n            }\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,OAAO,EAAEC,gBAAgB,EAAEC,MAAM,EAAEC,aAAa,QAAS,qBAAqB;AACvF,MAAMC,WAAW,GAAG,CAAC;AACrB,OAAO,MAAMC,WAAW,CAAC;EACrBC,WAAWA,CAACC,SAAS,EAAE;IACnB,IAAI,CAACA,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,KAAK,GAAG,CAACR,OAAO,CAACS,IAAI,CAAC;EAC/B;EACAC,MAAMA,CAACC,QAAQ,EAAEC,SAAS,EAAEC,KAAK,EAAEC,OAAO,EAAE;IACxC,IAAI,CAAC,IAAI,CAACN,KAAK,CAACO,QAAQ,CAACD,OAAO,CAAC,EAAE;MAC/B;IACJ;IACA,IAAI,CAACH,QAAQ,CAACK,OAAO,CAACC,IAAI,CAACC,QAAQ,CAACC,UAAU,KACzCP,SAAS,KAAKX,gBAAgB,CAACmB,IAAI,IAAIR,SAAS,KAAKX,gBAAgB,CAACoB,KAAK,CAAC,MAC5EV,QAAQ,CAACK,OAAO,CAACC,IAAI,CAACC,QAAQ,CAACI,QAAQ,KACnCV,SAAS,KAAKX,gBAAgB,CAACsB,GAAG,IAAIX,SAAS,KAAKX,gBAAgB,CAACuB,MAAM,CAAC,CAAC,EAAE;MACpF;IACJ;IACA,MAAMC,cAAc,GAAGd,QAAQ,CAACK,OAAO,CAACC,IAAI,CAACS,OAAO;MAAEnB,SAAS,GAAG,IAAI,CAACA,SAAS;MAAEoB,UAAU,GAAGpB,SAAS,CAACqB,MAAM,CAACC,IAAI;MAAEC,OAAO,GAAGnB,QAAQ,CAACoB,SAAS,CAAC,CAAC;IACpJ,IAAI,CAACN,cAAc,CAACO,MAAM,EAAE;MACxB,IAAKrB,QAAQ,CAACsB,QAAQ,CAACC,CAAC,GAAG9B,WAAW,IAAIO,QAAQ,CAACwB,QAAQ,CAACD,CAAC,IAAIP,UAAU,CAACS,MAAM,GAAGN,OAAO,IACvFnB,QAAQ,CAACsB,QAAQ,CAACC,CAAC,GAAG9B,WAAW,IAAIO,QAAQ,CAACwB,QAAQ,CAACD,CAAC,IAAI,CAACJ,OAAQ,IACrEnB,QAAQ,CAACsB,QAAQ,CAACI,CAAC,GAAGjC,WAAW,IAAIO,QAAQ,CAACwB,QAAQ,CAACE,CAAC,IAAIV,UAAU,CAACW,KAAK,GAAGR,OAAQ,IACvFnB,QAAQ,CAACsB,QAAQ,CAACI,CAAC,GAAGjC,WAAW,IAAIO,QAAQ,CAACwB,QAAQ,CAACE,CAAC,IAAI,CAACP,OAAQ,EAAE;QACxE;MACJ;MACA,IAAI,CAAC3B,aAAa,CAACQ,QAAQ,CAACwB,QAAQ,EAAE5B,SAAS,CAACqB,MAAM,CAACC,IAAI,EAAE3B,MAAM,CAACqC,MAAM,EAAET,OAAO,EAAElB,SAAS,CAAC,EAAE;QAC7FL,SAAS,CAACiC,SAAS,CAACC,MAAM,CAAC9B,QAAQ,CAAC;MACxC;IACJ,CAAC,MACI;MACD,MAAMwB,QAAQ,GAAGxB,QAAQ,CAACwB,QAAQ;MAClC,IAAK,CAACV,cAAc,CAACiB,OAAO,IACxBP,QAAQ,CAACD,CAAC,GAAGP,UAAU,CAACS,MAAM,GAAGN,OAAO,IACxClB,SAAS,KAAKX,gBAAgB,CAACuB,MAAM,IACpCC,cAAc,CAACiB,OAAO,IAAIP,QAAQ,CAACD,CAAC,GAAG,CAACJ,OAAO,IAAIlB,SAAS,KAAKX,gBAAgB,CAACsB,GAAI,EAAE;QACzFhB,SAAS,CAACiC,SAAS,CAACC,MAAM,CAAC9B,QAAQ,CAAC;MACxC;IACJ;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
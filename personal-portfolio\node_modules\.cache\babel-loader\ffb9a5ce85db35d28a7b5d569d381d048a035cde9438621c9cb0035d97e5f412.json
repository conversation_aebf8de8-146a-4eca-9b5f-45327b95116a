{"ast": null, "code": "import { getRangeValue, parseAlpha } from \"./NumberUtils.js\";\nvar RgbIndexes;\n(function (RgbIndexes) {\n  RgbIndexes[RgbIndexes[\"r\"] = 1] = \"r\";\n  RgbIndexes[RgbIndexes[\"g\"] = 2] = \"g\";\n  RgbIndexes[RgbIndexes[\"b\"] = 3] = \"b\";\n  RgbIndexes[RgbIndexes[\"a\"] = 5] = \"a\";\n})(RgbIndexes || (RgbIndexes = {}));\nexport class RgbColorManager {\n  constructor() {\n    this.key = \"rgb\";\n    this.stringPrefix = \"rgb\";\n  }\n  handleColor(color) {\n    const colorValue = color.value,\n      rgbColor = colorValue.rgb ?? color.value;\n    if (rgbColor.r !== undefined) {\n      return rgbColor;\n    }\n  }\n  handleRangeColor(color) {\n    const colorValue = color.value,\n      rgbColor = colorValue.rgb ?? color.value;\n    if (rgbColor.r !== undefined) {\n      return {\n        r: getRangeValue(rgbColor.r),\n        g: getRangeValue(rgbColor.g),\n        b: getRangeValue(rgbColor.b)\n      };\n    }\n  }\n  parseString(input) {\n    if (!input.startsWith(this.stringPrefix)) {\n      return;\n    }\n    const regex = /rgba?\\(\\s*(\\d+)\\s*,\\s*(\\d+)\\s*,\\s*(\\d+)\\s*(,\\s*([\\d.%]+)\\s*)?\\)/i,\n      result = regex.exec(input),\n      radix = 10,\n      minLength = 4,\n      defaultAlpha = 1;\n    return result ? {\n      a: result.length > minLength ? parseAlpha(result[RgbIndexes.a]) : defaultAlpha,\n      b: parseInt(result[RgbIndexes.b], radix),\n      g: parseInt(result[RgbIndexes.g], radix),\n      r: parseInt(result[RgbIndexes.r], radix)\n    } : undefined;\n  }\n}", "map": {"version": 3, "names": ["getRangeValue", "parseAlpha", "RgbIndexes", "RgbColorManager", "constructor", "key", "stringPrefix", "handleColor", "color", "colorValue", "value", "rgbColor", "rgb", "r", "undefined", "handleRangeColor", "g", "b", "parseString", "input", "startsWith", "regex", "result", "exec", "radix", "<PERSON><PERSON><PERSON><PERSON>", "defaultAlpha", "a", "length", "parseInt"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/engine/browser/Utils/RgbColorManager.js"], "sourcesContent": ["import { getRangeValue, parseAlpha } from \"./NumberUtils.js\";\nvar RgbIndexes;\n(function (RgbIndexes) {\n    RgbIndexes[RgbIndexes[\"r\"] = 1] = \"r\";\n    RgbIndexes[RgbIndexes[\"g\"] = 2] = \"g\";\n    RgbIndexes[RgbIndexes[\"b\"] = 3] = \"b\";\n    RgbIndexes[RgbIndexes[\"a\"] = 5] = \"a\";\n})(RgbIndexes || (RgbIndexes = {}));\nexport class RgbColorManager {\n    constructor() {\n        this.key = \"rgb\";\n        this.stringPrefix = \"rgb\";\n    }\n    handleColor(color) {\n        const colorValue = color.value, rgbColor = colorValue.rgb ?? color.value;\n        if (rgbColor.r !== undefined) {\n            return rgbColor;\n        }\n    }\n    handleRangeColor(color) {\n        const colorValue = color.value, rgbColor = colorValue.rgb ?? color.value;\n        if (rgbColor.r !== undefined) {\n            return {\n                r: getRangeValue(rgbColor.r),\n                g: getRangeValue(rgbColor.g),\n                b: getRangeValue(rgbColor.b),\n            };\n        }\n    }\n    parseString(input) {\n        if (!input.startsWith(this.stringPrefix)) {\n            return;\n        }\n        const regex = /rgba?\\(\\s*(\\d+)\\s*,\\s*(\\d+)\\s*,\\s*(\\d+)\\s*(,\\s*([\\d.%]+)\\s*)?\\)/i, result = regex.exec(input), radix = 10, minLength = 4, defaultAlpha = 1;\n        return result\n            ? {\n                a: result.length > minLength ? parseAlpha(result[RgbIndexes.a]) : defaultAlpha,\n                b: parseInt(result[RgbIndexes.b], radix),\n                g: parseInt(result[RgbIndexes.g], radix),\n                r: parseInt(result[RgbIndexes.r], radix),\n            }\n            : undefined;\n    }\n}\n"], "mappings": "AAAA,SAASA,aAAa,EAAEC,UAAU,QAAQ,kBAAkB;AAC5D,IAAIC,UAAU;AACd,CAAC,UAAUA,UAAU,EAAE;EACnBA,UAAU,CAACA,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG;EACrCA,UAAU,CAACA,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG;EACrCA,UAAU,CAACA,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG;EACrCA,UAAU,CAACA,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG;AACzC,CAAC,EAAEA,UAAU,KAAKA,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC;AACnC,OAAO,MAAMC,eAAe,CAAC;EACzBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,GAAG,GAAG,KAAK;IAChB,IAAI,CAACC,YAAY,GAAG,KAAK;EAC7B;EACAC,WAAWA,CAACC,KAAK,EAAE;IACf,MAAMC,UAAU,GAAGD,KAAK,CAACE,KAAK;MAAEC,QAAQ,GAAGF,UAAU,CAACG,GAAG,IAAIJ,KAAK,CAACE,KAAK;IACxE,IAAIC,QAAQ,CAACE,CAAC,KAAKC,SAAS,EAAE;MAC1B,OAAOH,QAAQ;IACnB;EACJ;EACAI,gBAAgBA,CAACP,KAAK,EAAE;IACpB,MAAMC,UAAU,GAAGD,KAAK,CAACE,KAAK;MAAEC,QAAQ,GAAGF,UAAU,CAACG,GAAG,IAAIJ,KAAK,CAACE,KAAK;IACxE,IAAIC,QAAQ,CAACE,CAAC,KAAKC,SAAS,EAAE;MAC1B,OAAO;QACHD,CAAC,EAAEb,aAAa,CAACW,QAAQ,CAACE,CAAC,CAAC;QAC5BG,CAAC,EAAEhB,aAAa,CAACW,QAAQ,CAACK,CAAC,CAAC;QAC5BC,CAAC,EAAEjB,aAAa,CAACW,QAAQ,CAACM,CAAC;MAC/B,CAAC;IACL;EACJ;EACAC,WAAWA,CAACC,KAAK,EAAE;IACf,IAAI,CAACA,KAAK,CAACC,UAAU,CAAC,IAAI,CAACd,YAAY,CAAC,EAAE;MACtC;IACJ;IACA,MAAMe,KAAK,GAAG,kEAAkE;MAAEC,MAAM,GAAGD,KAAK,CAACE,IAAI,CAACJ,KAAK,CAAC;MAAEK,KAAK,GAAG,EAAE;MAAEC,SAAS,GAAG,CAAC;MAAEC,YAAY,GAAG,CAAC;IACzJ,OAAOJ,MAAM,GACP;MACEK,CAAC,EAAEL,MAAM,CAACM,MAAM,GAAGH,SAAS,GAAGxB,UAAU,CAACqB,MAAM,CAACpB,UAAU,CAACyB,CAAC,CAAC,CAAC,GAAGD,YAAY;MAC9ET,CAAC,EAAEY,QAAQ,CAACP,MAAM,CAACpB,UAAU,CAACe,CAAC,CAAC,EAAEO,KAAK,CAAC;MACxCR,CAAC,EAAEa,QAAQ,CAACP,MAAM,CAACpB,UAAU,CAACc,CAAC,CAAC,EAAEQ,KAAK,CAAC;MACxCX,CAAC,EAAEgB,QAAQ,CAACP,MAAM,CAACpB,UAAU,CAACW,CAAC,CAAC,EAAEW,KAAK;IAC3C,CAAC,GACCV,SAAS;EACnB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { AnimatableColor } from \"./AnimatableColor\";\nimport { setRangeValue } from \"../../Utils\";\nexport class AnimatableGradient {\n  constructor() {\n    this.angle = new GradientAngle();\n    this.colors = [];\n    this.type = \"random\";\n  }\n\n  load(data) {\n    if (!data) {\n      return;\n    }\n\n    this.angle.load(data.angle);\n\n    if (data.colors !== undefined) {\n      this.colors = data.colors.map(s => {\n        const tmp = new AnimatableGradientColor();\n        tmp.load(s);\n        return tmp;\n      });\n    }\n\n    if (data.type !== undefined) {\n      this.type = data.type;\n    }\n  }\n\n}\nexport class GradientAngle {\n  constructor() {\n    this.value = 0;\n    this.animation = new GradientAngleAnimation();\n    this.direction = \"clockwise\";\n  }\n\n  load(data) {\n    if (!data) {\n      return;\n    }\n\n    this.animation.load(data.animation);\n\n    if (data.value !== undefined) {\n      this.value = data.value;\n    }\n\n    if (data.direction !== undefined) {\n      this.direction = data.direction;\n    }\n  }\n\n}\nexport class GradientColorOpacity {\n  constructor() {\n    this.value = 0;\n    this.animation = new GradientColorOpacityAnimation();\n  }\n\n  load(data) {\n    if (!data) {\n      return;\n    }\n\n    this.animation.load(data.animation);\n\n    if (data.value !== undefined) {\n      this.value = setRangeValue(data.value);\n    }\n  }\n\n}\nexport class AnimatableGradientColor {\n  constructor() {\n    this.stop = 0;\n    this.value = new AnimatableColor();\n  }\n\n  load(data) {\n    if (!data) {\n      return;\n    }\n\n    if (data.stop !== undefined) {\n      this.stop = data.stop;\n    }\n\n    this.value = AnimatableColor.create(this.value, data.value);\n\n    if (data.opacity !== undefined) {\n      this.opacity = new GradientColorOpacity();\n\n      if (typeof data.opacity === \"number\") {\n        this.opacity.value = data.opacity;\n      } else {\n        this.opacity.load(data.opacity);\n      }\n    }\n  }\n\n}\nexport class GradientAngleAnimation {\n  constructor() {\n    this.count = 0;\n    this.enable = false;\n    this.speed = 0;\n    this.sync = false;\n  }\n\n  load(data) {\n    if (!data) {\n      return;\n    }\n\n    if (data.count !== undefined) {\n      this.count = setRangeValue(data.count);\n    }\n\n    if (data.enable !== undefined) {\n      this.enable = data.enable;\n    }\n\n    if (data.speed !== undefined) {\n      this.speed = setRangeValue(data.speed);\n    }\n\n    if (data.sync !== undefined) {\n      this.sync = data.sync;\n    }\n  }\n\n}\nexport class GradientColorOpacityAnimation {\n  constructor() {\n    this.count = 0;\n    this.enable = false;\n    this.speed = 0;\n    this.sync = false;\n    this.startValue = \"random\";\n  }\n\n  load(data) {\n    if (!data) {\n      return;\n    }\n\n    if (data.count !== undefined) {\n      this.count = setRangeValue(data.count);\n    }\n\n    if (data.enable !== undefined) {\n      this.enable = data.enable;\n    }\n\n    if (data.speed !== undefined) {\n      this.speed = setRangeValue(data.speed);\n    }\n\n    if (data.sync !== undefined) {\n      this.sync = data.sync;\n    }\n\n    if (data.startValue !== undefined) {\n      this.startValue = data.startValue;\n    }\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Options/Classes/AnimatableGradient.js"], "names": ["AnimatableColor", "setRangeValue", "AnimatableGradient", "constructor", "angle", "GradientAngle", "colors", "type", "load", "data", "undefined", "map", "s", "tmp", "AnimatableGradientColor", "value", "animation", "GradientAngleAnimation", "direction", "GradientColorOpacity", "GradientColorOpacityAnimation", "stop", "create", "opacity", "count", "enable", "speed", "sync", "startValue"], "mappings": "AAAA,SAASA,eAAT,QAAgC,mBAAhC;AACA,SAASC,aAAT,QAA8B,aAA9B;AACA,OAAO,MAAMC,kBAAN,CAAyB;AAC5BC,EAAAA,WAAW,GAAG;AACV,SAAKC,KAAL,GAAa,IAAIC,aAAJ,EAAb;AACA,SAAKC,MAAL,GAAc,EAAd;AACA,SAAKC,IAAL,GAAY,QAAZ;AACH;;AACDC,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,QAAI,CAACA,IAAL,EAAW;AACP;AACH;;AACD,SAAKL,KAAL,CAAWI,IAAX,CAAgBC,IAAI,CAACL,KAArB;;AACA,QAAIK,IAAI,CAACH,MAAL,KAAgBI,SAApB,EAA+B;AAC3B,WAAKJ,MAAL,GAAcG,IAAI,CAACH,MAAL,CAAYK,GAAZ,CAAiBC,CAAD,IAAO;AACjC,cAAMC,GAAG,GAAG,IAAIC,uBAAJ,EAAZ;AACAD,QAAAA,GAAG,CAACL,IAAJ,CAASI,CAAT;AACA,eAAOC,GAAP;AACH,OAJa,CAAd;AAKH;;AACD,QAAIJ,IAAI,CAACF,IAAL,KAAcG,SAAlB,EAA6B;AACzB,WAAKH,IAAL,GAAYE,IAAI,CAACF,IAAjB;AACH;AACJ;;AArB2B;AAuBhC,OAAO,MAAMF,aAAN,CAAoB;AACvBF,EAAAA,WAAW,GAAG;AACV,SAAKY,KAAL,GAAa,CAAb;AACA,SAAKC,SAAL,GAAiB,IAAIC,sBAAJ,EAAjB;AACA,SAAKC,SAAL,GAAiB,WAAjB;AACH;;AACDV,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,QAAI,CAACA,IAAL,EAAW;AACP;AACH;;AACD,SAAKO,SAAL,CAAeR,IAAf,CAAoBC,IAAI,CAACO,SAAzB;;AACA,QAAIP,IAAI,CAACM,KAAL,KAAeL,SAAnB,EAA8B;AAC1B,WAAKK,KAAL,GAAaN,IAAI,CAACM,KAAlB;AACH;;AACD,QAAIN,IAAI,CAACS,SAAL,KAAmBR,SAAvB,EAAkC;AAC9B,WAAKQ,SAAL,GAAiBT,IAAI,CAACS,SAAtB;AACH;AACJ;;AAjBsB;AAmB3B,OAAO,MAAMC,oBAAN,CAA2B;AAC9BhB,EAAAA,WAAW,GAAG;AACV,SAAKY,KAAL,GAAa,CAAb;AACA,SAAKC,SAAL,GAAiB,IAAII,6BAAJ,EAAjB;AACH;;AACDZ,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,QAAI,CAACA,IAAL,EAAW;AACP;AACH;;AACD,SAAKO,SAAL,CAAeR,IAAf,CAAoBC,IAAI,CAACO,SAAzB;;AACA,QAAIP,IAAI,CAACM,KAAL,KAAeL,SAAnB,EAA8B;AAC1B,WAAKK,KAAL,GAAad,aAAa,CAACQ,IAAI,CAACM,KAAN,CAA1B;AACH;AACJ;;AAb6B;AAelC,OAAO,MAAMD,uBAAN,CAA8B;AACjCX,EAAAA,WAAW,GAAG;AACV,SAAKkB,IAAL,GAAY,CAAZ;AACA,SAAKN,KAAL,GAAa,IAAIf,eAAJ,EAAb;AACH;;AACDQ,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,QAAI,CAACA,IAAL,EAAW;AACP;AACH;;AACD,QAAIA,IAAI,CAACY,IAAL,KAAcX,SAAlB,EAA6B;AACzB,WAAKW,IAAL,GAAYZ,IAAI,CAACY,IAAjB;AACH;;AACD,SAAKN,KAAL,GAAaf,eAAe,CAACsB,MAAhB,CAAuB,KAAKP,KAA5B,EAAmCN,IAAI,CAACM,KAAxC,CAAb;;AACA,QAAIN,IAAI,CAACc,OAAL,KAAiBb,SAArB,EAAgC;AAC5B,WAAKa,OAAL,GAAe,IAAIJ,oBAAJ,EAAf;;AACA,UAAI,OAAOV,IAAI,CAACc,OAAZ,KAAwB,QAA5B,EAAsC;AAClC,aAAKA,OAAL,CAAaR,KAAb,GAAqBN,IAAI,CAACc,OAA1B;AACH,OAFD,MAGK;AACD,aAAKA,OAAL,CAAaf,IAAb,CAAkBC,IAAI,CAACc,OAAvB;AACH;AACJ;AACJ;;AAtBgC;AAwBrC,OAAO,MAAMN,sBAAN,CAA6B;AAChCd,EAAAA,WAAW,GAAG;AACV,SAAKqB,KAAL,GAAa,CAAb;AACA,SAAKC,MAAL,GAAc,KAAd;AACA,SAAKC,KAAL,GAAa,CAAb;AACA,SAAKC,IAAL,GAAY,KAAZ;AACH;;AACDnB,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,QAAI,CAACA,IAAL,EAAW;AACP;AACH;;AACD,QAAIA,IAAI,CAACe,KAAL,KAAed,SAAnB,EAA8B;AAC1B,WAAKc,KAAL,GAAavB,aAAa,CAACQ,IAAI,CAACe,KAAN,CAA1B;AACH;;AACD,QAAIf,IAAI,CAACgB,MAAL,KAAgBf,SAApB,EAA+B;AAC3B,WAAKe,MAAL,GAAchB,IAAI,CAACgB,MAAnB;AACH;;AACD,QAAIhB,IAAI,CAACiB,KAAL,KAAehB,SAAnB,EAA8B;AAC1B,WAAKgB,KAAL,GAAazB,aAAa,CAACQ,IAAI,CAACiB,KAAN,CAA1B;AACH;;AACD,QAAIjB,IAAI,CAACkB,IAAL,KAAcjB,SAAlB,EAA6B;AACzB,WAAKiB,IAAL,GAAYlB,IAAI,CAACkB,IAAjB;AACH;AACJ;;AAvB+B;AAyBpC,OAAO,MAAMP,6BAAN,CAAoC;AACvCjB,EAAAA,WAAW,GAAG;AACV,SAAKqB,KAAL,GAAa,CAAb;AACA,SAAKC,MAAL,GAAc,KAAd;AACA,SAAKC,KAAL,GAAa,CAAb;AACA,SAAKC,IAAL,GAAY,KAAZ;AACA,SAAKC,UAAL,GAAkB,QAAlB;AACH;;AACDpB,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,QAAI,CAACA,IAAL,EAAW;AACP;AACH;;AACD,QAAIA,IAAI,CAACe,KAAL,KAAed,SAAnB,EAA8B;AAC1B,WAAKc,KAAL,GAAavB,aAAa,CAACQ,IAAI,CAACe,KAAN,CAA1B;AACH;;AACD,QAAIf,IAAI,CAACgB,MAAL,KAAgBf,SAApB,EAA+B;AAC3B,WAAKe,MAAL,GAAchB,IAAI,CAACgB,MAAnB;AACH;;AACD,QAAIhB,IAAI,CAACiB,KAAL,KAAehB,SAAnB,EAA8B;AAC1B,WAAKgB,KAAL,GAAazB,aAAa,CAACQ,IAAI,CAACiB,KAAN,CAA1B;AACH;;AACD,QAAIjB,IAAI,CAACkB,IAAL,KAAcjB,SAAlB,EAA6B;AACzB,WAAKiB,IAAL,GAAYlB,IAAI,CAACkB,IAAjB;AACH;;AACD,QAAIlB,IAAI,CAACmB,UAAL,KAAoBlB,SAAxB,EAAmC;AAC/B,WAAKkB,UAAL,GAAkBnB,IAAI,CAACmB,UAAvB;AACH;AACJ;;AA3BsC", "sourcesContent": ["import { AnimatableColor } from \"./AnimatableColor\";\nimport { setRangeValue } from \"../../Utils\";\nexport class AnimatableGradient {\n    constructor() {\n        this.angle = new GradientAngle();\n        this.colors = [];\n        this.type = \"random\";\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        this.angle.load(data.angle);\n        if (data.colors !== undefined) {\n            this.colors = data.colors.map((s) => {\n                const tmp = new AnimatableGradientColor();\n                tmp.load(s);\n                return tmp;\n            });\n        }\n        if (data.type !== undefined) {\n            this.type = data.type;\n        }\n    }\n}\nexport class GradientAngle {\n    constructor() {\n        this.value = 0;\n        this.animation = new GradientAngleAnimation();\n        this.direction = \"clockwise\";\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        this.animation.load(data.animation);\n        if (data.value !== undefined) {\n            this.value = data.value;\n        }\n        if (data.direction !== undefined) {\n            this.direction = data.direction;\n        }\n    }\n}\nexport class GradientColorOpacity {\n    constructor() {\n        this.value = 0;\n        this.animation = new GradientColorOpacityAnimation();\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        this.animation.load(data.animation);\n        if (data.value !== undefined) {\n            this.value = setRangeValue(data.value);\n        }\n    }\n}\nexport class AnimatableGradientColor {\n    constructor() {\n        this.stop = 0;\n        this.value = new AnimatableColor();\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.stop !== undefined) {\n            this.stop = data.stop;\n        }\n        this.value = AnimatableColor.create(this.value, data.value);\n        if (data.opacity !== undefined) {\n            this.opacity = new GradientColorOpacity();\n            if (typeof data.opacity === \"number\") {\n                this.opacity.value = data.opacity;\n            }\n            else {\n                this.opacity.load(data.opacity);\n            }\n        }\n    }\n}\nexport class GradientAngleAnimation {\n    constructor() {\n        this.count = 0;\n        this.enable = false;\n        this.speed = 0;\n        this.sync = false;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.count !== undefined) {\n            this.count = setRangeValue(data.count);\n        }\n        if (data.enable !== undefined) {\n            this.enable = data.enable;\n        }\n        if (data.speed !== undefined) {\n            this.speed = setRangeValue(data.speed);\n        }\n        if (data.sync !== undefined) {\n            this.sync = data.sync;\n        }\n    }\n}\nexport class GradientColorOpacityAnimation {\n    constructor() {\n        this.count = 0;\n        this.enable = false;\n        this.speed = 0;\n        this.sync = false;\n        this.startValue = \"random\";\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.count !== undefined) {\n            this.count = setRangeValue(data.count);\n        }\n        if (data.enable !== undefined) {\n            this.enable = data.enable;\n        }\n        if (data.speed !== undefined) {\n            this.speed = setRangeValue(data.speed);\n        }\n        if (data.sync !== undefined) {\n            this.sync = data.sync;\n        }\n        if (data.startValue !== undefined) {\n            this.startValue = data.startValue;\n        }\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
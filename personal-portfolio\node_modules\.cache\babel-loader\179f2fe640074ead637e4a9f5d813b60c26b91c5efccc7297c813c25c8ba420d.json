{"ast": null, "code": "import { ColorAnimation } from \"./ColorAnimation.js\";\nexport class HslAnimation {\n  constructor() {\n    this.h = new ColorAnimation();\n    this.s = new ColorAnimation();\n    this.l = new ColorAnimation();\n  }\n  load(data) {\n    if (!data) {\n      return;\n    }\n    this.h.load(data.h);\n    this.s.load(data.s);\n    this.l.load(data.l);\n  }\n}", "map": {"version": 3, "names": ["ColorAnimation", "HslAnimation", "constructor", "h", "s", "l", "load", "data"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/engine/browser/Options/Classes/HslAnimation.js"], "sourcesContent": ["import { ColorAnimation } from \"./ColorAnimation.js\";\nexport class HslAnimation {\n    constructor() {\n        this.h = new ColorAnimation();\n        this.s = new ColorAnimation();\n        this.l = new ColorAnimation();\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        this.h.load(data.h);\n        this.s.load(data.s);\n        this.l.load(data.l);\n    }\n}\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,qBAAqB;AACpD,OAAO,MAAMC,YAAY,CAAC;EACtBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,CAAC,GAAG,IAAIH,cAAc,CAAC,CAAC;IAC7B,IAAI,CAACI,CAAC,GAAG,IAAIJ,cAAc,CAAC,CAAC;IAC7B,IAAI,CAACK,CAAC,GAAG,IAAIL,cAAc,CAAC,CAAC;EACjC;EACAM,IAAIA,CAACC,IAAI,EAAE;IACP,IAAI,CAACA,IAAI,EAAE;MACP;IACJ;IACA,IAAI,CAACJ,CAAC,CAACG,IAAI,CAACC,IAAI,CAACJ,CAAC,CAAC;IACnB,IAAI,CAACC,CAAC,CAACE,IAAI,CAACC,IAAI,CAACH,CAAC,CAAC;IACnB,IAAI,CAACC,CAAC,CAACC,IAAI,CAACC,IAAI,CAACF,CAAC,CAAC;EACvB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
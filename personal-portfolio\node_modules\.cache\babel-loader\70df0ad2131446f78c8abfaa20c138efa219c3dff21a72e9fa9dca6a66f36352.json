{"ast": null, "code": "import { EasingType } from \"@tsparticles/engine\";\nexport class RepulseBase {\n  constructor() {\n    this.distance = 200;\n    this.duration = 0.4;\n    this.factor = 100;\n    this.speed = 1;\n    this.maxSpeed = 50;\n    this.easing = EasingType.easeOutQuad;\n  }\n  load(data) {\n    if (!data) {\n      return;\n    }\n    if (data.distance !== undefined) {\n      this.distance = data.distance;\n    }\n    if (data.duration !== undefined) {\n      this.duration = data.duration;\n    }\n    if (data.easing !== undefined) {\n      this.easing = data.easing;\n    }\n    if (data.factor !== undefined) {\n      this.factor = data.factor;\n    }\n    if (data.speed !== undefined) {\n      this.speed = data.speed;\n    }\n    if (data.maxSpeed !== undefined) {\n      this.maxSpeed = data.maxSpeed;\n    }\n  }\n}", "map": {"version": 3, "names": ["EasingType", "RepulseBase", "constructor", "distance", "duration", "factor", "speed", "maxSpeed", "easing", "easeOutQuad", "load", "data", "undefined"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/interaction-external-repulse/browser/Options/Classes/RepulseBase.js"], "sourcesContent": ["import { EasingType } from \"@tsparticles/engine\";\nexport class RepulseBase {\n    constructor() {\n        this.distance = 200;\n        this.duration = 0.4;\n        this.factor = 100;\n        this.speed = 1;\n        this.maxSpeed = 50;\n        this.easing = EasingType.easeOutQuad;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.distance !== undefined) {\n            this.distance = data.distance;\n        }\n        if (data.duration !== undefined) {\n            this.duration = data.duration;\n        }\n        if (data.easing !== undefined) {\n            this.easing = data.easing;\n        }\n        if (data.factor !== undefined) {\n            this.factor = data.factor;\n        }\n        if (data.speed !== undefined) {\n            this.speed = data.speed;\n        }\n        if (data.maxSpeed !== undefined) {\n            this.maxSpeed = data.maxSpeed;\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,qBAAqB;AAChD,OAAO,MAAMC,WAAW,CAAC;EACrBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,QAAQ,GAAG,GAAG;IACnB,IAAI,CAACC,QAAQ,GAAG,GAAG;IACnB,IAAI,CAACC,MAAM,GAAG,GAAG;IACjB,IAAI,CAACC,KAAK,GAAG,CAAC;IACd,IAAI,CAACC,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACC,MAAM,GAAGR,UAAU,CAACS,WAAW;EACxC;EACAC,IAAIA,CAACC,IAAI,EAAE;IACP,IAAI,CAACA,IAAI,EAAE;MACP;IACJ;IACA,IAAIA,IAAI,CAACR,QAAQ,KAAKS,SAAS,EAAE;MAC7B,IAAI,CAACT,QAAQ,GAAGQ,IAAI,CAACR,QAAQ;IACjC;IACA,IAAIQ,IAAI,CAACP,QAAQ,KAAKQ,SAAS,EAAE;MAC7B,IAAI,CAACR,QAAQ,GAAGO,IAAI,CAACP,QAAQ;IACjC;IACA,IAAIO,IAAI,CAACH,MAAM,KAAKI,SAAS,EAAE;MAC3B,IAAI,CAACJ,MAAM,GAAGG,IAAI,CAACH,MAAM;IAC7B;IACA,IAAIG,IAAI,CAACN,MAAM,KAAKO,SAAS,EAAE;MAC3B,IAAI,CAACP,MAAM,GAAGM,IAAI,CAACN,MAAM;IAC7B;IACA,IAAIM,IAAI,CAACL,KAAK,KAAKM,SAAS,EAAE;MAC1B,IAAI,CAACN,KAAK,GAAGK,IAAI,CAACL,KAAK;IAC3B;IACA,IAAIK,IAAI,CAACJ,QAAQ,KAAKK,SAAS,EAAE;MAC7B,IAAI,CAACL,QAAQ,GAAGI,IAAI,CAACJ,QAAQ;IACjC;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
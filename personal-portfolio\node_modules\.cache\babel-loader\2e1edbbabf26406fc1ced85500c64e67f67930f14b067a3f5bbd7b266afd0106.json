{"ast": null, "code": "import { EmittersSquareShape } from \"./EmittersSquareShape.js\";\nexport class EmittersSquareShapeGenerator {\n  generate(position, size, fill, options) {\n    return new EmittersSquareShape(position, size, fill, options);\n  }\n}", "map": {"version": 3, "names": ["EmittersSquareShape", "EmittersSquareShapeGenerator", "generate", "position", "size", "fill", "options"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/plugin-emitters-shape-square/browser/EmittersSquareShapeGenerator.js"], "sourcesContent": ["import { EmittersSquareShape } from \"./EmittersSquareShape.js\";\nexport class EmittersSquareShapeGenerator {\n    generate(position, size, fill, options) {\n        return new EmittersSquareShape(position, size, fill, options);\n    }\n}\n"], "mappings": "AAAA,SAASA,mBAAmB,QAAQ,0BAA0B;AAC9D,OAAO,MAAMC,4BAA4B,CAAC;EACtCC,QAAQA,CAACC,QAAQ,EAAEC,IAAI,EAAEC,IAAI,EAAEC,OAAO,EAAE;IACpC,OAAO,IAAIN,mBAAmB,CAACG,QAAQ,EAAEC,IAAI,EAAEC,IAAI,EAAEC,OAAO,CAAC;EACjE;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
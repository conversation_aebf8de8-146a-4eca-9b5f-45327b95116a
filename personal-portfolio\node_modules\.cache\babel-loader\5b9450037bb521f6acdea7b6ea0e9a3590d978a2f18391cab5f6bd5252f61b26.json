{"ast": null, "code": "export class ParticlesDensity {\n  constructor() {\n    this.enable = false;\n    this.width = 1920;\n    this.height = 1080;\n  }\n  load(data) {\n    if (!data) {\n      return;\n    }\n    if (data.enable !== undefined) {\n      this.enable = data.enable;\n    }\n    const width = data.width;\n    if (width !== undefined) {\n      this.width = width;\n    }\n    const height = data.height;\n    if (height !== undefined) {\n      this.height = height;\n    }\n  }\n}", "map": {"version": 3, "names": ["ParticlesDensity", "constructor", "enable", "width", "height", "load", "data", "undefined"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/engine/browser/Options/Classes/Particles/Number/ParticlesDensity.js"], "sourcesContent": ["export class ParticlesDensity {\n    constructor() {\n        this.enable = false;\n        this.width = 1920;\n        this.height = 1080;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.enable !== undefined) {\n            this.enable = data.enable;\n        }\n        const width = data.width;\n        if (width !== undefined) {\n            this.width = width;\n        }\n        const height = data.height;\n        if (height !== undefined) {\n            this.height = height;\n        }\n    }\n}\n"], "mappings": "AAAA,OAAO,MAAMA,gBAAgB,CAAC;EAC1BC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,MAAM,GAAG,KAAK;IACnB,IAAI,CAACC,KAAK,GAAG,IAAI;IACjB,IAAI,CAACC,MAAM,GAAG,IAAI;EACtB;EACAC,IAAIA,CAACC,IAAI,EAAE;IACP,IAAI,CAACA,IAAI,EAAE;MACP;IACJ;IACA,IAAIA,IAAI,CAACJ,MAAM,KAAKK,SAAS,EAAE;MAC3B,IAAI,CAACL,MAAM,GAAGI,IAAI,CAACJ,MAAM;IAC7B;IACA,MAAMC,KAAK,GAAGG,IAAI,CAACH,KAAK;IACxB,IAAIA,KAAK,KAAKI,SAAS,EAAE;MACrB,IAAI,CAACJ,KAAK,GAAGA,KAAK;IACtB;IACA,MAAMC,MAAM,GAAGE,IAAI,CAACF,MAAM;IAC1B,IAAIA,MAAM,KAAKG,SAAS,EAAE;MACtB,IAAI,CAACH,MAAM,GAAGA,MAAM;IACxB;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { EmittersCircleShape } from \"./EmittersCircleShape.js\";\nexport class EmittersCircleShapeGenerator {\n  generate(position, size, fill, options) {\n    return new EmittersCircleShape(position, size, fill, options);\n  }\n}", "map": {"version": 3, "names": ["EmittersCircleShape", "EmittersCircleShapeGenerator", "generate", "position", "size", "fill", "options"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/plugin-emitters-shape-circle/browser/EmittersCircleShapeGenerator.js"], "sourcesContent": ["import { EmittersCircleShape } from \"./EmittersCircleShape.js\";\nexport class EmittersCircleShapeGenerator {\n    generate(position, size, fill, options) {\n        return new EmittersCircleShape(position, size, fill, options);\n    }\n}\n"], "mappings": "AAAA,SAASA,mBAAmB,QAAQ,0BAA0B;AAC9D,OAAO,MAAMC,4BAA4B,CAAC;EACtCC,QAAQA,CAACC,QAAQ,EAAEC,IAAI,EAAEC,IAAI,EAAEC,OAAO,EAAE;IACpC,OAAO,IAAIN,mBAAmB,CAACG,QAAQ,EAAEC,IAAI,EAAEC,IAAI,EAAEC,OAAO,CAAC;EACjE;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { ExternalInteractorBase, getRangeValue, itemFromArray } from \"@tsparticles/engine\";\nimport { Push } from \"./Options/Classes/Push.js\";\nconst pushMode = \"push\",\n  minQuantity = 0;\nexport class Pusher extends ExternalInteractorBase {\n  constructor(container) {\n    super(container);\n    this.handleClickMode = mode => {\n      if (mode !== pushMode) {\n        return;\n      }\n      const container = this.container,\n        options = container.actualOptions,\n        pushOptions = options.interactivity.modes.push;\n      if (!pushOptions) {\n        return;\n      }\n      const quantity = getRangeValue(pushOptions.quantity);\n      if (quantity <= minQuantity) {\n        return;\n      }\n      const group = itemFromArray([undefined, ...pushOptions.groups]),\n        groupOptions = group !== undefined ? container.actualOptions.particles.groups[group] : undefined;\n      void container.particles.push(quantity, container.interactivity.mouse, groupOptions, group);\n    };\n  }\n  clear() {}\n  init() {}\n  interact() {}\n  isEnabled() {\n    return true;\n  }\n  loadModeOptions(options, ...sources) {\n    if (!options.push) {\n      options.push = new Push();\n    }\n    for (const source of sources) {\n      options.push.load(source?.push);\n    }\n  }\n  reset() {}\n}", "map": {"version": 3, "names": ["ExternalInteractorBase", "getRangeValue", "itemFromArray", "<PERSON><PERSON>", "pushMode", "minQuantity", "<PERSON><PERSON><PERSON>", "constructor", "container", "handleClickMode", "mode", "options", "actualOptions", "pushOptions", "interactivity", "modes", "push", "quantity", "group", "undefined", "groups", "groupOptions", "particles", "mouse", "clear", "init", "interact", "isEnabled", "loadModeOptions", "sources", "source", "load", "reset"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/interaction-external-push/browser/Pusher.js"], "sourcesContent": ["import { ExternalInteractorBase, getRangeValue, itemFromArray, } from \"@tsparticles/engine\";\nimport { Push } from \"./Options/Classes/Push.js\";\nconst pushMode = \"push\", minQuantity = 0;\nexport class Pusher extends ExternalInteractorBase {\n    constructor(container) {\n        super(container);\n        this.handleClickMode = (mode) => {\n            if (mode !== pushMode) {\n                return;\n            }\n            const container = this.container, options = container.actualOptions, pushOptions = options.interactivity.modes.push;\n            if (!pushOptions) {\n                return;\n            }\n            const quantity = getRangeValue(pushOptions.quantity);\n            if (quantity <= minQuantity) {\n                return;\n            }\n            const group = itemFromArray([undefined, ...pushOptions.groups]), groupOptions = group !== undefined ? container.actualOptions.particles.groups[group] : undefined;\n            void container.particles.push(quantity, container.interactivity.mouse, groupOptions, group);\n        };\n    }\n    clear() {\n    }\n    init() {\n    }\n    interact() {\n    }\n    isEnabled() {\n        return true;\n    }\n    loadModeOptions(options, ...sources) {\n        if (!options.push) {\n            options.push = new Push();\n        }\n        for (const source of sources) {\n            options.push.load(source?.push);\n        }\n    }\n    reset() {\n    }\n}\n"], "mappings": "AAAA,SAASA,sBAAsB,EAAEC,aAAa,EAAEC,aAAa,QAAS,qBAAqB;AAC3F,SAASC,IAAI,QAAQ,2BAA2B;AAChD,MAAMC,QAAQ,GAAG,MAAM;EAAEC,WAAW,GAAG,CAAC;AACxC,OAAO,MAAMC,MAAM,SAASN,sBAAsB,CAAC;EAC/CO,WAAWA,CAACC,SAAS,EAAE;IACnB,KAAK,CAACA,SAAS,CAAC;IAChB,IAAI,CAACC,eAAe,GAAIC,IAAI,IAAK;MAC7B,IAAIA,IAAI,KAAKN,QAAQ,EAAE;QACnB;MACJ;MACA,MAAMI,SAAS,GAAG,IAAI,CAACA,SAAS;QAAEG,OAAO,GAAGH,SAAS,CAACI,aAAa;QAAEC,WAAW,GAAGF,OAAO,CAACG,aAAa,CAACC,KAAK,CAACC,IAAI;MACnH,IAAI,CAACH,WAAW,EAAE;QACd;MACJ;MACA,MAAMI,QAAQ,GAAGhB,aAAa,CAACY,WAAW,CAACI,QAAQ,CAAC;MACpD,IAAIA,QAAQ,IAAIZ,WAAW,EAAE;QACzB;MACJ;MACA,MAAMa,KAAK,GAAGhB,aAAa,CAAC,CAACiB,SAAS,EAAE,GAAGN,WAAW,CAACO,MAAM,CAAC,CAAC;QAAEC,YAAY,GAAGH,KAAK,KAAKC,SAAS,GAAGX,SAAS,CAACI,aAAa,CAACU,SAAS,CAACF,MAAM,CAACF,KAAK,CAAC,GAAGC,SAAS;MACjK,KAAKX,SAAS,CAACc,SAAS,CAACN,IAAI,CAACC,QAAQ,EAAET,SAAS,CAACM,aAAa,CAACS,KAAK,EAAEF,YAAY,EAAEH,KAAK,CAAC;IAC/F,CAAC;EACL;EACAM,KAAKA,CAAA,EAAG,CACR;EACAC,IAAIA,CAAA,EAAG,CACP;EACAC,QAAQA,CAAA,EAAG,CACX;EACAC,SAASA,CAAA,EAAG;IACR,OAAO,IAAI;EACf;EACAC,eAAeA,CAACjB,OAAO,EAAE,GAAGkB,OAAO,EAAE;IACjC,IAAI,CAAClB,OAAO,CAACK,IAAI,EAAE;MACfL,OAAO,CAACK,IAAI,GAAG,IAAIb,IAAI,CAAC,CAAC;IAC7B;IACA,KAAK,MAAM2B,MAAM,IAAID,OAAO,EAAE;MAC1BlB,OAAO,CAACK,IAAI,CAACe,IAAI,CAACD,MAAM,EAAEd,IAAI,CAAC;IACnC;EACJ;EACAgB,KAAKA,CAAA,EAAG,CACR;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
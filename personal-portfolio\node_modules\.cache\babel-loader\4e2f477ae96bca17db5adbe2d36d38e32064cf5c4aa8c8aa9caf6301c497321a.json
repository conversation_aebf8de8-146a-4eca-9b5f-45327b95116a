{"ast": null, "code": "export class EmitterShapeReplace {\n  constructor() {\n    this.color = false;\n    this.opacity = false;\n  }\n  load(data) {\n    if (!data) {\n      return;\n    }\n    if (data.color !== undefined) {\n      this.color = data.color;\n    }\n    if (data.opacity !== undefined) {\n      this.opacity = data.opacity;\n    }\n  }\n}", "map": {"version": 3, "names": ["Emitter<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "constructor", "color", "opacity", "load", "data", "undefined"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/plugin-emitters/browser/Options/Classes/EmitterShapeReplace.js"], "sourcesContent": ["export class EmitterShapeReplace {\n    constructor() {\n        this.color = false;\n        this.opacity = false;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.color !== undefined) {\n            this.color = data.color;\n        }\n        if (data.opacity !== undefined) {\n            this.opacity = data.opacity;\n        }\n    }\n}\n"], "mappings": "AAAA,OAAO,MAAMA,mBAAmB,CAAC;EAC7BC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,KAAK,GAAG,KAAK;IAClB,IAAI,CAACC,OAAO,GAAG,KAAK;EACxB;EACAC,IAAIA,CAACC,IAAI,EAAE;IACP,IAAI,CAACA,IAAI,EAAE;MACP;IACJ;IACA,IAAIA,IAAI,CAACH,KAAK,KAAKI,SAAS,EAAE;MAC1B,IAAI,CAACJ,KAAK,GAAGG,IAAI,CAACH,KAAK;IAC3B;IACA,IAAIG,IAAI,CAACF,OAAO,KAAKG,SAAS,EAAE;MAC5B,IAAI,CAACH,OAAO,GAAGE,IAAI,CAACF,OAAO;IAC/B;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
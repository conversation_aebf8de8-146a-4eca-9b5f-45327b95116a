{"ast": null, "code": "import { MoveTrailFill } from \"./MoveTrailFill.js\";\nexport class MoveTrail {\n  constructor() {\n    this.enable = false;\n    this.length = 10;\n    this.fill = new MoveTrailFill();\n  }\n  load(data) {\n    if (!data) {\n      return;\n    }\n    if (data.enable !== undefined) {\n      this.enable = data.enable;\n    }\n    if (data.fill !== undefined) {\n      this.fill.load(data.fill);\n    }\n    if (data.length !== undefined) {\n      this.length = data.length;\n    }\n  }\n}", "map": {"version": 3, "names": ["MoveTrailFill", "MoveTrail", "constructor", "enable", "length", "fill", "load", "data", "undefined"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/engine/browser/Options/Classes/Particles/Move/MoveTrail.js"], "sourcesContent": ["import { MoveTrailFill } from \"./MoveTrailFill.js\";\nexport class MoveTrail {\n    constructor() {\n        this.enable = false;\n        this.length = 10;\n        this.fill = new MoveTrailFill();\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.enable !== undefined) {\n            this.enable = data.enable;\n        }\n        if (data.fill !== undefined) {\n            this.fill.load(data.fill);\n        }\n        if (data.length !== undefined) {\n            this.length = data.length;\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,oBAAoB;AAClD,OAAO,MAAMC,SAAS,CAAC;EACnBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,MAAM,GAAG,KAAK;IACnB,IAAI,CAACC,MAAM,GAAG,EAAE;IAChB,IAAI,CAACC,IAAI,GAAG,IAAIL,aAAa,CAAC,CAAC;EACnC;EACAM,IAAIA,CAACC,IAAI,EAAE;IACP,IAAI,CAACA,IAAI,EAAE;MACP;IACJ;IACA,IAAIA,IAAI,CAACJ,MAAM,KAAKK,SAAS,EAAE;MAC3B,IAAI,CAACL,MAAM,GAAGI,IAAI,CAACJ,MAAM;IAC7B;IACA,IAAII,IAAI,CAACF,IAAI,KAAKG,SAAS,EAAE;MACzB,IAAI,CAACH,IAAI,CAACC,IAAI,CAACC,IAAI,CAACF,IAAI,CAAC;IAC7B;IACA,IAAIE,IAAI,CAACH,MAAM,KAAKI,SAAS,EAAE;MAC3B,IAAI,CAACJ,MAAM,GAAGG,IAAI,CAACH,MAAM;IAC7B;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
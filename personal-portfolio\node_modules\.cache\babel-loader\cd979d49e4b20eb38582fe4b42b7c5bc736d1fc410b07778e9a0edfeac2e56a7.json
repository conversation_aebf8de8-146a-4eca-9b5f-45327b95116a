{"ast": null, "code": "import { setRangeValue } from \"../../../../Utils/NumberUtils.js\";\nexport class MoveGravity {\n  constructor() {\n    this.acceleration = 9.81;\n    this.enable = false;\n    this.inverse = false;\n    this.maxSpeed = 50;\n  }\n  load(data) {\n    if (!data) {\n      return;\n    }\n    if (data.acceleration !== undefined) {\n      this.acceleration = setRangeValue(data.acceleration);\n    }\n    if (data.enable !== undefined) {\n      this.enable = data.enable;\n    }\n    if (data.inverse !== undefined) {\n      this.inverse = data.inverse;\n    }\n    if (data.maxSpeed !== undefined) {\n      this.maxSpeed = setRangeValue(data.maxSpeed);\n    }\n  }\n}", "map": {"version": 3, "names": ["setRangeValue", "MoveGravity", "constructor", "acceleration", "enable", "inverse", "maxSpeed", "load", "data", "undefined"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/engine/browser/Options/Classes/Particles/Move/MoveGravity.js"], "sourcesContent": ["import { setRangeValue } from \"../../../../Utils/NumberUtils.js\";\nexport class MoveGravity {\n    constructor() {\n        this.acceleration = 9.81;\n        this.enable = false;\n        this.inverse = false;\n        this.maxSpeed = 50;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.acceleration !== undefined) {\n            this.acceleration = setRangeValue(data.acceleration);\n        }\n        if (data.enable !== undefined) {\n            this.enable = data.enable;\n        }\n        if (data.inverse !== undefined) {\n            this.inverse = data.inverse;\n        }\n        if (data.maxSpeed !== undefined) {\n            this.maxSpeed = setRangeValue(data.maxSpeed);\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,kCAAkC;AAChE,OAAO,MAAMC,WAAW,CAAC;EACrBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,MAAM,GAAG,KAAK;IACnB,IAAI,CAACC,OAAO,GAAG,KAAK;IACpB,IAAI,CAACC,QAAQ,GAAG,EAAE;EACtB;EACAC,IAAIA,CAACC,IAAI,EAAE;IACP,IAAI,CAACA,IAAI,EAAE;MACP;IACJ;IACA,IAAIA,IAAI,CAACL,YAAY,KAAKM,SAAS,EAAE;MACjC,IAAI,CAACN,YAAY,GAAGH,aAAa,CAACQ,IAAI,CAACL,YAAY,CAAC;IACxD;IACA,IAAIK,IAAI,CAACJ,MAAM,KAAKK,SAAS,EAAE;MAC3B,IAAI,CAACL,MAAM,GAAGI,IAAI,CAACJ,MAAM;IAC7B;IACA,IAAII,IAAI,CAACH,OAAO,KAAKI,SAAS,EAAE;MAC5B,IAAI,CAACJ,OAAO,GAAGG,IAAI,CAACH,OAAO;IAC/B;IACA,IAAIG,IAAI,CAACF,QAAQ,KAAKG,SAAS,EAAE;MAC7B,IAAI,CAACH,QAAQ,GAAGN,aAAa,CAACQ,IAAI,CAACF,QAAQ,CAAC;IAChD;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
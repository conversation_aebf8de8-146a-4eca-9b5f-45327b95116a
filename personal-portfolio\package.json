{"name": "react-portfolio", "version": "0.1.0", "private": true, "dependencies": {"@emailjs/browser": "4.4.1", "@fortawesome/fontawesome-svg-core": "6.6.0", "@fortawesome/free-brands-svg-icons": "6.6.0", "@fortawesome/free-solid-svg-icons": "6.6.0", "@fortawesome/react-fontawesome": "0.2.2", "animate.css": "^4.1.1", "leaflet": "1.9.4", "loaders.css": "0.1.2", "react": "18.3.1", "react-dom": "18.3.1", "react-leaflet": "4.2.1", "react-loaders": "3.0.1", "react-router-dom": "6.28.0", "react-scripts": "5.0.1", "react-spinners": "0.14.1", "react-toastify": "10.0.6", "react-tsparticles": "2.12.2", "sass": "1.80.6", "styled-components": "6.1.13", "TagCloud": "2.5.0", "tsparticles": "3.5.0", "tsparticles-slim": "^2.12.0", "web-vitals": "4.2.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "prettify": "prettier --write ."}, "eslintConfig": {"extends": ["react-app", "react-app/jest", "prettier"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/eslint-parser": "7.25.9", "@babel/preset-react": "7.25.9", "eslint-config-prettier": "9.1.0", "eslint-plugin-import": "2.31.0", "prettier": "3.3.3"}}
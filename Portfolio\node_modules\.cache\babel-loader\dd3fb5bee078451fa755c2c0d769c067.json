{"ast": null, "code": "import { clamp, getDistance, getDistances, getRangeMax, getRangeValue, isInArray, isSsr } from \"../../Utils\";\n\nfunction applyDistance(particle) {\n  const initialPosition = particle.initialPosition;\n  const {\n    dx,\n    dy\n  } = getDistances(initialPosition, particle.position);\n  const dxFixed = Math.abs(dx),\n        dyFixed = Math.abs(dy);\n  const hDistance = particle.retina.maxDistance.horizontal;\n  const vDistance = particle.retina.maxDistance.vertical;\n\n  if (!hDistance && !vDistance) {\n    return;\n  }\n\n  if ((hDistance && dxFixed >= hDistance || vDistance && dyFixed >= vDistance) && !particle.misplaced) {\n    particle.misplaced = !!hDistance && dxFixed > hDistance || !!vDistance && dyFixed > vDistance;\n\n    if (hDistance) {\n      particle.velocity.x = particle.velocity.y / 2 - particle.velocity.x;\n    }\n\n    if (vDistance) {\n      particle.velocity.y = particle.velocity.x / 2 - particle.velocity.y;\n    }\n  } else if ((!hDistance || dxFixed < hDistance) && (!vDistance || dyFixed < vDistance) && particle.misplaced) {\n    particle.misplaced = false;\n  } else if (particle.misplaced) {\n    const pos = particle.position,\n          vel = particle.velocity;\n\n    if (hDistance && (pos.x < initialPosition.x && vel.x < 0 || pos.x > initialPosition.x && vel.x > 0)) {\n      vel.x *= -Math.random();\n    }\n\n    if (vDistance && (pos.y < initialPosition.y && vel.y < 0 || pos.y > initialPosition.y && vel.y > 0)) {\n      vel.y *= -Math.random();\n    }\n  }\n}\n\nexport class ParticlesMover {\n  constructor(container) {\n    this.container = container;\n  }\n\n  move(particle, delta) {\n    if (particle.destroyed) {\n      return;\n    }\n\n    this.moveParticle(particle, delta);\n    this.moveParallax(particle);\n  }\n\n  moveParticle(particle, delta) {\n    var _a, _b, _c;\n\n    var _d, _e;\n\n    const particleOptions = particle.options;\n    const moveOptions = particleOptions.move;\n\n    if (!moveOptions.enable) {\n      return;\n    }\n\n    const container = this.container,\n          slowFactor = this.getProximitySpeedFactor(particle),\n          baseSpeed = ((_a = (_d = particle.retina).moveSpeed) !== null && _a !== void 0 ? _a : _d.moveSpeed = getRangeValue(moveOptions.speed) * container.retina.pixelRatio) * container.retina.reduceFactor,\n          moveDrift = (_b = (_e = particle.retina).moveDrift) !== null && _b !== void 0 ? _b : _e.moveDrift = getRangeValue(particle.options.move.drift) * container.retina.pixelRatio,\n          maxSize = getRangeMax(particleOptions.size.value) * container.retina.pixelRatio,\n          sizeFactor = moveOptions.size ? particle.getRadius() / maxSize : 1,\n          diffFactor = 2,\n          speedFactor = sizeFactor * slowFactor * (delta.factor || 1) / diffFactor,\n          moveSpeed = baseSpeed * speedFactor;\n    this.applyPath(particle, delta);\n    const gravityOptions = particle.gravity;\n    const gravityFactor = gravityOptions.enable && gravityOptions.inverse ? -1 : 1;\n\n    if (gravityOptions.enable && moveSpeed) {\n      particle.velocity.y += gravityFactor * (gravityOptions.acceleration * delta.factor) / (60 * moveSpeed);\n    }\n\n    if (moveDrift && moveSpeed) {\n      particle.velocity.x += moveDrift * delta.factor / (60 * moveSpeed);\n    }\n\n    const decay = particle.moveDecay;\n\n    if (decay != 1) {\n      particle.velocity.multTo(decay);\n    }\n\n    const velocity = particle.velocity.mult(moveSpeed);\n    const maxSpeed = (_c = particle.retina.maxSpeed) !== null && _c !== void 0 ? _c : container.retina.maxSpeed;\n\n    if (gravityOptions.enable && maxSpeed > 0 && (!gravityOptions.inverse && velocity.y >= 0 && velocity.y >= maxSpeed || gravityOptions.inverse && velocity.y <= 0 && velocity.y <= -maxSpeed)) {\n      velocity.y = gravityFactor * maxSpeed;\n\n      if (moveSpeed) {\n        particle.velocity.y = velocity.y / moveSpeed;\n      }\n    }\n\n    const zIndexOptions = particle.options.zIndex,\n          zVelocityFactor = (1 - particle.zIndexFactor) ** zIndexOptions.velocityRate;\n\n    if (moveOptions.spin.enable) {\n      this.spin(particle, moveSpeed);\n    } else {\n      if (zVelocityFactor != 1) {\n        velocity.multTo(zVelocityFactor);\n      }\n\n      particle.position.addTo(velocity);\n\n      if (moveOptions.vibrate) {\n        particle.position.x += Math.sin(particle.position.x * Math.cos(particle.position.y));\n        particle.position.y += Math.cos(particle.position.y * Math.sin(particle.position.x));\n      }\n    }\n\n    applyDistance(particle);\n  }\n\n  spin(particle, moveSpeed) {\n    const container = this.container;\n\n    if (!particle.spin) {\n      return;\n    }\n\n    const updateFunc = {\n      x: particle.spin.direction === \"clockwise\" ? Math.cos : Math.sin,\n      y: particle.spin.direction === \"clockwise\" ? Math.sin : Math.cos\n    };\n    particle.position.x = particle.spin.center.x + particle.spin.radius * updateFunc.x(particle.spin.angle);\n    particle.position.y = particle.spin.center.y + particle.spin.radius * updateFunc.y(particle.spin.angle);\n    particle.spin.radius += particle.spin.acceleration;\n    const maxCanvasSize = Math.max(container.canvas.size.width, container.canvas.size.height);\n\n    if (particle.spin.radius > maxCanvasSize / 2) {\n      particle.spin.radius = maxCanvasSize / 2;\n      particle.spin.acceleration *= -1;\n    } else if (particle.spin.radius < 0) {\n      particle.spin.radius = 0;\n      particle.spin.acceleration *= -1;\n    }\n\n    particle.spin.angle += moveSpeed / 100 * (1 - particle.spin.radius / maxCanvasSize);\n  }\n\n  applyPath(particle, delta) {\n    const particlesOptions = particle.options;\n    const pathOptions = particlesOptions.move.path;\n    const pathEnabled = pathOptions.enable;\n\n    if (!pathEnabled) {\n      return;\n    }\n\n    const container = this.container;\n\n    if (particle.lastPathTime <= particle.pathDelay) {\n      particle.lastPathTime += delta.value;\n      return;\n    }\n\n    const path = container.pathGenerator.generate(particle);\n    particle.velocity.addTo(path);\n\n    if (pathOptions.clamp) {\n      particle.velocity.x = clamp(particle.velocity.x, -1, 1);\n      particle.velocity.y = clamp(particle.velocity.y, -1, 1);\n    }\n\n    particle.lastPathTime -= particle.pathDelay;\n  }\n\n  moveParallax(particle) {\n    const container = this.container;\n    const options = container.actualOptions;\n\n    if (isSsr() || !options.interactivity.events.onHover.parallax.enable) {\n      return;\n    }\n\n    const parallaxForce = options.interactivity.events.onHover.parallax.force;\n    const mousePos = container.interactivity.mouse.position;\n\n    if (!mousePos) {\n      return;\n    }\n\n    const canvasCenter = {\n      x: container.canvas.size.width / 2,\n      y: container.canvas.size.height / 2\n    };\n    const parallaxSmooth = options.interactivity.events.onHover.parallax.smooth;\n    const factor = particle.getRadius() / parallaxForce;\n    const tmp = {\n      x: (mousePos.x - canvasCenter.x) * factor,\n      y: (mousePos.y - canvasCenter.y) * factor\n    };\n    particle.offset.x += (tmp.x - particle.offset.x) / parallaxSmooth;\n    particle.offset.y += (tmp.y - particle.offset.y) / parallaxSmooth;\n  }\n\n  getProximitySpeedFactor(particle) {\n    const container = this.container;\n    const options = container.actualOptions;\n    const active = isInArray(\"slow\", options.interactivity.events.onHover.mode);\n\n    if (!active) {\n      return 1;\n    }\n\n    const mousePos = this.container.interactivity.mouse.position;\n\n    if (!mousePos) {\n      return 1;\n    }\n\n    const particlePos = particle.getPosition();\n    const dist = getDistance(mousePos, particlePos);\n    const radius = container.retina.slowModeRadius;\n\n    if (dist > radius) {\n      return 1;\n    }\n\n    const proximityFactor = dist / radius || 0;\n    const slowFactor = options.interactivity.modes.slow.factor;\n    return proximityFactor / slowFactor;\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Core/Utils/ParticlesMover.js"], "names": ["clamp", "getDistance", "getDistances", "getRangeMax", "getRangeValue", "isInArray", "isSsr", "applyDistance", "particle", "initialPosition", "dx", "dy", "position", "dxFixed", "Math", "abs", "dyFixed", "hDistance", "retina", "maxDistance", "horizontal", "vDistance", "vertical", "misplaced", "velocity", "x", "y", "pos", "vel", "random", "ParticlesMover", "constructor", "container", "move", "delta", "destroyed", "moveParticle", "moveParallax", "_a", "_b", "_c", "_d", "_e", "particleOptions", "options", "moveOptions", "enable", "slowFactor", "getProximitySpeedFactor", "baseSpeed", "moveSpeed", "speed", "pixelRatio", "reduceFactor", "moveDrift", "drift", "maxSize", "size", "value", "sizeFactor", "getRadius", "diffFactor", "speedFactor", "factor", "applyPath", "gravityOptions", "gravity", "gravityFactor", "inverse", "acceleration", "decay", "moveDecay", "multTo", "mult", "maxSpeed", "zIndexOptions", "zIndex", "zVelocityFactor", "zIndexFactor", "velocityRate", "spin", "addTo", "vibrate", "sin", "cos", "updateFunc", "direction", "center", "radius", "angle", "maxCanvasSize", "max", "canvas", "width", "height", "particlesOptions", "pathOptions", "path", "pathEnabled", "lastPathTime", "pathDelay", "pathGenerator", "generate", "actualOptions", "interactivity", "events", "onHover", "parallax", "parallaxForce", "force", "mousePos", "mouse", "canvasCenter", "parallaxSmooth", "smooth", "tmp", "offset", "active", "mode", "particlePos", "getPosition", "dist", "slowModeRadius", "proximityFactor", "modes", "slow"], "mappings": "AAAA,SAASA,KAAT,EAAgBC,WAAhB,EAA6BC,YAA7B,EAA2CC,WAA3C,EAAwDC,aAAxD,EAAuEC,SAAvE,EAAkFC,KAAlF,QAA+F,aAA/F;;AACA,SAASC,aAAT,CAAuBC,QAAvB,EAAiC;AAC7B,QAAMC,eAAe,GAAGD,QAAQ,CAACC,eAAjC;AACA,QAAM;AAAEC,IAAAA,EAAF;AAAMC,IAAAA;AAAN,MAAaT,YAAY,CAACO,eAAD,EAAkBD,QAAQ,CAACI,QAA3B,CAA/B;AACA,QAAMC,OAAO,GAAGC,IAAI,CAACC,GAAL,CAASL,EAAT,CAAhB;AAAA,QAA8BM,OAAO,GAAGF,IAAI,CAACC,GAAL,CAASJ,EAAT,CAAxC;AACA,QAAMM,SAAS,GAAGT,QAAQ,CAACU,MAAT,CAAgBC,WAAhB,CAA4BC,UAA9C;AACA,QAAMC,SAAS,GAAGb,QAAQ,CAACU,MAAT,CAAgBC,WAAhB,CAA4BG,QAA9C;;AACA,MAAI,CAACL,SAAD,IAAc,CAACI,SAAnB,EAA8B;AAC1B;AACH;;AACD,MAAI,CAAEJ,SAAS,IAAIJ,OAAO,IAAII,SAAzB,IAAwCI,SAAS,IAAIL,OAAO,IAAIK,SAAjE,KAAgF,CAACb,QAAQ,CAACe,SAA9F,EAAyG;AACrGf,IAAAA,QAAQ,CAACe,SAAT,GAAsB,CAAC,CAACN,SAAF,IAAeJ,OAAO,GAAGI,SAA1B,IAAyC,CAAC,CAACI,SAAF,IAAeL,OAAO,GAAGK,SAAvF;;AACA,QAAIJ,SAAJ,EAAe;AACXT,MAAAA,QAAQ,CAACgB,QAAT,CAAkBC,CAAlB,GAAsBjB,QAAQ,CAACgB,QAAT,CAAkBE,CAAlB,GAAsB,CAAtB,GAA0BlB,QAAQ,CAACgB,QAAT,CAAkBC,CAAlE;AACH;;AACD,QAAIJ,SAAJ,EAAe;AACXb,MAAAA,QAAQ,CAACgB,QAAT,CAAkBE,CAAlB,GAAsBlB,QAAQ,CAACgB,QAAT,CAAkBC,CAAlB,GAAsB,CAAtB,GAA0BjB,QAAQ,CAACgB,QAAT,CAAkBE,CAAlE;AACH;AACJ,GARD,MASK,IAAI,CAAC,CAACT,SAAD,IAAcJ,OAAO,GAAGI,SAAzB,MAAwC,CAACI,SAAD,IAAcL,OAAO,GAAGK,SAAhE,KAA8Eb,QAAQ,CAACe,SAA3F,EAAsG;AACvGf,IAAAA,QAAQ,CAACe,SAAT,GAAqB,KAArB;AACH,GAFI,MAGA,IAAIf,QAAQ,CAACe,SAAb,EAAwB;AACzB,UAAMI,GAAG,GAAGnB,QAAQ,CAACI,QAArB;AAAA,UAA+BgB,GAAG,GAAGpB,QAAQ,CAACgB,QAA9C;;AACA,QAAIP,SAAS,KAAMU,GAAG,CAACF,CAAJ,GAAQhB,eAAe,CAACgB,CAAxB,IAA6BG,GAAG,CAACH,CAAJ,GAAQ,CAAtC,IAA6CE,GAAG,CAACF,CAAJ,GAAQhB,eAAe,CAACgB,CAAxB,IAA6BG,GAAG,CAACH,CAAJ,GAAQ,CAAvF,CAAb,EAAyG;AACrGG,MAAAA,GAAG,CAACH,CAAJ,IAAS,CAACX,IAAI,CAACe,MAAL,EAAV;AACH;;AACD,QAAIR,SAAS,KAAMM,GAAG,CAACD,CAAJ,GAAQjB,eAAe,CAACiB,CAAxB,IAA6BE,GAAG,CAACF,CAAJ,GAAQ,CAAtC,IAA6CC,GAAG,CAACD,CAAJ,GAAQjB,eAAe,CAACiB,CAAxB,IAA6BE,GAAG,CAACF,CAAJ,GAAQ,CAAvF,CAAb,EAAyG;AACrGE,MAAAA,GAAG,CAACF,CAAJ,IAAS,CAACZ,IAAI,CAACe,MAAL,EAAV;AACH;AACJ;AACJ;;AACD,OAAO,MAAMC,cAAN,CAAqB;AACxBC,EAAAA,WAAW,CAACC,SAAD,EAAY;AACnB,SAAKA,SAAL,GAAiBA,SAAjB;AACH;;AACDC,EAAAA,IAAI,CAACzB,QAAD,EAAW0B,KAAX,EAAkB;AAClB,QAAI1B,QAAQ,CAAC2B,SAAb,EAAwB;AACpB;AACH;;AACD,SAAKC,YAAL,CAAkB5B,QAAlB,EAA4B0B,KAA5B;AACA,SAAKG,YAAL,CAAkB7B,QAAlB;AACH;;AACD4B,EAAAA,YAAY,CAAC5B,QAAD,EAAW0B,KAAX,EAAkB;AAC1B,QAAII,EAAJ,EAAQC,EAAR,EAAYC,EAAZ;;AACA,QAAIC,EAAJ,EAAQC,EAAR;;AACA,UAAMC,eAAe,GAAGnC,QAAQ,CAACoC,OAAjC;AACA,UAAMC,WAAW,GAAGF,eAAe,CAACV,IAApC;;AACA,QAAI,CAACY,WAAW,CAACC,MAAjB,EAAyB;AACrB;AACH;;AACD,UAAMd,SAAS,GAAG,KAAKA,SAAvB;AAAA,UAAkCe,UAAU,GAAG,KAAKC,uBAAL,CAA6BxC,QAA7B,CAA/C;AAAA,UAAuFyC,SAAS,GAAG,CAAC,CAACX,EAAE,GAAG,CAACG,EAAE,GAAGjC,QAAQ,CAACU,MAAf,EAAuBgC,SAA7B,MAA4C,IAA5C,IAAoDZ,EAAE,KAAK,KAAK,CAAhE,GAAoEA,EAApE,GAA0EG,EAAE,CAACS,SAAH,GAAe9C,aAAa,CAACyC,WAAW,CAACM,KAAb,CAAb,GAAmCnB,SAAS,CAACd,MAAV,CAAiBkC,UAA9I,IAC/FpB,SAAS,CAACd,MAAV,CAAiBmC,YADrB;AAAA,UACmCC,SAAS,GAAI,CAACf,EAAE,GAAG,CAACG,EAAE,GAAGlC,QAAQ,CAACU,MAAf,EAAuBoC,SAA7B,MAA4C,IAA5C,IAAoDf,EAAE,KAAK,KAAK,CAAhE,GAAoEA,EAApE,GAA0EG,EAAE,CAACY,SAAH,GAAelD,aAAa,CAACI,QAAQ,CAACoC,OAAT,CAAiBX,IAAjB,CAAsBsB,KAAvB,CAAb,GAA6CvB,SAAS,CAACd,MAAV,CAAiBkC,UADvM;AAAA,UACqNI,OAAO,GAAGrD,WAAW,CAACwC,eAAe,CAACc,IAAhB,CAAqBC,KAAtB,CAAX,GAA0C1B,SAAS,CAACd,MAAV,CAAiBkC,UAD1R;AAAA,UACsSO,UAAU,GAAGd,WAAW,CAACY,IAAZ,GAAmBjD,QAAQ,CAACoD,SAAT,KAAuBJ,OAA1C,GAAoD,CADvW;AAAA,UAC0WK,UAAU,GAAG,CADvX;AAAA,UAC0XC,WAAW,GAAIH,UAAU,GAAGZ,UAAb,IAA2Bb,KAAK,CAAC6B,MAAN,IAAgB,CAA3C,CAAD,GAAkDF,UAD1b;AAAA,UACscX,SAAS,GAAGD,SAAS,GAAGa,WAD9d;AAEA,SAAKE,SAAL,CAAexD,QAAf,EAAyB0B,KAAzB;AACA,UAAM+B,cAAc,GAAGzD,QAAQ,CAAC0D,OAAhC;AACA,UAAMC,aAAa,GAAGF,cAAc,CAACnB,MAAf,IAAyBmB,cAAc,CAACG,OAAxC,GAAkD,CAAC,CAAnD,GAAuD,CAA7E;;AACA,QAAIH,cAAc,CAACnB,MAAf,IAAyBI,SAA7B,EAAwC;AACpC1C,MAAAA,QAAQ,CAACgB,QAAT,CAAkBE,CAAlB,IAAwByC,aAAa,IAAIF,cAAc,CAACI,YAAf,GAA8BnC,KAAK,CAAC6B,MAAxC,CAAd,IAAkE,KAAKb,SAAvE,CAAvB;AACH;;AACD,QAAII,SAAS,IAAIJ,SAAjB,EAA4B;AACxB1C,MAAAA,QAAQ,CAACgB,QAAT,CAAkBC,CAAlB,IAAwB6B,SAAS,GAAGpB,KAAK,CAAC6B,MAAnB,IAA8B,KAAKb,SAAnC,CAAvB;AACH;;AACD,UAAMoB,KAAK,GAAG9D,QAAQ,CAAC+D,SAAvB;;AACA,QAAID,KAAK,IAAI,CAAb,EAAgB;AACZ9D,MAAAA,QAAQ,CAACgB,QAAT,CAAkBgD,MAAlB,CAAyBF,KAAzB;AACH;;AACD,UAAM9C,QAAQ,GAAGhB,QAAQ,CAACgB,QAAT,CAAkBiD,IAAlB,CAAuBvB,SAAvB,CAAjB;AACA,UAAMwB,QAAQ,GAAG,CAAClC,EAAE,GAAGhC,QAAQ,CAACU,MAAT,CAAgBwD,QAAtB,MAAoC,IAApC,IAA4ClC,EAAE,KAAK,KAAK,CAAxD,GAA4DA,EAA5D,GAAiER,SAAS,CAACd,MAAV,CAAiBwD,QAAnG;;AACA,QAAIT,cAAc,CAACnB,MAAf,IACA4B,QAAQ,GAAG,CADX,KAEE,CAACT,cAAc,CAACG,OAAhB,IAA2B5C,QAAQ,CAACE,CAAT,IAAc,CAAzC,IAA8CF,QAAQ,CAACE,CAAT,IAAcgD,QAA7D,IACIT,cAAc,CAACG,OAAf,IAA0B5C,QAAQ,CAACE,CAAT,IAAc,CAAxC,IAA6CF,QAAQ,CAACE,CAAT,IAAc,CAACgD,QAHjE,CAAJ,EAGiF;AAC7ElD,MAAAA,QAAQ,CAACE,CAAT,GAAayC,aAAa,GAAGO,QAA7B;;AACA,UAAIxB,SAAJ,EAAe;AACX1C,QAAAA,QAAQ,CAACgB,QAAT,CAAkBE,CAAlB,GAAsBF,QAAQ,CAACE,CAAT,GAAawB,SAAnC;AACH;AACJ;;AACD,UAAMyB,aAAa,GAAGnE,QAAQ,CAACoC,OAAT,CAAiBgC,MAAvC;AAAA,UAA+CC,eAAe,GAAG,CAAC,IAAIrE,QAAQ,CAACsE,YAAd,KAA+BH,aAAa,CAACI,YAA9G;;AACA,QAAIlC,WAAW,CAACmC,IAAZ,CAAiBlC,MAArB,EAA6B;AACzB,WAAKkC,IAAL,CAAUxE,QAAV,EAAoB0C,SAApB;AACH,KAFD,MAGK;AACD,UAAI2B,eAAe,IAAI,CAAvB,EAA0B;AACtBrD,QAAAA,QAAQ,CAACgD,MAAT,CAAgBK,eAAhB;AACH;;AACDrE,MAAAA,QAAQ,CAACI,QAAT,CAAkBqE,KAAlB,CAAwBzD,QAAxB;;AACA,UAAIqB,WAAW,CAACqC,OAAhB,EAAyB;AACrB1E,QAAAA,QAAQ,CAACI,QAAT,CAAkBa,CAAlB,IAAuBX,IAAI,CAACqE,GAAL,CAAS3E,QAAQ,CAACI,QAAT,CAAkBa,CAAlB,GAAsBX,IAAI,CAACsE,GAAL,CAAS5E,QAAQ,CAACI,QAAT,CAAkBc,CAA3B,CAA/B,CAAvB;AACAlB,QAAAA,QAAQ,CAACI,QAAT,CAAkBc,CAAlB,IAAuBZ,IAAI,CAACsE,GAAL,CAAS5E,QAAQ,CAACI,QAAT,CAAkBc,CAAlB,GAAsBZ,IAAI,CAACqE,GAAL,CAAS3E,QAAQ,CAACI,QAAT,CAAkBa,CAA3B,CAA/B,CAAvB;AACH;AACJ;;AACDlB,IAAAA,aAAa,CAACC,QAAD,CAAb;AACH;;AACDwE,EAAAA,IAAI,CAACxE,QAAD,EAAW0C,SAAX,EAAsB;AACtB,UAAMlB,SAAS,GAAG,KAAKA,SAAvB;;AACA,QAAI,CAACxB,QAAQ,CAACwE,IAAd,EAAoB;AAChB;AACH;;AACD,UAAMK,UAAU,GAAG;AACf5D,MAAAA,CAAC,EAAEjB,QAAQ,CAACwE,IAAT,CAAcM,SAAd,KAA4B,WAA5B,GAA0CxE,IAAI,CAACsE,GAA/C,GAAqDtE,IAAI,CAACqE,GAD9C;AAEfzD,MAAAA,CAAC,EAAElB,QAAQ,CAACwE,IAAT,CAAcM,SAAd,KAA4B,WAA5B,GAA0CxE,IAAI,CAACqE,GAA/C,GAAqDrE,IAAI,CAACsE;AAF9C,KAAnB;AAIA5E,IAAAA,QAAQ,CAACI,QAAT,CAAkBa,CAAlB,GAAsBjB,QAAQ,CAACwE,IAAT,CAAcO,MAAd,CAAqB9D,CAArB,GAAyBjB,QAAQ,CAACwE,IAAT,CAAcQ,MAAd,GAAuBH,UAAU,CAAC5D,CAAX,CAAajB,QAAQ,CAACwE,IAAT,CAAcS,KAA3B,CAAtE;AACAjF,IAAAA,QAAQ,CAACI,QAAT,CAAkBc,CAAlB,GAAsBlB,QAAQ,CAACwE,IAAT,CAAcO,MAAd,CAAqB7D,CAArB,GAAyBlB,QAAQ,CAACwE,IAAT,CAAcQ,MAAd,GAAuBH,UAAU,CAAC3D,CAAX,CAAalB,QAAQ,CAACwE,IAAT,CAAcS,KAA3B,CAAtE;AACAjF,IAAAA,QAAQ,CAACwE,IAAT,CAAcQ,MAAd,IAAwBhF,QAAQ,CAACwE,IAAT,CAAcX,YAAtC;AACA,UAAMqB,aAAa,GAAG5E,IAAI,CAAC6E,GAAL,CAAS3D,SAAS,CAAC4D,MAAV,CAAiBnC,IAAjB,CAAsBoC,KAA/B,EAAsC7D,SAAS,CAAC4D,MAAV,CAAiBnC,IAAjB,CAAsBqC,MAA5D,CAAtB;;AACA,QAAItF,QAAQ,CAACwE,IAAT,CAAcQ,MAAd,GAAuBE,aAAa,GAAG,CAA3C,EAA8C;AAC1ClF,MAAAA,QAAQ,CAACwE,IAAT,CAAcQ,MAAd,GAAuBE,aAAa,GAAG,CAAvC;AACAlF,MAAAA,QAAQ,CAACwE,IAAT,CAAcX,YAAd,IAA8B,CAAC,CAA/B;AACH,KAHD,MAIK,IAAI7D,QAAQ,CAACwE,IAAT,CAAcQ,MAAd,GAAuB,CAA3B,EAA8B;AAC/BhF,MAAAA,QAAQ,CAACwE,IAAT,CAAcQ,MAAd,GAAuB,CAAvB;AACAhF,MAAAA,QAAQ,CAACwE,IAAT,CAAcX,YAAd,IAA8B,CAAC,CAA/B;AACH;;AACD7D,IAAAA,QAAQ,CAACwE,IAAT,CAAcS,KAAd,IAAwBvC,SAAS,GAAG,GAAb,IAAqB,IAAI1C,QAAQ,CAACwE,IAAT,CAAcQ,MAAd,GAAuBE,aAAhD,CAAvB;AACH;;AACD1B,EAAAA,SAAS,CAACxD,QAAD,EAAW0B,KAAX,EAAkB;AACvB,UAAM6D,gBAAgB,GAAGvF,QAAQ,CAACoC,OAAlC;AACA,UAAMoD,WAAW,GAAGD,gBAAgB,CAAC9D,IAAjB,CAAsBgE,IAA1C;AACA,UAAMC,WAAW,GAAGF,WAAW,CAAClD,MAAhC;;AACA,QAAI,CAACoD,WAAL,EAAkB;AACd;AACH;;AACD,UAAMlE,SAAS,GAAG,KAAKA,SAAvB;;AACA,QAAIxB,QAAQ,CAAC2F,YAAT,IAAyB3F,QAAQ,CAAC4F,SAAtC,EAAiD;AAC7C5F,MAAAA,QAAQ,CAAC2F,YAAT,IAAyBjE,KAAK,CAACwB,KAA/B;AACA;AACH;;AACD,UAAMuC,IAAI,GAAGjE,SAAS,CAACqE,aAAV,CAAwBC,QAAxB,CAAiC9F,QAAjC,CAAb;AACAA,IAAAA,QAAQ,CAACgB,QAAT,CAAkByD,KAAlB,CAAwBgB,IAAxB;;AACA,QAAID,WAAW,CAAChG,KAAhB,EAAuB;AACnBQ,MAAAA,QAAQ,CAACgB,QAAT,CAAkBC,CAAlB,GAAsBzB,KAAK,CAACQ,QAAQ,CAACgB,QAAT,CAAkBC,CAAnB,EAAsB,CAAC,CAAvB,EAA0B,CAA1B,CAA3B;AACAjB,MAAAA,QAAQ,CAACgB,QAAT,CAAkBE,CAAlB,GAAsB1B,KAAK,CAACQ,QAAQ,CAACgB,QAAT,CAAkBE,CAAnB,EAAsB,CAAC,CAAvB,EAA0B,CAA1B,CAA3B;AACH;;AACDlB,IAAAA,QAAQ,CAAC2F,YAAT,IAAyB3F,QAAQ,CAAC4F,SAAlC;AACH;;AACD/D,EAAAA,YAAY,CAAC7B,QAAD,EAAW;AACnB,UAAMwB,SAAS,GAAG,KAAKA,SAAvB;AACA,UAAMY,OAAO,GAAGZ,SAAS,CAACuE,aAA1B;;AACA,QAAIjG,KAAK,MAAM,CAACsC,OAAO,CAAC4D,aAAR,CAAsBC,MAAtB,CAA6BC,OAA7B,CAAqCC,QAArC,CAA8C7D,MAA9D,EAAsE;AAClE;AACH;;AACD,UAAM8D,aAAa,GAAGhE,OAAO,CAAC4D,aAAR,CAAsBC,MAAtB,CAA6BC,OAA7B,CAAqCC,QAArC,CAA8CE,KAApE;AACA,UAAMC,QAAQ,GAAG9E,SAAS,CAACwE,aAAV,CAAwBO,KAAxB,CAA8BnG,QAA/C;;AACA,QAAI,CAACkG,QAAL,EAAe;AACX;AACH;;AACD,UAAME,YAAY,GAAG;AACjBvF,MAAAA,CAAC,EAAEO,SAAS,CAAC4D,MAAV,CAAiBnC,IAAjB,CAAsBoC,KAAtB,GAA8B,CADhB;AAEjBnE,MAAAA,CAAC,EAAEM,SAAS,CAAC4D,MAAV,CAAiBnC,IAAjB,CAAsBqC,MAAtB,GAA+B;AAFjB,KAArB;AAIA,UAAMmB,cAAc,GAAGrE,OAAO,CAAC4D,aAAR,CAAsBC,MAAtB,CAA6BC,OAA7B,CAAqCC,QAArC,CAA8CO,MAArE;AACA,UAAMnD,MAAM,GAAGvD,QAAQ,CAACoD,SAAT,KAAuBgD,aAAtC;AACA,UAAMO,GAAG,GAAG;AACR1F,MAAAA,CAAC,EAAE,CAACqF,QAAQ,CAACrF,CAAT,GAAauF,YAAY,CAACvF,CAA3B,IAAgCsC,MAD3B;AAERrC,MAAAA,CAAC,EAAE,CAACoF,QAAQ,CAACpF,CAAT,GAAasF,YAAY,CAACtF,CAA3B,IAAgCqC;AAF3B,KAAZ;AAIAvD,IAAAA,QAAQ,CAAC4G,MAAT,CAAgB3F,CAAhB,IAAqB,CAAC0F,GAAG,CAAC1F,CAAJ,GAAQjB,QAAQ,CAAC4G,MAAT,CAAgB3F,CAAzB,IAA8BwF,cAAnD;AACAzG,IAAAA,QAAQ,CAAC4G,MAAT,CAAgB1F,CAAhB,IAAqB,CAACyF,GAAG,CAACzF,CAAJ,GAAQlB,QAAQ,CAAC4G,MAAT,CAAgB1F,CAAzB,IAA8BuF,cAAnD;AACH;;AACDjE,EAAAA,uBAAuB,CAACxC,QAAD,EAAW;AAC9B,UAAMwB,SAAS,GAAG,KAAKA,SAAvB;AACA,UAAMY,OAAO,GAAGZ,SAAS,CAACuE,aAA1B;AACA,UAAMc,MAAM,GAAGhH,SAAS,CAAC,MAAD,EAASuC,OAAO,CAAC4D,aAAR,CAAsBC,MAAtB,CAA6BC,OAA7B,CAAqCY,IAA9C,CAAxB;;AACA,QAAI,CAACD,MAAL,EAAa;AACT,aAAO,CAAP;AACH;;AACD,UAAMP,QAAQ,GAAG,KAAK9E,SAAL,CAAewE,aAAf,CAA6BO,KAA7B,CAAmCnG,QAApD;;AACA,QAAI,CAACkG,QAAL,EAAe;AACX,aAAO,CAAP;AACH;;AACD,UAAMS,WAAW,GAAG/G,QAAQ,CAACgH,WAAT,EAApB;AACA,UAAMC,IAAI,GAAGxH,WAAW,CAAC6G,QAAD,EAAWS,WAAX,CAAxB;AACA,UAAM/B,MAAM,GAAGxD,SAAS,CAACd,MAAV,CAAiBwG,cAAhC;;AACA,QAAID,IAAI,GAAGjC,MAAX,EAAmB;AACf,aAAO,CAAP;AACH;;AACD,UAAMmC,eAAe,GAAGF,IAAI,GAAGjC,MAAP,IAAiB,CAAzC;AACA,UAAMzC,UAAU,GAAGH,OAAO,CAAC4D,aAAR,CAAsBoB,KAAtB,CAA4BC,IAA5B,CAAiC9D,MAApD;AACA,WAAO4D,eAAe,GAAG5E,UAAzB;AACH;;AApJuB", "sourcesContent": ["import { clamp, getDistance, getDistances, getRangeMax, getRangeValue, isInArray, isSsr } from \"../../Utils\";\nfunction applyDistance(particle) {\n    const initialPosition = particle.initialPosition;\n    const { dx, dy } = getDistances(initialPosition, particle.position);\n    const dxFixed = Math.abs(dx), dyFixed = Math.abs(dy);\n    const hDistance = particle.retina.maxDistance.horizontal;\n    const vDistance = particle.retina.maxDistance.vertical;\n    if (!hDistance && !vDistance) {\n        return;\n    }\n    if (((hDistance && dxFixed >= hDistance) || (vDistance && dyFixed >= vDistance)) && !particle.misplaced) {\n        particle.misplaced = (!!hDistance && dxFixed > hDistance) || (!!vDistance && dyFixed > vDistance);\n        if (hDistance) {\n            particle.velocity.x = particle.velocity.y / 2 - particle.velocity.x;\n        }\n        if (vDistance) {\n            particle.velocity.y = particle.velocity.x / 2 - particle.velocity.y;\n        }\n    }\n    else if ((!hDistance || dxFixed < hDistance) && (!vDistance || dyFixed < vDistance) && particle.misplaced) {\n        particle.misplaced = false;\n    }\n    else if (particle.misplaced) {\n        const pos = particle.position, vel = particle.velocity;\n        if (hDistance && ((pos.x < initialPosition.x && vel.x < 0) || (pos.x > initialPosition.x && vel.x > 0))) {\n            vel.x *= -Math.random();\n        }\n        if (vDistance && ((pos.y < initialPosition.y && vel.y < 0) || (pos.y > initialPosition.y && vel.y > 0))) {\n            vel.y *= -Math.random();\n        }\n    }\n}\nexport class ParticlesMover {\n    constructor(container) {\n        this.container = container;\n    }\n    move(particle, delta) {\n        if (particle.destroyed) {\n            return;\n        }\n        this.moveParticle(particle, delta);\n        this.moveParallax(particle);\n    }\n    moveParticle(particle, delta) {\n        var _a, _b, _c;\n        var _d, _e;\n        const particleOptions = particle.options;\n        const moveOptions = particleOptions.move;\n        if (!moveOptions.enable) {\n            return;\n        }\n        const container = this.container, slowFactor = this.getProximitySpeedFactor(particle), baseSpeed = ((_a = (_d = particle.retina).moveSpeed) !== null && _a !== void 0 ? _a : (_d.moveSpeed = getRangeValue(moveOptions.speed) * container.retina.pixelRatio)) *\n            container.retina.reduceFactor, moveDrift = ((_b = (_e = particle.retina).moveDrift) !== null && _b !== void 0 ? _b : (_e.moveDrift = getRangeValue(particle.options.move.drift) * container.retina.pixelRatio)), maxSize = getRangeMax(particleOptions.size.value) * container.retina.pixelRatio, sizeFactor = moveOptions.size ? particle.getRadius() / maxSize : 1, diffFactor = 2, speedFactor = (sizeFactor * slowFactor * (delta.factor || 1)) / diffFactor, moveSpeed = baseSpeed * speedFactor;\n        this.applyPath(particle, delta);\n        const gravityOptions = particle.gravity;\n        const gravityFactor = gravityOptions.enable && gravityOptions.inverse ? -1 : 1;\n        if (gravityOptions.enable && moveSpeed) {\n            particle.velocity.y += (gravityFactor * (gravityOptions.acceleration * delta.factor)) / (60 * moveSpeed);\n        }\n        if (moveDrift && moveSpeed) {\n            particle.velocity.x += (moveDrift * delta.factor) / (60 * moveSpeed);\n        }\n        const decay = particle.moveDecay;\n        if (decay != 1) {\n            particle.velocity.multTo(decay);\n        }\n        const velocity = particle.velocity.mult(moveSpeed);\n        const maxSpeed = (_c = particle.retina.maxSpeed) !== null && _c !== void 0 ? _c : container.retina.maxSpeed;\n        if (gravityOptions.enable &&\n            maxSpeed > 0 &&\n            ((!gravityOptions.inverse && velocity.y >= 0 && velocity.y >= maxSpeed) ||\n                (gravityOptions.inverse && velocity.y <= 0 && velocity.y <= -maxSpeed))) {\n            velocity.y = gravityFactor * maxSpeed;\n            if (moveSpeed) {\n                particle.velocity.y = velocity.y / moveSpeed;\n            }\n        }\n        const zIndexOptions = particle.options.zIndex, zVelocityFactor = (1 - particle.zIndexFactor) ** zIndexOptions.velocityRate;\n        if (moveOptions.spin.enable) {\n            this.spin(particle, moveSpeed);\n        }\n        else {\n            if (zVelocityFactor != 1) {\n                velocity.multTo(zVelocityFactor);\n            }\n            particle.position.addTo(velocity);\n            if (moveOptions.vibrate) {\n                particle.position.x += Math.sin(particle.position.x * Math.cos(particle.position.y));\n                particle.position.y += Math.cos(particle.position.y * Math.sin(particle.position.x));\n            }\n        }\n        applyDistance(particle);\n    }\n    spin(particle, moveSpeed) {\n        const container = this.container;\n        if (!particle.spin) {\n            return;\n        }\n        const updateFunc = {\n            x: particle.spin.direction === \"clockwise\" ? Math.cos : Math.sin,\n            y: particle.spin.direction === \"clockwise\" ? Math.sin : Math.cos,\n        };\n        particle.position.x = particle.spin.center.x + particle.spin.radius * updateFunc.x(particle.spin.angle);\n        particle.position.y = particle.spin.center.y + particle.spin.radius * updateFunc.y(particle.spin.angle);\n        particle.spin.radius += particle.spin.acceleration;\n        const maxCanvasSize = Math.max(container.canvas.size.width, container.canvas.size.height);\n        if (particle.spin.radius > maxCanvasSize / 2) {\n            particle.spin.radius = maxCanvasSize / 2;\n            particle.spin.acceleration *= -1;\n        }\n        else if (particle.spin.radius < 0) {\n            particle.spin.radius = 0;\n            particle.spin.acceleration *= -1;\n        }\n        particle.spin.angle += (moveSpeed / 100) * (1 - particle.spin.radius / maxCanvasSize);\n    }\n    applyPath(particle, delta) {\n        const particlesOptions = particle.options;\n        const pathOptions = particlesOptions.move.path;\n        const pathEnabled = pathOptions.enable;\n        if (!pathEnabled) {\n            return;\n        }\n        const container = this.container;\n        if (particle.lastPathTime <= particle.pathDelay) {\n            particle.lastPathTime += delta.value;\n            return;\n        }\n        const path = container.pathGenerator.generate(particle);\n        particle.velocity.addTo(path);\n        if (pathOptions.clamp) {\n            particle.velocity.x = clamp(particle.velocity.x, -1, 1);\n            particle.velocity.y = clamp(particle.velocity.y, -1, 1);\n        }\n        particle.lastPathTime -= particle.pathDelay;\n    }\n    moveParallax(particle) {\n        const container = this.container;\n        const options = container.actualOptions;\n        if (isSsr() || !options.interactivity.events.onHover.parallax.enable) {\n            return;\n        }\n        const parallaxForce = options.interactivity.events.onHover.parallax.force;\n        const mousePos = container.interactivity.mouse.position;\n        if (!mousePos) {\n            return;\n        }\n        const canvasCenter = {\n            x: container.canvas.size.width / 2,\n            y: container.canvas.size.height / 2,\n        };\n        const parallaxSmooth = options.interactivity.events.onHover.parallax.smooth;\n        const factor = particle.getRadius() / parallaxForce;\n        const tmp = {\n            x: (mousePos.x - canvasCenter.x) * factor,\n            y: (mousePos.y - canvasCenter.y) * factor,\n        };\n        particle.offset.x += (tmp.x - particle.offset.x) / parallaxSmooth;\n        particle.offset.y += (tmp.y - particle.offset.y) / parallaxSmooth;\n    }\n    getProximitySpeedFactor(particle) {\n        const container = this.container;\n        const options = container.actualOptions;\n        const active = isInArray(\"slow\", options.interactivity.events.onHover.mode);\n        if (!active) {\n            return 1;\n        }\n        const mousePos = this.container.interactivity.mouse.position;\n        if (!mousePos) {\n            return 1;\n        }\n        const particlePos = particle.getPosition();\n        const dist = getDistance(mousePos, particlePos);\n        const radius = container.retina.slowModeRadius;\n        if (dist > radius) {\n            return 1;\n        }\n        const proximityFactor = dist / radius || 0;\n        const slowFactor = options.interactivity.modes.slow.factor;\n        return proximityFactor / slowFactor;\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
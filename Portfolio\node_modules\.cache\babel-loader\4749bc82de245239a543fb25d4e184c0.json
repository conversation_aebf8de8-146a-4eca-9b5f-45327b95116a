{"ast": null, "code": "import { OpacityAnimation } from \"./OpacityAnimation\";\nimport { ValueWithRandom } from \"../../ValueWithRandom\";\nimport { setRangeValue } from \"../../../../Utils\";\nexport class Opacity extends ValueWithRandom {\n  constructor() {\n    super();\n    this.animation = new OpacityAnimation();\n    this.random.minimumValue = 0.1;\n    this.value = 1;\n  }\n\n  get anim() {\n    return this.animation;\n  }\n\n  set anim(value) {\n    this.animation = value;\n  }\n\n  load(data) {\n    var _a;\n\n    if (!data) {\n      return;\n    }\n\n    super.load(data);\n    const animation = (_a = data.animation) !== null && _a !== void 0 ? _a : data.anim;\n\n    if (animation !== undefined) {\n      this.animation.load(animation);\n      this.value = setRangeValue(this.value, this.animation.enable ? this.animation.minimumValue : undefined);\n    }\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Options/Classes/Particles/Opacity/Opacity.js"], "names": ["OpacityAnimation", "ValueWithRandom", "setRangeValue", "Opacity", "constructor", "animation", "random", "minimumValue", "value", "anim", "load", "data", "_a", "undefined", "enable"], "mappings": "AAAA,SAASA,gBAAT,QAAiC,oBAAjC;AACA,SAASC,eAAT,QAAgC,uBAAhC;AACA,SAASC,aAAT,QAA8B,mBAA9B;AACA,OAAO,MAAMC,OAAN,SAAsBF,eAAtB,CAAsC;AACzCG,EAAAA,WAAW,GAAG;AACV;AACA,SAAKC,SAAL,GAAiB,IAAIL,gBAAJ,EAAjB;AACA,SAAKM,MAAL,CAAYC,YAAZ,GAA2B,GAA3B;AACA,SAAKC,KAAL,GAAa,CAAb;AACH;;AACO,MAAJC,IAAI,GAAG;AACP,WAAO,KAAKJ,SAAZ;AACH;;AACO,MAAJI,IAAI,CAACD,KAAD,EAAQ;AACZ,SAAKH,SAAL,GAAiBG,KAAjB;AACH;;AACDE,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,QAAIC,EAAJ;;AACA,QAAI,CAACD,IAAL,EAAW;AACP;AACH;;AACD,UAAMD,IAAN,CAAWC,IAAX;AACA,UAAMN,SAAS,GAAG,CAACO,EAAE,GAAGD,IAAI,CAACN,SAAX,MAA0B,IAA1B,IAAkCO,EAAE,KAAK,KAAK,CAA9C,GAAkDA,EAAlD,GAAuDD,IAAI,CAACF,IAA9E;;AACA,QAAIJ,SAAS,KAAKQ,SAAlB,EAA6B;AACzB,WAAKR,SAAL,CAAeK,IAAf,CAAoBL,SAApB;AACA,WAAKG,KAAL,GAAaN,aAAa,CAAC,KAAKM,KAAN,EAAa,KAAKH,SAAL,CAAeS,MAAf,GAAwB,KAAKT,SAAL,CAAeE,YAAvC,GAAsDM,SAAnE,CAA1B;AACH;AACJ;;AAxBwC", "sourcesContent": ["import { OpacityAnimation } from \"./OpacityAnimation\";\nimport { ValueWithRandom } from \"../../ValueWithRandom\";\nimport { setRangeValue } from \"../../../../Utils\";\nexport class Opacity extends ValueWithRandom {\n    constructor() {\n        super();\n        this.animation = new OpacityAnimation();\n        this.random.minimumValue = 0.1;\n        this.value = 1;\n    }\n    get anim() {\n        return this.animation;\n    }\n    set anim(value) {\n        this.animation = value;\n    }\n    load(data) {\n        var _a;\n        if (!data) {\n            return;\n        }\n        super.load(data);\n        const animation = (_a = data.animation) !== null && _a !== void 0 ? _a : data.anim;\n        if (animation !== undefined) {\n            this.animation.load(animation);\n            this.value = setRangeValue(this.value, this.animation.enable ? this.animation.minimumValue : undefined);\n        }\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
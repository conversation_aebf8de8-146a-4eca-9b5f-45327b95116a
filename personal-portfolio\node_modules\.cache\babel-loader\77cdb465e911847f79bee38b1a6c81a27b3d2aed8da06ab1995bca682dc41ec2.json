{"ast": null, "code": "import { isObject } from \"tsparticles-engine\";\nexport class CircleDrawer {\n  draw(context, particle, radius) {\n    if (!particle.circleRange) {\n      particle.circleRange = {\n        min: 0,\n        max: Math.PI * 2\n      };\n    }\n    const circleRange = particle.circleRange;\n    context.arc(0, 0, radius, circleRange.min, circleRange.max, false);\n  }\n  getSidesCount() {\n    return 12;\n  }\n  particleInit(container, particle) {\n    const shapeData = particle.shapeData,\n      angle = shapeData?.angle ?? {\n        max: 360,\n        min: 0\n      };\n    particle.circleRange = !isObject(angle) ? {\n      min: 0,\n      max: angle * Math.PI / 180\n    } : {\n      min: angle.min * Math.PI / 180,\n      max: angle.max * Math.PI / 180\n    };\n  }\n}", "map": {"version": 3, "names": ["isObject", "CircleDrawer", "draw", "context", "particle", "radius", "circleRange", "min", "max", "Math", "PI", "arc", "getSidesCount", "particleInit", "container", "shapeData", "angle"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/tsparticles-shape-circle/esm/CircleDrawer.js"], "sourcesContent": ["import { isObject } from \"tsparticles-engine\";\nexport class CircleDrawer {\n    draw(context, particle, radius) {\n        if (!particle.circleRange) {\n            particle.circleRange = { min: 0, max: Math.PI * 2 };\n        }\n        const circleRange = particle.circleRange;\n        context.arc(0, 0, radius, circleRange.min, circleRange.max, false);\n    }\n    getSidesCount() {\n        return 12;\n    }\n    particleInit(container, particle) {\n        const shapeData = particle.shapeData, angle = shapeData?.angle ?? {\n            max: 360,\n            min: 0,\n        };\n        particle.circleRange = !isObject(angle)\n            ? {\n                min: 0,\n                max: (angle * Math.PI) / 180,\n            }\n            : { min: (angle.min * Math.PI) / 180, max: (angle.max * Math.PI) / 180 };\n    }\n}\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,oBAAoB;AAC7C,OAAO,MAAMC,YAAY,CAAC;EACtBC,IAAIA,CAACC,OAAO,EAAEC,QAAQ,EAAEC,MAAM,EAAE;IAC5B,IAAI,CAACD,QAAQ,CAACE,WAAW,EAAE;MACvBF,QAAQ,CAACE,WAAW,GAAG;QAAEC,GAAG,EAAE,CAAC;QAAEC,GAAG,EAAEC,IAAI,CAACC,EAAE,GAAG;MAAE,CAAC;IACvD;IACA,MAAMJ,WAAW,GAAGF,QAAQ,CAACE,WAAW;IACxCH,OAAO,CAACQ,GAAG,CAAC,CAAC,EAAE,CAAC,EAAEN,MAAM,EAAEC,WAAW,CAACC,GAAG,EAAED,WAAW,CAACE,GAAG,EAAE,KAAK,CAAC;EACtE;EACAI,aAAaA,CAAA,EAAG;IACZ,OAAO,EAAE;EACb;EACAC,YAAYA,CAACC,SAAS,EAAEV,QAAQ,EAAE;IAC9B,MAAMW,SAAS,GAAGX,QAAQ,CAACW,SAAS;MAAEC,KAAK,GAAGD,SAAS,EAAEC,KAAK,IAAI;QAC9DR,GAAG,EAAE,GAAG;QACRD,GAAG,EAAE;MACT,CAAC;IACDH,QAAQ,CAACE,WAAW,GAAG,CAACN,QAAQ,CAACgB,KAAK,CAAC,GACjC;MACET,GAAG,EAAE,CAAC;MACNC,GAAG,EAAGQ,KAAK,GAAGP,IAAI,CAACC,EAAE,GAAI;IAC7B,CAAC,GACC;MAAEH,GAAG,EAAGS,KAAK,CAACT,GAAG,GAAGE,IAAI,CAACC,EAAE,GAAI,GAAG;MAAEF,GAAG,EAAGQ,KAAK,CAACR,GAAG,GAAGC,IAAI,CAACC,EAAE,GAAI;IAAI,CAAC;EAChF;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
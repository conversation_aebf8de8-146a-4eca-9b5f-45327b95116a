{"ast": null, "code": "import { Circle } from \"./Circle\";\nimport { Rectangle } from \"./Rectangle\";\nexport class CircleWarp extends Circle {\n  constructor(x, y, radius, canvasSize) {\n    super(x, y, radius);\n    this.canvasSize = canvasSize;\n    this.canvasSize = {\n      height: canvasSize.height,\n      width: canvasSize.width\n    };\n  }\n\n  contains(point) {\n    if (super.contains(point)) {\n      return true;\n    }\n\n    const posNE = {\n      x: point.x - this.canvasSize.width,\n      y: point.y\n    };\n\n    if (super.contains(posNE)) {\n      return true;\n    }\n\n    const posSE = {\n      x: point.x - this.canvasSize.width,\n      y: point.y - this.canvasSize.height\n    };\n\n    if (super.contains(posSE)) {\n      return true;\n    }\n\n    const posSW = {\n      x: point.x,\n      y: point.y - this.canvasSize.height\n    };\n    return super.contains(posSW);\n  }\n\n  intersects(range) {\n    if (super.intersects(range)) {\n      return true;\n    }\n\n    const rect = range;\n    const circle = range;\n    const newPos = {\n      x: range.position.x - this.canvasSize.width,\n      y: range.position.y - this.canvasSize.height\n    };\n\n    if (circle.radius !== undefined) {\n      const biggerCircle = new Circle(newPos.x, newPos.y, circle.radius * 2);\n      return super.intersects(biggerCircle);\n    } else if (rect.size !== undefined) {\n      const rectSW = new Rectangle(newPos.x, newPos.y, rect.size.width * 2, rect.size.height * 2);\n      return super.intersects(rectSW);\n    }\n\n    return false;\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Core/Utils/CircleWarp.js"], "names": ["Circle", "Rectangle", "CircleWarp", "constructor", "x", "y", "radius", "canvasSize", "height", "width", "contains", "point", "posNE", "posSE", "posSW", "intersects", "range", "rect", "circle", "newPos", "position", "undefined", "biggerCircle", "size", "rectSW"], "mappings": "AAAA,SAASA,MAAT,QAAuB,UAAvB;AACA,SAASC,SAAT,QAA0B,aAA1B;AACA,OAAO,MAAMC,UAAN,SAAyBF,MAAzB,CAAgC;AACnCG,EAAAA,WAAW,CAACC,CAAD,EAAIC,CAAJ,EAAOC,MAAP,EAAeC,UAAf,EAA2B;AAClC,UAAMH,CAAN,EAASC,CAAT,EAAYC,MAAZ;AACA,SAAKC,UAAL,GAAkBA,UAAlB;AACA,SAAKA,UAAL,GAAkB;AACdC,MAAAA,MAAM,EAAED,UAAU,CAACC,MADL;AAEdC,MAAAA,KAAK,EAAEF,UAAU,CAACE;AAFJ,KAAlB;AAIH;;AACDC,EAAAA,QAAQ,CAACC,KAAD,EAAQ;AACZ,QAAI,MAAMD,QAAN,CAAeC,KAAf,CAAJ,EAA2B;AACvB,aAAO,IAAP;AACH;;AACD,UAAMC,KAAK,GAAG;AACVR,MAAAA,CAAC,EAAEO,KAAK,CAACP,CAAN,GAAU,KAAKG,UAAL,CAAgBE,KADnB;AAEVJ,MAAAA,CAAC,EAAEM,KAAK,CAACN;AAFC,KAAd;;AAIA,QAAI,MAAMK,QAAN,CAAeE,KAAf,CAAJ,EAA2B;AACvB,aAAO,IAAP;AACH;;AACD,UAAMC,KAAK,GAAG;AACVT,MAAAA,CAAC,EAAEO,KAAK,CAACP,CAAN,GAAU,KAAKG,UAAL,CAAgBE,KADnB;AAEVJ,MAAAA,CAAC,EAAEM,KAAK,CAACN,CAAN,GAAU,KAAKE,UAAL,CAAgBC;AAFnB,KAAd;;AAIA,QAAI,MAAME,QAAN,CAAeG,KAAf,CAAJ,EAA2B;AACvB,aAAO,IAAP;AACH;;AACD,UAAMC,KAAK,GAAG;AACVV,MAAAA,CAAC,EAAEO,KAAK,CAACP,CADC;AAEVC,MAAAA,CAAC,EAAEM,KAAK,CAACN,CAAN,GAAU,KAAKE,UAAL,CAAgBC;AAFnB,KAAd;AAIA,WAAO,MAAME,QAAN,CAAeI,KAAf,CAAP;AACH;;AACDC,EAAAA,UAAU,CAACC,KAAD,EAAQ;AACd,QAAI,MAAMD,UAAN,CAAiBC,KAAjB,CAAJ,EAA6B;AACzB,aAAO,IAAP;AACH;;AACD,UAAMC,IAAI,GAAGD,KAAb;AACA,UAAME,MAAM,GAAGF,KAAf;AACA,UAAMG,MAAM,GAAG;AACXf,MAAAA,CAAC,EAAEY,KAAK,CAACI,QAAN,CAAehB,CAAf,GAAmB,KAAKG,UAAL,CAAgBE,KAD3B;AAEXJ,MAAAA,CAAC,EAAEW,KAAK,CAACI,QAAN,CAAef,CAAf,GAAmB,KAAKE,UAAL,CAAgBC;AAF3B,KAAf;;AAIA,QAAIU,MAAM,CAACZ,MAAP,KAAkBe,SAAtB,EAAiC;AAC7B,YAAMC,YAAY,GAAG,IAAItB,MAAJ,CAAWmB,MAAM,CAACf,CAAlB,EAAqBe,MAAM,CAACd,CAA5B,EAA+Ba,MAAM,CAACZ,MAAP,GAAgB,CAA/C,CAArB;AACA,aAAO,MAAMS,UAAN,CAAiBO,YAAjB,CAAP;AACH,KAHD,MAIK,IAAIL,IAAI,CAACM,IAAL,KAAcF,SAAlB,EAA6B;AAC9B,YAAMG,MAAM,GAAG,IAAIvB,SAAJ,CAAckB,MAAM,CAACf,CAArB,EAAwBe,MAAM,CAACd,CAA/B,EAAkCY,IAAI,CAACM,IAAL,CAAUd,KAAV,GAAkB,CAApD,EAAuDQ,IAAI,CAACM,IAAL,CAAUf,MAAV,GAAmB,CAA1E,CAAf;AACA,aAAO,MAAMO,UAAN,CAAiBS,MAAjB,CAAP;AACH;;AACD,WAAO,KAAP;AACH;;AApDkC", "sourcesContent": ["import { Circle } from \"./Circle\";\nimport { Rectangle } from \"./Rectangle\";\nexport class CircleWarp extends Circle {\n    constructor(x, y, radius, canvasSize) {\n        super(x, y, radius);\n        this.canvasSize = canvasSize;\n        this.canvasSize = {\n            height: canvasSize.height,\n            width: canvasSize.width,\n        };\n    }\n    contains(point) {\n        if (super.contains(point)) {\n            return true;\n        }\n        const posNE = {\n            x: point.x - this.canvasSize.width,\n            y: point.y,\n        };\n        if (super.contains(posNE)) {\n            return true;\n        }\n        const posSE = {\n            x: point.x - this.canvasSize.width,\n            y: point.y - this.canvasSize.height,\n        };\n        if (super.contains(posSE)) {\n            return true;\n        }\n        const posSW = {\n            x: point.x,\n            y: point.y - this.canvasSize.height,\n        };\n        return super.contains(posSW);\n    }\n    intersects(range) {\n        if (super.intersects(range)) {\n            return true;\n        }\n        const rect = range;\n        const circle = range;\n        const newPos = {\n            x: range.position.x - this.canvasSize.width,\n            y: range.position.y - this.canvasSize.height,\n        };\n        if (circle.radius !== undefined) {\n            const biggerCircle = new Circle(newPos.x, newPos.y, circle.radius * 2);\n            return super.intersects(biggerCircle);\n        }\n        else if (rect.size !== undefined) {\n            const rectSW = new Rectangle(newPos.x, newPos.y, rect.size.width * 2, rect.size.height * 2);\n            return super.intersects(rectSW);\n        }\n        return false;\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
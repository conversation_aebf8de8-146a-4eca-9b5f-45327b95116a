{"ast": null, "code": "export var PixelMode;\n(function (PixelMode) {\n  PixelMode[\"precise\"] = \"precise\";\n  PixelMode[\"percent\"] = \"percent\";\n})(PixelMode || (PixelMode = {}));", "map": {"version": 3, "names": ["PixelMode"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/engine/browser/Enums/Modes/PixelMode.js"], "sourcesContent": ["export var PixelMode;\n(function (PixelMode) {\n    PixelMode[\"precise\"] = \"precise\";\n    PixelMode[\"percent\"] = \"percent\";\n})(PixelMode || (PixelMode = {}));\n"], "mappings": "AAAA,OAAO,IAAIA,SAAS;AACpB,CAAC,UAAUA,SAAS,EAAE;EAClBA,SAAS,CAAC,SAAS,CAAC,GAAG,SAAS;EAChCA,SAAS,CAAC,SAAS,CAAC,GAAG,SAAS;AACpC,CAAC,EAAEA,SAAS,KAAKA,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { setRangeValue } from \"../../../../Utils\";\nexport class RollLight {\n  constructor() {\n    this.enable = false;\n    this.value = 0;\n  }\n\n  load(data) {\n    if (!data) {\n      return;\n    }\n\n    if (data.enable !== undefined) {\n      this.enable = data.enable;\n    }\n\n    if (data.value !== undefined) {\n      this.value = setRangeValue(data.value);\n    }\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Options/Classes/Particles/Roll/RollLight.js"], "names": ["setRangeValue", "RollLight", "constructor", "enable", "value", "load", "data", "undefined"], "mappings": "AAAA,SAASA,aAAT,QAA8B,mBAA9B;AACA,OAAO,MAAMC,SAAN,CAAgB;AACnBC,EAAAA,WAAW,GAAG;AACV,SAAKC,MAAL,GAAc,KAAd;AACA,SAAKC,KAAL,GAAa,CAAb;AACH;;AACDC,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,QAAI,CAACA,IAAL,EAAW;AACP;AACH;;AACD,QAAIA,IAAI,CAACH,MAAL,KAAgBI,SAApB,EAA+B;AAC3B,WAAKJ,MAAL,GAAcG,IAAI,CAACH,MAAnB;AACH;;AACD,QAAIG,IAAI,CAACF,KAAL,KAAeG,SAAnB,EAA8B;AAC1B,WAAKH,KAAL,GAAaJ,aAAa,CAACM,IAAI,CAACF,KAAN,CAA1B;AACH;AACJ;;AAfkB", "sourcesContent": ["import { setRangeValue } from \"../../../../Utils\";\nexport class RollLight {\n    constructor() {\n        this.enable = false;\n        this.value = 0;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.enable !== undefined) {\n            this.enable = data.enable;\n        }\n        if (data.value !== undefined) {\n            this.value = setRangeValue(data.value);\n        }\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
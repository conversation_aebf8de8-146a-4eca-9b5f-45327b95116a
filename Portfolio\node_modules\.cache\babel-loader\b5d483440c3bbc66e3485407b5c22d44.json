{"ast": null, "code": "import { ColorUpdater } from \"./ColorUpdater\";\nexport async function loadColorUpdater(engine) {\n  await engine.addParticleUpdater(\"color\", container => new ColorUpdater(container));\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Updaters/Color/index.js"], "names": ["ColorUpdater", "loadColorUpdater", "engine", "addParticleUpdater", "container"], "mappings": "AAAA,SAASA,YAAT,QAA6B,gBAA7B;AACA,OAAO,eAAeC,gBAAf,CAAgCC,MAAhC,EAAwC;AAC3C,QAAMA,MAAM,CAACC,kBAAP,CAA0B,OAA1B,EAAoCC,SAAD,IAAe,IAAIJ,YAAJ,CAAiBI,SAAjB,CAAlD,CAAN;AACH", "sourcesContent": ["import { ColorUpdater } from \"./ColorUpdater\";\nexport async function loadColorUpdater(engine) {\n    await engine.addParticleUpdater(\"color\", (container) => new ColorUpdater(container));\n}\n"]}, "metadata": {}, "sourceType": "module"}
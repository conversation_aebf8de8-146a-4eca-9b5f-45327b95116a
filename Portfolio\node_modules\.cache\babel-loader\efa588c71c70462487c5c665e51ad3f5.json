{"ast": null, "code": "import { getRangeValue } from \"../../Utils\";\n\nfunction updateAngle(particle, delta) {\n  var _a;\n\n  const rotate = particle.rotate;\n\n  if (!rotate) {\n    return;\n  }\n\n  const rotateOptions = particle.options.rotate;\n  const rotateAnimation = rotateOptions.animation;\n  const speed = ((_a = rotate.velocity) !== null && _a !== void 0 ? _a : 0) * delta.factor;\n  const max = 2 * Math.PI;\n\n  if (!rotateAnimation.enable) {\n    return;\n  }\n\n  switch (rotate.status) {\n    case 0:\n      rotate.value += speed;\n\n      if (rotate.value > max) {\n        rotate.value -= max;\n      }\n\n      break;\n\n    case 1:\n    default:\n      rotate.value -= speed;\n\n      if (rotate.value < 0) {\n        rotate.value += max;\n      }\n\n      break;\n  }\n}\n\nexport class AngleUpdater {\n  constructor(container) {\n    this.container = container;\n  }\n\n  init(particle) {\n    const rotateOptions = particle.options.rotate;\n    particle.rotate = {\n      enable: rotateOptions.animation.enable,\n      value: getRangeValue(rotateOptions.value) * Math.PI / 180\n    };\n    let rotateDirection = rotateOptions.direction;\n\n    if (rotateDirection === \"random\") {\n      const index = Math.floor(Math.random() * 2);\n      rotateDirection = index > 0 ? \"counter-clockwise\" : \"clockwise\";\n    }\n\n    switch (rotateDirection) {\n      case \"counter-clockwise\":\n      case \"counterClockwise\":\n        particle.rotate.status = 1;\n        break;\n\n      case \"clockwise\":\n        particle.rotate.status = 0;\n        break;\n    }\n\n    const rotateAnimation = particle.options.rotate.animation;\n\n    if (rotateAnimation.enable) {\n      particle.rotate.velocity = getRangeValue(rotateAnimation.speed) / 360 * this.container.retina.reduceFactor;\n\n      if (!rotateAnimation.sync) {\n        particle.rotate.velocity *= Math.random();\n      }\n    }\n  }\n\n  isEnabled(particle) {\n    const rotate = particle.options.rotate;\n    const rotateAnimation = rotate.animation;\n    return !particle.destroyed && !particle.spawning && !rotate.path && rotateAnimation.enable;\n  }\n\n  update(particle, delta) {\n    if (!this.isEnabled(particle)) {\n      return;\n    }\n\n    updateAngle(particle, delta);\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Updaters/Angle/AngleUpdater.js"], "names": ["getRangeValue", "updateAngle", "particle", "delta", "_a", "rotate", "rotateOptions", "options", "rotateAnimation", "animation", "speed", "velocity", "factor", "max", "Math", "PI", "enable", "status", "value", "AngleUpdater", "constructor", "container", "init", "rotateDirection", "direction", "index", "floor", "random", "retina", "reduceFactor", "sync", "isEnabled", "destroyed", "spawning", "path", "update"], "mappings": "AAAA,SAASA,aAAT,QAA8B,aAA9B;;AACA,SAASC,WAAT,CAAqBC,QAArB,EAA+BC,KAA/B,EAAsC;AAClC,MAAIC,EAAJ;;AACA,QAAMC,MAAM,GAAGH,QAAQ,CAACG,MAAxB;;AACA,MAAI,CAACA,MAAL,EAAa;AACT;AACH;;AACD,QAAMC,aAAa,GAAGJ,QAAQ,CAACK,OAAT,CAAiBF,MAAvC;AACA,QAAMG,eAAe,GAAGF,aAAa,CAACG,SAAtC;AACA,QAAMC,KAAK,GAAG,CAAC,CAACN,EAAE,GAAGC,MAAM,CAACM,QAAb,MAA2B,IAA3B,IAAmCP,EAAE,KAAK,KAAK,CAA/C,GAAmDA,EAAnD,GAAwD,CAAzD,IAA8DD,KAAK,CAACS,MAAlF;AACA,QAAMC,GAAG,GAAG,IAAIC,IAAI,CAACC,EAArB;;AACA,MAAI,CAACP,eAAe,CAACQ,MAArB,EAA6B;AACzB;AACH;;AACD,UAAQX,MAAM,CAACY,MAAf;AACI,SAAK,CAAL;AACIZ,MAAAA,MAAM,CAACa,KAAP,IAAgBR,KAAhB;;AACA,UAAIL,MAAM,CAACa,KAAP,GAAeL,GAAnB,EAAwB;AACpBR,QAAAA,MAAM,CAACa,KAAP,IAAgBL,GAAhB;AACH;;AACD;;AACJ,SAAK,CAAL;AACA;AACIR,MAAAA,MAAM,CAACa,KAAP,IAAgBR,KAAhB;;AACA,UAAIL,MAAM,CAACa,KAAP,GAAe,CAAnB,EAAsB;AAClBb,QAAAA,MAAM,CAACa,KAAP,IAAgBL,GAAhB;AACH;;AACD;AAbR;AAeH;;AACD,OAAO,MAAMM,YAAN,CAAmB;AACtBC,EAAAA,WAAW,CAACC,SAAD,EAAY;AACnB,SAAKA,SAAL,GAAiBA,SAAjB;AACH;;AACDC,EAAAA,IAAI,CAACpB,QAAD,EAAW;AACX,UAAMI,aAAa,GAAGJ,QAAQ,CAACK,OAAT,CAAiBF,MAAvC;AACAH,IAAAA,QAAQ,CAACG,MAAT,GAAkB;AACdW,MAAAA,MAAM,EAAEV,aAAa,CAACG,SAAd,CAAwBO,MADlB;AAEdE,MAAAA,KAAK,EAAGlB,aAAa,CAACM,aAAa,CAACY,KAAf,CAAb,GAAqCJ,IAAI,CAACC,EAA3C,GAAiD;AAF1C,KAAlB;AAIA,QAAIQ,eAAe,GAAGjB,aAAa,CAACkB,SAApC;;AACA,QAAID,eAAe,KAAK,QAAxB,EAAkC;AAC9B,YAAME,KAAK,GAAGX,IAAI,CAACY,KAAL,CAAWZ,IAAI,CAACa,MAAL,KAAgB,CAA3B,CAAd;AACAJ,MAAAA,eAAe,GAAGE,KAAK,GAAG,CAAR,GAAY,mBAAZ,GAAkC,WAApD;AACH;;AACD,YAAQF,eAAR;AACI,WAAK,mBAAL;AACA,WAAK,kBAAL;AACIrB,QAAAA,QAAQ,CAACG,MAAT,CAAgBY,MAAhB,GAAyB,CAAzB;AACA;;AACJ,WAAK,WAAL;AACIf,QAAAA,QAAQ,CAACG,MAAT,CAAgBY,MAAhB,GAAyB,CAAzB;AACA;AAPR;;AASA,UAAMT,eAAe,GAAGN,QAAQ,CAACK,OAAT,CAAiBF,MAAjB,CAAwBI,SAAhD;;AACA,QAAID,eAAe,CAACQ,MAApB,EAA4B;AACxBd,MAAAA,QAAQ,CAACG,MAAT,CAAgBM,QAAhB,GACKX,aAAa,CAACQ,eAAe,CAACE,KAAjB,CAAb,GAAuC,GAAxC,GAA+C,KAAKW,SAAL,CAAeO,MAAf,CAAsBC,YADzE;;AAEA,UAAI,CAACrB,eAAe,CAACsB,IAArB,EAA2B;AACvB5B,QAAAA,QAAQ,CAACG,MAAT,CAAgBM,QAAhB,IAA4BG,IAAI,CAACa,MAAL,EAA5B;AACH;AACJ;AACJ;;AACDI,EAAAA,SAAS,CAAC7B,QAAD,EAAW;AAChB,UAAMG,MAAM,GAAGH,QAAQ,CAACK,OAAT,CAAiBF,MAAhC;AACA,UAAMG,eAAe,GAAGH,MAAM,CAACI,SAA/B;AACA,WAAO,CAACP,QAAQ,CAAC8B,SAAV,IAAuB,CAAC9B,QAAQ,CAAC+B,QAAjC,IAA6C,CAAC5B,MAAM,CAAC6B,IAArD,IAA6D1B,eAAe,CAACQ,MAApF;AACH;;AACDmB,EAAAA,MAAM,CAACjC,QAAD,EAAWC,KAAX,EAAkB;AACpB,QAAI,CAAC,KAAK4B,SAAL,CAAe7B,QAAf,CAAL,EAA+B;AAC3B;AACH;;AACDD,IAAAA,WAAW,CAACC,QAAD,EAAWC,KAAX,CAAX;AACH;;AA3CqB", "sourcesContent": ["import { getRangeValue } from \"../../Utils\";\nfunction updateAngle(particle, delta) {\n    var _a;\n    const rotate = particle.rotate;\n    if (!rotate) {\n        return;\n    }\n    const rotateOptions = particle.options.rotate;\n    const rotateAnimation = rotateOptions.animation;\n    const speed = ((_a = rotate.velocity) !== null && _a !== void 0 ? _a : 0) * delta.factor;\n    const max = 2 * Math.PI;\n    if (!rotateAnimation.enable) {\n        return;\n    }\n    switch (rotate.status) {\n        case 0:\n            rotate.value += speed;\n            if (rotate.value > max) {\n                rotate.value -= max;\n            }\n            break;\n        case 1:\n        default:\n            rotate.value -= speed;\n            if (rotate.value < 0) {\n                rotate.value += max;\n            }\n            break;\n    }\n}\nexport class AngleUpdater {\n    constructor(container) {\n        this.container = container;\n    }\n    init(particle) {\n        const rotateOptions = particle.options.rotate;\n        particle.rotate = {\n            enable: rotateOptions.animation.enable,\n            value: (getRangeValue(rotateOptions.value) * Math.PI) / 180,\n        };\n        let rotateDirection = rotateOptions.direction;\n        if (rotateDirection === \"random\") {\n            const index = Math.floor(Math.random() * 2);\n            rotateDirection = index > 0 ? \"counter-clockwise\" : \"clockwise\";\n        }\n        switch (rotateDirection) {\n            case \"counter-clockwise\":\n            case \"counterClockwise\":\n                particle.rotate.status = 1;\n                break;\n            case \"clockwise\":\n                particle.rotate.status = 0;\n                break;\n        }\n        const rotateAnimation = particle.options.rotate.animation;\n        if (rotateAnimation.enable) {\n            particle.rotate.velocity =\n                (getRangeValue(rotateAnimation.speed) / 360) * this.container.retina.reduceFactor;\n            if (!rotateAnimation.sync) {\n                particle.rotate.velocity *= Math.random();\n            }\n        }\n    }\n    isEnabled(particle) {\n        const rotate = particle.options.rotate;\n        const rotateAnimation = rotate.animation;\n        return !particle.destroyed && !particle.spawning && !rotate.path && rotateAnimation.enable;\n    }\n    update(particle, delta) {\n        if (!this.isEnabled(particle)) {\n            return;\n        }\n        updateAngle(particle, delta);\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
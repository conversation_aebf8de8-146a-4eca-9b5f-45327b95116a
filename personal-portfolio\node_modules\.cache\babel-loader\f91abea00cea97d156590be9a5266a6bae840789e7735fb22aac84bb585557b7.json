{"ast": null, "code": "export class CollisionsOverlap {\n  constructor() {\n    this.enable = true;\n    this.retries = 0;\n  }\n  load(data) {\n    if (!data) {\n      return;\n    }\n    if (data.enable !== undefined) {\n      this.enable = data.enable;\n    }\n    if (data.retries !== undefined) {\n      this.retries = data.retries;\n    }\n  }\n}", "map": {"version": 3, "names": ["CollisionsOverlap", "constructor", "enable", "retries", "load", "data", "undefined"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/engine/browser/Options/Classes/Particles/Collisions/CollisionsOverlap.js"], "sourcesContent": ["export class CollisionsOverlap {\n    constructor() {\n        this.enable = true;\n        this.retries = 0;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.enable !== undefined) {\n            this.enable = data.enable;\n        }\n        if (data.retries !== undefined) {\n            this.retries = data.retries;\n        }\n    }\n}\n"], "mappings": "AAAA,OAAO,MAAMA,iBAAiB,CAAC;EAC3BC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,MAAM,GAAG,IAAI;IAClB,IAAI,CAACC,OAAO,GAAG,CAAC;EACpB;EACAC,IAAIA,CAACC,IAAI,EAAE;IACP,IAAI,CAACA,IAAI,EAAE;MACP;IACJ;IACA,IAAIA,IAAI,CAACH,MAAM,KAAKI,SAAS,EAAE;MAC3B,IAAI,CAACJ,MAAM,GAAGG,IAAI,CAACH,MAAM;IAC7B;IACA,IAAIG,IAAI,CAACF,OAAO,KAAKG,SAAS,EAAE;MAC5B,IAAI,CAACH,OAAO,GAAGE,IAAI,CAACF,OAAO;IAC/B;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "export var OutModeDirection;\n(function (OutModeDirection) {\n  OutModeDirection[\"bottom\"] = \"bottom\";\n  OutModeDirection[\"left\"] = \"left\";\n  OutModeDirection[\"right\"] = \"right\";\n  OutModeDirection[\"top\"] = \"top\";\n})(OutModeDirection || (OutModeDirection = {}));", "map": {"version": 3, "names": ["OutModeDirection"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/engine/browser/Enums/Directions/OutModeDirection.js"], "sourcesContent": ["export var OutModeDirection;\n(function (OutModeDirection) {\n    OutModeDirection[\"bottom\"] = \"bottom\";\n    OutModeDirection[\"left\"] = \"left\";\n    OutModeDirection[\"right\"] = \"right\";\n    OutModeDirection[\"top\"] = \"top\";\n})(OutModeDirection || (OutModeDirection = {}));\n"], "mappings": "AAAA,OAAO,IAAIA,gBAAgB;AAC3B,CAAC,UAAUA,gBAAgB,EAAE;EACzBA,gBAAgB,CAAC,QAAQ,CAAC,GAAG,QAAQ;EACrCA,gBAAgB,CAAC,MAAM,CAAC,GAAG,MAAM;EACjCA,gBAAgB,CAAC,OAAO,CAAC,GAAG,OAAO;EACnCA,gBAAgB,CAAC,KAAK,CAAC,GAAG,KAAK;AACnC,CAAC,EAAEA,gBAAgB,KAAKA,gBAAgB,GAAG,CAAC,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
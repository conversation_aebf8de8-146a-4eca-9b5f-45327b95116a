{"ast": null, "code": "import { getRangeValue } from \"@tsparticles/engine\";\nimport { drawStar } from \"./Utils.js\";\nconst defaultInset = 2,\n  defaultSides = 5;\nexport class StarDrawer {\n  constructor() {\n    this.validTypes = [\"star\"];\n  }\n  draw(data) {\n    drawStar(data);\n  }\n  getSidesCount(particle) {\n    const star = particle.shapeData;\n    return Math.round(getRangeValue(star?.sides ?? defaultSides));\n  }\n  particleInit(container, particle) {\n    const star = particle.shapeData;\n    particle.starInset = getRangeValue(star?.inset ?? defaultInset);\n  }\n}", "map": {"version": 3, "names": ["getRangeValue", "drawStar", "defaultInset", "defaultSides", "StarDrawer", "constructor", "validTypes", "draw", "data", "getSidesCount", "particle", "star", "shapeData", "Math", "round", "sides", "particleInit", "container", "starInset", "inset"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/shape-star/browser/StarDrawer.js"], "sourcesContent": ["import { getRangeValue, } from \"@tsparticles/engine\";\nimport { drawStar } from \"./Utils.js\";\nconst defaultInset = 2, defaultSides = 5;\nexport class StarDrawer {\n    constructor() {\n        this.validTypes = [\"star\"];\n    }\n    draw(data) {\n        drawStar(data);\n    }\n    getSidesCount(particle) {\n        const star = particle.shapeData;\n        return Math.round(getRangeValue(star?.sides ?? defaultSides));\n    }\n    particleInit(container, particle) {\n        const star = particle.shapeData;\n        particle.starInset = getRangeValue(star?.inset ?? defaultInset);\n    }\n}\n"], "mappings": "AAAA,SAASA,aAAa,QAAS,qBAAqB;AACpD,SAASC,QAAQ,QAAQ,YAAY;AACrC,MAAMC,YAAY,GAAG,CAAC;EAAEC,YAAY,GAAG,CAAC;AACxC,OAAO,MAAMC,UAAU,CAAC;EACpBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,UAAU,GAAG,CAAC,MAAM,CAAC;EAC9B;EACAC,IAAIA,CAACC,IAAI,EAAE;IACPP,QAAQ,CAACO,IAAI,CAAC;EAClB;EACAC,aAAaA,CAACC,QAAQ,EAAE;IACpB,MAAMC,IAAI,GAAGD,QAAQ,CAACE,SAAS;IAC/B,OAAOC,IAAI,CAACC,KAAK,CAACd,aAAa,CAACW,IAAI,EAAEI,KAAK,IAAIZ,YAAY,CAAC,CAAC;EACjE;EACAa,YAAYA,CAACC,SAAS,EAAEP,QAAQ,EAAE;IAC9B,MAAMC,IAAI,GAAGD,QAAQ,CAACE,SAAS;IAC/BF,QAAQ,CAACQ,SAAS,GAAGlB,aAAa,CAACW,IAAI,EAAEQ,KAAK,IAAIjB,YAAY,CAAC;EACnE;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
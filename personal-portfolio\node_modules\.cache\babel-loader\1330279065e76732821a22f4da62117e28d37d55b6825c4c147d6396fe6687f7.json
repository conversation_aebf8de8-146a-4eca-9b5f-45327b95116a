{"ast": null, "code": "export class AbsorberSizeLimit {\n  constructor() {\n    this.radius = 0;\n    this.mass = 0;\n  }\n  load(data) {\n    if (!data) {\n      return;\n    }\n    if (data.mass !== undefined) {\n      this.mass = data.mass;\n    }\n    if (data.radius !== undefined) {\n      this.radius = data.radius;\n    }\n  }\n}", "map": {"version": 3, "names": ["AbsorberSizeLimit", "constructor", "radius", "mass", "load", "data", "undefined"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/plugin-absorbers/browser/Options/Classes/AbsorberSizeLimit.js"], "sourcesContent": ["export class AbsorberSizeLimit {\n    constructor() {\n        this.radius = 0;\n        this.mass = 0;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.mass !== undefined) {\n            this.mass = data.mass;\n        }\n        if (data.radius !== undefined) {\n            this.radius = data.radius;\n        }\n    }\n}\n"], "mappings": "AAAA,OAAO,MAAMA,iBAAiB,CAAC;EAC3BC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,MAAM,GAAG,CAAC;IACf,IAAI,CAACC,IAAI,GAAG,CAAC;EACjB;EACAC,IAAIA,CAACC,IAAI,EAAE;IACP,IAAI,CAACA,IAAI,EAAE;MACP;IACJ;IACA,IAAIA,IAAI,CAACF,IAAI,KAAKG,SAAS,EAAE;MACzB,IAAI,CAACH,IAAI,GAAGE,IAAI,CAACF,IAAI;IACzB;IACA,IAAIE,IAAI,CAACH,MAAM,KAAKI,SAAS,EAAE;MAC3B,IAAI,CAACJ,MAAM,GAAGG,IAAI,CAACH,MAAM;IAC7B;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
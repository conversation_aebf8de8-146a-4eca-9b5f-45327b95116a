{"ast": null, "code": "import { ColorAnimation } from \"./ColorAnimation\";\nexport class HslAnimation {\n  constructor() {\n    this.h = new ColorAnimation();\n    this.s = new ColorAnimation();\n    this.l = new ColorAnimation();\n  }\n\n  load(data) {\n    if (!data) {\n      return;\n    }\n\n    this.h.load(data.h);\n    this.s.load(data.s);\n    this.l.load(data.l);\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Options/Classes/HslAnimation.js"], "names": ["ColorAnimation", "HslAnimation", "constructor", "h", "s", "l", "load", "data"], "mappings": "AAAA,SAASA,cAAT,QAA+B,kBAA/B;AACA,OAAO,MAAMC,YAAN,CAAmB;AACtBC,EAAAA,WAAW,GAAG;AACV,SAAKC,CAAL,GAAS,IAAIH,cAAJ,EAAT;AACA,SAAKI,CAAL,GAAS,IAAIJ,cAAJ,EAAT;AACA,SAAKK,CAAL,GAAS,IAAIL,cAAJ,EAAT;AACH;;AACDM,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,QAAI,CAACA,IAAL,EAAW;AACP;AACH;;AACD,SAAKJ,CAAL,CAAOG,IAAP,CAAYC,IAAI,CAACJ,CAAjB;AACA,SAAKC,CAAL,CAAOE,IAAP,CAAYC,IAAI,CAACH,CAAjB;AACA,SAAKC,CAAL,CAAOC,IAAP,CAAYC,IAAI,CAACF,CAAjB;AACH;;AAbqB", "sourcesContent": ["import { ColorAnimation } from \"./ColorAnimation\";\nexport class HslAnimation {\n    constructor() {\n        this.h = new ColorAnimation();\n        this.s = new ColorAnimation();\n        this.l = new ColorAnimation();\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        this.h.load(data.h);\n        this.s.load(data.s);\n        this.l.load(data.l);\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
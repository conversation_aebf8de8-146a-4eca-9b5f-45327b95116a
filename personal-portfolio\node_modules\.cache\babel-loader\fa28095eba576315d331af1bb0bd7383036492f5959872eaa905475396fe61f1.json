{"ast": null, "code": "import { TrailMaker } from \"./TrailMaker.js\";\nexport async function loadExternalTrailInteraction(engine, refresh = true) {\n  await engine.addInteractor(\"externalTrail\", container => {\n    return Promise.resolve(new TrailMaker(container));\n  }, refresh);\n}\nexport * from \"./Options/Classes/Trail.js\";\nexport * from \"./Options/Interfaces/ITrail.js\";", "map": {"version": 3, "names": ["TrailMaker", "loadExternalTrailInteraction", "engine", "refresh", "addInteractor", "container", "Promise", "resolve"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/interaction-external-trail/browser/index.js"], "sourcesContent": ["import { TrailMaker } from \"./TrailMaker.js\";\nexport async function loadExternalTrailInteraction(engine, refresh = true) {\n    await engine.addInteractor(\"externalTrail\", container => {\n        return Promise.resolve(new TrailMaker(container));\n    }, refresh);\n}\nexport * from \"./Options/Classes/Trail.js\";\nexport * from \"./Options/Interfaces/ITrail.js\";\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,iBAAiB;AAC5C,OAAO,eAAeC,4BAA4BA,CAACC,MAAM,EAAEC,OAAO,GAAG,IAAI,EAAE;EACvE,MAAMD,MAAM,CAACE,aAAa,CAAC,eAAe,EAAEC,SAAS,IAAI;IACrD,OAAOC,OAAO,CAACC,OAAO,CAAC,IAAIP,UAAU,CAACK,SAAS,CAAC,CAAC;EACrD,CAAC,EAAEF,OAAO,CAAC;AACf;AACA,cAAc,4BAA4B;AAC1C,cAAc,gCAAgC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
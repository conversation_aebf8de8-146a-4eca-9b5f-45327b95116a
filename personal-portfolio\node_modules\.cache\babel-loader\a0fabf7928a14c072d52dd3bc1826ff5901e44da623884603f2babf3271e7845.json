{"ast": null, "code": "import { DivType } from \"../../../../Enums/Types/DivType.js\";\nexport class DivEvent {\n  constructor() {\n    this.selectors = [];\n    this.enable = false;\n    this.mode = [];\n    this.type = DivType.circle;\n  }\n  load(data) {\n    if (!data) {\n      return;\n    }\n    if (data.selectors !== undefined) {\n      this.selectors = data.selectors;\n    }\n    if (data.enable !== undefined) {\n      this.enable = data.enable;\n    }\n    if (data.mode !== undefined) {\n      this.mode = data.mode;\n    }\n    if (data.type !== undefined) {\n      this.type = data.type;\n    }\n  }\n}", "map": {"version": 3, "names": ["DivType", "DivEvent", "constructor", "selectors", "enable", "mode", "type", "circle", "load", "data", "undefined"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/engine/browser/Options/Classes/Interactivity/Events/DivEvent.js"], "sourcesContent": ["import { DivType } from \"../../../../Enums/Types/DivType.js\";\nexport class DivEvent {\n    constructor() {\n        this.selectors = [];\n        this.enable = false;\n        this.mode = [];\n        this.type = DivType.circle;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.selectors !== undefined) {\n            this.selectors = data.selectors;\n        }\n        if (data.enable !== undefined) {\n            this.enable = data.enable;\n        }\n        if (data.mode !== undefined) {\n            this.mode = data.mode;\n        }\n        if (data.type !== undefined) {\n            this.type = data.type;\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,oCAAoC;AAC5D,OAAO,MAAMC,QAAQ,CAAC;EAClBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,MAAM,GAAG,KAAK;IACnB,IAAI,CAACC,IAAI,GAAG,EAAE;IACd,IAAI,CAACC,IAAI,GAAGN,OAAO,CAACO,MAAM;EAC9B;EACAC,IAAIA,CAACC,IAAI,EAAE;IACP,IAAI,CAACA,IAAI,EAAE;MACP;IACJ;IACA,IAAIA,IAAI,CAACN,SAAS,KAAKO,SAAS,EAAE;MAC9B,IAAI,CAACP,SAAS,GAAGM,IAAI,CAACN,SAAS;IACnC;IACA,IAAIM,IAAI,CAACL,MAAM,KAAKM,SAAS,EAAE;MAC3B,IAAI,CAACN,MAAM,GAAGK,IAAI,CAACL,MAAM;IAC7B;IACA,IAAIK,IAAI,CAACJ,IAAI,KAAKK,SAAS,EAAE;MACzB,IAAI,CAACL,IAAI,GAAGI,IAAI,CAACJ,IAAI;IACzB;IACA,IAAII,IAAI,CAACH,IAAI,KAAKI,SAAS,EAAE;MACzB,IAAI,CAACJ,IAAI,GAAGG,IAAI,CAACH,IAAI;IACzB;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
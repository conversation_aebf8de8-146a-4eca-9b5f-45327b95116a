{"ast": null, "code": "import { deepExtend } from \"../../../../Utils\";\nexport class Trail {\n  constructor() {\n    this.delay = 1;\n    this.pauseOnStop = false;\n    this.quantity = 1;\n  }\n\n  load(data) {\n    if (data === undefined) {\n      return;\n    }\n\n    if (data.delay !== undefined) {\n      this.delay = data.delay;\n    }\n\n    if (data.quantity !== undefined) {\n      this.quantity = data.quantity;\n    }\n\n    if (data.particles !== undefined) {\n      this.particles = deepExtend({}, data.particles);\n    }\n\n    if (data.pauseOnStop !== undefined) {\n      this.pauseOnStop = data.pauseOnStop;\n    }\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Options/Classes/Interactivity/Modes/Trail.js"], "names": ["deepExtend", "Trail", "constructor", "delay", "pauseOnStop", "quantity", "load", "data", "undefined", "particles"], "mappings": "AAAA,SAASA,UAAT,QAA2B,mBAA3B;AACA,OAAO,MAAMC,KAAN,CAAY;AACfC,EAAAA,WAAW,GAAG;AACV,SAAKC,KAAL,GAAa,CAAb;AACA,SAAKC,WAAL,GAAmB,KAAnB;AACA,SAAKC,QAAL,GAAgB,CAAhB;AACH;;AACDC,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,QAAIA,IAAI,KAAKC,SAAb,EAAwB;AACpB;AACH;;AACD,QAAID,IAAI,CAACJ,KAAL,KAAeK,SAAnB,EAA8B;AAC1B,WAAKL,KAAL,GAAaI,IAAI,CAACJ,KAAlB;AACH;;AACD,QAAII,IAAI,CAACF,QAAL,KAAkBG,SAAtB,EAAiC;AAC7B,WAAKH,QAAL,GAAgBE,IAAI,CAACF,QAArB;AACH;;AACD,QAAIE,IAAI,CAACE,SAAL,KAAmBD,SAAvB,EAAkC;AAC9B,WAAKC,SAAL,GAAiBT,UAAU,CAAC,EAAD,EAAKO,IAAI,CAACE,SAAV,CAA3B;AACH;;AACD,QAAIF,IAAI,CAACH,WAAL,KAAqBI,SAAzB,EAAoC;AAChC,WAAKJ,WAAL,GAAmBG,IAAI,CAACH,WAAxB;AACH;AACJ;;AAtBc", "sourcesContent": ["import { deepExtend } from \"../../../../Utils\";\nexport class Trail {\n    constructor() {\n        this.delay = 1;\n        this.pauseOnStop = false;\n        this.quantity = 1;\n    }\n    load(data) {\n        if (data === undefined) {\n            return;\n        }\n        if (data.delay !== undefined) {\n            this.delay = data.delay;\n        }\n        if (data.quantity !== undefined) {\n            this.quantity = data.quantity;\n        }\n        if (data.particles !== undefined) {\n            this.particles = deepExtend({}, data.particles);\n        }\n        if (data.pauseOnStop !== undefined) {\n            this.pauseOnStop = data.pauseOnStop;\n        }\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
{"ast": null, "code": "import getUTCWeekYear from \"../getUTCWeekYear/index.js\";\nimport requiredArgs from \"../requiredArgs/index.js\";\nimport startOfUTCWeek from \"../startOfUTCWeek/index.js\";\nimport toInteger from \"../toInteger/index.js\"; // This function will be a part of public API when UTC function will be implemented.\n// See issue: https://github.com/date-fns/date-fns/issues/376\n\nexport default function startOfUTCWeekYear(dirtyDate, dirtyOptions) {\n  requiredArgs(1, arguments);\n  var options = dirtyOptions || {};\n  var locale = options.locale;\n  var localeFirstWeekContainsDate = locale && locale.options && locale.options.firstWeekContainsDate;\n  var defaultFirstWeekContainsDate = localeFirstWeekContainsDate == null ? 1 : toInteger(localeFirstWeekContainsDate);\n  var firstWeekContainsDate = options.firstWeekContainsDate == null ? defaultFirstWeekContainsDate : toInteger(options.firstWeekContainsDate);\n  var year = getUTCWeekYear(dirtyDate, dirtyOptions);\n  var firstWeek = new Date(0);\n  firstWeek.setUTCFullYear(year, 0, firstWeekContainsDate);\n  firstWeek.setUTCHours(0, 0, 0, 0);\n  var date = startOfUTCWeek(firstWeek, dirtyOptions);\n  return date;\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/date-fns/esm/_lib/startOfUTCWeekYear/index.js"], "names": ["getUTCWeekYear", "requiredArgs", "startOfUTCWeek", "toInteger", "startOfUTCWeekYear", "dirtyDate", "dirtyOptions", "arguments", "options", "locale", "localeFirstWeekContainsDate", "firstWeekContainsDate", "defaultFirstWeekContainsDate", "year", "firstWeek", "Date", "setUTCFullYear", "setUTCHours", "date"], "mappings": "AAAA,OAAOA,cAAP,MAA2B,4BAA3B;AACA,OAAOC,YAAP,MAAyB,0BAAzB;AACA,OAAOC,cAAP,MAA2B,4BAA3B;AACA,OAAOC,SAAP,MAAsB,uBAAtB,C,CAA+C;AAC/C;;AAEA,eAAe,SAASC,kBAAT,CAA4BC,SAA5B,EAAuCC,YAAvC,EAAqD;AAClEL,EAAAA,YAAY,CAAC,CAAD,EAAIM,SAAJ,CAAZ;AACA,MAAIC,OAAO,GAAGF,YAAY,IAAI,EAA9B;AACA,MAAIG,MAAM,GAAGD,OAAO,CAACC,MAArB;AACA,MAAIC,2BAA2B,GAAGD,MAAM,IAAIA,MAAM,CAACD,OAAjB,IAA4BC,MAAM,CAACD,OAAP,CAAeG,qBAA7E;AACA,MAAIC,4BAA4B,GAAGF,2BAA2B,IAAI,IAA/B,GAAsC,CAAtC,GAA0CP,SAAS,CAACO,2BAAD,CAAtF;AACA,MAAIC,qBAAqB,GAAGH,OAAO,CAACG,qBAAR,IAAiC,IAAjC,GAAwCC,4BAAxC,GAAuET,SAAS,CAACK,OAAO,CAACG,qBAAT,CAA5G;AACA,MAAIE,IAAI,GAAGb,cAAc,CAACK,SAAD,EAAYC,YAAZ,CAAzB;AACA,MAAIQ,SAAS,GAAG,IAAIC,IAAJ,CAAS,CAAT,CAAhB;AACAD,EAAAA,SAAS,CAACE,cAAV,CAAyBH,IAAzB,EAA+B,CAA/B,EAAkCF,qBAAlC;AACAG,EAAAA,SAAS,CAACG,WAAV,CAAsB,CAAtB,EAAyB,CAAzB,EAA4B,CAA5B,EAA+B,CAA/B;AACA,MAAIC,IAAI,GAAGhB,cAAc,CAACY,SAAD,EAAYR,YAAZ,CAAzB;AACA,SAAOY,IAAP;AACD", "sourcesContent": ["import getUTCWeekYear from \"../getUTCWeekYear/index.js\";\nimport requiredArgs from \"../requiredArgs/index.js\";\nimport startOfUTCWeek from \"../startOfUTCWeek/index.js\";\nimport toInteger from \"../toInteger/index.js\"; // This function will be a part of public API when UTC function will be implemented.\n// See issue: https://github.com/date-fns/date-fns/issues/376\n\nexport default function startOfUTCWeekYear(dirtyDate, dirtyOptions) {\n  requiredArgs(1, arguments);\n  var options = dirtyOptions || {};\n  var locale = options.locale;\n  var localeFirstWeekContainsDate = locale && locale.options && locale.options.firstWeekContainsDate;\n  var defaultFirstWeekContainsDate = localeFirstWeekContainsDate == null ? 1 : toInteger(localeFirstWeekContainsDate);\n  var firstWeekContainsDate = options.firstWeekContainsDate == null ? defaultFirstWeekContainsDate : toInteger(options.firstWeekContainsDate);\n  var year = getUTCWeekYear(dirtyDate, dirtyOptions);\n  var firstWeek = new Date(0);\n  firstWeek.setUTCFullYear(year, 0, firstWeekContainsDate);\n  firstWeek.setUTCHours(0, 0, 0, 0);\n  var date = startOfUTCWeek(firstWeek, dirtyOptions);\n  return date;\n}"]}, "metadata": {}, "sourceType": "module"}
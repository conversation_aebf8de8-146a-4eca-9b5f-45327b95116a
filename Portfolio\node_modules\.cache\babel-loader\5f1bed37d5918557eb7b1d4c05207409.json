{"ast": null, "code": "import { OptionsColor } from \"../OptionsColor\";\nexport class Shadow {\n  constructor() {\n    this.blur = 0;\n    this.color = new OptionsColor();\n    this.enable = false;\n    this.offset = {\n      x: 0,\n      y: 0\n    };\n    this.color.value = \"#000000\";\n  }\n\n  load(data) {\n    if (data === undefined) {\n      return;\n    }\n\n    if (data.blur !== undefined) {\n      this.blur = data.blur;\n    }\n\n    this.color = OptionsColor.create(this.color, data.color);\n\n    if (data.enable !== undefined) {\n      this.enable = data.enable;\n    }\n\n    if (data.offset === undefined) {\n      return;\n    }\n\n    if (data.offset.x !== undefined) {\n      this.offset.x = data.offset.x;\n    }\n\n    if (data.offset.y !== undefined) {\n      this.offset.y = data.offset.y;\n    }\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Options/Classes/Particles/Shadow.js"], "names": ["OptionsColor", "Shadow", "constructor", "blur", "color", "enable", "offset", "x", "y", "value", "load", "data", "undefined", "create"], "mappings": "AAAA,SAASA,YAAT,QAA6B,iBAA7B;AACA,OAAO,MAAMC,MAAN,CAAa;AAChBC,EAAAA,WAAW,GAAG;AACV,SAAKC,IAAL,GAAY,CAAZ;AACA,SAAKC,KAAL,GAAa,IAAIJ,YAAJ,EAAb;AACA,SAAKK,MAAL,GAAc,KAAd;AACA,SAAKC,MAAL,GAAc;AACVC,MAAAA,CAAC,EAAE,CADO;AAEVC,MAAAA,CAAC,EAAE;AAFO,KAAd;AAIA,SAAKJ,KAAL,CAAWK,KAAX,GAAmB,SAAnB;AACH;;AACDC,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,QAAIA,IAAI,KAAKC,SAAb,EAAwB;AACpB;AACH;;AACD,QAAID,IAAI,CAACR,IAAL,KAAcS,SAAlB,EAA6B;AACzB,WAAKT,IAAL,GAAYQ,IAAI,CAACR,IAAjB;AACH;;AACD,SAAKC,KAAL,GAAaJ,YAAY,CAACa,MAAb,CAAoB,KAAKT,KAAzB,EAAgCO,IAAI,CAACP,KAArC,CAAb;;AACA,QAAIO,IAAI,CAACN,MAAL,KAAgBO,SAApB,EAA+B;AAC3B,WAAKP,MAAL,GAAcM,IAAI,CAACN,MAAnB;AACH;;AACD,QAAIM,IAAI,CAACL,MAAL,KAAgBM,SAApB,EAA+B;AAC3B;AACH;;AACD,QAAID,IAAI,CAACL,MAAL,CAAYC,CAAZ,KAAkBK,SAAtB,EAAiC;AAC7B,WAAKN,MAAL,CAAYC,CAAZ,GAAgBI,IAAI,CAACL,MAAL,CAAYC,CAA5B;AACH;;AACD,QAAII,IAAI,CAACL,MAAL,CAAYE,CAAZ,KAAkBI,SAAtB,EAAiC;AAC7B,WAAKN,MAAL,CAAYE,CAAZ,GAAgBG,IAAI,CAACL,MAAL,CAAYE,CAA5B;AACH;AACJ;;AA/Be", "sourcesContent": ["import { OptionsColor } from \"../OptionsColor\";\nexport class Shadow {\n    constructor() {\n        this.blur = 0;\n        this.color = new OptionsColor();\n        this.enable = false;\n        this.offset = {\n            x: 0,\n            y: 0,\n        };\n        this.color.value = \"#000000\";\n    }\n    load(data) {\n        if (data === undefined) {\n            return;\n        }\n        if (data.blur !== undefined) {\n            this.blur = data.blur;\n        }\n        this.color = OptionsColor.create(this.color, data.color);\n        if (data.enable !== undefined) {\n            this.enable = data.enable;\n        }\n        if (data.offset === undefined) {\n            return;\n        }\n        if (data.offset.x !== undefined) {\n            this.offset.x = data.offset.x;\n        }\n        if (data.offset.y !== undefined) {\n            this.offset.y = data.offset.y;\n        }\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
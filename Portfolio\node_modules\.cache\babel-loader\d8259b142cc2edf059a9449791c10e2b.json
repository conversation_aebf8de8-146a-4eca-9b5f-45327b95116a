{"ast": null, "code": "import { setRangeValue } from \"../../../../Utils\";\nexport class Attract {\n  constructor() {\n    this.distance = 200;\n    this.enable = false;\n    this.rotate = {\n      x: 3000,\n      y: 3000\n    };\n  }\n\n  get rotateX() {\n    return this.rotate.x;\n  }\n\n  set rotateX(value) {\n    this.rotate.x = value;\n  }\n\n  get rotateY() {\n    return this.rotate.y;\n  }\n\n  set rotateY(value) {\n    this.rotate.y = value;\n  }\n\n  load(data) {\n    var _a, _b, _c, _d;\n\n    if (!data) {\n      return;\n    }\n\n    if (data.distance !== undefined) {\n      this.distance = setRangeValue(data.distance);\n    }\n\n    if (data.enable !== undefined) {\n      this.enable = data.enable;\n    }\n\n    const rotateX = (_b = (_a = data.rotate) === null || _a === void 0 ? void 0 : _a.x) !== null && _b !== void 0 ? _b : data.rotateX;\n\n    if (rotateX !== undefined) {\n      this.rotate.x = rotateX;\n    }\n\n    const rotateY = (_d = (_c = data.rotate) === null || _c === void 0 ? void 0 : _c.y) !== null && _d !== void 0 ? _d : data.rotateY;\n\n    if (rotateY !== undefined) {\n      this.rotate.y = rotateY;\n    }\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Options/Classes/Particles/Move/Attract.js"], "names": ["setRangeValue", "Attract", "constructor", "distance", "enable", "rotate", "x", "y", "rotateX", "value", "rotateY", "load", "data", "_a", "_b", "_c", "_d", "undefined"], "mappings": "AAAA,SAASA,aAAT,QAA8B,mBAA9B;AACA,OAAO,MAAMC,OAAN,CAAc;AACjBC,EAAAA,WAAW,GAAG;AACV,SAAKC,QAAL,GAAgB,GAAhB;AACA,SAAKC,MAAL,GAAc,KAAd;AACA,SAAKC,MAAL,GAAc;AACVC,MAAAA,CAAC,EAAE,IADO;AAEVC,MAAAA,CAAC,EAAE;AAFO,KAAd;AAIH;;AACU,MAAPC,OAAO,GAAG;AACV,WAAO,KAAKH,MAAL,CAAYC,CAAnB;AACH;;AACU,MAAPE,OAAO,CAACC,KAAD,EAAQ;AACf,SAAKJ,MAAL,CAAYC,CAAZ,GAAgBG,KAAhB;AACH;;AACU,MAAPC,OAAO,GAAG;AACV,WAAO,KAAKL,MAAL,CAAYE,CAAnB;AACH;;AACU,MAAPG,OAAO,CAACD,KAAD,EAAQ;AACf,SAAKJ,MAAL,CAAYE,CAAZ,GAAgBE,KAAhB;AACH;;AACDE,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,QAAIC,EAAJ,EAAQC,EAAR,EAAYC,EAAZ,EAAgBC,EAAhB;;AACA,QAAI,CAACJ,IAAL,EAAW;AACP;AACH;;AACD,QAAIA,IAAI,CAACT,QAAL,KAAkBc,SAAtB,EAAiC;AAC7B,WAAKd,QAAL,GAAgBH,aAAa,CAACY,IAAI,CAACT,QAAN,CAA7B;AACH;;AACD,QAAIS,IAAI,CAACR,MAAL,KAAgBa,SAApB,EAA+B;AAC3B,WAAKb,MAAL,GAAcQ,IAAI,CAACR,MAAnB;AACH;;AACD,UAAMI,OAAO,GAAG,CAACM,EAAE,GAAG,CAACD,EAAE,GAAGD,IAAI,CAACP,MAAX,MAAuB,IAAvB,IAA+BQ,EAAE,KAAK,KAAK,CAA3C,GAA+C,KAAK,CAApD,GAAwDA,EAAE,CAACP,CAAjE,MAAwE,IAAxE,IAAgFQ,EAAE,KAAK,KAAK,CAA5F,GAAgGA,EAAhG,GAAqGF,IAAI,CAACJ,OAA1H;;AACA,QAAIA,OAAO,KAAKS,SAAhB,EAA2B;AACvB,WAAKZ,MAAL,CAAYC,CAAZ,GAAgBE,OAAhB;AACH;;AACD,UAAME,OAAO,GAAG,CAACM,EAAE,GAAG,CAACD,EAAE,GAAGH,IAAI,CAACP,MAAX,MAAuB,IAAvB,IAA+BU,EAAE,KAAK,KAAK,CAA3C,GAA+C,KAAK,CAApD,GAAwDA,EAAE,CAACR,CAAjE,MAAwE,IAAxE,IAAgFS,EAAE,KAAK,KAAK,CAA5F,GAAgGA,EAAhG,GAAqGJ,IAAI,CAACF,OAA1H;;AACA,QAAIA,OAAO,KAAKO,SAAhB,EAA2B;AACvB,WAAKZ,MAAL,CAAYE,CAAZ,GAAgBG,OAAhB;AACH;AACJ;;AAxCgB", "sourcesContent": ["import { setRangeValue } from \"../../../../Utils\";\nexport class Attract {\n    constructor() {\n        this.distance = 200;\n        this.enable = false;\n        this.rotate = {\n            x: 3000,\n            y: 3000,\n        };\n    }\n    get rotateX() {\n        return this.rotate.x;\n    }\n    set rotateX(value) {\n        this.rotate.x = value;\n    }\n    get rotateY() {\n        return this.rotate.y;\n    }\n    set rotateY(value) {\n        this.rotate.y = value;\n    }\n    load(data) {\n        var _a, _b, _c, _d;\n        if (!data) {\n            return;\n        }\n        if (data.distance !== undefined) {\n            this.distance = setRangeValue(data.distance);\n        }\n        if (data.enable !== undefined) {\n            this.enable = data.enable;\n        }\n        const rotateX = (_b = (_a = data.rotate) === null || _a === void 0 ? void 0 : _a.x) !== null && _b !== void 0 ? _b : data.rotateX;\n        if (rotateX !== undefined) {\n            this.rotate.x = rotateX;\n        }\n        const rotateY = (_d = (_c = data.rotate) === null || _c === void 0 ? void 0 : _c.y) !== null && _d !== void 0 ? _d : data.rotateY;\n        if (rotateY !== undefined) {\n            this.rotate.y = rotateY;\n        }\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
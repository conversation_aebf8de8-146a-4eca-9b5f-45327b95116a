{"ast": null, "code": "import toDate from \"../../toDate/index.js\";\nimport startOfUTCWeek from \"../startOfUTCWeek/index.js\";\nimport startOfUTCWeekYear from \"../startOfUTCWeekYear/index.js\";\nimport requiredArgs from \"../requiredArgs/index.js\";\nvar MILLISECONDS_IN_WEEK = 604800000; // This function will be a part of public API when UTC function will be implemented.\n// See issue: https://github.com/date-fns/date-fns/issues/376\n\nexport default function getUTCWeek(dirtyDate, options) {\n  requiredArgs(1, arguments);\n  var date = toDate(dirtyDate);\n  var diff = startOfUTCWeek(date, options).getTime() - startOfUTCWeekYear(date, options).getTime(); // Round the number of days to the nearest integer\n  // because the number of milliseconds in a week is not constant\n  // (e.g. it's different in the week of the daylight saving time clock shift)\n\n  return Math.round(diff / MILLISECONDS_IN_WEEK) + 1;\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/date-fns/esm/_lib/getUTCWeek/index.js"], "names": ["toDate", "startOfUTCWeek", "startOfUTCWeekYear", "requiredArgs", "MILLISECONDS_IN_WEEK", "getUTCWeek", "dirtyDate", "options", "arguments", "date", "diff", "getTime", "Math", "round"], "mappings": "AAAA,OAAOA,MAAP,MAAmB,uBAAnB;AACA,OAAOC,cAAP,MAA2B,4BAA3B;AACA,OAAOC,kBAAP,MAA+B,gCAA/B;AACA,OAAOC,YAAP,MAAyB,0BAAzB;AACA,IAAIC,oBAAoB,GAAG,SAA3B,C,CAAsC;AACtC;;AAEA,eAAe,SAASC,UAAT,CAAoBC,SAApB,EAA+BC,OAA/B,EAAwC;AACrDJ,EAAAA,YAAY,CAAC,CAAD,EAAIK,SAAJ,CAAZ;AACA,MAAIC,IAAI,GAAGT,MAAM,CAACM,SAAD,CAAjB;AACA,MAAII,IAAI,GAAGT,cAAc,CAACQ,IAAD,EAAOF,OAAP,CAAd,CAA8BI,OAA9B,KAA0CT,kBAAkB,CAACO,IAAD,EAAOF,OAAP,CAAlB,CAAkCI,OAAlC,EAArD,CAHqD,CAG6C;AAClG;AACA;;AAEA,SAAOC,IAAI,CAACC,KAAL,CAAWH,IAAI,GAAGN,oBAAlB,IAA0C,CAAjD;AACD", "sourcesContent": ["import toDate from \"../../toDate/index.js\";\nimport startOfUTCWeek from \"../startOfUTCWeek/index.js\";\nimport startOfUTCWeekYear from \"../startOfUTCWeekYear/index.js\";\nimport requiredArgs from \"../requiredArgs/index.js\";\nvar MILLISECONDS_IN_WEEK = 604800000; // This function will be a part of public API when UTC function will be implemented.\n// See issue: https://github.com/date-fns/date-fns/issues/376\n\nexport default function getUTCWeek(dirtyDate, options) {\n  requiredArgs(1, arguments);\n  var date = toDate(dirtyDate);\n  var diff = startOfUTCWeek(date, options).getTime() - startOfUTCWeekYear(date, options).getTime(); // Round the number of days to the nearest integer\n  // because the number of milliseconds in a week is not constant\n  // (e.g. it's different in the week of the daylight saving time clock shift)\n\n  return Math.round(diff / MILLISECONDS_IN_WEEK) + 1;\n}"]}, "metadata": {}, "sourceType": "module"}
{"ast": null, "code": "import { ValueWithRandom } from \"../../../ValueWithRandom.js\";\nimport { deepExtend } from \"../../../../../Utils/Utils.js\";\nexport class MovePath {\n  constructor() {\n    this.clamp = true;\n    this.delay = new ValueWithRandom();\n    this.enable = false;\n    this.options = {};\n  }\n  load(data) {\n    if (!data) {\n      return;\n    }\n    if (data.clamp !== undefined) {\n      this.clamp = data.clamp;\n    }\n    this.delay.load(data.delay);\n    if (data.enable !== undefined) {\n      this.enable = data.enable;\n    }\n    this.generator = data.generator;\n    if (data.options) {\n      this.options = deepExtend(this.options, data.options);\n    }\n  }\n}", "map": {"version": 3, "names": ["ValueWithRandom", "deepExtend", "MovePath", "constructor", "clamp", "delay", "enable", "options", "load", "data", "undefined", "generator"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/engine/browser/Options/Classes/Particles/Move/Path/MovePath.js"], "sourcesContent": ["import { ValueWithRandom } from \"../../../ValueWithRandom.js\";\nimport { deepExtend } from \"../../../../../Utils/Utils.js\";\nexport class MovePath {\n    constructor() {\n        this.clamp = true;\n        this.delay = new ValueWithRandom();\n        this.enable = false;\n        this.options = {};\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.clamp !== undefined) {\n            this.clamp = data.clamp;\n        }\n        this.delay.load(data.delay);\n        if (data.enable !== undefined) {\n            this.enable = data.enable;\n        }\n        this.generator = data.generator;\n        if (data.options) {\n            this.options = deepExtend(this.options, data.options);\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,6BAA6B;AAC7D,SAASC,UAAU,QAAQ,+BAA+B;AAC1D,OAAO,MAAMC,QAAQ,CAAC;EAClBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,KAAK,GAAG,IAAI;IACjB,IAAI,CAACC,KAAK,GAAG,IAAIL,eAAe,CAAC,CAAC;IAClC,IAAI,CAACM,MAAM,GAAG,KAAK;IACnB,IAAI,CAACC,OAAO,GAAG,CAAC,CAAC;EACrB;EACAC,IAAIA,CAACC,IAAI,EAAE;IACP,IAAI,CAACA,IAAI,EAAE;MACP;IACJ;IACA,IAAIA,IAAI,CAACL,KAAK,KAAKM,SAAS,EAAE;MAC1B,IAAI,CAACN,KAAK,GAAGK,IAAI,CAACL,KAAK;IAC3B;IACA,IAAI,CAACC,KAAK,CAACG,IAAI,CAACC,IAAI,CAACJ,KAAK,CAAC;IAC3B,IAAII,IAAI,CAACH,MAAM,KAAKI,SAAS,EAAE;MAC3B,IAAI,CAACJ,MAAM,GAAGG,IAAI,CAACH,MAAM;IAC7B;IACA,IAAI,CAACK,SAAS,GAAGF,IAAI,CAACE,SAAS;IAC/B,IAAIF,IAAI,CAACF,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,GAAGN,UAAU,CAAC,IAAI,CAACM,OAAO,EAAEE,IAAI,CAACF,OAAO,CAAC;IACzD;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
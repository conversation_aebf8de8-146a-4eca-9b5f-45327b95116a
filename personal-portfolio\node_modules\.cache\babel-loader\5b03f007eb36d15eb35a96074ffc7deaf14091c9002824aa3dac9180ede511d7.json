{"ast": null, "code": "import { RollUpdater } from \"./RollUpdater.js\";\nexport async function loadRollUpdater(engine, refresh = true) {\n  await engine.addParticleUpdater(\"roll\", () => {\n    return Promise.resolve(new RollUpdater());\n  }, refresh);\n}", "map": {"version": 3, "names": ["RollUpdater", "loadRollUpdater", "engine", "refresh", "addParticleUpdater", "Promise", "resolve"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/updater-roll/browser/index.js"], "sourcesContent": ["import { RollUpdater } from \"./RollUpdater.js\";\nexport async function loadRollUpdater(engine, refresh = true) {\n    await engine.addParticleUpdater(\"roll\", () => {\n        return Promise.resolve(new RollUpdater());\n    }, refresh);\n}\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,kBAAkB;AAC9C,OAAO,eAAeC,eAAeA,CAACC,MAAM,EAAEC,OAAO,GAAG,IAAI,EAAE;EAC1D,MAAMD,MAAM,CAACE,kBAAkB,CAAC,MAAM,EAAE,MAAM;IAC1C,OAAOC,OAAO,CAACC,OAAO,CAAC,IAAIN,WAAW,CAAC,CAAC,CAAC;EAC7C,CAAC,EAAEG,OAAO,CAAC;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
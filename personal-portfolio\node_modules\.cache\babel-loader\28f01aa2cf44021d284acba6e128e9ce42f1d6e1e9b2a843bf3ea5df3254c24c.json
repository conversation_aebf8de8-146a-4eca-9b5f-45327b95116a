{"ast": null, "code": "import { executeOnSingleOrMultiple } from \"@tsparticles/engine\";\nimport { RepulseBase } from \"./RepulseBase.js\";\nimport { RepulseDiv } from \"./RepulseDiv.js\";\nexport class Repulse extends RepulseBase {\n  load(data) {\n    super.load(data);\n    if (!data) {\n      return;\n    }\n    this.divs = executeOnSingleOrMultiple(data.divs, div => {\n      const tmp = new RepulseDiv();\n      tmp.load(div);\n      return tmp;\n    });\n  }\n}", "map": {"version": 3, "names": ["executeOnSingleOrMultiple", "RepulseBase", "RepulseDiv", "Repulse", "load", "data", "divs", "div", "tmp"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/interaction-external-repulse/browser/Options/Classes/Repulse.js"], "sourcesContent": ["import { executeOnSingleOrMultiple, } from \"@tsparticles/engine\";\nimport { RepulseBase } from \"./RepulseBase.js\";\nimport { RepulseDiv } from \"./RepulseDiv.js\";\nexport class Repulse extends RepulseBase {\n    load(data) {\n        super.load(data);\n        if (!data) {\n            return;\n        }\n        this.divs = executeOnSingleOrMultiple(data.divs, div => {\n            const tmp = new RepulseDiv();\n            tmp.load(div);\n            return tmp;\n        });\n    }\n}\n"], "mappings": "AAAA,SAASA,yBAAyB,QAAS,qBAAqB;AAChE,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,UAAU,QAAQ,iBAAiB;AAC5C,OAAO,MAAMC,OAAO,SAASF,WAAW,CAAC;EACrCG,IAAIA,CAACC,IAAI,EAAE;IACP,KAAK,CAACD,IAAI,CAACC,IAAI,CAAC;IAChB,IAAI,CAACA,IAAI,EAAE;MACP;IACJ;IACA,IAAI,CAACC,IAAI,GAAGN,yBAAyB,CAACK,IAAI,CAACC,IAAI,EAAEC,GAAG,IAAI;MACpD,MAAMC,GAAG,GAAG,IAAIN,UAAU,CAAC,CAAC;MAC5BM,GAAG,CAACJ,IAAI,CAACG,GAAG,CAAC;MACb,OAAOC,GAAG;IACd,CAAC,CAAC;EACN;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { Attractor } from \"./Attractor\";\nexport async function loadParticlesAttractInteraction(engine) {\n  await engine.addInteractor(\"particlesAttract\", container => new Attractor(container));\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Interactions/Particles/Attract/index.js"], "names": ["Attractor", "loadParticlesAttractInteraction", "engine", "addInteractor", "container"], "mappings": "AAAA,SAASA,SAAT,QAA0B,aAA1B;AACA,OAAO,eAAeC,+BAAf,CAA+CC,MAA/C,EAAuD;AAC1D,QAAMA,MAAM,CAACC,aAAP,CAAqB,kBAArB,EAA0CC,SAAD,IAAe,IAAIJ,SAAJ,CAAcI,SAAd,CAAxD,CAAN;AACH", "sourcesContent": ["import { Attractor } from \"./Attractor\";\nexport async function loadParticlesAttractInteraction(engine) {\n    await engine.addInteractor(\"particlesAttract\", (container) => new Attractor(container));\n}\n"]}, "metadata": {}, "sourceType": "module"}
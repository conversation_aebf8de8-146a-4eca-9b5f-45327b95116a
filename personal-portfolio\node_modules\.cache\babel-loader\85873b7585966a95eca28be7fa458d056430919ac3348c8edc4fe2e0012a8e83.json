{"ast": null, "code": "import { Vector, calculateBounds, getDistances, getRandom, isPointInside, randomInRange } from \"tsparticles-engine\";\nexport class OutOutMode {\n  constructor(container) {\n    this.container = container;\n    this.modes = [\"out\"];\n  }\n  update(particle, direction, delta, outMode) {\n    if (!this.modes.includes(outMode)) {\n      return;\n    }\n    const container = this.container;\n    switch (particle.outType) {\n      case \"inside\":\n        {\n          const {\n            x: vx,\n            y: vy\n          } = particle.velocity;\n          const circVec = Vector.origin;\n          circVec.length = particle.moveCenter.radius;\n          circVec.angle = particle.velocity.angle + Math.PI;\n          circVec.addTo(Vector.create(particle.moveCenter));\n          const {\n            dx,\n            dy\n          } = getDistances(particle.position, circVec);\n          if (vx <= 0 && dx >= 0 || vy <= 0 && dy >= 0 || vx >= 0 && dx <= 0 || vy >= 0 && dy <= 0) {\n            return;\n          }\n          particle.position.x = Math.floor(randomInRange({\n            min: 0,\n            max: container.canvas.size.width\n          }));\n          particle.position.y = Math.floor(randomInRange({\n            min: 0,\n            max: container.canvas.size.height\n          }));\n          const {\n            dx: newDx,\n            dy: newDy\n          } = getDistances(particle.position, particle.moveCenter);\n          particle.direction = Math.atan2(-newDy, -newDx);\n          particle.velocity.angle = particle.direction;\n          break;\n        }\n      default:\n        {\n          if (isPointInside(particle.position, container.canvas.size, Vector.origin, particle.getRadius(), direction)) {\n            return;\n          }\n          switch (particle.outType) {\n            case \"outside\":\n              {\n                particle.position.x = Math.floor(randomInRange({\n                  min: -particle.moveCenter.radius,\n                  max: particle.moveCenter.radius\n                })) + particle.moveCenter.x;\n                particle.position.y = Math.floor(randomInRange({\n                  min: -particle.moveCenter.radius,\n                  max: particle.moveCenter.radius\n                })) + particle.moveCenter.y;\n                const {\n                  dx,\n                  dy\n                } = getDistances(particle.position, particle.moveCenter);\n                if (particle.moveCenter.radius) {\n                  particle.direction = Math.atan2(dy, dx);\n                  particle.velocity.angle = particle.direction;\n                }\n                break;\n              }\n            case \"normal\":\n              {\n                const wrap = particle.options.move.warp,\n                  canvasSize = container.canvas.size,\n                  newPos = {\n                    bottom: canvasSize.height + particle.getRadius() + particle.offset.y,\n                    left: -particle.getRadius() - particle.offset.x,\n                    right: canvasSize.width + particle.getRadius() + particle.offset.x,\n                    top: -particle.getRadius() - particle.offset.y\n                  },\n                  sizeValue = particle.getRadius(),\n                  nextBounds = calculateBounds(particle.position, sizeValue);\n                if (direction === \"right\" && nextBounds.left > canvasSize.width + particle.offset.x) {\n                  particle.position.x = newPos.left;\n                  particle.initialPosition.x = particle.position.x;\n                  if (!wrap) {\n                    particle.position.y = getRandom() * canvasSize.height;\n                    particle.initialPosition.y = particle.position.y;\n                  }\n                } else if (direction === \"left\" && nextBounds.right < -particle.offset.x) {\n                  particle.position.x = newPos.right;\n                  particle.initialPosition.x = particle.position.x;\n                  if (!wrap) {\n                    particle.position.y = getRandom() * canvasSize.height;\n                    particle.initialPosition.y = particle.position.y;\n                  }\n                }\n                if (direction === \"bottom\" && nextBounds.top > canvasSize.height + particle.offset.y) {\n                  if (!wrap) {\n                    particle.position.x = getRandom() * canvasSize.width;\n                    particle.initialPosition.x = particle.position.x;\n                  }\n                  particle.position.y = newPos.top;\n                  particle.initialPosition.y = particle.position.y;\n                } else if (direction === \"top\" && nextBounds.bottom < -particle.offset.y) {\n                  if (!wrap) {\n                    particle.position.x = getRandom() * canvasSize.width;\n                    particle.initialPosition.x = particle.position.x;\n                  }\n                  particle.position.y = newPos.bottom;\n                  particle.initialPosition.y = particle.position.y;\n                }\n                break;\n              }\n          }\n          break;\n        }\n    }\n  }\n}", "map": {"version": 3, "names": ["Vector", "calculateBounds", "getDistances", "getRandom", "isPointInside", "randomInRange", "OutOutMode", "constructor", "container", "modes", "update", "particle", "direction", "delta", "outMode", "includes", "outType", "x", "vx", "y", "vy", "velocity", "circVec", "origin", "length", "moveCenter", "radius", "angle", "Math", "PI", "addTo", "create", "dx", "dy", "position", "floor", "min", "max", "canvas", "size", "width", "height", "newDx", "newDy", "atan2", "getRadius", "wrap", "options", "move", "warp", "canvasSize", "newPos", "bottom", "offset", "left", "right", "top", "sizeValue", "nextBounds", "initialPosition"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/tsparticles-updater-out-modes/esm/OutOutMode.js"], "sourcesContent": ["import { Vector, calculateBounds, getDistances, getRandom, isPointInside, randomInRange, } from \"tsparticles-engine\";\nexport class OutOutMode {\n    constructor(container) {\n        this.container = container;\n        this.modes = [\"out\"];\n    }\n    update(particle, direction, delta, outMode) {\n        if (!this.modes.includes(outMode)) {\n            return;\n        }\n        const container = this.container;\n        switch (particle.outType) {\n            case \"inside\": {\n                const { x: vx, y: vy } = particle.velocity;\n                const circVec = Vector.origin;\n                circVec.length = particle.moveCenter.radius;\n                circVec.angle = particle.velocity.angle + Math.PI;\n                circVec.addTo(Vector.create(particle.moveCenter));\n                const { dx, dy } = getDistances(particle.position, circVec);\n                if ((vx <= 0 && dx >= 0) || (vy <= 0 && dy >= 0) || (vx >= 0 && dx <= 0) || (vy >= 0 && dy <= 0)) {\n                    return;\n                }\n                particle.position.x = Math.floor(randomInRange({\n                    min: 0,\n                    max: container.canvas.size.width,\n                }));\n                particle.position.y = Math.floor(randomInRange({\n                    min: 0,\n                    max: container.canvas.size.height,\n                }));\n                const { dx: newDx, dy: newDy } = getDistances(particle.position, particle.moveCenter);\n                particle.direction = Math.atan2(-newDy, -newDx);\n                particle.velocity.angle = particle.direction;\n                break;\n            }\n            default: {\n                if (isPointInside(particle.position, container.canvas.size, Vector.origin, particle.getRadius(), direction)) {\n                    return;\n                }\n                switch (particle.outType) {\n                    case \"outside\": {\n                        particle.position.x =\n                            Math.floor(randomInRange({\n                                min: -particle.moveCenter.radius,\n                                max: particle.moveCenter.radius,\n                            })) + particle.moveCenter.x;\n                        particle.position.y =\n                            Math.floor(randomInRange({\n                                min: -particle.moveCenter.radius,\n                                max: particle.moveCenter.radius,\n                            })) + particle.moveCenter.y;\n                        const { dx, dy } = getDistances(particle.position, particle.moveCenter);\n                        if (particle.moveCenter.radius) {\n                            particle.direction = Math.atan2(dy, dx);\n                            particle.velocity.angle = particle.direction;\n                        }\n                        break;\n                    }\n                    case \"normal\": {\n                        const wrap = particle.options.move.warp, canvasSize = container.canvas.size, newPos = {\n                            bottom: canvasSize.height + particle.getRadius() + particle.offset.y,\n                            left: -particle.getRadius() - particle.offset.x,\n                            right: canvasSize.width + particle.getRadius() + particle.offset.x,\n                            top: -particle.getRadius() - particle.offset.y,\n                        }, sizeValue = particle.getRadius(), nextBounds = calculateBounds(particle.position, sizeValue);\n                        if (direction === \"right\" &&\n                            nextBounds.left > canvasSize.width + particle.offset.x) {\n                            particle.position.x = newPos.left;\n                            particle.initialPosition.x = particle.position.x;\n                            if (!wrap) {\n                                particle.position.y = getRandom() * canvasSize.height;\n                                particle.initialPosition.y = particle.position.y;\n                            }\n                        }\n                        else if (direction === \"left\" && nextBounds.right < -particle.offset.x) {\n                            particle.position.x = newPos.right;\n                            particle.initialPosition.x = particle.position.x;\n                            if (!wrap) {\n                                particle.position.y = getRandom() * canvasSize.height;\n                                particle.initialPosition.y = particle.position.y;\n                            }\n                        }\n                        if (direction === \"bottom\" &&\n                            nextBounds.top > canvasSize.height + particle.offset.y) {\n                            if (!wrap) {\n                                particle.position.x = getRandom() * canvasSize.width;\n                                particle.initialPosition.x = particle.position.x;\n                            }\n                            particle.position.y = newPos.top;\n                            particle.initialPosition.y = particle.position.y;\n                        }\n                        else if (direction === \"top\" && nextBounds.bottom < -particle.offset.y) {\n                            if (!wrap) {\n                                particle.position.x = getRandom() * canvasSize.width;\n                                particle.initialPosition.x = particle.position.x;\n                            }\n                            particle.position.y = newPos.bottom;\n                            particle.initialPosition.y = particle.position.y;\n                        }\n                        break;\n                    }\n                }\n                break;\n            }\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,MAAM,EAAEC,eAAe,EAAEC,YAAY,EAAEC,SAAS,EAAEC,aAAa,EAAEC,aAAa,QAAS,oBAAoB;AACpH,OAAO,MAAMC,UAAU,CAAC;EACpBC,WAAWA,CAACC,SAAS,EAAE;IACnB,IAAI,CAACA,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,KAAK,GAAG,CAAC,KAAK,CAAC;EACxB;EACAC,MAAMA,CAACC,QAAQ,EAAEC,SAAS,EAAEC,KAAK,EAAEC,OAAO,EAAE;IACxC,IAAI,CAAC,IAAI,CAACL,KAAK,CAACM,QAAQ,CAACD,OAAO,CAAC,EAAE;MAC/B;IACJ;IACA,MAAMN,SAAS,GAAG,IAAI,CAACA,SAAS;IAChC,QAAQG,QAAQ,CAACK,OAAO;MACpB,KAAK,QAAQ;QAAE;UACX,MAAM;YAAEC,CAAC,EAAEC,EAAE;YAAEC,CAAC,EAAEC;UAAG,CAAC,GAAGT,QAAQ,CAACU,QAAQ;UAC1C,MAAMC,OAAO,GAAGtB,MAAM,CAACuB,MAAM;UAC7BD,OAAO,CAACE,MAAM,GAAGb,QAAQ,CAACc,UAAU,CAACC,MAAM;UAC3CJ,OAAO,CAACK,KAAK,GAAGhB,QAAQ,CAACU,QAAQ,CAACM,KAAK,GAAGC,IAAI,CAACC,EAAE;UACjDP,OAAO,CAACQ,KAAK,CAAC9B,MAAM,CAAC+B,MAAM,CAACpB,QAAQ,CAACc,UAAU,CAAC,CAAC;UACjD,MAAM;YAAEO,EAAE;YAAEC;UAAG,CAAC,GAAG/B,YAAY,CAACS,QAAQ,CAACuB,QAAQ,EAAEZ,OAAO,CAAC;UAC3D,IAAKJ,EAAE,IAAI,CAAC,IAAIc,EAAE,IAAI,CAAC,IAAMZ,EAAE,IAAI,CAAC,IAAIa,EAAE,IAAI,CAAE,IAAKf,EAAE,IAAI,CAAC,IAAIc,EAAE,IAAI,CAAE,IAAKZ,EAAE,IAAI,CAAC,IAAIa,EAAE,IAAI,CAAE,EAAE;YAC9F;UACJ;UACAtB,QAAQ,CAACuB,QAAQ,CAACjB,CAAC,GAAGW,IAAI,CAACO,KAAK,CAAC9B,aAAa,CAAC;YAC3C+B,GAAG,EAAE,CAAC;YACNC,GAAG,EAAE7B,SAAS,CAAC8B,MAAM,CAACC,IAAI,CAACC;UAC/B,CAAC,CAAC,CAAC;UACH7B,QAAQ,CAACuB,QAAQ,CAACf,CAAC,GAAGS,IAAI,CAACO,KAAK,CAAC9B,aAAa,CAAC;YAC3C+B,GAAG,EAAE,CAAC;YACNC,GAAG,EAAE7B,SAAS,CAAC8B,MAAM,CAACC,IAAI,CAACE;UAC/B,CAAC,CAAC,CAAC;UACH,MAAM;YAAET,EAAE,EAAEU,KAAK;YAAET,EAAE,EAAEU;UAAM,CAAC,GAAGzC,YAAY,CAACS,QAAQ,CAACuB,QAAQ,EAAEvB,QAAQ,CAACc,UAAU,CAAC;UACrFd,QAAQ,CAACC,SAAS,GAAGgB,IAAI,CAACgB,KAAK,CAAC,CAACD,KAAK,EAAE,CAACD,KAAK,CAAC;UAC/C/B,QAAQ,CAACU,QAAQ,CAACM,KAAK,GAAGhB,QAAQ,CAACC,SAAS;UAC5C;QACJ;MACA;QAAS;UACL,IAAIR,aAAa,CAACO,QAAQ,CAACuB,QAAQ,EAAE1B,SAAS,CAAC8B,MAAM,CAACC,IAAI,EAAEvC,MAAM,CAACuB,MAAM,EAAEZ,QAAQ,CAACkC,SAAS,CAAC,CAAC,EAAEjC,SAAS,CAAC,EAAE;YACzG;UACJ;UACA,QAAQD,QAAQ,CAACK,OAAO;YACpB,KAAK,SAAS;cAAE;gBACZL,QAAQ,CAACuB,QAAQ,CAACjB,CAAC,GACfW,IAAI,CAACO,KAAK,CAAC9B,aAAa,CAAC;kBACrB+B,GAAG,EAAE,CAACzB,QAAQ,CAACc,UAAU,CAACC,MAAM;kBAChCW,GAAG,EAAE1B,QAAQ,CAACc,UAAU,CAACC;gBAC7B,CAAC,CAAC,CAAC,GAAGf,QAAQ,CAACc,UAAU,CAACR,CAAC;gBAC/BN,QAAQ,CAACuB,QAAQ,CAACf,CAAC,GACfS,IAAI,CAACO,KAAK,CAAC9B,aAAa,CAAC;kBACrB+B,GAAG,EAAE,CAACzB,QAAQ,CAACc,UAAU,CAACC,MAAM;kBAChCW,GAAG,EAAE1B,QAAQ,CAACc,UAAU,CAACC;gBAC7B,CAAC,CAAC,CAAC,GAAGf,QAAQ,CAACc,UAAU,CAACN,CAAC;gBAC/B,MAAM;kBAAEa,EAAE;kBAAEC;gBAAG,CAAC,GAAG/B,YAAY,CAACS,QAAQ,CAACuB,QAAQ,EAAEvB,QAAQ,CAACc,UAAU,CAAC;gBACvE,IAAId,QAAQ,CAACc,UAAU,CAACC,MAAM,EAAE;kBAC5Bf,QAAQ,CAACC,SAAS,GAAGgB,IAAI,CAACgB,KAAK,CAACX,EAAE,EAAED,EAAE,CAAC;kBACvCrB,QAAQ,CAACU,QAAQ,CAACM,KAAK,GAAGhB,QAAQ,CAACC,SAAS;gBAChD;gBACA;cACJ;YACA,KAAK,QAAQ;cAAE;gBACX,MAAMkC,IAAI,GAAGnC,QAAQ,CAACoC,OAAO,CAACC,IAAI,CAACC,IAAI;kBAAEC,UAAU,GAAG1C,SAAS,CAAC8B,MAAM,CAACC,IAAI;kBAAEY,MAAM,GAAG;oBAClFC,MAAM,EAAEF,UAAU,CAACT,MAAM,GAAG9B,QAAQ,CAACkC,SAAS,CAAC,CAAC,GAAGlC,QAAQ,CAAC0C,MAAM,CAAClC,CAAC;oBACpEmC,IAAI,EAAE,CAAC3C,QAAQ,CAACkC,SAAS,CAAC,CAAC,GAAGlC,QAAQ,CAAC0C,MAAM,CAACpC,CAAC;oBAC/CsC,KAAK,EAAEL,UAAU,CAACV,KAAK,GAAG7B,QAAQ,CAACkC,SAAS,CAAC,CAAC,GAAGlC,QAAQ,CAAC0C,MAAM,CAACpC,CAAC;oBAClEuC,GAAG,EAAE,CAAC7C,QAAQ,CAACkC,SAAS,CAAC,CAAC,GAAGlC,QAAQ,CAAC0C,MAAM,CAAClC;kBACjD,CAAC;kBAAEsC,SAAS,GAAG9C,QAAQ,CAACkC,SAAS,CAAC,CAAC;kBAAEa,UAAU,GAAGzD,eAAe,CAACU,QAAQ,CAACuB,QAAQ,EAAEuB,SAAS,CAAC;gBAC/F,IAAI7C,SAAS,KAAK,OAAO,IACrB8C,UAAU,CAACJ,IAAI,GAAGJ,UAAU,CAACV,KAAK,GAAG7B,QAAQ,CAAC0C,MAAM,CAACpC,CAAC,EAAE;kBACxDN,QAAQ,CAACuB,QAAQ,CAACjB,CAAC,GAAGkC,MAAM,CAACG,IAAI;kBACjC3C,QAAQ,CAACgD,eAAe,CAAC1C,CAAC,GAAGN,QAAQ,CAACuB,QAAQ,CAACjB,CAAC;kBAChD,IAAI,CAAC6B,IAAI,EAAE;oBACPnC,QAAQ,CAACuB,QAAQ,CAACf,CAAC,GAAGhB,SAAS,CAAC,CAAC,GAAG+C,UAAU,CAACT,MAAM;oBACrD9B,QAAQ,CAACgD,eAAe,CAACxC,CAAC,GAAGR,QAAQ,CAACuB,QAAQ,CAACf,CAAC;kBACpD;gBACJ,CAAC,MACI,IAAIP,SAAS,KAAK,MAAM,IAAI8C,UAAU,CAACH,KAAK,GAAG,CAAC5C,QAAQ,CAAC0C,MAAM,CAACpC,CAAC,EAAE;kBACpEN,QAAQ,CAACuB,QAAQ,CAACjB,CAAC,GAAGkC,MAAM,CAACI,KAAK;kBAClC5C,QAAQ,CAACgD,eAAe,CAAC1C,CAAC,GAAGN,QAAQ,CAACuB,QAAQ,CAACjB,CAAC;kBAChD,IAAI,CAAC6B,IAAI,EAAE;oBACPnC,QAAQ,CAACuB,QAAQ,CAACf,CAAC,GAAGhB,SAAS,CAAC,CAAC,GAAG+C,UAAU,CAACT,MAAM;oBACrD9B,QAAQ,CAACgD,eAAe,CAACxC,CAAC,GAAGR,QAAQ,CAACuB,QAAQ,CAACf,CAAC;kBACpD;gBACJ;gBACA,IAAIP,SAAS,KAAK,QAAQ,IACtB8C,UAAU,CAACF,GAAG,GAAGN,UAAU,CAACT,MAAM,GAAG9B,QAAQ,CAAC0C,MAAM,CAAClC,CAAC,EAAE;kBACxD,IAAI,CAAC2B,IAAI,EAAE;oBACPnC,QAAQ,CAACuB,QAAQ,CAACjB,CAAC,GAAGd,SAAS,CAAC,CAAC,GAAG+C,UAAU,CAACV,KAAK;oBACpD7B,QAAQ,CAACgD,eAAe,CAAC1C,CAAC,GAAGN,QAAQ,CAACuB,QAAQ,CAACjB,CAAC;kBACpD;kBACAN,QAAQ,CAACuB,QAAQ,CAACf,CAAC,GAAGgC,MAAM,CAACK,GAAG;kBAChC7C,QAAQ,CAACgD,eAAe,CAACxC,CAAC,GAAGR,QAAQ,CAACuB,QAAQ,CAACf,CAAC;gBACpD,CAAC,MACI,IAAIP,SAAS,KAAK,KAAK,IAAI8C,UAAU,CAACN,MAAM,GAAG,CAACzC,QAAQ,CAAC0C,MAAM,CAAClC,CAAC,EAAE;kBACpE,IAAI,CAAC2B,IAAI,EAAE;oBACPnC,QAAQ,CAACuB,QAAQ,CAACjB,CAAC,GAAGd,SAAS,CAAC,CAAC,GAAG+C,UAAU,CAACV,KAAK;oBACpD7B,QAAQ,CAACgD,eAAe,CAAC1C,CAAC,GAAGN,QAAQ,CAACuB,QAAQ,CAACjB,CAAC;kBACpD;kBACAN,QAAQ,CAACuB,QAAQ,CAACf,CAAC,GAAGgC,MAAM,CAACC,MAAM;kBACnCzC,QAAQ,CAACgD,eAAe,CAACxC,CAAC,GAAGR,QAAQ,CAACuB,QAAQ,CAACf,CAAC;gBACpD;gBACA;cACJ;UACJ;UACA;QACJ;IACJ;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
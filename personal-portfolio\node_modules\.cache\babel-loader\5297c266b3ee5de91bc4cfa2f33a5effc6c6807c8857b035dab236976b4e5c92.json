{"ast": null, "code": "import { millisecondsToSeconds } from \"@tsparticles/engine\";\nconst defaultDistance = 0,\n  double = 2,\n  doublePI = Math.PI * double,\n  distanceFactor = 60;\nexport function updateWobble(particle, delta) {\n  const {\n      wobble: wobbleOptions\n    } = particle.options,\n    {\n      wobble\n    } = particle;\n  if (!wobbleOptions?.enable || !wobble) {\n    return;\n  }\n  const angleSpeed = wobble.angleSpeed * delta.factor,\n    moveSpeed = wobble.moveSpeed * delta.factor,\n    distance = moveSpeed * ((particle.retina.wobbleDistance ?? defaultDistance) * delta.factor) / (millisecondsToSeconds / distanceFactor),\n    max = doublePI,\n    {\n      position\n    } = particle;\n  wobble.angle += angleSpeed;\n  if (wobble.angle > max) {\n    wobble.angle -= max;\n  }\n  position.x += distance * Math.cos(wobble.angle);\n  position.y += distance * Math.abs(Math.sin(wobble.angle));\n}", "map": {"version": 3, "names": ["millisecondsToSeconds", "defaultDistance", "double", "doublePI", "Math", "PI", "distanceFactor", "updateWobble", "particle", "delta", "wobble", "wobbleOptions", "options", "enable", "angleSpeed", "factor", "moveSpeed", "distance", "retina", "wobbleDistance", "max", "position", "angle", "x", "cos", "y", "abs", "sin"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/updater-wobble/browser/Utils.js"], "sourcesContent": ["import { millisecondsToSeconds } from \"@tsparticles/engine\";\nconst defaultDistance = 0, double = 2, doublePI = Math.PI * double, distanceFactor = 60;\nexport function updateWobble(particle, delta) {\n    const { wobble: wobbleOptions } = particle.options, { wobble } = particle;\n    if (!wobbleOptions?.enable || !wobble) {\n        return;\n    }\n    const angleSpeed = wobble.angleSpeed * delta.factor, moveSpeed = wobble.moveSpeed * delta.factor, distance = (moveSpeed * ((particle.retina.wobbleDistance ?? defaultDistance) * delta.factor)) /\n        (millisecondsToSeconds / distanceFactor), max = doublePI, { position } = particle;\n    wobble.angle += angleSpeed;\n    if (wobble.angle > max) {\n        wobble.angle -= max;\n    }\n    position.x += distance * Math.cos(wobble.angle);\n    position.y += distance * Math.abs(Math.sin(wobble.angle));\n}\n"], "mappings": "AAAA,SAASA,qBAAqB,QAAQ,qBAAqB;AAC3D,MAAMC,eAAe,GAAG,CAAC;EAAEC,MAAM,GAAG,CAAC;EAAEC,QAAQ,GAAGC,IAAI,CAACC,EAAE,GAAGH,MAAM;EAAEI,cAAc,GAAG,EAAE;AACvF,OAAO,SAASC,YAAYA,CAACC,QAAQ,EAAEC,KAAK,EAAE;EAC1C,MAAM;MAAEC,MAAM,EAAEC;IAAc,CAAC,GAAGH,QAAQ,CAACI,OAAO;IAAE;MAAEF;IAAO,CAAC,GAAGF,QAAQ;EACzE,IAAI,CAACG,aAAa,EAAEE,MAAM,IAAI,CAACH,MAAM,EAAE;IACnC;EACJ;EACA,MAAMI,UAAU,GAAGJ,MAAM,CAACI,UAAU,GAAGL,KAAK,CAACM,MAAM;IAAEC,SAAS,GAAGN,MAAM,CAACM,SAAS,GAAGP,KAAK,CAACM,MAAM;IAAEE,QAAQ,GAAID,SAAS,IAAI,CAACR,QAAQ,CAACU,MAAM,CAACC,cAAc,IAAIlB,eAAe,IAAIQ,KAAK,CAACM,MAAM,CAAC,IACzLf,qBAAqB,GAAGM,cAAc,CAAC;IAAEc,GAAG,GAAGjB,QAAQ;IAAE;MAAEkB;IAAS,CAAC,GAAGb,QAAQ;EACrFE,MAAM,CAACY,KAAK,IAAIR,UAAU;EAC1B,IAAIJ,MAAM,CAACY,KAAK,GAAGF,GAAG,EAAE;IACpBV,MAAM,CAACY,KAAK,IAAIF,GAAG;EACvB;EACAC,QAAQ,CAACE,CAAC,IAAIN,QAAQ,GAAGb,IAAI,CAACoB,GAAG,CAACd,MAAM,CAACY,KAAK,CAAC;EAC/CD,QAAQ,CAACI,CAAC,IAAIR,QAAQ,GAAGb,IAAI,CAACsB,GAAG,CAACtB,IAAI,CAACuB,GAAG,CAACjB,MAAM,CAACY,KAAK,CAAC,CAAC;AAC7D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
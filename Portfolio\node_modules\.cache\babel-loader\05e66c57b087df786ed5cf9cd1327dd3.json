{"ast": null, "code": "import getBoundingClientRect from \"./getBoundingClientRect.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport getWindowScroll from \"./getWindowScroll.js\";\nexport default function getWindowScrollBarX(element) {\n  // If <html> has a CSS width greater than the viewport, then this will be\n  // incorrect for RTL.\n  // Popper 1 is broken in this case and never had a bug report so let's assume\n  // it's not an issue. I don't think anyone ever specifies width on <html>\n  // anyway.\n  // Browsers where the left scrollbar doesn't cause an issue report `0` for\n  // this (e.g. Edge 2019, IE11, Safari)\n  return getBoundingClientRect(getDocumentElement(element)).left + getWindowScroll(element).scrollLeft;\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/@popperjs/core/lib/dom-utils/getWindowScrollBarX.js"], "names": ["getBoundingClientRect", "getDocumentElement", "getWindowScroll", "getWindowScrollBarX", "element", "left", "scrollLeft"], "mappings": "AAAA,OAAOA,qBAAP,MAAkC,4BAAlC;AACA,OAAOC,kBAAP,MAA+B,yBAA/B;AACA,OAAOC,eAAP,MAA4B,sBAA5B;AACA,eAAe,SAASC,mBAAT,CAA6BC,OAA7B,EAAsC;AACnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAOJ,qBAAqB,CAACC,kBAAkB,CAACG,OAAD,CAAnB,CAArB,CAAmDC,IAAnD,GAA0DH,eAAe,CAACE,OAAD,CAAf,CAAyBE,UAA1F;AACD", "sourcesContent": ["import getBoundingClientRect from \"./getBoundingClientRect.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport getWindowScroll from \"./getWindowScroll.js\";\nexport default function getWindowScrollBarX(element) {\n  // If <html> has a CSS width greater than the viewport, then this will be\n  // incorrect for RTL.\n  // Popper 1 is broken in this case and never had a bug report so let's assume\n  // it's not an issue. I don't think anyone ever specifies width on <html>\n  // anyway.\n  // Browsers where the left scrollbar doesn't cause an issue report `0` for\n  // this (e.g. Edge 2019, IE11, Safari)\n  return getBoundingClientRect(getDocumentElement(element)).left + getWindowScroll(element).scrollLeft;\n}"]}, "metadata": {}, "sourceType": "module"}
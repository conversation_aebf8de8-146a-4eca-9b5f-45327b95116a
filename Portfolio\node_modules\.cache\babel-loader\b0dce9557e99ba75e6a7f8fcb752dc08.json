{"ast": null, "code": "import { ThemeDefault } from \"./ThemeDefault\";\nimport { deepExtend } from \"../../../Utils\";\nexport class Theme {\n  constructor() {\n    this.name = \"\";\n    this.default = new ThemeDefault();\n  }\n\n  load(data) {\n    if (data === undefined) {\n      return;\n    }\n\n    if (data.name !== undefined) {\n      this.name = data.name;\n    }\n\n    this.default.load(data.default);\n\n    if (data.options !== undefined) {\n      this.options = deepExtend({}, data.options);\n    }\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Options/Classes/Theme/Theme.js"], "names": ["ThemeDefault", "deepExtend", "Theme", "constructor", "name", "default", "load", "data", "undefined", "options"], "mappings": "AAAA,SAASA,YAAT,QAA6B,gBAA7B;AACA,SAASC,UAAT,QAA2B,gBAA3B;AACA,OAAO,MAAMC,KAAN,CAAY;AACfC,EAAAA,WAAW,GAAG;AACV,SAAKC,IAAL,GAAY,EAAZ;AACA,SAAKC,OAAL,GAAe,IAAIL,YAAJ,EAAf;AACH;;AACDM,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,QAAIA,IAAI,KAAKC,SAAb,EAAwB;AACpB;AACH;;AACD,QAAID,IAAI,CAACH,IAAL,KAAcI,SAAlB,EAA6B;AACzB,WAAKJ,IAAL,GAAYG,IAAI,CAACH,IAAjB;AACH;;AACD,SAAKC,OAAL,CAAaC,IAAb,CAAkBC,IAAI,CAACF,OAAvB;;AACA,QAAIE,IAAI,CAACE,OAAL,KAAiBD,SAArB,EAAgC;AAC5B,WAAKC,OAAL,GAAeR,UAAU,CAAC,EAAD,EAAKM,IAAI,CAACE,OAAV,CAAzB;AACH;AACJ;;AAhBc", "sourcesContent": ["import { ThemeDefault } from \"./ThemeDefault\";\nimport { deepExtend } from \"../../../Utils\";\nexport class Theme {\n    constructor() {\n        this.name = \"\";\n        this.default = new ThemeDefault();\n    }\n    load(data) {\n        if (data === undefined) {\n            return;\n        }\n        if (data.name !== undefined) {\n            this.name = data.name;\n        }\n        this.default.load(data.default);\n        if (data.options !== undefined) {\n            this.options = deepExtend({}, data.options);\n        }\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
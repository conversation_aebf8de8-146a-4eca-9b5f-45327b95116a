{"ast": null, "code": "import { errorPrefix } from \"tsparticles-engine\";\nimport { replaceImageColor } from \"./Utils\";\nexport class ImageDrawer {\n  constructor(engine) {\n    this.loadImageShape = async imageShape => {\n      if (!this._engine.loadImage) {\n        throw new Error(`${errorPrefix} image shape not initialized`);\n      }\n      await this._engine.loadImage({\n        gif: imageShape.gif,\n        name: imageShape.name,\n        replaceColor: imageShape.replaceColor ?? imageShape.replace_color ?? false,\n        src: imageShape.src\n      });\n    };\n    this._engine = engine;\n  }\n  addImage(image) {\n    if (!this._engine.images) {\n      this._engine.images = [];\n    }\n    this._engine.images.push(image);\n  }\n  draw(context, particle, radius, opacity, delta) {\n    const image = particle.image,\n      element = image?.element;\n    if (!image) {\n      return;\n    }\n    context.globalAlpha = opacity;\n    if (image.gif && image.gifData) {\n      const offscreenCanvas = new OffscreenCanvas(image.gifData.width, image.gifData.height),\n        offscreenContext = offscreenCanvas.getContext(\"2d\");\n      if (!offscreenContext) {\n        throw new Error(\"could not create offscreen canvas context\");\n      }\n      offscreenContext.imageSmoothingQuality = \"low\";\n      offscreenContext.imageSmoothingEnabled = false;\n      offscreenContext.clearRect(0, 0, offscreenCanvas.width, offscreenCanvas.height);\n      if (particle.gifLoopCount === undefined) {\n        particle.gifLoopCount = image.gifLoopCount ?? 0;\n      }\n      let frameIndex = particle.gifFrame ?? 0;\n      const pos = {\n          x: -image.gifData.width * 0.5,\n          y: -image.gifData.height * 0.5\n        },\n        frame = image.gifData.frames[frameIndex];\n      if (particle.gifTime === undefined) {\n        particle.gifTime = 0;\n      }\n      if (!frame.bitmap) {\n        return;\n      }\n      context.scale(radius / image.gifData.width, radius / image.gifData.height);\n      switch (frame.disposalMethod) {\n        case 4:\n        case 5:\n        case 6:\n        case 7:\n        case 0:\n          offscreenContext.drawImage(frame.bitmap, frame.left, frame.top);\n          context.drawImage(offscreenCanvas, pos.x, pos.y);\n          offscreenContext.clearRect(0, 0, offscreenCanvas.width, offscreenCanvas.height);\n          break;\n        case 1:\n          offscreenContext.drawImage(frame.bitmap, frame.left, frame.top);\n          context.drawImage(offscreenCanvas, pos.x, pos.y);\n          break;\n        case 2:\n          offscreenContext.drawImage(frame.bitmap, frame.left, frame.top);\n          context.drawImage(offscreenCanvas, pos.x, pos.y);\n          offscreenContext.clearRect(0, 0, offscreenCanvas.width, offscreenCanvas.height);\n          if (image.gifData.globalColorTable.length === 0) {\n            offscreenContext.putImageData(image.gifData.frames[0].image, pos.x + frame.left, pos.y + frame.top);\n          } else {\n            offscreenContext.putImageData(image.gifData.backgroundImage, pos.x, pos.y);\n          }\n          break;\n        case 3:\n          {\n            const previousImageData = offscreenContext.getImageData(0, 0, offscreenCanvas.width, offscreenCanvas.height);\n            offscreenContext.drawImage(frame.bitmap, frame.left, frame.top);\n            context.drawImage(offscreenCanvas, pos.x, pos.y);\n            offscreenContext.clearRect(0, 0, offscreenCanvas.width, offscreenCanvas.height);\n            offscreenContext.putImageData(previousImageData, 0, 0);\n          }\n          break;\n      }\n      particle.gifTime += delta.value;\n      if (particle.gifTime > frame.delayTime) {\n        particle.gifTime -= frame.delayTime;\n        if (++frameIndex >= image.gifData.frames.length) {\n          if (--particle.gifLoopCount <= 0) {\n            return;\n          }\n          frameIndex = 0;\n          offscreenContext.clearRect(0, 0, offscreenCanvas.width, offscreenCanvas.height);\n        }\n        particle.gifFrame = frameIndex;\n      }\n      context.scale(image.gifData.width / radius, image.gifData.height / radius);\n    } else if (element) {\n      const ratio = image.ratio,\n        pos = {\n          x: -radius,\n          y: -radius\n        };\n      context.drawImage(element, pos.x, pos.y, radius * 2, radius * 2 / ratio);\n    }\n    context.globalAlpha = 1;\n  }\n  getSidesCount() {\n    return 12;\n  }\n  async init(container) {\n    const options = container.actualOptions;\n    if (!options.preload || !this._engine.loadImage) {\n      return;\n    }\n    for (const imageData of options.preload) {\n      await this._engine.loadImage(imageData);\n    }\n  }\n  loadShape(particle) {\n    if (particle.shape !== \"image\" && particle.shape !== \"images\") {\n      return;\n    }\n    if (!this._engine.images) {\n      this._engine.images = [];\n    }\n    const imageData = particle.shapeData,\n      image = this._engine.images.find(t => t.name === imageData.name || t.source === imageData.src);\n    if (!image) {\n      this.loadImageShape(imageData).then(() => {\n        this.loadShape(particle);\n      });\n    }\n  }\n  particleInit(container, particle) {\n    if (particle.shape !== \"image\" && particle.shape !== \"images\") {\n      return;\n    }\n    if (!this._engine.images) {\n      this._engine.images = [];\n    }\n    const images = this._engine.images,\n      imageData = particle.shapeData,\n      color = particle.getFillColor(),\n      image = images.find(t => t.name === imageData.name || t.source === imageData.src);\n    if (!image) {\n      return;\n    }\n    const replaceColor = imageData.replaceColor ?? imageData.replace_color ?? image.replaceColor;\n    if (image.loading) {\n      setTimeout(() => {\n        this.particleInit(container, particle);\n      });\n      return;\n    }\n    (async () => {\n      let imageRes;\n      if (image.svgData && color) {\n        imageRes = await replaceImageColor(image, imageData, color, particle);\n      } else {\n        imageRes = {\n          color,\n          data: image,\n          element: image.element,\n          gif: image.gif,\n          gifData: image.gifData,\n          gifLoopCount: image.gifLoopCount,\n          loaded: true,\n          ratio: imageData.width && imageData.height ? imageData.width / imageData.height : image.ratio ?? 1,\n          replaceColor: replaceColor,\n          source: imageData.src\n        };\n      }\n      if (!imageRes.ratio) {\n        imageRes.ratio = 1;\n      }\n      const fill = imageData.fill ?? particle.fill,\n        close = imageData.close ?? particle.close,\n        imageShape = {\n          image: imageRes,\n          fill,\n          close\n        };\n      particle.image = imageShape.image;\n      particle.fill = imageShape.fill;\n      particle.close = imageShape.close;\n    })();\n  }\n}", "map": {"version": 3, "names": ["errorPrefix", "replaceImageColor", "ImageDrawer", "constructor", "engine", "loadImageShape", "imageShape", "_engine", "loadImage", "Error", "gif", "name", "replaceColor", "replace_color", "src", "addImage", "image", "images", "push", "draw", "context", "particle", "radius", "opacity", "delta", "element", "globalAlpha", "gifData", "offscreenCanvas", "OffscreenCanvas", "width", "height", "offscreenContext", "getContext", "imageSmoothingQuality", "imageSmoothingEnabled", "clearRect", "gifLoopCount", "undefined", "frameIndex", "gif<PERSON><PERSON><PERSON>", "pos", "x", "y", "frame", "frames", "gifTime", "bitmap", "scale", "disposalMethod", "drawImage", "left", "top", "globalColorTable", "length", "putImageData", "backgroundImage", "previousImageData", "getImageData", "value", "delayTime", "ratio", "getSidesCount", "init", "container", "options", "actualOptions", "preload", "imageData", "loadShape", "shape", "shapeData", "find", "t", "source", "then", "particleInit", "color", "getFillColor", "loading", "setTimeout", "imageRes", "svgData", "data", "loaded", "fill", "close"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/tsparticles-shape-image/esm/ImageDrawer.js"], "sourcesContent": ["import { errorPrefix } from \"tsparticles-engine\";\nimport { replaceImageColor } from \"./Utils\";\nexport class ImageDrawer {\n    constructor(engine) {\n        this.loadImageShape = async (imageShape) => {\n            if (!this._engine.loadImage) {\n                throw new Error(`${errorPrefix} image shape not initialized`);\n            }\n            await this._engine.loadImage({\n                gif: imageShape.gif,\n                name: imageShape.name,\n                replaceColor: imageShape.replaceColor ?? imageShape.replace_color ?? false,\n                src: imageShape.src,\n            });\n        };\n        this._engine = engine;\n    }\n    addImage(image) {\n        if (!this._engine.images) {\n            this._engine.images = [];\n        }\n        this._engine.images.push(image);\n    }\n    draw(context, particle, radius, opacity, delta) {\n        const image = particle.image, element = image?.element;\n        if (!image) {\n            return;\n        }\n        context.globalAlpha = opacity;\n        if (image.gif && image.gifData) {\n            const offscreenCanvas = new OffscreenCanvas(image.gifData.width, image.gifData.height), offscreenContext = offscreenCanvas.getContext(\"2d\");\n            if (!offscreenContext) {\n                throw new Error(\"could not create offscreen canvas context\");\n            }\n            offscreenContext.imageSmoothingQuality = \"low\";\n            offscreenContext.imageSmoothingEnabled = false;\n            offscreenContext.clearRect(0, 0, offscreenCanvas.width, offscreenCanvas.height);\n            if (particle.gifLoopCount === undefined) {\n                particle.gifLoopCount = image.gifLoopCount ?? 0;\n            }\n            let frameIndex = particle.gifFrame ?? 0;\n            const pos = { x: -image.gifData.width * 0.5, y: -image.gifData.height * 0.5 }, frame = image.gifData.frames[frameIndex];\n            if (particle.gifTime === undefined) {\n                particle.gifTime = 0;\n            }\n            if (!frame.bitmap) {\n                return;\n            }\n            context.scale(radius / image.gifData.width, radius / image.gifData.height);\n            switch (frame.disposalMethod) {\n                case 4:\n                case 5:\n                case 6:\n                case 7:\n                case 0:\n                    offscreenContext.drawImage(frame.bitmap, frame.left, frame.top);\n                    context.drawImage(offscreenCanvas, pos.x, pos.y);\n                    offscreenContext.clearRect(0, 0, offscreenCanvas.width, offscreenCanvas.height);\n                    break;\n                case 1:\n                    offscreenContext.drawImage(frame.bitmap, frame.left, frame.top);\n                    context.drawImage(offscreenCanvas, pos.x, pos.y);\n                    break;\n                case 2:\n                    offscreenContext.drawImage(frame.bitmap, frame.left, frame.top);\n                    context.drawImage(offscreenCanvas, pos.x, pos.y);\n                    offscreenContext.clearRect(0, 0, offscreenCanvas.width, offscreenCanvas.height);\n                    if (image.gifData.globalColorTable.length === 0) {\n                        offscreenContext.putImageData(image.gifData.frames[0].image, pos.x + frame.left, pos.y + frame.top);\n                    }\n                    else {\n                        offscreenContext.putImageData(image.gifData.backgroundImage, pos.x, pos.y);\n                    }\n                    break;\n                case 3:\n                    {\n                        const previousImageData = offscreenContext.getImageData(0, 0, offscreenCanvas.width, offscreenCanvas.height);\n                        offscreenContext.drawImage(frame.bitmap, frame.left, frame.top);\n                        context.drawImage(offscreenCanvas, pos.x, pos.y);\n                        offscreenContext.clearRect(0, 0, offscreenCanvas.width, offscreenCanvas.height);\n                        offscreenContext.putImageData(previousImageData, 0, 0);\n                    }\n                    break;\n            }\n            particle.gifTime += delta.value;\n            if (particle.gifTime > frame.delayTime) {\n                particle.gifTime -= frame.delayTime;\n                if (++frameIndex >= image.gifData.frames.length) {\n                    if (--particle.gifLoopCount <= 0) {\n                        return;\n                    }\n                    frameIndex = 0;\n                    offscreenContext.clearRect(0, 0, offscreenCanvas.width, offscreenCanvas.height);\n                }\n                particle.gifFrame = frameIndex;\n            }\n            context.scale(image.gifData.width / radius, image.gifData.height / radius);\n        }\n        else if (element) {\n            const ratio = image.ratio, pos = {\n                x: -radius,\n                y: -radius,\n            };\n            context.drawImage(element, pos.x, pos.y, radius * 2, (radius * 2) / ratio);\n        }\n        context.globalAlpha = 1;\n    }\n    getSidesCount() {\n        return 12;\n    }\n    async init(container) {\n        const options = container.actualOptions;\n        if (!options.preload || !this._engine.loadImage) {\n            return;\n        }\n        for (const imageData of options.preload) {\n            await this._engine.loadImage(imageData);\n        }\n    }\n    loadShape(particle) {\n        if (particle.shape !== \"image\" && particle.shape !== \"images\") {\n            return;\n        }\n        if (!this._engine.images) {\n            this._engine.images = [];\n        }\n        const imageData = particle.shapeData, image = this._engine.images.find((t) => t.name === imageData.name || t.source === imageData.src);\n        if (!image) {\n            this.loadImageShape(imageData).then(() => {\n                this.loadShape(particle);\n            });\n        }\n    }\n    particleInit(container, particle) {\n        if (particle.shape !== \"image\" && particle.shape !== \"images\") {\n            return;\n        }\n        if (!this._engine.images) {\n            this._engine.images = [];\n        }\n        const images = this._engine.images, imageData = particle.shapeData, color = particle.getFillColor(), image = images.find((t) => t.name === imageData.name || t.source === imageData.src);\n        if (!image) {\n            return;\n        }\n        const replaceColor = imageData.replaceColor ?? imageData.replace_color ?? image.replaceColor;\n        if (image.loading) {\n            setTimeout(() => {\n                this.particleInit(container, particle);\n            });\n            return;\n        }\n        (async () => {\n            let imageRes;\n            if (image.svgData && color) {\n                imageRes = await replaceImageColor(image, imageData, color, particle);\n            }\n            else {\n                imageRes = {\n                    color,\n                    data: image,\n                    element: image.element,\n                    gif: image.gif,\n                    gifData: image.gifData,\n                    gifLoopCount: image.gifLoopCount,\n                    loaded: true,\n                    ratio: imageData.width && imageData.height ? imageData.width / imageData.height : image.ratio ?? 1,\n                    replaceColor: replaceColor,\n                    source: imageData.src,\n                };\n            }\n            if (!imageRes.ratio) {\n                imageRes.ratio = 1;\n            }\n            const fill = imageData.fill ?? particle.fill, close = imageData.close ?? particle.close, imageShape = {\n                image: imageRes,\n                fill,\n                close,\n            };\n            particle.image = imageShape.image;\n            particle.fill = imageShape.fill;\n            particle.close = imageShape.close;\n        })();\n    }\n}\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,SAASC,iBAAiB,QAAQ,SAAS;AAC3C,OAAO,MAAMC,WAAW,CAAC;EACrBC,WAAWA,CAACC,MAAM,EAAE;IAChB,IAAI,CAACC,cAAc,GAAG,MAAOC,UAAU,IAAK;MACxC,IAAI,CAAC,IAAI,CAACC,OAAO,CAACC,SAAS,EAAE;QACzB,MAAM,IAAIC,KAAK,CAAC,GAAGT,WAAW,8BAA8B,CAAC;MACjE;MACA,MAAM,IAAI,CAACO,OAAO,CAACC,SAAS,CAAC;QACzBE,GAAG,EAAEJ,UAAU,CAACI,GAAG;QACnBC,IAAI,EAAEL,UAAU,CAACK,IAAI;QACrBC,YAAY,EAAEN,UAAU,CAACM,YAAY,IAAIN,UAAU,CAACO,aAAa,IAAI,KAAK;QAC1EC,GAAG,EAAER,UAAU,CAACQ;MACpB,CAAC,CAAC;IACN,CAAC;IACD,IAAI,CAACP,OAAO,GAAGH,MAAM;EACzB;EACAW,QAAQA,CAACC,KAAK,EAAE;IACZ,IAAI,CAAC,IAAI,CAACT,OAAO,CAACU,MAAM,EAAE;MACtB,IAAI,CAACV,OAAO,CAACU,MAAM,GAAG,EAAE;IAC5B;IACA,IAAI,CAACV,OAAO,CAACU,MAAM,CAACC,IAAI,CAACF,KAAK,CAAC;EACnC;EACAG,IAAIA,CAACC,OAAO,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,OAAO,EAAEC,KAAK,EAAE;IAC5C,MAAMR,KAAK,GAAGK,QAAQ,CAACL,KAAK;MAAES,OAAO,GAAGT,KAAK,EAAES,OAAO;IACtD,IAAI,CAACT,KAAK,EAAE;MACR;IACJ;IACAI,OAAO,CAACM,WAAW,GAAGH,OAAO;IAC7B,IAAIP,KAAK,CAACN,GAAG,IAAIM,KAAK,CAACW,OAAO,EAAE;MAC5B,MAAMC,eAAe,GAAG,IAAIC,eAAe,CAACb,KAAK,CAACW,OAAO,CAACG,KAAK,EAAEd,KAAK,CAACW,OAAO,CAACI,MAAM,CAAC;QAAEC,gBAAgB,GAAGJ,eAAe,CAACK,UAAU,CAAC,IAAI,CAAC;MAC3I,IAAI,CAACD,gBAAgB,EAAE;QACnB,MAAM,IAAIvB,KAAK,CAAC,2CAA2C,CAAC;MAChE;MACAuB,gBAAgB,CAACE,qBAAqB,GAAG,KAAK;MAC9CF,gBAAgB,CAACG,qBAAqB,GAAG,KAAK;MAC9CH,gBAAgB,CAACI,SAAS,CAAC,CAAC,EAAE,CAAC,EAAER,eAAe,CAACE,KAAK,EAAEF,eAAe,CAACG,MAAM,CAAC;MAC/E,IAAIV,QAAQ,CAACgB,YAAY,KAAKC,SAAS,EAAE;QACrCjB,QAAQ,CAACgB,YAAY,GAAGrB,KAAK,CAACqB,YAAY,IAAI,CAAC;MACnD;MACA,IAAIE,UAAU,GAAGlB,QAAQ,CAACmB,QAAQ,IAAI,CAAC;MACvC,MAAMC,GAAG,GAAG;UAAEC,CAAC,EAAE,CAAC1B,KAAK,CAACW,OAAO,CAACG,KAAK,GAAG,GAAG;UAAEa,CAAC,EAAE,CAAC3B,KAAK,CAACW,OAAO,CAACI,MAAM,GAAG;QAAI,CAAC;QAAEa,KAAK,GAAG5B,KAAK,CAACW,OAAO,CAACkB,MAAM,CAACN,UAAU,CAAC;MACvH,IAAIlB,QAAQ,CAACyB,OAAO,KAAKR,SAAS,EAAE;QAChCjB,QAAQ,CAACyB,OAAO,GAAG,CAAC;MACxB;MACA,IAAI,CAACF,KAAK,CAACG,MAAM,EAAE;QACf;MACJ;MACA3B,OAAO,CAAC4B,KAAK,CAAC1B,MAAM,GAAGN,KAAK,CAACW,OAAO,CAACG,KAAK,EAAER,MAAM,GAAGN,KAAK,CAACW,OAAO,CAACI,MAAM,CAAC;MAC1E,QAAQa,KAAK,CAACK,cAAc;QACxB,KAAK,CAAC;QACN,KAAK,CAAC;QACN,KAAK,CAAC;QACN,KAAK,CAAC;QACN,KAAK,CAAC;UACFjB,gBAAgB,CAACkB,SAAS,CAACN,KAAK,CAACG,MAAM,EAAEH,KAAK,CAACO,IAAI,EAAEP,KAAK,CAACQ,GAAG,CAAC;UAC/DhC,OAAO,CAAC8B,SAAS,CAACtB,eAAe,EAAEa,GAAG,CAACC,CAAC,EAAED,GAAG,CAACE,CAAC,CAAC;UAChDX,gBAAgB,CAACI,SAAS,CAAC,CAAC,EAAE,CAAC,EAAER,eAAe,CAACE,KAAK,EAAEF,eAAe,CAACG,MAAM,CAAC;UAC/E;QACJ,KAAK,CAAC;UACFC,gBAAgB,CAACkB,SAAS,CAACN,KAAK,CAACG,MAAM,EAAEH,KAAK,CAACO,IAAI,EAAEP,KAAK,CAACQ,GAAG,CAAC;UAC/DhC,OAAO,CAAC8B,SAAS,CAACtB,eAAe,EAAEa,GAAG,CAACC,CAAC,EAAED,GAAG,CAACE,CAAC,CAAC;UAChD;QACJ,KAAK,CAAC;UACFX,gBAAgB,CAACkB,SAAS,CAACN,KAAK,CAACG,MAAM,EAAEH,KAAK,CAACO,IAAI,EAAEP,KAAK,CAACQ,GAAG,CAAC;UAC/DhC,OAAO,CAAC8B,SAAS,CAACtB,eAAe,EAAEa,GAAG,CAACC,CAAC,EAAED,GAAG,CAACE,CAAC,CAAC;UAChDX,gBAAgB,CAACI,SAAS,CAAC,CAAC,EAAE,CAAC,EAAER,eAAe,CAACE,KAAK,EAAEF,eAAe,CAACG,MAAM,CAAC;UAC/E,IAAIf,KAAK,CAACW,OAAO,CAAC0B,gBAAgB,CAACC,MAAM,KAAK,CAAC,EAAE;YAC7CtB,gBAAgB,CAACuB,YAAY,CAACvC,KAAK,CAACW,OAAO,CAACkB,MAAM,CAAC,CAAC,CAAC,CAAC7B,KAAK,EAAEyB,GAAG,CAACC,CAAC,GAAGE,KAAK,CAACO,IAAI,EAAEV,GAAG,CAACE,CAAC,GAAGC,KAAK,CAACQ,GAAG,CAAC;UACvG,CAAC,MACI;YACDpB,gBAAgB,CAACuB,YAAY,CAACvC,KAAK,CAACW,OAAO,CAAC6B,eAAe,EAAEf,GAAG,CAACC,CAAC,EAAED,GAAG,CAACE,CAAC,CAAC;UAC9E;UACA;QACJ,KAAK,CAAC;UACF;YACI,MAAMc,iBAAiB,GAAGzB,gBAAgB,CAAC0B,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE9B,eAAe,CAACE,KAAK,EAAEF,eAAe,CAACG,MAAM,CAAC;YAC5GC,gBAAgB,CAACkB,SAAS,CAACN,KAAK,CAACG,MAAM,EAAEH,KAAK,CAACO,IAAI,EAAEP,KAAK,CAACQ,GAAG,CAAC;YAC/DhC,OAAO,CAAC8B,SAAS,CAACtB,eAAe,EAAEa,GAAG,CAACC,CAAC,EAAED,GAAG,CAACE,CAAC,CAAC;YAChDX,gBAAgB,CAACI,SAAS,CAAC,CAAC,EAAE,CAAC,EAAER,eAAe,CAACE,KAAK,EAAEF,eAAe,CAACG,MAAM,CAAC;YAC/EC,gBAAgB,CAACuB,YAAY,CAACE,iBAAiB,EAAE,CAAC,EAAE,CAAC,CAAC;UAC1D;UACA;MACR;MACApC,QAAQ,CAACyB,OAAO,IAAItB,KAAK,CAACmC,KAAK;MAC/B,IAAItC,QAAQ,CAACyB,OAAO,GAAGF,KAAK,CAACgB,SAAS,EAAE;QACpCvC,QAAQ,CAACyB,OAAO,IAAIF,KAAK,CAACgB,SAAS;QACnC,IAAI,EAAErB,UAAU,IAAIvB,KAAK,CAACW,OAAO,CAACkB,MAAM,CAACS,MAAM,EAAE;UAC7C,IAAI,EAAEjC,QAAQ,CAACgB,YAAY,IAAI,CAAC,EAAE;YAC9B;UACJ;UACAE,UAAU,GAAG,CAAC;UACdP,gBAAgB,CAACI,SAAS,CAAC,CAAC,EAAE,CAAC,EAAER,eAAe,CAACE,KAAK,EAAEF,eAAe,CAACG,MAAM,CAAC;QACnF;QACAV,QAAQ,CAACmB,QAAQ,GAAGD,UAAU;MAClC;MACAnB,OAAO,CAAC4B,KAAK,CAAChC,KAAK,CAACW,OAAO,CAACG,KAAK,GAAGR,MAAM,EAAEN,KAAK,CAACW,OAAO,CAACI,MAAM,GAAGT,MAAM,CAAC;IAC9E,CAAC,MACI,IAAIG,OAAO,EAAE;MACd,MAAMoC,KAAK,GAAG7C,KAAK,CAAC6C,KAAK;QAAEpB,GAAG,GAAG;UAC7BC,CAAC,EAAE,CAACpB,MAAM;UACVqB,CAAC,EAAE,CAACrB;QACR,CAAC;MACDF,OAAO,CAAC8B,SAAS,CAACzB,OAAO,EAAEgB,GAAG,CAACC,CAAC,EAAED,GAAG,CAACE,CAAC,EAAErB,MAAM,GAAG,CAAC,EAAGA,MAAM,GAAG,CAAC,GAAIuC,KAAK,CAAC;IAC9E;IACAzC,OAAO,CAACM,WAAW,GAAG,CAAC;EAC3B;EACAoC,aAAaA,CAAA,EAAG;IACZ,OAAO,EAAE;EACb;EACA,MAAMC,IAAIA,CAACC,SAAS,EAAE;IAClB,MAAMC,OAAO,GAAGD,SAAS,CAACE,aAAa;IACvC,IAAI,CAACD,OAAO,CAACE,OAAO,IAAI,CAAC,IAAI,CAAC5D,OAAO,CAACC,SAAS,EAAE;MAC7C;IACJ;IACA,KAAK,MAAM4D,SAAS,IAAIH,OAAO,CAACE,OAAO,EAAE;MACrC,MAAM,IAAI,CAAC5D,OAAO,CAACC,SAAS,CAAC4D,SAAS,CAAC;IAC3C;EACJ;EACAC,SAASA,CAAChD,QAAQ,EAAE;IAChB,IAAIA,QAAQ,CAACiD,KAAK,KAAK,OAAO,IAAIjD,QAAQ,CAACiD,KAAK,KAAK,QAAQ,EAAE;MAC3D;IACJ;IACA,IAAI,CAAC,IAAI,CAAC/D,OAAO,CAACU,MAAM,EAAE;MACtB,IAAI,CAACV,OAAO,CAACU,MAAM,GAAG,EAAE;IAC5B;IACA,MAAMmD,SAAS,GAAG/C,QAAQ,CAACkD,SAAS;MAAEvD,KAAK,GAAG,IAAI,CAACT,OAAO,CAACU,MAAM,CAACuD,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAAC9D,IAAI,KAAKyD,SAAS,CAACzD,IAAI,IAAI8D,CAAC,CAACC,MAAM,KAAKN,SAAS,CAACtD,GAAG,CAAC;IACtI,IAAI,CAACE,KAAK,EAAE;MACR,IAAI,CAACX,cAAc,CAAC+D,SAAS,CAAC,CAACO,IAAI,CAAC,MAAM;QACtC,IAAI,CAACN,SAAS,CAAChD,QAAQ,CAAC;MAC5B,CAAC,CAAC;IACN;EACJ;EACAuD,YAAYA,CAACZ,SAAS,EAAE3C,QAAQ,EAAE;IAC9B,IAAIA,QAAQ,CAACiD,KAAK,KAAK,OAAO,IAAIjD,QAAQ,CAACiD,KAAK,KAAK,QAAQ,EAAE;MAC3D;IACJ;IACA,IAAI,CAAC,IAAI,CAAC/D,OAAO,CAACU,MAAM,EAAE;MACtB,IAAI,CAACV,OAAO,CAACU,MAAM,GAAG,EAAE;IAC5B;IACA,MAAMA,MAAM,GAAG,IAAI,CAACV,OAAO,CAACU,MAAM;MAAEmD,SAAS,GAAG/C,QAAQ,CAACkD,SAAS;MAAEM,KAAK,GAAGxD,QAAQ,CAACyD,YAAY,CAAC,CAAC;MAAE9D,KAAK,GAAGC,MAAM,CAACuD,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAAC9D,IAAI,KAAKyD,SAAS,CAACzD,IAAI,IAAI8D,CAAC,CAACC,MAAM,KAAKN,SAAS,CAACtD,GAAG,CAAC;IACxL,IAAI,CAACE,KAAK,EAAE;MACR;IACJ;IACA,MAAMJ,YAAY,GAAGwD,SAAS,CAACxD,YAAY,IAAIwD,SAAS,CAACvD,aAAa,IAAIG,KAAK,CAACJ,YAAY;IAC5F,IAAII,KAAK,CAAC+D,OAAO,EAAE;MACfC,UAAU,CAAC,MAAM;QACb,IAAI,CAACJ,YAAY,CAACZ,SAAS,EAAE3C,QAAQ,CAAC;MAC1C,CAAC,CAAC;MACF;IACJ;IACA,CAAC,YAAY;MACT,IAAI4D,QAAQ;MACZ,IAAIjE,KAAK,CAACkE,OAAO,IAAIL,KAAK,EAAE;QACxBI,QAAQ,GAAG,MAAMhF,iBAAiB,CAACe,KAAK,EAAEoD,SAAS,EAAES,KAAK,EAAExD,QAAQ,CAAC;MACzE,CAAC,MACI;QACD4D,QAAQ,GAAG;UACPJ,KAAK;UACLM,IAAI,EAAEnE,KAAK;UACXS,OAAO,EAAET,KAAK,CAACS,OAAO;UACtBf,GAAG,EAAEM,KAAK,CAACN,GAAG;UACdiB,OAAO,EAAEX,KAAK,CAACW,OAAO;UACtBU,YAAY,EAAErB,KAAK,CAACqB,YAAY;UAChC+C,MAAM,EAAE,IAAI;UACZvB,KAAK,EAAEO,SAAS,CAACtC,KAAK,IAAIsC,SAAS,CAACrC,MAAM,GAAGqC,SAAS,CAACtC,KAAK,GAAGsC,SAAS,CAACrC,MAAM,GAAGf,KAAK,CAAC6C,KAAK,IAAI,CAAC;UAClGjD,YAAY,EAAEA,YAAY;UAC1B8D,MAAM,EAAEN,SAAS,CAACtD;QACtB,CAAC;MACL;MACA,IAAI,CAACmE,QAAQ,CAACpB,KAAK,EAAE;QACjBoB,QAAQ,CAACpB,KAAK,GAAG,CAAC;MACtB;MACA,MAAMwB,IAAI,GAAGjB,SAAS,CAACiB,IAAI,IAAIhE,QAAQ,CAACgE,IAAI;QAAEC,KAAK,GAAGlB,SAAS,CAACkB,KAAK,IAAIjE,QAAQ,CAACiE,KAAK;QAAEhF,UAAU,GAAG;UAClGU,KAAK,EAAEiE,QAAQ;UACfI,IAAI;UACJC;QACJ,CAAC;MACDjE,QAAQ,CAACL,KAAK,GAAGV,UAAU,CAACU,KAAK;MACjCK,QAAQ,CAACgE,IAAI,GAAG/E,UAAU,CAAC+E,IAAI;MAC/BhE,QAAQ,CAACiE,KAAK,GAAGhF,UAAU,CAACgF,KAAK;IACrC,CAAC,EAAE,CAAC;EACR;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
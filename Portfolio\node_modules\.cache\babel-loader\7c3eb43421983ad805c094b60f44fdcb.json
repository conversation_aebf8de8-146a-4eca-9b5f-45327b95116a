{"ast": null, "code": "import { bounceHorizontal, bounceVertical } from \"./Utils\";\nimport { calculateBounds, isPointInside } from \"../../Utils\";\nexport class OutOfCanvasUpdater {\n  constructor(container) {\n    this.container = container;\n  }\n\n  init() {}\n\n  isEnabled(particle) {\n    return !particle.destroyed && !particle.spawning;\n  }\n\n  update(particle, delta) {\n    var _a, _b, _c, _d;\n\n    const outModes = particle.options.move.outModes;\n    this.updateOutMode(particle, delta, (_a = outModes.bottom) !== null && _a !== void 0 ? _a : outModes.default, \"bottom\");\n    this.updateOutMode(particle, delta, (_b = outModes.left) !== null && _b !== void 0 ? _b : outModes.default, \"left\");\n    this.updateOutMode(particle, delta, (_c = outModes.right) !== null && _c !== void 0 ? _c : outModes.default, \"right\");\n    this.updateOutMode(particle, delta, (_d = outModes.top) !== null && _d !== void 0 ? _d : outModes.default, \"top\");\n  }\n\n  updateOutMode(particle, delta, outMode, direction) {\n    switch (outMode) {\n      case \"bounce\":\n      case \"bounce-vertical\":\n      case \"bounce-horizontal\":\n      case \"bounceVertical\":\n      case \"bounceHorizontal\":\n      case \"split\":\n        this.bounce(particle, delta, direction, outMode);\n        break;\n\n      case \"destroy\":\n        this.destroy(particle, direction);\n        break;\n\n      case \"out\":\n        this.out(particle, direction);\n        break;\n\n      case \"none\":\n      default:\n        this.none(particle, direction);\n        break;\n    }\n  }\n\n  destroy(particle, direction) {\n    const container = this.container;\n\n    if (isPointInside(particle.position, container.canvas.size, particle.getRadius(), direction)) {\n      return;\n    }\n\n    container.particles.remove(particle, undefined, true);\n  }\n\n  out(particle, direction) {\n    const container = this.container;\n\n    if (isPointInside(particle.position, container.canvas.size, particle.getRadius(), direction)) {\n      return;\n    }\n\n    const wrap = particle.options.move.warp,\n          canvasSize = container.canvas.size,\n          newPos = {\n      bottom: canvasSize.height + particle.getRadius() + particle.offset.y,\n      left: -particle.getRadius() - particle.offset.x,\n      right: canvasSize.width + particle.getRadius() + particle.offset.x,\n      top: -particle.getRadius() - particle.offset.y\n    },\n          sizeValue = particle.getRadius(),\n          nextBounds = calculateBounds(particle.position, sizeValue);\n\n    if (direction === \"right\" && nextBounds.left > canvasSize.width + particle.offset.x) {\n      particle.position.x = newPos.left;\n      particle.initialPosition.x = particle.position.x;\n\n      if (!wrap) {\n        particle.position.y = Math.random() * canvasSize.height;\n        particle.initialPosition.y = particle.position.y;\n      }\n    } else if (direction === \"left\" && nextBounds.right < -particle.offset.x) {\n      particle.position.x = newPos.right;\n      particle.initialPosition.x = particle.position.x;\n\n      if (!wrap) {\n        particle.position.y = Math.random() * canvasSize.height;\n        particle.initialPosition.y = particle.position.y;\n      }\n    }\n\n    if (direction === \"bottom\" && nextBounds.top > canvasSize.height + particle.offset.y) {\n      if (!wrap) {\n        particle.position.x = Math.random() * canvasSize.width;\n        particle.initialPosition.x = particle.position.x;\n      }\n\n      particle.position.y = newPos.top;\n      particle.initialPosition.y = particle.position.y;\n    } else if (direction === \"top\" && nextBounds.bottom < -particle.offset.y) {\n      if (!wrap) {\n        particle.position.x = Math.random() * canvasSize.width;\n        particle.initialPosition.x = particle.position.x;\n      }\n\n      particle.position.y = newPos.bottom;\n      particle.initialPosition.y = particle.position.y;\n    }\n  }\n\n  bounce(particle, delta, direction, outMode) {\n    const container = this.container;\n    let handled = false;\n\n    for (const [, plugin] of container.plugins) {\n      if (plugin.particleBounce !== undefined) {\n        handled = plugin.particleBounce(particle, delta, direction);\n      }\n\n      if (handled) {\n        break;\n      }\n    }\n\n    if (handled) {\n      return;\n    }\n\n    const pos = particle.getPosition(),\n          offset = particle.offset,\n          size = particle.getRadius(),\n          bounds = calculateBounds(pos, size),\n          canvasSize = container.canvas.size;\n    bounceHorizontal({\n      particle,\n      outMode,\n      direction,\n      bounds,\n      canvasSize,\n      offset,\n      size\n    });\n    bounceVertical({\n      particle,\n      outMode,\n      direction,\n      bounds,\n      canvasSize,\n      offset,\n      size\n    });\n  }\n\n  none(particle, direction) {\n    if (particle.options.move.distance.horizontal && (direction === \"left\" || direction === \"right\") || particle.options.move.distance.vertical && (direction === \"top\" || direction === \"bottom\")) {\n      return;\n    }\n\n    const gravityOptions = particle.options.move.gravity,\n          container = this.container;\n    const canvasSize = container.canvas.size;\n    const pRadius = particle.getRadius();\n\n    if (!gravityOptions.enable) {\n      if (particle.velocity.y > 0 && particle.position.y <= canvasSize.height + pRadius || particle.velocity.y < 0 && particle.position.y >= -pRadius || particle.velocity.x > 0 && particle.position.x <= canvasSize.width + pRadius || particle.velocity.x < 0 && particle.position.x >= -pRadius) {\n        return;\n      }\n\n      if (!isPointInside(particle.position, container.canvas.size, pRadius, direction)) {\n        container.particles.remove(particle);\n      }\n    } else {\n      const position = particle.position;\n\n      if (!gravityOptions.inverse && position.y > canvasSize.height + pRadius && direction === \"bottom\" || gravityOptions.inverse && position.y < -pRadius && direction === \"top\") {\n        container.particles.remove(particle);\n      }\n    }\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Updaters/OutModes/OutOfCanvasUpdater.js"], "names": ["bounceHorizontal", "bounceVertical", "calculateBounds", "isPointInside", "OutOfCanvasUpdater", "constructor", "container", "init", "isEnabled", "particle", "destroyed", "spawning", "update", "delta", "_a", "_b", "_c", "_d", "outModes", "options", "move", "updateOutMode", "bottom", "default", "left", "right", "top", "outMode", "direction", "bounce", "destroy", "out", "none", "position", "canvas", "size", "getRadius", "particles", "remove", "undefined", "wrap", "warp", "canvasSize", "newPos", "height", "offset", "y", "x", "width", "sizeValue", "nextBounds", "initialPosition", "Math", "random", "handled", "plugin", "plugins", "particleBounce", "pos", "getPosition", "bounds", "distance", "horizontal", "vertical", "gravityOptions", "gravity", "pRadius", "enable", "velocity", "inverse"], "mappings": "AAAA,SAASA,gBAAT,EAA2BC,cAA3B,QAAiD,SAAjD;AACA,SAASC,eAAT,EAA0BC,aAA1B,QAA+C,aAA/C;AACA,OAAO,MAAMC,kBAAN,CAAyB;AAC5BC,EAAAA,WAAW,CAACC,SAAD,EAAY;AACnB,SAAKA,SAAL,GAAiBA,SAAjB;AACH;;AACDC,EAAAA,IAAI,GAAG,CACN;;AACDC,EAAAA,SAAS,CAACC,QAAD,EAAW;AAChB,WAAO,CAACA,QAAQ,CAACC,SAAV,IAAuB,CAACD,QAAQ,CAACE,QAAxC;AACH;;AACDC,EAAAA,MAAM,CAACH,QAAD,EAAWI,KAAX,EAAkB;AACpB,QAAIC,EAAJ,EAAQC,EAAR,EAAYC,EAAZ,EAAgBC,EAAhB;;AACA,UAAMC,QAAQ,GAAGT,QAAQ,CAACU,OAAT,CAAiBC,IAAjB,CAAsBF,QAAvC;AACA,SAAKG,aAAL,CAAmBZ,QAAnB,EAA6BI,KAA7B,EAAoC,CAACC,EAAE,GAAGI,QAAQ,CAACI,MAAf,MAA2B,IAA3B,IAAmCR,EAAE,KAAK,KAAK,CAA/C,GAAmDA,EAAnD,GAAwDI,QAAQ,CAACK,OAArG,EAA8G,QAA9G;AACA,SAAKF,aAAL,CAAmBZ,QAAnB,EAA6BI,KAA7B,EAAoC,CAACE,EAAE,GAAGG,QAAQ,CAACM,IAAf,MAAyB,IAAzB,IAAiCT,EAAE,KAAK,KAAK,CAA7C,GAAiDA,EAAjD,GAAsDG,QAAQ,CAACK,OAAnG,EAA4G,MAA5G;AACA,SAAKF,aAAL,CAAmBZ,QAAnB,EAA6BI,KAA7B,EAAoC,CAACG,EAAE,GAAGE,QAAQ,CAACO,KAAf,MAA0B,IAA1B,IAAkCT,EAAE,KAAK,KAAK,CAA9C,GAAkDA,EAAlD,GAAuDE,QAAQ,CAACK,OAApG,EAA6G,OAA7G;AACA,SAAKF,aAAL,CAAmBZ,QAAnB,EAA6BI,KAA7B,EAAoC,CAACI,EAAE,GAAGC,QAAQ,CAACQ,GAAf,MAAwB,IAAxB,IAAgCT,EAAE,KAAK,KAAK,CAA5C,GAAgDA,EAAhD,GAAqDC,QAAQ,CAACK,OAAlG,EAA2G,KAA3G;AACH;;AACDF,EAAAA,aAAa,CAACZ,QAAD,EAAWI,KAAX,EAAkBc,OAAlB,EAA2BC,SAA3B,EAAsC;AAC/C,YAAQD,OAAR;AACI,WAAK,QAAL;AACA,WAAK,iBAAL;AACA,WAAK,mBAAL;AACA,WAAK,gBAAL;AACA,WAAK,kBAAL;AACA,WAAK,OAAL;AACI,aAAKE,MAAL,CAAYpB,QAAZ,EAAsBI,KAAtB,EAA6Be,SAA7B,EAAwCD,OAAxC;AACA;;AACJ,WAAK,SAAL;AACI,aAAKG,OAAL,CAAarB,QAAb,EAAuBmB,SAAvB;AACA;;AACJ,WAAK,KAAL;AACI,aAAKG,GAAL,CAAStB,QAAT,EAAmBmB,SAAnB;AACA;;AACJ,WAAK,MAAL;AACA;AACI,aAAKI,IAAL,CAAUvB,QAAV,EAAoBmB,SAApB;AACA;AAlBR;AAoBH;;AACDE,EAAAA,OAAO,CAACrB,QAAD,EAAWmB,SAAX,EAAsB;AACzB,UAAMtB,SAAS,GAAG,KAAKA,SAAvB;;AACA,QAAIH,aAAa,CAACM,QAAQ,CAACwB,QAAV,EAAoB3B,SAAS,CAAC4B,MAAV,CAAiBC,IAArC,EAA2C1B,QAAQ,CAAC2B,SAAT,EAA3C,EAAiER,SAAjE,CAAjB,EAA8F;AAC1F;AACH;;AACDtB,IAAAA,SAAS,CAAC+B,SAAV,CAAoBC,MAApB,CAA2B7B,QAA3B,EAAqC8B,SAArC,EAAgD,IAAhD;AACH;;AACDR,EAAAA,GAAG,CAACtB,QAAD,EAAWmB,SAAX,EAAsB;AACrB,UAAMtB,SAAS,GAAG,KAAKA,SAAvB;;AACA,QAAIH,aAAa,CAACM,QAAQ,CAACwB,QAAV,EAAoB3B,SAAS,CAAC4B,MAAV,CAAiBC,IAArC,EAA2C1B,QAAQ,CAAC2B,SAAT,EAA3C,EAAiER,SAAjE,CAAjB,EAA8F;AAC1F;AACH;;AACD,UAAMY,IAAI,GAAG/B,QAAQ,CAACU,OAAT,CAAiBC,IAAjB,CAAsBqB,IAAnC;AAAA,UAAyCC,UAAU,GAAGpC,SAAS,CAAC4B,MAAV,CAAiBC,IAAvE;AAAA,UAA6EQ,MAAM,GAAG;AAClFrB,MAAAA,MAAM,EAAEoB,UAAU,CAACE,MAAX,GAAoBnC,QAAQ,CAAC2B,SAAT,EAApB,GAA2C3B,QAAQ,CAACoC,MAAT,CAAgBC,CADe;AAElFtB,MAAAA,IAAI,EAAE,CAACf,QAAQ,CAAC2B,SAAT,EAAD,GAAwB3B,QAAQ,CAACoC,MAAT,CAAgBE,CAFoC;AAGlFtB,MAAAA,KAAK,EAAEiB,UAAU,CAACM,KAAX,GAAmBvC,QAAQ,CAAC2B,SAAT,EAAnB,GAA0C3B,QAAQ,CAACoC,MAAT,CAAgBE,CAHiB;AAIlFrB,MAAAA,GAAG,EAAE,CAACjB,QAAQ,CAAC2B,SAAT,EAAD,GAAwB3B,QAAQ,CAACoC,MAAT,CAAgBC;AAJqC,KAAtF;AAAA,UAKGG,SAAS,GAAGxC,QAAQ,CAAC2B,SAAT,EALf;AAAA,UAKqCc,UAAU,GAAGhD,eAAe,CAACO,QAAQ,CAACwB,QAAV,EAAoBgB,SAApB,CALjE;;AAMA,QAAIrB,SAAS,KAAK,OAAd,IAAyBsB,UAAU,CAAC1B,IAAX,GAAkBkB,UAAU,CAACM,KAAX,GAAmBvC,QAAQ,CAACoC,MAAT,CAAgBE,CAAlF,EAAqF;AACjFtC,MAAAA,QAAQ,CAACwB,QAAT,CAAkBc,CAAlB,GAAsBJ,MAAM,CAACnB,IAA7B;AACAf,MAAAA,QAAQ,CAAC0C,eAAT,CAAyBJ,CAAzB,GAA6BtC,QAAQ,CAACwB,QAAT,CAAkBc,CAA/C;;AACA,UAAI,CAACP,IAAL,EAAW;AACP/B,QAAAA,QAAQ,CAACwB,QAAT,CAAkBa,CAAlB,GAAsBM,IAAI,CAACC,MAAL,KAAgBX,UAAU,CAACE,MAAjD;AACAnC,QAAAA,QAAQ,CAAC0C,eAAT,CAAyBL,CAAzB,GAA6BrC,QAAQ,CAACwB,QAAT,CAAkBa,CAA/C;AACH;AACJ,KAPD,MAQK,IAAIlB,SAAS,KAAK,MAAd,IAAwBsB,UAAU,CAACzB,KAAX,GAAmB,CAAChB,QAAQ,CAACoC,MAAT,CAAgBE,CAAhE,EAAmE;AACpEtC,MAAAA,QAAQ,CAACwB,QAAT,CAAkBc,CAAlB,GAAsBJ,MAAM,CAAClB,KAA7B;AACAhB,MAAAA,QAAQ,CAAC0C,eAAT,CAAyBJ,CAAzB,GAA6BtC,QAAQ,CAACwB,QAAT,CAAkBc,CAA/C;;AACA,UAAI,CAACP,IAAL,EAAW;AACP/B,QAAAA,QAAQ,CAACwB,QAAT,CAAkBa,CAAlB,GAAsBM,IAAI,CAACC,MAAL,KAAgBX,UAAU,CAACE,MAAjD;AACAnC,QAAAA,QAAQ,CAAC0C,eAAT,CAAyBL,CAAzB,GAA6BrC,QAAQ,CAACwB,QAAT,CAAkBa,CAA/C;AACH;AACJ;;AACD,QAAIlB,SAAS,KAAK,QAAd,IAA0BsB,UAAU,CAACxB,GAAX,GAAiBgB,UAAU,CAACE,MAAX,GAAoBnC,QAAQ,CAACoC,MAAT,CAAgBC,CAAnF,EAAsF;AAClF,UAAI,CAACN,IAAL,EAAW;AACP/B,QAAAA,QAAQ,CAACwB,QAAT,CAAkBc,CAAlB,GAAsBK,IAAI,CAACC,MAAL,KAAgBX,UAAU,CAACM,KAAjD;AACAvC,QAAAA,QAAQ,CAAC0C,eAAT,CAAyBJ,CAAzB,GAA6BtC,QAAQ,CAACwB,QAAT,CAAkBc,CAA/C;AACH;;AACDtC,MAAAA,QAAQ,CAACwB,QAAT,CAAkBa,CAAlB,GAAsBH,MAAM,CAACjB,GAA7B;AACAjB,MAAAA,QAAQ,CAAC0C,eAAT,CAAyBL,CAAzB,GAA6BrC,QAAQ,CAACwB,QAAT,CAAkBa,CAA/C;AACH,KAPD,MAQK,IAAIlB,SAAS,KAAK,KAAd,IAAuBsB,UAAU,CAAC5B,MAAX,GAAoB,CAACb,QAAQ,CAACoC,MAAT,CAAgBC,CAAhE,EAAmE;AACpE,UAAI,CAACN,IAAL,EAAW;AACP/B,QAAAA,QAAQ,CAACwB,QAAT,CAAkBc,CAAlB,GAAsBK,IAAI,CAACC,MAAL,KAAgBX,UAAU,CAACM,KAAjD;AACAvC,QAAAA,QAAQ,CAAC0C,eAAT,CAAyBJ,CAAzB,GAA6BtC,QAAQ,CAACwB,QAAT,CAAkBc,CAA/C;AACH;;AACDtC,MAAAA,QAAQ,CAACwB,QAAT,CAAkBa,CAAlB,GAAsBH,MAAM,CAACrB,MAA7B;AACAb,MAAAA,QAAQ,CAAC0C,eAAT,CAAyBL,CAAzB,GAA6BrC,QAAQ,CAACwB,QAAT,CAAkBa,CAA/C;AACH;AACJ;;AACDjB,EAAAA,MAAM,CAACpB,QAAD,EAAWI,KAAX,EAAkBe,SAAlB,EAA6BD,OAA7B,EAAsC;AACxC,UAAMrB,SAAS,GAAG,KAAKA,SAAvB;AACA,QAAIgD,OAAO,GAAG,KAAd;;AACA,SAAK,MAAM,GAAGC,MAAH,CAAX,IAAyBjD,SAAS,CAACkD,OAAnC,EAA4C;AACxC,UAAID,MAAM,CAACE,cAAP,KAA0BlB,SAA9B,EAAyC;AACrCe,QAAAA,OAAO,GAAGC,MAAM,CAACE,cAAP,CAAsBhD,QAAtB,EAAgCI,KAAhC,EAAuCe,SAAvC,CAAV;AACH;;AACD,UAAI0B,OAAJ,EAAa;AACT;AACH;AACJ;;AACD,QAAIA,OAAJ,EAAa;AACT;AACH;;AACD,UAAMI,GAAG,GAAGjD,QAAQ,CAACkD,WAAT,EAAZ;AAAA,UAAoCd,MAAM,GAAGpC,QAAQ,CAACoC,MAAtD;AAAA,UAA8DV,IAAI,GAAG1B,QAAQ,CAAC2B,SAAT,EAArE;AAAA,UAA2FwB,MAAM,GAAG1D,eAAe,CAACwD,GAAD,EAAMvB,IAAN,CAAnH;AAAA,UAAgIO,UAAU,GAAGpC,SAAS,CAAC4B,MAAV,CAAiBC,IAA9J;AACAnC,IAAAA,gBAAgB,CAAC;AAAES,MAAAA,QAAF;AAAYkB,MAAAA,OAAZ;AAAqBC,MAAAA,SAArB;AAAgCgC,MAAAA,MAAhC;AAAwClB,MAAAA,UAAxC;AAAoDG,MAAAA,MAApD;AAA4DV,MAAAA;AAA5D,KAAD,CAAhB;AACAlC,IAAAA,cAAc,CAAC;AAAEQ,MAAAA,QAAF;AAAYkB,MAAAA,OAAZ;AAAqBC,MAAAA,SAArB;AAAgCgC,MAAAA,MAAhC;AAAwClB,MAAAA,UAAxC;AAAoDG,MAAAA,MAApD;AAA4DV,MAAAA;AAA5D,KAAD,CAAd;AACH;;AACDH,EAAAA,IAAI,CAACvB,QAAD,EAAWmB,SAAX,EAAsB;AACtB,QAAKnB,QAAQ,CAACU,OAAT,CAAiBC,IAAjB,CAAsByC,QAAtB,CAA+BC,UAA/B,KACAlC,SAAS,KAAK,MAAd,IAAwBA,SAAS,KAAK,OADtC,CAAD,IAECnB,QAAQ,CAACU,OAAT,CAAiBC,IAAjB,CAAsByC,QAAtB,CAA+BE,QAA/B,KACInC,SAAS,KAAK,KAAd,IAAuBA,SAAS,KAAK,QADzC,CAFL,EAG0D;AACtD;AACH;;AACD,UAAMoC,cAAc,GAAGvD,QAAQ,CAACU,OAAT,CAAiBC,IAAjB,CAAsB6C,OAA7C;AAAA,UAAsD3D,SAAS,GAAG,KAAKA,SAAvE;AACA,UAAMoC,UAAU,GAAGpC,SAAS,CAAC4B,MAAV,CAAiBC,IAApC;AACA,UAAM+B,OAAO,GAAGzD,QAAQ,CAAC2B,SAAT,EAAhB;;AACA,QAAI,CAAC4B,cAAc,CAACG,MAApB,EAA4B;AACxB,UAAK1D,QAAQ,CAAC2D,QAAT,CAAkBtB,CAAlB,GAAsB,CAAtB,IAA2BrC,QAAQ,CAACwB,QAAT,CAAkBa,CAAlB,IAAuBJ,UAAU,CAACE,MAAX,GAAoBsB,OAAvE,IACCzD,QAAQ,CAAC2D,QAAT,CAAkBtB,CAAlB,GAAsB,CAAtB,IAA2BrC,QAAQ,CAACwB,QAAT,CAAkBa,CAAlB,IAAuB,CAACoB,OADpD,IAECzD,QAAQ,CAAC2D,QAAT,CAAkBrB,CAAlB,GAAsB,CAAtB,IAA2BtC,QAAQ,CAACwB,QAAT,CAAkBc,CAAlB,IAAuBL,UAAU,CAACM,KAAX,GAAmBkB,OAFtE,IAGCzD,QAAQ,CAAC2D,QAAT,CAAkBrB,CAAlB,GAAsB,CAAtB,IAA2BtC,QAAQ,CAACwB,QAAT,CAAkBc,CAAlB,IAAuB,CAACmB,OAHxD,EAGkE;AAC9D;AACH;;AACD,UAAI,CAAC/D,aAAa,CAACM,QAAQ,CAACwB,QAAV,EAAoB3B,SAAS,CAAC4B,MAAV,CAAiBC,IAArC,EAA2C+B,OAA3C,EAAoDtC,SAApD,CAAlB,EAAkF;AAC9EtB,QAAAA,SAAS,CAAC+B,SAAV,CAAoBC,MAApB,CAA2B7B,QAA3B;AACH;AACJ,KAVD,MAWK;AACD,YAAMwB,QAAQ,GAAGxB,QAAQ,CAACwB,QAA1B;;AACA,UAAK,CAAC+B,cAAc,CAACK,OAAhB,IACDpC,QAAQ,CAACa,CAAT,GAAaJ,UAAU,CAACE,MAAX,GAAoBsB,OADhC,IAEDtC,SAAS,KAAK,QAFd,IAGCoC,cAAc,CAACK,OAAf,IAA0BpC,QAAQ,CAACa,CAAT,GAAa,CAACoB,OAAxC,IAAmDtC,SAAS,KAAK,KAHtE,EAG8E;AAC1EtB,QAAAA,SAAS,CAAC+B,SAAV,CAAoBC,MAApB,CAA2B7B,QAA3B;AACH;AACJ;AACJ;;AA1I2B", "sourcesContent": ["import { bounceHorizontal, bounceVertical } from \"./Utils\";\nimport { calculateBounds, isPointInside } from \"../../Utils\";\nexport class OutOfCanvasUpdater {\n    constructor(container) {\n        this.container = container;\n    }\n    init() {\n    }\n    isEnabled(particle) {\n        return !particle.destroyed && !particle.spawning;\n    }\n    update(particle, delta) {\n        var _a, _b, _c, _d;\n        const outModes = particle.options.move.outModes;\n        this.updateOutMode(particle, delta, (_a = outModes.bottom) !== null && _a !== void 0 ? _a : outModes.default, \"bottom\");\n        this.updateOutMode(particle, delta, (_b = outModes.left) !== null && _b !== void 0 ? _b : outModes.default, \"left\");\n        this.updateOutMode(particle, delta, (_c = outModes.right) !== null && _c !== void 0 ? _c : outModes.default, \"right\");\n        this.updateOutMode(particle, delta, (_d = outModes.top) !== null && _d !== void 0 ? _d : outModes.default, \"top\");\n    }\n    updateOutMode(particle, delta, outMode, direction) {\n        switch (outMode) {\n            case \"bounce\":\n            case \"bounce-vertical\":\n            case \"bounce-horizontal\":\n            case \"bounceVertical\":\n            case \"bounceHorizontal\":\n            case \"split\":\n                this.bounce(particle, delta, direction, outMode);\n                break;\n            case \"destroy\":\n                this.destroy(particle, direction);\n                break;\n            case \"out\":\n                this.out(particle, direction);\n                break;\n            case \"none\":\n            default:\n                this.none(particle, direction);\n                break;\n        }\n    }\n    destroy(particle, direction) {\n        const container = this.container;\n        if (isPointInside(particle.position, container.canvas.size, particle.getRadius(), direction)) {\n            return;\n        }\n        container.particles.remove(particle, undefined, true);\n    }\n    out(particle, direction) {\n        const container = this.container;\n        if (isPointInside(particle.position, container.canvas.size, particle.getRadius(), direction)) {\n            return;\n        }\n        const wrap = particle.options.move.warp, canvasSize = container.canvas.size, newPos = {\n            bottom: canvasSize.height + particle.getRadius() + particle.offset.y,\n            left: -particle.getRadius() - particle.offset.x,\n            right: canvasSize.width + particle.getRadius() + particle.offset.x,\n            top: -particle.getRadius() - particle.offset.y,\n        }, sizeValue = particle.getRadius(), nextBounds = calculateBounds(particle.position, sizeValue);\n        if (direction === \"right\" && nextBounds.left > canvasSize.width + particle.offset.x) {\n            particle.position.x = newPos.left;\n            particle.initialPosition.x = particle.position.x;\n            if (!wrap) {\n                particle.position.y = Math.random() * canvasSize.height;\n                particle.initialPosition.y = particle.position.y;\n            }\n        }\n        else if (direction === \"left\" && nextBounds.right < -particle.offset.x) {\n            particle.position.x = newPos.right;\n            particle.initialPosition.x = particle.position.x;\n            if (!wrap) {\n                particle.position.y = Math.random() * canvasSize.height;\n                particle.initialPosition.y = particle.position.y;\n            }\n        }\n        if (direction === \"bottom\" && nextBounds.top > canvasSize.height + particle.offset.y) {\n            if (!wrap) {\n                particle.position.x = Math.random() * canvasSize.width;\n                particle.initialPosition.x = particle.position.x;\n            }\n            particle.position.y = newPos.top;\n            particle.initialPosition.y = particle.position.y;\n        }\n        else if (direction === \"top\" && nextBounds.bottom < -particle.offset.y) {\n            if (!wrap) {\n                particle.position.x = Math.random() * canvasSize.width;\n                particle.initialPosition.x = particle.position.x;\n            }\n            particle.position.y = newPos.bottom;\n            particle.initialPosition.y = particle.position.y;\n        }\n    }\n    bounce(particle, delta, direction, outMode) {\n        const container = this.container;\n        let handled = false;\n        for (const [, plugin] of container.plugins) {\n            if (plugin.particleBounce !== undefined) {\n                handled = plugin.particleBounce(particle, delta, direction);\n            }\n            if (handled) {\n                break;\n            }\n        }\n        if (handled) {\n            return;\n        }\n        const pos = particle.getPosition(), offset = particle.offset, size = particle.getRadius(), bounds = calculateBounds(pos, size), canvasSize = container.canvas.size;\n        bounceHorizontal({ particle, outMode, direction, bounds, canvasSize, offset, size });\n        bounceVertical({ particle, outMode, direction, bounds, canvasSize, offset, size });\n    }\n    none(particle, direction) {\n        if ((particle.options.move.distance.horizontal &&\n            (direction === \"left\" || direction === \"right\")) ||\n            (particle.options.move.distance.vertical &&\n                (direction === \"top\" || direction === \"bottom\"))) {\n            return;\n        }\n        const gravityOptions = particle.options.move.gravity, container = this.container;\n        const canvasSize = container.canvas.size;\n        const pRadius = particle.getRadius();\n        if (!gravityOptions.enable) {\n            if ((particle.velocity.y > 0 && particle.position.y <= canvasSize.height + pRadius) ||\n                (particle.velocity.y < 0 && particle.position.y >= -pRadius) ||\n                (particle.velocity.x > 0 && particle.position.x <= canvasSize.width + pRadius) ||\n                (particle.velocity.x < 0 && particle.position.x >= -pRadius)) {\n                return;\n            }\n            if (!isPointInside(particle.position, container.canvas.size, pRadius, direction)) {\n                container.particles.remove(particle);\n            }\n        }\n        else {\n            const position = particle.position;\n            if ((!gravityOptions.inverse &&\n                position.y > canvasSize.height + pRadius &&\n                direction === \"bottom\") ||\n                (gravityOptions.inverse && position.y < -pRadius && direction === \"top\")) {\n                container.particles.remove(particle);\n            }\n        }\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
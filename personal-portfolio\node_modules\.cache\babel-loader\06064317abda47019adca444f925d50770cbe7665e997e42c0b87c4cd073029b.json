{"ast": null, "code": "export var DisposalMethod;\n(function (DisposalMethod) {\n  DisposalMethod[DisposalMethod[\"Replace\"] = 0] = \"Replace\";\n  DisposalMethod[DisposalMethod[\"Combine\"] = 1] = \"Combine\";\n  DisposalMethod[DisposalMethod[\"RestoreBackground\"] = 2] = \"RestoreBackground\";\n  DisposalMethod[DisposalMethod[\"RestorePrevious\"] = 3] = \"RestorePrevious\";\n  DisposalMethod[DisposalMethod[\"UndefinedA\"] = 4] = \"UndefinedA\";\n  DisposalMethod[DisposalMethod[\"UndefinedB\"] = 5] = \"UndefinedB\";\n  DisposalMethod[DisposalMethod[\"UndefinedC\"] = 6] = \"UndefinedC\";\n  DisposalMethod[DisposalMethod[\"UndefinedD\"] = 7] = \"UndefinedD\";\n})(DisposalMethod || (DisposalMethod = {}));", "map": {"version": 3, "names": ["DisposalMethod"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/shape-image/browser/GifUtils/Enums/DisposalMethod.js"], "sourcesContent": ["export var DisposalMethod;\n(function (DisposalMethod) {\n    DisposalMethod[DisposalMethod[\"Replace\"] = 0] = \"Replace\";\n    DisposalMethod[DisposalMethod[\"Combine\"] = 1] = \"Combine\";\n    DisposalMethod[DisposalMethod[\"RestoreBackground\"] = 2] = \"RestoreBackground\";\n    DisposalMethod[DisposalMethod[\"RestorePrevious\"] = 3] = \"RestorePrevious\";\n    DisposalMethod[DisposalMethod[\"UndefinedA\"] = 4] = \"UndefinedA\";\n    DisposalMethod[DisposalMethod[\"UndefinedB\"] = 5] = \"UndefinedB\";\n    DisposalMethod[DisposalMethod[\"UndefinedC\"] = 6] = \"UndefinedC\";\n    DisposalMethod[DisposalMethod[\"UndefinedD\"] = 7] = \"UndefinedD\";\n})(DisposalMethod || (DisposalMethod = {}));\n"], "mappings": "AAAA,OAAO,IAAIA,cAAc;AACzB,CAAC,UAAUA,cAAc,EAAE;EACvBA,cAAc,CAACA,cAAc,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS;EACzDA,cAAc,CAACA,cAAc,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS;EACzDA,cAAc,CAACA,cAAc,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC,GAAG,mBAAmB;EAC7EA,cAAc,CAACA,cAAc,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,GAAG,iBAAiB;EACzEA,cAAc,CAACA,cAAc,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,GAAG,YAAY;EAC/DA,cAAc,CAACA,cAAc,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,GAAG,YAAY;EAC/DA,cAAc,CAACA,cAAc,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,GAAG,YAAY;EAC/DA,cAAc,CAACA,cAAc,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,GAAG,YAAY;AACnE,CAAC,EAAEA,cAAc,KAAKA,cAAc,GAAG,CAAC,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { executeOnSingleOr<PERSON>ultiple, isArray, isInArray } from \"@tsparticles/engine\";\nimport { Emitter } from \"./Options/Classes/Emitter.js\";\nimport { EmitterClickMode } from \"./Enums/EmitterClickMode.js\";\nimport { Emitters } from \"./Emitters.js\";\nexport class EmittersPlugin {\n  constructor(engine) {\n    this._engine = engine;\n    this.id = \"emitters\";\n  }\n  getPlugin(container) {\n    return Promise.resolve(new Emitters(this._engine, container));\n  }\n  loadOptions(options, source) {\n    if (!this.needsPlugin(options) && !this.needsPlugin(source)) {\n      return;\n    }\n    if (source?.emitters) {\n      options.emitters = executeOnSingleOrMultiple(source.emitters, emitter => {\n        const tmp = new Emitter();\n        tmp.load(emitter);\n        return tmp;\n      });\n    }\n    const interactivityEmitters = source?.interactivity?.modes?.emitters;\n    if (interactivityEmitters) {\n      if (isArray(interactivityEmitters)) {\n        options.interactivity.modes.emitters = {\n          random: {\n            count: 1,\n            enable: true\n          },\n          value: interactivityEmitters.map(s => {\n            const tmp = new Emitter();\n            tmp.load(s);\n            return tmp;\n          })\n        };\n      } else {\n        const emitterMode = interactivityEmitters;\n        if (emitterMode.value !== undefined) {\n          const defaultCount = 1;\n          if (isArray(emitterMode.value)) {\n            options.interactivity.modes.emitters = {\n              random: {\n                count: emitterMode.random.count ?? defaultCount,\n                enable: emitterMode.random.enable ?? false\n              },\n              value: emitterMode.value.map(s => {\n                const tmp = new Emitter();\n                tmp.load(s);\n                return tmp;\n              })\n            };\n          } else {\n            const tmp = new Emitter();\n            tmp.load(emitterMode.value);\n            options.interactivity.modes.emitters = {\n              random: {\n                count: emitterMode.random.count ?? defaultCount,\n                enable: emitterMode.random.enable ?? false\n              },\n              value: tmp\n            };\n          }\n        } else {\n          const emitterOptions = options.interactivity.modes.emitters = {\n            random: {\n              count: 1,\n              enable: false\n            },\n            value: new Emitter()\n          };\n          emitterOptions.value.load(interactivityEmitters);\n        }\n      }\n    }\n  }\n  needsPlugin(options) {\n    if (!options) {\n      return false;\n    }\n    const emitters = options.emitters;\n    return isArray(emitters) && !!emitters.length || emitters !== undefined || !!options.interactivity?.events?.onClick?.mode && isInArray(EmitterClickMode.emitter, options.interactivity.events.onClick.mode);\n  }\n}", "map": {"version": 3, "names": ["executeOnSingleOrMultiple", "isArray", "isInArray", "Emitter", "EmitterClickMode", "Emitters", "EmittersPlugin", "constructor", "engine", "_engine", "id", "getPlugin", "container", "Promise", "resolve", "loadOptions", "options", "source", "needsPlugin", "emitters", "emitter", "tmp", "load", "interactivityEmitters", "interactivity", "modes", "random", "count", "enable", "value", "map", "s", "emitterMode", "undefined", "defaultCount", "emitterOptions", "length", "events", "onClick", "mode"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/plugin-emitters/browser/EmittersPlugin.js"], "sourcesContent": ["import { executeOnSingleOr<PERSON>ultiple, isArray, isInArray, } from \"@tsparticles/engine\";\nimport { Emitter } from \"./Options/Classes/Emitter.js\";\nimport { EmitterClickMode } from \"./Enums/EmitterClickMode.js\";\nimport { Emitters } from \"./Emitters.js\";\nexport class EmittersPlugin {\n    constructor(engine) {\n        this._engine = engine;\n        this.id = \"emitters\";\n    }\n    getPlugin(container) {\n        return Promise.resolve(new Emitters(this._engine, container));\n    }\n    loadOptions(options, source) {\n        if (!this.needsPlugin(options) && !this.needsPlugin(source)) {\n            return;\n        }\n        if (source?.emitters) {\n            options.emitters = executeOnSingleOrMultiple(source.emitters, emitter => {\n                const tmp = new Emitter();\n                tmp.load(emitter);\n                return tmp;\n            });\n        }\n        const interactivityEmitters = source?.interactivity?.modes?.emitters;\n        if (interactivityEmitters) {\n            if (isArray(interactivityEmitters)) {\n                options.interactivity.modes.emitters = {\n                    random: {\n                        count: 1,\n                        enable: true,\n                    },\n                    value: interactivityEmitters.map(s => {\n                        const tmp = new Emitter();\n                        tmp.load(s);\n                        return tmp;\n                    }),\n                };\n            }\n            else {\n                const emitterMode = interactivityEmitters;\n                if (emitterMode.value !== undefined) {\n                    const defaultCount = 1;\n                    if (isArray(emitterMode.value)) {\n                        options.interactivity.modes.emitters = {\n                            random: {\n                                count: emitterMode.random.count ?? defaultCount,\n                                enable: emitterMode.random.enable ?? false,\n                            },\n                            value: emitterMode.value.map(s => {\n                                const tmp = new Emitter();\n                                tmp.load(s);\n                                return tmp;\n                            }),\n                        };\n                    }\n                    else {\n                        const tmp = new Emitter();\n                        tmp.load(emitterMode.value);\n                        options.interactivity.modes.emitters = {\n                            random: {\n                                count: emitterMode.random.count ?? defaultCount,\n                                enable: emitterMode.random.enable ?? false,\n                            },\n                            value: tmp,\n                        };\n                    }\n                }\n                else {\n                    const emitterOptions = (options.interactivity.modes.emitters = {\n                        random: {\n                            count: 1,\n                            enable: false,\n                        },\n                        value: new Emitter(),\n                    });\n                    emitterOptions.value.load(interactivityEmitters);\n                }\n            }\n        }\n    }\n    needsPlugin(options) {\n        if (!options) {\n            return false;\n        }\n        const emitters = options.emitters;\n        return ((isArray(emitters) && !!emitters.length) ||\n            emitters !== undefined ||\n            (!!options.interactivity?.events?.onClick?.mode &&\n                isInArray(EmitterClickMode.emitter, options.interactivity.events.onClick.mode)));\n    }\n}\n"], "mappings": "AAAA,SAASA,yBAAyB,EAAEC,OAAO,EAAEC,SAAS,QAAS,qBAAqB;AACpF,SAASC,OAAO,QAAQ,8BAA8B;AACtD,SAASC,gBAAgB,QAAQ,6BAA6B;AAC9D,SAASC,QAAQ,QAAQ,eAAe;AACxC,OAAO,MAAMC,cAAc,CAAC;EACxBC,WAAWA,CAACC,MAAM,EAAE;IAChB,IAAI,CAACC,OAAO,GAAGD,MAAM;IACrB,IAAI,CAACE,EAAE,GAAG,UAAU;EACxB;EACAC,SAASA,CAACC,SAAS,EAAE;IACjB,OAAOC,OAAO,CAACC,OAAO,CAAC,IAAIT,QAAQ,CAAC,IAAI,CAACI,OAAO,EAAEG,SAAS,CAAC,CAAC;EACjE;EACAG,WAAWA,CAACC,OAAO,EAAEC,MAAM,EAAE;IACzB,IAAI,CAAC,IAAI,CAACC,WAAW,CAACF,OAAO,CAAC,IAAI,CAAC,IAAI,CAACE,WAAW,CAACD,MAAM,CAAC,EAAE;MACzD;IACJ;IACA,IAAIA,MAAM,EAAEE,QAAQ,EAAE;MAClBH,OAAO,CAACG,QAAQ,GAAGnB,yBAAyB,CAACiB,MAAM,CAACE,QAAQ,EAAEC,OAAO,IAAI;QACrE,MAAMC,GAAG,GAAG,IAAIlB,OAAO,CAAC,CAAC;QACzBkB,GAAG,CAACC,IAAI,CAACF,OAAO,CAAC;QACjB,OAAOC,GAAG;MACd,CAAC,CAAC;IACN;IACA,MAAME,qBAAqB,GAAGN,MAAM,EAAEO,aAAa,EAAEC,KAAK,EAAEN,QAAQ;IACpE,IAAII,qBAAqB,EAAE;MACvB,IAAItB,OAAO,CAACsB,qBAAqB,CAAC,EAAE;QAChCP,OAAO,CAACQ,aAAa,CAACC,KAAK,CAACN,QAAQ,GAAG;UACnCO,MAAM,EAAE;YACJC,KAAK,EAAE,CAAC;YACRC,MAAM,EAAE;UACZ,CAAC;UACDC,KAAK,EAAEN,qBAAqB,CAACO,GAAG,CAACC,CAAC,IAAI;YAClC,MAAMV,GAAG,GAAG,IAAIlB,OAAO,CAAC,CAAC;YACzBkB,GAAG,CAACC,IAAI,CAACS,CAAC,CAAC;YACX,OAAOV,GAAG;UACd,CAAC;QACL,CAAC;MACL,CAAC,MACI;QACD,MAAMW,WAAW,GAAGT,qBAAqB;QACzC,IAAIS,WAAW,CAACH,KAAK,KAAKI,SAAS,EAAE;UACjC,MAAMC,YAAY,GAAG,CAAC;UACtB,IAAIjC,OAAO,CAAC+B,WAAW,CAACH,KAAK,CAAC,EAAE;YAC5Bb,OAAO,CAACQ,aAAa,CAACC,KAAK,CAACN,QAAQ,GAAG;cACnCO,MAAM,EAAE;gBACJC,KAAK,EAAEK,WAAW,CAACN,MAAM,CAACC,KAAK,IAAIO,YAAY;gBAC/CN,MAAM,EAAEI,WAAW,CAACN,MAAM,CAACE,MAAM,IAAI;cACzC,CAAC;cACDC,KAAK,EAAEG,WAAW,CAACH,KAAK,CAACC,GAAG,CAACC,CAAC,IAAI;gBAC9B,MAAMV,GAAG,GAAG,IAAIlB,OAAO,CAAC,CAAC;gBACzBkB,GAAG,CAACC,IAAI,CAACS,CAAC,CAAC;gBACX,OAAOV,GAAG;cACd,CAAC;YACL,CAAC;UACL,CAAC,MACI;YACD,MAAMA,GAAG,GAAG,IAAIlB,OAAO,CAAC,CAAC;YACzBkB,GAAG,CAACC,IAAI,CAACU,WAAW,CAACH,KAAK,CAAC;YAC3Bb,OAAO,CAACQ,aAAa,CAACC,KAAK,CAACN,QAAQ,GAAG;cACnCO,MAAM,EAAE;gBACJC,KAAK,EAAEK,WAAW,CAACN,MAAM,CAACC,KAAK,IAAIO,YAAY;gBAC/CN,MAAM,EAAEI,WAAW,CAACN,MAAM,CAACE,MAAM,IAAI;cACzC,CAAC;cACDC,KAAK,EAAER;YACX,CAAC;UACL;QACJ,CAAC,MACI;UACD,MAAMc,cAAc,GAAInB,OAAO,CAACQ,aAAa,CAACC,KAAK,CAACN,QAAQ,GAAG;YAC3DO,MAAM,EAAE;cACJC,KAAK,EAAE,CAAC;cACRC,MAAM,EAAE;YACZ,CAAC;YACDC,KAAK,EAAE,IAAI1B,OAAO,CAAC;UACvB,CAAE;UACFgC,cAAc,CAACN,KAAK,CAACP,IAAI,CAACC,qBAAqB,CAAC;QACpD;MACJ;IACJ;EACJ;EACAL,WAAWA,CAACF,OAAO,EAAE;IACjB,IAAI,CAACA,OAAO,EAAE;MACV,OAAO,KAAK;IAChB;IACA,MAAMG,QAAQ,GAAGH,OAAO,CAACG,QAAQ;IACjC,OAASlB,OAAO,CAACkB,QAAQ,CAAC,IAAI,CAAC,CAACA,QAAQ,CAACiB,MAAM,IAC3CjB,QAAQ,KAAKc,SAAS,IACrB,CAAC,CAACjB,OAAO,CAACQ,aAAa,EAAEa,MAAM,EAAEC,OAAO,EAAEC,IAAI,IAC3CrC,SAAS,CAACE,gBAAgB,CAACgB,OAAO,EAAEJ,OAAO,CAACQ,aAAa,CAACa,MAAM,CAACC,OAAO,CAACC,IAAI,CAAE;EAC3F;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
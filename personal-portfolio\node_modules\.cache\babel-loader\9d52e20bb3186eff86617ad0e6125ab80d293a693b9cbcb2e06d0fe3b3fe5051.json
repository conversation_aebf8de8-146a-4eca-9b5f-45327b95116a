{"ast": null, "code": "export class Point {\n  constructor(position, particle) {\n    this.position = position;\n    this.particle = particle;\n  }\n}", "map": {"version": 3, "names": ["Point", "constructor", "position", "particle"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/engine/browser/Core/Utils/Point.js"], "sourcesContent": ["export class Point {\n    constructor(position, particle) {\n        this.position = position;\n        this.particle = particle;\n    }\n}\n"], "mappings": "AAAA,OAAO,MAAMA,KAAK,CAAC;EACfC,WAAWA,CAACC,QAAQ,EAAEC,QAAQ,EAAE;IAC5B,IAAI,CAACD,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;EAC5B;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
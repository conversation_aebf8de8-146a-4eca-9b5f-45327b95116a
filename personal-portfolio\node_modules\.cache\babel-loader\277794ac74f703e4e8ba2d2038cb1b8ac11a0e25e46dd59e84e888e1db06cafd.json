{"ast": null, "code": "export var InteractorType;\n(function (InteractorType) {\n  InteractorType[\"external\"] = \"external\";\n  InteractorType[\"particles\"] = \"particles\";\n})(InteractorType || (InteractorType = {}));", "map": {"version": 3, "names": ["InteractorType"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/engine/browser/Enums/Types/InteractorType.js"], "sourcesContent": ["export var InteractorType;\n(function (InteractorType) {\n    InteractorType[\"external\"] = \"external\";\n    InteractorType[\"particles\"] = \"particles\";\n})(InteractorType || (InteractorType = {}));\n"], "mappings": "AAAA,OAAO,IAAIA,cAAc;AACzB,CAAC,UAAUA,cAAc,EAAE;EACvBA,cAAc,CAAC,UAAU,CAAC,GAAG,UAAU;EACvCA,cAAc,CAAC,WAAW,CAAC,GAAG,WAAW;AAC7C,CAAC,EAAEA,cAAc,KAAKA,cAAc,GAAG,CAAC,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
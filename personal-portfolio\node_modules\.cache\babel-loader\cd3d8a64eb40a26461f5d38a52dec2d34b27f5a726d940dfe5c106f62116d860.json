{"ast": null, "code": "import { Attractor } from \"./Attractor.js\";\nexport async function loadExternalAttractInteraction(engine, refresh = true) {\n  await engine.addInteractor(\"externalAttract\", container => {\n    return Promise.resolve(new Attractor(engine, container));\n  }, refresh);\n}\nexport * from \"./Options/Classes/Attract.js\";\nexport * from \"./Options/Interfaces/IAttract.js\";", "map": {"version": 3, "names": ["Attractor", "loadExternalAttractInteraction", "engine", "refresh", "addInteractor", "container", "Promise", "resolve"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/interaction-external-attract/browser/index.js"], "sourcesContent": ["import { Attractor } from \"./Attractor.js\";\nexport async function loadExternalAttractInteraction(engine, refresh = true) {\n    await engine.addInteractor(\"externalAttract\", container => {\n        return Promise.resolve(new Attractor(engine, container));\n    }, refresh);\n}\nexport * from \"./Options/Classes/Attract.js\";\nexport * from \"./Options/Interfaces/IAttract.js\";\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,gBAAgB;AAC1C,OAAO,eAAeC,8BAA8BA,CAACC,MAAM,EAAEC,OAAO,GAAG,IAAI,EAAE;EACzE,MAAMD,MAAM,CAACE,aAAa,CAAC,iBAAiB,EAAEC,SAAS,IAAI;IACvD,OAAOC,OAAO,CAACC,OAAO,CAAC,IAAIP,SAAS,CAACE,MAAM,EAAEG,SAAS,CAAC,CAAC;EAC5D,CAAC,EAAEF,OAAO,CAAC;AACf;AACA,cAAc,8BAA8B;AAC5C,cAAc,kCAAkC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
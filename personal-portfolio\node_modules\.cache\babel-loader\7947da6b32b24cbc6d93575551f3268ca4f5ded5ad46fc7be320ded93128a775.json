{"ast": null, "code": "import { DestroyType } from \"../../../../Enums/Types/DestroyType.js\";\nimport { RangedAnimationOptions } from \"../../AnimationOptions.js\";\nexport class SizeAnimation extends RangedAnimationOptions {\n  constructor() {\n    super();\n    this.destroy = DestroyType.none;\n    this.speed = 5;\n  }\n  load(data) {\n    super.load(data);\n    if (!data) {\n      return;\n    }\n    if (data.destroy !== undefined) {\n      this.destroy = data.destroy;\n    }\n  }\n}", "map": {"version": 3, "names": ["DestroyType", "RangedAnimationOptions", "SizeAnimation", "constructor", "destroy", "none", "speed", "load", "data", "undefined"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/engine/browser/Options/Classes/Particles/Size/SizeAnimation.js"], "sourcesContent": ["import { DestroyType } from \"../../../../Enums/Types/DestroyType.js\";\nimport { RangedAnimationOptions } from \"../../AnimationOptions.js\";\nexport class SizeAnimation extends RangedAnimationOptions {\n    constructor() {\n        super();\n        this.destroy = DestroyType.none;\n        this.speed = 5;\n    }\n    load(data) {\n        super.load(data);\n        if (!data) {\n            return;\n        }\n        if (data.destroy !== undefined) {\n            this.destroy = data.destroy;\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,wCAAwC;AACpE,SAASC,sBAAsB,QAAQ,2BAA2B;AAClE,OAAO,MAAMC,aAAa,SAASD,sBAAsB,CAAC;EACtDE,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,OAAO,GAAGJ,WAAW,CAACK,IAAI;IAC/B,IAAI,CAACC,KAAK,GAAG,CAAC;EAClB;EACAC,IAAIA,CAACC,IAAI,EAAE;IACP,KAAK,CAACD,IAAI,CAACC,IAAI,CAAC;IAChB,IAAI,CAACA,IAAI,EAAE;MACP;IACJ;IACA,IAAIA,IAAI,CAACJ,OAAO,KAAKK,SAAS,EAAE;MAC5B,IAAI,CAACL,OAAO,GAAGI,IAAI,CAACJ,OAAO;IAC/B;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "export class FullScreen {\n  constructor() {\n    this.enable = true;\n    this.zIndex = 0;\n  }\n\n  load(data) {\n    if (!data) {\n      return;\n    }\n\n    if (data.enable !== undefined) {\n      this.enable = data.enable;\n    }\n\n    if (data.zIndex !== undefined) {\n      this.zIndex = data.zIndex;\n    }\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Options/Classes/FullScreen/FullScreen.js"], "names": ["FullScreen", "constructor", "enable", "zIndex", "load", "data", "undefined"], "mappings": "AAAA,OAAO,MAAMA,UAAN,CAAiB;AACpBC,EAAAA,WAAW,GAAG;AACV,SAAKC,MAAL,GAAc,IAAd;AACA,SAAKC,MAAL,GAAc,CAAd;AACH;;AACDC,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,QAAI,CAACA,IAAL,EAAW;AACP;AACH;;AACD,QAAIA,IAAI,CAACH,MAAL,KAAgBI,SAApB,EAA+B;AAC3B,WAAKJ,MAAL,GAAcG,IAAI,CAACH,MAAnB;AACH;;AACD,QAAIG,IAAI,CAACF,MAAL,KAAgBG,SAApB,EAA+B;AAC3B,WAAKH,MAAL,GAAcE,IAAI,CAACF,MAAnB;AACH;AACJ;;AAfmB", "sourcesContent": ["export class FullScreen {\n    constructor() {\n        this.enable = true;\n        this.zIndex = 0;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.enable !== undefined) {\n            this.enable = data.enable;\n        }\n        if (data.zIndex !== undefined) {\n            this.zIndex = data.zIndex;\n        }\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
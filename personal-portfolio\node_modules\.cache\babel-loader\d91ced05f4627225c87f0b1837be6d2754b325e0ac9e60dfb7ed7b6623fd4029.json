{"ast": null, "code": "import { PixelMode } from \"../../../../Enums/Modes/PixelMode.js\";\nexport class MoveCenter {\n  constructor() {\n    this.x = 50;\n    this.y = 50;\n    this.mode = PixelMode.percent;\n    this.radius = 0;\n  }\n  load(data) {\n    if (!data) {\n      return;\n    }\n    if (data.x !== undefined) {\n      this.x = data.x;\n    }\n    if (data.y !== undefined) {\n      this.y = data.y;\n    }\n    if (data.mode !== undefined) {\n      this.mode = data.mode;\n    }\n    if (data.radius !== undefined) {\n      this.radius = data.radius;\n    }\n  }\n}", "map": {"version": 3, "names": ["PixelMode", "MoveCenter", "constructor", "x", "y", "mode", "percent", "radius", "load", "data", "undefined"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/engine/browser/Options/Classes/Particles/Move/MoveCenter.js"], "sourcesContent": ["import { PixelMode } from \"../../../../Enums/Modes/PixelMode.js\";\nexport class MoveCenter {\n    constructor() {\n        this.x = 50;\n        this.y = 50;\n        this.mode = PixelMode.percent;\n        this.radius = 0;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.x !== undefined) {\n            this.x = data.x;\n        }\n        if (data.y !== undefined) {\n            this.y = data.y;\n        }\n        if (data.mode !== undefined) {\n            this.mode = data.mode;\n        }\n        if (data.radius !== undefined) {\n            this.radius = data.radius;\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,sCAAsC;AAChE,OAAO,MAAMC,UAAU,CAAC;EACpBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,CAAC,GAAG,EAAE;IACX,IAAI,CAACC,CAAC,GAAG,EAAE;IACX,IAAI,CAACC,IAAI,GAAGL,SAAS,CAACM,OAAO;IAC7B,IAAI,CAACC,MAAM,GAAG,CAAC;EACnB;EACAC,IAAIA,CAACC,IAAI,EAAE;IACP,IAAI,CAACA,IAAI,EAAE;MACP;IACJ;IACA,IAAIA,IAAI,CAACN,CAAC,KAAKO,SAAS,EAAE;MACtB,IAAI,CAACP,CAAC,GAAGM,IAAI,CAACN,CAAC;IACnB;IACA,IAAIM,IAAI,CAACL,CAAC,KAAKM,SAAS,EAAE;MACtB,IAAI,CAACN,CAAC,GAAGK,IAAI,CAACL,CAAC;IACnB;IACA,IAAIK,IAAI,CAACJ,IAAI,KAAKK,SAAS,EAAE;MACzB,IAAI,CAACL,IAAI,GAAGI,IAAI,CAACJ,IAAI;IACzB;IACA,IAAII,IAAI,CAACF,MAAM,KAAKG,SAAS,EAAE;MAC3B,IAAI,CAACH,MAAM,GAAGE,IAAI,CAACF,MAAM;IAC7B;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
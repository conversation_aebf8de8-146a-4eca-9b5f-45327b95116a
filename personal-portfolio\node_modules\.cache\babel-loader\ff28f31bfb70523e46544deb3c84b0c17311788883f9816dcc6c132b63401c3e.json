{"ast": null, "code": "import { OutMode, OutModeDirection, ParticleOutType, Vector, calculateBounds, getDistances, getRandom, isPointInside, randomInRange } from \"@tsparticles/engine\";\nconst minVelocity = 0,\n  minDistance = 0;\nexport class OutOutMode {\n  constructor(container) {\n    this.container = container;\n    this.modes = [OutMode.out];\n  }\n  update(particle, direction, delta, outMode) {\n    if (!this.modes.includes(outMode)) {\n      return;\n    }\n    const container = this.container;\n    switch (particle.outType) {\n      case ParticleOutType.inside:\n        {\n          const {\n            x: vx,\n            y: vy\n          } = particle.velocity;\n          const circVec = Vector.origin;\n          circVec.length = particle.moveCenter.radius;\n          circVec.angle = particle.velocity.angle + Math.PI;\n          circVec.addTo(Vector.create(particle.moveCenter));\n          const {\n            dx,\n            dy\n          } = getDistances(particle.position, circVec);\n          if (vx <= minVelocity && dx >= minDistance || vy <= minVelocity && dy >= minDistance || vx >= minVelocity && dx <= minDistance || vy >= minVelocity && dy <= minDistance) {\n            return;\n          }\n          particle.position.x = Math.floor(randomInRange({\n            min: 0,\n            max: container.canvas.size.width\n          }));\n          particle.position.y = Math.floor(randomInRange({\n            min: 0,\n            max: container.canvas.size.height\n          }));\n          const {\n            dx: newDx,\n            dy: newDy\n          } = getDistances(particle.position, particle.moveCenter);\n          particle.direction = Math.atan2(-newDy, -newDx);\n          particle.velocity.angle = particle.direction;\n          break;\n        }\n      default:\n        {\n          if (isPointInside(particle.position, container.canvas.size, Vector.origin, particle.getRadius(), direction)) {\n            return;\n          }\n          switch (particle.outType) {\n            case ParticleOutType.outside:\n              {\n                particle.position.x = Math.floor(randomInRange({\n                  min: -particle.moveCenter.radius,\n                  max: particle.moveCenter.radius\n                })) + particle.moveCenter.x;\n                particle.position.y = Math.floor(randomInRange({\n                  min: -particle.moveCenter.radius,\n                  max: particle.moveCenter.radius\n                })) + particle.moveCenter.y;\n                const {\n                  dx,\n                  dy\n                } = getDistances(particle.position, particle.moveCenter);\n                if (particle.moveCenter.radius) {\n                  particle.direction = Math.atan2(dy, dx);\n                  particle.velocity.angle = particle.direction;\n                }\n                break;\n              }\n            case ParticleOutType.normal:\n              {\n                const warp = particle.options.move.warp,\n                  canvasSize = container.canvas.size,\n                  newPos = {\n                    bottom: canvasSize.height + particle.getRadius() + particle.offset.y,\n                    left: -particle.getRadius() - particle.offset.x,\n                    right: canvasSize.width + particle.getRadius() + particle.offset.x,\n                    top: -particle.getRadius() - particle.offset.y\n                  },\n                  sizeValue = particle.getRadius(),\n                  nextBounds = calculateBounds(particle.position, sizeValue);\n                if (direction === OutModeDirection.right && nextBounds.left > canvasSize.width + particle.offset.x) {\n                  particle.position.x = newPos.left;\n                  particle.initialPosition.x = particle.position.x;\n                  if (!warp) {\n                    particle.position.y = getRandom() * canvasSize.height;\n                    particle.initialPosition.y = particle.position.y;\n                  }\n                } else if (direction === OutModeDirection.left && nextBounds.right < -particle.offset.x) {\n                  particle.position.x = newPos.right;\n                  particle.initialPosition.x = particle.position.x;\n                  if (!warp) {\n                    particle.position.y = getRandom() * canvasSize.height;\n                    particle.initialPosition.y = particle.position.y;\n                  }\n                }\n                if (direction === OutModeDirection.bottom && nextBounds.top > canvasSize.height + particle.offset.y) {\n                  if (!warp) {\n                    particle.position.x = getRandom() * canvasSize.width;\n                    particle.initialPosition.x = particle.position.x;\n                  }\n                  particle.position.y = newPos.top;\n                  particle.initialPosition.y = particle.position.y;\n                } else if (direction === OutModeDirection.top && nextBounds.bottom < -particle.offset.y) {\n                  if (!warp) {\n                    particle.position.x = getRandom() * canvasSize.width;\n                    particle.initialPosition.x = particle.position.x;\n                  }\n                  particle.position.y = newPos.bottom;\n                  particle.initialPosition.y = particle.position.y;\n                }\n                break;\n              }\n          }\n          break;\n        }\n    }\n  }\n}", "map": {"version": 3, "names": ["OutMode", "OutModeDirection", "ParticleOutType", "Vector", "calculateBounds", "getDistances", "getRandom", "isPointInside", "randomInRange", "minVelocity", "minDistance", "OutOutMode", "constructor", "container", "modes", "out", "update", "particle", "direction", "delta", "outMode", "includes", "outType", "inside", "x", "vx", "y", "vy", "velocity", "circVec", "origin", "length", "moveCenter", "radius", "angle", "Math", "PI", "addTo", "create", "dx", "dy", "position", "floor", "min", "max", "canvas", "size", "width", "height", "newDx", "newDy", "atan2", "getRadius", "outside", "normal", "warp", "options", "move", "canvasSize", "newPos", "bottom", "offset", "left", "right", "top", "sizeValue", "nextBounds", "initialPosition"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/updater-out-modes/browser/OutOutMode.js"], "sourcesContent": ["import { OutMode, OutModeDirection, ParticleOutType, Vector, calculateBounds, getDistances, getRandom, isPointInside, randomInRange, } from \"@tsparticles/engine\";\nconst minVelocity = 0, minDistance = 0;\nexport class OutOutMode {\n    constructor(container) {\n        this.container = container;\n        this.modes = [OutMode.out];\n    }\n    update(particle, direction, delta, outMode) {\n        if (!this.modes.includes(outMode)) {\n            return;\n        }\n        const container = this.container;\n        switch (particle.outType) {\n            case ParticleOutType.inside: {\n                const { x: vx, y: vy } = particle.velocity;\n                const circVec = Vector.origin;\n                circVec.length = particle.moveCenter.radius;\n                circVec.angle = particle.velocity.angle + Math.PI;\n                circVec.addTo(Vector.create(particle.moveCenter));\n                const { dx, dy } = getDistances(particle.position, circVec);\n                if ((vx <= minVelocity && dx >= minDistance) ||\n                    (vy <= minVelocity && dy >= minDistance) ||\n                    (vx >= minVelocity && dx <= minDistance) ||\n                    (vy >= minVelocity && dy <= minDistance)) {\n                    return;\n                }\n                particle.position.x = Math.floor(randomInRange({\n                    min: 0,\n                    max: container.canvas.size.width,\n                }));\n                particle.position.y = Math.floor(randomInRange({\n                    min: 0,\n                    max: container.canvas.size.height,\n                }));\n                const { dx: newDx, dy: newDy } = getDistances(particle.position, particle.moveCenter);\n                particle.direction = Math.atan2(-newDy, -newDx);\n                particle.velocity.angle = particle.direction;\n                break;\n            }\n            default: {\n                if (isPointInside(particle.position, container.canvas.size, Vector.origin, particle.getRadius(), direction)) {\n                    return;\n                }\n                switch (particle.outType) {\n                    case ParticleOutType.outside: {\n                        particle.position.x =\n                            Math.floor(randomInRange({\n                                min: -particle.moveCenter.radius,\n                                max: particle.moveCenter.radius,\n                            })) + particle.moveCenter.x;\n                        particle.position.y =\n                            Math.floor(randomInRange({\n                                min: -particle.moveCenter.radius,\n                                max: particle.moveCenter.radius,\n                            })) + particle.moveCenter.y;\n                        const { dx, dy } = getDistances(particle.position, particle.moveCenter);\n                        if (particle.moveCenter.radius) {\n                            particle.direction = Math.atan2(dy, dx);\n                            particle.velocity.angle = particle.direction;\n                        }\n                        break;\n                    }\n                    case ParticleOutType.normal: {\n                        const warp = particle.options.move.warp, canvasSize = container.canvas.size, newPos = {\n                            bottom: canvasSize.height + particle.getRadius() + particle.offset.y,\n                            left: -particle.getRadius() - particle.offset.x,\n                            right: canvasSize.width + particle.getRadius() + particle.offset.x,\n                            top: -particle.getRadius() - particle.offset.y,\n                        }, sizeValue = particle.getRadius(), nextBounds = calculateBounds(particle.position, sizeValue);\n                        if (direction === OutModeDirection.right &&\n                            nextBounds.left > canvasSize.width + particle.offset.x) {\n                            particle.position.x = newPos.left;\n                            particle.initialPosition.x = particle.position.x;\n                            if (!warp) {\n                                particle.position.y = getRandom() * canvasSize.height;\n                                particle.initialPosition.y = particle.position.y;\n                            }\n                        }\n                        else if (direction === OutModeDirection.left && nextBounds.right < -particle.offset.x) {\n                            particle.position.x = newPos.right;\n                            particle.initialPosition.x = particle.position.x;\n                            if (!warp) {\n                                particle.position.y = getRandom() * canvasSize.height;\n                                particle.initialPosition.y = particle.position.y;\n                            }\n                        }\n                        if (direction === OutModeDirection.bottom &&\n                            nextBounds.top > canvasSize.height + particle.offset.y) {\n                            if (!warp) {\n                                particle.position.x = getRandom() * canvasSize.width;\n                                particle.initialPosition.x = particle.position.x;\n                            }\n                            particle.position.y = newPos.top;\n                            particle.initialPosition.y = particle.position.y;\n                        }\n                        else if (direction === OutModeDirection.top && nextBounds.bottom < -particle.offset.y) {\n                            if (!warp) {\n                                particle.position.x = getRandom() * canvasSize.width;\n                                particle.initialPosition.x = particle.position.x;\n                            }\n                            particle.position.y = newPos.bottom;\n                            particle.initialPosition.y = particle.position.y;\n                        }\n                        break;\n                    }\n                }\n                break;\n            }\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,OAAO,EAAEC,gBAAgB,EAAEC,eAAe,EAAEC,MAAM,EAAEC,eAAe,EAAEC,YAAY,EAAEC,SAAS,EAAEC,aAAa,EAAEC,aAAa,QAAS,qBAAqB;AACjK,MAAMC,WAAW,GAAG,CAAC;EAAEC,WAAW,GAAG,CAAC;AACtC,OAAO,MAAMC,UAAU,CAAC;EACpBC,WAAWA,CAACC,SAAS,EAAE;IACnB,IAAI,CAACA,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,KAAK,GAAG,CAACd,OAAO,CAACe,GAAG,CAAC;EAC9B;EACAC,MAAMA,CAACC,QAAQ,EAAEC,SAAS,EAAEC,KAAK,EAAEC,OAAO,EAAE;IACxC,IAAI,CAAC,IAAI,CAACN,KAAK,CAACO,QAAQ,CAACD,OAAO,CAAC,EAAE;MAC/B;IACJ;IACA,MAAMP,SAAS,GAAG,IAAI,CAACA,SAAS;IAChC,QAAQI,QAAQ,CAACK,OAAO;MACpB,KAAKpB,eAAe,CAACqB,MAAM;QAAE;UACzB,MAAM;YAAEC,CAAC,EAAEC,EAAE;YAAEC,CAAC,EAAEC;UAAG,CAAC,GAAGV,QAAQ,CAACW,QAAQ;UAC1C,MAAMC,OAAO,GAAG1B,MAAM,CAAC2B,MAAM;UAC7BD,OAAO,CAACE,MAAM,GAAGd,QAAQ,CAACe,UAAU,CAACC,MAAM;UAC3CJ,OAAO,CAACK,KAAK,GAAGjB,QAAQ,CAACW,QAAQ,CAACM,KAAK,GAAGC,IAAI,CAACC,EAAE;UACjDP,OAAO,CAACQ,KAAK,CAAClC,MAAM,CAACmC,MAAM,CAACrB,QAAQ,CAACe,UAAU,CAAC,CAAC;UACjD,MAAM;YAAEO,EAAE;YAAEC;UAAG,CAAC,GAAGnC,YAAY,CAACY,QAAQ,CAACwB,QAAQ,EAAEZ,OAAO,CAAC;UAC3D,IAAKJ,EAAE,IAAIhB,WAAW,IAAI8B,EAAE,IAAI7B,WAAW,IACtCiB,EAAE,IAAIlB,WAAW,IAAI+B,EAAE,IAAI9B,WAAY,IACvCe,EAAE,IAAIhB,WAAW,IAAI8B,EAAE,IAAI7B,WAAY,IACvCiB,EAAE,IAAIlB,WAAW,IAAI+B,EAAE,IAAI9B,WAAY,EAAE;YAC1C;UACJ;UACAO,QAAQ,CAACwB,QAAQ,CAACjB,CAAC,GAAGW,IAAI,CAACO,KAAK,CAAClC,aAAa,CAAC;YAC3CmC,GAAG,EAAE,CAAC;YACNC,GAAG,EAAE/B,SAAS,CAACgC,MAAM,CAACC,IAAI,CAACC;UAC/B,CAAC,CAAC,CAAC;UACH9B,QAAQ,CAACwB,QAAQ,CAACf,CAAC,GAAGS,IAAI,CAACO,KAAK,CAAClC,aAAa,CAAC;YAC3CmC,GAAG,EAAE,CAAC;YACNC,GAAG,EAAE/B,SAAS,CAACgC,MAAM,CAACC,IAAI,CAACE;UAC/B,CAAC,CAAC,CAAC;UACH,MAAM;YAAET,EAAE,EAAEU,KAAK;YAAET,EAAE,EAAEU;UAAM,CAAC,GAAG7C,YAAY,CAACY,QAAQ,CAACwB,QAAQ,EAAExB,QAAQ,CAACe,UAAU,CAAC;UACrFf,QAAQ,CAACC,SAAS,GAAGiB,IAAI,CAACgB,KAAK,CAAC,CAACD,KAAK,EAAE,CAACD,KAAK,CAAC;UAC/ChC,QAAQ,CAACW,QAAQ,CAACM,KAAK,GAAGjB,QAAQ,CAACC,SAAS;UAC5C;QACJ;MACA;QAAS;UACL,IAAIX,aAAa,CAACU,QAAQ,CAACwB,QAAQ,EAAE5B,SAAS,CAACgC,MAAM,CAACC,IAAI,EAAE3C,MAAM,CAAC2B,MAAM,EAAEb,QAAQ,CAACmC,SAAS,CAAC,CAAC,EAAElC,SAAS,CAAC,EAAE;YACzG;UACJ;UACA,QAAQD,QAAQ,CAACK,OAAO;YACpB,KAAKpB,eAAe,CAACmD,OAAO;cAAE;gBAC1BpC,QAAQ,CAACwB,QAAQ,CAACjB,CAAC,GACfW,IAAI,CAACO,KAAK,CAAClC,aAAa,CAAC;kBACrBmC,GAAG,EAAE,CAAC1B,QAAQ,CAACe,UAAU,CAACC,MAAM;kBAChCW,GAAG,EAAE3B,QAAQ,CAACe,UAAU,CAACC;gBAC7B,CAAC,CAAC,CAAC,GAAGhB,QAAQ,CAACe,UAAU,CAACR,CAAC;gBAC/BP,QAAQ,CAACwB,QAAQ,CAACf,CAAC,GACfS,IAAI,CAACO,KAAK,CAAClC,aAAa,CAAC;kBACrBmC,GAAG,EAAE,CAAC1B,QAAQ,CAACe,UAAU,CAACC,MAAM;kBAChCW,GAAG,EAAE3B,QAAQ,CAACe,UAAU,CAACC;gBAC7B,CAAC,CAAC,CAAC,GAAGhB,QAAQ,CAACe,UAAU,CAACN,CAAC;gBAC/B,MAAM;kBAAEa,EAAE;kBAAEC;gBAAG,CAAC,GAAGnC,YAAY,CAACY,QAAQ,CAACwB,QAAQ,EAAExB,QAAQ,CAACe,UAAU,CAAC;gBACvE,IAAIf,QAAQ,CAACe,UAAU,CAACC,MAAM,EAAE;kBAC5BhB,QAAQ,CAACC,SAAS,GAAGiB,IAAI,CAACgB,KAAK,CAACX,EAAE,EAAED,EAAE,CAAC;kBACvCtB,QAAQ,CAACW,QAAQ,CAACM,KAAK,GAAGjB,QAAQ,CAACC,SAAS;gBAChD;gBACA;cACJ;YACA,KAAKhB,eAAe,CAACoD,MAAM;cAAE;gBACzB,MAAMC,IAAI,GAAGtC,QAAQ,CAACuC,OAAO,CAACC,IAAI,CAACF,IAAI;kBAAEG,UAAU,GAAG7C,SAAS,CAACgC,MAAM,CAACC,IAAI;kBAAEa,MAAM,GAAG;oBAClFC,MAAM,EAAEF,UAAU,CAACV,MAAM,GAAG/B,QAAQ,CAACmC,SAAS,CAAC,CAAC,GAAGnC,QAAQ,CAAC4C,MAAM,CAACnC,CAAC;oBACpEoC,IAAI,EAAE,CAAC7C,QAAQ,CAACmC,SAAS,CAAC,CAAC,GAAGnC,QAAQ,CAAC4C,MAAM,CAACrC,CAAC;oBAC/CuC,KAAK,EAAEL,UAAU,CAACX,KAAK,GAAG9B,QAAQ,CAACmC,SAAS,CAAC,CAAC,GAAGnC,QAAQ,CAAC4C,MAAM,CAACrC,CAAC;oBAClEwC,GAAG,EAAE,CAAC/C,QAAQ,CAACmC,SAAS,CAAC,CAAC,GAAGnC,QAAQ,CAAC4C,MAAM,CAACnC;kBACjD,CAAC;kBAAEuC,SAAS,GAAGhD,QAAQ,CAACmC,SAAS,CAAC,CAAC;kBAAEc,UAAU,GAAG9D,eAAe,CAACa,QAAQ,CAACwB,QAAQ,EAAEwB,SAAS,CAAC;gBAC/F,IAAI/C,SAAS,KAAKjB,gBAAgB,CAAC8D,KAAK,IACpCG,UAAU,CAACJ,IAAI,GAAGJ,UAAU,CAACX,KAAK,GAAG9B,QAAQ,CAAC4C,MAAM,CAACrC,CAAC,EAAE;kBACxDP,QAAQ,CAACwB,QAAQ,CAACjB,CAAC,GAAGmC,MAAM,CAACG,IAAI;kBACjC7C,QAAQ,CAACkD,eAAe,CAAC3C,CAAC,GAAGP,QAAQ,CAACwB,QAAQ,CAACjB,CAAC;kBAChD,IAAI,CAAC+B,IAAI,EAAE;oBACPtC,QAAQ,CAACwB,QAAQ,CAACf,CAAC,GAAGpB,SAAS,CAAC,CAAC,GAAGoD,UAAU,CAACV,MAAM;oBACrD/B,QAAQ,CAACkD,eAAe,CAACzC,CAAC,GAAGT,QAAQ,CAACwB,QAAQ,CAACf,CAAC;kBACpD;gBACJ,CAAC,MACI,IAAIR,SAAS,KAAKjB,gBAAgB,CAAC6D,IAAI,IAAII,UAAU,CAACH,KAAK,GAAG,CAAC9C,QAAQ,CAAC4C,MAAM,CAACrC,CAAC,EAAE;kBACnFP,QAAQ,CAACwB,QAAQ,CAACjB,CAAC,GAAGmC,MAAM,CAACI,KAAK;kBAClC9C,QAAQ,CAACkD,eAAe,CAAC3C,CAAC,GAAGP,QAAQ,CAACwB,QAAQ,CAACjB,CAAC;kBAChD,IAAI,CAAC+B,IAAI,EAAE;oBACPtC,QAAQ,CAACwB,QAAQ,CAACf,CAAC,GAAGpB,SAAS,CAAC,CAAC,GAAGoD,UAAU,CAACV,MAAM;oBACrD/B,QAAQ,CAACkD,eAAe,CAACzC,CAAC,GAAGT,QAAQ,CAACwB,QAAQ,CAACf,CAAC;kBACpD;gBACJ;gBACA,IAAIR,SAAS,KAAKjB,gBAAgB,CAAC2D,MAAM,IACrCM,UAAU,CAACF,GAAG,GAAGN,UAAU,CAACV,MAAM,GAAG/B,QAAQ,CAAC4C,MAAM,CAACnC,CAAC,EAAE;kBACxD,IAAI,CAAC6B,IAAI,EAAE;oBACPtC,QAAQ,CAACwB,QAAQ,CAACjB,CAAC,GAAGlB,SAAS,CAAC,CAAC,GAAGoD,UAAU,CAACX,KAAK;oBACpD9B,QAAQ,CAACkD,eAAe,CAAC3C,CAAC,GAAGP,QAAQ,CAACwB,QAAQ,CAACjB,CAAC;kBACpD;kBACAP,QAAQ,CAACwB,QAAQ,CAACf,CAAC,GAAGiC,MAAM,CAACK,GAAG;kBAChC/C,QAAQ,CAACkD,eAAe,CAACzC,CAAC,GAAGT,QAAQ,CAACwB,QAAQ,CAACf,CAAC;gBACpD,CAAC,MACI,IAAIR,SAAS,KAAKjB,gBAAgB,CAAC+D,GAAG,IAAIE,UAAU,CAACN,MAAM,GAAG,CAAC3C,QAAQ,CAAC4C,MAAM,CAACnC,CAAC,EAAE;kBACnF,IAAI,CAAC6B,IAAI,EAAE;oBACPtC,QAAQ,CAACwB,QAAQ,CAACjB,CAAC,GAAGlB,SAAS,CAAC,CAAC,GAAGoD,UAAU,CAACX,KAAK;oBACpD9B,QAAQ,CAACkD,eAAe,CAAC3C,CAAC,GAAGP,QAAQ,CAACwB,QAAQ,CAACjB,CAAC;kBACpD;kBACAP,QAAQ,CAACwB,QAAQ,CAACf,CAAC,GAAGiC,MAAM,CAACC,MAAM;kBACnC3C,QAAQ,CAACkD,eAAe,CAACzC,CAAC,GAAGT,QAAQ,CAACwB,QAAQ,CAACf,CAAC;gBACpD;gBACA;cACJ;UACJ;UACA;QACJ;IACJ;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
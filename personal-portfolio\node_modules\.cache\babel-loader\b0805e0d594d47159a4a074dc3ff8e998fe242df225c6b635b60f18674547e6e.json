{"ast": null, "code": "import { calculateBounds } from \"tsparticles-engine\";\nimport { bounceHorizontal, bounceVertical } from \"./Utils\";\nexport class BounceOutMode {\n  constructor(container) {\n    this.container = container;\n    this.modes = [\"bounce\", \"bounce-vertical\", \"bounce-horizontal\", \"bounceVertical\", \"bounceHorizontal\", \"split\"];\n  }\n  update(particle, direction, delta, outMode) {\n    if (!this.modes.includes(outMode)) {\n      return;\n    }\n    const container = this.container;\n    let handled = false;\n    for (const [, plugin] of container.plugins) {\n      if (plugin.particleBounce !== undefined) {\n        handled = plugin.particleBounce(particle, delta, direction);\n      }\n      if (handled) {\n        break;\n      }\n    }\n    if (handled) {\n      return;\n    }\n    const pos = particle.getPosition(),\n      offset = particle.offset,\n      size = particle.getRadius(),\n      bounds = calculateBounds(pos, size),\n      canvasSize = container.canvas.size;\n    bounceHorizontal({\n      particle,\n      outMode,\n      direction,\n      bounds,\n      canvasSize,\n      offset,\n      size\n    });\n    bounceVertical({\n      particle,\n      outMode,\n      direction,\n      bounds,\n      canvasSize,\n      offset,\n      size\n    });\n  }\n}", "map": {"version": 3, "names": ["calculateBounds", "bounceHorizontal", "bounceVertical", "BounceOutMode", "constructor", "container", "modes", "update", "particle", "direction", "delta", "outMode", "includes", "handled", "plugin", "plugins", "particleBounce", "undefined", "pos", "getPosition", "offset", "size", "getRadius", "bounds", "canvasSize", "canvas"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/tsparticles-updater-out-modes/esm/BounceOutMode.js"], "sourcesContent": ["import { calculateBounds, } from \"tsparticles-engine\";\nimport { bounceHorizontal, bounceVertical } from \"./Utils\";\nexport class BounceOutMode {\n    constructor(container) {\n        this.container = container;\n        this.modes = [\n            \"bounce\",\n            \"bounce-vertical\",\n            \"bounce-horizontal\",\n            \"bounceVertical\",\n            \"bounceHorizontal\",\n            \"split\",\n        ];\n    }\n    update(particle, direction, delta, outMode) {\n        if (!this.modes.includes(outMode)) {\n            return;\n        }\n        const container = this.container;\n        let handled = false;\n        for (const [, plugin] of container.plugins) {\n            if (plugin.particleBounce !== undefined) {\n                handled = plugin.particleBounce(particle, delta, direction);\n            }\n            if (handled) {\n                break;\n            }\n        }\n        if (handled) {\n            return;\n        }\n        const pos = particle.getPosition(), offset = particle.offset, size = particle.getRadius(), bounds = calculateBounds(pos, size), canvasSize = container.canvas.size;\n        bounceHorizontal({ particle, outMode, direction, bounds, canvasSize, offset, size });\n        bounceVertical({ particle, outMode, direction, bounds, canvasSize, offset, size });\n    }\n}\n"], "mappings": "AAAA,SAASA,eAAe,QAAS,oBAAoB;AACrD,SAASC,gBAAgB,EAAEC,cAAc,QAAQ,SAAS;AAC1D,OAAO,MAAMC,aAAa,CAAC;EACvBC,WAAWA,CAACC,SAAS,EAAE;IACnB,IAAI,CAACA,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,KAAK,GAAG,CACT,QAAQ,EACR,iBAAiB,EACjB,mBAAmB,EACnB,gBAAgB,EAChB,kBAAkB,EAClB,OAAO,CACV;EACL;EACAC,MAAMA,CAACC,QAAQ,EAAEC,SAAS,EAAEC,KAAK,EAAEC,OAAO,EAAE;IACxC,IAAI,CAAC,IAAI,CAACL,KAAK,CAACM,QAAQ,CAACD,OAAO,CAAC,EAAE;MAC/B;IACJ;IACA,MAAMN,SAAS,GAAG,IAAI,CAACA,SAAS;IAChC,IAAIQ,OAAO,GAAG,KAAK;IACnB,KAAK,MAAM,GAAGC,MAAM,CAAC,IAAIT,SAAS,CAACU,OAAO,EAAE;MACxC,IAAID,MAAM,CAACE,cAAc,KAAKC,SAAS,EAAE;QACrCJ,OAAO,GAAGC,MAAM,CAACE,cAAc,CAACR,QAAQ,EAAEE,KAAK,EAAED,SAAS,CAAC;MAC/D;MACA,IAAII,OAAO,EAAE;QACT;MACJ;IACJ;IACA,IAAIA,OAAO,EAAE;MACT;IACJ;IACA,MAAMK,GAAG,GAAGV,QAAQ,CAACW,WAAW,CAAC,CAAC;MAAEC,MAAM,GAAGZ,QAAQ,CAACY,MAAM;MAAEC,IAAI,GAAGb,QAAQ,CAACc,SAAS,CAAC,CAAC;MAAEC,MAAM,GAAGvB,eAAe,CAACkB,GAAG,EAAEG,IAAI,CAAC;MAAEG,UAAU,GAAGnB,SAAS,CAACoB,MAAM,CAACJ,IAAI;IAClKpB,gBAAgB,CAAC;MAAEO,QAAQ;MAAEG,OAAO;MAAEF,SAAS;MAAEc,MAAM;MAAEC,UAAU;MAAEJ,MAAM;MAAEC;IAAK,CAAC,CAAC;IACpFnB,cAAc,CAAC;MAAEM,QAAQ;MAAEG,OAAO;MAAEF,SAAS;MAAEc,MAAM;MAAEC,UAAU;MAAEJ,MAAM;MAAEC;IAAK,CAAC,CAAC;EACtF;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
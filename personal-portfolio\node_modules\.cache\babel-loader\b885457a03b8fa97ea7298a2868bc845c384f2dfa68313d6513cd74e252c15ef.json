{"ast": null, "code": "import { bounce } from \"./Bounce.js\";\nexport function destroy(p1, p2) {\n  if (!p1.unbreakable && !p2.unbreakable) {\n    bounce(p1, p2);\n  }\n  if (p1.getRadius() === undefined && p2.getRadius() !== undefined) {\n    p1.destroy();\n  } else if (p1.getRadius() !== undefined && p2.getRadius() === undefined) {\n    p2.destroy();\n  } else if (p1.getRadius() !== undefined && p2.getRadius() !== undefined) {\n    const deleteP = p1.getRadius() >= p2.getRadius() ? p2 : p1;\n    deleteP.destroy();\n  }\n}", "map": {"version": 3, "names": ["bounce", "destroy", "p1", "p2", "unbreakable", "getRadius", "undefined", "deleteP"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/interaction-particles-collisions/browser/Destroy.js"], "sourcesContent": ["import { bounce } from \"./Bounce.js\";\nexport function destroy(p1, p2) {\n    if (!p1.unbreakable && !p2.unbreakable) {\n        bounce(p1, p2);\n    }\n    if (p1.getRadius() === undefined && p2.getRadius() !== undefined) {\n        p1.destroy();\n    }\n    else if (p1.getRadius() !== undefined && p2.getRadius() === undefined) {\n        p2.destroy();\n    }\n    else if (p1.getRadius() !== undefined && p2.getRadius() !== undefined) {\n        const deleteP = p1.getRadius() >= p2.getRadius() ? p2 : p1;\n        deleteP.destroy();\n    }\n}\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,aAAa;AACpC,OAAO,SAASC,OAAOA,CAACC,EAAE,EAAEC,EAAE,EAAE;EAC5B,IAAI,CAACD,EAAE,CAACE,WAAW,IAAI,CAACD,EAAE,CAACC,WAAW,EAAE;IACpCJ,MAAM,CAACE,EAAE,EAAEC,EAAE,CAAC;EAClB;EACA,IAAID,EAAE,CAACG,SAAS,CAAC,CAAC,KAAKC,SAAS,IAAIH,EAAE,CAACE,SAAS,CAAC,CAAC,KAAKC,SAAS,EAAE;IAC9DJ,EAAE,CAACD,OAAO,CAAC,CAAC;EAChB,CAAC,MACI,IAAIC,EAAE,CAACG,SAAS,CAAC,CAAC,KAAKC,SAAS,IAAIH,EAAE,CAACE,SAAS,CAAC,CAAC,KAAKC,SAAS,EAAE;IACnEH,EAAE,CAACF,OAAO,CAAC,CAAC;EAChB,CAAC,MACI,IAAIC,EAAE,CAACG,SAAS,CAAC,CAAC,KAAKC,SAAS,IAAIH,EAAE,CAACE,SAAS,CAAC,CAAC,KAAKC,SAAS,EAAE;IACnE,MAAMC,OAAO,GAAGL,EAAE,CAACG,SAAS,CAAC,CAAC,IAAIF,EAAE,CAACE,SAAS,CAAC,CAAC,GAAGF,EAAE,GAAGD,EAAE;IAC1DK,OAAO,CAACN,OAAO,CAAC,CAAC;EACrB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
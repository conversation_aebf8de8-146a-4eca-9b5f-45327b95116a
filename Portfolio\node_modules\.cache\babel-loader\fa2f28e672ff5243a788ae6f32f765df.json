{"ast": null, "code": "import { AnimatableColor } from \"../AnimatableColor\";\nimport { AnimatableGradient } from \"../AnimatableGradient\";\nimport { Bounce } from \"./Bounce/Bounce\";\nimport { Collisions } from \"./Collisions/Collisions\";\nimport { Destroy } from \"./Destroy/Destroy\";\nimport { Life } from \"./Life/Life\";\nimport { Links } from \"./Links/Links\";\nimport { Move } from \"./Move/Move\";\nimport { Opacity } from \"./Opacity/Opacity\";\nimport { Orbit } from \"./Orbit/Orbit\";\nimport { ParticlesNumber } from \"./Number/ParticlesNumber\";\nimport { Repulse } from \"./Repulse/Repulse\";\nimport { Roll } from \"./Roll/Roll\";\nimport { Rotate } from \"./Rotate/Rotate\";\nimport { Shadow } from \"./Shadow\";\nimport { Shape } from \"./Shape/Shape\";\nimport { Size } from \"./Size/Size\";\nimport { Stroke } from \"./Stroke\";\nimport { Tilt } from \"./Tilt/Tilt\";\nimport { Twinkle } from \"./Twinkle/Twinkle\";\nimport { Wobble } from \"./Wobble/Wobble\";\nimport { ZIndex } from \"./ZIndex/ZIndex\";\nimport { deepExtend } from \"../../../Utils\";\nexport class ParticlesOptions {\n  constructor() {\n    this.bounce = new Bounce();\n    this.collisions = new Collisions();\n    this.color = new AnimatableColor();\n    this.destroy = new Destroy();\n    this.gradient = [];\n    this.groups = {};\n    this.life = new Life();\n    this.links = new Links();\n    this.move = new Move();\n    this.number = new ParticlesNumber();\n    this.opacity = new Opacity();\n    this.orbit = new Orbit();\n    this.reduceDuplicates = false;\n    this.repulse = new Repulse();\n    this.roll = new Roll();\n    this.rotate = new Rotate();\n    this.shadow = new Shadow();\n    this.shape = new Shape();\n    this.size = new Size();\n    this.stroke = new Stroke();\n    this.tilt = new Tilt();\n    this.twinkle = new Twinkle();\n    this.wobble = new Wobble();\n    this.zIndex = new ZIndex();\n  }\n\n  get line_linked() {\n    return this.links;\n  }\n\n  set line_linked(value) {\n    this.links = value;\n  }\n\n  get lineLinked() {\n    return this.links;\n  }\n\n  set lineLinked(value) {\n    this.links = value;\n  }\n\n  load(data) {\n    var _a, _b, _c, _d, _e, _f, _g, _h;\n\n    if (data === undefined) {\n      return;\n    }\n\n    this.bounce.load(data.bounce);\n    this.color.load(AnimatableColor.create(this.color, data.color));\n    this.destroy.load(data.destroy);\n    this.life.load(data.life);\n    const links = (_b = (_a = data.links) !== null && _a !== void 0 ? _a : data.lineLinked) !== null && _b !== void 0 ? _b : data.line_linked;\n\n    if (links !== undefined) {\n      this.links.load(links);\n    }\n\n    if (data.groups !== undefined) {\n      for (const group in data.groups) {\n        const item = data.groups[group];\n\n        if (item !== undefined) {\n          this.groups[group] = deepExtend((_c = this.groups[group]) !== null && _c !== void 0 ? _c : {}, item);\n        }\n      }\n    }\n\n    this.move.load(data.move);\n    this.number.load(data.number);\n    this.opacity.load(data.opacity);\n    this.orbit.load(data.orbit);\n\n    if (data.reduceDuplicates !== undefined) {\n      this.reduceDuplicates = data.reduceDuplicates;\n    }\n\n    this.repulse.load(data.repulse);\n    this.roll.load(data.roll);\n    this.rotate.load(data.rotate);\n    this.shape.load(data.shape);\n    this.size.load(data.size);\n    this.shadow.load(data.shadow);\n    this.tilt.load(data.tilt);\n    this.twinkle.load(data.twinkle);\n    this.wobble.load(data.wobble);\n    this.zIndex.load(data.zIndex);\n    const collisions = (_e = (_d = data.move) === null || _d === void 0 ? void 0 : _d.collisions) !== null && _e !== void 0 ? _e : (_f = data.move) === null || _f === void 0 ? void 0 : _f.bounce;\n\n    if (collisions !== undefined) {\n      this.collisions.enable = collisions;\n    }\n\n    this.collisions.load(data.collisions);\n    const strokeToLoad = (_g = data.stroke) !== null && _g !== void 0 ? _g : (_h = data.shape) === null || _h === void 0 ? void 0 : _h.stroke;\n\n    if (strokeToLoad) {\n      if (strokeToLoad instanceof Array) {\n        this.stroke = strokeToLoad.map(s => {\n          const tmp = new Stroke();\n          tmp.load(s);\n          return tmp;\n        });\n      } else {\n        if (this.stroke instanceof Array) {\n          this.stroke = new Stroke();\n        }\n\n        this.stroke.load(strokeToLoad);\n      }\n    }\n\n    const gradientToLoad = data.gradient;\n\n    if (gradientToLoad) {\n      if (gradientToLoad instanceof Array) {\n        this.gradient = gradientToLoad.map(s => {\n          const tmp = new AnimatableGradient();\n          tmp.load(s);\n          return tmp;\n        });\n      } else {\n        if (this.gradient instanceof Array) {\n          this.gradient = new AnimatableGradient();\n        }\n\n        this.gradient.load(gradientToLoad);\n      }\n    }\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Options/Classes/Particles/ParticlesOptions.js"], "names": ["AnimatableColor", "AnimatableGradient", "<PERSON><PERSON><PERSON>", "Collisions", "Destroy", "Life", "Links", "Move", "Opacity", "Orbit", "ParticlesNumber", "Repulse", "Roll", "Rotate", "Shadow", "<PERSON><PERSON><PERSON>", "Size", "Stroke", "Tilt", "Twinkle", "Wobble", "ZIndex", "deepExtend", "ParticlesOptions", "constructor", "bounce", "collisions", "color", "destroy", "gradient", "groups", "life", "links", "move", "number", "opacity", "orbit", "reduceDuplicates", "repulse", "roll", "rotate", "shadow", "shape", "size", "stroke", "tilt", "twinkle", "wobble", "zIndex", "line_linked", "value", "lineLinked", "load", "data", "_a", "_b", "_c", "_d", "_e", "_f", "_g", "_h", "undefined", "create", "group", "item", "enable", "strokeToLoad", "Array", "map", "s", "tmp", "gradientToLoad"], "mappings": "AAAA,SAASA,eAAT,QAAgC,oBAAhC;AACA,SAASC,kBAAT,QAAmC,uBAAnC;AACA,SAASC,MAAT,QAAuB,iBAAvB;AACA,SAASC,UAAT,QAA2B,yBAA3B;AACA,SAASC,OAAT,QAAwB,mBAAxB;AACA,SAASC,IAAT,QAAqB,aAArB;AACA,SAASC,KAAT,QAAsB,eAAtB;AACA,SAASC,IAAT,QAAqB,aAArB;AACA,SAASC,OAAT,QAAwB,mBAAxB;AACA,SAASC,KAAT,QAAsB,eAAtB;AACA,SAASC,eAAT,QAAgC,0BAAhC;AACA,SAASC,OAAT,QAAwB,mBAAxB;AACA,SAASC,IAAT,QAAqB,aAArB;AACA,SAASC,MAAT,QAAuB,iBAAvB;AACA,SAASC,MAAT,QAAuB,UAAvB;AACA,SAASC,KAAT,QAAsB,eAAtB;AACA,SAASC,IAAT,QAAqB,aAArB;AACA,SAASC,MAAT,QAAuB,UAAvB;AACA,SAASC,IAAT,QAAqB,aAArB;AACA,SAASC,OAAT,QAAwB,mBAAxB;AACA,SAASC,MAAT,QAAuB,iBAAvB;AACA,SAASC,MAAT,QAAuB,iBAAvB;AACA,SAASC,UAAT,QAA2B,gBAA3B;AACA,OAAO,MAAMC,gBAAN,CAAuB;AAC1BC,EAAAA,WAAW,GAAG;AACV,SAAKC,MAAL,GAAc,IAAIvB,MAAJ,EAAd;AACA,SAAKwB,UAAL,GAAkB,IAAIvB,UAAJ,EAAlB;AACA,SAAKwB,KAAL,GAAa,IAAI3B,eAAJ,EAAb;AACA,SAAK4B,OAAL,GAAe,IAAIxB,OAAJ,EAAf;AACA,SAAKyB,QAAL,GAAgB,EAAhB;AACA,SAAKC,MAAL,GAAc,EAAd;AACA,SAAKC,IAAL,GAAY,IAAI1B,IAAJ,EAAZ;AACA,SAAK2B,KAAL,GAAa,IAAI1B,KAAJ,EAAb;AACA,SAAK2B,IAAL,GAAY,IAAI1B,IAAJ,EAAZ;AACA,SAAK2B,MAAL,GAAc,IAAIxB,eAAJ,EAAd;AACA,SAAKyB,OAAL,GAAe,IAAI3B,OAAJ,EAAf;AACA,SAAK4B,KAAL,GAAa,IAAI3B,KAAJ,EAAb;AACA,SAAK4B,gBAAL,GAAwB,KAAxB;AACA,SAAKC,OAAL,GAAe,IAAI3B,OAAJ,EAAf;AACA,SAAK4B,IAAL,GAAY,IAAI3B,IAAJ,EAAZ;AACA,SAAK4B,MAAL,GAAc,IAAI3B,MAAJ,EAAd;AACA,SAAK4B,MAAL,GAAc,IAAI3B,MAAJ,EAAd;AACA,SAAK4B,KAAL,GAAa,IAAI3B,KAAJ,EAAb;AACA,SAAK4B,IAAL,GAAY,IAAI3B,IAAJ,EAAZ;AACA,SAAK4B,MAAL,GAAc,IAAI3B,MAAJ,EAAd;AACA,SAAK4B,IAAL,GAAY,IAAI3B,IAAJ,EAAZ;AACA,SAAK4B,OAAL,GAAe,IAAI3B,OAAJ,EAAf;AACA,SAAK4B,MAAL,GAAc,IAAI3B,MAAJ,EAAd;AACA,SAAK4B,MAAL,GAAc,IAAI3B,MAAJ,EAAd;AACH;;AACc,MAAX4B,WAAW,GAAG;AACd,WAAO,KAAKjB,KAAZ;AACH;;AACc,MAAXiB,WAAW,CAACC,KAAD,EAAQ;AACnB,SAAKlB,KAAL,GAAakB,KAAb;AACH;;AACa,MAAVC,UAAU,GAAG;AACb,WAAO,KAAKnB,KAAZ;AACH;;AACa,MAAVmB,UAAU,CAACD,KAAD,EAAQ;AAClB,SAAKlB,KAAL,GAAakB,KAAb;AACH;;AACDE,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,QAAIC,EAAJ,EAAQC,EAAR,EAAYC,EAAZ,EAAgBC,EAAhB,EAAoBC,EAApB,EAAwBC,EAAxB,EAA4BC,EAA5B,EAAgCC,EAAhC;;AACA,QAAIR,IAAI,KAAKS,SAAb,EAAwB;AACpB;AACH;;AACD,SAAKrC,MAAL,CAAY2B,IAAZ,CAAiBC,IAAI,CAAC5B,MAAtB;AACA,SAAKE,KAAL,CAAWyB,IAAX,CAAgBpD,eAAe,CAAC+D,MAAhB,CAAuB,KAAKpC,KAA5B,EAAmC0B,IAAI,CAAC1B,KAAxC,CAAhB;AACA,SAAKC,OAAL,CAAawB,IAAb,CAAkBC,IAAI,CAACzB,OAAvB;AACA,SAAKG,IAAL,CAAUqB,IAAV,CAAeC,IAAI,CAACtB,IAApB;AACA,UAAMC,KAAK,GAAG,CAACuB,EAAE,GAAG,CAACD,EAAE,GAAGD,IAAI,CAACrB,KAAX,MAAsB,IAAtB,IAA8BsB,EAAE,KAAK,KAAK,CAA1C,GAA8CA,EAA9C,GAAmDD,IAAI,CAACF,UAA9D,MAA8E,IAA9E,IAAsFI,EAAE,KAAK,KAAK,CAAlG,GAAsGA,EAAtG,GAA2GF,IAAI,CAACJ,WAA9H;;AACA,QAAIjB,KAAK,KAAK8B,SAAd,EAAyB;AACrB,WAAK9B,KAAL,CAAWoB,IAAX,CAAgBpB,KAAhB;AACH;;AACD,QAAIqB,IAAI,CAACvB,MAAL,KAAgBgC,SAApB,EAA+B;AAC3B,WAAK,MAAME,KAAX,IAAoBX,IAAI,CAACvB,MAAzB,EAAiC;AAC7B,cAAMmC,IAAI,GAAGZ,IAAI,CAACvB,MAAL,CAAYkC,KAAZ,CAAb;;AACA,YAAIC,IAAI,KAAKH,SAAb,EAAwB;AACpB,eAAKhC,MAAL,CAAYkC,KAAZ,IAAqB1C,UAAU,CAAC,CAACkC,EAAE,GAAG,KAAK1B,MAAL,CAAYkC,KAAZ,CAAN,MAA8B,IAA9B,IAAsCR,EAAE,KAAK,KAAK,CAAlD,GAAsDA,EAAtD,GAA2D,EAA5D,EAAgES,IAAhE,CAA/B;AACH;AACJ;AACJ;;AACD,SAAKhC,IAAL,CAAUmB,IAAV,CAAeC,IAAI,CAACpB,IAApB;AACA,SAAKC,MAAL,CAAYkB,IAAZ,CAAiBC,IAAI,CAACnB,MAAtB;AACA,SAAKC,OAAL,CAAaiB,IAAb,CAAkBC,IAAI,CAAClB,OAAvB;AACA,SAAKC,KAAL,CAAWgB,IAAX,CAAgBC,IAAI,CAACjB,KAArB;;AACA,QAAIiB,IAAI,CAAChB,gBAAL,KAA0ByB,SAA9B,EAAyC;AACrC,WAAKzB,gBAAL,GAAwBgB,IAAI,CAAChB,gBAA7B;AACH;;AACD,SAAKC,OAAL,CAAac,IAAb,CAAkBC,IAAI,CAACf,OAAvB;AACA,SAAKC,IAAL,CAAUa,IAAV,CAAeC,IAAI,CAACd,IAApB;AACA,SAAKC,MAAL,CAAYY,IAAZ,CAAiBC,IAAI,CAACb,MAAtB;AACA,SAAKE,KAAL,CAAWU,IAAX,CAAgBC,IAAI,CAACX,KAArB;AACA,SAAKC,IAAL,CAAUS,IAAV,CAAeC,IAAI,CAACV,IAApB;AACA,SAAKF,MAAL,CAAYW,IAAZ,CAAiBC,IAAI,CAACZ,MAAtB;AACA,SAAKI,IAAL,CAAUO,IAAV,CAAeC,IAAI,CAACR,IAApB;AACA,SAAKC,OAAL,CAAaM,IAAb,CAAkBC,IAAI,CAACP,OAAvB;AACA,SAAKC,MAAL,CAAYK,IAAZ,CAAiBC,IAAI,CAACN,MAAtB;AACA,SAAKC,MAAL,CAAYI,IAAZ,CAAiBC,IAAI,CAACL,MAAtB;AACA,UAAMtB,UAAU,GAAG,CAACgC,EAAE,GAAG,CAACD,EAAE,GAAGJ,IAAI,CAACpB,IAAX,MAAqB,IAArB,IAA6BwB,EAAE,KAAK,KAAK,CAAzC,GAA6C,KAAK,CAAlD,GAAsDA,EAAE,CAAC/B,UAA/D,MAA+E,IAA/E,IAAuFgC,EAAE,KAAK,KAAK,CAAnG,GAAuGA,EAAvG,GAA4G,CAACC,EAAE,GAAGN,IAAI,CAACpB,IAAX,MAAqB,IAArB,IAA6B0B,EAAE,KAAK,KAAK,CAAzC,GAA6C,KAAK,CAAlD,GAAsDA,EAAE,CAAClC,MAAxL;;AACA,QAAIC,UAAU,KAAKoC,SAAnB,EAA8B;AAC1B,WAAKpC,UAAL,CAAgBwC,MAAhB,GAAyBxC,UAAzB;AACH;;AACD,SAAKA,UAAL,CAAgB0B,IAAhB,CAAqBC,IAAI,CAAC3B,UAA1B;AACA,UAAMyC,YAAY,GAAG,CAACP,EAAE,GAAGP,IAAI,CAACT,MAAX,MAAuB,IAAvB,IAA+BgB,EAAE,KAAK,KAAK,CAA3C,GAA+CA,EAA/C,GAAoD,CAACC,EAAE,GAAGR,IAAI,CAACX,KAAX,MAAsB,IAAtB,IAA8BmB,EAAE,KAAK,KAAK,CAA1C,GAA8C,KAAK,CAAnD,GAAuDA,EAAE,CAACjB,MAAnI;;AACA,QAAIuB,YAAJ,EAAkB;AACd,UAAIA,YAAY,YAAYC,KAA5B,EAAmC;AAC/B,aAAKxB,MAAL,GAAcuB,YAAY,CAACE,GAAb,CAAkBC,CAAD,IAAO;AAClC,gBAAMC,GAAG,GAAG,IAAItD,MAAJ,EAAZ;AACAsD,UAAAA,GAAG,CAACnB,IAAJ,CAASkB,CAAT;AACA,iBAAOC,GAAP;AACH,SAJa,CAAd;AAKH,OAND,MAOK;AACD,YAAI,KAAK3B,MAAL,YAAuBwB,KAA3B,EAAkC;AAC9B,eAAKxB,MAAL,GAAc,IAAI3B,MAAJ,EAAd;AACH;;AACD,aAAK2B,MAAL,CAAYQ,IAAZ,CAAiBe,YAAjB;AACH;AACJ;;AACD,UAAMK,cAAc,GAAGnB,IAAI,CAACxB,QAA5B;;AACA,QAAI2C,cAAJ,EAAoB;AAChB,UAAIA,cAAc,YAAYJ,KAA9B,EAAqC;AACjC,aAAKvC,QAAL,GAAgB2C,cAAc,CAACH,GAAf,CAAoBC,CAAD,IAAO;AACtC,gBAAMC,GAAG,GAAG,IAAItE,kBAAJ,EAAZ;AACAsE,UAAAA,GAAG,CAACnB,IAAJ,CAASkB,CAAT;AACA,iBAAOC,GAAP;AACH,SAJe,CAAhB;AAKH,OAND,MAOK;AACD,YAAI,KAAK1C,QAAL,YAAyBuC,KAA7B,EAAoC;AAChC,eAAKvC,QAAL,GAAgB,IAAI5B,kBAAJ,EAAhB;AACH;;AACD,aAAK4B,QAAL,CAAcuB,IAAd,CAAmBoB,cAAnB;AACH;AACJ;AACJ;;AAlHyB", "sourcesContent": ["import { AnimatableColor } from \"../AnimatableColor\";\nimport { AnimatableGradient } from \"../AnimatableGradient\";\nimport { Bounce } from \"./Bounce/Bounce\";\nimport { Collisions } from \"./Collisions/Collisions\";\nimport { Destroy } from \"./Destroy/Destroy\";\nimport { Life } from \"./Life/Life\";\nimport { Links } from \"./Links/Links\";\nimport { Move } from \"./Move/Move\";\nimport { Opacity } from \"./Opacity/Opacity\";\nimport { Orbit } from \"./Orbit/Orbit\";\nimport { ParticlesNumber } from \"./Number/ParticlesNumber\";\nimport { Repulse } from \"./Repulse/Repulse\";\nimport { Roll } from \"./Roll/Roll\";\nimport { Rotate } from \"./Rotate/Rotate\";\nimport { Shadow } from \"./Shadow\";\nimport { Shape } from \"./Shape/Shape\";\nimport { Size } from \"./Size/Size\";\nimport { Stroke } from \"./Stroke\";\nimport { Tilt } from \"./Tilt/Tilt\";\nimport { Twinkle } from \"./Twinkle/Twinkle\";\nimport { Wobble } from \"./Wobble/Wobble\";\nimport { ZIndex } from \"./ZIndex/ZIndex\";\nimport { deepExtend } from \"../../../Utils\";\nexport class ParticlesOptions {\n    constructor() {\n        this.bounce = new Bounce();\n        this.collisions = new Collisions();\n        this.color = new AnimatableColor();\n        this.destroy = new Destroy();\n        this.gradient = [];\n        this.groups = {};\n        this.life = new Life();\n        this.links = new Links();\n        this.move = new Move();\n        this.number = new ParticlesNumber();\n        this.opacity = new Opacity();\n        this.orbit = new Orbit();\n        this.reduceDuplicates = false;\n        this.repulse = new Repulse();\n        this.roll = new Roll();\n        this.rotate = new Rotate();\n        this.shadow = new Shadow();\n        this.shape = new Shape();\n        this.size = new Size();\n        this.stroke = new Stroke();\n        this.tilt = new Tilt();\n        this.twinkle = new Twinkle();\n        this.wobble = new Wobble();\n        this.zIndex = new ZIndex();\n    }\n    get line_linked() {\n        return this.links;\n    }\n    set line_linked(value) {\n        this.links = value;\n    }\n    get lineLinked() {\n        return this.links;\n    }\n    set lineLinked(value) {\n        this.links = value;\n    }\n    load(data) {\n        var _a, _b, _c, _d, _e, _f, _g, _h;\n        if (data === undefined) {\n            return;\n        }\n        this.bounce.load(data.bounce);\n        this.color.load(AnimatableColor.create(this.color, data.color));\n        this.destroy.load(data.destroy);\n        this.life.load(data.life);\n        const links = (_b = (_a = data.links) !== null && _a !== void 0 ? _a : data.lineLinked) !== null && _b !== void 0 ? _b : data.line_linked;\n        if (links !== undefined) {\n            this.links.load(links);\n        }\n        if (data.groups !== undefined) {\n            for (const group in data.groups) {\n                const item = data.groups[group];\n                if (item !== undefined) {\n                    this.groups[group] = deepExtend((_c = this.groups[group]) !== null && _c !== void 0 ? _c : {}, item);\n                }\n            }\n        }\n        this.move.load(data.move);\n        this.number.load(data.number);\n        this.opacity.load(data.opacity);\n        this.orbit.load(data.orbit);\n        if (data.reduceDuplicates !== undefined) {\n            this.reduceDuplicates = data.reduceDuplicates;\n        }\n        this.repulse.load(data.repulse);\n        this.roll.load(data.roll);\n        this.rotate.load(data.rotate);\n        this.shape.load(data.shape);\n        this.size.load(data.size);\n        this.shadow.load(data.shadow);\n        this.tilt.load(data.tilt);\n        this.twinkle.load(data.twinkle);\n        this.wobble.load(data.wobble);\n        this.zIndex.load(data.zIndex);\n        const collisions = (_e = (_d = data.move) === null || _d === void 0 ? void 0 : _d.collisions) !== null && _e !== void 0 ? _e : (_f = data.move) === null || _f === void 0 ? void 0 : _f.bounce;\n        if (collisions !== undefined) {\n            this.collisions.enable = collisions;\n        }\n        this.collisions.load(data.collisions);\n        const strokeToLoad = (_g = data.stroke) !== null && _g !== void 0 ? _g : (_h = data.shape) === null || _h === void 0 ? void 0 : _h.stroke;\n        if (strokeToLoad) {\n            if (strokeToLoad instanceof Array) {\n                this.stroke = strokeToLoad.map((s) => {\n                    const tmp = new Stroke();\n                    tmp.load(s);\n                    return tmp;\n                });\n            }\n            else {\n                if (this.stroke instanceof Array) {\n                    this.stroke = new Stroke();\n                }\n                this.stroke.load(strokeToLoad);\n            }\n        }\n        const gradientToLoad = data.gradient;\n        if (gradientToLoad) {\n            if (gradientToLoad instanceof Array) {\n                this.gradient = gradientToLoad.map((s) => {\n                    const tmp = new AnimatableGradient();\n                    tmp.load(s);\n                    return tmp;\n                });\n            }\n            else {\n                if (this.gradient instanceof Array) {\n                    this.gradient = new AnimatableGradient();\n                }\n                this.gradient.load(gradientToLoad);\n            }\n        }\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
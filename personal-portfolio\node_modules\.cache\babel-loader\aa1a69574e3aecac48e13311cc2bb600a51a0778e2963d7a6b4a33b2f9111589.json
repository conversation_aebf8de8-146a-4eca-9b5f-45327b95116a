{"ast": null, "code": "import { EasingType } from \"@tsparticles/engine\";\nexport class Attract {\n  constructor() {\n    this.distance = 200;\n    this.duration = 0.4;\n    this.easing = EasingType.easeOutQuad;\n    this.factor = 1;\n    this.maxSpeed = 50;\n    this.speed = 1;\n  }\n  load(data) {\n    if (!data) {\n      return;\n    }\n    if (data.distance !== undefined) {\n      this.distance = data.distance;\n    }\n    if (data.duration !== undefined) {\n      this.duration = data.duration;\n    }\n    if (data.easing !== undefined) {\n      this.easing = data.easing;\n    }\n    if (data.factor !== undefined) {\n      this.factor = data.factor;\n    }\n    if (data.maxSpeed !== undefined) {\n      this.maxSpeed = data.maxSpeed;\n    }\n    if (data.speed !== undefined) {\n      this.speed = data.speed;\n    }\n  }\n}", "map": {"version": 3, "names": ["EasingType", "Attract", "constructor", "distance", "duration", "easing", "easeOutQuad", "factor", "maxSpeed", "speed", "load", "data", "undefined"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/interaction-external-attract/browser/Options/Classes/Attract.js"], "sourcesContent": ["import { EasingType } from \"@tsparticles/engine\";\nexport class Attract {\n    constructor() {\n        this.distance = 200;\n        this.duration = 0.4;\n        this.easing = EasingType.easeOutQuad;\n        this.factor = 1;\n        this.maxSpeed = 50;\n        this.speed = 1;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.distance !== undefined) {\n            this.distance = data.distance;\n        }\n        if (data.duration !== undefined) {\n            this.duration = data.duration;\n        }\n        if (data.easing !== undefined) {\n            this.easing = data.easing;\n        }\n        if (data.factor !== undefined) {\n            this.factor = data.factor;\n        }\n        if (data.maxSpeed !== undefined) {\n            this.maxSpeed = data.maxSpeed;\n        }\n        if (data.speed !== undefined) {\n            this.speed = data.speed;\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,qBAAqB;AAChD,OAAO,MAAMC,OAAO,CAAC;EACjBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,QAAQ,GAAG,GAAG;IACnB,IAAI,CAACC,QAAQ,GAAG,GAAG;IACnB,IAAI,CAACC,MAAM,GAAGL,UAAU,CAACM,WAAW;IACpC,IAAI,CAACC,MAAM,GAAG,CAAC;IACf,IAAI,CAACC,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACC,KAAK,GAAG,CAAC;EAClB;EACAC,IAAIA,CAACC,IAAI,EAAE;IACP,IAAI,CAACA,IAAI,EAAE;MACP;IACJ;IACA,IAAIA,IAAI,CAACR,QAAQ,KAAKS,SAAS,EAAE;MAC7B,IAAI,CAACT,QAAQ,GAAGQ,IAAI,CAACR,QAAQ;IACjC;IACA,IAAIQ,IAAI,CAACP,QAAQ,KAAKQ,SAAS,EAAE;MAC7B,IAAI,CAACR,QAAQ,GAAGO,IAAI,CAACP,QAAQ;IACjC;IACA,IAAIO,IAAI,CAACN,MAAM,KAAKO,SAAS,EAAE;MAC3B,IAAI,CAACP,MAAM,GAAGM,IAAI,CAACN,MAAM;IAC7B;IACA,IAAIM,IAAI,CAACJ,MAAM,KAAKK,SAAS,EAAE;MAC3B,IAAI,CAACL,MAAM,GAAGI,IAAI,CAACJ,MAAM;IAC7B;IACA,IAAII,IAAI,CAACH,QAAQ,KAAKI,SAAS,EAAE;MAC7B,IAAI,CAACJ,QAAQ,GAAGG,IAAI,CAACH,QAAQ;IACjC;IACA,IAAIG,IAAI,CAACF,KAAK,KAAKG,SAAS,EAAE;MAC1B,IAAI,CAACH,KAAK,GAAGE,IAAI,CAACF,KAAK;IAC3B;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
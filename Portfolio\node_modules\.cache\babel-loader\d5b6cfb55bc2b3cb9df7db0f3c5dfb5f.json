{"ast": null, "code": "export class ExternalInteractorBase {\n  constructor(container) {\n    this.container = container;\n    this.type = 0;\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Core/Utils/ExternalInteractorBase.js"], "names": ["ExternalInteractorBase", "constructor", "container", "type"], "mappings": "AAAA,OAAO,MAAMA,sBAAN,CAA6B;AAChCC,EAAAA,WAAW,CAACC,SAAD,EAAY;AACnB,SAAKA,SAAL,GAAiBA,SAAjB;AACA,SAAKC,IAAL,GAAY,CAAZ;AACH;;AAJ+B", "sourcesContent": ["export class ExternalInteractorBase {\n    constructor(container) {\n        this.container = container;\n        this.type = 0;\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
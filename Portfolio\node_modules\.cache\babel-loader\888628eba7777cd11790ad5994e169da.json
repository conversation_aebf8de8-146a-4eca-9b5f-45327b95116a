{"ast": null, "code": "import { Constants, ExternalInteractorBase } from \"../../../Core\";\nimport { getDistance, getLinkColor, getLinkRandomColor, isInArray } from \"../../../Utils\";\nexport class <PERSON>rabber extends ExternalInteractorBase {\n  constructor(container) {\n    super(container);\n  }\n\n  isEnabled() {\n    const container = this.container,\n          mouse = container.interactivity.mouse,\n          events = container.actualOptions.interactivity.events;\n    return events.onHover.enable && !!mouse.position && isInArray(\"grab\", events.onHover.mode);\n  }\n\n  reset() {}\n\n  async interact() {\n    var _a;\n\n    const container = this.container,\n          options = container.actualOptions,\n          interactivity = options.interactivity;\n\n    if (interactivity.events.onHover.enable && container.interactivity.status === Constants.mouseMoveEvent) {\n      const mousePos = container.interactivity.mouse.position;\n\n      if (!mousePos) {\n        return;\n      }\n\n      const distance = container.retina.grabModeDistance,\n            query = container.particles.quadTree.queryCircle(mousePos, distance);\n\n      for (const particle of query) {\n        const pos = particle.getPosition(),\n              pointDistance = getDistance(pos, mousePos);\n\n        if (pointDistance <= distance) {\n          const grabLineOptions = interactivity.modes.grab.links,\n                lineOpacity = grabLineOptions.opacity,\n                opacityLine = lineOpacity - pointDistance * lineOpacity / distance;\n\n          if (opacityLine <= 0) {\n            continue;\n          }\n\n          const optColor = (_a = grabLineOptions.color) !== null && _a !== void 0 ? _a : particle.options.links.color;\n\n          if (!container.particles.grabLineColor) {\n            const linksOptions = options.interactivity.modes.grab.links;\n            container.particles.grabLineColor = getLinkRandomColor(optColor, linksOptions.blink, linksOptions.consent);\n          }\n\n          const colorLine = getLinkColor(particle, undefined, container.particles.grabLineColor);\n\n          if (!colorLine) {\n            return;\n          }\n\n          container.canvas.drawGrabLine(particle, colorLine, opacityLine, mousePos);\n        }\n      }\n    }\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Interactions/External/Grab/Grabber.js"], "names": ["Constants", "ExternalInteractorBase", "getDistance", "getLinkColor", "getLinkRandomColor", "isInArray", "<PERSON><PERSON><PERSON>", "constructor", "container", "isEnabled", "mouse", "interactivity", "events", "actualOptions", "onHover", "enable", "position", "mode", "reset", "interact", "_a", "options", "status", "mouseMoveEvent", "mousePos", "distance", "retina", "grabModeDistance", "query", "particles", "quadTree", "queryCircle", "particle", "pos", "getPosition", "pointDistance", "grabLineOptions", "modes", "grab", "links", "lineOpacity", "opacity", "opacityLine", "optColor", "color", "grabLineColor", "linksOptions", "blink", "consent", "colorLine", "undefined", "canvas", "drawGrabLine"], "mappings": "AAAA,SAASA,SAAT,EAAoBC,sBAApB,QAAkD,eAAlD;AACA,SAASC,WAAT,EAAsBC,YAAtB,EAAoCC,kBAApC,EAAwDC,SAAxD,QAAyE,gBAAzE;AACA,OAAO,MAAMC,OAAN,SAAsBL,sBAAtB,CAA6C;AAChDM,EAAAA,WAAW,CAACC,SAAD,EAAY;AACnB,UAAMA,SAAN;AACH;;AACDC,EAAAA,SAAS,GAAG;AACR,UAAMD,SAAS,GAAG,KAAKA,SAAvB;AAAA,UAAkCE,KAAK,GAAGF,SAAS,CAACG,aAAV,CAAwBD,KAAlE;AAAA,UAAyEE,MAAM,GAAGJ,SAAS,CAACK,aAAV,CAAwBF,aAAxB,CAAsCC,MAAxH;AACA,WAAOA,MAAM,CAACE,OAAP,CAAeC,MAAf,IAAyB,CAAC,CAACL,KAAK,CAACM,QAAjC,IAA6CX,SAAS,CAAC,MAAD,EAASO,MAAM,CAACE,OAAP,CAAeG,IAAxB,CAA7D;AACH;;AACDC,EAAAA,KAAK,GAAG,CACP;;AACa,QAARC,QAAQ,GAAG;AACb,QAAIC,EAAJ;;AACA,UAAMZ,SAAS,GAAG,KAAKA,SAAvB;AAAA,UAAkCa,OAAO,GAAGb,SAAS,CAACK,aAAtD;AAAA,UAAqEF,aAAa,GAAGU,OAAO,CAACV,aAA7F;;AACA,QAAIA,aAAa,CAACC,MAAd,CAAqBE,OAArB,CAA6BC,MAA7B,IAAuCP,SAAS,CAACG,aAAV,CAAwBW,MAAxB,KAAmCtB,SAAS,CAACuB,cAAxF,EAAwG;AACpG,YAAMC,QAAQ,GAAGhB,SAAS,CAACG,aAAV,CAAwBD,KAAxB,CAA8BM,QAA/C;;AACA,UAAI,CAACQ,QAAL,EAAe;AACX;AACH;;AACD,YAAMC,QAAQ,GAAGjB,SAAS,CAACkB,MAAV,CAAiBC,gBAAlC;AAAA,YAAoDC,KAAK,GAAGpB,SAAS,CAACqB,SAAV,CAAoBC,QAApB,CAA6BC,WAA7B,CAAyCP,QAAzC,EAAmDC,QAAnD,CAA5D;;AACA,WAAK,MAAMO,QAAX,IAAuBJ,KAAvB,EAA8B;AAC1B,cAAMK,GAAG,GAAGD,QAAQ,CAACE,WAAT,EAAZ;AAAA,cAAoCC,aAAa,GAAGjC,WAAW,CAAC+B,GAAD,EAAMT,QAAN,CAA/D;;AACA,YAAIW,aAAa,IAAIV,QAArB,EAA+B;AAC3B,gBAAMW,eAAe,GAAGzB,aAAa,CAAC0B,KAAd,CAAoBC,IAApB,CAAyBC,KAAjD;AAAA,gBAAwDC,WAAW,GAAGJ,eAAe,CAACK,OAAtF;AAAA,gBAA+FC,WAAW,GAAGF,WAAW,GAAIL,aAAa,GAAGK,WAAjB,GAAgCf,QAA3J;;AACA,cAAIiB,WAAW,IAAI,CAAnB,EAAsB;AAClB;AACH;;AACD,gBAAMC,QAAQ,GAAG,CAACvB,EAAE,GAAGgB,eAAe,CAACQ,KAAtB,MAAiC,IAAjC,IAAyCxB,EAAE,KAAK,KAAK,CAArD,GAAyDA,EAAzD,GAA8DY,QAAQ,CAACX,OAAT,CAAiBkB,KAAjB,CAAuBK,KAAtG;;AACA,cAAI,CAACpC,SAAS,CAACqB,SAAV,CAAoBgB,aAAzB,EAAwC;AACpC,kBAAMC,YAAY,GAAGzB,OAAO,CAACV,aAAR,CAAsB0B,KAAtB,CAA4BC,IAA5B,CAAiCC,KAAtD;AACA/B,YAAAA,SAAS,CAACqB,SAAV,CAAoBgB,aAApB,GAAoCzC,kBAAkB,CAACuC,QAAD,EAAWG,YAAY,CAACC,KAAxB,EAA+BD,YAAY,CAACE,OAA5C,CAAtD;AACH;;AACD,gBAAMC,SAAS,GAAG9C,YAAY,CAAC6B,QAAD,EAAWkB,SAAX,EAAsB1C,SAAS,CAACqB,SAAV,CAAoBgB,aAA1C,CAA9B;;AACA,cAAI,CAACI,SAAL,EAAgB;AACZ;AACH;;AACDzC,UAAAA,SAAS,CAAC2C,MAAV,CAAiBC,YAAjB,CAA8BpB,QAA9B,EAAwCiB,SAAxC,EAAmDP,WAAnD,EAAgElB,QAAhE;AACH;AACJ;AACJ;AACJ;;AAvC+C", "sourcesContent": ["import { Constants, ExternalInteractorBase } from \"../../../Core\";\nimport { getDistance, getLinkColor, getLinkRandomColor, isInArray } from \"../../../Utils\";\nexport class <PERSON>rabber extends ExternalInteractorBase {\n    constructor(container) {\n        super(container);\n    }\n    isEnabled() {\n        const container = this.container, mouse = container.interactivity.mouse, events = container.actualOptions.interactivity.events;\n        return events.onHover.enable && !!mouse.position && isInArray(\"grab\", events.onHover.mode);\n    }\n    reset() {\n    }\n    async interact() {\n        var _a;\n        const container = this.container, options = container.actualOptions, interactivity = options.interactivity;\n        if (interactivity.events.onHover.enable && container.interactivity.status === Constants.mouseMoveEvent) {\n            const mousePos = container.interactivity.mouse.position;\n            if (!mousePos) {\n                return;\n            }\n            const distance = container.retina.grabModeDistance, query = container.particles.quadTree.queryCircle(mousePos, distance);\n            for (const particle of query) {\n                const pos = particle.getPosition(), pointDistance = getDistance(pos, mousePos);\n                if (pointDistance <= distance) {\n                    const grabLineOptions = interactivity.modes.grab.links, lineOpacity = grabLineOptions.opacity, opacityLine = lineOpacity - (pointDistance * lineOpacity) / distance;\n                    if (opacityLine <= 0) {\n                        continue;\n                    }\n                    const optColor = (_a = grabLineOptions.color) !== null && _a !== void 0 ? _a : particle.options.links.color;\n                    if (!container.particles.grabLineColor) {\n                        const linksOptions = options.interactivity.modes.grab.links;\n                        container.particles.grabLineColor = getLinkRandomColor(optColor, linksOptions.blink, linksOptions.consent);\n                    }\n                    const colorLine = getLinkColor(particle, undefined, container.particles.grabLineColor);\n                    if (!colorLine) {\n                        return;\n                    }\n                    container.canvas.drawGrabLine(particle, colorLine, opacityLine, mousePos);\n                }\n            }\n        }\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
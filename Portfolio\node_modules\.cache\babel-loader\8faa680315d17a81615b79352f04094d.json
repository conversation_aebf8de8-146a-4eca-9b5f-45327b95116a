{"ast": null, "code": "import { setRangeValue } from \"../../../../Utils\";\nexport class MoveGravity {\n  constructor() {\n    this.acceleration = 9.81;\n    this.enable = false;\n    this.inverse = false;\n    this.maxSpeed = 50;\n  }\n\n  load(data) {\n    if (!data) {\n      return;\n    }\n\n    if (data.acceleration !== undefined) {\n      this.acceleration = setRangeValue(data.acceleration);\n    }\n\n    if (data.enable !== undefined) {\n      this.enable = data.enable;\n    }\n\n    if (data.inverse !== undefined) {\n      this.inverse = data.inverse;\n    }\n\n    if (data.maxSpeed !== undefined) {\n      this.maxSpeed = setRangeValue(data.maxSpeed);\n    }\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Options/Classes/Particles/Move/MoveGravity.js"], "names": ["setRangeValue", "MoveGravity", "constructor", "acceleration", "enable", "inverse", "maxSpeed", "load", "data", "undefined"], "mappings": "AAAA,SAASA,aAAT,QAA8B,mBAA9B;AACA,OAAO,MAAMC,WAAN,CAAkB;AACrBC,EAAAA,WAAW,GAAG;AACV,SAAKC,YAAL,GAAoB,IAApB;AACA,SAAKC,MAAL,GAAc,KAAd;AACA,SAAKC,OAAL,GAAe,KAAf;AACA,SAAKC,QAAL,GAAgB,EAAhB;AACH;;AACDC,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,QAAI,CAACA,IAAL,EAAW;AACP;AACH;;AACD,QAAIA,IAAI,CAACL,YAAL,KAAsBM,SAA1B,EAAqC;AACjC,WAAKN,YAAL,GAAoBH,aAAa,CAACQ,IAAI,CAACL,YAAN,CAAjC;AACH;;AACD,QAAIK,IAAI,CAACJ,MAAL,KAAgBK,SAApB,EAA+B;AAC3B,WAAKL,MAAL,GAAcI,IAAI,CAACJ,MAAnB;AACH;;AACD,QAAII,IAAI,CAACH,OAAL,KAAiBI,SAArB,EAAgC;AAC5B,WAAKJ,OAAL,GAAeG,IAAI,CAACH,OAApB;AACH;;AACD,QAAIG,IAAI,CAACF,QAAL,KAAkBG,SAAtB,EAAiC;AAC7B,WAAKH,QAAL,GAAgBN,aAAa,CAACQ,IAAI,CAACF,QAAN,CAA7B;AACH;AACJ;;AAvBoB", "sourcesContent": ["import { setRangeValue } from \"../../../../Utils\";\nexport class MoveGravity {\n    constructor() {\n        this.acceleration = 9.81;\n        this.enable = false;\n        this.inverse = false;\n        this.maxSpeed = 50;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.acceleration !== undefined) {\n            this.acceleration = setRangeValue(data.acceleration);\n        }\n        if (data.enable !== undefined) {\n            this.enable = data.enable;\n        }\n        if (data.inverse !== undefined) {\n            this.inverse = data.inverse;\n        }\n        if (data.maxSpeed !== undefined) {\n            this.maxSpeed = setRangeValue(data.maxSpeed);\n        }\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
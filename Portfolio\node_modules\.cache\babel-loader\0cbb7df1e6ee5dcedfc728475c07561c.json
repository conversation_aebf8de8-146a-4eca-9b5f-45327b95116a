{"ast": null, "code": "import { PolygonDrawer } from \"./PolygonDrawer\";\nimport { TriangleDrawer } from \"./TriangleDrawer\";\nexport async function loadGenericPolygonShape(engine) {\n  await engine.addShape(\"polygon\", new PolygonDrawer());\n}\nexport async function loadTriangleShape(engine) {\n  await engine.addShape(\"triangle\", new TriangleDrawer());\n}\nexport async function loadPolygonShape(engine) {\n  await loadGenericPolygonShape(engine);\n  await loadTriangleShape(engine);\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Shapes/Polygon/index.js"], "names": ["PolygonDrawer", "TriangleDrawer", "loadGenericPolygonShape", "engine", "addShape", "loadTriangleShape", "loadPolygonShape"], "mappings": "AAAA,SAASA,aAAT,QAA8B,iBAA9B;AACA,SAASC,cAAT,QAA+B,kBAA/B;AACA,OAAO,eAAeC,uBAAf,CAAuCC,MAAvC,EAA+C;AAClD,QAAMA,MAAM,CAACC,QAAP,CAAgB,SAAhB,EAA2B,IAAIJ,aAAJ,EAA3B,CAAN;AACH;AACD,OAAO,eAAeK,iBAAf,CAAiCF,MAAjC,EAAyC;AAC5C,QAAMA,MAAM,CAACC,QAAP,CAAgB,UAAhB,EAA4B,IAAIH,cAAJ,EAA5B,CAAN;AACH;AACD,OAAO,eAAeK,gBAAf,CAAgCH,MAAhC,EAAwC;AAC3C,QAAMD,uBAAuB,CAACC,MAAD,CAA7B;AACA,QAAME,iBAAiB,CAACF,MAAD,CAAvB;AACH", "sourcesContent": ["import { PolygonDrawer } from \"./PolygonDrawer\";\nimport { TriangleDrawer } from \"./TriangleDrawer\";\nexport async function loadGenericPolygonShape(engine) {\n    await engine.addShape(\"polygon\", new PolygonDrawer());\n}\nexport async function loadTriangleShape(engine) {\n    await engine.addShape(\"triangle\", new TriangleDrawer());\n}\nexport async function loadPolygonShape(engine) {\n    await loadGenericPolygonShape(engine);\n    await loadTriangleShape(engine);\n}\n"]}, "metadata": {}, "sourceType": "module"}
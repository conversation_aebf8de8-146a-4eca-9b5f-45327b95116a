{"ast": null, "code": "import { OptionsColor } from \"../../OptionsColor\";\nexport class LinksTriangle {\n  constructor() {\n    this.enable = false;\n    this.frequency = 1;\n  }\n\n  load(data) {\n    if (data === undefined) {\n      return;\n    }\n\n    if (data.color !== undefined) {\n      this.color = OptionsColor.create(this.color, data.color);\n    }\n\n    if (data.enable !== undefined) {\n      this.enable = data.enable;\n    }\n\n    if (data.frequency !== undefined) {\n      this.frequency = data.frequency;\n    }\n\n    if (data.opacity !== undefined) {\n      this.opacity = data.opacity;\n    }\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Options/Classes/Particles/Links/LinksTriangle.js"], "names": ["OptionsColor", "LinksTriangle", "constructor", "enable", "frequency", "load", "data", "undefined", "color", "create", "opacity"], "mappings": "AAAA,SAASA,YAAT,QAA6B,oBAA7B;AACA,OAAO,MAAMC,aAAN,CAAoB;AACvBC,EAAAA,WAAW,GAAG;AACV,SAAKC,MAAL,GAAc,KAAd;AACA,SAAKC,SAAL,GAAiB,CAAjB;AACH;;AACDC,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,QAAIA,IAAI,KAAKC,SAAb,EAAwB;AACpB;AACH;;AACD,QAAID,IAAI,CAACE,KAAL,KAAeD,SAAnB,EAA8B;AAC1B,WAAKC,KAAL,GAAaR,YAAY,CAACS,MAAb,CAAoB,KAAKD,KAAzB,EAAgCF,IAAI,CAACE,KAArC,CAAb;AACH;;AACD,QAAIF,IAAI,CAACH,MAAL,KAAgBI,SAApB,EAA+B;AAC3B,WAAKJ,MAAL,GAAcG,IAAI,CAACH,MAAnB;AACH;;AACD,QAAIG,IAAI,CAACF,SAAL,KAAmBG,SAAvB,EAAkC;AAC9B,WAAKH,SAAL,GAAiBE,IAAI,CAACF,SAAtB;AACH;;AACD,QAAIE,IAAI,CAACI,OAAL,KAAiBH,SAArB,EAAgC;AAC5B,WAAKG,OAAL,GAAeJ,IAAI,CAACI,OAApB;AACH;AACJ;;AArBsB", "sourcesContent": ["import { OptionsColor } from \"../../OptionsColor\";\nexport class LinksTriangle {\n    constructor() {\n        this.enable = false;\n        this.frequency = 1;\n    }\n    load(data) {\n        if (data === undefined) {\n            return;\n        }\n        if (data.color !== undefined) {\n            this.color = OptionsColor.create(this.color, data.color);\n        }\n        if (data.enable !== undefined) {\n            this.enable = data.enable;\n        }\n        if (data.frequency !== undefined) {\n            this.frequency = data.frequency;\n        }\n        if (data.opacity !== undefined) {\n            this.opacity = data.opacity;\n        }\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
{"ast": null, "code": "import { ThemeMode } from \"../../../Enums/Modes/ThemeMode.js\";\nexport class ThemeDefault {\n  constructor() {\n    this.auto = false;\n    this.mode = ThemeMode.any;\n    this.value = false;\n  }\n  load(data) {\n    if (!data) {\n      return;\n    }\n    if (data.auto !== undefined) {\n      this.auto = data.auto;\n    }\n    if (data.mode !== undefined) {\n      this.mode = data.mode;\n    }\n    if (data.value !== undefined) {\n      this.value = data.value;\n    }\n  }\n}", "map": {"version": 3, "names": ["ThemeMode", "ThemeDefault", "constructor", "auto", "mode", "any", "value", "load", "data", "undefined"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/engine/browser/Options/Classes/Theme/ThemeDefault.js"], "sourcesContent": ["import { ThemeMode } from \"../../../Enums/Modes/ThemeMode.js\";\nexport class ThemeDefault {\n    constructor() {\n        this.auto = false;\n        this.mode = ThemeMode.any;\n        this.value = false;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.auto !== undefined) {\n            this.auto = data.auto;\n        }\n        if (data.mode !== undefined) {\n            this.mode = data.mode;\n        }\n        if (data.value !== undefined) {\n            this.value = data.value;\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,mCAAmC;AAC7D,OAAO,MAAMC,YAAY,CAAC;EACtBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,IAAI,GAAG,KAAK;IACjB,IAAI,CAACC,IAAI,GAAGJ,SAAS,CAACK,GAAG;IACzB,IAAI,CAACC,KAAK,GAAG,KAAK;EACtB;EACAC,IAAIA,CAACC,IAAI,EAAE;IACP,IAAI,CAACA,IAAI,EAAE;MACP;IACJ;IACA,IAAIA,IAAI,CAACL,IAAI,KAAKM,SAAS,EAAE;MACzB,IAAI,CAACN,IAAI,GAAGK,IAAI,CAACL,IAAI;IACzB;IACA,IAAIK,IAAI,CAACJ,IAAI,KAAKK,SAAS,EAAE;MACzB,IAAI,CAACL,IAAI,GAAGI,IAAI,CAACJ,IAAI;IACzB;IACA,IAAII,IAAI,CAACF,KAAK,KAAKG,SAAS,EAAE;MAC1B,IAAI,CAACH,KAAK,GAAGE,IAAI,CAACF,KAAK;IAC3B;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
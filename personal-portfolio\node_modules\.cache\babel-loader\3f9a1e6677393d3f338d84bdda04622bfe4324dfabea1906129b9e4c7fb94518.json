{"ast": null, "code": "import { OptionsColor } from \"@tsparticles/engine\";\nimport { LinksShadow } from \"./LinksShadow.js\";\nimport { LinksTriangle } from \"./LinksTriangle.js\";\nexport class Links {\n  constructor() {\n    this.blink = false;\n    this.color = new OptionsColor();\n    this.color.value = \"#fff\";\n    this.consent = false;\n    this.distance = 100;\n    this.enable = false;\n    this.frequency = 1;\n    this.opacity = 1;\n    this.shadow = new LinksShadow();\n    this.triangles = new LinksTriangle();\n    this.width = 1;\n    this.warp = false;\n  }\n  load(data) {\n    if (!data) {\n      return;\n    }\n    if (data.id !== undefined) {\n      this.id = data.id;\n    }\n    if (data.blink !== undefined) {\n      this.blink = data.blink;\n    }\n    this.color = OptionsColor.create(this.color, data.color);\n    if (data.consent !== undefined) {\n      this.consent = data.consent;\n    }\n    if (data.distance !== undefined) {\n      this.distance = data.distance;\n    }\n    if (data.enable !== undefined) {\n      this.enable = data.enable;\n    }\n    if (data.frequency !== undefined) {\n      this.frequency = data.frequency;\n    }\n    if (data.opacity !== undefined) {\n      this.opacity = data.opacity;\n    }\n    this.shadow.load(data.shadow);\n    this.triangles.load(data.triangles);\n    if (data.width !== undefined) {\n      this.width = data.width;\n    }\n    if (data.warp !== undefined) {\n      this.warp = data.warp;\n    }\n  }\n}", "map": {"version": 3, "names": ["OptionsColor", "LinksShadow", "LinksTriangle", "Links", "constructor", "blink", "color", "value", "consent", "distance", "enable", "frequency", "opacity", "shadow", "triangles", "width", "warp", "load", "data", "id", "undefined", "create"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/interaction-particles-links/browser/Options/Classes/Links.js"], "sourcesContent": ["import { OptionsColor } from \"@tsparticles/engine\";\nimport { LinksShadow } from \"./LinksShadow.js\";\nimport { LinksTriangle } from \"./LinksTriangle.js\";\nexport class Links {\n    constructor() {\n        this.blink = false;\n        this.color = new OptionsColor();\n        this.color.value = \"#fff\";\n        this.consent = false;\n        this.distance = 100;\n        this.enable = false;\n        this.frequency = 1;\n        this.opacity = 1;\n        this.shadow = new LinksShadow();\n        this.triangles = new LinksTriangle();\n        this.width = 1;\n        this.warp = false;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.id !== undefined) {\n            this.id = data.id;\n        }\n        if (data.blink !== undefined) {\n            this.blink = data.blink;\n        }\n        this.color = OptionsColor.create(this.color, data.color);\n        if (data.consent !== undefined) {\n            this.consent = data.consent;\n        }\n        if (data.distance !== undefined) {\n            this.distance = data.distance;\n        }\n        if (data.enable !== undefined) {\n            this.enable = data.enable;\n        }\n        if (data.frequency !== undefined) {\n            this.frequency = data.frequency;\n        }\n        if (data.opacity !== undefined) {\n            this.opacity = data.opacity;\n        }\n        this.shadow.load(data.shadow);\n        this.triangles.load(data.triangles);\n        if (data.width !== undefined) {\n            this.width = data.width;\n        }\n        if (data.warp !== undefined) {\n            this.warp = data.warp;\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,qBAAqB;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,aAAa,QAAQ,oBAAoB;AAClD,OAAO,MAAMC,KAAK,CAAC;EACfC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,KAAK,GAAG,KAAK;IAClB,IAAI,CAACC,KAAK,GAAG,IAAIN,YAAY,CAAC,CAAC;IAC/B,IAAI,CAACM,KAAK,CAACC,KAAK,GAAG,MAAM;IACzB,IAAI,CAACC,OAAO,GAAG,KAAK;IACpB,IAAI,CAACC,QAAQ,GAAG,GAAG;IACnB,IAAI,CAACC,MAAM,GAAG,KAAK;IACnB,IAAI,CAACC,SAAS,GAAG,CAAC;IAClB,IAAI,CAACC,OAAO,GAAG,CAAC;IAChB,IAAI,CAACC,MAAM,GAAG,IAAIZ,WAAW,CAAC,CAAC;IAC/B,IAAI,CAACa,SAAS,GAAG,IAAIZ,aAAa,CAAC,CAAC;IACpC,IAAI,CAACa,KAAK,GAAG,CAAC;IACd,IAAI,CAACC,IAAI,GAAG,KAAK;EACrB;EACAC,IAAIA,CAACC,IAAI,EAAE;IACP,IAAI,CAACA,IAAI,EAAE;MACP;IACJ;IACA,IAAIA,IAAI,CAACC,EAAE,KAAKC,SAAS,EAAE;MACvB,IAAI,CAACD,EAAE,GAAGD,IAAI,CAACC,EAAE;IACrB;IACA,IAAID,IAAI,CAACb,KAAK,KAAKe,SAAS,EAAE;MAC1B,IAAI,CAACf,KAAK,GAAGa,IAAI,CAACb,KAAK;IAC3B;IACA,IAAI,CAACC,KAAK,GAAGN,YAAY,CAACqB,MAAM,CAAC,IAAI,CAACf,KAAK,EAAEY,IAAI,CAACZ,KAAK,CAAC;IACxD,IAAIY,IAAI,CAACV,OAAO,KAAKY,SAAS,EAAE;MAC5B,IAAI,CAACZ,OAAO,GAAGU,IAAI,CAACV,OAAO;IAC/B;IACA,IAAIU,IAAI,CAACT,QAAQ,KAAKW,SAAS,EAAE;MAC7B,IAAI,CAACX,QAAQ,GAAGS,IAAI,CAACT,QAAQ;IACjC;IACA,IAAIS,IAAI,CAACR,MAAM,KAAKU,SAAS,EAAE;MAC3B,IAAI,CAACV,MAAM,GAAGQ,IAAI,CAACR,MAAM;IAC7B;IACA,IAAIQ,IAAI,CAACP,SAAS,KAAKS,SAAS,EAAE;MAC9B,IAAI,CAACT,SAAS,GAAGO,IAAI,CAACP,SAAS;IACnC;IACA,IAAIO,IAAI,CAACN,OAAO,KAAKQ,SAAS,EAAE;MAC5B,IAAI,CAACR,OAAO,GAAGM,IAAI,CAACN,OAAO;IAC/B;IACA,IAAI,CAACC,MAAM,CAACI,IAAI,CAACC,IAAI,CAACL,MAAM,CAAC;IAC7B,IAAI,CAACC,SAAS,CAACG,IAAI,CAACC,IAAI,CAACJ,SAAS,CAAC;IACnC,IAAII,IAAI,CAACH,KAAK,KAAKK,SAAS,EAAE;MAC1B,IAAI,CAACL,KAAK,GAAGG,IAAI,CAACH,KAAK;IAC3B;IACA,IAAIG,IAAI,CAACF,IAAI,KAAKI,SAAS,EAAE;MACzB,IAAI,CAACJ,IAAI,GAAGE,IAAI,CAACF,IAAI;IACzB;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
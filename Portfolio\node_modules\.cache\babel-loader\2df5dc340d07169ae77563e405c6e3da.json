{"ast": null, "code": "import { RepulseBase } from \"./RepulseBase\";\nimport { RepulseDiv } from \"./RepulseDiv\";\nexport class Repulse extends RepulseBase {\n  load(data) {\n    super.load(data);\n\n    if ((data === null || data === void 0 ? void 0 : data.divs) === undefined) {\n      return;\n    }\n\n    if (data.divs instanceof Array) {\n      this.divs = data.divs.map(s => {\n        const tmp = new RepulseDiv();\n        tmp.load(s);\n        return tmp;\n      });\n    } else {\n      if (this.divs instanceof Array || !this.divs) {\n        this.divs = new RepulseDiv();\n      }\n\n      this.divs.load(data.divs);\n    }\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Options/Classes/Interactivity/Modes/Repulse.js"], "names": ["RepulseBase", "RepulseDiv", "Repulse", "load", "data", "divs", "undefined", "Array", "map", "s", "tmp"], "mappings": "AAAA,SAASA,WAAT,QAA4B,eAA5B;AACA,SAASC,UAAT,QAA2B,cAA3B;AACA,OAAO,MAAMC,OAAN,SAAsBF,WAAtB,CAAkC;AACrCG,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,UAAMD,IAAN,CAAWC,IAAX;;AACA,QAAI,CAACA,IAAI,KAAK,IAAT,IAAiBA,IAAI,KAAK,KAAK,CAA/B,GAAmC,KAAK,CAAxC,GAA4CA,IAAI,CAACC,IAAlD,MAA4DC,SAAhE,EAA2E;AACvE;AACH;;AACD,QAAIF,IAAI,CAACC,IAAL,YAAqBE,KAAzB,EAAgC;AAC5B,WAAKF,IAAL,GAAYD,IAAI,CAACC,IAAL,CAAUG,GAAV,CAAeC,CAAD,IAAO;AAC7B,cAAMC,GAAG,GAAG,IAAIT,UAAJ,EAAZ;AACAS,QAAAA,GAAG,CAACP,IAAJ,CAASM,CAAT;AACA,eAAOC,GAAP;AACH,OAJW,CAAZ;AAKH,KAND,MAOK;AACD,UAAI,KAAKL,IAAL,YAAqBE,KAArB,IAA8B,CAAC,KAAKF,IAAxC,EAA8C;AAC1C,aAAKA,IAAL,GAAY,IAAIJ,UAAJ,EAAZ;AACH;;AACD,WAAKI,IAAL,CAAUF,IAAV,CAAeC,IAAI,CAACC,IAApB;AACH;AACJ;;AAnBoC", "sourcesContent": ["import { RepulseBase } from \"./RepulseBase\";\nimport { RepulseDiv } from \"./RepulseDiv\";\nexport class Repulse extends RepulseBase {\n    load(data) {\n        super.load(data);\n        if ((data === null || data === void 0 ? void 0 : data.divs) === undefined) {\n            return;\n        }\n        if (data.divs instanceof Array) {\n            this.divs = data.divs.map((s) => {\n                const tmp = new RepulseDiv();\n                tmp.load(s);\n                return tmp;\n            });\n        }\n        else {\n            if (this.divs instanceof Array || !this.divs) {\n                this.divs = new RepulseDiv();\n            }\n            this.divs.load(data.divs);\n        }\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
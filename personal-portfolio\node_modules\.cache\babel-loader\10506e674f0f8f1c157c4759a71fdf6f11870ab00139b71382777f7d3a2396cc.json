{"ast": null, "code": "export var InteractivityDetect;\n(function (InteractivityDetect) {\n  InteractivityDetect[\"canvas\"] = \"canvas\";\n  InteractivityDetect[\"parent\"] = \"parent\";\n  InteractivityDetect[\"window\"] = \"window\";\n})(InteractivityDetect || (InteractivityDetect = {}));", "map": {"version": 3, "names": ["InteractivityDetect"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/engine/browser/Enums/InteractivityDetect.js"], "sourcesContent": ["export var InteractivityDetect;\n(function (InteractivityDetect) {\n    InteractivityDetect[\"canvas\"] = \"canvas\";\n    InteractivityDetect[\"parent\"] = \"parent\";\n    InteractivityDetect[\"window\"] = \"window\";\n})(InteractivityDetect || (InteractivityDetect = {}));\n"], "mappings": "AAAA,OAAO,IAAIA,mBAAmB;AAC9B,CAAC,UAAUA,mBAAmB,EAAE;EAC5BA,mBAAmB,CAAC,QAAQ,CAAC,GAAG,QAAQ;EACxCA,mBAAmB,CAAC,QAAQ,CAAC,GAAG,QAAQ;EACxCA,mBAAmB,CAAC,QAAQ,CAAC,GAAG,QAAQ;AAC5C,CAAC,EAAEA,mBAAmB,KAAKA,mBAAmB,GAAG,CAAC,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
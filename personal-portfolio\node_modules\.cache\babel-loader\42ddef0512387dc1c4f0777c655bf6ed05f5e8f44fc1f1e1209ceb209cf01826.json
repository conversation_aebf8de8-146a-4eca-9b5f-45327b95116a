{"ast": null, "code": "import { drawLine } from \"./Utils.js\";\nconst sides = 1;\nexport class LineDrawer {\n  constructor() {\n    this.validTypes = [\"line\"];\n  }\n  draw(data) {\n    drawLine(data);\n  }\n  getSidesCount() {\n    return sides;\n  }\n}", "map": {"version": 3, "names": ["drawLine", "sides", "LineDrawer", "constructor", "validTypes", "draw", "data", "getSidesCount"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/shape-line/browser/LineDrawer.js"], "sourcesContent": ["import { drawLine } from \"./Utils.js\";\nconst sides = 1;\nexport class LineDrawer {\n    constructor() {\n        this.validTypes = [\"line\"];\n    }\n    draw(data) {\n        drawLine(data);\n    }\n    getSidesCount() {\n        return sides;\n    }\n}\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,YAAY;AACrC,MAAMC,KAAK,GAAG,CAAC;AACf,OAAO,MAAMC,UAAU,CAAC;EACpBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,UAAU,GAAG,CAAC,MAAM,CAAC;EAC9B;EACAC,IAAIA,CAACC,IAAI,EAAE;IACPN,QAAQ,CAACM,IAAI,CAAC;EAClB;EACAC,aAAaA,CAAA,EAAG;IACZ,OAAON,KAAK;EAChB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { setRangeValue } from \"@tsparticles/engine\";\nexport class WobbleSpeed {\n  constructor() {\n    this.angle = 50;\n    this.move = 10;\n  }\n  load(data) {\n    if (!data) {\n      return;\n    }\n    if (data.angle !== undefined) {\n      this.angle = setRangeValue(data.angle);\n    }\n    if (data.move !== undefined) {\n      this.move = setRangeValue(data.move);\n    }\n  }\n}", "map": {"version": 3, "names": ["setRangeValue", "WobbleSpeed", "constructor", "angle", "move", "load", "data", "undefined"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/updater-wobble/browser/Options/Classes/WobbleSpeed.js"], "sourcesContent": ["import { setRangeValue } from \"@tsparticles/engine\";\nexport class WobbleSpeed {\n    constructor() {\n        this.angle = 50;\n        this.move = 10;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.angle !== undefined) {\n            this.angle = setRangeValue(data.angle);\n        }\n        if (data.move !== undefined) {\n            this.move = setRangeValue(data.move);\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,qBAAqB;AACnD,OAAO,MAAMC,WAAW,CAAC;EACrBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,KAAK,GAAG,EAAE;IACf,IAAI,CAACC,IAAI,GAAG,EAAE;EAClB;EACAC,IAAIA,CAACC,IAAI,EAAE;IACP,IAAI,CAACA,IAAI,EAAE;MACP;IACJ;IACA,IAAIA,IAAI,CAACH,KAAK,KAAKI,SAAS,EAAE;MAC1B,IAAI,CAACJ,KAAK,GAAGH,aAAa,CAACM,IAAI,CAACH,KAAK,CAAC;IAC1C;IACA,IAAIG,IAAI,CAACF,IAAI,KAAKG,SAAS,EAAE;MACzB,IAAI,CAACH,IAAI,GAAGJ,aAAa,CAACM,IAAI,CAACF,IAAI,CAAC;IACxC;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
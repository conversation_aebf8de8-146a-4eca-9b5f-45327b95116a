{"ast": null, "code": "import toDate from \"../../toDate/index.js\";\nimport requiredArgs from \"../requiredArgs/index.js\";\nimport toInteger from \"../toInteger/index.js\"; // This function will be a part of public API when UTC function will be implemented.\n// See issue: https://github.com/date-fns/date-fns/issues/376\n\nexport default function startOfUTCWeek(dirtyDate, dirtyOptions) {\n  requiredArgs(1, arguments);\n  var options = dirtyOptions || {};\n  var locale = options.locale;\n  var localeWeekStartsOn = locale && locale.options && locale.options.weekStartsOn;\n  var defaultWeekStartsOn = localeWeekStartsOn == null ? 0 : toInteger(localeWeekStartsOn);\n  var weekStartsOn = options.weekStartsOn == null ? defaultWeekStartsOn : toInteger(options.weekStartsOn); // Test if weekStartsOn is between 0 and 6 _and_ is not NaN\n\n  if (!(weekStartsOn >= 0 && weekStartsOn <= 6)) {\n    throw new RangeError('weekStartsOn must be between 0 and 6 inclusively');\n  }\n\n  var date = toDate(dirtyDate);\n  var day = date.getUTCDay();\n  var diff = (day < weekStartsOn ? 7 : 0) + day - weekStartsOn;\n  date.setUTCDate(date.getUTCDate() - diff);\n  date.setUTCHours(0, 0, 0, 0);\n  return date;\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/date-fns/esm/_lib/startOfUTCWeek/index.js"], "names": ["toDate", "requiredArgs", "toInteger", "startOfUTCWeek", "dirtyDate", "dirtyOptions", "arguments", "options", "locale", "localeWeekStartsOn", "weekStartsOn", "defaultWeekStartsOn", "RangeError", "date", "day", "getUTCDay", "diff", "setUTCDate", "getUTCDate", "setUTCHours"], "mappings": "AAAA,OAAOA,MAAP,MAAmB,uBAAnB;AACA,OAAOC,YAAP,MAAyB,0BAAzB;AACA,OAAOC,SAAP,MAAsB,uBAAtB,C,CAA+C;AAC/C;;AAEA,eAAe,SAASC,cAAT,CAAwBC,SAAxB,EAAmCC,YAAnC,EAAiD;AAC9DJ,EAAAA,YAAY,CAAC,CAAD,EAAIK,SAAJ,CAAZ;AACA,MAAIC,OAAO,GAAGF,YAAY,IAAI,EAA9B;AACA,MAAIG,MAAM,GAAGD,OAAO,CAACC,MAArB;AACA,MAAIC,kBAAkB,GAAGD,MAAM,IAAIA,MAAM,CAACD,OAAjB,IAA4BC,MAAM,CAACD,OAAP,CAAeG,YAApE;AACA,MAAIC,mBAAmB,GAAGF,kBAAkB,IAAI,IAAtB,GAA6B,CAA7B,GAAiCP,SAAS,CAACO,kBAAD,CAApE;AACA,MAAIC,YAAY,GAAGH,OAAO,CAACG,YAAR,IAAwB,IAAxB,GAA+BC,mBAA/B,GAAqDT,SAAS,CAACK,OAAO,CAACG,YAAT,CAAjF,CAN8D,CAM2C;;AAEzG,MAAI,EAAEA,YAAY,IAAI,CAAhB,IAAqBA,YAAY,IAAI,CAAvC,CAAJ,EAA+C;AAC7C,UAAM,IAAIE,UAAJ,CAAe,kDAAf,CAAN;AACD;;AAED,MAAIC,IAAI,GAAGb,MAAM,CAACI,SAAD,CAAjB;AACA,MAAIU,GAAG,GAAGD,IAAI,CAACE,SAAL,EAAV;AACA,MAAIC,IAAI,GAAG,CAACF,GAAG,GAAGJ,YAAN,GAAqB,CAArB,GAAyB,CAA1B,IAA+BI,GAA/B,GAAqCJ,YAAhD;AACAG,EAAAA,IAAI,CAACI,UAAL,CAAgBJ,IAAI,CAACK,UAAL,KAAoBF,IAApC;AACAH,EAAAA,IAAI,CAACM,WAAL,CAAiB,CAAjB,EAAoB,CAApB,EAAuB,CAAvB,EAA0B,CAA1B;AACA,SAAON,IAAP;AACD", "sourcesContent": ["import toDate from \"../../toDate/index.js\";\nimport requiredArgs from \"../requiredArgs/index.js\";\nimport toInteger from \"../toInteger/index.js\"; // This function will be a part of public API when UTC function will be implemented.\n// See issue: https://github.com/date-fns/date-fns/issues/376\n\nexport default function startOfUTCWeek(dirtyDate, dirtyOptions) {\n  requiredArgs(1, arguments);\n  var options = dirtyOptions || {};\n  var locale = options.locale;\n  var localeWeekStartsOn = locale && locale.options && locale.options.weekStartsOn;\n  var defaultWeekStartsOn = localeWeekStartsOn == null ? 0 : toInteger(localeWeekStartsOn);\n  var weekStartsOn = options.weekStartsOn == null ? defaultWeekStartsOn : toInteger(options.weekStartsOn); // Test if weekStartsOn is between 0 and 6 _and_ is not NaN\n\n  if (!(weekStartsOn >= 0 && weekStartsOn <= 6)) {\n    throw new RangeError('weekStartsOn must be between 0 and 6 inclusively');\n  }\n\n  var date = toDate(dirtyDate);\n  var day = date.getUTCDay();\n  var diff = (day < weekStartsOn ? 7 : 0) + day - weekStartsOn;\n  date.setUTCDate(date.getUTCDate() - diff);\n  date.setUTCHours(0, 0, 0, 0);\n  return date;\n}"]}, "metadata": {}, "sourceType": "module"}
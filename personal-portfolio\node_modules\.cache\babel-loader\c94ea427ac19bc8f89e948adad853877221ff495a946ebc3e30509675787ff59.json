{"ast": null, "code": "import { setRangeValue } from \"@tsparticles/engine\";\nexport class DestroyBounds {\n  load(data) {\n    if (!data) {\n      return;\n    }\n    if (data.bottom !== undefined) {\n      this.bottom = setRangeValue(data.bottom);\n    }\n    if (data.left !== undefined) {\n      this.left = setRangeValue(data.left);\n    }\n    if (data.right !== undefined) {\n      this.right = setRangeValue(data.right);\n    }\n    if (data.top !== undefined) {\n      this.top = setRangeValue(data.top);\n    }\n  }\n}", "map": {"version": 3, "names": ["setRangeValue", "DestroyBounds", "load", "data", "bottom", "undefined", "left", "right", "top"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/updater-destroy/browser/Options/Classes/DestroyBounds.js"], "sourcesContent": ["import { setRangeValue } from \"@tsparticles/engine\";\nexport class DestroyBounds {\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.bottom !== undefined) {\n            this.bottom = setRangeValue(data.bottom);\n        }\n        if (data.left !== undefined) {\n            this.left = setRangeValue(data.left);\n        }\n        if (data.right !== undefined) {\n            this.right = setRangeValue(data.right);\n        }\n        if (data.top !== undefined) {\n            this.top = setRangeValue(data.top);\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,qBAAqB;AACnD,OAAO,MAAMC,aAAa,CAAC;EACvBC,IAAIA,CAACC,IAAI,EAAE;IACP,IAAI,CAACA,IAAI,EAAE;MACP;IACJ;IACA,IAAIA,IAAI,CAACC,MAAM,KAAKC,SAAS,EAAE;MAC3B,IAAI,CAACD,MAAM,GAAGJ,aAAa,CAACG,IAAI,CAACC,MAAM,CAAC;IAC5C;IACA,IAAID,IAAI,CAACG,IAAI,KAAKD,SAAS,EAAE;MACzB,IAAI,CAACC,IAAI,GAAGN,aAAa,CAACG,IAAI,CAACG,IAAI,CAAC;IACxC;IACA,IAAIH,IAAI,CAACI,KAAK,KAAKF,SAAS,EAAE;MAC1B,IAAI,CAACE,KAAK,GAAGP,aAAa,CAACG,IAAI,CAACI,KAAK,CAAC;IAC1C;IACA,IAAIJ,IAAI,CAACK,GAAG,KAAKH,SAAS,EAAE;MACxB,IAAI,CAACG,GAAG,GAAGR,aAAa,CAACG,IAAI,CAACK,GAAG,CAAC;IACtC;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
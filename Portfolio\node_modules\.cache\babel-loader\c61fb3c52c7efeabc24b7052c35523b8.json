{"ast": null, "code": "import { Circle, Constants, ExternalInteractorBase, Rectangle } from \"../../../Core\";\nimport { clamp, colorMix, colorToHsl, divMode, divModeExecute, getDistance, getRangeMax, isDivModeEnabled, isInArray, itemFromArray, rgbToHsl } from \"../../../Utils\";\n\nfunction calculateBubbleValue(particleValue, modeValue, optionsValue, ratio) {\n  if (modeValue >= optionsValue) {\n    const value = particleValue + (modeValue - optionsValue) * ratio;\n    return clamp(value, particleValue, modeValue);\n  } else if (modeValue < optionsValue) {\n    const value = particleValue - (optionsValue - modeValue) * ratio;\n    return clamp(value, modeValue, particleValue);\n  }\n}\n\nexport class Bubbler extends ExternalInteractorBase {\n  constructor(container) {\n    super(container);\n  }\n\n  isEnabled() {\n    const container = this.container,\n          options = container.actualOptions,\n          mouse = container.interactivity.mouse,\n          events = options.interactivity.events,\n          divs = events.onDiv,\n          divBubble = isDivModeEnabled(\"bubble\", divs);\n\n    if (!(divBubble || events.onHover.enable && mouse.position || events.onClick.enable && mouse.clickPosition)) {\n      return false;\n    }\n\n    const hoverMode = events.onHover.mode;\n    const clickMode = events.onClick.mode;\n    return isInArray(\"bubble\", hoverMode) || isInArray(\"bubble\", clickMode) || divBubble;\n  }\n\n  reset(particle, force) {\n    if (!(!particle.bubble.inRange || force)) {\n      return;\n    }\n\n    delete particle.bubble.div;\n    delete particle.bubble.opacity;\n    delete particle.bubble.radius;\n    delete particle.bubble.color;\n  }\n\n  async interact() {\n    const options = this.container.actualOptions,\n          events = options.interactivity.events,\n          onHover = events.onHover,\n          onClick = events.onClick,\n          hoverEnabled = onHover.enable,\n          hoverMode = onHover.mode,\n          clickEnabled = onClick.enable,\n          clickMode = onClick.mode,\n          divs = events.onDiv;\n\n    if (hoverEnabled && isInArray(\"bubble\", hoverMode)) {\n      this.hoverBubble();\n    } else if (clickEnabled && isInArray(\"bubble\", clickMode)) {\n      this.clickBubble();\n    } else {\n      divModeExecute(\"bubble\", divs, (selector, div) => this.singleSelectorHover(selector, div));\n    }\n  }\n\n  singleSelectorHover(selector, div) {\n    const container = this.container,\n          selectors = document.querySelectorAll(selector);\n\n    if (!selectors.length) {\n      return;\n    }\n\n    selectors.forEach(item => {\n      const elem = item,\n            pxRatio = container.retina.pixelRatio,\n            pos = {\n        x: (elem.offsetLeft + elem.offsetWidth / 2) * pxRatio,\n        y: (elem.offsetTop + elem.offsetHeight / 2) * pxRatio\n      },\n            repulseRadius = elem.offsetWidth / 2 * pxRatio,\n            area = div.type === \"circle\" ? new Circle(pos.x, pos.y, repulseRadius) : new Rectangle(elem.offsetLeft * pxRatio, elem.offsetTop * pxRatio, elem.offsetWidth * pxRatio, elem.offsetHeight * pxRatio),\n            query = container.particles.quadTree.query(area);\n\n      for (const particle of query) {\n        if (!area.contains(particle.getPosition())) {\n          continue;\n        }\n\n        particle.bubble.inRange = true;\n        const divs = container.actualOptions.interactivity.modes.bubble.divs;\n        const divBubble = divMode(divs, elem);\n\n        if (!particle.bubble.div || particle.bubble.div !== elem) {\n          this.reset(particle, true);\n          particle.bubble.div = elem;\n        }\n\n        this.hoverBubbleSize(particle, 1, divBubble);\n        this.hoverBubbleOpacity(particle, 1, divBubble);\n        this.hoverBubbleColor(particle, 1, divBubble);\n      }\n    });\n  }\n\n  process(particle, distMouse, timeSpent, data) {\n    const container = this.container,\n          bubbleParam = data.bubbleObj.optValue;\n\n    if (bubbleParam === undefined) {\n      return;\n    }\n\n    const options = container.actualOptions,\n          bubbleDuration = options.interactivity.modes.bubble.duration,\n          bubbleDistance = container.retina.bubbleModeDistance,\n          particlesParam = data.particlesObj.optValue,\n          pObjBubble = data.bubbleObj.value,\n          pObj = data.particlesObj.value || 0,\n          type = data.type;\n\n    if (bubbleParam === particlesParam) {\n      return;\n    }\n\n    if (!container.bubble.durationEnd) {\n      if (distMouse <= bubbleDistance) {\n        const obj = pObjBubble !== null && pObjBubble !== void 0 ? pObjBubble : pObj;\n\n        if (obj !== bubbleParam) {\n          const value = pObj - timeSpent * (pObj - bubbleParam) / bubbleDuration;\n\n          if (type === \"size\") {\n            particle.bubble.radius = value;\n          }\n\n          if (type === \"opacity\") {\n            particle.bubble.opacity = value;\n          }\n        }\n      } else {\n        if (type === \"size\") {\n          delete particle.bubble.radius;\n        }\n\n        if (type === \"opacity\") {\n          delete particle.bubble.opacity;\n        }\n      }\n    } else if (pObjBubble) {\n      if (type === \"size\") {\n        delete particle.bubble.radius;\n      }\n\n      if (type === \"opacity\") {\n        delete particle.bubble.opacity;\n      }\n    }\n  }\n\n  clickBubble() {\n    var _a, _b;\n\n    const container = this.container,\n          options = container.actualOptions,\n          mouseClickPos = container.interactivity.mouse.clickPosition;\n\n    if (!mouseClickPos) {\n      return;\n    }\n\n    const distance = container.retina.bubbleModeDistance,\n          query = container.particles.quadTree.queryCircle(mouseClickPos, distance);\n\n    for (const particle of query) {\n      if (!container.bubble.clicking) {\n        continue;\n      }\n\n      particle.bubble.inRange = !container.bubble.durationEnd;\n      const pos = particle.getPosition(),\n            distMouse = getDistance(pos, mouseClickPos),\n            timeSpent = (new Date().getTime() - (container.interactivity.mouse.clickTime || 0)) / 1000;\n\n      if (timeSpent > options.interactivity.modes.bubble.duration) {\n        container.bubble.durationEnd = true;\n      }\n\n      if (timeSpent > options.interactivity.modes.bubble.duration * 2) {\n        container.bubble.clicking = false;\n        container.bubble.durationEnd = false;\n      }\n\n      const sizeData = {\n        bubbleObj: {\n          optValue: container.retina.bubbleModeSize,\n          value: particle.bubble.radius\n        },\n        particlesObj: {\n          optValue: getRangeMax(particle.options.size.value) * container.retina.pixelRatio,\n          value: particle.size.value\n        },\n        type: \"size\"\n      };\n      this.process(particle, distMouse, timeSpent, sizeData);\n      const opacityData = {\n        bubbleObj: {\n          optValue: options.interactivity.modes.bubble.opacity,\n          value: particle.bubble.opacity\n        },\n        particlesObj: {\n          optValue: getRangeMax(particle.options.opacity.value),\n          value: (_b = (_a = particle.opacity) === null || _a === void 0 ? void 0 : _a.value) !== null && _b !== void 0 ? _b : 1\n        },\n        type: \"opacity\"\n      };\n      this.process(particle, distMouse, timeSpent, opacityData);\n\n      if (!container.bubble.durationEnd) {\n        if (distMouse <= container.retina.bubbleModeDistance) {\n          this.hoverBubbleColor(particle, distMouse);\n        } else {\n          delete particle.bubble.color;\n        }\n      } else {\n        delete particle.bubble.color;\n      }\n    }\n  }\n\n  hoverBubble() {\n    const container = this.container,\n          mousePos = container.interactivity.mouse.position;\n\n    if (mousePos === undefined) {\n      return;\n    }\n\n    const distance = container.retina.bubbleModeDistance,\n          query = container.particles.quadTree.queryCircle(mousePos, distance);\n\n    for (const particle of query) {\n      particle.bubble.inRange = true;\n      const pos = particle.getPosition(),\n            pointDistance = getDistance(pos, mousePos),\n            ratio = 1 - pointDistance / distance;\n\n      if (pointDistance <= distance) {\n        if (ratio >= 0 && container.interactivity.status === Constants.mouseMoveEvent) {\n          this.hoverBubbleSize(particle, ratio);\n          this.hoverBubbleOpacity(particle, ratio);\n          this.hoverBubbleColor(particle, ratio);\n        }\n      } else {\n        this.reset(particle);\n      }\n\n      if (container.interactivity.status === Constants.mouseLeaveEvent) {\n        this.reset(particle);\n      }\n    }\n  }\n\n  hoverBubbleSize(particle, ratio, divBubble) {\n    const container = this.container,\n          modeSize = (divBubble === null || divBubble === void 0 ? void 0 : divBubble.size) ? divBubble.size * container.retina.pixelRatio : container.retina.bubbleModeSize;\n\n    if (modeSize === undefined) {\n      return;\n    }\n\n    const optSize = getRangeMax(particle.options.size.value) * container.retina.pixelRatio;\n    const pSize = particle.size.value;\n    const size = calculateBubbleValue(pSize, modeSize, optSize, ratio);\n\n    if (size !== undefined) {\n      particle.bubble.radius = size;\n    }\n  }\n\n  hoverBubbleOpacity(particle, ratio, divBubble) {\n    var _a, _b, _c;\n\n    const container = this.container,\n          options = container.actualOptions,\n          modeOpacity = (_a = divBubble === null || divBubble === void 0 ? void 0 : divBubble.opacity) !== null && _a !== void 0 ? _a : options.interactivity.modes.bubble.opacity;\n\n    if (!modeOpacity) {\n      return;\n    }\n\n    const optOpacity = particle.options.opacity.value;\n    const pOpacity = (_c = (_b = particle.opacity) === null || _b === void 0 ? void 0 : _b.value) !== null && _c !== void 0 ? _c : 1;\n    const opacity = calculateBubbleValue(pOpacity, modeOpacity, getRangeMax(optOpacity), ratio);\n\n    if (opacity !== undefined) {\n      particle.bubble.opacity = opacity;\n    }\n  }\n\n  hoverBubbleColor(particle, ratio, divBubble) {\n    const options = this.container.actualOptions;\n    const bubbleOptions = divBubble !== null && divBubble !== void 0 ? divBubble : options.interactivity.modes.bubble;\n\n    if (!particle.bubble.finalColor) {\n      const modeColor = bubbleOptions.color;\n\n      if (!modeColor) {\n        return;\n      }\n\n      const bubbleColor = modeColor instanceof Array ? itemFromArray(modeColor) : modeColor;\n      particle.bubble.finalColor = colorToHsl(bubbleColor);\n    }\n\n    if (!particle.bubble.finalColor) {\n      return;\n    }\n\n    if (bubbleOptions.mix) {\n      particle.bubble.color = undefined;\n      const pColor = particle.getFillColor();\n      particle.bubble.color = pColor ? rgbToHsl(colorMix(pColor, particle.bubble.finalColor, 1 - ratio, ratio)) : particle.bubble.finalColor;\n    } else {\n      particle.bubble.color = particle.bubble.finalColor;\n    }\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Interactions/External/Bubble/Bubbler.js"], "names": ["Circle", "Constants", "ExternalInteractorBase", "Rectangle", "clamp", "colorMix", "colorToHsl", "divMode", "divModeExecute", "getDistance", "getRangeMax", "isDivModeEnabled", "isInArray", "itemFromArray", "rgbToHsl", "calculateBubbleValue", "particleValue", "modeValue", "optionsValue", "ratio", "value", "Bubbler", "constructor", "container", "isEnabled", "options", "actualOptions", "mouse", "interactivity", "events", "divs", "onDiv", "divBubble", "onHover", "enable", "position", "onClick", "clickPosition", "hoverMode", "mode", "clickMode", "reset", "particle", "force", "bubble", "inRange", "div", "opacity", "radius", "color", "interact", "hoverEnabled", "clickEnabled", "hoverBubble", "clickBubble", "selector", "singleSelectorHover", "selectors", "document", "querySelectorAll", "length", "for<PERSON>ach", "item", "elem", "pxRatio", "retina", "pixelRatio", "pos", "x", "offsetLeft", "offsetWidth", "y", "offsetTop", "offsetHeight", "repulseRadius", "area", "type", "query", "particles", "quadTree", "contains", "getPosition", "modes", "hoverBubbleSize", "hoverBubbleOpacity", "hoverBubbleColor", "process", "distMouse", "timeSpent", "data", "bubbleParam", "bubbleObj", "optValue", "undefined", "bubbleDuration", "duration", "bubbleDistance", "bubbleModeDistance", "particlesParam", "particlesObj", "pObjBubble", "pObj", "durationEnd", "obj", "_a", "_b", "mouseClickPos", "distance", "queryCircle", "clicking", "Date", "getTime", "clickTime", "sizeData", "bubbleModeSize", "size", "opacityData", "mousePos", "pointDistance", "status", "mouseMoveEvent", "mouseLeaveEvent", "modeSize", "optSize", "pSize", "_c", "modeOpacity", "optOpacity", "pOpacity", "bubbleOptions", "finalColor", "modeColor", "bubbleColor", "Array", "mix", "pColor", "getFillColor"], "mappings": "AAAA,SAASA,MAAT,EAAiBC,SAAjB,EAA4BC,sBAA5B,EAAoDC,SAApD,QAAqE,eAArE;AACA,SAASC,KAAT,EAAgBC,QAAhB,EAA0BC,UAA1B,EAAsCC,OAAtC,EAA+CC,cAA/C,EAA+DC,WAA/D,EAA4EC,WAA5E,EAAyFC,gBAAzF,EAA2GC,SAA3G,EAAsHC,aAAtH,EAAqIC,QAArI,QAAsJ,gBAAtJ;;AACA,SAASC,oBAAT,CAA8BC,aAA9B,EAA6CC,SAA7C,EAAwDC,YAAxD,EAAsEC,KAAtE,EAA6E;AACzE,MAAIF,SAAS,IAAIC,YAAjB,EAA+B;AAC3B,UAAME,KAAK,GAAGJ,aAAa,GAAG,CAACC,SAAS,GAAGC,YAAb,IAA6BC,KAA3D;AACA,WAAOf,KAAK,CAACgB,KAAD,EAAQJ,aAAR,EAAuBC,SAAvB,CAAZ;AACH,GAHD,MAIK,IAAIA,SAAS,GAAGC,YAAhB,EAA8B;AAC/B,UAAME,KAAK,GAAGJ,aAAa,GAAG,CAACE,YAAY,GAAGD,SAAhB,IAA6BE,KAA3D;AACA,WAAOf,KAAK,CAACgB,KAAD,EAAQH,SAAR,EAAmBD,aAAnB,CAAZ;AACH;AACJ;;AACD,OAAO,MAAMK,OAAN,SAAsBnB,sBAAtB,CAA6C;AAChDoB,EAAAA,WAAW,CAACC,SAAD,EAAY;AACnB,UAAMA,SAAN;AACH;;AACDC,EAAAA,SAAS,GAAG;AACR,UAAMD,SAAS,GAAG,KAAKA,SAAvB;AAAA,UAAkCE,OAAO,GAAGF,SAAS,CAACG,aAAtD;AAAA,UAAqEC,KAAK,GAAGJ,SAAS,CAACK,aAAV,CAAwBD,KAArG;AAAA,UAA4GE,MAAM,GAAGJ,OAAO,CAACG,aAAR,CAAsBC,MAA3I;AAAA,UAAmJC,IAAI,GAAGD,MAAM,CAACE,KAAjK;AAAA,UAAwKC,SAAS,GAAGrB,gBAAgB,CAAC,QAAD,EAAWmB,IAAX,CAApM;;AACA,QAAI,EAAEE,SAAS,IAAKH,MAAM,CAACI,OAAP,CAAeC,MAAf,IAAyBP,KAAK,CAACQ,QAA7C,IAA2DN,MAAM,CAACO,OAAP,CAAeF,MAAf,IAAyBP,KAAK,CAACU,aAA5F,CAAJ,EAAiH;AAC7G,aAAO,KAAP;AACH;;AACD,UAAMC,SAAS,GAAGT,MAAM,CAACI,OAAP,CAAeM,IAAjC;AACA,UAAMC,SAAS,GAAGX,MAAM,CAACO,OAAP,CAAeG,IAAjC;AACA,WAAO3B,SAAS,CAAC,QAAD,EAAW0B,SAAX,CAAT,IAAkC1B,SAAS,CAAC,QAAD,EAAW4B,SAAX,CAA3C,IAAoER,SAA3E;AACH;;AACDS,EAAAA,KAAK,CAACC,QAAD,EAAWC,KAAX,EAAkB;AACnB,QAAI,EAAE,CAACD,QAAQ,CAACE,MAAT,CAAgBC,OAAjB,IAA4BF,KAA9B,CAAJ,EAA0C;AACtC;AACH;;AACD,WAAOD,QAAQ,CAACE,MAAT,CAAgBE,GAAvB;AACA,WAAOJ,QAAQ,CAACE,MAAT,CAAgBG,OAAvB;AACA,WAAOL,QAAQ,CAACE,MAAT,CAAgBI,MAAvB;AACA,WAAON,QAAQ,CAACE,MAAT,CAAgBK,KAAvB;AACH;;AACa,QAARC,QAAQ,GAAG;AACb,UAAMzB,OAAO,GAAG,KAAKF,SAAL,CAAeG,aAA/B;AAAA,UAA8CG,MAAM,GAAGJ,OAAO,CAACG,aAAR,CAAsBC,MAA7E;AAAA,UAAqFI,OAAO,GAAGJ,MAAM,CAACI,OAAtG;AAAA,UAA+GG,OAAO,GAAGP,MAAM,CAACO,OAAhI;AAAA,UAAyIe,YAAY,GAAGlB,OAAO,CAACC,MAAhK;AAAA,UAAwKI,SAAS,GAAGL,OAAO,CAACM,IAA5L;AAAA,UAAkMa,YAAY,GAAGhB,OAAO,CAACF,MAAzN;AAAA,UAAiOM,SAAS,GAAGJ,OAAO,CAACG,IAArP;AAAA,UAA2PT,IAAI,GAAGD,MAAM,CAACE,KAAzQ;;AACA,QAAIoB,YAAY,IAAIvC,SAAS,CAAC,QAAD,EAAW0B,SAAX,CAA7B,EAAoD;AAChD,WAAKe,WAAL;AACH,KAFD,MAGK,IAAID,YAAY,IAAIxC,SAAS,CAAC,QAAD,EAAW4B,SAAX,CAA7B,EAAoD;AACrD,WAAKc,WAAL;AACH,KAFI,MAGA;AACD9C,MAAAA,cAAc,CAAC,QAAD,EAAWsB,IAAX,EAAiB,CAACyB,QAAD,EAAWT,GAAX,KAAmB,KAAKU,mBAAL,CAAyBD,QAAzB,EAAmCT,GAAnC,CAApC,CAAd;AACH;AACJ;;AACDU,EAAAA,mBAAmB,CAACD,QAAD,EAAWT,GAAX,EAAgB;AAC/B,UAAMvB,SAAS,GAAG,KAAKA,SAAvB;AAAA,UAAkCkC,SAAS,GAAGC,QAAQ,CAACC,gBAAT,CAA0BJ,QAA1B,CAA9C;;AACA,QAAI,CAACE,SAAS,CAACG,MAAf,EAAuB;AACnB;AACH;;AACDH,IAAAA,SAAS,CAACI,OAAV,CAAmBC,IAAD,IAAU;AACxB,YAAMC,IAAI,GAAGD,IAAb;AAAA,YAAmBE,OAAO,GAAGzC,SAAS,CAAC0C,MAAV,CAAiBC,UAA9C;AAAA,YAA0DC,GAAG,GAAG;AAC5DC,QAAAA,CAAC,EAAE,CAACL,IAAI,CAACM,UAAL,GAAkBN,IAAI,CAACO,WAAL,GAAmB,CAAtC,IAA2CN,OADc;AAE5DO,QAAAA,CAAC,EAAE,CAACR,IAAI,CAACS,SAAL,GAAiBT,IAAI,CAACU,YAAL,GAAoB,CAAtC,IAA2CT;AAFc,OAAhE;AAAA,YAGGU,aAAa,GAAIX,IAAI,CAACO,WAAL,GAAmB,CAApB,GAAyBN,OAH5C;AAAA,YAGqDW,IAAI,GAAG7B,GAAG,CAAC8B,IAAJ,KAAa,QAAb,GACtD,IAAI5E,MAAJ,CAAWmE,GAAG,CAACC,CAAf,EAAkBD,GAAG,CAACI,CAAtB,EAAyBG,aAAzB,CADsD,GAEtD,IAAIvE,SAAJ,CAAc4D,IAAI,CAACM,UAAL,GAAkBL,OAAhC,EAAyCD,IAAI,CAACS,SAAL,GAAiBR,OAA1D,EAAmED,IAAI,CAACO,WAAL,GAAmBN,OAAtF,EAA+FD,IAAI,CAACU,YAAL,GAAoBT,OAAnH,CALN;AAAA,YAKmIa,KAAK,GAAGtD,SAAS,CAACuD,SAAV,CAAoBC,QAApB,CAA6BF,KAA7B,CAAmCF,IAAnC,CAL3I;;AAMA,WAAK,MAAMjC,QAAX,IAAuBmC,KAAvB,EAA8B;AAC1B,YAAI,CAACF,IAAI,CAACK,QAAL,CAActC,QAAQ,CAACuC,WAAT,EAAd,CAAL,EAA4C;AACxC;AACH;;AACDvC,QAAAA,QAAQ,CAACE,MAAT,CAAgBC,OAAhB,GAA0B,IAA1B;AACA,cAAMf,IAAI,GAAGP,SAAS,CAACG,aAAV,CAAwBE,aAAxB,CAAsCsD,KAAtC,CAA4CtC,MAA5C,CAAmDd,IAAhE;AACA,cAAME,SAAS,GAAGzB,OAAO,CAACuB,IAAD,EAAOiC,IAAP,CAAzB;;AACA,YAAI,CAACrB,QAAQ,CAACE,MAAT,CAAgBE,GAAjB,IAAwBJ,QAAQ,CAACE,MAAT,CAAgBE,GAAhB,KAAwBiB,IAApD,EAA0D;AACtD,eAAKtB,KAAL,CAAWC,QAAX,EAAqB,IAArB;AACAA,UAAAA,QAAQ,CAACE,MAAT,CAAgBE,GAAhB,GAAsBiB,IAAtB;AACH;;AACD,aAAKoB,eAAL,CAAqBzC,QAArB,EAA+B,CAA/B,EAAkCV,SAAlC;AACA,aAAKoD,kBAAL,CAAwB1C,QAAxB,EAAkC,CAAlC,EAAqCV,SAArC;AACA,aAAKqD,gBAAL,CAAsB3C,QAAtB,EAAgC,CAAhC,EAAmCV,SAAnC;AACH;AACJ,KAtBD;AAuBH;;AACDsD,EAAAA,OAAO,CAAC5C,QAAD,EAAW6C,SAAX,EAAsBC,SAAtB,EAAiCC,IAAjC,EAAuC;AAC1C,UAAMlE,SAAS,GAAG,KAAKA,SAAvB;AAAA,UAAkCmE,WAAW,GAAGD,IAAI,CAACE,SAAL,CAAeC,QAA/D;;AACA,QAAIF,WAAW,KAAKG,SAApB,EAA+B;AAC3B;AACH;;AACD,UAAMpE,OAAO,GAAGF,SAAS,CAACG,aAA1B;AAAA,UAAyCoE,cAAc,GAAGrE,OAAO,CAACG,aAAR,CAAsBsD,KAAtB,CAA4BtC,MAA5B,CAAmCmD,QAA7F;AAAA,UAAuGC,cAAc,GAAGzE,SAAS,CAAC0C,MAAV,CAAiBgC,kBAAzI;AAAA,UAA6JC,cAAc,GAAGT,IAAI,CAACU,YAAL,CAAkBP,QAAhM;AAAA,UAA0MQ,UAAU,GAAGX,IAAI,CAACE,SAAL,CAAevE,KAAtO;AAAA,UAA6OiF,IAAI,GAAGZ,IAAI,CAACU,YAAL,CAAkB/E,KAAlB,IAA2B,CAA/Q;AAAA,UAAkRwD,IAAI,GAAGa,IAAI,CAACb,IAA9R;;AACA,QAAIc,WAAW,KAAKQ,cAApB,EAAoC;AAChC;AACH;;AACD,QAAI,CAAC3E,SAAS,CAACqB,MAAV,CAAiB0D,WAAtB,EAAmC;AAC/B,UAAIf,SAAS,IAAIS,cAAjB,EAAiC;AAC7B,cAAMO,GAAG,GAAGH,UAAU,KAAK,IAAf,IAAuBA,UAAU,KAAK,KAAK,CAA3C,GAA+CA,UAA/C,GAA4DC,IAAxE;;AACA,YAAIE,GAAG,KAAKb,WAAZ,EAAyB;AACrB,gBAAMtE,KAAK,GAAGiF,IAAI,GAAIb,SAAS,IAAIa,IAAI,GAAGX,WAAX,CAAV,GAAqCI,cAA1D;;AACA,cAAIlB,IAAI,KAAK,MAAb,EAAqB;AACjBlC,YAAAA,QAAQ,CAACE,MAAT,CAAgBI,MAAhB,GAAyB5B,KAAzB;AACH;;AACD,cAAIwD,IAAI,KAAK,SAAb,EAAwB;AACpBlC,YAAAA,QAAQ,CAACE,MAAT,CAAgBG,OAAhB,GAA0B3B,KAA1B;AACH;AACJ;AACJ,OAXD,MAYK;AACD,YAAIwD,IAAI,KAAK,MAAb,EAAqB;AACjB,iBAAOlC,QAAQ,CAACE,MAAT,CAAgBI,MAAvB;AACH;;AACD,YAAI4B,IAAI,KAAK,SAAb,EAAwB;AACpB,iBAAOlC,QAAQ,CAACE,MAAT,CAAgBG,OAAvB;AACH;AACJ;AACJ,KArBD,MAsBK,IAAIqD,UAAJ,EAAgB;AACjB,UAAIxB,IAAI,KAAK,MAAb,EAAqB;AACjB,eAAOlC,QAAQ,CAACE,MAAT,CAAgBI,MAAvB;AACH;;AACD,UAAI4B,IAAI,KAAK,SAAb,EAAwB;AACpB,eAAOlC,QAAQ,CAACE,MAAT,CAAgBG,OAAvB;AACH;AACJ;AACJ;;AACDO,EAAAA,WAAW,GAAG;AACV,QAAIkD,EAAJ,EAAQC,EAAR;;AACA,UAAMlF,SAAS,GAAG,KAAKA,SAAvB;AAAA,UAAkCE,OAAO,GAAGF,SAAS,CAACG,aAAtD;AAAA,UAAqEgF,aAAa,GAAGnF,SAAS,CAACK,aAAV,CAAwBD,KAAxB,CAA8BU,aAAnH;;AACA,QAAI,CAACqE,aAAL,EAAoB;AAChB;AACH;;AACD,UAAMC,QAAQ,GAAGpF,SAAS,CAAC0C,MAAV,CAAiBgC,kBAAlC;AAAA,UAAsDpB,KAAK,GAAGtD,SAAS,CAACuD,SAAV,CAAoBC,QAApB,CAA6B6B,WAA7B,CAAyCF,aAAzC,EAAwDC,QAAxD,CAA9D;;AACA,SAAK,MAAMjE,QAAX,IAAuBmC,KAAvB,EAA8B;AAC1B,UAAI,CAACtD,SAAS,CAACqB,MAAV,CAAiBiE,QAAtB,EAAgC;AAC5B;AACH;;AACDnE,MAAAA,QAAQ,CAACE,MAAT,CAAgBC,OAAhB,GAA0B,CAACtB,SAAS,CAACqB,MAAV,CAAiB0D,WAA5C;AACA,YAAMnC,GAAG,GAAGzB,QAAQ,CAACuC,WAAT,EAAZ;AAAA,YAAoCM,SAAS,GAAG9E,WAAW,CAAC0D,GAAD,EAAMuC,aAAN,CAA3D;AAAA,YAAiFlB,SAAS,GAAG,CAAC,IAAIsB,IAAJ,GAAWC,OAAX,MAAwBxF,SAAS,CAACK,aAAV,CAAwBD,KAAxB,CAA8BqF,SAA9B,IAA2C,CAAnE,CAAD,IAA0E,IAAvK;;AACA,UAAIxB,SAAS,GAAG/D,OAAO,CAACG,aAAR,CAAsBsD,KAAtB,CAA4BtC,MAA5B,CAAmCmD,QAAnD,EAA6D;AACzDxE,QAAAA,SAAS,CAACqB,MAAV,CAAiB0D,WAAjB,GAA+B,IAA/B;AACH;;AACD,UAAId,SAAS,GAAG/D,OAAO,CAACG,aAAR,CAAsBsD,KAAtB,CAA4BtC,MAA5B,CAAmCmD,QAAnC,GAA8C,CAA9D,EAAiE;AAC7DxE,QAAAA,SAAS,CAACqB,MAAV,CAAiBiE,QAAjB,GAA4B,KAA5B;AACAtF,QAAAA,SAAS,CAACqB,MAAV,CAAiB0D,WAAjB,GAA+B,KAA/B;AACH;;AACD,YAAMW,QAAQ,GAAG;AACbtB,QAAAA,SAAS,EAAE;AACPC,UAAAA,QAAQ,EAAErE,SAAS,CAAC0C,MAAV,CAAiBiD,cADpB;AAEP9F,UAAAA,KAAK,EAAEsB,QAAQ,CAACE,MAAT,CAAgBI;AAFhB,SADE;AAKbmD,QAAAA,YAAY,EAAE;AACVP,UAAAA,QAAQ,EAAElF,WAAW,CAACgC,QAAQ,CAACjB,OAAT,CAAiB0F,IAAjB,CAAsB/F,KAAvB,CAAX,GAA2CG,SAAS,CAAC0C,MAAV,CAAiBC,UAD5D;AAEV9C,UAAAA,KAAK,EAAEsB,QAAQ,CAACyE,IAAT,CAAc/F;AAFX,SALD;AASbwD,QAAAA,IAAI,EAAE;AATO,OAAjB;AAWA,WAAKU,OAAL,CAAa5C,QAAb,EAAuB6C,SAAvB,EAAkCC,SAAlC,EAA6CyB,QAA7C;AACA,YAAMG,WAAW,GAAG;AAChBzB,QAAAA,SAAS,EAAE;AACPC,UAAAA,QAAQ,EAAEnE,OAAO,CAACG,aAAR,CAAsBsD,KAAtB,CAA4BtC,MAA5B,CAAmCG,OADtC;AAEP3B,UAAAA,KAAK,EAAEsB,QAAQ,CAACE,MAAT,CAAgBG;AAFhB,SADK;AAKhBoD,QAAAA,YAAY,EAAE;AACVP,UAAAA,QAAQ,EAAElF,WAAW,CAACgC,QAAQ,CAACjB,OAAT,CAAiBsB,OAAjB,CAAyB3B,KAA1B,CADX;AAEVA,UAAAA,KAAK,EAAE,CAACqF,EAAE,GAAG,CAACD,EAAE,GAAG9D,QAAQ,CAACK,OAAf,MAA4B,IAA5B,IAAoCyD,EAAE,KAAK,KAAK,CAAhD,GAAoD,KAAK,CAAzD,GAA6DA,EAAE,CAACpF,KAAtE,MAAiF,IAAjF,IAAyFqF,EAAE,KAAK,KAAK,CAArG,GAAyGA,EAAzG,GAA8G;AAF3G,SALE;AAShB7B,QAAAA,IAAI,EAAE;AATU,OAApB;AAWA,WAAKU,OAAL,CAAa5C,QAAb,EAAuB6C,SAAvB,EAAkCC,SAAlC,EAA6C4B,WAA7C;;AACA,UAAI,CAAC7F,SAAS,CAACqB,MAAV,CAAiB0D,WAAtB,EAAmC;AAC/B,YAAIf,SAAS,IAAIhE,SAAS,CAAC0C,MAAV,CAAiBgC,kBAAlC,EAAsD;AAClD,eAAKZ,gBAAL,CAAsB3C,QAAtB,EAAgC6C,SAAhC;AACH,SAFD,MAGK;AACD,iBAAO7C,QAAQ,CAACE,MAAT,CAAgBK,KAAvB;AACH;AACJ,OAPD,MAQK;AACD,eAAOP,QAAQ,CAACE,MAAT,CAAgBK,KAAvB;AACH;AACJ;AACJ;;AACDI,EAAAA,WAAW,GAAG;AACV,UAAM9B,SAAS,GAAG,KAAKA,SAAvB;AAAA,UAAkC8F,QAAQ,GAAG9F,SAAS,CAACK,aAAV,CAAwBD,KAAxB,CAA8BQ,QAA3E;;AACA,QAAIkF,QAAQ,KAAKxB,SAAjB,EAA4B;AACxB;AACH;;AACD,UAAMc,QAAQ,GAAGpF,SAAS,CAAC0C,MAAV,CAAiBgC,kBAAlC;AAAA,UAAsDpB,KAAK,GAAGtD,SAAS,CAACuD,SAAV,CAAoBC,QAApB,CAA6B6B,WAA7B,CAAyCS,QAAzC,EAAmDV,QAAnD,CAA9D;;AACA,SAAK,MAAMjE,QAAX,IAAuBmC,KAAvB,EAA8B;AAC1BnC,MAAAA,QAAQ,CAACE,MAAT,CAAgBC,OAAhB,GAA0B,IAA1B;AACA,YAAMsB,GAAG,GAAGzB,QAAQ,CAACuC,WAAT,EAAZ;AAAA,YAAoCqC,aAAa,GAAG7G,WAAW,CAAC0D,GAAD,EAAMkD,QAAN,CAA/D;AAAA,YAAgFlG,KAAK,GAAG,IAAImG,aAAa,GAAGX,QAA5G;;AACA,UAAIW,aAAa,IAAIX,QAArB,EAA+B;AAC3B,YAAIxF,KAAK,IAAI,CAAT,IAAcI,SAAS,CAACK,aAAV,CAAwB2F,MAAxB,KAAmCtH,SAAS,CAACuH,cAA/D,EAA+E;AAC3E,eAAKrC,eAAL,CAAqBzC,QAArB,EAA+BvB,KAA/B;AACA,eAAKiE,kBAAL,CAAwB1C,QAAxB,EAAkCvB,KAAlC;AACA,eAAKkE,gBAAL,CAAsB3C,QAAtB,EAAgCvB,KAAhC;AACH;AACJ,OAND,MAOK;AACD,aAAKsB,KAAL,CAAWC,QAAX;AACH;;AACD,UAAInB,SAAS,CAACK,aAAV,CAAwB2F,MAAxB,KAAmCtH,SAAS,CAACwH,eAAjD,EAAkE;AAC9D,aAAKhF,KAAL,CAAWC,QAAX;AACH;AACJ;AACJ;;AACDyC,EAAAA,eAAe,CAACzC,QAAD,EAAWvB,KAAX,EAAkBa,SAAlB,EAA6B;AACxC,UAAMT,SAAS,GAAG,KAAKA,SAAvB;AAAA,UAAkCmG,QAAQ,GAAG,CAAC1F,SAAS,KAAK,IAAd,IAAsBA,SAAS,KAAK,KAAK,CAAzC,GAA6C,KAAK,CAAlD,GAAsDA,SAAS,CAACmF,IAAjE,IAAyEnF,SAAS,CAACmF,IAAV,GAAiB5F,SAAS,CAAC0C,MAAV,CAAiBC,UAA3G,GAAwH3C,SAAS,CAAC0C,MAAV,CAAiBiD,cAAtL;;AACA,QAAIQ,QAAQ,KAAK7B,SAAjB,EAA4B;AACxB;AACH;;AACD,UAAM8B,OAAO,GAAGjH,WAAW,CAACgC,QAAQ,CAACjB,OAAT,CAAiB0F,IAAjB,CAAsB/F,KAAvB,CAAX,GAA2CG,SAAS,CAAC0C,MAAV,CAAiBC,UAA5E;AACA,UAAM0D,KAAK,GAAGlF,QAAQ,CAACyE,IAAT,CAAc/F,KAA5B;AACA,UAAM+F,IAAI,GAAGpG,oBAAoB,CAAC6G,KAAD,EAAQF,QAAR,EAAkBC,OAAlB,EAA2BxG,KAA3B,CAAjC;;AACA,QAAIgG,IAAI,KAAKtB,SAAb,EAAwB;AACpBnD,MAAAA,QAAQ,CAACE,MAAT,CAAgBI,MAAhB,GAAyBmE,IAAzB;AACH;AACJ;;AACD/B,EAAAA,kBAAkB,CAAC1C,QAAD,EAAWvB,KAAX,EAAkBa,SAAlB,EAA6B;AAC3C,QAAIwE,EAAJ,EAAQC,EAAR,EAAYoB,EAAZ;;AACA,UAAMtG,SAAS,GAAG,KAAKA,SAAvB;AAAA,UAAkCE,OAAO,GAAGF,SAAS,CAACG,aAAtD;AAAA,UAAqEoG,WAAW,GAAG,CAACtB,EAAE,GAAGxE,SAAS,KAAK,IAAd,IAAsBA,SAAS,KAAK,KAAK,CAAzC,GAA6C,KAAK,CAAlD,GAAsDA,SAAS,CAACe,OAAtE,MAAmF,IAAnF,IAA2FyD,EAAE,KAAK,KAAK,CAAvG,GAA2GA,EAA3G,GAAgH/E,OAAO,CAACG,aAAR,CAAsBsD,KAAtB,CAA4BtC,MAA5B,CAAmCG,OAAtO;;AACA,QAAI,CAAC+E,WAAL,EAAkB;AACd;AACH;;AACD,UAAMC,UAAU,GAAGrF,QAAQ,CAACjB,OAAT,CAAiBsB,OAAjB,CAAyB3B,KAA5C;AACA,UAAM4G,QAAQ,GAAG,CAACH,EAAE,GAAG,CAACpB,EAAE,GAAG/D,QAAQ,CAACK,OAAf,MAA4B,IAA5B,IAAoC0D,EAAE,KAAK,KAAK,CAAhD,GAAoD,KAAK,CAAzD,GAA6DA,EAAE,CAACrF,KAAtE,MAAiF,IAAjF,IAAyFyG,EAAE,KAAK,KAAK,CAArG,GAAyGA,EAAzG,GAA8G,CAA/H;AACA,UAAM9E,OAAO,GAAGhC,oBAAoB,CAACiH,QAAD,EAAWF,WAAX,EAAwBpH,WAAW,CAACqH,UAAD,CAAnC,EAAiD5G,KAAjD,CAApC;;AACA,QAAI4B,OAAO,KAAK8C,SAAhB,EAA2B;AACvBnD,MAAAA,QAAQ,CAACE,MAAT,CAAgBG,OAAhB,GAA0BA,OAA1B;AACH;AACJ;;AACDsC,EAAAA,gBAAgB,CAAC3C,QAAD,EAAWvB,KAAX,EAAkBa,SAAlB,EAA6B;AACzC,UAAMP,OAAO,GAAG,KAAKF,SAAL,CAAeG,aAA/B;AACA,UAAMuG,aAAa,GAAGjG,SAAS,KAAK,IAAd,IAAsBA,SAAS,KAAK,KAAK,CAAzC,GAA6CA,SAA7C,GAAyDP,OAAO,CAACG,aAAR,CAAsBsD,KAAtB,CAA4BtC,MAA3G;;AACA,QAAI,CAACF,QAAQ,CAACE,MAAT,CAAgBsF,UAArB,EAAiC;AAC7B,YAAMC,SAAS,GAAGF,aAAa,CAAChF,KAAhC;;AACA,UAAI,CAACkF,SAAL,EAAgB;AACZ;AACH;;AACD,YAAMC,WAAW,GAAGD,SAAS,YAAYE,KAArB,GAA6BxH,aAAa,CAACsH,SAAD,CAA1C,GAAwDA,SAA5E;AACAzF,MAAAA,QAAQ,CAACE,MAAT,CAAgBsF,UAAhB,GAA6B5H,UAAU,CAAC8H,WAAD,CAAvC;AACH;;AACD,QAAI,CAAC1F,QAAQ,CAACE,MAAT,CAAgBsF,UAArB,EAAiC;AAC7B;AACH;;AACD,QAAID,aAAa,CAACK,GAAlB,EAAuB;AACnB5F,MAAAA,QAAQ,CAACE,MAAT,CAAgBK,KAAhB,GAAwB4C,SAAxB;AACA,YAAM0C,MAAM,GAAG7F,QAAQ,CAAC8F,YAAT,EAAf;AACA9F,MAAAA,QAAQ,CAACE,MAAT,CAAgBK,KAAhB,GAAwBsF,MAAM,GACxBzH,QAAQ,CAACT,QAAQ,CAACkI,MAAD,EAAS7F,QAAQ,CAACE,MAAT,CAAgBsF,UAAzB,EAAqC,IAAI/G,KAAzC,EAAgDA,KAAhD,CAAT,CADgB,GAExBuB,QAAQ,CAACE,MAAT,CAAgBsF,UAFtB;AAGH,KAND,MAOK;AACDxF,MAAAA,QAAQ,CAACE,MAAT,CAAgBK,KAAhB,GAAwBP,QAAQ,CAACE,MAAT,CAAgBsF,UAAxC;AACH;AACJ;;AAzO+C", "sourcesContent": ["import { Circle, Constants, ExternalInteractorBase, Rectangle } from \"../../../Core\";\nimport { clamp, colorMix, colorToHsl, divMode, divModeExecute, getDistance, getRangeMax, isDivModeEnabled, isInArray, itemFromArray, rgbToHsl, } from \"../../../Utils\";\nfunction calculateBubbleValue(particleValue, modeValue, optionsValue, ratio) {\n    if (modeValue >= optionsValue) {\n        const value = particleValue + (modeValue - optionsValue) * ratio;\n        return clamp(value, particleValue, modeValue);\n    }\n    else if (modeValue < optionsValue) {\n        const value = particleValue - (optionsValue - modeValue) * ratio;\n        return clamp(value, modeValue, particleValue);\n    }\n}\nexport class Bubbler extends ExternalInteractorBase {\n    constructor(container) {\n        super(container);\n    }\n    isEnabled() {\n        const container = this.container, options = container.actualOptions, mouse = container.interactivity.mouse, events = options.interactivity.events, divs = events.onDiv, divBubble = isDivModeEnabled(\"bubble\", divs);\n        if (!(divBubble || (events.onHover.enable && mouse.position) || (events.onClick.enable && mouse.clickPosition))) {\n            return false;\n        }\n        const hoverMode = events.onHover.mode;\n        const clickMode = events.onClick.mode;\n        return isInArray(\"bubble\", hoverMode) || isInArray(\"bubble\", clickMode) || divBubble;\n    }\n    reset(particle, force) {\n        if (!(!particle.bubble.inRange || force)) {\n            return;\n        }\n        delete particle.bubble.div;\n        delete particle.bubble.opacity;\n        delete particle.bubble.radius;\n        delete particle.bubble.color;\n    }\n    async interact() {\n        const options = this.container.actualOptions, events = options.interactivity.events, onHover = events.onHover, onClick = events.onClick, hoverEnabled = onHover.enable, hoverMode = onHover.mode, clickEnabled = onClick.enable, clickMode = onClick.mode, divs = events.onDiv;\n        if (hoverEnabled && isInArray(\"bubble\", hoverMode)) {\n            this.hoverBubble();\n        }\n        else if (clickEnabled && isInArray(\"bubble\", clickMode)) {\n            this.clickBubble();\n        }\n        else {\n            divModeExecute(\"bubble\", divs, (selector, div) => this.singleSelectorHover(selector, div));\n        }\n    }\n    singleSelectorHover(selector, div) {\n        const container = this.container, selectors = document.querySelectorAll(selector);\n        if (!selectors.length) {\n            return;\n        }\n        selectors.forEach((item) => {\n            const elem = item, pxRatio = container.retina.pixelRatio, pos = {\n                x: (elem.offsetLeft + elem.offsetWidth / 2) * pxRatio,\n                y: (elem.offsetTop + elem.offsetHeight / 2) * pxRatio,\n            }, repulseRadius = (elem.offsetWidth / 2) * pxRatio, area = div.type === \"circle\"\n                ? new Circle(pos.x, pos.y, repulseRadius)\n                : new Rectangle(elem.offsetLeft * pxRatio, elem.offsetTop * pxRatio, elem.offsetWidth * pxRatio, elem.offsetHeight * pxRatio), query = container.particles.quadTree.query(area);\n            for (const particle of query) {\n                if (!area.contains(particle.getPosition())) {\n                    continue;\n                }\n                particle.bubble.inRange = true;\n                const divs = container.actualOptions.interactivity.modes.bubble.divs;\n                const divBubble = divMode(divs, elem);\n                if (!particle.bubble.div || particle.bubble.div !== elem) {\n                    this.reset(particle, true);\n                    particle.bubble.div = elem;\n                }\n                this.hoverBubbleSize(particle, 1, divBubble);\n                this.hoverBubbleOpacity(particle, 1, divBubble);\n                this.hoverBubbleColor(particle, 1, divBubble);\n            }\n        });\n    }\n    process(particle, distMouse, timeSpent, data) {\n        const container = this.container, bubbleParam = data.bubbleObj.optValue;\n        if (bubbleParam === undefined) {\n            return;\n        }\n        const options = container.actualOptions, bubbleDuration = options.interactivity.modes.bubble.duration, bubbleDistance = container.retina.bubbleModeDistance, particlesParam = data.particlesObj.optValue, pObjBubble = data.bubbleObj.value, pObj = data.particlesObj.value || 0, type = data.type;\n        if (bubbleParam === particlesParam) {\n            return;\n        }\n        if (!container.bubble.durationEnd) {\n            if (distMouse <= bubbleDistance) {\n                const obj = pObjBubble !== null && pObjBubble !== void 0 ? pObjBubble : pObj;\n                if (obj !== bubbleParam) {\n                    const value = pObj - (timeSpent * (pObj - bubbleParam)) / bubbleDuration;\n                    if (type === \"size\") {\n                        particle.bubble.radius = value;\n                    }\n                    if (type === \"opacity\") {\n                        particle.bubble.opacity = value;\n                    }\n                }\n            }\n            else {\n                if (type === \"size\") {\n                    delete particle.bubble.radius;\n                }\n                if (type === \"opacity\") {\n                    delete particle.bubble.opacity;\n                }\n            }\n        }\n        else if (pObjBubble) {\n            if (type === \"size\") {\n                delete particle.bubble.radius;\n            }\n            if (type === \"opacity\") {\n                delete particle.bubble.opacity;\n            }\n        }\n    }\n    clickBubble() {\n        var _a, _b;\n        const container = this.container, options = container.actualOptions, mouseClickPos = container.interactivity.mouse.clickPosition;\n        if (!mouseClickPos) {\n            return;\n        }\n        const distance = container.retina.bubbleModeDistance, query = container.particles.quadTree.queryCircle(mouseClickPos, distance);\n        for (const particle of query) {\n            if (!container.bubble.clicking) {\n                continue;\n            }\n            particle.bubble.inRange = !container.bubble.durationEnd;\n            const pos = particle.getPosition(), distMouse = getDistance(pos, mouseClickPos), timeSpent = (new Date().getTime() - (container.interactivity.mouse.clickTime || 0)) / 1000;\n            if (timeSpent > options.interactivity.modes.bubble.duration) {\n                container.bubble.durationEnd = true;\n            }\n            if (timeSpent > options.interactivity.modes.bubble.duration * 2) {\n                container.bubble.clicking = false;\n                container.bubble.durationEnd = false;\n            }\n            const sizeData = {\n                bubbleObj: {\n                    optValue: container.retina.bubbleModeSize,\n                    value: particle.bubble.radius,\n                },\n                particlesObj: {\n                    optValue: getRangeMax(particle.options.size.value) * container.retina.pixelRatio,\n                    value: particle.size.value,\n                },\n                type: \"size\",\n            };\n            this.process(particle, distMouse, timeSpent, sizeData);\n            const opacityData = {\n                bubbleObj: {\n                    optValue: options.interactivity.modes.bubble.opacity,\n                    value: particle.bubble.opacity,\n                },\n                particlesObj: {\n                    optValue: getRangeMax(particle.options.opacity.value),\n                    value: (_b = (_a = particle.opacity) === null || _a === void 0 ? void 0 : _a.value) !== null && _b !== void 0 ? _b : 1,\n                },\n                type: \"opacity\",\n            };\n            this.process(particle, distMouse, timeSpent, opacityData);\n            if (!container.bubble.durationEnd) {\n                if (distMouse <= container.retina.bubbleModeDistance) {\n                    this.hoverBubbleColor(particle, distMouse);\n                }\n                else {\n                    delete particle.bubble.color;\n                }\n            }\n            else {\n                delete particle.bubble.color;\n            }\n        }\n    }\n    hoverBubble() {\n        const container = this.container, mousePos = container.interactivity.mouse.position;\n        if (mousePos === undefined) {\n            return;\n        }\n        const distance = container.retina.bubbleModeDistance, query = container.particles.quadTree.queryCircle(mousePos, distance);\n        for (const particle of query) {\n            particle.bubble.inRange = true;\n            const pos = particle.getPosition(), pointDistance = getDistance(pos, mousePos), ratio = 1 - pointDistance / distance;\n            if (pointDistance <= distance) {\n                if (ratio >= 0 && container.interactivity.status === Constants.mouseMoveEvent) {\n                    this.hoverBubbleSize(particle, ratio);\n                    this.hoverBubbleOpacity(particle, ratio);\n                    this.hoverBubbleColor(particle, ratio);\n                }\n            }\n            else {\n                this.reset(particle);\n            }\n            if (container.interactivity.status === Constants.mouseLeaveEvent) {\n                this.reset(particle);\n            }\n        }\n    }\n    hoverBubbleSize(particle, ratio, divBubble) {\n        const container = this.container, modeSize = (divBubble === null || divBubble === void 0 ? void 0 : divBubble.size) ? divBubble.size * container.retina.pixelRatio : container.retina.bubbleModeSize;\n        if (modeSize === undefined) {\n            return;\n        }\n        const optSize = getRangeMax(particle.options.size.value) * container.retina.pixelRatio;\n        const pSize = particle.size.value;\n        const size = calculateBubbleValue(pSize, modeSize, optSize, ratio);\n        if (size !== undefined) {\n            particle.bubble.radius = size;\n        }\n    }\n    hoverBubbleOpacity(particle, ratio, divBubble) {\n        var _a, _b, _c;\n        const container = this.container, options = container.actualOptions, modeOpacity = (_a = divBubble === null || divBubble === void 0 ? void 0 : divBubble.opacity) !== null && _a !== void 0 ? _a : options.interactivity.modes.bubble.opacity;\n        if (!modeOpacity) {\n            return;\n        }\n        const optOpacity = particle.options.opacity.value;\n        const pOpacity = (_c = (_b = particle.opacity) === null || _b === void 0 ? void 0 : _b.value) !== null && _c !== void 0 ? _c : 1;\n        const opacity = calculateBubbleValue(pOpacity, modeOpacity, getRangeMax(optOpacity), ratio);\n        if (opacity !== undefined) {\n            particle.bubble.opacity = opacity;\n        }\n    }\n    hoverBubbleColor(particle, ratio, divBubble) {\n        const options = this.container.actualOptions;\n        const bubbleOptions = divBubble !== null && divBubble !== void 0 ? divBubble : options.interactivity.modes.bubble;\n        if (!particle.bubble.finalColor) {\n            const modeColor = bubbleOptions.color;\n            if (!modeColor) {\n                return;\n            }\n            const bubbleColor = modeColor instanceof Array ? itemFromArray(modeColor) : modeColor;\n            particle.bubble.finalColor = colorToHsl(bubbleColor);\n        }\n        if (!particle.bubble.finalColor) {\n            return;\n        }\n        if (bubbleOptions.mix) {\n            particle.bubble.color = undefined;\n            const pColor = particle.getFillColor();\n            particle.bubble.color = pColor\n                ? rgbToHsl(colorMix(pColor, particle.bubble.finalColor, 1 - ratio, ratio))\n                : particle.bubble.finalColor;\n        }\n        else {\n            particle.bubble.color = particle.bubble.finalColor;\n        }\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
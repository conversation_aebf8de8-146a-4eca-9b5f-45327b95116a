{"ast": null, "code": "import { PolygonDrawerBase } from \"./PolygonDrawerBase.js\";\nconst yFactor = 1.66,\n  sides = 3,\n  double = 2;\nexport class TriangleDrawer extends PolygonDrawerBase {\n  constructor() {\n    super(...arguments);\n    this.validTypes = [\"triangle\"];\n  }\n  getCenter(particle, radius) {\n    return {\n      x: -radius,\n      y: radius / yFactor\n    };\n  }\n  getSidesCount() {\n    return sides;\n  }\n  getSidesData(particle, radius) {\n    const diameter = radius * double;\n    return {\n      count: {\n        denominator: 2,\n        numerator: 3\n      },\n      length: diameter\n    };\n  }\n}", "map": {"version": 3, "names": ["PolygonDrawerBase", "yFactor", "sides", "double", "TriangleDrawer", "constructor", "arguments", "validTypes", "getCenter", "particle", "radius", "x", "y", "getSidesCount", "getSidesData", "diameter", "count", "denominator", "numerator", "length"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/shape-polygon/browser/TriangleDrawer.js"], "sourcesContent": ["import { PolygonDrawerBase } from \"./PolygonDrawerBase.js\";\nconst yFactor = 1.66, sides = 3, double = 2;\nexport class TriangleDrawer extends PolygonDrawerBase {\n    constructor() {\n        super(...arguments);\n        this.validTypes = [\"triangle\"];\n    }\n    getCenter(particle, radius) {\n        return {\n            x: -radius,\n            y: radius / yFactor,\n        };\n    }\n    getSidesCount() {\n        return sides;\n    }\n    getSidesData(particle, radius) {\n        const diameter = radius * double;\n        return {\n            count: {\n                denominator: 2,\n                numerator: 3,\n            },\n            length: diameter,\n        };\n    }\n}\n"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,wBAAwB;AAC1D,MAAMC,OAAO,GAAG,IAAI;EAAEC,KAAK,GAAG,CAAC;EAAEC,MAAM,GAAG,CAAC;AAC3C,OAAO,MAAMC,cAAc,SAASJ,iBAAiB,CAAC;EAClDK,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGC,SAAS,CAAC;IACnB,IAAI,CAACC,UAAU,GAAG,CAAC,UAAU,CAAC;EAClC;EACAC,SAASA,CAACC,QAAQ,EAAEC,MAAM,EAAE;IACxB,OAAO;MACHC,CAAC,EAAE,CAACD,MAAM;MACVE,CAAC,EAAEF,MAAM,GAAGT;IAChB,CAAC;EACL;EACAY,aAAaA,CAAA,EAAG;IACZ,OAAOX,KAAK;EAChB;EACAY,YAAYA,CAACL,QAAQ,EAAEC,MAAM,EAAE;IAC3B,MAAMK,QAAQ,GAAGL,MAAM,GAAGP,MAAM;IAChC,OAAO;MACHa,KAAK,EAAE;QACHC,WAAW,EAAE,CAAC;QACdC,SAAS,EAAE;MACf,CAAC;MACDC,MAAM,EAAEJ;IACZ,CAAC;EACL;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
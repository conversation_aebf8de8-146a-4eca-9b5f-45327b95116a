{"ast": null, "code": "export class ByteStream {\n  constructor(bytes) {\n    this.pos = 0;\n    this.data = new Uint8ClampedArray(bytes);\n  }\n  getString(count) {\n    const slice = this.data.slice(this.pos, this.pos + count);\n    this.pos += slice.length;\n    return slice.reduce((acc, curr) => acc + String.fromCharCode(curr), \"\");\n  }\n  nextByte() {\n    return this.data[this.pos++];\n  }\n  nextTwoBytes() {\n    const increment = 2,\n      previous = 1,\n      shift = 8;\n    this.pos += increment;\n    return this.data[this.pos - increment] + (this.data[this.pos - previous] << shift);\n  }\n  readSubBlocks() {\n    let blockString = \"\",\n      size = 0;\n    const minCount = 0,\n      emptySize = 0;\n    do {\n      size = this.data[this.pos++];\n      for (let count = size; --count >= minCount; blockString += String.fromCharCode(this.data[this.pos++])) {}\n    } while (size !== emptySize);\n    return blockString;\n  }\n  readSubBlocksBin() {\n    let size = this.data[this.pos],\n      len = 0;\n    const emptySize = 0,\n      increment = 1;\n    for (let offset = 0; size !== emptySize; offset += size + increment, size = this.data[this.pos + offset]) {\n      len += size;\n    }\n    const blockData = new Uint8Array(len);\n    size = this.data[this.pos++];\n    for (let i = 0; size !== emptySize; size = this.data[this.pos++]) {\n      for (let count = size; --count >= emptySize; blockData[i++] = this.data[this.pos++]) {}\n    }\n    return blockData;\n  }\n  skipSubBlocks() {\n    for (const increment = 1, noData = 0; this.data[this.pos] !== noData; this.pos += this.data[this.pos] + increment) {}\n    this.pos++;\n  }\n}", "map": {"version": 3, "names": ["ByteStream", "constructor", "bytes", "pos", "data", "Uint8ClampedArray", "getString", "count", "slice", "length", "reduce", "acc", "curr", "String", "fromCharCode", "nextByte", "nextTwoBytes", "increment", "previous", "shift", "readSubBlocks", "blockString", "size", "minCount", "emptySize", "readSubBlocksBin", "len", "offset", "blockData", "Uint8Array", "i", "skip<PERSON><PERSON><PERSON><PERSON>s", "noData"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/shape-image/browser/GifUtils/ByteStream.js"], "sourcesContent": ["export class ByteStream {\n    constructor(bytes) {\n        this.pos = 0;\n        this.data = new Uint8ClampedArray(bytes);\n    }\n    getString(count) {\n        const slice = this.data.slice(this.pos, this.pos + count);\n        this.pos += slice.length;\n        return slice.reduce((acc, curr) => acc + String.fromCharCode(curr), \"\");\n    }\n    nextByte() {\n        return this.data[this.pos++];\n    }\n    nextTwoBytes() {\n        const increment = 2, previous = 1, shift = 8;\n        this.pos += increment;\n        return this.data[this.pos - increment] + (this.data[this.pos - previous] << shift);\n    }\n    readSubBlocks() {\n        let blockString = \"\", size = 0;\n        const minCount = 0, emptySize = 0;\n        do {\n            size = this.data[this.pos++];\n            for (let count = size; --count >= minCount; blockString += String.fromCharCode(this.data[this.pos++])) {\n            }\n        } while (size !== emptySize);\n        return blockString;\n    }\n    readSubBlocksBin() {\n        let size = this.data[this.pos], len = 0;\n        const emptySize = 0, increment = 1;\n        for (let offset = 0; size !== emptySize; offset += size + increment, size = this.data[this.pos + offset]) {\n            len += size;\n        }\n        const blockData = new Uint8Array(len);\n        size = this.data[this.pos++];\n        for (let i = 0; size !== emptySize; size = this.data[this.pos++]) {\n            for (let count = size; --count >= emptySize; blockData[i++] = this.data[this.pos++]) {\n            }\n        }\n        return blockData;\n    }\n    skipSubBlocks() {\n        for (const increment = 1, noData = 0; this.data[this.pos] !== noData; this.pos += this.data[this.pos] + increment) {\n        }\n        this.pos++;\n    }\n}\n"], "mappings": "AAAA,OAAO,MAAMA,UAAU,CAAC;EACpBC,WAAWA,CAACC,KAAK,EAAE;IACf,IAAI,CAACC,GAAG,GAAG,CAAC;IACZ,IAAI,CAACC,IAAI,GAAG,IAAIC,iBAAiB,CAACH,KAAK,CAAC;EAC5C;EACAI,SAASA,CAACC,KAAK,EAAE;IACb,MAAMC,KAAK,GAAG,IAAI,CAACJ,IAAI,CAACI,KAAK,CAAC,IAAI,CAACL,GAAG,EAAE,IAAI,CAACA,GAAG,GAAGI,KAAK,CAAC;IACzD,IAAI,CAACJ,GAAG,IAAIK,KAAK,CAACC,MAAM;IACxB,OAAOD,KAAK,CAACE,MAAM,CAAC,CAACC,GAAG,EAAEC,IAAI,KAAKD,GAAG,GAAGE,MAAM,CAACC,YAAY,CAACF,IAAI,CAAC,EAAE,EAAE,CAAC;EAC3E;EACAG,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACX,IAAI,CAAC,IAAI,CAACD,GAAG,EAAE,CAAC;EAChC;EACAa,YAAYA,CAAA,EAAG;IACX,MAAMC,SAAS,GAAG,CAAC;MAAEC,QAAQ,GAAG,CAAC;MAAEC,KAAK,GAAG,CAAC;IAC5C,IAAI,CAAChB,GAAG,IAAIc,SAAS;IACrB,OAAO,IAAI,CAACb,IAAI,CAAC,IAAI,CAACD,GAAG,GAAGc,SAAS,CAAC,IAAI,IAAI,CAACb,IAAI,CAAC,IAAI,CAACD,GAAG,GAAGe,QAAQ,CAAC,IAAIC,KAAK,CAAC;EACtF;EACAC,aAAaA,CAAA,EAAG;IACZ,IAAIC,WAAW,GAAG,EAAE;MAAEC,IAAI,GAAG,CAAC;IAC9B,MAAMC,QAAQ,GAAG,CAAC;MAAEC,SAAS,GAAG,CAAC;IACjC,GAAG;MACCF,IAAI,GAAG,IAAI,CAAClB,IAAI,CAAC,IAAI,CAACD,GAAG,EAAE,CAAC;MAC5B,KAAK,IAAII,KAAK,GAAGe,IAAI,EAAE,EAAEf,KAAK,IAAIgB,QAAQ,EAAEF,WAAW,IAAIR,MAAM,CAACC,YAAY,CAAC,IAAI,CAACV,IAAI,CAAC,IAAI,CAACD,GAAG,EAAE,CAAC,CAAC,EAAE,CACvG;IACJ,CAAC,QAAQmB,IAAI,KAAKE,SAAS;IAC3B,OAAOH,WAAW;EACtB;EACAI,gBAAgBA,CAAA,EAAG;IACf,IAAIH,IAAI,GAAG,IAAI,CAAClB,IAAI,CAAC,IAAI,CAACD,GAAG,CAAC;MAAEuB,GAAG,GAAG,CAAC;IACvC,MAAMF,SAAS,GAAG,CAAC;MAAEP,SAAS,GAAG,CAAC;IAClC,KAAK,IAAIU,MAAM,GAAG,CAAC,EAAEL,IAAI,KAAKE,SAAS,EAAEG,MAAM,IAAIL,IAAI,GAAGL,SAAS,EAAEK,IAAI,GAAG,IAAI,CAAClB,IAAI,CAAC,IAAI,CAACD,GAAG,GAAGwB,MAAM,CAAC,EAAE;MACtGD,GAAG,IAAIJ,IAAI;IACf;IACA,MAAMM,SAAS,GAAG,IAAIC,UAAU,CAACH,GAAG,CAAC;IACrCJ,IAAI,GAAG,IAAI,CAAClB,IAAI,CAAC,IAAI,CAACD,GAAG,EAAE,CAAC;IAC5B,KAAK,IAAI2B,CAAC,GAAG,CAAC,EAAER,IAAI,KAAKE,SAAS,EAAEF,IAAI,GAAG,IAAI,CAAClB,IAAI,CAAC,IAAI,CAACD,GAAG,EAAE,CAAC,EAAE;MAC9D,KAAK,IAAII,KAAK,GAAGe,IAAI,EAAE,EAAEf,KAAK,IAAIiB,SAAS,EAAEI,SAAS,CAACE,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC1B,IAAI,CAAC,IAAI,CAACD,GAAG,EAAE,CAAC,EAAE,CACrF;IACJ;IACA,OAAOyB,SAAS;EACpB;EACAG,aAAaA,CAAA,EAAG;IACZ,KAAK,MAAMd,SAAS,GAAG,CAAC,EAAEe,MAAM,GAAG,CAAC,EAAE,IAAI,CAAC5B,IAAI,CAAC,IAAI,CAACD,GAAG,CAAC,KAAK6B,MAAM,EAAE,IAAI,CAAC7B,GAAG,IAAI,IAAI,CAACC,IAAI,CAAC,IAAI,CAACD,GAAG,CAAC,GAAGc,SAAS,EAAE,CACnH;IACA,IAAI,CAACd,GAAG,EAAE;EACd;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
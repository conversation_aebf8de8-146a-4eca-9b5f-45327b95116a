{"ast": null, "code": "import { OptionsColor } from \"../../../../Options/Classes/OptionsColor\";\nimport { PolygonMaskDrawStroke } from \"./PolygonMaskDrawStroke\";\nexport class PolygonMaskDraw {\n  constructor() {\n    this.enable = false;\n    this.stroke = new PolygonMaskDrawStroke();\n  }\n\n  get lineWidth() {\n    return this.stroke.width;\n  }\n\n  set lineWidth(value) {\n    this.stroke.width = value;\n  }\n\n  get lineColor() {\n    return this.stroke.color;\n  }\n\n  set lineColor(value) {\n    this.stroke.color = OptionsColor.create(this.stroke.color, value);\n  }\n\n  load(data) {\n    var _a;\n\n    if (!data) {\n      return;\n    }\n\n    if (data.enable !== undefined) {\n      this.enable = data.enable;\n    }\n\n    const stroke = (_a = data.stroke) !== null && _a !== void 0 ? _a : {\n      color: data.lineColor,\n      width: data.lineWidth\n    };\n    this.stroke.load(stroke);\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Plugins/PolygonMask/Options/Classes/PolygonMaskDraw.js"], "names": ["OptionsColor", "PolygonMaskDrawStroke", "PolygonMaskDraw", "constructor", "enable", "stroke", "lineWidth", "width", "value", "lineColor", "color", "create", "load", "data", "_a", "undefined"], "mappings": "AAAA,SAASA,YAAT,QAA6B,0CAA7B;AACA,SAASC,qBAAT,QAAsC,yBAAtC;AACA,OAAO,MAAMC,eAAN,CAAsB;AACzBC,EAAAA,WAAW,GAAG;AACV,SAAKC,MAAL,GAAc,KAAd;AACA,SAAKC,MAAL,GAAc,IAAIJ,qBAAJ,EAAd;AACH;;AACY,MAATK,SAAS,GAAG;AACZ,WAAO,KAAKD,MAAL,CAAYE,KAAnB;AACH;;AACY,MAATD,SAAS,CAACE,KAAD,EAAQ;AACjB,SAAKH,MAAL,CAAYE,KAAZ,GAAoBC,KAApB;AACH;;AACY,MAATC,SAAS,GAAG;AACZ,WAAO,KAAKJ,MAAL,CAAYK,KAAnB;AACH;;AACY,MAATD,SAAS,CAACD,KAAD,EAAQ;AACjB,SAAKH,MAAL,CAAYK,KAAZ,GAAoBV,YAAY,CAACW,MAAb,CAAoB,KAAKN,MAAL,CAAYK,KAAhC,EAAuCF,KAAvC,CAApB;AACH;;AACDI,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,QAAIC,EAAJ;;AACA,QAAI,CAACD,IAAL,EAAW;AACP;AACH;;AACD,QAAIA,IAAI,CAACT,MAAL,KAAgBW,SAApB,EAA+B;AAC3B,WAAKX,MAAL,GAAcS,IAAI,CAACT,MAAnB;AACH;;AACD,UAAMC,MAAM,GAAG,CAACS,EAAE,GAAGD,IAAI,CAACR,MAAX,MAAuB,IAAvB,IAA+BS,EAAE,KAAK,KAAK,CAA3C,GAA+CA,EAA/C,GAAoD;AAC/DJ,MAAAA,KAAK,EAAEG,IAAI,CAACJ,SADmD;AAE/DF,MAAAA,KAAK,EAAEM,IAAI,CAACP;AAFmD,KAAnE;AAIA,SAAKD,MAAL,CAAYO,IAAZ,CAAiBP,MAAjB;AACH;;AA9BwB", "sourcesContent": ["import { OptionsColor } from \"../../../../Options/Classes/OptionsColor\";\nimport { PolygonMaskDrawStroke } from \"./PolygonMaskDrawStroke\";\nexport class PolygonMaskDraw {\n    constructor() {\n        this.enable = false;\n        this.stroke = new PolygonMaskDrawStroke();\n    }\n    get lineWidth() {\n        return this.stroke.width;\n    }\n    set lineWidth(value) {\n        this.stroke.width = value;\n    }\n    get lineColor() {\n        return this.stroke.color;\n    }\n    set lineColor(value) {\n        this.stroke.color = OptionsColor.create(this.stroke.color, value);\n    }\n    load(data) {\n        var _a;\n        if (!data) {\n            return;\n        }\n        if (data.enable !== undefined) {\n            this.enable = data.enable;\n        }\n        const stroke = (_a = data.stroke) !== null && _a !== void 0 ? _a : {\n            color: data.lineColor,\n            width: data.lineWidth,\n        };\n        this.stroke.load(stroke);\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
{"ast": null, "code": "import { Vector, isPointInside } from \"tsparticles-engine\";\nexport class NoneOutMode {\n  constructor(container) {\n    this.container = container;\n    this.modes = [\"none\"];\n  }\n  update(particle, direction, delta, outMode) {\n    if (!this.modes.includes(outMode)) {\n      return;\n    }\n    if (particle.options.move.distance.horizontal && (direction === \"left\" || direction === \"right\") || particle.options.move.distance.vertical && (direction === \"top\" || direction === \"bottom\")) {\n      return;\n    }\n    const gravityOptions = particle.options.move.gravity,\n      container = this.container;\n    const canvasSize = container.canvas.size;\n    const pRadius = particle.getRadius();\n    if (!gravityOptions.enable) {\n      if (particle.velocity.y > 0 && particle.position.y <= canvasSize.height + pRadius || particle.velocity.y < 0 && particle.position.y >= -pRadius || particle.velocity.x > 0 && particle.position.x <= canvasSize.width + pRadius || particle.velocity.x < 0 && particle.position.x >= -pRadius) {\n        return;\n      }\n      if (!isPointInside(particle.position, container.canvas.size, Vector.origin, pRadius, direction)) {\n        container.particles.remove(particle);\n      }\n    } else {\n      const position = particle.position;\n      if (!gravityOptions.inverse && position.y > canvasSize.height + pRadius && direction === \"bottom\" || gravityOptions.inverse && position.y < -pRadius && direction === \"top\") {\n        container.particles.remove(particle);\n      }\n    }\n  }\n}", "map": {"version": 3, "names": ["Vector", "isPointInside", "NoneOutMode", "constructor", "container", "modes", "update", "particle", "direction", "delta", "outMode", "includes", "options", "move", "distance", "horizontal", "vertical", "gravityOptions", "gravity", "canvasSize", "canvas", "size", "pRadius", "getRadius", "enable", "velocity", "y", "position", "height", "x", "width", "origin", "particles", "remove", "inverse"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/tsparticles-updater-out-modes/esm/NoneOutMode.js"], "sourcesContent": ["import { Vector, isPointInside, } from \"tsparticles-engine\";\nexport class NoneOutMode {\n    constructor(container) {\n        this.container = container;\n        this.modes = [\"none\"];\n    }\n    update(particle, direction, delta, outMode) {\n        if (!this.modes.includes(outMode)) {\n            return;\n        }\n        if ((particle.options.move.distance.horizontal &&\n            (direction === \"left\" || direction === \"right\")) ||\n            (particle.options.move.distance.vertical &&\n                (direction === \"top\" || direction === \"bottom\"))) {\n            return;\n        }\n        const gravityOptions = particle.options.move.gravity, container = this.container;\n        const canvasSize = container.canvas.size;\n        const pRadius = particle.getRadius();\n        if (!gravityOptions.enable) {\n            if ((particle.velocity.y > 0 && particle.position.y <= canvasSize.height + pRadius) ||\n                (particle.velocity.y < 0 && particle.position.y >= -pRadius) ||\n                (particle.velocity.x > 0 && particle.position.x <= canvasSize.width + pRadius) ||\n                (particle.velocity.x < 0 && particle.position.x >= -pRadius)) {\n                return;\n            }\n            if (!isPointInside(particle.position, container.canvas.size, Vector.origin, pRadius, direction)) {\n                container.particles.remove(particle);\n            }\n        }\n        else {\n            const position = particle.position;\n            if ((!gravityOptions.inverse &&\n                position.y > canvasSize.height + pRadius &&\n                direction === \"bottom\") ||\n                (gravityOptions.inverse && position.y < -pRadius && direction === \"top\")) {\n                container.particles.remove(particle);\n            }\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,MAAM,EAAEC,aAAa,QAAS,oBAAoB;AAC3D,OAAO,MAAMC,WAAW,CAAC;EACrBC,WAAWA,CAACC,SAAS,EAAE;IACnB,IAAI,CAACA,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,KAAK,GAAG,CAAC,MAAM,CAAC;EACzB;EACAC,MAAMA,CAACC,QAAQ,EAAEC,SAAS,EAAEC,KAAK,EAAEC,OAAO,EAAE;IACxC,IAAI,CAAC,IAAI,CAACL,KAAK,CAACM,QAAQ,CAACD,OAAO,CAAC,EAAE;MAC/B;IACJ;IACA,IAAKH,QAAQ,CAACK,OAAO,CAACC,IAAI,CAACC,QAAQ,CAACC,UAAU,KACzCP,SAAS,KAAK,MAAM,IAAIA,SAAS,KAAK,OAAO,CAAC,IAC9CD,QAAQ,CAACK,OAAO,CAACC,IAAI,CAACC,QAAQ,CAACE,QAAQ,KACnCR,SAAS,KAAK,KAAK,IAAIA,SAAS,KAAK,QAAQ,CAAE,EAAE;MACtD;IACJ;IACA,MAAMS,cAAc,GAAGV,QAAQ,CAACK,OAAO,CAACC,IAAI,CAACK,OAAO;MAAEd,SAAS,GAAG,IAAI,CAACA,SAAS;IAChF,MAAMe,UAAU,GAAGf,SAAS,CAACgB,MAAM,CAACC,IAAI;IACxC,MAAMC,OAAO,GAAGf,QAAQ,CAACgB,SAAS,CAAC,CAAC;IACpC,IAAI,CAACN,cAAc,CAACO,MAAM,EAAE;MACxB,IAAKjB,QAAQ,CAACkB,QAAQ,CAACC,CAAC,GAAG,CAAC,IAAInB,QAAQ,CAACoB,QAAQ,CAACD,CAAC,IAAIP,UAAU,CAACS,MAAM,GAAGN,OAAO,IAC7Ef,QAAQ,CAACkB,QAAQ,CAACC,CAAC,GAAG,CAAC,IAAInB,QAAQ,CAACoB,QAAQ,CAACD,CAAC,IAAI,CAACJ,OAAQ,IAC3Df,QAAQ,CAACkB,QAAQ,CAACI,CAAC,GAAG,CAAC,IAAItB,QAAQ,CAACoB,QAAQ,CAACE,CAAC,IAAIV,UAAU,CAACW,KAAK,GAAGR,OAAQ,IAC7Ef,QAAQ,CAACkB,QAAQ,CAACI,CAAC,GAAG,CAAC,IAAItB,QAAQ,CAACoB,QAAQ,CAACE,CAAC,IAAI,CAACP,OAAQ,EAAE;QAC9D;MACJ;MACA,IAAI,CAACrB,aAAa,CAACM,QAAQ,CAACoB,QAAQ,EAAEvB,SAAS,CAACgB,MAAM,CAACC,IAAI,EAAErB,MAAM,CAAC+B,MAAM,EAAET,OAAO,EAAEd,SAAS,CAAC,EAAE;QAC7FJ,SAAS,CAAC4B,SAAS,CAACC,MAAM,CAAC1B,QAAQ,CAAC;MACxC;IACJ,CAAC,MACI;MACD,MAAMoB,QAAQ,GAAGpB,QAAQ,CAACoB,QAAQ;MAClC,IAAK,CAACV,cAAc,CAACiB,OAAO,IACxBP,QAAQ,CAACD,CAAC,GAAGP,UAAU,CAACS,MAAM,GAAGN,OAAO,IACxCd,SAAS,KAAK,QAAQ,IACrBS,cAAc,CAACiB,OAAO,IAAIP,QAAQ,CAACD,CAAC,GAAG,CAACJ,OAAO,IAAId,SAAS,KAAK,KAAM,EAAE;QAC1EJ,SAAS,CAAC4B,SAAS,CAACC,MAAM,CAAC1B,QAAQ,CAAC;MACxC;IACJ;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
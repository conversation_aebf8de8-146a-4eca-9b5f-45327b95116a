{"ast": null, "code": "import { Circle, Constants, ExternalInteractorBase, Rectangle, Vector } from \"../../../Core\";\nimport { calculateBounds, circleBounce, circleBounceDataFromParticle, divModeExecute, isDivModeEnabled, isInArray, rectBounce } from \"../../../Utils\";\nexport class <PERSON>uncer extends ExternalInteractorBase {\n  constructor(container) {\n    super(container);\n  }\n\n  isEnabled() {\n    const container = this.container,\n          options = container.actualOptions,\n          mouse = container.interactivity.mouse,\n          events = options.interactivity.events,\n          divs = events.onDiv;\n    return mouse.position && events.onHover.enable && isInArray(\"bounce\", events.onHover.mode) || isDivModeEnabled(\"bounce\", divs);\n  }\n\n  async interact() {\n    const container = this.container,\n          options = container.actualOptions,\n          events = options.interactivity.events,\n          mouseMoveStatus = container.interactivity.status === Constants.mouseMoveEvent,\n          hoverEnabled = events.onHover.enable,\n          hoverMode = events.onHover.mode,\n          divs = events.onDiv;\n\n    if (mouseMoveStatus && hoverEnabled && isInArray(\"bounce\", hoverMode)) {\n      this.processMouseBounce();\n    } else {\n      divModeExecute(\"bounce\", divs, (selector, div) => this.singleSelectorBounce(selector, div));\n    }\n  }\n\n  reset() {}\n\n  processMouseBounce() {\n    const container = this.container,\n          pxRatio = container.retina.pixelRatio,\n          tolerance = 10 * pxRatio,\n          mousePos = container.interactivity.mouse.position,\n          radius = container.retina.bounceModeDistance;\n\n    if (mousePos) {\n      this.processBounce(mousePos, radius, new Circle(mousePos.x, mousePos.y, radius + tolerance));\n    }\n  }\n\n  singleSelectorBounce(selector, div) {\n    const container = this.container;\n    const query = document.querySelectorAll(selector);\n\n    if (!query.length) {\n      return;\n    }\n\n    query.forEach(item => {\n      const elem = item,\n            pxRatio = container.retina.pixelRatio,\n            pos = {\n        x: (elem.offsetLeft + elem.offsetWidth / 2) * pxRatio,\n        y: (elem.offsetTop + elem.offsetHeight / 2) * pxRatio\n      },\n            radius = elem.offsetWidth / 2 * pxRatio,\n            tolerance = 10 * pxRatio;\n      const area = div.type === \"circle\" ? new Circle(pos.x, pos.y, radius + tolerance) : new Rectangle(elem.offsetLeft * pxRatio - tolerance, elem.offsetTop * pxRatio - tolerance, elem.offsetWidth * pxRatio + tolerance * 2, elem.offsetHeight * pxRatio + tolerance * 2);\n      this.processBounce(pos, radius, area);\n    });\n  }\n\n  processBounce(position, radius, area) {\n    const query = this.container.particles.quadTree.query(area);\n\n    for (const particle of query) {\n      if (area instanceof Circle) {\n        circleBounce(circleBounceDataFromParticle(particle), {\n          position,\n          radius,\n          mass: radius ** 2 * Math.PI / 2,\n          velocity: Vector.origin,\n          factor: Vector.origin\n        });\n      } else if (area instanceof Rectangle) {\n        rectBounce(particle, calculateBounds(position, radius));\n      }\n    }\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Interactions/External/Bounce/Bouncer.js"], "names": ["Circle", "Constants", "ExternalInteractorBase", "Rectangle", "Vector", "calculateBounds", "circleBounce", "circleBounceDataFromParticle", "divModeExecute", "isDivModeEnabled", "isInArray", "rectBounce", "<PERSON><PERSON><PERSON>", "constructor", "container", "isEnabled", "options", "actualOptions", "mouse", "interactivity", "events", "divs", "onDiv", "position", "onHover", "enable", "mode", "interact", "mouseMoveStatus", "status", "mouseMoveEvent", "hoverEnabled", "hoverMode", "processMouseBounce", "selector", "div", "singleSelectorBounce", "reset", "pxRatio", "retina", "pixelRatio", "tolerance", "mousePos", "radius", "bounceModeDistance", "processBounce", "x", "y", "query", "document", "querySelectorAll", "length", "for<PERSON>ach", "item", "elem", "pos", "offsetLeft", "offsetWidth", "offsetTop", "offsetHeight", "area", "type", "particles", "quadTree", "particle", "mass", "Math", "PI", "velocity", "origin", "factor"], "mappings": "AAAA,SAASA,MAAT,EAAiBC,SAAjB,EAA4BC,sBAA5B,EAAoDC,SAApD,EAA+DC,MAA/D,QAA6E,eAA7E;AACA,SAASC,eAAT,EAA0BC,YAA1B,EAAwCC,4BAAxC,EAAsEC,cAAtE,EAAsFC,gBAAtF,EAAwGC,SAAxG,EAAmHC,UAAnH,QAAsI,gBAAtI;AACA,OAAO,MAAMC,OAAN,SAAsBV,sBAAtB,CAA6C;AAChDW,EAAAA,WAAW,CAACC,SAAD,EAAY;AACnB,UAAMA,SAAN;AACH;;AACDC,EAAAA,SAAS,GAAG;AACR,UAAMD,SAAS,GAAG,KAAKA,SAAvB;AAAA,UAAkCE,OAAO,GAAGF,SAAS,CAACG,aAAtD;AAAA,UAAqEC,KAAK,GAAGJ,SAAS,CAACK,aAAV,CAAwBD,KAArG;AAAA,UAA4GE,MAAM,GAAGJ,OAAO,CAACG,aAAR,CAAsBC,MAA3I;AAAA,UAAmJC,IAAI,GAAGD,MAAM,CAACE,KAAjK;AACA,WAASJ,KAAK,CAACK,QAAN,IAAkBH,MAAM,CAACI,OAAP,CAAeC,MAAjC,IAA2Cf,SAAS,CAAC,QAAD,EAAWU,MAAM,CAACI,OAAP,CAAeE,IAA1B,CAArD,IACJjB,gBAAgB,CAAC,QAAD,EAAWY,IAAX,CADpB;AAEH;;AACa,QAARM,QAAQ,GAAG;AACb,UAAMb,SAAS,GAAG,KAAKA,SAAvB;AAAA,UAAkCE,OAAO,GAAGF,SAAS,CAACG,aAAtD;AAAA,UAAqEG,MAAM,GAAGJ,OAAO,CAACG,aAAR,CAAsBC,MAApG;AAAA,UAA4GQ,eAAe,GAAGd,SAAS,CAACK,aAAV,CAAwBU,MAAxB,KAAmC5B,SAAS,CAAC6B,cAA3K;AAAA,UAA2LC,YAAY,GAAGX,MAAM,CAACI,OAAP,CAAeC,MAAzN;AAAA,UAAiOO,SAAS,GAAGZ,MAAM,CAACI,OAAP,CAAeE,IAA5P;AAAA,UAAkQL,IAAI,GAAGD,MAAM,CAACE,KAAhR;;AACA,QAAIM,eAAe,IAAIG,YAAnB,IAAmCrB,SAAS,CAAC,QAAD,EAAWsB,SAAX,CAAhD,EAAuE;AACnE,WAAKC,kBAAL;AACH,KAFD,MAGK;AACDzB,MAAAA,cAAc,CAAC,QAAD,EAAWa,IAAX,EAAiB,CAACa,QAAD,EAAWC,GAAX,KAAmB,KAAKC,oBAAL,CAA0BF,QAA1B,EAAoCC,GAApC,CAApC,CAAd;AACH;AACJ;;AACDE,EAAAA,KAAK,GAAG,CACP;;AACDJ,EAAAA,kBAAkB,GAAG;AACjB,UAAMnB,SAAS,GAAG,KAAKA,SAAvB;AAAA,UAAkCwB,OAAO,GAAGxB,SAAS,CAACyB,MAAV,CAAiBC,UAA7D;AAAA,UAAyEC,SAAS,GAAG,KAAKH,OAA1F;AAAA,UAAmGI,QAAQ,GAAG5B,SAAS,CAACK,aAAV,CAAwBD,KAAxB,CAA8BK,QAA5I;AAAA,UAAsJoB,MAAM,GAAG7B,SAAS,CAACyB,MAAV,CAAiBK,kBAAhL;;AACA,QAAIF,QAAJ,EAAc;AACV,WAAKG,aAAL,CAAmBH,QAAnB,EAA6BC,MAA7B,EAAqC,IAAI3C,MAAJ,CAAW0C,QAAQ,CAACI,CAApB,EAAuBJ,QAAQ,CAACK,CAAhC,EAAmCJ,MAAM,GAAGF,SAA5C,CAArC;AACH;AACJ;;AACDL,EAAAA,oBAAoB,CAACF,QAAD,EAAWC,GAAX,EAAgB;AAChC,UAAMrB,SAAS,GAAG,KAAKA,SAAvB;AACA,UAAMkC,KAAK,GAAGC,QAAQ,CAACC,gBAAT,CAA0BhB,QAA1B,CAAd;;AACA,QAAI,CAACc,KAAK,CAACG,MAAX,EAAmB;AACf;AACH;;AACDH,IAAAA,KAAK,CAACI,OAAN,CAAeC,IAAD,IAAU;AACpB,YAAMC,IAAI,GAAGD,IAAb;AAAA,YAAmBf,OAAO,GAAGxB,SAAS,CAACyB,MAAV,CAAiBC,UAA9C;AAAA,YAA0De,GAAG,GAAG;AAC5DT,QAAAA,CAAC,EAAE,CAACQ,IAAI,CAACE,UAAL,GAAkBF,IAAI,CAACG,WAAL,GAAmB,CAAtC,IAA2CnB,OADc;AAE5DS,QAAAA,CAAC,EAAE,CAACO,IAAI,CAACI,SAAL,GAAiBJ,IAAI,CAACK,YAAL,GAAoB,CAAtC,IAA2CrB;AAFc,OAAhE;AAAA,YAGGK,MAAM,GAAIW,IAAI,CAACG,WAAL,GAAmB,CAApB,GAAyBnB,OAHrC;AAAA,YAG8CG,SAAS,GAAG,KAAKH,OAH/D;AAIA,YAAMsB,IAAI,GAAGzB,GAAG,CAAC0B,IAAJ,KAAa,QAAb,GACP,IAAI7D,MAAJ,CAAWuD,GAAG,CAACT,CAAf,EAAkBS,GAAG,CAACR,CAAtB,EAAyBJ,MAAM,GAAGF,SAAlC,CADO,GAEP,IAAItC,SAAJ,CAAcmD,IAAI,CAACE,UAAL,GAAkBlB,OAAlB,GAA4BG,SAA1C,EAAqDa,IAAI,CAACI,SAAL,GAAiBpB,OAAjB,GAA2BG,SAAhF,EAA2Fa,IAAI,CAACG,WAAL,GAAmBnB,OAAnB,GAA6BG,SAAS,GAAG,CAApI,EAAuIa,IAAI,CAACK,YAAL,GAAoBrB,OAApB,GAA8BG,SAAS,GAAG,CAAjL,CAFN;AAGA,WAAKI,aAAL,CAAmBU,GAAnB,EAAwBZ,MAAxB,EAAgCiB,IAAhC;AACH,KATD;AAUH;;AACDf,EAAAA,aAAa,CAACtB,QAAD,EAAWoB,MAAX,EAAmBiB,IAAnB,EAAyB;AAClC,UAAMZ,KAAK,GAAG,KAAKlC,SAAL,CAAegD,SAAf,CAAyBC,QAAzB,CAAkCf,KAAlC,CAAwCY,IAAxC,CAAd;;AACA,SAAK,MAAMI,QAAX,IAAuBhB,KAAvB,EAA8B;AAC1B,UAAIY,IAAI,YAAY5D,MAApB,EAA4B;AACxBM,QAAAA,YAAY,CAACC,4BAA4B,CAACyD,QAAD,CAA7B,EAAyC;AACjDzC,UAAAA,QADiD;AAEjDoB,UAAAA,MAFiD;AAGjDsB,UAAAA,IAAI,EAAGtB,MAAM,IAAI,CAAV,GAAcuB,IAAI,CAACC,EAApB,GAA0B,CAHiB;AAIjDC,UAAAA,QAAQ,EAAEhE,MAAM,CAACiE,MAJgC;AAKjDC,UAAAA,MAAM,EAAElE,MAAM,CAACiE;AALkC,SAAzC,CAAZ;AAOH,OARD,MASK,IAAIT,IAAI,YAAYzD,SAApB,EAA+B;AAChCQ,QAAAA,UAAU,CAACqD,QAAD,EAAW3D,eAAe,CAACkB,QAAD,EAAWoB,MAAX,CAA1B,CAAV;AACH;AACJ;AACJ;;AA3D+C", "sourcesContent": ["import { Circle, Constants, ExternalInteractorBase, Rectangle, Vector } from \"../../../Core\";\nimport { calculateBounds, circleBounce, circleBounceDataFromParticle, divModeExecute, isDivModeEnabled, isInArray, rectBounce, } from \"../../../Utils\";\nexport class <PERSON>uncer extends ExternalInteractorBase {\n    constructor(container) {\n        super(container);\n    }\n    isEnabled() {\n        const container = this.container, options = container.actualOptions, mouse = container.interactivity.mouse, events = options.interactivity.events, divs = events.onDiv;\n        return ((mouse.position && events.onHover.enable && isInArray(\"bounce\", events.onHover.mode)) ||\n            isDivModeEnabled(\"bounce\", divs));\n    }\n    async interact() {\n        const container = this.container, options = container.actualOptions, events = options.interactivity.events, mouseMoveStatus = container.interactivity.status === Constants.mouseMoveEvent, hoverEnabled = events.onHover.enable, hoverMode = events.onHover.mode, divs = events.onDiv;\n        if (mouseMoveStatus && hoverEnabled && isInArray(\"bounce\", hoverMode)) {\n            this.processMouseBounce();\n        }\n        else {\n            divModeExecute(\"bounce\", divs, (selector, div) => this.singleSelectorBounce(selector, div));\n        }\n    }\n    reset() {\n    }\n    processMouseBounce() {\n        const container = this.container, pxRatio = container.retina.pixelRatio, tolerance = 10 * pxRatio, mousePos = container.interactivity.mouse.position, radius = container.retina.bounceModeDistance;\n        if (mousePos) {\n            this.processBounce(mousePos, radius, new Circle(mousePos.x, mousePos.y, radius + tolerance));\n        }\n    }\n    singleSelectorBounce(selector, div) {\n        const container = this.container;\n        const query = document.querySelectorAll(selector);\n        if (!query.length) {\n            return;\n        }\n        query.forEach((item) => {\n            const elem = item, pxRatio = container.retina.pixelRatio, pos = {\n                x: (elem.offsetLeft + elem.offsetWidth / 2) * pxRatio,\n                y: (elem.offsetTop + elem.offsetHeight / 2) * pxRatio,\n            }, radius = (elem.offsetWidth / 2) * pxRatio, tolerance = 10 * pxRatio;\n            const area = div.type === \"circle\"\n                ? new Circle(pos.x, pos.y, radius + tolerance)\n                : new Rectangle(elem.offsetLeft * pxRatio - tolerance, elem.offsetTop * pxRatio - tolerance, elem.offsetWidth * pxRatio + tolerance * 2, elem.offsetHeight * pxRatio + tolerance * 2);\n            this.processBounce(pos, radius, area);\n        });\n    }\n    processBounce(position, radius, area) {\n        const query = this.container.particles.quadTree.query(area);\n        for (const particle of query) {\n            if (area instanceof Circle) {\n                circleBounce(circleBounceDataFromParticle(particle), {\n                    position,\n                    radius,\n                    mass: (radius ** 2 * Math.PI) / 2,\n                    velocity: Vector.origin,\n                    factor: Vector.origin,\n                });\n            }\n            else if (area instanceof Rectangle) {\n                rectBounce(particle, calculateBounds(position, radius));\n            }\n        }\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
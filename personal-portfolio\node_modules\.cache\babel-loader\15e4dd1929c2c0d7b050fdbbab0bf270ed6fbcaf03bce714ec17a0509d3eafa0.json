{"ast": null, "code": "import { EasingType, addEasing } from \"@tsparticles/engine\";\nexport async function loadEasingQuadPlugin() {\n  addEasing(EasingType.easeInQuad, value => value ** 2);\n  addEasing(EasingType.easeOutQuad, value => 1 - (1 - value) ** 2);\n  addEasing(EasingType.easeInOutQuad, value => value < 0.5 ? 2 * value ** 2 : 1 - (-2 * value + 2) ** 2 / 2);\n  await Promise.resolve();\n}", "map": {"version": 3, "names": ["EasingType", "addEasing", "loadEasingQuadPlugin", "easeInQuad", "value", "easeOutQuad", "easeInOutQuad", "Promise", "resolve"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/plugin-easing-quad/browser/index.js"], "sourcesContent": ["import { EasingType, addEasing } from \"@tsparticles/engine\";\nexport async function loadEasingQuadPlugin() {\n    addEasing(EasingType.easeInQuad, value => value ** 2);\n    addEasing(EasingType.easeOutQuad, value => 1 - (1 - value) ** 2);\n    addEasing(EasingType.easeInOutQuad, value => (value < 0.5 ? 2 * value ** 2 : 1 - (-2 * value + 2) ** 2 / 2));\n    await Promise.resolve();\n}\n"], "mappings": "AAAA,SAASA,UAAU,EAAEC,SAAS,QAAQ,qBAAqB;AAC3D,OAAO,eAAeC,oBAAoBA,CAAA,EAAG;EACzCD,SAAS,CAACD,UAAU,CAACG,UAAU,EAAEC,KAAK,IAAIA,KAAK,IAAI,CAAC,CAAC;EACrDH,SAAS,CAACD,UAAU,CAACK,WAAW,EAAED,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC,GAAGA,KAAK,KAAK,CAAC,CAAC;EAChEH,SAAS,CAACD,UAAU,CAACM,aAAa,EAAEF,KAAK,IAAKA,KAAK,GAAG,GAAG,GAAG,CAAC,GAAGA,KAAK,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,GAAGA,KAAK,GAAG,CAAC,KAAK,CAAC,GAAG,CAAE,CAAC;EAC5G,MAAMG,OAAO,CAACC,OAAO,CAAC,CAAC;AAC3B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { EmojiDrawer } from \"./EmojiDrawer.js\";\nexport async function loadEmojiShape(engine, refresh = true) {\n  await engine.addShape(new EmojiDrawer(), refresh);\n}", "map": {"version": 3, "names": ["EmojiDrawer", "loadEmojiShape", "engine", "refresh", "addShape"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/shape-emoji/browser/index.js"], "sourcesContent": ["import { EmojiDrawer } from \"./EmojiDrawer.js\";\nexport async function loadEmojiShape(engine, refresh = true) {\n    await engine.addShape(new EmojiDrawer(), refresh);\n}\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,kBAAkB;AAC9C,OAAO,eAAeC,cAAcA,CAACC,MAAM,EAAEC,OAAO,GAAG,IAAI,EAAE;EACzD,MAAMD,MAAM,CAACE,QAAQ,CAAC,IAAIJ,WAAW,CAAC,CAAC,EAAEG,OAAO,CAAC;AACrD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
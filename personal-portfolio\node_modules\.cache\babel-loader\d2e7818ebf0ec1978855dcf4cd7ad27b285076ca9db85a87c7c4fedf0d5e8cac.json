{"ast": null, "code": "import { ThemeDefault } from \"./ThemeDefault.js\";\nimport { deepExtend } from \"../../../Utils/Utils.js\";\nexport class Theme {\n  constructor() {\n    this.name = \"\";\n    this.default = new ThemeDefault();\n  }\n  load(data) {\n    if (!data) {\n      return;\n    }\n    if (data.name !== undefined) {\n      this.name = data.name;\n    }\n    this.default.load(data.default);\n    if (data.options !== undefined) {\n      this.options = deepExtend({}, data.options);\n    }\n  }\n}", "map": {"version": 3, "names": ["ThemeDefault", "deepExtend", "Theme", "constructor", "name", "default", "load", "data", "undefined", "options"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/engine/browser/Options/Classes/Theme/Theme.js"], "sourcesContent": ["import { ThemeDefault } from \"./ThemeDefault.js\";\nimport { deepExtend } from \"../../../Utils/Utils.js\";\nexport class Theme {\n    constructor() {\n        this.name = \"\";\n        this.default = new ThemeDefault();\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.name !== undefined) {\n            this.name = data.name;\n        }\n        this.default.load(data.default);\n        if (data.options !== undefined) {\n            this.options = deepExtend({}, data.options);\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,mBAAmB;AAChD,SAASC,UAAU,QAAQ,yBAAyB;AACpD,OAAO,MAAMC,KAAK,CAAC;EACfC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,IAAI,GAAG,EAAE;IACd,IAAI,CAACC,OAAO,GAAG,IAAIL,YAAY,CAAC,CAAC;EACrC;EACAM,IAAIA,CAACC,IAAI,EAAE;IACP,IAAI,CAACA,IAAI,EAAE;MACP;IACJ;IACA,IAAIA,IAAI,CAACH,IAAI,KAAKI,SAAS,EAAE;MACzB,IAAI,CAACJ,IAAI,GAAGG,IAAI,CAACH,IAAI;IACzB;IACA,IAAI,CAACC,OAAO,CAACC,IAAI,CAACC,IAAI,CAACF,OAAO,CAAC;IAC/B,IAAIE,IAAI,CAACE,OAAO,KAAKD,SAAS,EAAE;MAC5B,IAAI,CAACC,OAAO,GAAGR,UAAU,CAAC,CAAC,CAAC,EAAEM,IAAI,CAACE,OAAO,CAAC;IAC/C;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
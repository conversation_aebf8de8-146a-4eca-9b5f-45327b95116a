{"ast": null, "code": "import { deepExtend } from \"@tsparticles/engine\";\nexport class Trail {\n  constructor() {\n    this.delay = 1;\n    this.pauseOnStop = false;\n    this.quantity = 1;\n  }\n  load(data) {\n    if (!data) {\n      return;\n    }\n    if (data.delay !== undefined) {\n      this.delay = data.delay;\n    }\n    if (data.quantity !== undefined) {\n      this.quantity = data.quantity;\n    }\n    if (data.particles !== undefined) {\n      this.particles = deepExtend({}, data.particles);\n    }\n    if (data.pauseOnStop !== undefined) {\n      this.pauseOnStop = data.pauseOnStop;\n    }\n  }\n}", "map": {"version": 3, "names": ["deepExtend", "Trail", "constructor", "delay", "pauseOnStop", "quantity", "load", "data", "undefined", "particles"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/interaction-external-trail/browser/Options/Classes/Trail.js"], "sourcesContent": ["import { deepExtend } from \"@tsparticles/engine\";\nexport class Trail {\n    constructor() {\n        this.delay = 1;\n        this.pauseOnStop = false;\n        this.quantity = 1;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.delay !== undefined) {\n            this.delay = data.delay;\n        }\n        if (data.quantity !== undefined) {\n            this.quantity = data.quantity;\n        }\n        if (data.particles !== undefined) {\n            this.particles = deepExtend({}, data.particles);\n        }\n        if (data.pauseOnStop !== undefined) {\n            this.pauseOnStop = data.pauseOnStop;\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,qBAAqB;AAChD,OAAO,MAAMC,KAAK,CAAC;EACfC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,KAAK,GAAG,CAAC;IACd,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,QAAQ,GAAG,CAAC;EACrB;EACAC,IAAIA,CAACC,IAAI,EAAE;IACP,IAAI,CAACA,IAAI,EAAE;MACP;IACJ;IACA,IAAIA,IAAI,CAACJ,KAAK,KAAKK,SAAS,EAAE;MAC1B,IAAI,CAACL,KAAK,GAAGI,IAAI,CAACJ,KAAK;IAC3B;IACA,IAAII,IAAI,CAACF,QAAQ,KAAKG,SAAS,EAAE;MAC7B,IAAI,CAACH,QAAQ,GAAGE,IAAI,CAACF,QAAQ;IACjC;IACA,IAAIE,IAAI,CAACE,SAAS,KAAKD,SAAS,EAAE;MAC9B,IAAI,CAACC,SAAS,GAAGT,UAAU,CAAC,CAAC,CAAC,EAAEO,IAAI,CAACE,SAAS,CAAC;IACnD;IACA,IAAIF,IAAI,CAACH,WAAW,KAAKI,SAAS,EAAE;MAChC,IAAI,CAACJ,WAAW,GAAGG,IAAI,CAACH,WAAW;IACvC;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
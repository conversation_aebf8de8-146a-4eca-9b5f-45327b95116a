{"ast": null, "code": "const fixFactor = Math.sqrt(2);\nexport class SquareDrawer {\n  getSidesCount() {\n    return 4;\n  }\n\n  draw(context, particle, radius) {\n    context.rect(-radius / fixFactor, -radius / fixFactor, radius * 2 / fixFactor, radius * 2 / fixFactor);\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Shapes/Square/SquareDrawer.js"], "names": ["fixFactor", "Math", "sqrt", "SquareDrawer", "getSidesCount", "draw", "context", "particle", "radius", "rect"], "mappings": "AAAA,MAAMA,SAAS,GAAGC,IAAI,CAACC,IAAL,CAAU,CAAV,CAAlB;AACA,OAAO,MAAMC,YAAN,CAAmB;AACtBC,EAAAA,aAAa,GAAG;AACZ,WAAO,CAAP;AACH;;AACDC,EAAAA,IAAI,CAACC,OAAD,EAAUC,QAAV,EAAoBC,MAApB,EAA4B;AAC5BF,IAAAA,OAAO,CAACG,IAAR,CAAa,CAACD,MAAD,GAAUR,SAAvB,EAAkC,CAACQ,MAAD,GAAUR,SAA5C,EAAwDQ,MAAM,GAAG,CAAV,GAAeR,SAAtE,EAAkFQ,MAAM,GAAG,CAAV,GAAeR,SAAhG;AACH;;AANqB", "sourcesContent": ["const fixFactor = Math.sqrt(2);\nexport class SquareDrawer {\n    getSidesCount() {\n        return 4;\n    }\n    draw(context, particle, radius) {\n        context.rect(-radius / fixFactor, -radius / fixFactor, (radius * 2) / fixFactor, (radius * 2) / fixFactor);\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
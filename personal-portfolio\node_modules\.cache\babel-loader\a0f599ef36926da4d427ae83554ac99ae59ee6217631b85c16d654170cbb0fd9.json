{"ast": null, "code": "import { executeOnSingleOrMultiple, isInArray, itemFromSingleOrMultiple, loadFont } from \"tsparticles-engine\";\nexport const validTypes = [\"text\", \"character\", \"char\"];\nexport class TextDrawer {\n  draw(context, particle, radius, opacity) {\n    const character = particle.shapeData;\n    if (character === undefined) {\n      return;\n    }\n    const textData = character.value;\n    if (textData === undefined) {\n      return;\n    }\n    if (particle.text === undefined) {\n      particle.text = itemFromSingleOrMultiple(textData, particle.randomIndexData);\n    }\n    const text = particle.text,\n      style = character.style ?? \"\",\n      weight = character.weight ?? \"400\",\n      size = Math.round(radius) * 2,\n      font = character.font ?? \"Verdana\",\n      fill = particle.fill,\n      offsetX = text.length * radius / 2;\n    context.font = `${style} ${weight} ${size}px \"${font}\"`;\n    const pos = {\n      x: -offsetX,\n      y: radius / 2\n    };\n    context.globalAlpha = opacity;\n    if (fill) {\n      context.fillText(text, pos.x, pos.y);\n    } else {\n      context.strokeText(text, pos.x, pos.y);\n    }\n    context.globalAlpha = 1;\n  }\n  getSidesCount() {\n    return 12;\n  }\n  async init(container) {\n    const options = container.actualOptions;\n    if (validTypes.find(t => isInArray(t, options.particles.shape.type))) {\n      const shapeOptions = validTypes.map(t => options.particles.shape.options[t]).find(t => !!t),\n        promises = [];\n      executeOnSingleOrMultiple(shapeOptions, shape => {\n        promises.push(loadFont(shape.font, shape.weight));\n      });\n      await Promise.all(promises);\n    }\n  }\n  particleInit(container, particle) {\n    if (!particle.shape || !validTypes.includes(particle.shape)) {\n      return;\n    }\n    const character = particle.shapeData;\n    if (character === undefined) {\n      return;\n    }\n    const textData = character.value;\n    if (textData === undefined) {\n      return;\n    }\n    particle.text = itemFromSingleOrMultiple(textData, particle.randomIndexData);\n  }\n}", "map": {"version": 3, "names": ["executeOnSingleOrMultiple", "isInArray", "itemFromSingleOrMultiple", "loadFont", "validTypes", "TextDrawer", "draw", "context", "particle", "radius", "opacity", "character", "shapeData", "undefined", "textData", "value", "text", "randomIndexData", "style", "weight", "size", "Math", "round", "font", "fill", "offsetX", "length", "pos", "x", "y", "globalAlpha", "fillText", "strokeText", "getSidesCount", "init", "container", "options", "actualOptions", "find", "t", "particles", "shape", "type", "shapeOptions", "map", "promises", "push", "Promise", "all", "particleInit", "includes"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/tsparticles-shape-text/esm/TextDrawer.js"], "sourcesContent": ["import { executeOnSingleOrMultiple, isInArray, itemFromSingleOrMultiple, loadFont, } from \"tsparticles-engine\";\nexport const validTypes = [\"text\", \"character\", \"char\"];\nexport class TextDrawer {\n    draw(context, particle, radius, opacity) {\n        const character = particle.shapeData;\n        if (character === undefined) {\n            return;\n        }\n        const textData = character.value;\n        if (textData === undefined) {\n            return;\n        }\n        if (particle.text === undefined) {\n            particle.text = itemFromSingleOrMultiple(textData, particle.randomIndexData);\n        }\n        const text = particle.text, style = character.style ?? \"\", weight = character.weight ?? \"400\", size = Math.round(radius) * 2, font = character.font ?? \"Verdana\", fill = particle.fill, offsetX = (text.length * radius) / 2;\n        context.font = `${style} ${weight} ${size}px \"${font}\"`;\n        const pos = {\n            x: -offsetX,\n            y: radius / 2,\n        };\n        context.globalAlpha = opacity;\n        if (fill) {\n            context.fillText(text, pos.x, pos.y);\n        }\n        else {\n            context.strokeText(text, pos.x, pos.y);\n        }\n        context.globalAlpha = 1;\n    }\n    getSidesCount() {\n        return 12;\n    }\n    async init(container) {\n        const options = container.actualOptions;\n        if (validTypes.find((t) => isInArray(t, options.particles.shape.type))) {\n            const shapeOptions = validTypes\n                .map((t) => options.particles.shape.options[t])\n                .find((t) => !!t), promises = [];\n            executeOnSingleOrMultiple(shapeOptions, (shape) => {\n                promises.push(loadFont(shape.font, shape.weight));\n            });\n            await Promise.all(promises);\n        }\n    }\n    particleInit(container, particle) {\n        if (!particle.shape || !validTypes.includes(particle.shape)) {\n            return;\n        }\n        const character = particle.shapeData;\n        if (character === undefined) {\n            return;\n        }\n        const textData = character.value;\n        if (textData === undefined) {\n            return;\n        }\n        particle.text = itemFromSingleOrMultiple(textData, particle.randomIndexData);\n    }\n}\n"], "mappings": "AAAA,SAASA,yBAAyB,EAAEC,SAAS,EAAEC,wBAAwB,EAAEC,QAAQ,QAAS,oBAAoB;AAC9G,OAAO,MAAMC,UAAU,GAAG,CAAC,MAAM,EAAE,WAAW,EAAE,MAAM,CAAC;AACvD,OAAO,MAAMC,UAAU,CAAC;EACpBC,IAAIA,CAACC,OAAO,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,OAAO,EAAE;IACrC,MAAMC,SAAS,GAAGH,QAAQ,CAACI,SAAS;IACpC,IAAID,SAAS,KAAKE,SAAS,EAAE;MACzB;IACJ;IACA,MAAMC,QAAQ,GAAGH,SAAS,CAACI,KAAK;IAChC,IAAID,QAAQ,KAAKD,SAAS,EAAE;MACxB;IACJ;IACA,IAAIL,QAAQ,CAACQ,IAAI,KAAKH,SAAS,EAAE;MAC7BL,QAAQ,CAACQ,IAAI,GAAGd,wBAAwB,CAACY,QAAQ,EAAEN,QAAQ,CAACS,eAAe,CAAC;IAChF;IACA,MAAMD,IAAI,GAAGR,QAAQ,CAACQ,IAAI;MAAEE,KAAK,GAAGP,SAAS,CAACO,KAAK,IAAI,EAAE;MAAEC,MAAM,GAAGR,SAAS,CAACQ,MAAM,IAAI,KAAK;MAAEC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACb,MAAM,CAAC,GAAG,CAAC;MAAEc,IAAI,GAAGZ,SAAS,CAACY,IAAI,IAAI,SAAS;MAAEC,IAAI,GAAGhB,QAAQ,CAACgB,IAAI;MAAEC,OAAO,GAAIT,IAAI,CAACU,MAAM,GAAGjB,MAAM,GAAI,CAAC;IAC5NF,OAAO,CAACgB,IAAI,GAAG,GAAGL,KAAK,IAAIC,MAAM,IAAIC,IAAI,OAAOG,IAAI,GAAG;IACvD,MAAMI,GAAG,GAAG;MACRC,CAAC,EAAE,CAACH,OAAO;MACXI,CAAC,EAAEpB,MAAM,GAAG;IAChB,CAAC;IACDF,OAAO,CAACuB,WAAW,GAAGpB,OAAO;IAC7B,IAAIc,IAAI,EAAE;MACNjB,OAAO,CAACwB,QAAQ,CAACf,IAAI,EAAEW,GAAG,CAACC,CAAC,EAAED,GAAG,CAACE,CAAC,CAAC;IACxC,CAAC,MACI;MACDtB,OAAO,CAACyB,UAAU,CAAChB,IAAI,EAAEW,GAAG,CAACC,CAAC,EAAED,GAAG,CAACE,CAAC,CAAC;IAC1C;IACAtB,OAAO,CAACuB,WAAW,GAAG,CAAC;EAC3B;EACAG,aAAaA,CAAA,EAAG;IACZ,OAAO,EAAE;EACb;EACA,MAAMC,IAAIA,CAACC,SAAS,EAAE;IAClB,MAAMC,OAAO,GAAGD,SAAS,CAACE,aAAa;IACvC,IAAIjC,UAAU,CAACkC,IAAI,CAAEC,CAAC,IAAKtC,SAAS,CAACsC,CAAC,EAAEH,OAAO,CAACI,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC,CAAC,EAAE;MACpE,MAAMC,YAAY,GAAGvC,UAAU,CAC1BwC,GAAG,CAAEL,CAAC,IAAKH,OAAO,CAACI,SAAS,CAACC,KAAK,CAACL,OAAO,CAACG,CAAC,CAAC,CAAC,CAC9CD,IAAI,CAAEC,CAAC,IAAK,CAAC,CAACA,CAAC,CAAC;QAAEM,QAAQ,GAAG,EAAE;MACpC7C,yBAAyB,CAAC2C,YAAY,EAAGF,KAAK,IAAK;QAC/CI,QAAQ,CAACC,IAAI,CAAC3C,QAAQ,CAACsC,KAAK,CAAClB,IAAI,EAAEkB,KAAK,CAACtB,MAAM,CAAC,CAAC;MACrD,CAAC,CAAC;MACF,MAAM4B,OAAO,CAACC,GAAG,CAACH,QAAQ,CAAC;IAC/B;EACJ;EACAI,YAAYA,CAACd,SAAS,EAAE3B,QAAQ,EAAE;IAC9B,IAAI,CAACA,QAAQ,CAACiC,KAAK,IAAI,CAACrC,UAAU,CAAC8C,QAAQ,CAAC1C,QAAQ,CAACiC,KAAK,CAAC,EAAE;MACzD;IACJ;IACA,MAAM9B,SAAS,GAAGH,QAAQ,CAACI,SAAS;IACpC,IAAID,SAAS,KAAKE,SAAS,EAAE;MACzB;IACJ;IACA,MAAMC,QAAQ,GAAGH,SAAS,CAACI,KAAK;IAChC,IAAID,QAAQ,KAAKD,SAAS,EAAE;MACxB;IACJ;IACAL,QAAQ,CAACQ,IAAI,GAAGd,wBAAwB,CAACY,QAAQ,EAAEN,QAAQ,CAACS,eAAe,CAAC;EAChF;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
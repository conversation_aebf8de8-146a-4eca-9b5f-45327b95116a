{"ast": null, "code": "import { AbsorbersPlugin } from \"./AbsorbersPlugin.js\";\nexport async function loadAbsorbersPlugin(engine, refresh = true) {\n  await engine.addPlugin(new AbsorbersPlugin(), refresh);\n}\nexport * from \"./AbsorberContainer.js\";\nexport * from \"./Enums/AbsorberClickMode.js\";", "map": {"version": 3, "names": ["AbsorbersPlugin", "loadAbsorbersPlugin", "engine", "refresh", "addPlugin"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/plugin-absorbers/browser/index.js"], "sourcesContent": ["import { AbsorbersPlugin } from \"./AbsorbersPlugin.js\";\nexport async function loadAbsorbersPlugin(engine, refresh = true) {\n    await engine.addPlugin(new AbsorbersPlugin(), refresh);\n}\nexport * from \"./AbsorberContainer.js\";\nexport * from \"./Enums/AbsorberClickMode.js\";\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,sBAAsB;AACtD,OAAO,eAAeC,mBAAmBA,CAACC,MAAM,EAAEC,OAAO,GAAG,IAAI,EAAE;EAC9D,MAAMD,MAAM,CAACE,SAAS,CAAC,IAAIJ,eAAe,CAAC,CAAC,EAAEG,OAAO,CAAC;AAC1D;AACA,cAAc,wBAAwB;AACtC,cAAc,8BAA8B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { AlterType, getRandom, getRangeValue, halfRandom, rangeColorToHsl } from \"@tsparticles/engine\";\nimport { RollMode } from \"./RollMode.js\";\nconst double = 2,\n  doublePI = Math.PI * double,\n  maxAngle = 360;\nexport function initParticle(particle) {\n  const rollOpt = particle.options.roll;\n  if (!rollOpt?.enable) {\n    particle.roll = {\n      enable: false,\n      horizontal: false,\n      vertical: false,\n      angle: 0,\n      speed: 0\n    };\n    return;\n  }\n  particle.roll = {\n    enable: rollOpt.enable,\n    horizontal: rollOpt.mode === RollMode.horizontal || rollOpt.mode === RollMode.both,\n    vertical: rollOpt.mode === RollMode.vertical || rollOpt.mode === RollMode.both,\n    angle: getRandom() * doublePI,\n    speed: getRangeValue(rollOpt.speed) / maxAngle\n  };\n  if (rollOpt.backColor) {\n    particle.backColor = rangeColorToHsl(rollOpt.backColor);\n  } else if (rollOpt.darken.enable && rollOpt.enlighten.enable) {\n    const alterType = getRandom() >= halfRandom ? AlterType.darken : AlterType.enlighten;\n    particle.roll.alter = {\n      type: alterType,\n      value: getRangeValue(alterType === AlterType.darken ? rollOpt.darken.value : rollOpt.enlighten.value)\n    };\n  } else if (rollOpt.darken.enable) {\n    particle.roll.alter = {\n      type: AlterType.darken,\n      value: getRangeValue(rollOpt.darken.value)\n    };\n  } else if (rollOpt.enlighten.enable) {\n    particle.roll.alter = {\n      type: AlterType.enlighten,\n      value: getRangeValue(rollOpt.enlighten.value)\n    };\n  }\n}\nexport function updateRoll(particle, delta) {\n  const roll = particle.options.roll,\n    data = particle.roll;\n  if (!data || !roll?.enable) {\n    return;\n  }\n  const speed = data.speed * delta.factor,\n    max = doublePI;\n  data.angle += speed;\n  if (data.angle > max) {\n    data.angle -= max;\n  }\n}", "map": {"version": 3, "names": ["AlterType", "getRandom", "getRangeValue", "halfRandom", "rangeColorToHsl", "RollMode", "double", "doublePI", "Math", "PI", "maxAngle", "initParticle", "particle", "rollOpt", "options", "roll", "enable", "horizontal", "vertical", "angle", "speed", "mode", "both", "backColor", "darken", "enlighten", "alterType", "alter", "type", "value", "updateRoll", "delta", "data", "factor", "max"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/updater-roll/browser/Utils.js"], "sourcesContent": ["import { AlterType, getRandom, getRangeValue, halfRandom, rangeColorToHsl } from \"@tsparticles/engine\";\nimport { RollMode } from \"./RollMode.js\";\nconst double = 2, doublePI = Math.PI * double, maxAngle = 360;\nexport function initParticle(particle) {\n    const rollOpt = particle.options.roll;\n    if (!rollOpt?.enable) {\n        particle.roll = {\n            enable: false,\n            horizontal: false,\n            vertical: false,\n            angle: 0,\n            speed: 0,\n        };\n        return;\n    }\n    particle.roll = {\n        enable: rollOpt.enable,\n        horizontal: rollOpt.mode === RollMode.horizontal || rollOpt.mode === RollMode.both,\n        vertical: rollOpt.mode === RollMode.vertical || rollOpt.mode === RollMode.both,\n        angle: getRandom() * doublePI,\n        speed: getRangeValue(rollOpt.speed) / maxAngle,\n    };\n    if (rollOpt.backColor) {\n        particle.backColor = rangeColorToHsl(rollOpt.backColor);\n    }\n    else if (rollOpt.darken.enable && rollOpt.enlighten.enable) {\n        const alterType = getRandom() >= halfRandom ? AlterType.darken : AlterType.enlighten;\n        particle.roll.alter = {\n            type: alterType,\n            value: getRangeValue(alterType === AlterType.darken ? rollOpt.darken.value : rollOpt.enlighten.value),\n        };\n    }\n    else if (rollOpt.darken.enable) {\n        particle.roll.alter = {\n            type: AlterType.darken,\n            value: getRangeValue(rollOpt.darken.value),\n        };\n    }\n    else if (rollOpt.enlighten.enable) {\n        particle.roll.alter = {\n            type: AlterType.enlighten,\n            value: getRangeValue(rollOpt.enlighten.value),\n        };\n    }\n}\nexport function updateRoll(particle, delta) {\n    const roll = particle.options.roll, data = particle.roll;\n    if (!data || !roll?.enable) {\n        return;\n    }\n    const speed = data.speed * delta.factor, max = doublePI;\n    data.angle += speed;\n    if (data.angle > max) {\n        data.angle -= max;\n    }\n}\n"], "mappings": "AAAA,SAASA,SAAS,EAAEC,SAAS,EAAEC,aAAa,EAAEC,UAAU,EAAEC,eAAe,QAAQ,qBAAqB;AACtG,SAASC,QAAQ,QAAQ,eAAe;AACxC,MAAMC,MAAM,GAAG,CAAC;EAAEC,QAAQ,GAAGC,IAAI,CAACC,EAAE,GAAGH,MAAM;EAAEI,QAAQ,GAAG,GAAG;AAC7D,OAAO,SAASC,YAAYA,CAACC,QAAQ,EAAE;EACnC,MAAMC,OAAO,GAAGD,QAAQ,CAACE,OAAO,CAACC,IAAI;EACrC,IAAI,CAACF,OAAO,EAAEG,MAAM,EAAE;IAClBJ,QAAQ,CAACG,IAAI,GAAG;MACZC,MAAM,EAAE,KAAK;MACbC,UAAU,EAAE,KAAK;MACjBC,QAAQ,EAAE,KAAK;MACfC,KAAK,EAAE,CAAC;MACRC,KAAK,EAAE;IACX,CAAC;IACD;EACJ;EACAR,QAAQ,CAACG,IAAI,GAAG;IACZC,MAAM,EAAEH,OAAO,CAACG,MAAM;IACtBC,UAAU,EAAEJ,OAAO,CAACQ,IAAI,KAAKhB,QAAQ,CAACY,UAAU,IAAIJ,OAAO,CAACQ,IAAI,KAAKhB,QAAQ,CAACiB,IAAI;IAClFJ,QAAQ,EAAEL,OAAO,CAACQ,IAAI,KAAKhB,QAAQ,CAACa,QAAQ,IAAIL,OAAO,CAACQ,IAAI,KAAKhB,QAAQ,CAACiB,IAAI;IAC9EH,KAAK,EAAElB,SAAS,CAAC,CAAC,GAAGM,QAAQ;IAC7Ba,KAAK,EAAElB,aAAa,CAACW,OAAO,CAACO,KAAK,CAAC,GAAGV;EAC1C,CAAC;EACD,IAAIG,OAAO,CAACU,SAAS,EAAE;IACnBX,QAAQ,CAACW,SAAS,GAAGnB,eAAe,CAACS,OAAO,CAACU,SAAS,CAAC;EAC3D,CAAC,MACI,IAAIV,OAAO,CAACW,MAAM,CAACR,MAAM,IAAIH,OAAO,CAACY,SAAS,CAACT,MAAM,EAAE;IACxD,MAAMU,SAAS,GAAGzB,SAAS,CAAC,CAAC,IAAIE,UAAU,GAAGH,SAAS,CAACwB,MAAM,GAAGxB,SAAS,CAACyB,SAAS;IACpFb,QAAQ,CAACG,IAAI,CAACY,KAAK,GAAG;MAClBC,IAAI,EAAEF,SAAS;MACfG,KAAK,EAAE3B,aAAa,CAACwB,SAAS,KAAK1B,SAAS,CAACwB,MAAM,GAAGX,OAAO,CAACW,MAAM,CAACK,KAAK,GAAGhB,OAAO,CAACY,SAAS,CAACI,KAAK;IACxG,CAAC;EACL,CAAC,MACI,IAAIhB,OAAO,CAACW,MAAM,CAACR,MAAM,EAAE;IAC5BJ,QAAQ,CAACG,IAAI,CAACY,KAAK,GAAG;MAClBC,IAAI,EAAE5B,SAAS,CAACwB,MAAM;MACtBK,KAAK,EAAE3B,aAAa,CAACW,OAAO,CAACW,MAAM,CAACK,KAAK;IAC7C,CAAC;EACL,CAAC,MACI,IAAIhB,OAAO,CAACY,SAAS,CAACT,MAAM,EAAE;IAC/BJ,QAAQ,CAACG,IAAI,CAACY,KAAK,GAAG;MAClBC,IAAI,EAAE5B,SAAS,CAACyB,SAAS;MACzBI,KAAK,EAAE3B,aAAa,CAACW,OAAO,CAACY,SAAS,CAACI,KAAK;IAChD,CAAC;EACL;AACJ;AACA,OAAO,SAASC,UAAUA,CAAClB,QAAQ,EAAEmB,KAAK,EAAE;EACxC,MAAMhB,IAAI,GAAGH,QAAQ,CAACE,OAAO,CAACC,IAAI;IAAEiB,IAAI,GAAGpB,QAAQ,CAACG,IAAI;EACxD,IAAI,CAACiB,IAAI,IAAI,CAACjB,IAAI,EAAEC,MAAM,EAAE;IACxB;EACJ;EACA,MAAMI,KAAK,GAAGY,IAAI,CAACZ,KAAK,GAAGW,KAAK,CAACE,MAAM;IAAEC,GAAG,GAAG3B,QAAQ;EACvDyB,IAAI,CAACb,KAAK,IAAIC,KAAK;EACnB,IAAIY,IAAI,CAACb,KAAK,GAAGe,GAAG,EAAE;IAClBF,IAAI,CAACb,KAAK,IAAIe,GAAG;EACrB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
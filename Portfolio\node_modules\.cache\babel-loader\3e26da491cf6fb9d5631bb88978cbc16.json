{"ast": null, "code": "import getDocumentElement from \"./getDocumentElement.js\";\nimport getComputedStyle from \"./getComputedStyle.js\";\nimport getWindowScrollBarX from \"./getWindowScrollBarX.js\";\nimport getWindowScroll from \"./getWindowScroll.js\";\nimport { max } from \"../utils/math.js\"; // Gets the entire size of the scrollable document area, even extending outside\n// of the `<html>` and `<body>` rect bounds if horizontally scrollable\n\nexport default function getDocumentRect(element) {\n  var _element$ownerDocumen;\n\n  var html = getDocumentElement(element);\n  var winScroll = getWindowScroll(element);\n  var body = (_element$ownerDocumen = element.ownerDocument) == null ? void 0 : _element$ownerDocumen.body;\n  var width = max(html.scrollWidth, html.clientWidth, body ? body.scrollWidth : 0, body ? body.clientWidth : 0);\n  var height = max(html.scrollHeight, html.clientHeight, body ? body.scrollHeight : 0, body ? body.clientHeight : 0);\n  var x = -winScroll.scrollLeft + getWindowScrollBarX(element);\n  var y = -winScroll.scrollTop;\n\n  if (getComputedStyle(body || html).direction === 'rtl') {\n    x += max(html.clientWidth, body ? body.clientWidth : 0) - width;\n  }\n\n  return {\n    width: width,\n    height: height,\n    x: x,\n    y: y\n  };\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/@popperjs/core/lib/dom-utils/getDocumentRect.js"], "names": ["getDocumentElement", "getComputedStyle", "getWindowScrollBarX", "getWindowScroll", "max", "getDocumentRect", "element", "_element$ownerDocumen", "html", "winScroll", "body", "ownerDocument", "width", "scrollWidth", "clientWidth", "height", "scrollHeight", "clientHeight", "x", "scrollLeft", "y", "scrollTop", "direction"], "mappings": "AAAA,OAAOA,kBAAP,MAA+B,yBAA/B;AACA,OAAOC,gBAAP,MAA6B,uBAA7B;AACA,OAAOC,mBAAP,MAAgC,0BAAhC;AACA,OAAOC,eAAP,MAA4B,sBAA5B;AACA,SAASC,GAAT,QAAoB,kBAApB,C,CAAwC;AACxC;;AAEA,eAAe,SAASC,eAAT,CAAyBC,OAAzB,EAAkC;AAC/C,MAAIC,qBAAJ;;AAEA,MAAIC,IAAI,GAAGR,kBAAkB,CAACM,OAAD,CAA7B;AACA,MAAIG,SAAS,GAAGN,eAAe,CAACG,OAAD,CAA/B;AACA,MAAII,IAAI,GAAG,CAACH,qBAAqB,GAAGD,OAAO,CAACK,aAAjC,KAAmD,IAAnD,GAA0D,KAAK,CAA/D,GAAmEJ,qBAAqB,CAACG,IAApG;AACA,MAAIE,KAAK,GAAGR,GAAG,CAACI,IAAI,CAACK,WAAN,EAAmBL,IAAI,CAACM,WAAxB,EAAqCJ,IAAI,GAAGA,IAAI,CAACG,WAAR,GAAsB,CAA/D,EAAkEH,IAAI,GAAGA,IAAI,CAACI,WAAR,GAAsB,CAA5F,CAAf;AACA,MAAIC,MAAM,GAAGX,GAAG,CAACI,IAAI,CAACQ,YAAN,EAAoBR,IAAI,CAACS,YAAzB,EAAuCP,IAAI,GAAGA,IAAI,CAACM,YAAR,GAAuB,CAAlE,EAAqEN,IAAI,GAAGA,IAAI,CAACO,YAAR,GAAuB,CAAhG,CAAhB;AACA,MAAIC,CAAC,GAAG,CAACT,SAAS,CAACU,UAAX,GAAwBjB,mBAAmB,CAACI,OAAD,CAAnD;AACA,MAAIc,CAAC,GAAG,CAACX,SAAS,CAACY,SAAnB;;AAEA,MAAIpB,gBAAgB,CAACS,IAAI,IAAIF,IAAT,CAAhB,CAA+Bc,SAA/B,KAA6C,KAAjD,EAAwD;AACtDJ,IAAAA,CAAC,IAAId,GAAG,CAACI,IAAI,CAACM,WAAN,EAAmBJ,IAAI,GAAGA,IAAI,CAACI,WAAR,GAAsB,CAA7C,CAAH,GAAqDF,KAA1D;AACD;;AAED,SAAO;AACLA,IAAAA,KAAK,EAAEA,KADF;AAELG,IAAAA,MAAM,EAAEA,MAFH;AAGLG,IAAAA,CAAC,EAAEA,CAHE;AAILE,IAAAA,CAAC,EAAEA;AAJE,GAAP;AAMD", "sourcesContent": ["import getDocumentElement from \"./getDocumentElement.js\";\nimport getComputedStyle from \"./getComputedStyle.js\";\nimport getWindowScrollBarX from \"./getWindowScrollBarX.js\";\nimport getWindowScroll from \"./getWindowScroll.js\";\nimport { max } from \"../utils/math.js\"; // Gets the entire size of the scrollable document area, even extending outside\n// of the `<html>` and `<body>` rect bounds if horizontally scrollable\n\nexport default function getDocumentRect(element) {\n  var _element$ownerDocumen;\n\n  var html = getDocumentElement(element);\n  var winScroll = getWindowScroll(element);\n  var body = (_element$ownerDocumen = element.ownerDocument) == null ? void 0 : _element$ownerDocumen.body;\n  var width = max(html.scrollWidth, html.clientWidth, body ? body.scrollWidth : 0, body ? body.clientWidth : 0);\n  var height = max(html.scrollHeight, html.clientHeight, body ? body.scrollHeight : 0, body ? body.clientHeight : 0);\n  var x = -winScroll.scrollLeft + getWindowScrollBarX(element);\n  var y = -winScroll.scrollTop;\n\n  if (getComputedStyle(body || html).direction === 'rtl') {\n    x += max(html.clientWidth, body ? body.clientWidth : 0) - width;\n  }\n\n  return {\n    width: width,\n    height: height,\n    x: x,\n    y: y\n  };\n}"]}, "metadata": {}, "sourceType": "module"}
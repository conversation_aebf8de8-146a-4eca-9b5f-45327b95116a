{"ast": null, "code": "import { ParallaxMover } from \"./ParallaxMover.js\";\nexport async function loadParallaxMover(engine, refresh = true) {\n  await engine.addMover(\"parallax\", () => {\n    return Promise.resolve(new ParallaxMover());\n  }, refresh);\n}", "map": {"version": 3, "names": ["ParallaxMover", "loadParallaxMover", "engine", "refresh", "addMover", "Promise", "resolve"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/move-parallax/browser/index.js"], "sourcesContent": ["import { ParallaxMover } from \"./ParallaxMover.js\";\nexport async function loadParallaxMover(engine, refresh = true) {\n    await engine.addMover(\"parallax\", () => {\n        return Promise.resolve(new ParallaxMover());\n    }, refresh);\n}\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,oBAAoB;AAClD,OAAO,eAAeC,iBAAiBA,CAACC,MAAM,EAAEC,OAAO,GAAG,IAAI,EAAE;EAC5D,MAAMD,MAAM,CAACE,QAAQ,CAAC,UAAU,EAAE,MAAM;IACpC,OAAOC,OAAO,CAACC,OAAO,CAAC,IAAIN,aAAa,CAAC,CAAC,CAAC;EAC/C,CAAC,EAAEG,OAAO,CAAC;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
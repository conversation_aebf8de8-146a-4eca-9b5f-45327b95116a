{"ast": null, "code": "import { degToRad } from \"@tsparticles/engine\";\nconst piDeg = 180,\n  origin = {\n    x: 0,\n    y: 0\n  },\n  sidesOffset = 2;\nexport function drawPolygon(data, start, side) {\n  const {\n      context\n    } = data,\n    sideCount = side.count.numerator * side.count.denominator,\n    decimalSides = side.count.numerator / side.count.denominator,\n    interiorAngleDegrees = piDeg * (decimalSides - sidesOffset) / decimalSides,\n    interiorAngle = Math.PI - degToRad(interiorAngleDegrees);\n  if (!context) {\n    return;\n  }\n  context.beginPath();\n  context.translate(start.x, start.y);\n  context.moveTo(origin.x, origin.y);\n  for (let i = 0; i < sideCount; i++) {\n    context.lineTo(side.length, origin.y);\n    context.translate(side.length, origin.y);\n    context.rotate(interiorAngle);\n  }\n}", "map": {"version": 3, "names": ["degToRad", "piDeg", "origin", "x", "y", "sidesOffset", "drawPolygon", "data", "start", "side", "context", "sideCount", "count", "numerator", "denominator", "decimalSides", "interiorAngleDegrees", "interiorAngle", "Math", "PI", "beginPath", "translate", "moveTo", "i", "lineTo", "length", "rotate"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/shape-polygon/browser/Utils.js"], "sourcesContent": ["import { degToRad } from \"@tsparticles/engine\";\nconst piDeg = 180, origin = { x: 0, y: 0 }, sidesOffset = 2;\nexport function drawPolygon(data, start, side) {\n    const { context } = data, sideCount = side.count.numerator * side.count.denominator, decimalSides = side.count.numerator / side.count.denominator, interiorAngleDegrees = (piDeg * (decimalSides - sidesOffset)) / decimalSides, interiorAngle = Math.PI - degToRad(interiorAngleDegrees);\n    if (!context) {\n        return;\n    }\n    context.beginPath();\n    context.translate(start.x, start.y);\n    context.moveTo(origin.x, origin.y);\n    for (let i = 0; i < sideCount; i++) {\n        context.lineTo(side.length, origin.y);\n        context.translate(side.length, origin.y);\n        context.rotate(interiorAngle);\n    }\n}\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,qBAAqB;AAC9C,MAAMC,KAAK,GAAG,GAAG;EAAEC,MAAM,GAAG;IAAEC,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE;EAAE,CAAC;EAAEC,WAAW,GAAG,CAAC;AAC3D,OAAO,SAASC,WAAWA,CAACC,IAAI,EAAEC,KAAK,EAAEC,IAAI,EAAE;EAC3C,MAAM;MAAEC;IAAQ,CAAC,GAAGH,IAAI;IAAEI,SAAS,GAAGF,IAAI,CAACG,KAAK,CAACC,SAAS,GAAGJ,IAAI,CAACG,KAAK,CAACE,WAAW;IAAEC,YAAY,GAAGN,IAAI,CAACG,KAAK,CAACC,SAAS,GAAGJ,IAAI,CAACG,KAAK,CAACE,WAAW;IAAEE,oBAAoB,GAAIf,KAAK,IAAIc,YAAY,GAAGV,WAAW,CAAC,GAAIU,YAAY;IAAEE,aAAa,GAAGC,IAAI,CAACC,EAAE,GAAGnB,QAAQ,CAACgB,oBAAoB,CAAC;EACzR,IAAI,CAACN,OAAO,EAAE;IACV;EACJ;EACAA,OAAO,CAACU,SAAS,CAAC,CAAC;EACnBV,OAAO,CAACW,SAAS,CAACb,KAAK,CAACL,CAAC,EAAEK,KAAK,CAACJ,CAAC,CAAC;EACnCM,OAAO,CAACY,MAAM,CAACpB,MAAM,CAACC,CAAC,EAAED,MAAM,CAACE,CAAC,CAAC;EAClC,KAAK,IAAImB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGZ,SAAS,EAAEY,CAAC,EAAE,EAAE;IAChCb,OAAO,CAACc,MAAM,CAACf,IAAI,CAACgB,MAAM,EAAEvB,MAAM,CAACE,CAAC,CAAC;IACrCM,OAAO,CAACW,SAAS,CAACZ,IAAI,CAACgB,MAAM,EAAEvB,MAAM,CAACE,CAAC,CAAC;IACxCM,OAAO,CAACgB,MAAM,CAACT,aAAa,CAAC;EACjC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
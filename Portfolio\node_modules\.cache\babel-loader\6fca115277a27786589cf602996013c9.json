{"ast": null, "code": "import { isInArray, itemFromArray, loadFont } from \"../../Utils\";\nexport const validTypes = [\"text\", \"character\", \"char\"];\nexport class TextDrawer {\n  getSidesCount() {\n    return 12;\n  }\n\n  async init(container) {\n    const options = container.actualOptions;\n\n    if (validTypes.find(t => isInArray(t, options.particles.shape.type))) {\n      const shapeOptions = validTypes.map(t => options.particles.shape.options[t]).find(t => !!t);\n\n      if (shapeOptions instanceof Array) {\n        const promises = [];\n\n        for (const character of shapeOptions) {\n          promises.push(loadFont(character));\n        }\n\n        await Promise.allSettled(promises);\n      } else {\n        if (shapeOptions !== undefined) {\n          await loadFont(shapeOptions);\n        }\n      }\n    }\n  }\n\n  draw(context, particle, radius, opacity) {\n    var _a, _b, _c;\n\n    const character = particle.shapeData;\n\n    if (character === undefined) {\n      return;\n    }\n\n    const textData = character.value;\n\n    if (textData === undefined) {\n      return;\n    }\n\n    const textParticle = particle;\n\n    if (textParticle.text === undefined) {\n      textParticle.text = textData instanceof Array ? itemFromArray(textData, particle.randomIndexData) : textData;\n    }\n\n    const text = textParticle.text;\n    const style = (_a = character.style) !== null && _a !== void 0 ? _a : \"\";\n    const weight = (_b = character.weight) !== null && _b !== void 0 ? _b : \"400\";\n    const size = Math.round(radius) * 2;\n    const font = (_c = character.font) !== null && _c !== void 0 ? _c : \"Verdana\";\n    const fill = particle.fill;\n    const offsetX = text.length * radius / 2;\n    context.font = `${style} ${weight} ${size}px \"${font}\"`;\n    const pos = {\n      x: -offsetX,\n      y: radius / 2\n    };\n    context.globalAlpha = opacity;\n\n    if (fill) {\n      context.fillText(text, pos.x, pos.y);\n    } else {\n      context.strokeText(text, pos.x, pos.y);\n    }\n\n    context.globalAlpha = 1;\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Shapes/Text/TextDrawer.js"], "names": ["isInArray", "itemFromArray", "loadFont", "validTypes", "TextDrawer", "getSidesCount", "init", "container", "options", "actualOptions", "find", "t", "particles", "shape", "type", "shapeOptions", "map", "Array", "promises", "character", "push", "Promise", "allSettled", "undefined", "draw", "context", "particle", "radius", "opacity", "_a", "_b", "_c", "shapeData", "textData", "value", "textParticle", "text", "randomIndexData", "style", "weight", "size", "Math", "round", "font", "fill", "offsetX", "length", "pos", "x", "y", "globalAlpha", "fillText", "strokeText"], "mappings": "AAAA,SAASA,SAAT,EAAoBC,aAApB,EAAmCC,QAAnC,QAAmD,aAAnD;AACA,OAAO,MAAMC,UAAU,GAAG,CAAC,MAAD,EAAS,WAAT,EAAsB,MAAtB,CAAnB;AACP,OAAO,MAAMC,UAAN,CAAiB;AACpBC,EAAAA,aAAa,GAAG;AACZ,WAAO,EAAP;AACH;;AACS,QAAJC,IAAI,CAACC,SAAD,EAAY;AAClB,UAAMC,OAAO,GAAGD,SAAS,CAACE,aAA1B;;AACA,QAAIN,UAAU,CAACO,IAAX,CAAiBC,CAAD,IAAOX,SAAS,CAACW,CAAD,EAAIH,OAAO,CAACI,SAAR,CAAkBC,KAAlB,CAAwBC,IAA5B,CAAhC,CAAJ,EAAwE;AACpE,YAAMC,YAAY,GAAGZ,UAAU,CAACa,GAAX,CAAgBL,CAAD,IAAOH,OAAO,CAACI,SAAR,CAAkBC,KAAlB,CAAwBL,OAAxB,CAAgCG,CAAhC,CAAtB,EAA0DD,IAA1D,CAAgEC,CAAD,IAAO,CAAC,CAACA,CAAxE,CAArB;;AACA,UAAII,YAAY,YAAYE,KAA5B,EAAmC;AAC/B,cAAMC,QAAQ,GAAG,EAAjB;;AACA,aAAK,MAAMC,SAAX,IAAwBJ,YAAxB,EAAsC;AAClCG,UAAAA,QAAQ,CAACE,IAAT,CAAclB,QAAQ,CAACiB,SAAD,CAAtB;AACH;;AACD,cAAME,OAAO,CAACC,UAAR,CAAmBJ,QAAnB,CAAN;AACH,OAND,MAOK;AACD,YAAIH,YAAY,KAAKQ,SAArB,EAAgC;AAC5B,gBAAMrB,QAAQ,CAACa,YAAD,CAAd;AACH;AACJ;AACJ;AACJ;;AACDS,EAAAA,IAAI,CAACC,OAAD,EAAUC,QAAV,EAAoBC,MAApB,EAA4BC,OAA5B,EAAqC;AACrC,QAAIC,EAAJ,EAAQC,EAAR,EAAYC,EAAZ;;AACA,UAAMZ,SAAS,GAAGO,QAAQ,CAACM,SAA3B;;AACA,QAAIb,SAAS,KAAKI,SAAlB,EAA6B;AACzB;AACH;;AACD,UAAMU,QAAQ,GAAGd,SAAS,CAACe,KAA3B;;AACA,QAAID,QAAQ,KAAKV,SAAjB,EAA4B;AACxB;AACH;;AACD,UAAMY,YAAY,GAAGT,QAArB;;AACA,QAAIS,YAAY,CAACC,IAAb,KAAsBb,SAA1B,EAAqC;AACjCY,MAAAA,YAAY,CAACC,IAAb,GACIH,QAAQ,YAAYhB,KAApB,GAA4BhB,aAAa,CAACgC,QAAD,EAAWP,QAAQ,CAACW,eAApB,CAAzC,GAAgFJ,QADpF;AAEH;;AACD,UAAMG,IAAI,GAAGD,YAAY,CAACC,IAA1B;AACA,UAAME,KAAK,GAAG,CAACT,EAAE,GAAGV,SAAS,CAACmB,KAAhB,MAA2B,IAA3B,IAAmCT,EAAE,KAAK,KAAK,CAA/C,GAAmDA,EAAnD,GAAwD,EAAtE;AACA,UAAMU,MAAM,GAAG,CAACT,EAAE,GAAGX,SAAS,CAACoB,MAAhB,MAA4B,IAA5B,IAAoCT,EAAE,KAAK,KAAK,CAAhD,GAAoDA,EAApD,GAAyD,KAAxE;AACA,UAAMU,IAAI,GAAGC,IAAI,CAACC,KAAL,CAAWf,MAAX,IAAqB,CAAlC;AACA,UAAMgB,IAAI,GAAG,CAACZ,EAAE,GAAGZ,SAAS,CAACwB,IAAhB,MAA0B,IAA1B,IAAkCZ,EAAE,KAAK,KAAK,CAA9C,GAAkDA,EAAlD,GAAuD,SAApE;AACA,UAAMa,IAAI,GAAGlB,QAAQ,CAACkB,IAAtB;AACA,UAAMC,OAAO,GAAIT,IAAI,CAACU,MAAL,GAAcnB,MAAf,GAAyB,CAAzC;AACAF,IAAAA,OAAO,CAACkB,IAAR,GAAgB,GAAEL,KAAM,IAAGC,MAAO,IAAGC,IAAK,OAAMG,IAAK,GAArD;AACA,UAAMI,GAAG,GAAG;AACRC,MAAAA,CAAC,EAAE,CAACH,OADI;AAERI,MAAAA,CAAC,EAAEtB,MAAM,GAAG;AAFJ,KAAZ;AAIAF,IAAAA,OAAO,CAACyB,WAAR,GAAsBtB,OAAtB;;AACA,QAAIgB,IAAJ,EAAU;AACNnB,MAAAA,OAAO,CAAC0B,QAAR,CAAiBf,IAAjB,EAAuBW,GAAG,CAACC,CAA3B,EAA8BD,GAAG,CAACE,CAAlC;AACH,KAFD,MAGK;AACDxB,MAAAA,OAAO,CAAC2B,UAAR,CAAmBhB,IAAnB,EAAyBW,GAAG,CAACC,CAA7B,EAAgCD,GAAG,CAACE,CAApC;AACH;;AACDxB,IAAAA,OAAO,CAACyB,WAAR,GAAsB,CAAtB;AACH;;AAzDmB", "sourcesContent": ["import { isInArray, itemFromArray, loadFont } from \"../../Utils\";\nexport const validTypes = [\"text\", \"character\", \"char\"];\nexport class TextDrawer {\n    getSidesCount() {\n        return 12;\n    }\n    async init(container) {\n        const options = container.actualOptions;\n        if (validTypes.find((t) => isInArray(t, options.particles.shape.type))) {\n            const shapeOptions = validTypes.map((t) => options.particles.shape.options[t]).find((t) => !!t);\n            if (shapeOptions instanceof Array) {\n                const promises = [];\n                for (const character of shapeOptions) {\n                    promises.push(loadFont(character));\n                }\n                await Promise.allSettled(promises);\n            }\n            else {\n                if (shapeOptions !== undefined) {\n                    await loadFont(shapeOptions);\n                }\n            }\n        }\n    }\n    draw(context, particle, radius, opacity) {\n        var _a, _b, _c;\n        const character = particle.shapeData;\n        if (character === undefined) {\n            return;\n        }\n        const textData = character.value;\n        if (textData === undefined) {\n            return;\n        }\n        const textParticle = particle;\n        if (textParticle.text === undefined) {\n            textParticle.text =\n                textData instanceof Array ? itemFromArray(textData, particle.randomIndexData) : textData;\n        }\n        const text = textParticle.text;\n        const style = (_a = character.style) !== null && _a !== void 0 ? _a : \"\";\n        const weight = (_b = character.weight) !== null && _b !== void 0 ? _b : \"400\";\n        const size = Math.round(radius) * 2;\n        const font = (_c = character.font) !== null && _c !== void 0 ? _c : \"Verdana\";\n        const fill = particle.fill;\n        const offsetX = (text.length * radius) / 2;\n        context.font = `${style} ${weight} ${size}px \"${font}\"`;\n        const pos = {\n            x: -offsetX,\n            y: radius / 2,\n        };\n        context.globalAlpha = opacity;\n        if (fill) {\n            context.fillText(text, pos.x, pos.y);\n        }\n        else {\n            context.strokeText(text, pos.x, pos.y);\n        }\n        context.globalAlpha = 1;\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
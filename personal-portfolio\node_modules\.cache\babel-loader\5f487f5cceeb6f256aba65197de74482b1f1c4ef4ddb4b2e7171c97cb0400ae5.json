{"ast": null, "code": "import { drawLine, getDistance, getDistances, getRandom, getStyleFromRgb, rangeColorToRgb } from \"@tsparticles/engine\";\nexport function drawTriangle(context, p1, p2, p3) {\n  context.beginPath();\n  context.moveTo(p1.x, p1.y);\n  context.lineTo(p2.x, p2.y);\n  context.lineTo(p3.x, p3.y);\n  context.closePath();\n}\nexport function drawLinkLine(params) {\n  let drawn = false;\n  const {\n    begin,\n    end,\n    maxDistance,\n    context,\n    canvasSize,\n    width,\n    backgroundMask,\n    colorLine,\n    opacity,\n    links\n  } = params;\n  if (getDistance(begin, end) <= maxDistance) {\n    drawLine(context, begin, end);\n    drawn = true;\n  } else if (links.warp) {\n    let pi1;\n    let pi2;\n    const endNE = {\n      x: end.x - canvasSize.width,\n      y: end.y\n    };\n    const d1 = getDistances(begin, endNE);\n    if (d1.distance <= maxDistance) {\n      const yi = begin.y - d1.dy / d1.dx * begin.x;\n      pi1 = {\n        x: 0,\n        y: yi\n      };\n      pi2 = {\n        x: canvasSize.width,\n        y: yi\n      };\n    } else {\n      const endSW = {\n        x: end.x,\n        y: end.y - canvasSize.height\n      };\n      const d2 = getDistances(begin, endSW);\n      if (d2.distance <= maxDistance) {\n        const yi = begin.y - d2.dy / d2.dx * begin.x;\n        const xi = -yi / (d2.dy / d2.dx);\n        pi1 = {\n          x: xi,\n          y: 0\n        };\n        pi2 = {\n          x: xi,\n          y: canvasSize.height\n        };\n      } else {\n        const endSE = {\n          x: end.x - canvasSize.width,\n          y: end.y - canvasSize.height\n        };\n        const d3 = getDistances(begin, endSE);\n        if (d3.distance <= maxDistance) {\n          const yi = begin.y - d3.dy / d3.dx * begin.x;\n          const xi = -yi / (d3.dy / d3.dx);\n          pi1 = {\n            x: xi,\n            y: yi\n          };\n          pi2 = {\n            x: pi1.x + canvasSize.width,\n            y: pi1.y + canvasSize.height\n          };\n        }\n      }\n    }\n    if (pi1 && pi2) {\n      drawLine(context, begin, pi1);\n      drawLine(context, end, pi2);\n      drawn = true;\n    }\n  }\n  if (!drawn) {\n    return;\n  }\n  context.lineWidth = width;\n  if (backgroundMask.enable) {\n    context.globalCompositeOperation = backgroundMask.composite;\n  }\n  context.strokeStyle = getStyleFromRgb(colorLine, opacity);\n  const {\n    shadow\n  } = links;\n  if (shadow.enable) {\n    const shadowColor = rangeColorToRgb(shadow.color);\n    if (shadowColor) {\n      context.shadowBlur = shadow.blur;\n      context.shadowColor = getStyleFromRgb(shadowColor);\n    }\n  }\n  context.stroke();\n}\nexport function drawLinkTriangle(params) {\n  const {\n    context,\n    pos1,\n    pos2,\n    pos3,\n    backgroundMask,\n    colorTriangle,\n    opacityTriangle\n  } = params;\n  drawTriangle(context, pos1, pos2, pos3);\n  if (backgroundMask.enable) {\n    context.globalCompositeOperation = backgroundMask.composite;\n  }\n  context.fillStyle = getStyleFromRgb(colorTriangle, opacityTriangle);\n  context.fill();\n}\nexport function getLinkKey(ids) {\n  ids.sort((a, b) => a - b);\n  return ids.join(\"_\");\n}\nexport function setLinkFrequency(particles, dictionary) {\n  const key = getLinkKey(particles.map(t => t.id));\n  let res = dictionary.get(key);\n  if (res === undefined) {\n    res = getRandom();\n    dictionary.set(key, res);\n  }\n  return res;\n}", "map": {"version": 3, "names": ["drawLine", "getDistance", "getDistances", "getRandom", "getStyleFromRgb", "rangeColorToRgb", "drawTriangle", "context", "p1", "p2", "p3", "beginPath", "moveTo", "x", "y", "lineTo", "closePath", "drawLinkLine", "params", "drawn", "begin", "end", "maxDistance", "canvasSize", "width", "backgroundMask", "colorLine", "opacity", "links", "warp", "pi1", "pi2", "endNE", "d1", "distance", "yi", "dy", "dx", "endSW", "height", "d2", "xi", "endSE", "d3", "lineWidth", "enable", "globalCompositeOperation", "composite", "strokeStyle", "shadow", "shadowColor", "color", "<PERSON><PERSON><PERSON><PERSON>", "blur", "stroke", "drawLinkTriangle", "pos1", "pos2", "pos3", "colorTriangle", "opacityTriangle", "fillStyle", "fill", "getLinkKey", "ids", "sort", "a", "b", "join", "setLinkFrequency", "particles", "dictionary", "key", "map", "t", "id", "res", "get", "undefined", "set"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/interaction-particles-links/browser/Utils.js"], "sourcesContent": ["import { drawLine, getDistance, getDistances, getRandom, getStyleFromRgb, rangeColorToRgb, } from \"@tsparticles/engine\";\nexport function drawTriangle(context, p1, p2, p3) {\n    context.beginPath();\n    context.moveTo(p1.x, p1.y);\n    context.lineTo(p2.x, p2.y);\n    context.lineTo(p3.x, p3.y);\n    context.closePath();\n}\nexport function drawLinkLine(params) {\n    let drawn = false;\n    const { begin, end, maxDistance, context, canvasSize, width, backgroundMask, colorLine, opacity, links } = params;\n    if (getDistance(begin, end) <= maxDistance) {\n        drawLine(context, begin, end);\n        drawn = true;\n    }\n    else if (links.warp) {\n        let pi1;\n        let pi2;\n        const endNE = {\n            x: end.x - canvasSize.width,\n            y: end.y,\n        };\n        const d1 = getDistances(begin, endNE);\n        if (d1.distance <= maxDistance) {\n            const yi = begin.y - (d1.dy / d1.dx) * begin.x;\n            pi1 = { x: 0, y: yi };\n            pi2 = { x: canvasSize.width, y: yi };\n        }\n        else {\n            const endSW = {\n                x: end.x,\n                y: end.y - canvasSize.height,\n            };\n            const d2 = getDistances(begin, endSW);\n            if (d2.distance <= maxDistance) {\n                const yi = begin.y - (d2.dy / d2.dx) * begin.x;\n                const xi = -yi / (d2.dy / d2.dx);\n                pi1 = { x: xi, y: 0 };\n                pi2 = { x: xi, y: canvasSize.height };\n            }\n            else {\n                const endSE = {\n                    x: end.x - canvasSize.width,\n                    y: end.y - canvasSize.height,\n                };\n                const d3 = getDistances(begin, endSE);\n                if (d3.distance <= maxDistance) {\n                    const yi = begin.y - (d3.dy / d3.dx) * begin.x;\n                    const xi = -yi / (d3.dy / d3.dx);\n                    pi1 = { x: xi, y: yi };\n                    pi2 = { x: pi1.x + canvasSize.width, y: pi1.y + canvasSize.height };\n                }\n            }\n        }\n        if (pi1 && pi2) {\n            drawLine(context, begin, pi1);\n            drawLine(context, end, pi2);\n            drawn = true;\n        }\n    }\n    if (!drawn) {\n        return;\n    }\n    context.lineWidth = width;\n    if (backgroundMask.enable) {\n        context.globalCompositeOperation = backgroundMask.composite;\n    }\n    context.strokeStyle = getStyleFromRgb(colorLine, opacity);\n    const { shadow } = links;\n    if (shadow.enable) {\n        const shadowColor = rangeColorToRgb(shadow.color);\n        if (shadowColor) {\n            context.shadowBlur = shadow.blur;\n            context.shadowColor = getStyleFromRgb(shadowColor);\n        }\n    }\n    context.stroke();\n}\nexport function drawLinkTriangle(params) {\n    const { context, pos1, pos2, pos3, backgroundMask, colorTriangle, opacityTriangle } = params;\n    drawTriangle(context, pos1, pos2, pos3);\n    if (backgroundMask.enable) {\n        context.globalCompositeOperation = backgroundMask.composite;\n    }\n    context.fillStyle = getStyleFromRgb(colorTriangle, opacityTriangle);\n    context.fill();\n}\nexport function getLinkKey(ids) {\n    ids.sort((a, b) => a - b);\n    return ids.join(\"_\");\n}\nexport function setLinkFrequency(particles, dictionary) {\n    const key = getLinkKey(particles.map(t => t.id));\n    let res = dictionary.get(key);\n    if (res === undefined) {\n        res = getRandom();\n        dictionary.set(key, res);\n    }\n    return res;\n}\n"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,WAAW,EAAEC,YAAY,EAAEC,SAAS,EAAEC,eAAe,EAAEC,eAAe,QAAS,qBAAqB;AACvH,OAAO,SAASC,YAAYA,CAACC,OAAO,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE;EAC9CH,OAAO,CAACI,SAAS,CAAC,CAAC;EACnBJ,OAAO,CAACK,MAAM,CAACJ,EAAE,CAACK,CAAC,EAAEL,EAAE,CAACM,CAAC,CAAC;EAC1BP,OAAO,CAACQ,MAAM,CAACN,EAAE,CAACI,CAAC,EAAEJ,EAAE,CAACK,CAAC,CAAC;EAC1BP,OAAO,CAACQ,MAAM,CAACL,EAAE,CAACG,CAAC,EAAEH,EAAE,CAACI,CAAC,CAAC;EAC1BP,OAAO,CAACS,SAAS,CAAC,CAAC;AACvB;AACA,OAAO,SAASC,YAAYA,CAACC,MAAM,EAAE;EACjC,IAAIC,KAAK,GAAG,KAAK;EACjB,MAAM;IAAEC,KAAK;IAAEC,GAAG;IAAEC,WAAW;IAAEf,OAAO;IAAEgB,UAAU;IAAEC,KAAK;IAAEC,cAAc;IAAEC,SAAS;IAAEC,OAAO;IAAEC;EAAM,CAAC,GAAGV,MAAM;EACjH,IAAIjB,WAAW,CAACmB,KAAK,EAAEC,GAAG,CAAC,IAAIC,WAAW,EAAE;IACxCtB,QAAQ,CAACO,OAAO,EAAEa,KAAK,EAAEC,GAAG,CAAC;IAC7BF,KAAK,GAAG,IAAI;EAChB,CAAC,MACI,IAAIS,KAAK,CAACC,IAAI,EAAE;IACjB,IAAIC,GAAG;IACP,IAAIC,GAAG;IACP,MAAMC,KAAK,GAAG;MACVnB,CAAC,EAAEQ,GAAG,CAACR,CAAC,GAAGU,UAAU,CAACC,KAAK;MAC3BV,CAAC,EAAEO,GAAG,CAACP;IACX,CAAC;IACD,MAAMmB,EAAE,GAAG/B,YAAY,CAACkB,KAAK,EAAEY,KAAK,CAAC;IACrC,IAAIC,EAAE,CAACC,QAAQ,IAAIZ,WAAW,EAAE;MAC5B,MAAMa,EAAE,GAAGf,KAAK,CAACN,CAAC,GAAImB,EAAE,CAACG,EAAE,GAAGH,EAAE,CAACI,EAAE,GAAIjB,KAAK,CAACP,CAAC;MAC9CiB,GAAG,GAAG;QAAEjB,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAEqB;MAAG,CAAC;MACrBJ,GAAG,GAAG;QAAElB,CAAC,EAAEU,UAAU,CAACC,KAAK;QAAEV,CAAC,EAAEqB;MAAG,CAAC;IACxC,CAAC,MACI;MACD,MAAMG,KAAK,GAAG;QACVzB,CAAC,EAAEQ,GAAG,CAACR,CAAC;QACRC,CAAC,EAAEO,GAAG,CAACP,CAAC,GAAGS,UAAU,CAACgB;MAC1B,CAAC;MACD,MAAMC,EAAE,GAAGtC,YAAY,CAACkB,KAAK,EAAEkB,KAAK,CAAC;MACrC,IAAIE,EAAE,CAACN,QAAQ,IAAIZ,WAAW,EAAE;QAC5B,MAAMa,EAAE,GAAGf,KAAK,CAACN,CAAC,GAAI0B,EAAE,CAACJ,EAAE,GAAGI,EAAE,CAACH,EAAE,GAAIjB,KAAK,CAACP,CAAC;QAC9C,MAAM4B,EAAE,GAAG,CAACN,EAAE,IAAIK,EAAE,CAACJ,EAAE,GAAGI,EAAE,CAACH,EAAE,CAAC;QAChCP,GAAG,GAAG;UAAEjB,CAAC,EAAE4B,EAAE;UAAE3B,CAAC,EAAE;QAAE,CAAC;QACrBiB,GAAG,GAAG;UAAElB,CAAC,EAAE4B,EAAE;UAAE3B,CAAC,EAAES,UAAU,CAACgB;QAAO,CAAC;MACzC,CAAC,MACI;QACD,MAAMG,KAAK,GAAG;UACV7B,CAAC,EAAEQ,GAAG,CAACR,CAAC,GAAGU,UAAU,CAACC,KAAK;UAC3BV,CAAC,EAAEO,GAAG,CAACP,CAAC,GAAGS,UAAU,CAACgB;QAC1B,CAAC;QACD,MAAMI,EAAE,GAAGzC,YAAY,CAACkB,KAAK,EAAEsB,KAAK,CAAC;QACrC,IAAIC,EAAE,CAACT,QAAQ,IAAIZ,WAAW,EAAE;UAC5B,MAAMa,EAAE,GAAGf,KAAK,CAACN,CAAC,GAAI6B,EAAE,CAACP,EAAE,GAAGO,EAAE,CAACN,EAAE,GAAIjB,KAAK,CAACP,CAAC;UAC9C,MAAM4B,EAAE,GAAG,CAACN,EAAE,IAAIQ,EAAE,CAACP,EAAE,GAAGO,EAAE,CAACN,EAAE,CAAC;UAChCP,GAAG,GAAG;YAAEjB,CAAC,EAAE4B,EAAE;YAAE3B,CAAC,EAAEqB;UAAG,CAAC;UACtBJ,GAAG,GAAG;YAAElB,CAAC,EAAEiB,GAAG,CAACjB,CAAC,GAAGU,UAAU,CAACC,KAAK;YAAEV,CAAC,EAAEgB,GAAG,CAAChB,CAAC,GAAGS,UAAU,CAACgB;UAAO,CAAC;QACvE;MACJ;IACJ;IACA,IAAIT,GAAG,IAAIC,GAAG,EAAE;MACZ/B,QAAQ,CAACO,OAAO,EAAEa,KAAK,EAAEU,GAAG,CAAC;MAC7B9B,QAAQ,CAACO,OAAO,EAAEc,GAAG,EAAEU,GAAG,CAAC;MAC3BZ,KAAK,GAAG,IAAI;IAChB;EACJ;EACA,IAAI,CAACA,KAAK,EAAE;IACR;EACJ;EACAZ,OAAO,CAACqC,SAAS,GAAGpB,KAAK;EACzB,IAAIC,cAAc,CAACoB,MAAM,EAAE;IACvBtC,OAAO,CAACuC,wBAAwB,GAAGrB,cAAc,CAACsB,SAAS;EAC/D;EACAxC,OAAO,CAACyC,WAAW,GAAG5C,eAAe,CAACsB,SAAS,EAAEC,OAAO,CAAC;EACzD,MAAM;IAAEsB;EAAO,CAAC,GAAGrB,KAAK;EACxB,IAAIqB,MAAM,CAACJ,MAAM,EAAE;IACf,MAAMK,WAAW,GAAG7C,eAAe,CAAC4C,MAAM,CAACE,KAAK,CAAC;IACjD,IAAID,WAAW,EAAE;MACb3C,OAAO,CAAC6C,UAAU,GAAGH,MAAM,CAACI,IAAI;MAChC9C,OAAO,CAAC2C,WAAW,GAAG9C,eAAe,CAAC8C,WAAW,CAAC;IACtD;EACJ;EACA3C,OAAO,CAAC+C,MAAM,CAAC,CAAC;AACpB;AACA,OAAO,SAASC,gBAAgBA,CAACrC,MAAM,EAAE;EACrC,MAAM;IAAEX,OAAO;IAAEiD,IAAI;IAAEC,IAAI;IAAEC,IAAI;IAAEjC,cAAc;IAAEkC,aAAa;IAAEC;EAAgB,CAAC,GAAG1C,MAAM;EAC5FZ,YAAY,CAACC,OAAO,EAAEiD,IAAI,EAAEC,IAAI,EAAEC,IAAI,CAAC;EACvC,IAAIjC,cAAc,CAACoB,MAAM,EAAE;IACvBtC,OAAO,CAACuC,wBAAwB,GAAGrB,cAAc,CAACsB,SAAS;EAC/D;EACAxC,OAAO,CAACsD,SAAS,GAAGzD,eAAe,CAACuD,aAAa,EAAEC,eAAe,CAAC;EACnErD,OAAO,CAACuD,IAAI,CAAC,CAAC;AAClB;AACA,OAAO,SAASC,UAAUA,CAACC,GAAG,EAAE;EAC5BA,GAAG,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,CAAC;EACzB,OAAOH,GAAG,CAACI,IAAI,CAAC,GAAG,CAAC;AACxB;AACA,OAAO,SAASC,gBAAgBA,CAACC,SAAS,EAAEC,UAAU,EAAE;EACpD,MAAMC,GAAG,GAAGT,UAAU,CAACO,SAAS,CAACG,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,CAAC,CAAC;EAChD,IAAIC,GAAG,GAAGL,UAAU,CAACM,GAAG,CAACL,GAAG,CAAC;EAC7B,IAAII,GAAG,KAAKE,SAAS,EAAE;IACnBF,GAAG,GAAGzE,SAAS,CAAC,CAAC;IACjBoE,UAAU,CAACQ,GAAG,CAACP,GAAG,EAAEI,GAAG,CAAC;EAC5B;EACA,OAAOA,GAAG;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "export var GIFDataHeaders;\n(function (GIFDataHeaders) {\n  GIFDataHeaders[GIFDataHeaders[\"Extension\"] = 33] = \"Extension\";\n  GIFDataHeaders[GIFDataHeaders[\"ApplicationExtension\"] = 255] = \"ApplicationExtension\";\n  GIFDataHeaders[GIFDataHeaders[\"GraphicsControlExtension\"] = 249] = \"GraphicsControlExtension\";\n  GIFDataHeaders[GIFDataHeaders[\"PlainTextExtension\"] = 1] = \"PlainTextExtension\";\n  GIFDataHeaders[GIFDataHeaders[\"CommentExtension\"] = 254] = \"CommentExtension\";\n  GIFDataHeaders[GIFDataHeaders[\"Image\"] = 44] = \"Image\";\n  GIFDataHeaders[GIFDataHeaders[\"EndOfFile\"] = 59] = \"EndOfFile\";\n})(GIFDataHeaders || (GIFDataHeaders = {}));", "map": {"version": 3, "names": ["GIFDataHeaders"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/shape-image/browser/GifUtils/Types/GIFDataHeaders.js"], "sourcesContent": ["export var GIFDataHeaders;\n(function (GIFDataHeaders) {\n    GIFDataHeaders[GIFDataHeaders[\"Extension\"] = 33] = \"Extension\";\n    GIFDataHeaders[GIFDataHeaders[\"ApplicationExtension\"] = 255] = \"ApplicationExtension\";\n    GIFDataHeaders[GIFDataHeaders[\"GraphicsControlExtension\"] = 249] = \"GraphicsControlExtension\";\n    GIFDataHeaders[GIFDataHeaders[\"PlainTextExtension\"] = 1] = \"PlainTextExtension\";\n    GIFDataHeaders[GIFDataHeaders[\"CommentExtension\"] = 254] = \"CommentExtension\";\n    GIFDataHeaders[GIFDataHeaders[\"Image\"] = 44] = \"Image\";\n    GIFDataHeaders[GIFDataHeaders[\"EndOfFile\"] = 59] = \"EndOfFile\";\n})(GIFDataHeaders || (GIFDataHeaders = {}));\n"], "mappings": "AAAA,OAAO,IAAIA,cAAc;AACzB,CAAC,UAAUA,cAAc,EAAE;EACvBA,cAAc,CAACA,cAAc,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC,GAAG,WAAW;EAC9DA,cAAc,CAACA,cAAc,CAAC,sBAAsB,CAAC,GAAG,GAAG,CAAC,GAAG,sBAAsB;EACrFA,cAAc,CAACA,cAAc,CAAC,0BAA0B,CAAC,GAAG,GAAG,CAAC,GAAG,0BAA0B;EAC7FA,cAAc,CAACA,cAAc,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC,GAAG,oBAAoB;EAC/EA,cAAc,CAACA,cAAc,CAAC,kBAAkB,CAAC,GAAG,GAAG,CAAC,GAAG,kBAAkB;EAC7EA,cAAc,CAACA,cAAc,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC,GAAG,OAAO;EACtDA,cAAc,CAACA,cAAc,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC,GAAG,WAAW;AAClE,CAAC,EAAEA,cAAc,KAAKA,cAAc,GAAG,CAAC,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
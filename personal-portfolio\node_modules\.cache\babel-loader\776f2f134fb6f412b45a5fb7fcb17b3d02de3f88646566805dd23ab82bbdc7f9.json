{"ast": null, "code": "import { CollisionMode } from \"../../../../Enums/Modes/CollisionMode.js\";\nimport { CollisionsAbsorb } from \"./CollisionsAbsorb.js\";\nimport { CollisionsOverlap } from \"./CollisionsOverlap.js\";\nimport { ParticlesBounce } from \"../Bounce/ParticlesBounce.js\";\nimport { setRangeValue } from \"../../../../Utils/NumberUtils.js\";\nexport class Collisions {\n  constructor() {\n    this.absorb = new CollisionsAbsorb();\n    this.bounce = new ParticlesBounce();\n    this.enable = false;\n    this.maxSpeed = 50;\n    this.mode = CollisionMode.bounce;\n    this.overlap = new CollisionsOverlap();\n  }\n  load(data) {\n    if (!data) {\n      return;\n    }\n    this.absorb.load(data.absorb);\n    this.bounce.load(data.bounce);\n    if (data.enable !== undefined) {\n      this.enable = data.enable;\n    }\n    if (data.maxSpeed !== undefined) {\n      this.maxSpeed = setRangeValue(data.maxSpeed);\n    }\n    if (data.mode !== undefined) {\n      this.mode = data.mode;\n    }\n    this.overlap.load(data.overlap);\n  }\n}", "map": {"version": 3, "names": ["CollisionMode", "CollisionsAbsorb", "CollisionsOverlap", "ParticlesBounce", "setRangeValue", "Collisions", "constructor", "absorb", "bounce", "enable", "maxSpeed", "mode", "overlap", "load", "data", "undefined"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/engine/browser/Options/Classes/Particles/Collisions/Collisions.js"], "sourcesContent": ["import { CollisionMode } from \"../../../../Enums/Modes/CollisionMode.js\";\nimport { CollisionsAbsorb } from \"./CollisionsAbsorb.js\";\nimport { CollisionsOverlap } from \"./CollisionsOverlap.js\";\nimport { ParticlesBounce } from \"../Bounce/ParticlesBounce.js\";\nimport { setRangeValue } from \"../../../../Utils/NumberUtils.js\";\nexport class Collisions {\n    constructor() {\n        this.absorb = new CollisionsAbsorb();\n        this.bounce = new ParticlesBounce();\n        this.enable = false;\n        this.maxSpeed = 50;\n        this.mode = CollisionMode.bounce;\n        this.overlap = new CollisionsOverlap();\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        this.absorb.load(data.absorb);\n        this.bounce.load(data.bounce);\n        if (data.enable !== undefined) {\n            this.enable = data.enable;\n        }\n        if (data.maxSpeed !== undefined) {\n            this.maxSpeed = setRangeValue(data.maxSpeed);\n        }\n        if (data.mode !== undefined) {\n            this.mode = data.mode;\n        }\n        this.overlap.load(data.overlap);\n    }\n}\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,0CAA0C;AACxE,SAASC,gBAAgB,QAAQ,uBAAuB;AACxD,SAASC,iBAAiB,QAAQ,wBAAwB;AAC1D,SAASC,eAAe,QAAQ,8BAA8B;AAC9D,SAASC,aAAa,QAAQ,kCAAkC;AAChE,OAAO,MAAMC,UAAU,CAAC;EACpBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,MAAM,GAAG,IAAIN,gBAAgB,CAAC,CAAC;IACpC,IAAI,CAACO,MAAM,GAAG,IAAIL,eAAe,CAAC,CAAC;IACnC,IAAI,CAACM,MAAM,GAAG,KAAK;IACnB,IAAI,CAACC,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACC,IAAI,GAAGX,aAAa,CAACQ,MAAM;IAChC,IAAI,CAACI,OAAO,GAAG,IAAIV,iBAAiB,CAAC,CAAC;EAC1C;EACAW,IAAIA,CAACC,IAAI,EAAE;IACP,IAAI,CAACA,IAAI,EAAE;MACP;IACJ;IACA,IAAI,CAACP,MAAM,CAACM,IAAI,CAACC,IAAI,CAACP,MAAM,CAAC;IAC7B,IAAI,CAACC,MAAM,CAACK,IAAI,CAACC,IAAI,CAACN,MAAM,CAAC;IAC7B,IAAIM,IAAI,CAACL,MAAM,KAAKM,SAAS,EAAE;MAC3B,IAAI,CAACN,MAAM,GAAGK,IAAI,CAACL,MAAM;IAC7B;IACA,IAAIK,IAAI,CAACJ,QAAQ,KAAKK,SAAS,EAAE;MAC7B,IAAI,CAACL,QAAQ,GAAGN,aAAa,CAACU,IAAI,CAACJ,QAAQ,CAAC;IAChD;IACA,IAAII,IAAI,CAACH,IAAI,KAAKI,SAAS,EAAE;MACzB,IAAI,CAACJ,IAAI,GAAGG,IAAI,CAACH,IAAI;IACzB;IACA,IAAI,CAACC,OAAO,CAACC,IAAI,CAACC,IAAI,CAACF,OAAO,CAAC;EACnC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
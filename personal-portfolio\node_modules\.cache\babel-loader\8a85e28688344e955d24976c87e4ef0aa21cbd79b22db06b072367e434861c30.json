{"ast": null, "code": "import { InteractorType } from \"../../Enums/Types/InteractorType.js\";\nexport class ParticlesInteractorBase {\n  constructor(container) {\n    this.type = InteractorType.particles;\n    this.container = container;\n  }\n}", "map": {"version": 3, "names": ["InteractorType", "ParticlesInteractorBase", "constructor", "container", "type", "particles"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/engine/browser/Core/Utils/ParticlesInteractorBase.js"], "sourcesContent": ["import { InteractorType } from \"../../Enums/Types/InteractorType.js\";\nexport class ParticlesInteractorBase {\n    constructor(container) {\n        this.type = InteractorType.particles;\n        this.container = container;\n    }\n}\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,qCAAqC;AACpE,OAAO,MAAMC,uBAAuB,CAAC;EACjCC,WAAWA,CAACC,SAAS,EAAE;IACnB,IAAI,CAACC,IAAI,GAAGJ,cAAc,CAACK,SAAS;IACpC,IAAI,CAACF,SAAS,GAAGA,SAAS;EAC9B;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
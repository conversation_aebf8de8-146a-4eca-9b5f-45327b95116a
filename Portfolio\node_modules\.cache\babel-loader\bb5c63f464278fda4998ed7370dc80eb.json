{"ast": null, "code": "var formatRelativeLocale = {\n  lastWeek: \"'last' eeee 'at' p\",\n  yesterday: \"'yesterday at' p\",\n  today: \"'today at' p\",\n  tomorrow: \"'tomorrow at' p\",\n  nextWeek: \"eeee 'at' p\",\n  other: 'P'\n};\n\nvar formatRelative = function (token, _date, _baseDate, _options) {\n  return formatRelativeLocale[token];\n};\n\nexport default formatRelative;", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/date-fns/esm/locale/en-US/_lib/formatRelative/index.js"], "names": ["formatRelativeLocale", "lastWeek", "yesterday", "today", "tomorrow", "nextWeek", "other", "formatRelative", "token", "_date", "_baseDate", "_options"], "mappings": "AAAA,IAAIA,oBAAoB,GAAG;AACzBC,EAAAA,QAAQ,EAAE,oBADe;AAEzBC,EAAAA,SAAS,EAAE,kBAFc;AAGzBC,EAAAA,KAAK,EAAE,cAHkB;AAIzBC,EAAAA,QAAQ,EAAE,iBAJe;AAKzBC,EAAAA,QAAQ,EAAE,aALe;AAMzBC,EAAAA,KAAK,EAAE;AANkB,CAA3B;;AASA,IAAIC,cAAc,GAAG,UAAUC,KAAV,EAAiBC,KAAjB,EAAwBC,SAAxB,EAAmCC,QAAnC,EAA6C;AAChE,SAAOX,oBAAoB,CAACQ,KAAD,CAA3B;AACD,CAFD;;AAIA,eAAeD,cAAf", "sourcesContent": ["var formatRelativeLocale = {\n  lastWeek: \"'last' eeee 'at' p\",\n  yesterday: \"'yesterday at' p\",\n  today: \"'today at' p\",\n  tomorrow: \"'tomorrow at' p\",\n  nextWeek: \"eeee 'at' p\",\n  other: 'P'\n};\n\nvar formatRelative = function (token, _date, _baseDate, _options) {\n  return formatRelativeLocale[token];\n};\n\nexport default formatRelative;"]}, "metadata": {}, "sourceType": "module"}
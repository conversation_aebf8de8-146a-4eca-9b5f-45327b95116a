{"ast": null, "code": "import { setRangeValue } from \"../../Utils\";\nexport class AnimationOptions {\n  constructor() {\n    this.count = 0;\n    this.enable = false;\n    this.speed = 1;\n    this.sync = false;\n  }\n\n  load(data) {\n    if (!data) {\n      return;\n    }\n\n    if (data.count !== undefined) {\n      this.count = setRangeValue(data.count);\n    }\n\n    if (data.enable !== undefined) {\n      this.enable = data.enable;\n    }\n\n    if (data.speed !== undefined) {\n      this.speed = setRangeValue(data.speed);\n    }\n\n    if (data.sync !== undefined) {\n      this.sync = data.sync;\n    }\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Options/Classes/AnimationOptions.js"], "names": ["setRangeValue", "AnimationOptions", "constructor", "count", "enable", "speed", "sync", "load", "data", "undefined"], "mappings": "AAAA,SAASA,aAAT,QAA8B,aAA9B;AACA,OAAO,MAAMC,gBAAN,CAAuB;AAC1BC,EAAAA,WAAW,GAAG;AACV,SAAKC,KAAL,GAAa,CAAb;AACA,SAAKC,MAAL,GAAc,KAAd;AACA,SAAKC,KAAL,GAAa,CAAb;AACA,SAAKC,IAAL,GAAY,KAAZ;AACH;;AACDC,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,QAAI,CAACA,IAAL,EAAW;AACP;AACH;;AACD,QAAIA,IAAI,CAACL,KAAL,KAAeM,SAAnB,EAA8B;AAC1B,WAAKN,KAAL,GAAaH,aAAa,CAACQ,IAAI,CAACL,KAAN,CAA1B;AACH;;AACD,QAAIK,IAAI,CAACJ,MAAL,KAAgBK,SAApB,EAA+B;AAC3B,WAAKL,MAAL,GAAcI,IAAI,CAACJ,MAAnB;AACH;;AACD,QAAII,IAAI,CAACH,KAAL,KAAeI,SAAnB,EAA8B;AAC1B,WAAKJ,KAAL,GAAaL,aAAa,CAACQ,IAAI,CAACH,KAAN,CAA1B;AACH;;AACD,QAAIG,IAAI,CAACF,IAAL,KAAcG,SAAlB,EAA6B;AACzB,WAAKH,IAAL,GAAYE,IAAI,CAACF,IAAjB;AACH;AACJ;;AAvByB", "sourcesContent": ["import { setRangeValue } from \"../../Utils\";\nexport class AnimationOptions {\n    constructor() {\n        this.count = 0;\n        this.enable = false;\n        this.speed = 1;\n        this.sync = false;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.count !== undefined) {\n            this.count = setRangeValue(data.count);\n        }\n        if (data.enable !== undefined) {\n            this.enable = data.enable;\n        }\n        if (data.speed !== undefined) {\n            this.speed = setRangeValue(data.speed);\n        }\n        if (data.sync !== undefined) {\n            this.sync = data.sync;\n        }\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
{"ast": null, "code": "import { SquareDrawer } from \"./SquareDrawer\";\nexport async function loadSquareShape(engine) {\n  const drawer = new SquareDrawer();\n  await engine.addShape(\"edge\", drawer);\n  await engine.addShape(\"square\", drawer);\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Shapes/Square/index.js"], "names": ["SquareDrawer", "loadSquareShape", "engine", "drawer", "addShape"], "mappings": "AAAA,SAASA,YAAT,QAA6B,gBAA7B;AACA,OAAO,eAAeC,eAAf,CAA+BC,MAA/B,EAAuC;AAC1C,QAAMC,MAAM,GAAG,IAAIH,YAAJ,EAAf;AACA,QAAME,MAAM,CAACE,QAAP,CAAgB,MAAhB,EAAwBD,MAAxB,CAAN;AACA,QAAMD,MAAM,CAACE,QAAP,CAAgB,QAAhB,EAA0BD,MAA1B,CAAN;AACH", "sourcesContent": ["import { SquareDrawer } from \"./SquareDrawer\";\nexport async function loadSquareShape(engine) {\n    const drawer = new SquareDrawer();\n    await engine.addShape(\"edge\", drawer);\n    await engine.addShape(\"square\", drawer);\n}\n"]}, "metadata": {}, "sourceType": "module"}
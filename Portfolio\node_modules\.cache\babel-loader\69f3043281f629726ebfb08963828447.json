{"ast": null, "code": "import { StrokeColorUpdater } from \"./StrokeColorUpdater\";\nexport async function loadStrokeColorUpdater(engine) {\n  await engine.addParticleUpdater(\"strokeColor\", container => new StrokeColorUpdater(container));\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Updaters/StrokeColor/index.js"], "names": ["StrokeColorUpdater", "loadStrokeColorUpdater", "engine", "addParticleUpdater", "container"], "mappings": "AAAA,SAASA,kBAAT,QAAmC,sBAAnC;AACA,OAAO,eAAeC,sBAAf,CAAsCC,MAAtC,EAA8C;AACjD,QAAMA,MAAM,CAACC,kBAAP,CAA0B,aAA1B,EAA0CC,SAAD,IAAe,IAAIJ,kBAAJ,CAAuBI,SAAvB,CAAxD,CAAN;AACH", "sourcesContent": ["import { StrokeColorUpdater } from \"./StrokeColorUpdater\";\nexport async function loadStrokeColorUpdater(engine) {\n    await engine.addParticleUpdater(\"strokeColor\", (container) => new StrokeColorUpdater(container));\n}\n"]}, "metadata": {}, "sourceType": "module"}
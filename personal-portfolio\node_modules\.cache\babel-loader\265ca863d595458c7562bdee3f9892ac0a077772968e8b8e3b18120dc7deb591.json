{"ast": null, "code": "import { OptionsColor } from \"../OptionsColor.js\";\nexport class Shadow {\n  constructor() {\n    this.blur = 0;\n    this.color = new OptionsColor();\n    this.enable = false;\n    this.offset = {\n      x: 0,\n      y: 0\n    };\n    this.color.value = \"#000\";\n  }\n  load(data) {\n    if (!data) {\n      return;\n    }\n    if (data.blur !== undefined) {\n      this.blur = data.blur;\n    }\n    this.color = OptionsColor.create(this.color, data.color);\n    if (data.enable !== undefined) {\n      this.enable = data.enable;\n    }\n    if (data.offset === undefined) {\n      return;\n    }\n    if (data.offset.x !== undefined) {\n      this.offset.x = data.offset.x;\n    }\n    if (data.offset.y !== undefined) {\n      this.offset.y = data.offset.y;\n    }\n  }\n}", "map": {"version": 3, "names": ["OptionsColor", "Shadow", "constructor", "blur", "color", "enable", "offset", "x", "y", "value", "load", "data", "undefined", "create"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/engine/browser/Options/Classes/Particles/Shadow.js"], "sourcesContent": ["import { OptionsColor } from \"../OptionsColor.js\";\nexport class Shadow {\n    constructor() {\n        this.blur = 0;\n        this.color = new OptionsColor();\n        this.enable = false;\n        this.offset = {\n            x: 0,\n            y: 0,\n        };\n        this.color.value = \"#000\";\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.blur !== undefined) {\n            this.blur = data.blur;\n        }\n        this.color = OptionsColor.create(this.color, data.color);\n        if (data.enable !== undefined) {\n            this.enable = data.enable;\n        }\n        if (data.offset === undefined) {\n            return;\n        }\n        if (data.offset.x !== undefined) {\n            this.offset.x = data.offset.x;\n        }\n        if (data.offset.y !== undefined) {\n            this.offset.y = data.offset.y;\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,oBAAoB;AACjD,OAAO,MAAMC,MAAM,CAAC;EAChBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,IAAI,GAAG,CAAC;IACb,IAAI,CAACC,KAAK,GAAG,IAAIJ,YAAY,CAAC,CAAC;IAC/B,IAAI,CAACK,MAAM,GAAG,KAAK;IACnB,IAAI,CAACC,MAAM,GAAG;MACVC,CAAC,EAAE,CAAC;MACJC,CAAC,EAAE;IACP,CAAC;IACD,IAAI,CAACJ,KAAK,CAACK,KAAK,GAAG,MAAM;EAC7B;EACAC,IAAIA,CAACC,IAAI,EAAE;IACP,IAAI,CAACA,IAAI,EAAE;MACP;IACJ;IACA,IAAIA,IAAI,CAACR,IAAI,KAAKS,SAAS,EAAE;MACzB,IAAI,CAACT,IAAI,GAAGQ,IAAI,CAACR,IAAI;IACzB;IACA,IAAI,CAACC,KAAK,GAAGJ,YAAY,CAACa,MAAM,CAAC,IAAI,CAACT,KAAK,EAAEO,IAAI,CAACP,KAAK,CAAC;IACxD,IAAIO,IAAI,CAACN,MAAM,KAAKO,SAAS,EAAE;MAC3B,IAAI,CAACP,MAAM,GAAGM,IAAI,CAACN,MAAM;IAC7B;IACA,IAAIM,IAAI,CAACL,MAAM,KAAKM,SAAS,EAAE;MAC3B;IACJ;IACA,IAAID,IAAI,CAACL,MAAM,CAACC,CAAC,KAAKK,SAAS,EAAE;MAC7B,IAAI,CAACN,MAAM,CAACC,CAAC,GAAGI,IAAI,CAACL,MAAM,CAACC,CAAC;IACjC;IACA,IAAII,IAAI,CAACL,MAAM,CAACE,CAAC,KAAKI,SAAS,EAAE;MAC7B,IAAI,CAACN,MAAM,CAACE,CAAC,GAAGG,IAAI,CAACL,MAAM,CAACE,CAAC;IACjC;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
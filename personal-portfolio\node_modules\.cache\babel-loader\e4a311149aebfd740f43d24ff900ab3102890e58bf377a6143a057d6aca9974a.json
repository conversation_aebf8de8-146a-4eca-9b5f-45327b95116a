{"ast": null, "code": "import { ValueWithRandom } from \"tsparticles-engine\";\nimport { RotateAnimation } from \"./RotateAnimation\";\nexport class Rotate extends ValueWithRandom {\n  constructor() {\n    super();\n    this.animation = new RotateAnimation();\n    this.direction = \"clockwise\";\n    this.path = false;\n    this.value = 0;\n  }\n  load(data) {\n    if (!data) {\n      return;\n    }\n    super.load(data);\n    if (data.direction !== undefined) {\n      this.direction = data.direction;\n    }\n    this.animation.load(data.animation);\n    if (data.path !== undefined) {\n      this.path = data.path;\n    }\n  }\n}", "map": {"version": 3, "names": ["ValueWithRandom", "RotateAnimation", "Rotate", "constructor", "animation", "direction", "path", "value", "load", "data", "undefined"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/tsparticles-updater-rotate/esm/Options/Classes/Rotate.js"], "sourcesContent": ["import { ValueWithRandom, } from \"tsparticles-engine\";\nimport { RotateAnimation } from \"./RotateAnimation\";\nexport class Rotate extends ValueWithRandom {\n    constructor() {\n        super();\n        this.animation = new RotateAnimation();\n        this.direction = \"clockwise\";\n        this.path = false;\n        this.value = 0;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        super.load(data);\n        if (data.direction !== undefined) {\n            this.direction = data.direction;\n        }\n        this.animation.load(data.animation);\n        if (data.path !== undefined) {\n            this.path = data.path;\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,eAAe,QAAS,oBAAoB;AACrD,SAASC,eAAe,QAAQ,mBAAmB;AACnD,OAAO,MAAMC,MAAM,SAASF,eAAe,CAAC;EACxCG,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,SAAS,GAAG,IAAIH,eAAe,CAAC,CAAC;IACtC,IAAI,CAACI,SAAS,GAAG,WAAW;IAC5B,IAAI,CAACC,IAAI,GAAG,KAAK;IACjB,IAAI,CAACC,KAAK,GAAG,CAAC;EAClB;EACAC,IAAIA,CAACC,IAAI,EAAE;IACP,IAAI,CAACA,IAAI,EAAE;MACP;IACJ;IACA,KAAK,CAACD,IAAI,CAACC,IAAI,CAAC;IAChB,IAAIA,IAAI,CAACJ,SAAS,KAAKK,SAAS,EAAE;MAC9B,IAAI,CAACL,SAAS,GAAGI,IAAI,CAACJ,SAAS;IACnC;IACA,IAAI,CAACD,SAAS,CAACI,IAAI,CAACC,IAAI,CAACL,SAAS,CAAC;IACnC,IAAIK,IAAI,CAACH,IAAI,KAAKI,SAAS,EAAE;MACzB,IAAI,CAACJ,IAAI,GAAGG,IAAI,CAACH,IAAI;IACzB;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
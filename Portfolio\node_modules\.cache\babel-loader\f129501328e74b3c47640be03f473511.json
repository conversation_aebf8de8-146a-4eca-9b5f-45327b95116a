{"ast": null, "code": "import { setRangeValue } from \"../../../../Utils\";\nexport class MoveAngle {\n  constructor() {\n    this.offset = 0;\n    this.value = 90;\n  }\n\n  load(data) {\n    if (data === undefined) {\n      return;\n    }\n\n    if (data.offset !== undefined) {\n      this.offset = setRangeValue(data.offset);\n    }\n\n    if (data.value !== undefined) {\n      this.value = setRangeValue(data.value);\n    }\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Options/Classes/Particles/Move/MoveAngle.js"], "names": ["setRangeValue", "MoveAngle", "constructor", "offset", "value", "load", "data", "undefined"], "mappings": "AAAA,SAASA,aAAT,QAA8B,mBAA9B;AACA,OAAO,MAAMC,SAAN,CAAgB;AACnBC,EAAAA,WAAW,GAAG;AACV,SAAKC,MAAL,GAAc,CAAd;AACA,SAAKC,KAAL,GAAa,EAAb;AACH;;AACDC,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,QAAIA,IAAI,KAAKC,SAAb,EAAwB;AACpB;AACH;;AACD,QAAID,IAAI,CAACH,MAAL,KAAgBI,SAApB,EAA+B;AAC3B,WAAKJ,MAAL,GAAcH,aAAa,CAACM,IAAI,CAACH,MAAN,CAA3B;AACH;;AACD,QAAIG,IAAI,CAACF,KAAL,KAAeG,SAAnB,EAA8B;AAC1B,WAAKH,KAAL,GAAaJ,aAAa,CAACM,IAAI,CAACF,KAAN,CAA1B;AACH;AACJ;;AAfkB", "sourcesContent": ["import { setRangeValue } from \"../../../../Utils\";\nexport class MoveAngle {\n    constructor() {\n        this.offset = 0;\n        this.value = 90;\n    }\n    load(data) {\n        if (data === undefined) {\n            return;\n        }\n        if (data.offset !== undefined) {\n            this.offset = setRangeValue(data.offset);\n        }\n        if (data.value !== undefined) {\n            this.value = setRangeValue(data.value);\n        }\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
{"ast": null, "code": "import { Vector } from \"./Vector\";\nexport class Vector3d extends Vector {\n  constructor(x, y, z) {\n    super(x, y);\n    this.z = z === undefined ? x.z : z;\n  }\n\n  static clone(source) {\n    return Vector3d.create(source.x, source.y, source.z);\n  }\n\n  static create(x, y, z) {\n    return new Vector3d(x, y, z);\n  }\n\n  add(v) {\n    return v instanceof Vector3d ? Vector3d.create(this.x + v.x, this.y + v.y, this.z + v.z) : super.add(v);\n  }\n\n  addTo(v) {\n    super.addTo(v);\n\n    if (v instanceof Vector3d) {\n      this.z += v.z;\n    }\n  }\n\n  sub(v) {\n    return v instanceof Vector3d ? Vector3d.create(this.x - v.x, this.y - v.y, this.z - v.z) : super.sub(v);\n  }\n\n  subFrom(v) {\n    super.subFrom(v);\n\n    if (v instanceof Vector3d) {\n      this.z -= v.z;\n    }\n  }\n\n  mult(n) {\n    return Vector3d.create(this.x * n, this.y * n, this.z * n);\n  }\n\n  multTo(n) {\n    super.multTo(n);\n    this.z *= n;\n  }\n\n  div(n) {\n    return Vector3d.create(this.x / n, this.y / n, this.z / n);\n  }\n\n  divTo(n) {\n    super.divTo(n);\n    this.z /= n;\n  }\n\n  copy() {\n    return Vector3d.clone(this);\n  }\n\n  setTo(v) {\n    super.setTo(v);\n\n    if (v instanceof Vector3d) {\n      this.z = v.z;\n    }\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Core/Utils/Vector3d.js"], "names": ["Vector", "Vector3d", "constructor", "x", "y", "z", "undefined", "clone", "source", "create", "add", "v", "addTo", "sub", "subFrom", "mult", "n", "multTo", "div", "divTo", "copy", "setTo"], "mappings": "AAAA,SAASA,MAAT,QAAuB,UAAvB;AACA,OAAO,MAAMC,QAAN,SAAuBD,MAAvB,CAA8B;AACjCE,EAAAA,WAAW,CAACC,CAAD,EAAIC,CAAJ,EAAOC,CAAP,EAAU;AACjB,UAAMF,CAAN,EAASC,CAAT;AACA,SAAKC,CAAL,GAASA,CAAC,KAAKC,SAAN,GAAkBH,CAAC,CAACE,CAApB,GAAwBA,CAAjC;AACH;;AACW,SAALE,KAAK,CAACC,MAAD,EAAS;AACjB,WAAOP,QAAQ,CAACQ,MAAT,CAAgBD,MAAM,CAACL,CAAvB,EAA0BK,MAAM,CAACJ,CAAjC,EAAoCI,MAAM,CAACH,CAA3C,CAAP;AACH;;AACY,SAANI,MAAM,CAACN,CAAD,EAAIC,CAAJ,EAAOC,CAAP,EAAU;AACnB,WAAO,IAAIJ,QAAJ,CAAaE,CAAb,EAAgBC,CAAhB,EAAmBC,CAAnB,CAAP;AACH;;AACDK,EAAAA,GAAG,CAACC,CAAD,EAAI;AACH,WAAOA,CAAC,YAAYV,QAAb,GAAwBA,QAAQ,CAACQ,MAAT,CAAgB,KAAKN,CAAL,GAASQ,CAAC,CAACR,CAA3B,EAA8B,KAAKC,CAAL,GAASO,CAAC,CAACP,CAAzC,EAA4C,KAAKC,CAAL,GAASM,CAAC,CAACN,CAAvD,CAAxB,GAAoF,MAAMK,GAAN,CAAUC,CAAV,CAA3F;AACH;;AACDC,EAAAA,KAAK,CAACD,CAAD,EAAI;AACL,UAAMC,KAAN,CAAYD,CAAZ;;AACA,QAAIA,CAAC,YAAYV,QAAjB,EAA2B;AACvB,WAAKI,CAAL,IAAUM,CAAC,CAACN,CAAZ;AACH;AACJ;;AACDQ,EAAAA,GAAG,CAACF,CAAD,EAAI;AACH,WAAOA,CAAC,YAAYV,QAAb,GAAwBA,QAAQ,CAACQ,MAAT,CAAgB,KAAKN,CAAL,GAASQ,CAAC,CAACR,CAA3B,EAA8B,KAAKC,CAAL,GAASO,CAAC,CAACP,CAAzC,EAA4C,KAAKC,CAAL,GAASM,CAAC,CAACN,CAAvD,CAAxB,GAAoF,MAAMQ,GAAN,CAAUF,CAAV,CAA3F;AACH;;AACDG,EAAAA,OAAO,CAACH,CAAD,EAAI;AACP,UAAMG,OAAN,CAAcH,CAAd;;AACA,QAAIA,CAAC,YAAYV,QAAjB,EAA2B;AACvB,WAAKI,CAAL,IAAUM,CAAC,CAACN,CAAZ;AACH;AACJ;;AACDU,EAAAA,IAAI,CAACC,CAAD,EAAI;AACJ,WAAOf,QAAQ,CAACQ,MAAT,CAAgB,KAAKN,CAAL,GAASa,CAAzB,EAA4B,KAAKZ,CAAL,GAASY,CAArC,EAAwC,KAAKX,CAAL,GAASW,CAAjD,CAAP;AACH;;AACDC,EAAAA,MAAM,CAACD,CAAD,EAAI;AACN,UAAMC,MAAN,CAAaD,CAAb;AACA,SAAKX,CAAL,IAAUW,CAAV;AACH;;AACDE,EAAAA,GAAG,CAACF,CAAD,EAAI;AACH,WAAOf,QAAQ,CAACQ,MAAT,CAAgB,KAAKN,CAAL,GAASa,CAAzB,EAA4B,KAAKZ,CAAL,GAASY,CAArC,EAAwC,KAAKX,CAAL,GAASW,CAAjD,CAAP;AACH;;AACDG,EAAAA,KAAK,CAACH,CAAD,EAAI;AACL,UAAMG,KAAN,CAAYH,CAAZ;AACA,SAAKX,CAAL,IAAUW,CAAV;AACH;;AACDI,EAAAA,IAAI,GAAG;AACH,WAAOnB,QAAQ,CAACM,KAAT,CAAe,IAAf,CAAP;AACH;;AACDc,EAAAA,KAAK,CAACV,CAAD,EAAI;AACL,UAAMU,KAAN,CAAYV,CAAZ;;AACA,QAAIA,CAAC,YAAYV,QAAjB,EAA2B;AACvB,WAAKI,CAAL,GAASM,CAAC,CAACN,CAAX;AACH;AACJ;;AAnDgC", "sourcesContent": ["import { Vector } from \"./Vector\";\nexport class Vector3d extends Vector {\n    constructor(x, y, z) {\n        super(x, y);\n        this.z = z === undefined ? x.z : z;\n    }\n    static clone(source) {\n        return Vector3d.create(source.x, source.y, source.z);\n    }\n    static create(x, y, z) {\n        return new Vector3d(x, y, z);\n    }\n    add(v) {\n        return v instanceof Vector3d ? Vector3d.create(this.x + v.x, this.y + v.y, this.z + v.z) : super.add(v);\n    }\n    addTo(v) {\n        super.addTo(v);\n        if (v instanceof Vector3d) {\n            this.z += v.z;\n        }\n    }\n    sub(v) {\n        return v instanceof Vector3d ? Vector3d.create(this.x - v.x, this.y - v.y, this.z - v.z) : super.sub(v);\n    }\n    subFrom(v) {\n        super.subFrom(v);\n        if (v instanceof Vector3d) {\n            this.z -= v.z;\n        }\n    }\n    mult(n) {\n        return Vector3d.create(this.x * n, this.y * n, this.z * n);\n    }\n    multTo(n) {\n        super.multTo(n);\n        this.z *= n;\n    }\n    div(n) {\n        return Vector3d.create(this.x / n, this.y / n, this.z / n);\n    }\n    divTo(n) {\n        super.divTo(n);\n        this.z /= n;\n    }\n    copy() {\n        return Vector3d.clone(this);\n    }\n    setTo(v) {\n        super.setTo(v);\n        if (v instanceof Vector3d) {\n            this.z = v.z;\n        }\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
{"ast": null, "code": "import { setRangeValue } from \"../../../../Utils\";\nexport class TiltAnimation {\n  constructor() {\n    this.enable = false;\n    this.speed = 0;\n    this.sync = false;\n  }\n\n  load(data) {\n    if (data === undefined) {\n      return;\n    }\n\n    if (data.enable !== undefined) {\n      this.enable = data.enable;\n    }\n\n    if (data.speed !== undefined) {\n      this.speed = setRangeValue(data.speed);\n    }\n\n    if (data.sync !== undefined) {\n      this.sync = data.sync;\n    }\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Options/Classes/Particles/Tilt/TiltAnimation.js"], "names": ["setRangeValue", "TiltAnimation", "constructor", "enable", "speed", "sync", "load", "data", "undefined"], "mappings": "AAAA,SAASA,aAAT,QAA8B,mBAA9B;AACA,OAAO,MAAMC,aAAN,CAAoB;AACvBC,EAAAA,WAAW,GAAG;AACV,SAAKC,MAAL,GAAc,KAAd;AACA,SAAKC,KAAL,GAAa,CAAb;AACA,SAAKC,IAAL,GAAY,KAAZ;AACH;;AACDC,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,QAAIA,IAAI,KAAKC,SAAb,EAAwB;AACpB;AACH;;AACD,QAAID,IAAI,CAACJ,MAAL,KAAgBK,SAApB,EAA+B;AAC3B,WAAKL,MAAL,GAAcI,IAAI,CAACJ,MAAnB;AACH;;AACD,QAAII,IAAI,CAACH,KAAL,KAAeI,SAAnB,EAA8B;AAC1B,WAAKJ,KAAL,GAAaJ,aAAa,CAACO,IAAI,CAACH,KAAN,CAA1B;AACH;;AACD,QAAIG,IAAI,CAACF,IAAL,KAAcG,SAAlB,EAA6B;AACzB,WAAKH,IAAL,GAAYE,IAAI,CAACF,IAAjB;AACH;AACJ;;AAnBsB", "sourcesContent": ["import { setRangeValue } from \"../../../../Utils\";\nexport class TiltAnimation {\n    constructor() {\n        this.enable = false;\n        this.speed = 0;\n        this.sync = false;\n    }\n    load(data) {\n        if (data === undefined) {\n            return;\n        }\n        if (data.enable !== undefined) {\n            this.enable = data.enable;\n        }\n        if (data.speed !== undefined) {\n            this.speed = setRangeValue(data.speed);\n        }\n        if (data.sync !== undefined) {\n            this.sync = data.sync;\n        }\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
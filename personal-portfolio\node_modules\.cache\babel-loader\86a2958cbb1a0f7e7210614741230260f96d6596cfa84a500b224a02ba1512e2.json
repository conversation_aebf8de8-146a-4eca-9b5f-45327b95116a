{"ast": null, "code": "import { getRandom, percentDenominator, updateAnimation } from \"@tsparticles/engine\";\nconst minLoops = 0;\nexport class SizeUpdater {\n  init(particle) {\n    const container = particle.container,\n      sizeOptions = particle.options.size,\n      sizeAnimation = sizeOptions.animation;\n    if (sizeAnimation.enable) {\n      particle.size.velocity = (particle.retina.sizeAnimationSpeed ?? container.retina.sizeAnimationSpeed) / percentDenominator * container.retina.reduceFactor;\n      if (!sizeAnimation.sync) {\n        particle.size.velocity *= getRandom();\n      }\n    }\n  }\n  isEnabled(particle) {\n    return !particle.destroyed && !particle.spawning && particle.size.enable && ((particle.size.maxLoops ?? minLoops) <= minLoops || (particle.size.maxLoops ?? minLoops) > minLoops && (particle.size.loops ?? minLoops) < (particle.size.maxLoops ?? minLoops));\n  }\n  reset(particle) {\n    particle.size.loops = minLoops;\n  }\n  update(particle, delta) {\n    if (!this.isEnabled(particle)) {\n      return;\n    }\n    updateAnimation(particle, particle.size, true, particle.options.size.animation.destroy, delta);\n  }\n}", "map": {"version": 3, "names": ["getRandom", "percentDenominator", "updateAnimation", "minLoops", "SizeUpdater", "init", "particle", "container", "sizeOptions", "options", "size", "sizeAnimation", "animation", "enable", "velocity", "retina", "sizeAnimationSpeed", "reduceFactor", "sync", "isEnabled", "destroyed", "spawning", "max<PERSON><PERSON>s", "loops", "reset", "update", "delta", "destroy"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/updater-size/browser/SizeUpdater.js"], "sourcesContent": ["import { getRandom, percentDenominator, updateAnimation, } from \"@tsparticles/engine\";\nconst minLoops = 0;\nexport class SizeUpdater {\n    init(particle) {\n        const container = particle.container, sizeOptions = particle.options.size, sizeAnimation = sizeOptions.animation;\n        if (sizeAnimation.enable) {\n            particle.size.velocity =\n                ((particle.retina.sizeAnimationSpeed ?? container.retina.sizeAnimationSpeed) / percentDenominator) *\n                    container.retina.reduceFactor;\n            if (!sizeAnimation.sync) {\n                particle.size.velocity *= getRandom();\n            }\n        }\n    }\n    isEnabled(particle) {\n        return (!particle.destroyed &&\n            !particle.spawning &&\n            particle.size.enable &&\n            ((particle.size.maxLoops ?? minLoops) <= minLoops ||\n                ((particle.size.maxLoops ?? minLoops) > minLoops &&\n                    (particle.size.loops ?? minLoops) < (particle.size.maxLoops ?? minLoops))));\n    }\n    reset(particle) {\n        particle.size.loops = minLoops;\n    }\n    update(particle, delta) {\n        if (!this.isEnabled(particle)) {\n            return;\n        }\n        updateAnimation(particle, particle.size, true, particle.options.size.animation.destroy, delta);\n    }\n}\n"], "mappings": "AAAA,SAASA,SAAS,EAAEC,kBAAkB,EAAEC,eAAe,QAAS,qBAAqB;AACrF,MAAMC,QAAQ,GAAG,CAAC;AAClB,OAAO,MAAMC,WAAW,CAAC;EACrBC,IAAIA,CAACC,QAAQ,EAAE;IACX,MAAMC,SAAS,GAAGD,QAAQ,CAACC,SAAS;MAAEC,WAAW,GAAGF,QAAQ,CAACG,OAAO,CAACC,IAAI;MAAEC,aAAa,GAAGH,WAAW,CAACI,SAAS;IAChH,IAAID,aAAa,CAACE,MAAM,EAAE;MACtBP,QAAQ,CAACI,IAAI,CAACI,QAAQ,GACjB,CAACR,QAAQ,CAACS,MAAM,CAACC,kBAAkB,IAAIT,SAAS,CAACQ,MAAM,CAACC,kBAAkB,IAAIf,kBAAkB,GAC7FM,SAAS,CAACQ,MAAM,CAACE,YAAY;MACrC,IAAI,CAACN,aAAa,CAACO,IAAI,EAAE;QACrBZ,QAAQ,CAACI,IAAI,CAACI,QAAQ,IAAId,SAAS,CAAC,CAAC;MACzC;IACJ;EACJ;EACAmB,SAASA,CAACb,QAAQ,EAAE;IAChB,OAAQ,CAACA,QAAQ,CAACc,SAAS,IACvB,CAACd,QAAQ,CAACe,QAAQ,IAClBf,QAAQ,CAACI,IAAI,CAACG,MAAM,KACnB,CAACP,QAAQ,CAACI,IAAI,CAACY,QAAQ,IAAInB,QAAQ,KAAKA,QAAQ,IAC5C,CAACG,QAAQ,CAACI,IAAI,CAACY,QAAQ,IAAInB,QAAQ,IAAIA,QAAQ,IAC5C,CAACG,QAAQ,CAACI,IAAI,CAACa,KAAK,IAAIpB,QAAQ,KAAKG,QAAQ,CAACI,IAAI,CAACY,QAAQ,IAAInB,QAAQ,CAAE,CAAC;EAC1F;EACAqB,KAAKA,CAAClB,QAAQ,EAAE;IACZA,QAAQ,CAACI,IAAI,CAACa,KAAK,GAAGpB,QAAQ;EAClC;EACAsB,MAAMA,CAACnB,QAAQ,EAAEoB,KAAK,EAAE;IACpB,IAAI,CAAC,IAAI,CAACP,SAAS,CAACb,QAAQ,CAAC,EAAE;MAC3B;IACJ;IACAJ,eAAe,CAACI,QAAQ,EAAEA,QAAQ,CAACI,IAAI,EAAE,IAAI,EAAEJ,QAAQ,CAACG,OAAO,CAACC,IAAI,CAACE,SAAS,CAACe,OAAO,EAAED,KAAK,CAAC;EAClG;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
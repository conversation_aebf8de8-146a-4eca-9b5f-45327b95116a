{"ast": null, "code": "import { Circle, Constants, ExternalInteractorBase, Rectangle, Vector } from \"../../../Core\";\nimport { calcEasing, clamp, divMode, divModeExecute, getDistances, isDivModeEnabled, isInArray } from \"../../../Utils\";\nexport class Repulser extends ExternalInteractorBase {\n  constructor(container) {\n    super(container);\n  }\n\n  isEnabled() {\n    const container = this.container,\n          options = container.actualOptions,\n          mouse = container.interactivity.mouse,\n          events = options.interactivity.events,\n          divs = events.onDiv,\n          divRepulse = isDivModeEnabled(\"repulse\", divs);\n\n    if (!(divRepulse || events.onHover.enable && mouse.position || events.onClick.enable && mouse.clickPosition)) {\n      return false;\n    }\n\n    const hoverMode = events.onHover.mode,\n          clickMode = events.onClick.mode;\n    return isInArray(\"repulse\", hoverMode) || isInArray(\"repulse\", clickMode) || divRepulse;\n  }\n\n  reset() {}\n\n  async interact() {\n    const container = this.container,\n          options = container.actualOptions,\n          mouseMoveStatus = container.interactivity.status === Constants.mouseMoveEvent,\n          events = options.interactivity.events,\n          hoverEnabled = events.onHover.enable,\n          hoverMode = events.onHover.mode,\n          clickEnabled = events.onClick.enable,\n          clickMode = events.onClick.mode,\n          divs = events.onDiv;\n\n    if (mouseMoveStatus && hoverEnabled && isInArray(\"repulse\", hoverMode)) {\n      this.hoverRepulse();\n    } else if (clickEnabled && isInArray(\"repulse\", clickMode)) {\n      this.clickRepulse();\n    } else {\n      divModeExecute(\"repulse\", divs, (selector, div) => this.singleSelectorRepulse(selector, div));\n    }\n  }\n\n  singleSelectorRepulse(selector, div) {\n    const container = this.container,\n          query = document.querySelectorAll(selector);\n\n    if (!query.length) {\n      return;\n    }\n\n    query.forEach(item => {\n      const elem = item,\n            pxRatio = container.retina.pixelRatio,\n            pos = {\n        x: (elem.offsetLeft + elem.offsetWidth / 2) * pxRatio,\n        y: (elem.offsetTop + elem.offsetHeight / 2) * pxRatio\n      },\n            repulseRadius = elem.offsetWidth / 2 * pxRatio,\n            area = div.type === \"circle\" ? new Circle(pos.x, pos.y, repulseRadius) : new Rectangle(elem.offsetLeft * pxRatio, elem.offsetTop * pxRatio, elem.offsetWidth * pxRatio, elem.offsetHeight * pxRatio),\n            divs = container.actualOptions.interactivity.modes.repulse.divs,\n            divRepulse = divMode(divs, elem);\n      this.processRepulse(pos, repulseRadius, area, divRepulse);\n    });\n  }\n\n  hoverRepulse() {\n    const container = this.container,\n          mousePos = container.interactivity.mouse.position;\n\n    if (!mousePos) {\n      return;\n    }\n\n    const repulseRadius = container.retina.repulseModeDistance;\n    this.processRepulse(mousePos, repulseRadius, new Circle(mousePos.x, mousePos.y, repulseRadius));\n  }\n\n  processRepulse(position, repulseRadius, area, divRepulse) {\n    var _a;\n\n    const container = this.container,\n          query = container.particles.quadTree.query(area),\n          repulseOptions = container.actualOptions.interactivity.modes.repulse;\n\n    for (const particle of query) {\n      const {\n        dx,\n        dy,\n        distance\n      } = getDistances(particle.position, position),\n            velocity = ((_a = divRepulse === null || divRepulse === void 0 ? void 0 : divRepulse.speed) !== null && _a !== void 0 ? _a : repulseOptions.speed) * repulseOptions.factor,\n            repulseFactor = clamp(calcEasing(1 - distance / repulseRadius, repulseOptions.easing) * velocity, 0, repulseOptions.maxSpeed),\n            normVec = Vector.create(distance === 0 ? velocity : dx / distance * repulseFactor, distance === 0 ? velocity : dy / distance * repulseFactor);\n      particle.position.addTo(normVec);\n    }\n  }\n\n  clickRepulse() {\n    const container = this.container;\n\n    if (!container.repulse.finish) {\n      if (!container.repulse.count) {\n        container.repulse.count = 0;\n      }\n\n      container.repulse.count++;\n\n      if (container.repulse.count === container.particles.count) {\n        container.repulse.finish = true;\n      }\n    }\n\n    if (container.repulse.clicking) {\n      const repulseDistance = container.retina.repulseModeDistance,\n            repulseRadius = Math.pow(repulseDistance / 6, 3),\n            mouseClickPos = container.interactivity.mouse.clickPosition;\n\n      if (mouseClickPos === undefined) {\n        return;\n      }\n\n      const range = new Circle(mouseClickPos.x, mouseClickPos.y, repulseRadius),\n            query = container.particles.quadTree.query(range);\n\n      for (const particle of query) {\n        const {\n          dx,\n          dy,\n          distance\n        } = getDistances(mouseClickPos, particle.position),\n              d = distance ** 2,\n              velocity = container.actualOptions.interactivity.modes.repulse.speed,\n              force = -repulseRadius * velocity / d;\n\n        if (d <= repulseRadius) {\n          container.repulse.particles.push(particle);\n          const vect = Vector.create(dx, dy);\n          vect.length = force;\n          particle.velocity.setTo(vect);\n        }\n      }\n    } else if (container.repulse.clicking === false) {\n      for (const particle of container.repulse.particles) {\n        particle.velocity.setTo(particle.initialVelocity);\n      }\n\n      container.repulse.particles = [];\n    }\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Interactions/External/Repulse/Repulser.js"], "names": ["Circle", "Constants", "ExternalInteractorBase", "Rectangle", "Vector", "calcEasing", "clamp", "divMode", "divModeExecute", "getDistances", "isDivModeEnabled", "isInArray", "<PERSON><PERSON><PERSON>", "constructor", "container", "isEnabled", "options", "actualOptions", "mouse", "interactivity", "events", "divs", "onDiv", "divRepulse", "onHover", "enable", "position", "onClick", "clickPosition", "hoverMode", "mode", "clickMode", "reset", "interact", "mouseMoveStatus", "status", "mouseMoveEvent", "hoverEnabled", "clickEnabled", "hoverRepulse", "clickRepulse", "selector", "div", "singleSelectorRepulse", "query", "document", "querySelectorAll", "length", "for<PERSON>ach", "item", "elem", "pxRatio", "retina", "pixelRatio", "pos", "x", "offsetLeft", "offsetWidth", "y", "offsetTop", "offsetHeight", "repulseRadius", "area", "type", "modes", "repulse", "processRepulse", "mousePos", "repulseModeDistance", "_a", "particles", "quadTree", "repulseOptions", "particle", "dx", "dy", "distance", "velocity", "speed", "factor", "repulseFactor", "easing", "maxSpeed", "normVec", "create", "addTo", "finish", "count", "clicking", "repulseDistance", "Math", "pow", "mouseClickPos", "undefined", "range", "d", "force", "push", "vect", "setTo", "initialVelocity"], "mappings": "AAAA,SAASA,MAAT,EAAiBC,SAAjB,EAA4BC,sBAA5B,EAAoDC,SAApD,EAA+DC,MAA/D,QAA6E,eAA7E;AACA,SAASC,UAAT,EAAqBC,KAArB,EAA4BC,OAA5B,EAAqCC,cAArC,EAAqDC,YAArD,EAAmEC,gBAAnE,EAAqFC,SAArF,QAAsG,gBAAtG;AACA,OAAO,MAAMC,QAAN,SAAuBV,sBAAvB,CAA8C;AACjDW,EAAAA,WAAW,CAACC,SAAD,EAAY;AACnB,UAAMA,SAAN;AACH;;AACDC,EAAAA,SAAS,GAAG;AACR,UAAMD,SAAS,GAAG,KAAKA,SAAvB;AAAA,UAAkCE,OAAO,GAAGF,SAAS,CAACG,aAAtD;AAAA,UAAqEC,KAAK,GAAGJ,SAAS,CAACK,aAAV,CAAwBD,KAArG;AAAA,UAA4GE,MAAM,GAAGJ,OAAO,CAACG,aAAR,CAAsBC,MAA3I;AAAA,UAAmJC,IAAI,GAAGD,MAAM,CAACE,KAAjK;AAAA,UAAwKC,UAAU,GAAGb,gBAAgB,CAAC,SAAD,EAAYW,IAAZ,CAArM;;AACA,QAAI,EAAEE,UAAU,IAAKH,MAAM,CAACI,OAAP,CAAeC,MAAf,IAAyBP,KAAK,CAACQ,QAA9C,IAA4DN,MAAM,CAACO,OAAP,CAAeF,MAAf,IAAyBP,KAAK,CAACU,aAA7F,CAAJ,EAAkH;AAC9G,aAAO,KAAP;AACH;;AACD,UAAMC,SAAS,GAAGT,MAAM,CAACI,OAAP,CAAeM,IAAjC;AAAA,UAAuCC,SAAS,GAAGX,MAAM,CAACO,OAAP,CAAeG,IAAlE;AACA,WAAOnB,SAAS,CAAC,SAAD,EAAYkB,SAAZ,CAAT,IAAmClB,SAAS,CAAC,SAAD,EAAYoB,SAAZ,CAA5C,IAAsER,UAA7E;AACH;;AACDS,EAAAA,KAAK,GAAG,CACP;;AACa,QAARC,QAAQ,GAAG;AACb,UAAMnB,SAAS,GAAG,KAAKA,SAAvB;AAAA,UAAkCE,OAAO,GAAGF,SAAS,CAACG,aAAtD;AAAA,UAAqEiB,eAAe,GAAGpB,SAAS,CAACK,aAAV,CAAwBgB,MAAxB,KAAmClC,SAAS,CAACmC,cAApI;AAAA,UAAoJhB,MAAM,GAAGJ,OAAO,CAACG,aAAR,CAAsBC,MAAnL;AAAA,UAA2LiB,YAAY,GAAGjB,MAAM,CAACI,OAAP,CAAeC,MAAzN;AAAA,UAAiOI,SAAS,GAAGT,MAAM,CAACI,OAAP,CAAeM,IAA5P;AAAA,UAAkQQ,YAAY,GAAGlB,MAAM,CAACO,OAAP,CAAeF,MAAhS;AAAA,UAAwSM,SAAS,GAAGX,MAAM,CAACO,OAAP,CAAeG,IAAnU;AAAA,UAAyUT,IAAI,GAAGD,MAAM,CAACE,KAAvV;;AACA,QAAIY,eAAe,IAAIG,YAAnB,IAAmC1B,SAAS,CAAC,SAAD,EAAYkB,SAAZ,CAAhD,EAAwE;AACpE,WAAKU,YAAL;AACH,KAFD,MAGK,IAAID,YAAY,IAAI3B,SAAS,CAAC,SAAD,EAAYoB,SAAZ,CAA7B,EAAqD;AACtD,WAAKS,YAAL;AACH,KAFI,MAGA;AACDhC,MAAAA,cAAc,CAAC,SAAD,EAAYa,IAAZ,EAAkB,CAACoB,QAAD,EAAWC,GAAX,KAAmB,KAAKC,qBAAL,CAA2BF,QAA3B,EAAqCC,GAArC,CAArC,CAAd;AACH;AACJ;;AACDC,EAAAA,qBAAqB,CAACF,QAAD,EAAWC,GAAX,EAAgB;AACjC,UAAM5B,SAAS,GAAG,KAAKA,SAAvB;AAAA,UAAkC8B,KAAK,GAAGC,QAAQ,CAACC,gBAAT,CAA0BL,QAA1B,CAA1C;;AACA,QAAI,CAACG,KAAK,CAACG,MAAX,EAAmB;AACf;AACH;;AACDH,IAAAA,KAAK,CAACI,OAAN,CAAeC,IAAD,IAAU;AACpB,YAAMC,IAAI,GAAGD,IAAb;AAAA,YAAmBE,OAAO,GAAGrC,SAAS,CAACsC,MAAV,CAAiBC,UAA9C;AAAA,YAA0DC,GAAG,GAAG;AAC5DC,QAAAA,CAAC,EAAE,CAACL,IAAI,CAACM,UAAL,GAAkBN,IAAI,CAACO,WAAL,GAAmB,CAAtC,IAA2CN,OADc;AAE5DO,QAAAA,CAAC,EAAE,CAACR,IAAI,CAACS,SAAL,GAAiBT,IAAI,CAACU,YAAL,GAAoB,CAAtC,IAA2CT;AAFc,OAAhE;AAAA,YAGGU,aAAa,GAAIX,IAAI,CAACO,WAAL,GAAmB,CAApB,GAAyBN,OAH5C;AAAA,YAGqDW,IAAI,GAAGpB,GAAG,CAACqB,IAAJ,KAAa,QAAb,GACtD,IAAI/D,MAAJ,CAAWsD,GAAG,CAACC,CAAf,EAAkBD,GAAG,CAACI,CAAtB,EAAyBG,aAAzB,CADsD,GAEtD,IAAI1D,SAAJ,CAAc+C,IAAI,CAACM,UAAL,GAAkBL,OAAhC,EAAyCD,IAAI,CAACS,SAAL,GAAiBR,OAA1D,EAAmED,IAAI,CAACO,WAAL,GAAmBN,OAAtF,EAA+FD,IAAI,CAACU,YAAL,GAAoBT,OAAnH,CALN;AAAA,YAKmI9B,IAAI,GAAGP,SAAS,CAACG,aAAV,CAAwBE,aAAxB,CAAsC6C,KAAtC,CAA4CC,OAA5C,CAAoD5C,IAL9L;AAAA,YAKoME,UAAU,GAAGhB,OAAO,CAACc,IAAD,EAAO6B,IAAP,CALxN;AAMA,WAAKgB,cAAL,CAAoBZ,GAApB,EAAyBO,aAAzB,EAAwCC,IAAxC,EAA8CvC,UAA9C;AACH,KARD;AASH;;AACDgB,EAAAA,YAAY,GAAG;AACX,UAAMzB,SAAS,GAAG,KAAKA,SAAvB;AAAA,UAAkCqD,QAAQ,GAAGrD,SAAS,CAACK,aAAV,CAAwBD,KAAxB,CAA8BQ,QAA3E;;AACA,QAAI,CAACyC,QAAL,EAAe;AACX;AACH;;AACD,UAAMN,aAAa,GAAG/C,SAAS,CAACsC,MAAV,CAAiBgB,mBAAvC;AACA,SAAKF,cAAL,CAAoBC,QAApB,EAA8BN,aAA9B,EAA6C,IAAI7D,MAAJ,CAAWmE,QAAQ,CAACZ,CAApB,EAAuBY,QAAQ,CAACT,CAAhC,EAAmCG,aAAnC,CAA7C;AACH;;AACDK,EAAAA,cAAc,CAACxC,QAAD,EAAWmC,aAAX,EAA0BC,IAA1B,EAAgCvC,UAAhC,EAA4C;AACtD,QAAI8C,EAAJ;;AACA,UAAMvD,SAAS,GAAG,KAAKA,SAAvB;AAAA,UAAkC8B,KAAK,GAAG9B,SAAS,CAACwD,SAAV,CAAoBC,QAApB,CAA6B3B,KAA7B,CAAmCkB,IAAnC,CAA1C;AAAA,UAAoFU,cAAc,GAAG1D,SAAS,CAACG,aAAV,CAAwBE,aAAxB,CAAsC6C,KAAtC,CAA4CC,OAAjJ;;AACA,SAAK,MAAMQ,QAAX,IAAuB7B,KAAvB,EAA8B;AAC1B,YAAM;AAAE8B,QAAAA,EAAF;AAAMC,QAAAA,EAAN;AAAUC,QAAAA;AAAV,UAAuBnE,YAAY,CAACgE,QAAQ,CAAC/C,QAAV,EAAoBA,QAApB,CAAzC;AAAA,YAAwEmD,QAAQ,GAAG,CAAC,CAACR,EAAE,GAAG9C,UAAU,KAAK,IAAf,IAAuBA,UAAU,KAAK,KAAK,CAA3C,GAA+C,KAAK,CAApD,GAAwDA,UAAU,CAACuD,KAAzE,MAAoF,IAApF,IAA4FT,EAAE,KAAK,KAAK,CAAxG,GAA4GA,EAA5G,GAAiHG,cAAc,CAACM,KAAjI,IAA0IN,cAAc,CAACO,MAA5O;AAAA,YAAoPC,aAAa,GAAG1E,KAAK,CAACD,UAAU,CAAC,IAAIuE,QAAQ,GAAGf,aAAhB,EAA+BW,cAAc,CAACS,MAA9C,CAAV,GAAkEJ,QAAnE,EAA6E,CAA7E,EAAgFL,cAAc,CAACU,QAA/F,CAAzQ;AAAA,YAAmXC,OAAO,GAAG/E,MAAM,CAACgF,MAAP,CAAcR,QAAQ,KAAK,CAAb,GAAiBC,QAAjB,GAA6BH,EAAE,GAAGE,QAAN,GAAkBI,aAA5D,EAA2EJ,QAAQ,KAAK,CAAb,GAAiBC,QAAjB,GAA6BF,EAAE,GAAGC,QAAN,GAAkBI,aAAzH,CAA7X;AACAP,MAAAA,QAAQ,CAAC/C,QAAT,CAAkB2D,KAAlB,CAAwBF,OAAxB;AACH;AACJ;;AACD3C,EAAAA,YAAY,GAAG;AACX,UAAM1B,SAAS,GAAG,KAAKA,SAAvB;;AACA,QAAI,CAACA,SAAS,CAACmD,OAAV,CAAkBqB,MAAvB,EAA+B;AAC3B,UAAI,CAACxE,SAAS,CAACmD,OAAV,CAAkBsB,KAAvB,EAA8B;AAC1BzE,QAAAA,SAAS,CAACmD,OAAV,CAAkBsB,KAAlB,GAA0B,CAA1B;AACH;;AACDzE,MAAAA,SAAS,CAACmD,OAAV,CAAkBsB,KAAlB;;AACA,UAAIzE,SAAS,CAACmD,OAAV,CAAkBsB,KAAlB,KAA4BzE,SAAS,CAACwD,SAAV,CAAoBiB,KAApD,EAA2D;AACvDzE,QAAAA,SAAS,CAACmD,OAAV,CAAkBqB,MAAlB,GAA2B,IAA3B;AACH;AACJ;;AACD,QAAIxE,SAAS,CAACmD,OAAV,CAAkBuB,QAAtB,EAAgC;AAC5B,YAAMC,eAAe,GAAG3E,SAAS,CAACsC,MAAV,CAAiBgB,mBAAzC;AAAA,YAA8DP,aAAa,GAAG6B,IAAI,CAACC,GAAL,CAASF,eAAe,GAAG,CAA3B,EAA8B,CAA9B,CAA9E;AAAA,YAAgHG,aAAa,GAAG9E,SAAS,CAACK,aAAV,CAAwBD,KAAxB,CAA8BU,aAA9J;;AACA,UAAIgE,aAAa,KAAKC,SAAtB,EAAiC;AAC7B;AACH;;AACD,YAAMC,KAAK,GAAG,IAAI9F,MAAJ,CAAW4F,aAAa,CAACrC,CAAzB,EAA4BqC,aAAa,CAAClC,CAA1C,EAA6CG,aAA7C,CAAd;AAAA,YAA2EjB,KAAK,GAAG9B,SAAS,CAACwD,SAAV,CAAoBC,QAApB,CAA6B3B,KAA7B,CAAmCkD,KAAnC,CAAnF;;AACA,WAAK,MAAMrB,QAAX,IAAuB7B,KAAvB,EAA8B;AAC1B,cAAM;AAAE8B,UAAAA,EAAF;AAAMC,UAAAA,EAAN;AAAUC,UAAAA;AAAV,YAAuBnE,YAAY,CAACmF,aAAD,EAAgBnB,QAAQ,CAAC/C,QAAzB,CAAzC;AAAA,cAA6EqE,CAAC,GAAGnB,QAAQ,IAAI,CAA7F;AAAA,cAAgGC,QAAQ,GAAG/D,SAAS,CAACG,aAAV,CAAwBE,aAAxB,CAAsC6C,KAAtC,CAA4CC,OAA5C,CAAoDa,KAA/J;AAAA,cAAsKkB,KAAK,GAAI,CAACnC,aAAD,GAAiBgB,QAAlB,GAA8BkB,CAA5M;;AACA,YAAIA,CAAC,IAAIlC,aAAT,EAAwB;AACpB/C,UAAAA,SAAS,CAACmD,OAAV,CAAkBK,SAAlB,CAA4B2B,IAA5B,CAAiCxB,QAAjC;AACA,gBAAMyB,IAAI,GAAG9F,MAAM,CAACgF,MAAP,CAAcV,EAAd,EAAkBC,EAAlB,CAAb;AACAuB,UAAAA,IAAI,CAACnD,MAAL,GAAciD,KAAd;AACAvB,UAAAA,QAAQ,CAACI,QAAT,CAAkBsB,KAAlB,CAAwBD,IAAxB;AACH;AACJ;AACJ,KAfD,MAgBK,IAAIpF,SAAS,CAACmD,OAAV,CAAkBuB,QAAlB,KAA+B,KAAnC,EAA0C;AAC3C,WAAK,MAAMf,QAAX,IAAuB3D,SAAS,CAACmD,OAAV,CAAkBK,SAAzC,EAAoD;AAChDG,QAAAA,QAAQ,CAACI,QAAT,CAAkBsB,KAAlB,CAAwB1B,QAAQ,CAAC2B,eAAjC;AACH;;AACDtF,MAAAA,SAAS,CAACmD,OAAV,CAAkBK,SAAlB,GAA8B,EAA9B;AACH;AACJ;;AA1FgD", "sourcesContent": ["import { Circle, Constants, ExternalInteractorBase, Rectangle, Vector } from \"../../../Core\";\nimport { calcEasing, clamp, divMode, divModeExecute, getDistances, isDivModeEnabled, isInArray } from \"../../../Utils\";\nexport class Repulser extends ExternalInteractorBase {\n    constructor(container) {\n        super(container);\n    }\n    isEnabled() {\n        const container = this.container, options = container.actualOptions, mouse = container.interactivity.mouse, events = options.interactivity.events, divs = events.onDiv, divRepulse = isDivModeEnabled(\"repulse\", divs);\n        if (!(divRepulse || (events.onHover.enable && mouse.position) || (events.onClick.enable && mouse.clickPosition))) {\n            return false;\n        }\n        const hoverMode = events.onHover.mode, clickMode = events.onClick.mode;\n        return isInArray(\"repulse\", hoverMode) || isInArray(\"repulse\", clickMode) || divRepulse;\n    }\n    reset() {\n    }\n    async interact() {\n        const container = this.container, options = container.actualOptions, mouseMoveStatus = container.interactivity.status === Constants.mouseMoveEvent, events = options.interactivity.events, hoverEnabled = events.onHover.enable, hoverMode = events.onHover.mode, clickEnabled = events.onClick.enable, clickMode = events.onClick.mode, divs = events.onDiv;\n        if (mouseMoveStatus && hoverEnabled && isInArray(\"repulse\", hoverMode)) {\n            this.hoverRepulse();\n        }\n        else if (clickEnabled && isInArray(\"repulse\", clickMode)) {\n            this.clickRepulse();\n        }\n        else {\n            divModeExecute(\"repulse\", divs, (selector, div) => this.singleSelectorRepulse(selector, div));\n        }\n    }\n    singleSelectorRepulse(selector, div) {\n        const container = this.container, query = document.querySelectorAll(selector);\n        if (!query.length) {\n            return;\n        }\n        query.forEach((item) => {\n            const elem = item, pxRatio = container.retina.pixelRatio, pos = {\n                x: (elem.offsetLeft + elem.offsetWidth / 2) * pxRatio,\n                y: (elem.offsetTop + elem.offsetHeight / 2) * pxRatio,\n            }, repulseRadius = (elem.offsetWidth / 2) * pxRatio, area = div.type === \"circle\"\n                ? new Circle(pos.x, pos.y, repulseRadius)\n                : new Rectangle(elem.offsetLeft * pxRatio, elem.offsetTop * pxRatio, elem.offsetWidth * pxRatio, elem.offsetHeight * pxRatio), divs = container.actualOptions.interactivity.modes.repulse.divs, divRepulse = divMode(divs, elem);\n            this.processRepulse(pos, repulseRadius, area, divRepulse);\n        });\n    }\n    hoverRepulse() {\n        const container = this.container, mousePos = container.interactivity.mouse.position;\n        if (!mousePos) {\n            return;\n        }\n        const repulseRadius = container.retina.repulseModeDistance;\n        this.processRepulse(mousePos, repulseRadius, new Circle(mousePos.x, mousePos.y, repulseRadius));\n    }\n    processRepulse(position, repulseRadius, area, divRepulse) {\n        var _a;\n        const container = this.container, query = container.particles.quadTree.query(area), repulseOptions = container.actualOptions.interactivity.modes.repulse;\n        for (const particle of query) {\n            const { dx, dy, distance } = getDistances(particle.position, position), velocity = ((_a = divRepulse === null || divRepulse === void 0 ? void 0 : divRepulse.speed) !== null && _a !== void 0 ? _a : repulseOptions.speed) * repulseOptions.factor, repulseFactor = clamp(calcEasing(1 - distance / repulseRadius, repulseOptions.easing) * velocity, 0, repulseOptions.maxSpeed), normVec = Vector.create(distance === 0 ? velocity : (dx / distance) * repulseFactor, distance === 0 ? velocity : (dy / distance) * repulseFactor);\n            particle.position.addTo(normVec);\n        }\n    }\n    clickRepulse() {\n        const container = this.container;\n        if (!container.repulse.finish) {\n            if (!container.repulse.count) {\n                container.repulse.count = 0;\n            }\n            container.repulse.count++;\n            if (container.repulse.count === container.particles.count) {\n                container.repulse.finish = true;\n            }\n        }\n        if (container.repulse.clicking) {\n            const repulseDistance = container.retina.repulseModeDistance, repulseRadius = Math.pow(repulseDistance / 6, 3), mouseClickPos = container.interactivity.mouse.clickPosition;\n            if (mouseClickPos === undefined) {\n                return;\n            }\n            const range = new Circle(mouseClickPos.x, mouseClickPos.y, repulseRadius), query = container.particles.quadTree.query(range);\n            for (const particle of query) {\n                const { dx, dy, distance } = getDistances(mouseClickPos, particle.position), d = distance ** 2, velocity = container.actualOptions.interactivity.modes.repulse.speed, force = (-repulseRadius * velocity) / d;\n                if (d <= repulseRadius) {\n                    container.repulse.particles.push(particle);\n                    const vect = Vector.create(dx, dy);\n                    vect.length = force;\n                    particle.velocity.setTo(vect);\n                }\n            }\n        }\n        else if (container.repulse.clicking === false) {\n            for (const particle of container.repulse.particles) {\n                particle.velocity.setTo(particle.initialVelocity);\n            }\n            container.repulse.particles = [];\n        }\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
{"ast": null, "code": "import { deepExtend, setRangeValue } from \"../../../../Utils\";\nimport { AnimatableColor } from \"../../../../Options/Classes/AnimatableColor\";\nimport { EmitterLife } from \"./EmitterLife\";\nimport { EmitterRate } from \"./EmitterRate\";\nimport { EmitterSize } from \"./EmitterSize\";\nexport class Emitter {\n  constructor() {\n    this.autoPlay = true;\n    this.fill = true;\n    this.life = new EmitterLife();\n    this.rate = new EmitterRate();\n    this.shape = \"square\";\n    this.startCount = 0;\n  }\n\n  load(data) {\n    if (data === undefined) {\n      return;\n    }\n\n    if (data.autoPlay !== undefined) {\n      this.autoPlay = data.autoPlay;\n    }\n\n    if (data.size !== undefined) {\n      if (this.size === undefined) {\n        this.size = new EmitterSize();\n      }\n\n      this.size.load(data.size);\n    }\n\n    if (data.direction !== undefined) {\n      this.direction = data.direction;\n    }\n\n    this.domId = data.domId;\n\n    if (data.fill !== undefined) {\n      this.fill = data.fill;\n    }\n\n    this.life.load(data.life);\n    this.name = data.name;\n\n    if (data.particles !== undefined) {\n      this.particles = deepExtend({}, data.particles);\n    }\n\n    this.rate.load(data.rate);\n\n    if (data.shape !== undefined) {\n      this.shape = data.shape;\n    }\n\n    if (data.position !== undefined) {\n      this.position = {};\n\n      if (data.position.x !== undefined) {\n        this.position.x = setRangeValue(data.position.x);\n      }\n\n      if (data.position.y !== undefined) {\n        this.position.y = setRangeValue(data.position.y);\n      }\n    }\n\n    if (data.spawnColor !== undefined) {\n      if (this.spawnColor === undefined) {\n        this.spawnColor = new AnimatableColor();\n      }\n\n      this.spawnColor.load(data.spawnColor);\n    }\n\n    if (data.startCount !== undefined) {\n      this.startCount = data.startCount;\n    }\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Plugins/Emitters/Options/Classes/Emitter.js"], "names": ["deepExtend", "setRangeValue", "AnimatableColor", "EmitterLife", "EmitterRate", "EmitterSize", "Emitter", "constructor", "autoPlay", "fill", "life", "rate", "shape", "startCount", "load", "data", "undefined", "size", "direction", "domId", "name", "particles", "position", "x", "y", "spawnColor"], "mappings": "AAAA,SAASA,UAAT,EAAqBC,aAArB,QAA0C,mBAA1C;AACA,SAASC,eAAT,QAAgC,6CAAhC;AACA,SAASC,WAAT,QAA4B,eAA5B;AACA,SAASC,WAAT,QAA4B,eAA5B;AACA,SAASC,WAAT,QAA4B,eAA5B;AACA,OAAO,MAAMC,OAAN,CAAc;AACjBC,EAAAA,WAAW,GAAG;AACV,SAAKC,QAAL,GAAgB,IAAhB;AACA,SAAKC,IAAL,GAAY,IAAZ;AACA,SAAKC,IAAL,GAAY,IAAIP,WAAJ,EAAZ;AACA,SAAKQ,IAAL,GAAY,IAAIP,WAAJ,EAAZ;AACA,SAAKQ,KAAL,GAAa,QAAb;AACA,SAAKC,UAAL,GAAkB,CAAlB;AACH;;AACDC,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,QAAIA,IAAI,KAAKC,SAAb,EAAwB;AACpB;AACH;;AACD,QAAID,IAAI,CAACP,QAAL,KAAkBQ,SAAtB,EAAiC;AAC7B,WAAKR,QAAL,GAAgBO,IAAI,CAACP,QAArB;AACH;;AACD,QAAIO,IAAI,CAACE,IAAL,KAAcD,SAAlB,EAA6B;AACzB,UAAI,KAAKC,IAAL,KAAcD,SAAlB,EAA6B;AACzB,aAAKC,IAAL,GAAY,IAAIZ,WAAJ,EAAZ;AACH;;AACD,WAAKY,IAAL,CAAUH,IAAV,CAAeC,IAAI,CAACE,IAApB;AACH;;AACD,QAAIF,IAAI,CAACG,SAAL,KAAmBF,SAAvB,EAAkC;AAC9B,WAAKE,SAAL,GAAiBH,IAAI,CAACG,SAAtB;AACH;;AACD,SAAKC,KAAL,GAAaJ,IAAI,CAACI,KAAlB;;AACA,QAAIJ,IAAI,CAACN,IAAL,KAAcO,SAAlB,EAA6B;AACzB,WAAKP,IAAL,GAAYM,IAAI,CAACN,IAAjB;AACH;;AACD,SAAKC,IAAL,CAAUI,IAAV,CAAeC,IAAI,CAACL,IAApB;AACA,SAAKU,IAAL,GAAYL,IAAI,CAACK,IAAjB;;AACA,QAAIL,IAAI,CAACM,SAAL,KAAmBL,SAAvB,EAAkC;AAC9B,WAAKK,SAAL,GAAiBrB,UAAU,CAAC,EAAD,EAAKe,IAAI,CAACM,SAAV,CAA3B;AACH;;AACD,SAAKV,IAAL,CAAUG,IAAV,CAAeC,IAAI,CAACJ,IAApB;;AACA,QAAII,IAAI,CAACH,KAAL,KAAeI,SAAnB,EAA8B;AAC1B,WAAKJ,KAAL,GAAaG,IAAI,CAACH,KAAlB;AACH;;AACD,QAAIG,IAAI,CAACO,QAAL,KAAkBN,SAAtB,EAAiC;AAC7B,WAAKM,QAAL,GAAgB,EAAhB;;AACA,UAAIP,IAAI,CAACO,QAAL,CAAcC,CAAd,KAAoBP,SAAxB,EAAmC;AAC/B,aAAKM,QAAL,CAAcC,CAAd,GAAkBtB,aAAa,CAACc,IAAI,CAACO,QAAL,CAAcC,CAAf,CAA/B;AACH;;AACD,UAAIR,IAAI,CAACO,QAAL,CAAcE,CAAd,KAAoBR,SAAxB,EAAmC;AAC/B,aAAKM,QAAL,CAAcE,CAAd,GAAkBvB,aAAa,CAACc,IAAI,CAACO,QAAL,CAAcE,CAAf,CAA/B;AACH;AACJ;;AACD,QAAIT,IAAI,CAACU,UAAL,KAAoBT,SAAxB,EAAmC;AAC/B,UAAI,KAAKS,UAAL,KAAoBT,SAAxB,EAAmC;AAC/B,aAAKS,UAAL,GAAkB,IAAIvB,eAAJ,EAAlB;AACH;;AACD,WAAKuB,UAAL,CAAgBX,IAAhB,CAAqBC,IAAI,CAACU,UAA1B;AACH;;AACD,QAAIV,IAAI,CAACF,UAAL,KAAoBG,SAAxB,EAAmC;AAC/B,WAAKH,UAAL,GAAkBE,IAAI,CAACF,UAAvB;AACH;AACJ;;AAxDgB", "sourcesContent": ["import { deepExtend, setRangeValue } from \"../../../../Utils\";\nimport { AnimatableColor } from \"../../../../Options/Classes/AnimatableColor\";\nimport { EmitterLife } from \"./EmitterLife\";\nimport { EmitterRate } from \"./EmitterRate\";\nimport { EmitterSize } from \"./EmitterSize\";\nexport class Emitter {\n    constructor() {\n        this.autoPlay = true;\n        this.fill = true;\n        this.life = new EmitterLife();\n        this.rate = new EmitterRate();\n        this.shape = \"square\";\n        this.startCount = 0;\n    }\n    load(data) {\n        if (data === undefined) {\n            return;\n        }\n        if (data.autoPlay !== undefined) {\n            this.autoPlay = data.autoPlay;\n        }\n        if (data.size !== undefined) {\n            if (this.size === undefined) {\n                this.size = new EmitterSize();\n            }\n            this.size.load(data.size);\n        }\n        if (data.direction !== undefined) {\n            this.direction = data.direction;\n        }\n        this.domId = data.domId;\n        if (data.fill !== undefined) {\n            this.fill = data.fill;\n        }\n        this.life.load(data.life);\n        this.name = data.name;\n        if (data.particles !== undefined) {\n            this.particles = deepExtend({}, data.particles);\n        }\n        this.rate.load(data.rate);\n        if (data.shape !== undefined) {\n            this.shape = data.shape;\n        }\n        if (data.position !== undefined) {\n            this.position = {};\n            if (data.position.x !== undefined) {\n                this.position.x = setRangeValue(data.position.x);\n            }\n            if (data.position.y !== undefined) {\n                this.position.y = setRangeValue(data.position.y);\n            }\n        }\n        if (data.spawnColor !== undefined) {\n            if (this.spawnColor === undefined) {\n                this.spawnColor = new AnimatableColor();\n            }\n            this.spawnColor.load(data.spawnColor);\n        }\n        if (data.startCount !== undefined) {\n            this.startCount = data.startCount;\n        }\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
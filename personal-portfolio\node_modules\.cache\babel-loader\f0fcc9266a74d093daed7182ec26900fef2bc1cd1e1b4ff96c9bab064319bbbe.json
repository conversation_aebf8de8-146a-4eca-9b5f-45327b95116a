{"ast": null, "code": "import { InteractorType } from \"../../Enums/Types/InteractorType.js\";\nexport class ExternalInteractorBase {\n  constructor(container) {\n    this.type = InteractorType.external;\n    this.container = container;\n  }\n}", "map": {"version": 3, "names": ["InteractorType", "ExternalInteractorBase", "constructor", "container", "type", "external"], "sources": ["D:/Audio Songs/React-portfolio/personal-portfolio/node_modules/@tsparticles/engine/browser/Core/Utils/ExternalInteractorBase.js"], "sourcesContent": ["import { InteractorType } from \"../../Enums/Types/InteractorType.js\";\nexport class ExternalInteractorBase {\n    constructor(container) {\n        this.type = InteractorType.external;\n        this.container = container;\n    }\n}\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,qCAAqC;AACpE,OAAO,MAAMC,sBAAsB,CAAC;EAChCC,WAAWA,CAACC,SAAS,EAAE;IACnB,IAAI,CAACC,IAAI,GAAGJ,cAAc,CAACK,QAAQ;IACnC,IAAI,CAACF,SAAS,GAAGA,SAAS;EAC9B;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { Bouncer } from \"./Bouncer\";\nexport async function loadExternalBounceInteraction(engine) {\n  await engine.addInteractor(\"externalBounce\", container => new Bouncer(container));\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Interactions/External/Bounce/index.js"], "names": ["<PERSON><PERSON><PERSON>", "loadExternalBounceInteraction", "engine", "addInteractor", "container"], "mappings": "AAAA,SAASA,OAAT,QAAwB,WAAxB;AACA,OAAO,eAAeC,6BAAf,CAA6CC,MAA7C,EAAqD;AACxD,QAAMA,MAAM,CAACC,aAAP,CAAqB,gBAArB,EAAwCC,SAAD,IAAe,IAAIJ,OAAJ,CAAYI,SAAZ,CAAtD,CAAN;AACH", "sourcesContent": ["import { Bouncer } from \"./Bouncer\";\nexport async function loadExternalBounceInteraction(engine) {\n    await engine.addInteractor(\"externalBounce\", (container) => new Bouncer(container));\n}\n"]}, "metadata": {}, "sourceType": "module"}
{"ast": null, "code": "export class ClickEvent {\n  constructor() {\n    this.enable = false;\n    this.mode = [];\n  }\n\n  load(data) {\n    if (data === undefined) {\n      return;\n    }\n\n    if (data.enable !== undefined) {\n      this.enable = data.enable;\n    }\n\n    if (data.mode !== undefined) {\n      this.mode = data.mode;\n    }\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Options/Classes/Interactivity/Events/ClickEvent.js"], "names": ["ClickEvent", "constructor", "enable", "mode", "load", "data", "undefined"], "mappings": "AAAA,OAAO,MAAMA,UAAN,CAAiB;AACpBC,EAAAA,WAAW,GAAG;AACV,SAAKC,MAAL,GAAc,KAAd;AACA,SAAKC,IAAL,GAAY,EAAZ;AACH;;AACDC,EAAAA,IAAI,CAACC,IAAD,EAAO;AACP,QAAIA,IAAI,KAAKC,SAAb,EAAwB;AACpB;AACH;;AACD,QAAID,IAAI,CAACH,MAAL,KAAgBI,SAApB,EAA+B;AAC3B,WAAKJ,MAAL,GAAcG,IAAI,CAACH,MAAnB;AACH;;AACD,QAAIG,IAAI,CAACF,IAAL,KAAcG,SAAlB,EAA6B;AACzB,WAAKH,IAAL,GAAYE,IAAI,CAACF,IAAjB;AACH;AACJ;;AAfmB", "sourcesContent": ["export class ClickEvent {\n    constructor() {\n        this.enable = false;\n        this.mode = [];\n    }\n    load(data) {\n        if (data === undefined) {\n            return;\n        }\n        if (data.enable !== undefined) {\n            this.enable = data.enable;\n        }\n        if (data.mode !== undefined) {\n            this.mode = data.mode;\n        }\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
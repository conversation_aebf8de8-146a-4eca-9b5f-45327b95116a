{"ast": null, "code": "import { Absorber } from \"./Options/Classes/Absorber\";\nimport { AbsorberInstance } from \"./AbsorberInstance\";\nimport { itemFromArray } from \"../../Utils\";\nexport class Absorbers {\n  constructor(container) {\n    this.container = container;\n    this.array = [];\n    this.absorbers = [];\n    this.interactivityAbsorbers = [];\n    const overridableContainer = container;\n\n    overridableContainer.getAbsorber = idxOrName => idxOrName === undefined || typeof idxOrName === \"number\" ? this.array[idxOrName || 0] : this.array.find(t => t.name === idxOrName);\n\n    overridableContainer.addAbsorber = (options, position) => this.addAbsorber(options, position);\n  }\n\n  init(options) {\n    var _a, _b;\n\n    if (!options) {\n      return;\n    }\n\n    if (options.absorbers) {\n      if (options.absorbers instanceof Array) {\n        this.absorbers = options.absorbers.map(s => {\n          const tmp = new Absorber();\n          tmp.load(s);\n          return tmp;\n        });\n      } else {\n        if (this.absorbers instanceof Array) {\n          this.absorbers = new Absorber();\n        }\n\n        this.absorbers.load(options.absorbers);\n      }\n    }\n\n    const interactivityAbsorbers = (_b = (_a = options.interactivity) === null || _a === void 0 ? void 0 : _a.modes) === null || _b === void 0 ? void 0 : _b.absorbers;\n\n    if (interactivityAbsorbers) {\n      if (interactivityAbsorbers instanceof Array) {\n        this.interactivityAbsorbers = interactivityAbsorbers.map(s => {\n          const tmp = new Absorber();\n          tmp.load(s);\n          return tmp;\n        });\n      } else {\n        if (this.interactivityAbsorbers instanceof Array) {\n          this.interactivityAbsorbers = new Absorber();\n        }\n\n        this.interactivityAbsorbers.load(interactivityAbsorbers);\n      }\n    }\n\n    if (this.absorbers instanceof Array) {\n      for (const absorberOptions of this.absorbers) {\n        this.addAbsorber(absorberOptions);\n      }\n    } else {\n      this.addAbsorber(this.absorbers);\n    }\n  }\n\n  particleUpdate(particle) {\n    for (const absorber of this.array) {\n      absorber.attract(particle);\n\n      if (particle.destroyed) {\n        break;\n      }\n    }\n  }\n\n  draw(context) {\n    for (const absorber of this.array) {\n      context.save();\n      absorber.draw(context);\n      context.restore();\n    }\n  }\n\n  stop() {\n    this.array = [];\n  }\n\n  resize() {\n    for (const absorber of this.array) {\n      absorber.resize();\n    }\n  }\n\n  handleClickMode(mode) {\n    const container = this.container;\n    const absorberOptions = this.absorbers;\n    const modeAbsorbers = this.interactivityAbsorbers;\n\n    if (mode === \"absorber\") {\n      let absorbersModeOptions;\n\n      if (modeAbsorbers instanceof Array) {\n        if (modeAbsorbers.length > 0) {\n          absorbersModeOptions = itemFromArray(modeAbsorbers);\n        }\n      } else {\n        absorbersModeOptions = modeAbsorbers;\n      }\n\n      const absorbersOptions = absorbersModeOptions !== null && absorbersModeOptions !== void 0 ? absorbersModeOptions : absorberOptions instanceof Array ? itemFromArray(absorberOptions) : absorberOptions;\n      const aPosition = container.interactivity.mouse.clickPosition;\n      this.addAbsorber(absorbersOptions, aPosition);\n    }\n  }\n\n  addAbsorber(options, position) {\n    const absorber = new AbsorberInstance(this, this.container, options, position);\n    this.array.push(absorber);\n    return absorber;\n  }\n\n  removeAbsorber(absorber) {\n    const index = this.array.indexOf(absorber);\n\n    if (index >= 0) {\n      this.array.splice(index, 1);\n    }\n  }\n\n}", "map": {"version": 3, "sources": ["D:/Audio Songs/React-portfolio/Portfolio/node_modules/tsparticles/esm/Plugins/Absorbers/Absorbers.js"], "names": ["Absorber", "AbsorberInstance", "itemFromArray", "Absorbers", "constructor", "container", "array", "absorbers", "interactivityAbsorbers", "overridableContainer", "getAbsorber", "idxOrName", "undefined", "find", "t", "name", "addAbsorber", "options", "position", "init", "_a", "_b", "Array", "map", "s", "tmp", "load", "interactivity", "modes", "absorberOptions", "particleUpdate", "particle", "absorber", "attract", "destroyed", "draw", "context", "save", "restore", "stop", "resize", "handleClickMode", "mode", "modeAbsorbers", "absorbersModeOptions", "length", "absorbersOptions", "aPosition", "mouse", "clickPosition", "push", "removeAbsorber", "index", "indexOf", "splice"], "mappings": "AAAA,SAASA,QAAT,QAAyB,4BAAzB;AACA,SAASC,gBAAT,QAAiC,oBAAjC;AACA,SAASC,aAAT,QAA8B,aAA9B;AACA,OAAO,MAAMC,SAAN,CAAgB;AACnBC,EAAAA,WAAW,CAACC,SAAD,EAAY;AACnB,SAAKA,SAAL,GAAiBA,SAAjB;AACA,SAAKC,KAAL,GAAa,EAAb;AACA,SAAKC,SAAL,GAAiB,EAAjB;AACA,SAAKC,sBAAL,GAA8B,EAA9B;AACA,UAAMC,oBAAoB,GAAGJ,SAA7B;;AACAI,IAAAA,oBAAoB,CAACC,WAArB,GAAoCC,SAAD,IAAeA,SAAS,KAAKC,SAAd,IAA2B,OAAOD,SAAP,KAAqB,QAAhD,GAC5C,KAAKL,KAAL,CAAWK,SAAS,IAAI,CAAxB,CAD4C,GAE5C,KAAKL,KAAL,CAAWO,IAAX,CAAiBC,CAAD,IAAOA,CAAC,CAACC,IAAF,KAAWJ,SAAlC,CAFN;;AAGAF,IAAAA,oBAAoB,CAACO,WAArB,GAAmC,CAACC,OAAD,EAAUC,QAAV,KAAuB,KAAKF,WAAL,CAAiBC,OAAjB,EAA0BC,QAA1B,CAA1D;AACH;;AACDC,EAAAA,IAAI,CAACF,OAAD,EAAU;AACV,QAAIG,EAAJ,EAAQC,EAAR;;AACA,QAAI,CAACJ,OAAL,EAAc;AACV;AACH;;AACD,QAAIA,OAAO,CAACV,SAAZ,EAAuB;AACnB,UAAIU,OAAO,CAACV,SAAR,YAA6Be,KAAjC,EAAwC;AACpC,aAAKf,SAAL,GAAiBU,OAAO,CAACV,SAAR,CAAkBgB,GAAlB,CAAuBC,CAAD,IAAO;AAC1C,gBAAMC,GAAG,GAAG,IAAIzB,QAAJ,EAAZ;AACAyB,UAAAA,GAAG,CAACC,IAAJ,CAASF,CAAT;AACA,iBAAOC,GAAP;AACH,SAJgB,CAAjB;AAKH,OAND,MAOK;AACD,YAAI,KAAKlB,SAAL,YAA0Be,KAA9B,EAAqC;AACjC,eAAKf,SAAL,GAAiB,IAAIP,QAAJ,EAAjB;AACH;;AACD,aAAKO,SAAL,CAAemB,IAAf,CAAoBT,OAAO,CAACV,SAA5B;AACH;AACJ;;AACD,UAAMC,sBAAsB,GAAG,CAACa,EAAE,GAAG,CAACD,EAAE,GAAGH,OAAO,CAACU,aAAd,MAAiC,IAAjC,IAAyCP,EAAE,KAAK,KAAK,CAArD,GAAyD,KAAK,CAA9D,GAAkEA,EAAE,CAACQ,KAA3E,MAAsF,IAAtF,IAA8FP,EAAE,KAAK,KAAK,CAA1G,GAA8G,KAAK,CAAnH,GAAuHA,EAAE,CAACd,SAAzJ;;AACA,QAAIC,sBAAJ,EAA4B;AACxB,UAAIA,sBAAsB,YAAYc,KAAtC,EAA6C;AACzC,aAAKd,sBAAL,GAA8BA,sBAAsB,CAACe,GAAvB,CAA4BC,CAAD,IAAO;AAC5D,gBAAMC,GAAG,GAAG,IAAIzB,QAAJ,EAAZ;AACAyB,UAAAA,GAAG,CAACC,IAAJ,CAASF,CAAT;AACA,iBAAOC,GAAP;AACH,SAJ6B,CAA9B;AAKH,OAND,MAOK;AACD,YAAI,KAAKjB,sBAAL,YAAuCc,KAA3C,EAAkD;AAC9C,eAAKd,sBAAL,GAA8B,IAAIR,QAAJ,EAA9B;AACH;;AACD,aAAKQ,sBAAL,CAA4BkB,IAA5B,CAAiClB,sBAAjC;AACH;AACJ;;AACD,QAAI,KAAKD,SAAL,YAA0Be,KAA9B,EAAqC;AACjC,WAAK,MAAMO,eAAX,IAA8B,KAAKtB,SAAnC,EAA8C;AAC1C,aAAKS,WAAL,CAAiBa,eAAjB;AACH;AACJ,KAJD,MAKK;AACD,WAAKb,WAAL,CAAiB,KAAKT,SAAtB;AACH;AACJ;;AACDuB,EAAAA,cAAc,CAACC,QAAD,EAAW;AACrB,SAAK,MAAMC,QAAX,IAAuB,KAAK1B,KAA5B,EAAmC;AAC/B0B,MAAAA,QAAQ,CAACC,OAAT,CAAiBF,QAAjB;;AACA,UAAIA,QAAQ,CAACG,SAAb,EAAwB;AACpB;AACH;AACJ;AACJ;;AACDC,EAAAA,IAAI,CAACC,OAAD,EAAU;AACV,SAAK,MAAMJ,QAAX,IAAuB,KAAK1B,KAA5B,EAAmC;AAC/B8B,MAAAA,OAAO,CAACC,IAAR;AACAL,MAAAA,QAAQ,CAACG,IAAT,CAAcC,OAAd;AACAA,MAAAA,OAAO,CAACE,OAAR;AACH;AACJ;;AACDC,EAAAA,IAAI,GAAG;AACH,SAAKjC,KAAL,GAAa,EAAb;AACH;;AACDkC,EAAAA,MAAM,GAAG;AACL,SAAK,MAAMR,QAAX,IAAuB,KAAK1B,KAA5B,EAAmC;AAC/B0B,MAAAA,QAAQ,CAACQ,MAAT;AACH;AACJ;;AACDC,EAAAA,eAAe,CAACC,IAAD,EAAO;AAClB,UAAMrC,SAAS,GAAG,KAAKA,SAAvB;AACA,UAAMwB,eAAe,GAAG,KAAKtB,SAA7B;AACA,UAAMoC,aAAa,GAAG,KAAKnC,sBAA3B;;AACA,QAAIkC,IAAI,KAAK,UAAb,EAAyB;AACrB,UAAIE,oBAAJ;;AACA,UAAID,aAAa,YAAYrB,KAA7B,EAAoC;AAChC,YAAIqB,aAAa,CAACE,MAAd,GAAuB,CAA3B,EAA8B;AAC1BD,UAAAA,oBAAoB,GAAG1C,aAAa,CAACyC,aAAD,CAApC;AACH;AACJ,OAJD,MAKK;AACDC,QAAAA,oBAAoB,GAAGD,aAAvB;AACH;;AACD,YAAMG,gBAAgB,GAAGF,oBAAoB,KAAK,IAAzB,IAAiCA,oBAAoB,KAAK,KAAK,CAA/D,GAAmEA,oBAAnE,GAA2Ff,eAAe,YAAYP,KAA3B,GAAmCpB,aAAa,CAAC2B,eAAD,CAAhD,GAAoEA,eAAxL;AACA,YAAMkB,SAAS,GAAG1C,SAAS,CAACsB,aAAV,CAAwBqB,KAAxB,CAA8BC,aAAhD;AACA,WAAKjC,WAAL,CAAiB8B,gBAAjB,EAAmCC,SAAnC;AACH;AACJ;;AACD/B,EAAAA,WAAW,CAACC,OAAD,EAAUC,QAAV,EAAoB;AAC3B,UAAMc,QAAQ,GAAG,IAAI/B,gBAAJ,CAAqB,IAArB,EAA2B,KAAKI,SAAhC,EAA2CY,OAA3C,EAAoDC,QAApD,CAAjB;AACA,SAAKZ,KAAL,CAAW4C,IAAX,CAAgBlB,QAAhB;AACA,WAAOA,QAAP;AACH;;AACDmB,EAAAA,cAAc,CAACnB,QAAD,EAAW;AACrB,UAAMoB,KAAK,GAAG,KAAK9C,KAAL,CAAW+C,OAAX,CAAmBrB,QAAnB,CAAd;;AACA,QAAIoB,KAAK,IAAI,CAAb,EAAgB;AACZ,WAAK9C,KAAL,CAAWgD,MAAX,CAAkBF,KAAlB,EAAyB,CAAzB;AACH;AACJ;;AA7GkB", "sourcesContent": ["import { Absorber } from \"./Options/Classes/Absorber\";\nimport { AbsorberInstance } from \"./AbsorberInstance\";\nimport { itemFromArray } from \"../../Utils\";\nexport class Absorbers {\n    constructor(container) {\n        this.container = container;\n        this.array = [];\n        this.absorbers = [];\n        this.interactivityAbsorbers = [];\n        const overridableContainer = container;\n        overridableContainer.getAbsorber = (idxOrName) => idxOrName === undefined || typeof idxOrName === \"number\"\n            ? this.array[idxOrName || 0]\n            : this.array.find((t) => t.name === idxOrName);\n        overridableContainer.addAbsorber = (options, position) => this.addAbsorber(options, position);\n    }\n    init(options) {\n        var _a, _b;\n        if (!options) {\n            return;\n        }\n        if (options.absorbers) {\n            if (options.absorbers instanceof Array) {\n                this.absorbers = options.absorbers.map((s) => {\n                    const tmp = new Absorber();\n                    tmp.load(s);\n                    return tmp;\n                });\n            }\n            else {\n                if (this.absorbers instanceof Array) {\n                    this.absorbers = new Absorber();\n                }\n                this.absorbers.load(options.absorbers);\n            }\n        }\n        const interactivityAbsorbers = (_b = (_a = options.interactivity) === null || _a === void 0 ? void 0 : _a.modes) === null || _b === void 0 ? void 0 : _b.absorbers;\n        if (interactivityAbsorbers) {\n            if (interactivityAbsorbers instanceof Array) {\n                this.interactivityAbsorbers = interactivityAbsorbers.map((s) => {\n                    const tmp = new Absorber();\n                    tmp.load(s);\n                    return tmp;\n                });\n            }\n            else {\n                if (this.interactivityAbsorbers instanceof Array) {\n                    this.interactivityAbsorbers = new Absorber();\n                }\n                this.interactivityAbsorbers.load(interactivityAbsorbers);\n            }\n        }\n        if (this.absorbers instanceof Array) {\n            for (const absorberOptions of this.absorbers) {\n                this.addAbsorber(absorberOptions);\n            }\n        }\n        else {\n            this.addAbsorber(this.absorbers);\n        }\n    }\n    particleUpdate(particle) {\n        for (const absorber of this.array) {\n            absorber.attract(particle);\n            if (particle.destroyed) {\n                break;\n            }\n        }\n    }\n    draw(context) {\n        for (const absorber of this.array) {\n            context.save();\n            absorber.draw(context);\n            context.restore();\n        }\n    }\n    stop() {\n        this.array = [];\n    }\n    resize() {\n        for (const absorber of this.array) {\n            absorber.resize();\n        }\n    }\n    handleClickMode(mode) {\n        const container = this.container;\n        const absorberOptions = this.absorbers;\n        const modeAbsorbers = this.interactivityAbsorbers;\n        if (mode === \"absorber\") {\n            let absorbersModeOptions;\n            if (modeAbsorbers instanceof Array) {\n                if (modeAbsorbers.length > 0) {\n                    absorbersModeOptions = itemFromArray(modeAbsorbers);\n                }\n            }\n            else {\n                absorbersModeOptions = modeAbsorbers;\n            }\n            const absorbersOptions = absorbersModeOptions !== null && absorbersModeOptions !== void 0 ? absorbersModeOptions : (absorberOptions instanceof Array ? itemFromArray(absorberOptions) : absorberOptions);\n            const aPosition = container.interactivity.mouse.clickPosition;\n            this.addAbsorber(absorbersOptions, aPosition);\n        }\n    }\n    addAbsorber(options, position) {\n        const absorber = new AbsorberInstance(this, this.container, options, position);\n        this.array.push(absorber);\n        return absorber;\n    }\n    removeAbsorber(absorber) {\n        const index = this.array.indexOf(absorber);\n        if (index >= 0) {\n            this.array.splice(index, 1);\n        }\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}